import React, { useState, useEffect } from 'react';
import { Card } from '../../../components/Card';
import { 
  AcademicCapIcon,
  UserGroupIcon,
  BookOpenIcon,
  ClipboardDocumentListIcon,
  ChartBarIcon,
  BuildingLibraryIcon
} from '@heroicons/react/24/solid';

interface TeacherDashboardProps {
  activeSubSection: string;
}

export const TeacherDashboard: React.FC<TeacherDashboardProps> = ({ activeSubSection }) => {
  const [teacherData, setTeacherData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTeacherData = async () => {
      try {
        setLoading(true);
        await new Promise(resolve => setTimeout(resolve, 500));
        
        const mockData = {
          overview: {
            totalClasses: 6,
            totalStudents: 142,
            avgGrade: 87.5,
            pendingAssignments: 23
          },
          classes: [
            { name: 'Advanced Mathematics', students: 28, grade: 'Grade 11', color: 'bg-blue-500/20 text-blue-400' },
            { name: 'Calculus I', students: 24, grade: 'Grade 12', color: 'bg-green-500/20 text-green-400' },
            { name: 'Statistics', students: 32, grade: 'Grade 10', color: 'bg-purple-500/20 text-purple-400' }
          ],
          recentAssignments: [
            { title: 'Quadratic Equations Test', class: 'Advanced Mathematics', dueDate: 'March 18', submitted: 24, total: 28 },
            { title: 'Calculus Problem Set 5', class: 'Calculus I', dueDate: 'March 20', submitted: 20, total: 24 },
            { title: 'Statistics Project', class: 'Statistics', dueDate: 'March 25', submitted: 15, total: 32 }
          ]
        };
        
        setTeacherData(mockData);
      } catch (error) {
        console.error('Failed to fetch teacher data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTeacherData();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-cyan-400">Loading Teacher Dashboard...</div>
      </div>
    );
  }

  const renderContent = () => {
    switch (activeSubSection) {
      case 'dashboard':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Teacher Overview */}
            <Card title="Teaching Overview" className="lg:col-span-3">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4">
                <div className="text-center">
                  <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <AcademicCapIcon className="w-6 h-6 text-green-400" />
                  </div>
                  <p className="text-2xl font-bold text-white">{teacherData.overview.totalClasses}</p>
                  <p className="text-sm text-gray-400">My Classes</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <UserGroupIcon className="w-6 h-6 text-blue-400" />
                  </div>
                  <p className="text-2xl font-bold text-white">{teacherData.overview.totalStudents}</p>
                  <p className="text-sm text-gray-400">Total Students</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <ChartBarIcon className="w-6 h-6 text-yellow-400" />
                  </div>
                  <p className="text-2xl font-bold text-white">{teacherData.overview.avgGrade}%</p>
                  <p className="text-sm text-gray-400">Avg Grade</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <ClipboardDocumentListIcon className="w-6 h-6 text-red-400" />
                  </div>
                  <p className="text-2xl font-bold text-white">{teacherData.overview.pendingAssignments}</p>
                  <p className="text-sm text-gray-400">To Grade</p>
                </div>
              </div>
            </Card>

            {/* My Classes */}
            <Card title="My Classes" className="md:col-span-2">
              <div className="space-y-3 p-4">
                {teacherData.classes.map((cls: any, index: number) => (
                  <div key={index} className={`p-3 rounded-lg ${cls.color.split(' ')[0]} border border-gray-600/30`}>
                    <div className="flex justify-between items-center">
                      <div>
                        <h4 className={`font-semibold ${cls.color.split(' ').slice(1).join(' ')}`}>{cls.name}</h4>
                        <p className="text-sm text-gray-400">{cls.grade} • {cls.students} students</p>
                      </div>
                      <div className={`w-8 h-8 rounded-lg ${cls.color.split(' ')[0]} flex items-center justify-center`}>
                        <BookOpenIcon className={`w-4 h-4 ${cls.color.split(' ').slice(1).join(' ')}`} />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Recent Assignments */}
            <Card title="Recent Assignments">
              <div className="space-y-3 p-4">
                {teacherData.recentAssignments.map((assignment: any, index: number) => (
                  <div key={index} className="p-3 rounded-lg bg-gray-800/40 border border-gray-600/30">
                    <h4 className="text-sm font-medium text-white mb-1">{assignment.title}</h4>
                    <p className="text-xs text-gray-400 mb-2">{assignment.class} • Due: {assignment.dueDate}</p>
                    <div className="flex items-center gap-2">
                      <div className="flex-1 bg-gray-700 rounded-full h-2">
                        <div 
                          className="bg-cyan-400 h-2 rounded-full" 
                          style={{ width: `${(assignment.submitted / assignment.total) * 100}%` }}
                        ></div>
                      </div>
                      <span className="text-xs text-gray-400">{assignment.submitted}/{assignment.total}</span>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </div>
        );

      case 'classes':
        return (
          <Card title="My Classes">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Class Management</h3>
              <p className="text-gray-400">Manage your classes, view student rosters, and track attendance.</p>
            </div>
          </Card>
        );

      case 'students':
        return (
          <Card title="Student Management">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Student Overview</h3>
              <p className="text-gray-400">View student profiles, track progress, and manage communications.</p>
            </div>
          </Card>
        );

      case 'curriculum':
        return (
          <Card title="Curriculum Planning">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Curriculum Management</h3>
              <p className="text-gray-400">Plan lessons, manage curriculum standards, and track learning objectives.</p>
            </div>
          </Card>
        );

      case 'assignments':
        return (
          <Card title="Assignment Center">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Assignment Management</h3>
              <p className="text-gray-400">Create, distribute, and grade assignments across all your classes.</p>
            </div>
          </Card>
        );

      case 'grades':
        return (
          <Card title="Grade Book">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Grade Management</h3>
              <p className="text-gray-400">Record grades, generate reports, and track student performance.</p>
            </div>
          </Card>
        );

      case 'resources':
        return (
          <Card title="Teaching Resources">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Resource Library</h3>
              <p className="text-gray-400">Access teaching materials, lesson plans, and educational resources.</p>
            </div>
          </Card>
        );

      case 'settings':
        return (
          <Card title="Teacher Settings">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Personal Settings</h3>
              <p className="text-gray-400">Configure your teaching preferences and account settings.</p>
            </div>
          </Card>
        );

      default:
        return (
          <Card title="Teacher Dashboard">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Welcome to Teacher Portal</h3>
              <p className="text-gray-400">Select a section from the navigation to get started.</p>
            </div>
          </Card>
        );
    }
  };

  return (
    <div className="p-4 h-full">
      {renderContent()}
    </div>
  );
};

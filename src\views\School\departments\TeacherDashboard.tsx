import React, { useState, useEffect } from 'react';
import { Card } from '../../../components/Card';
import { 
  AcademicCapIcon,
  UserGroupIcon,
  BookOpenIcon,
  ClipboardDocumentListIcon,
  ChartBarIcon,
  BuildingLibraryIcon
} from '@heroicons/react/24/solid';

interface TeacherDashboardProps {
  activeSubSection: string;
}

export const TeacherDashboard: React.FC<TeacherDashboardProps> = ({ activeSubSection }) => {
  const [teacherData, setTeacherData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTeacherData = async () => {
      try {
        setLoading(true);
        await new Promise(resolve => setTimeout(resolve, 500));
        
        const mockData = {
          overview: {
            totalClasses: 6,
            totalStudents: 142,
            avgGrade: 87.5,
            pendingAssignments: 23,
            aiAssistantInteractions: 847,
            emotionalWellnessScore: 94,
            adaptiveLearningPaths: 156,
            hologramLessons: 12
          },
          classes: [
            { name: 'Quantum Physics VR', students: 28, grade: 'Grade 11', color: 'bg-blue-500/20 text-blue-400', aiTutor: 'Einstein AI', engagement: 96 },
            { name: 'Neural Network Programming', students: 24, grade: 'Grade 12', color: 'bg-green-500/20 text-green-400', aiTutor: 'Turing AI', engagement: 94 },
            { name: 'Bioengineering Lab', students: 32, grade: 'Grade 10', color: 'bg-purple-500/20 text-purple-400', aiTutor: 'Darwin AI', engagement: 92 }
          ],
          aiInsights: [
            { student: 'Alex Chen', insight: 'Shows exceptional pattern recognition in quantum mechanics', confidence: 97, action: 'Advanced track recommended' },
            { student: 'Maya Patel', insight: 'Emotional stress detected during complex problems', confidence: 89, action: 'Wellness check scheduled' },
            { student: 'Jordan Smith', insight: 'Learning style: Visual-Kinesthetic hybrid', confidence: 94, action: 'VR modules assigned' }
          ],
          recentAssignments: [
            { title: 'Quantum Entanglement Simulation', class: 'Quantum Physics VR', dueDate: 'March 18', submitted: 24, total: 28, aiGraded: 18, avgTime: '2.3h' },
            { title: 'Neural Network Architecture', class: 'Neural Network Programming', dueDate: 'March 20', submitted: 20, total: 24, aiGraded: 15, avgTime: '3.1h' },
            { title: 'Gene Editing Ethics Debate', class: 'Bioengineering Lab', dueDate: 'March 25', submitted: 15, total: 32, aiGraded: 8, avgTime: '1.8h' }
          ]
        };
        
        setTeacherData(mockData);
      } catch (error) {
        console.error('Failed to fetch teacher data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTeacherData();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-cyan-400">Loading Teacher Dashboard...</div>
      </div>
    );
  }

  const renderContent = () => {
    switch (activeSubSection) {
      case 'dashboard':
        return (
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-2 h-full">
            {/* AI-Enhanced Teaching Overview - Compact Metrics */}
            <div className="lg:col-span-12">
              <Card title="🤖 AI-Enhanced Teaching Hub" className="hud-card">
                <div className="grid grid-cols-4 gap-2 p-2">
                  <div className="metric-card hover-lift animate-fade-in-scale">
                    <div className="w-10 h-10 bg-gradient-to-br from-green-400/20 to-green-600/20 rounded-lg flex items-center justify-center mx-auto mb-2 border border-green-400/30 animate-pulse-glow">
                      <AcademicCapIcon className="w-5 h-5 text-green-400" style={{ filter: 'drop-shadow(0 0 6px rgba(34, 197, 94, 0.6))' }} />
                    </div>
                    <p className="text-xl font-bold text-white mb-1" style={{ textShadow: '0 0 10px rgba(255, 255, 255, 0.5)' }}>{teacherData.overview.hologramLessons}</p>
                    <p className="text-xs text-gray-300 font-medium">Hologram Lessons</p>
                  </div>
                  <div className="metric-card hover-lift animate-fade-in-scale" style={{ animationDelay: '0.1s' }}>
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-400/20 to-blue-600/20 rounded-lg flex items-center justify-center mx-auto mb-2 border border-blue-400/30">
                      <UserGroupIcon className="w-5 h-5 text-blue-400" style={{ filter: 'drop-shadow(0 0 6px rgba(59, 130, 246, 0.6))' }} />
                    </div>
                    <p className="text-xl font-bold text-white mb-1" style={{ textShadow: '0 0 10px rgba(255, 255, 255, 0.5)' }}>{teacherData.overview.aiAssistantInteractions}</p>
                    <p className="text-xs text-gray-300 font-medium">AI Interactions</p>
                  </div>
                  <div className="metric-card hover-lift animate-fade-in-scale" style={{ animationDelay: '0.2s' }}>
                    <div className="w-10 h-10 bg-gradient-to-br from-yellow-400/20 to-yellow-600/20 rounded-lg flex items-center justify-center mx-auto mb-2 border border-yellow-400/30">
                      <ChartBarIcon className="w-5 h-5 text-yellow-400" style={{ filter: 'drop-shadow(0 0 6px rgba(234, 179, 8, 0.6))' }} />
                    </div>
                    <p className="text-xl font-bold text-white mb-1" style={{ textShadow: '0 0 10px rgba(255, 255, 255, 0.5)' }}>{teacherData.overview.emotionalWellnessScore}%</p>
                    <p className="text-xs text-gray-300 font-medium">Wellness Score</p>
                  </div>
                  <div className="metric-card hover-lift animate-fade-in-scale" style={{ animationDelay: '0.3s' }}>
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-400/20 to-purple-600/20 rounded-lg flex items-center justify-center mx-auto mb-2 border border-purple-400/30">
                      <ClipboardDocumentListIcon className="w-5 h-5 text-purple-400" style={{ filter: 'drop-shadow(0 0 6px rgba(147, 51, 234, 0.6))' }} />
                    </div>
                    <p className="text-xl font-bold text-white mb-1" style={{ textShadow: '0 0 10px rgba(255, 255, 255, 0.5)' }}>{teacherData.overview.adaptiveLearningPaths}</p>
                    <p className="text-xs text-gray-300 font-medium">Learning Paths</p>
                  </div>
                </div>
              </Card>
            </div>

            {/* AI Student Insights */}
            <div className="lg:col-span-7">
              <Card title="🧠 AI Student Insights" className="hud-card h-full">
                <div className="space-y-2 p-3 max-h-64 overflow-y-auto">
                  {teacherData.aiInsights.map((insight: any, index: number) => (
                    <div key={index} className="p-3 rounded-lg bg-gradient-to-r from-purple-900/30 to-blue-900/30 border border-purple-400/40 backdrop-filter backdrop-blur-sm">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="text-sm font-semibold text-white">{insight.student}</h4>
                        <span className="status-badge active text-xs">
                          {insight.confidence}% confidence
                        </span>
                      </div>
                      <p className="text-xs text-gray-300 mb-2 leading-relaxed">{insight.insight}</p>
                      <p className="text-xs text-cyan-400 font-medium">💡 {insight.action}</p>
                    </div>
                  ))}
                </div>
              </Card>
            </div>

            {/* Next-Gen Classes */}
            <div className="lg:col-span-5">
              <Card title="🚀 Next-Gen Classes" className="hud-card h-full">
                <div className="space-y-3 p-3">
                  {teacherData.classes.map((cls: any, index: number) => (
                    <div key={index} className={`p-3 rounded-lg ${cls.color.split(' ')[0]} border border-gray-500/40 backdrop-filter backdrop-blur-sm transition-all duration-300 hover:scale-102 hover:border-cyan-400/60`}>
                      <div className="flex justify-between items-center">
                        <div className="flex-1">
                          <h4 className={`font-semibold text-sm ${cls.color.split(' ').slice(1).join(' ')}`}>{cls.name}</h4>
                          <p className="text-xs text-gray-400 mt-1">{cls.grade} • {cls.students} students</p>
                          <div className="flex items-center gap-2 mt-2">
                            <span className="text-xs text-cyan-400 font-medium">🤖 {cls.aiTutor}</span>
                            <span className="w-1 h-1 bg-gray-500 rounded-full"></span>
                            <span className="text-xs text-green-400 font-medium">{cls.engagement}% engaged</span>
                          </div>
                        </div>
                        <div className={`w-8 h-8 rounded-lg ${cls.color.split(' ')[0]} flex items-center justify-center border border-gray-400/30`}>
                          <BookOpenIcon className={`w-4 h-4 ${cls.color.split(' ').slice(1).join(' ')}`} style={{ filter: 'drop-shadow(0 0 4px rgba(59, 130, 246, 0.4))' }} />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </div>

            {/* AI-Graded Assignments */}
            <div className="lg:col-span-12">
              <Card title="🎯 AI-Graded Assignments" className="hud-card">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 p-2">
                  {teacherData.recentAssignments.map((assignment: any, index: number) => (
                    <div key={index} className="p-3 rounded-lg bg-gradient-to-br from-gray-800/60 to-gray-700/60 border border-gray-500/40 backdrop-filter backdrop-blur-sm transition-all duration-300 hover:border-cyan-400/60 hover:scale-102">
                      <h4 className="text-sm font-semibold text-white mb-2">{assignment.title}</h4>
                      <p className="text-xs text-gray-400 mb-3">{assignment.class} • Due: {assignment.dueDate}</p>

                      {/* Progress Bar */}
                      <div className="flex items-center gap-2 mb-3">
                        <div className="flex-1 bg-gray-700/60 rounded-full h-2 border border-gray-600/30 progress-bar">
                          <div
                            className="bg-gradient-to-r from-cyan-400 to-blue-500 h-2 rounded-full transition-all duration-500 shadow-lg"
                            style={{
                              width: `${(assignment.submitted / assignment.total) * 100}%`,
                              boxShadow: '0 0 8px rgba(0, 207, 255, 0.6)'
                            }}
                          ></div>
                        </div>
                        <span className="text-xs text-gray-300 font-medium">{assignment.submitted}/{assignment.total}</span>
                      </div>

                      {/* Status Info */}
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-purple-400 font-medium">🤖 {assignment.aiGraded} AI-graded</span>
                        <span className="text-xs text-yellow-400 font-medium">⏱️ Avg: {assignment.avgTime}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </div>
          </div>
        );

      case 'classes':
        return (
          <Card title="My Classes">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Class Management</h3>
              <p className="text-gray-400">Manage your classes, view student rosters, and track attendance.</p>
            </div>
          </Card>
        );

      case 'students':
        return (
          <Card title="Student Management">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Student Overview</h3>
              <p className="text-gray-400">View student profiles, track progress, and manage communications.</p>
            </div>
          </Card>
        );

      case 'curriculum':
        return (
          <Card title="Curriculum Planning">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Curriculum Management</h3>
              <p className="text-gray-400">Plan lessons, manage curriculum standards, and track learning objectives.</p>
            </div>
          </Card>
        );

      case 'assignments':
        return (
          <Card title="Assignment Center">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Assignment Management</h3>
              <p className="text-gray-400">Create, distribute, and grade assignments across all your classes.</p>
            </div>
          </Card>
        );

      case 'grades':
        return (
          <Card title="Grade Book">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Grade Management</h3>
              <p className="text-gray-400">Record grades, generate reports, and track student performance.</p>
            </div>
          </Card>
        );

      case 'resources':
        return (
          <Card title="Teaching Resources">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Resource Library</h3>
              <p className="text-gray-400">Access teaching materials, lesson plans, and educational resources.</p>
            </div>
          </Card>
        );

      case 'settings':
        return (
          <Card title="Teacher Settings">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Personal Settings</h3>
              <p className="text-gray-400">Configure your teaching preferences and account settings.</p>
            </div>
          </Card>
        );

      default:
        return (
          <Card title="Teacher Dashboard">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Welcome to Teacher Portal</h3>
              <p className="text-gray-400">Select a section from the navigation to get started.</p>
            </div>
          </Card>
        );
    }
  };

  return (
    <div className="dashboard-grid dashboard-grid-teacher content-no-scroll">
      {renderContent()}
    </div>
  );
};

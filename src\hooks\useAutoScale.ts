import { useEffect, useRef, useState } from 'react';

interface UseAutoScaleOptions {
  targetWidth?: number;
  targetHeight?: number;
  minScale?: number;
  maxScale?: number;
}

export const useAutoScale = (options: UseAutoScaleOptions = {}) => {
  const {
    targetWidth = 1920,
    targetHeight = 1080,
    minScale = 0.5,
    maxScale = 1.0
  } = options;

  const containerRef = useRef<HTMLElement>(null);
  const [scale, setScale] = useState(1);

  useEffect(() => {
    const calculateScale = () => {
      if (!containerRef.current) return;

      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // Calculate scale factors for both dimensions
      const scaleX = viewportWidth / targetWidth;
      const scaleY = viewportHeight / targetHeight;

      // Use the smaller scale to ensure content fits in both dimensions
      const calculatedScale = Math.min(scaleX, scaleY);

      // Clamp the scale within the specified bounds
      const finalScale = Math.max(minScale, Math.min(maxScale, calculatedScale));

      setScale(finalScale);

      // Apply the scale to the container
      if (containerRef.current) {
        containerRef.current.style.transform = `scale(${finalScale})`;
        containerRef.current.style.transformOrigin = 'top left';
        
        // Adjust container size to prevent overflow
        const scaledWidth = targetWidth * finalScale;
        const scaledHeight = targetHeight * finalScale;
        
        if (scaledWidth < viewportWidth) {
          containerRef.current.style.left = `${(viewportWidth - scaledWidth) / 2}px`;
        }
        
        if (scaledHeight < viewportHeight) {
          containerRef.current.style.top = `${(viewportHeight - scaledHeight) / 2}px`;
        }
      }
    };

    // Calculate initial scale
    calculateScale();

    // Recalculate on window resize
    const handleResize = () => {
      calculateScale();
    };

    window.addEventListener('resize', handleResize);
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [targetWidth, targetHeight, minScale, maxScale]);

  return { containerRef, scale };
};

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _src_components_Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../src/components/Sidebar */ \"./src/components/Sidebar.tsx\");\n/* harmony import */ var _src_components_Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../src/components/Header */ \"./src/components/Header.tsx\");\n/* harmony import */ var _src_components_Footer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../src/components/Footer */ \"./src/components/Footer.tsx\");\n/* harmony import */ var _src_components_RightSidebar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../src/components/RightSidebar */ \"./src/components/RightSidebar.tsx\");\n/* harmony import */ var _src_views_School_SchoolView__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../src/views/School/SchoolView */ \"./src/views/School/SchoolView.tsx\");\n/* harmony import */ var _src_hooks_useAutoScale__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../src/hooks/useAutoScale */ \"./src/hooks/useAutoScale.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst LoadingComponent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-grow flex items-center justify-center text-cyan-400\",\n        children: \"Loading View...\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n        lineNumber: 12,\n        columnNumber: 32\n    }, undefined);\n_c = LoadingComponent;\n// Dynamically import views for code splitting and performance\nconst viewComponents = {\n    Dashboard: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Dashboard_DashboardView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Dashboard/DashboardView */ \"./src/views/Dashboard/DashboardView.tsx\")).then((mod)=>mod.DashboardView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Dashboard/DashboardView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    School: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/School/SchoolView */ \"./src/views/School/SchoolView.tsx\")).then((mod)=>mod.SchoolView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/School/SchoolView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Tool: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Tool_ToolView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Tool/ToolView */ \"./src/views/Tool/ToolView.tsx\")).then((mod)=>mod.ToolView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Tool/ToolView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Market: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Market_MarketView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Market/MarketView */ \"./src/views/Market/MarketView.tsx\")).then((mod)=>mod.MarketView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Market/MarketView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Bookstore: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Bookstore_BookstoreView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Bookstore/BookstoreView */ \"./src/views/Bookstore/BookstoreView.tsx\")).then((mod)=>mod.BookstoreView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Bookstore/BookstoreView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Concierge: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Concierge_ConciergeView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Concierge/ConciergeView */ \"./src/views/Concierge/ConciergeView.tsx\")).then((mod)=>mod.ConciergeView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Concierge/ConciergeView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Analytic: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Analytic_AnalyticView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Analytic/AnalyticView */ \"./src/views/Analytic/AnalyticView.tsx\")).then((mod)=>mod.AnalyticView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Analytic/AnalyticView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Setting: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Setting_SettingView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Setting/SettingView */ \"./src/views/Setting/SettingView.tsx\")).then((mod)=>mod.SettingView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Setting/SettingView\"\n            ]\n        },\n        loading: LoadingComponent\n    })\n};\nconst HomePage = ()=>{\n    _s();\n    const [activeView, setActiveView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Dashboard\");\n    const [activeDepartment, setActiveDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"school\");\n    // Auto-scaling hook to fit content perfectly in viewport\n    const { containerRef } = (0,_src_hooks_useAutoScale__WEBPACK_IMPORTED_MODULE_9__.useAutoScale)({\n        targetWidth: 1920,\n        targetHeight: 1080,\n        minScale: 0.5,\n        maxScale: 1.0\n    });\n    const ActiveComponent = viewComponents[activeView] || viewComponents.Dashboard;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Eyes Shield UI\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"A futuristic, high-tech dashboard UI with a glassmorphism theme, displaying various data visualizations and system statuses on a semi-transparent, glowing panel.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1.0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                ref: containerRef,\n                className: \"main-background dashboard-container font-poppins text-[#E0E0E0] flex\",\n                style: {\n                    fontSize: \"clamp(10px, 0.7vw, 14px)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/60 backdrop-blur-xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10 bg-container-bg flex-1 flex border-0 min-h-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Sidebar__WEBPACK_IMPORTED_MODULE_4__.Sidebar, {\n                                    activeView: activeView,\n                                    setActiveView: (view)=>setActiveView(view)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 flex flex-col min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-2 py-1\",\n                                            style: {\n                                                padding: \"clamp(4px, 0.5vw, 16px)\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Header__WEBPACK_IMPORTED_MODULE_5__.Header, {\n                                                showSchoolButtons: activeView === \"School\",\n                                                activeDepartment: activeDepartment,\n                                                setActiveDepartment: setActiveDepartment\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-h-0 auto-fit-content px-2\",\n                                            style: {\n                                                padding: \"0 clamp(4px, 0.5vw, 16px)\"\n                                            },\n                                            children: activeView === \"School\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_views_School_SchoolView__WEBPACK_IMPORTED_MODULE_8__.SchoolView, {\n                                                activeDepartment: activeDepartment,\n                                                setActiveDepartment: setActiveDepartment\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveComponent, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-2 py-1\",\n                                            style: {\n                                                padding: \"clamp(4px, 0.5vw, 16px)\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Footer__WEBPACK_IMPORTED_MODULE_6__.Footer, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_RightSidebar__WEBPACK_IMPORTED_MODULE_7__.RightSidebar, {\n                                    activeView: activeView,\n                                    setActiveView: (view)=>setActiveView(view)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(HomePage, \"lZWvVB+WwEO/PKVXjTCA+9U7koE=\", false, function() {\n    return [\n        _src_hooks_useAutoScale__WEBPACK_IMPORTED_MODULE_9__.useAutoScale\n    ];\n});\n_c1 = HomePage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HomePage);\nvar _c, _c1;\n$RefreshReg$(_c, \"LoadingComponent\");\n$RefreshReg$(_c1, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9pbmRleC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDd0M7QUFDWDtBQUNNO0FBQ2lCO0FBQ2dCO0FBQ2xCO0FBQ1k7QUFDRjtBQUNIO0FBRXpELE1BQU1VLG1CQUFtQixrQkFBTSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFBMkQ7Ozs7OztLQUFuR0Y7QUFFTiw4REFBOEQ7QUFDOUQsTUFBTUcsaUJBQWlCO0lBQ3JCQyxXQUFXWCxtREFBT0EsQ0FBQyxJQUFNLHlOQUFPLENBQXdDWSxJQUFJLENBQUNDLENBQUFBLE1BQU9BLElBQUlDLGFBQWE7Ozs7OztRQUFLQyxTQUFTUjs7SUFDbkhTLFFBQVFoQixtREFBT0EsQ0FBQyxJQUFNLCtKQUFPLENBQWtDWSxJQUFJLENBQUNDLENBQUFBLE1BQU9BLElBQUlSLFVBQVU7Ozs7OztRQUFLVSxTQUFTUjs7SUFDdkdVLE1BQU1qQixtREFBT0EsQ0FBQyxJQUFNLDJMQUFPLENBQThCWSxJQUFJLENBQUNDLENBQUFBLE1BQU9BLElBQUlLLFFBQVE7Ozs7OztRQUFLSCxTQUFTUjs7SUFDL0ZZLFFBQVFuQixtREFBT0EsQ0FBQyxJQUFNLHVNQUFPLENBQWtDWSxJQUFJLENBQUNDLENBQUFBLE1BQU9BLElBQUlPLFVBQVU7Ozs7OztRQUFLTCxTQUFTUjs7SUFDdkdjLFdBQVdyQixtREFBT0EsQ0FBQyxJQUFNLHlOQUFPLENBQXdDWSxJQUFJLENBQUNDLENBQUFBLE1BQU9BLElBQUlTLGFBQWE7Ozs7OztRQUFLUCxTQUFTUjs7SUFDbkhnQixXQUFXdkIsbURBQU9BLENBQUMsSUFBTSx5TkFBTyxDQUF3Q1ksSUFBSSxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJVyxhQUFhOzs7Ozs7UUFBS1QsU0FBU1I7O0lBQ25Ia0IsVUFBVXpCLG1EQUFPQSxDQUFDLElBQU0sbU5BQU8sQ0FBc0NZLElBQUksQ0FBQ0MsQ0FBQUEsTUFBT0EsSUFBSWEsWUFBWTs7Ozs7O1FBQUtYLFNBQVNSOztJQUMvR29CLFNBQVMzQixtREFBT0EsQ0FBQyxJQUFNLDZNQUFPLENBQW9DWSxJQUFJLENBQUNDLENBQUFBLE1BQU9BLElBQUllLFdBQVc7Ozs7OztRQUFLYixTQUFTUjs7QUFDN0c7QUFJQSxNQUFNc0IsV0FBcUI7O0lBQ3pCLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHakMsK0NBQVFBLENBQVU7SUFDdEQsTUFBTSxDQUFDa0Msa0JBQWtCQyxvQkFBb0IsR0FBR25DLCtDQUFRQSxDQUFtQjtJQUUzRSx5REFBeUQ7SUFDekQsTUFBTSxFQUFFb0MsWUFBWSxFQUFFLEdBQUc1QixxRUFBWUEsQ0FBQztRQUNwQzZCLGFBQWE7UUFDYkMsY0FBYztRQUNkQyxVQUFVO1FBQ1ZDLFVBQVU7SUFDWjtJQUVBLE1BQU1DLGtCQUFrQjdCLGNBQWMsQ0FBQ29CLFdBQVcsSUFBSXBCLGVBQWVDLFNBQVM7SUFFOUUscUJBQ0U7OzBCQUNFLDhEQUFDWixrREFBSUE7O2tDQUNILDhEQUFDeUM7a0NBQU07Ozs7OztrQ0FDUCw4REFBQ0M7d0JBQUtDLE1BQUs7d0JBQWNDLFNBQVE7Ozs7OztrQ0FDakMsOERBQUNGO3dCQUFLQyxNQUFLO3dCQUFXQyxTQUFROzs7Ozs7Ozs7Ozs7MEJBRWhDLDhEQUFDQztnQkFDQ0MsS0FBS1g7Z0JBQ0x6QixXQUFVO2dCQUNWcUMsT0FBTztvQkFBRUMsVUFBVTtnQkFBMkI7O2tDQUU5Qyw4REFBQ3ZDO3dCQUFJQyxXQUFVOzs7Ozs7a0NBRWYsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNSLDREQUFPQTtvQ0FBQzZCLFlBQVlBO29DQUFZQyxlQUFlLENBQUNpQixPQUFTakIsY0FBY2lCOzs7Ozs7OENBQ3hFLDhEQUFDeEM7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs0Q0FBWXFDLE9BQU87Z0RBQUVHLFNBQVM7NENBQTBCO3NEQUNyRSw0RUFBQy9DLDBEQUFNQTtnREFDTGdELG1CQUFtQnBCLGVBQWU7Z0RBQ2xDRSxrQkFBa0JBO2dEQUNsQkMscUJBQXFCQTs7Ozs7Ozs7Ozs7c0RBR3pCLDhEQUFDekI7NENBQUlDLFdBQVU7NENBQXVDcUMsT0FBTztnREFBRUcsU0FBUzs0Q0FBNEI7c0RBQ2pHbkIsZUFBZSx5QkFDZCw4REFBQ3pCLG9FQUFVQTtnREFDVDJCLGtCQUFrQkE7Z0RBQ2xCQyxxQkFBcUJBOzs7OzswRUFHdkIsOERBQUNNOzs7Ozs7Ozs7O3NEQUdMLDhEQUFDL0I7NENBQUlDLFdBQVU7NENBQVlxQyxPQUFPO2dEQUFFRyxTQUFTOzRDQUEwQjtzREFDckUsNEVBQUM5QywwREFBTUE7Ozs7Ozs7Ozs7Ozs7Ozs7OENBR1gsOERBQUNDLHNFQUFZQTtvQ0FBQzBCLFlBQVlBO29DQUFZQyxlQUFlLENBQUNpQixPQUFTakIsY0FBY2lCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXpGO0dBM0RNbkI7O1FBS3FCdkIsaUVBQVlBOzs7TUFMakN1QjtBQTZETiwrREFBZUEsUUFBUUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9wYWdlcy9pbmRleC50c3g/MDdmZiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBIZWFkIGZyb20gJ25leHQvaGVhZCc7XG5pbXBvcnQgZHluYW1pYyBmcm9tICduZXh0L2R5bmFtaWMnO1xuaW1wb3J0IHsgU2lkZWJhciB9IGZyb20gJy4uL3NyYy9jb21wb25lbnRzL1NpZGViYXInO1xuaW1wb3J0IHsgSGVhZGVyLCBTY2hvb2xEZXBhcnRtZW50IH0gZnJvbSAnLi4vc3JjL2NvbXBvbmVudHMvSGVhZGVyJztcbmltcG9ydCB7IEZvb3RlciB9IGZyb20gJy4uL3NyYy9jb21wb25lbnRzL0Zvb3Rlcic7XG5pbXBvcnQgeyBSaWdodFNpZGViYXIgfSBmcm9tICcuLi9zcmMvY29tcG9uZW50cy9SaWdodFNpZGViYXInO1xuaW1wb3J0IHsgU2Nob29sVmlldyB9IGZyb20gJy4uL3NyYy92aWV3cy9TY2hvb2wvU2Nob29sVmlldyc7XG5pbXBvcnQgeyB1c2VBdXRvU2NhbGUgfSBmcm9tICcuLi9zcmMvaG9va3MvdXNlQXV0b1NjYWxlJztcblxuY29uc3QgTG9hZGluZ0NvbXBvbmVudCA9ICgpID0+IDxkaXYgY2xhc3NOYW1lPVwiZmxleC1ncm93IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHRleHQtY3lhbi00MDBcIj5Mb2FkaW5nIFZpZXcuLi48L2Rpdj47XG5cbi8vIER5bmFtaWNhbGx5IGltcG9ydCB2aWV3cyBmb3IgY29kZSBzcGxpdHRpbmcgYW5kIHBlcmZvcm1hbmNlXG5jb25zdCB2aWV3Q29tcG9uZW50cyA9IHtcbiAgRGFzaGJvYXJkOiBkeW5hbWljKCgpID0+IGltcG9ydCgnLi4vc3JjL3ZpZXdzL0Rhc2hib2FyZC9EYXNoYm9hcmRWaWV3JykudGhlbihtb2QgPT4gbW9kLkRhc2hib2FyZFZpZXcpLCB7IGxvYWRpbmc6IExvYWRpbmdDb21wb25lbnQgfSksXG4gIFNjaG9vbDogZHluYW1pYygoKSA9PiBpbXBvcnQoJy4uL3NyYy92aWV3cy9TY2hvb2wvU2Nob29sVmlldycpLnRoZW4obW9kID0+IG1vZC5TY2hvb2xWaWV3KSwgeyBsb2FkaW5nOiBMb2FkaW5nQ29tcG9uZW50IH0pLFxuICBUb29sOiBkeW5hbWljKCgpID0+IGltcG9ydCgnLi4vc3JjL3ZpZXdzL1Rvb2wvVG9vbFZpZXcnKS50aGVuKG1vZCA9PiBtb2QuVG9vbFZpZXcpLCB7IGxvYWRpbmc6IExvYWRpbmdDb21wb25lbnQgfSksXG4gIE1hcmtldDogZHluYW1pYygoKSA9PiBpbXBvcnQoJy4uL3NyYy92aWV3cy9NYXJrZXQvTWFya2V0VmlldycpLnRoZW4obW9kID0+IG1vZC5NYXJrZXRWaWV3KSwgeyBsb2FkaW5nOiBMb2FkaW5nQ29tcG9uZW50IH0pLFxuICBCb29rc3RvcmU6IGR5bmFtaWMoKCkgPT4gaW1wb3J0KCcuLi9zcmMvdmlld3MvQm9va3N0b3JlL0Jvb2tzdG9yZVZpZXcnKS50aGVuKG1vZCA9PiBtb2QuQm9va3N0b3JlVmlldyksIHsgbG9hZGluZzogTG9hZGluZ0NvbXBvbmVudCB9KSxcbiAgQ29uY2llcmdlOiBkeW5hbWljKCgpID0+IGltcG9ydCgnLi4vc3JjL3ZpZXdzL0NvbmNpZXJnZS9Db25jaWVyZ2VWaWV3JykudGhlbihtb2QgPT4gbW9kLkNvbmNpZXJnZVZpZXcpLCB7IGxvYWRpbmc6IExvYWRpbmdDb21wb25lbnQgfSksXG4gIEFuYWx5dGljOiBkeW5hbWljKCgpID0+IGltcG9ydCgnLi4vc3JjL3ZpZXdzL0FuYWx5dGljL0FuYWx5dGljVmlldycpLnRoZW4obW9kID0+IG1vZC5BbmFseXRpY1ZpZXcpLCB7IGxvYWRpbmc6IExvYWRpbmdDb21wb25lbnQgfSksXG4gIFNldHRpbmc6IGR5bmFtaWMoKCkgPT4gaW1wb3J0KCcuLi9zcmMvdmlld3MvU2V0dGluZy9TZXR0aW5nVmlldycpLnRoZW4obW9kID0+IG1vZC5TZXR0aW5nVmlldyksIHsgbG9hZGluZzogTG9hZGluZ0NvbXBvbmVudCB9KSxcbn07XG5cbnR5cGUgVmlld0tleSA9IGtleW9mIHR5cGVvZiB2aWV3Q29tcG9uZW50cztcblxuY29uc3QgSG9tZVBhZ2U6IFJlYWN0LkZDID0gKCkgPT4ge1xuICBjb25zdCBbYWN0aXZlVmlldywgc2V0QWN0aXZlVmlld10gPSB1c2VTdGF0ZTxWaWV3S2V5PignRGFzaGJvYXJkJyk7XG4gIGNvbnN0IFthY3RpdmVEZXBhcnRtZW50LCBzZXRBY3RpdmVEZXBhcnRtZW50XSA9IHVzZVN0YXRlPFNjaG9vbERlcGFydG1lbnQ+KCdzY2hvb2wnKTtcblxuICAvLyBBdXRvLXNjYWxpbmcgaG9vayB0byBmaXQgY29udGVudCBwZXJmZWN0bHkgaW4gdmlld3BvcnRcbiAgY29uc3QgeyBjb250YWluZXJSZWYgfSA9IHVzZUF1dG9TY2FsZSh7XG4gICAgdGFyZ2V0V2lkdGg6IDE5MjAsXG4gICAgdGFyZ2V0SGVpZ2h0OiAxMDgwLFxuICAgIG1pblNjYWxlOiAwLjUsXG4gICAgbWF4U2NhbGU6IDEuMFxuICB9KTtcblxuICBjb25zdCBBY3RpdmVDb21wb25lbnQgPSB2aWV3Q29tcG9uZW50c1thY3RpdmVWaWV3XSB8fCB2aWV3Q29tcG9uZW50cy5EYXNoYm9hcmQ7XG5cbiAgcmV0dXJuIChcbiAgICA8PlxuICAgICAgPEhlYWQ+XG4gICAgICAgIDx0aXRsZT5FeWVzIFNoaWVsZCBVSTwvdGl0bGU+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJkZXNjcmlwdGlvblwiIGNvbnRlbnQ9XCJBIGZ1dHVyaXN0aWMsIGhpZ2gtdGVjaCBkYXNoYm9hcmQgVUkgd2l0aCBhIGdsYXNzbW9ycGhpc20gdGhlbWUsIGRpc3BsYXlpbmcgdmFyaW91cyBkYXRhIHZpc3VhbGl6YXRpb25zIGFuZCBzeXN0ZW0gc3RhdHVzZXMgb24gYSBzZW1pLXRyYW5zcGFyZW50LCBnbG93aW5nIHBhbmVsLlwiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJ2aWV3cG9ydFwiIGNvbnRlbnQ9XCJ3aWR0aD1kZXZpY2Utd2lkdGgsIGluaXRpYWwtc2NhbGU9MS4wXCIgLz5cbiAgICAgIDwvSGVhZD5cbiAgICAgIDxtYWluXG4gICAgICAgIHJlZj17Y29udGFpbmVyUmVmfVxuICAgICAgICBjbGFzc05hbWU9XCJtYWluLWJhY2tncm91bmQgZGFzaGJvYXJkLWNvbnRhaW5lciBmb250LXBvcHBpbnMgdGV4dC1bI0UwRTBFMF0gZmxleFwiXG4gICAgICAgIHN0eWxlPXt7IGZvbnRTaXplOiAnY2xhbXAoMTBweCwgMC43dncsIDE0cHgpJyB9fVxuICAgICAgPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctYmxhY2svNjAgYmFja2Ryb3AtYmx1ci14bFwiPjwvZGl2PlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB6LTEwIGJnLWNvbnRhaW5lci1iZyBmbGV4LTEgZmxleCBib3JkZXItMCBtaW4taC0wXCI+XG4gICAgICAgICAgICA8U2lkZWJhciBhY3RpdmVWaWV3PXthY3RpdmVWaWV3fSBzZXRBY3RpdmVWaWV3PXsodmlldykgPT4gc2V0QWN0aXZlVmlldyh2aWV3IGFzIFZpZXdLZXkpfSAvPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBmbGV4LWNvbCBtaW4tdy0wXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtMiBweS0xXCIgc3R5bGU9e3sgcGFkZGluZzogJ2NsYW1wKDRweCwgMC41dncsIDE2cHgpJyB9fT5cbiAgICAgICAgICAgICAgICA8SGVhZGVyXG4gICAgICAgICAgICAgICAgICBzaG93U2Nob29sQnV0dG9ucz17YWN0aXZlVmlldyA9PT0gJ1NjaG9vbCd9XG4gICAgICAgICAgICAgICAgICBhY3RpdmVEZXBhcnRtZW50PXthY3RpdmVEZXBhcnRtZW50fVxuICAgICAgICAgICAgICAgICAgc2V0QWN0aXZlRGVwYXJ0bWVudD17c2V0QWN0aXZlRGVwYXJ0bWVudH1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLWgtMCBhdXRvLWZpdC1jb250ZW50IHB4LTJcIiBzdHlsZT17eyBwYWRkaW5nOiAnMCBjbGFtcCg0cHgsIDAuNXZ3LCAxNnB4KScgfX0+XG4gICAgICAgICAgICAgICAge2FjdGl2ZVZpZXcgPT09ICdTY2hvb2wnID8gKFxuICAgICAgICAgICAgICAgICAgPFNjaG9vbFZpZXdcbiAgICAgICAgICAgICAgICAgICAgYWN0aXZlRGVwYXJ0bWVudD17YWN0aXZlRGVwYXJ0bWVudH1cbiAgICAgICAgICAgICAgICAgICAgc2V0QWN0aXZlRGVwYXJ0bWVudD17c2V0QWN0aXZlRGVwYXJ0bWVudH1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgIDxBY3RpdmVDb21wb25lbnQgLz5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC0yIHB5LTFcIiBzdHlsZT17eyBwYWRkaW5nOiAnY2xhbXAoNHB4LCAwLjV2dywgMTZweCknIH19PlxuICAgICAgICAgICAgICAgIDxGb290ZXIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxSaWdodFNpZGViYXIgYWN0aXZlVmlldz17YWN0aXZlVmlld30gc2V0QWN0aXZlVmlldz17KHZpZXcpID0+IHNldEFjdGl2ZVZpZXcodmlldyBhcyBWaWV3S2V5KX0gLz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L21haW4+XG4gICAgPC8+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBIb21lUGFnZTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwiSGVhZCIsImR5bmFtaWMiLCJTaWRlYmFyIiwiSGVhZGVyIiwiRm9vdGVyIiwiUmlnaHRTaWRlYmFyIiwiU2Nob29sVmlldyIsInVzZUF1dG9TY2FsZSIsIkxvYWRpbmdDb21wb25lbnQiLCJkaXYiLCJjbGFzc05hbWUiLCJ2aWV3Q29tcG9uZW50cyIsIkRhc2hib2FyZCIsInRoZW4iLCJtb2QiLCJEYXNoYm9hcmRWaWV3IiwibG9hZGluZyIsIlNjaG9vbCIsIlRvb2wiLCJUb29sVmlldyIsIk1hcmtldCIsIk1hcmtldFZpZXciLCJCb29rc3RvcmUiLCJCb29rc3RvcmVWaWV3IiwiQ29uY2llcmdlIiwiQ29uY2llcmdlVmlldyIsIkFuYWx5dGljIiwiQW5hbHl0aWNWaWV3IiwiU2V0dGluZyIsIlNldHRpbmdWaWV3IiwiSG9tZVBhZ2UiLCJhY3RpdmVWaWV3Iiwic2V0QWN0aXZlVmlldyIsImFjdGl2ZURlcGFydG1lbnQiLCJzZXRBY3RpdmVEZXBhcnRtZW50IiwiY29udGFpbmVyUmVmIiwidGFyZ2V0V2lkdGgiLCJ0YXJnZXRIZWlnaHQiLCJtaW5TY2FsZSIsIm1heFNjYWxlIiwiQWN0aXZlQ29tcG9uZW50IiwidGl0bGUiLCJtZXRhIiwibmFtZSIsImNvbnRlbnQiLCJtYWluIiwicmVmIiwic3R5bGUiLCJmb250U2l6ZSIsInZpZXciLCJwYWRkaW5nIiwic2hvd1NjaG9vbEJ1dHRvbnMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./pages/index.tsx\n"));

/***/ })

});
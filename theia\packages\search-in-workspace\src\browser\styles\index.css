/********************************************************************************
 * Copyright (C) 2017-2018 <PERSON><PERSON> and others.
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License v. 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * This Source Code may also be made available under the following Secondary
 * Licenses when the conditions for such availability set forth in the Eclipse
 * Public License v. 2.0 are satisfied: GNU General Public License, version 2
 * with the GNU Classpath Exception which is available at
 * https://www.gnu.org/software/classpath/license.html.
 *
 * SPDX-License-Identifier: EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0
 ********************************************************************************/

#search-in-workspace {
  height: 100%;
  display: flex;
  flex-flow: column nowrap;
}

#search-in-workspace .theia-TreeContainer.empty {
  overflow: hidden;
}

.t-siw-search-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  flex: 1 1 auto;
}

.t-siw-search-container .theia-ExpansionToggle {
  padding-right: 4px;
  min-width: 6px;
}

.t-siw-search-container .theia-input {
  box-sizing: border-box;
  flex: 1;
  line-height: var(--theia-content-line-height);
  max-height: calc(2 * 3px + 7 * var(--theia-content-line-height));
  min-width: 16px;
  min-height: 26px;
  resize: none;
  width: 100%;
}

.t-siw-search-container #search-input-field:focus {
  border: none;
  outline: none;
}

.t-siw-search-container #search-input-field {
  background: none;
  border: none;
  padding-block: 2px;
}

.t-siw-search-container .searchHeader {
  padding: var(--theia-ui-padding)
           max(var(--theia-scrollbar-width), var(--theia-ui-padding))
           calc(var(--theia-ui-padding) * 2)
           0;
}

#theia-main-content-panel .t-siw-search-container .searchHeader {
  padding-top: 10px;
}

.t-siw-search-container .searchHeader .controls.button-container {
  height: var(--theia-content-line-height);
  margin-bottom: 5px;
}

.t-siw-search-container .searchHeader .search-field-container {
  background: var(--theia-input-background);
  border-style: solid;
  border-width: var(--theia-border-width);
  border-color: var(--theia-input-background);
  border-radius: 2px;
}

.t-siw-search-container .searchHeader .search-field-container.focused {
  border-color: var(--theia-focusBorder);
}

.t-siw-search-container .searchHeader .search-field {
  display: flex;
  align-items: center;
}

.t-siw-search-container .searchHeader .search-field:focus {
  border: none;
  outline: none;
}

.t-siw-search-container .searchHeader .search-field .option {
  opacity: 0.7;
  cursor: pointer;
}

.t-siw-search-container .searchHeader .search-field .option.enabled {
  color: var(--theia-inputOption-activeForeground);
  border: var(--theia-border-width) var(--theia-inputOption-activeBorder) solid;
  background-color: var(--theia-inputOption-activeBackground);
  opacity: 1;
}

.t-siw-search-container .searchHeader .search-field .option:hover {
  opacity: 1;
}

.t-siw-search-container .searchHeader .search-field .option-buttons {
  display: flex;
  align-items: center;
  align-self: flex-start;
  background-color: unset;
  margin: auto 2px;
}

.t-siw-search-container .searchHeader .search-field-container.tooManyResults {
  border-style: solid;
  border-width: var(--theia-border-width);
  border-color: var(--theia-inputValidation-warningBorder);
}

.t-siw-search-container
.searchHeader
.search-field-container
.search-notification {
  height: 0;
  display: none;
  width: 100%;
  position: relative;
}

.t-siw-search-container
.searchHeader
.search-field-container.focused
.search-notification.show {
  display: block;
}

.t-siw-search-container .searchHeader .search-notification div {
  background-color: var(--theia-inputValidation-warningBackground);
  width: calc(100% + 2px);
  margin-left: -1px;
  color: var(--theia-inputValidation-warningForeground);
  z-index: 1000;
  position: absolute;
  border: 1px solid var(--theia-inputValidation-warningBorder);
  box-sizing: border-box;
  padding: 3px;
}

.t-siw-search-container .searchHeader .button-container {
  text-align: center;
  display: flex;
  justify-content: center;
}

.t-siw-search-container .searchHeader .search-field .option,
.t-siw-search-container .searchHeader .button-container .btn {
  width: 21px;
  height: 21px;
  margin: 0 1px;
  display: inline-block;
  box-sizing: border-box;
  align-items: center;
  user-select: none;
  background-repeat: no-repeat;
  background-position: center;
  border: var(--theia-border-width) solid transparent;
}

.t-siw-search-container .searchHeader .search-field .fa.option {
  display: flex;
  align-items: center;
  justify-content: center;
}

.t-siw-search-container .searchHeader .search-details {
  position: relative;
  margin-top: var(--theia-ui-padding);
}

.t-siw-search-container .searchHeader .search-details .button-container {
  position: absolute;
  width: 25px;
  top: 0;
  right: 0;
  cursor: pointer;
}

.t-siw-search-container .searchHeader .glob-field-container.hidden {
  display: none;
}

.t-siw-search-container .searchHeader .glob-field-container .glob-field {
  margin-bottom: var(--theia-ui-padding);
  margin-left: calc(var(--theia-ui-padding) * 3);
  display: flex;
  flex-direction: column;
}

.t-siw-search-container .searchHeader .glob-field-container .glob-field .label {
  margin-bottom: 4px;
  user-select: none;
  font-size: var(--theia-ui-font-size0);
}

.t-siw-search-container
  .searchHeader
  .glob-field-container
  .glob-field
  .theia-input:not(:focus)::placeholder {
  color: transparent;
}

.t-siw-search-container .resultContainer {
  height: 100%;
}

.t-siw-search-container .result {
  overflow: hidden;
  width: 100%;
  flex: 1;
}

.t-siw-search-container .result .result-head {
  display: flex;
}

.t-siw-search-container .result .result-head .fa,
.t-siw-search-container .result .result-head .theia-file-icons-js {
  margin: 0 3px;
}

.t-siw-search-container .result .result-head .file-name {
  margin-right: 5px;
}

.t-siw-search-container .result .result-head .file-path {
  font-size: var(--theia-ui-font-size0);
  margin-left: 3px;
}

.t-siw-search-container .resultLine {
  flex-grow: 1;
}

.t-siw-search-container .resultLine .match {
  white-space: pre;
  background: var(--theia-editor-findMatchHighlightBackground);
  border: 1px solid var(--theia-editor-findMatchHighlightBorder);
}
.theia-hc .t-siw-search-container .resultLine .match {
  border-style: dashed;
}

.t-siw-search-container .resultLine .match.strike-through {
  text-decoration: line-through;
  background: var(--theia-diffEditor-removedTextBackground);
  border-color: var(--theia-diffEditor-removedTextBorder);
}

.t-siw-search-container .resultLine .replace-term {
  background: var(--theia-diffEditor-insertedTextBackground);
  border: 1px solid var(--theia-diffEditor-insertedTextBorder);
}
.theia-hc .t-siw-search-container .resultLine .replace-term {
  border-style: dashed;
}

.t-siw-search-container .match-line-num {
    font-size: .9em;
    margin-left: 7px;
    margin-right: 4px;
    opacity: .7;
}

.t-siw-search-container .result-head-info {
  align-items: center;
  display: inline-flex;
  flex-grow: 1;
}

.search-in-workspace-editor-match {
  background: var(--theia-editor-findMatchHighlightBackground);
}

.current-search-in-workspace-editor-match {
  background: var(--theia-editor-findMatchBackground);
}

.current-match-range-highlight {
  background: var(--theia-editor-findRangeHighlightBackground);
}

.result-node-buttons {
  display: none;
}

.theia-TreeNode:hover .result-node-buttons {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  align-self: center;
}

.theia-TreeNode:hover .result-head .notification-count-container {
  display: none;
}

.result-node-buttons > span {
  padding: 2px;
  margin-left: var(--theia-ui-padding);
  border-radius: 5px;
}

.result-node-buttons > span:hover {
  background-color: var(--theia-toolbar-hoverBackground);
}

.search-and-replace-container {
  display: flex;
}

.replace-toggle {
  display: flex;
  align-items: center;
  width: 14px;
  min-width: 14px;
  justify-content: center;
  margin-left: 2px;
  margin-right: 2px;
  box-sizing: border-box;
}

.theia-side-panel .replace-toggle .codicon {
  padding: 0px;
}

.replace-toggle:hover {
  background: rgba(50%, 50%, 50%, 0.2);
}

.search-and-replace-fields {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.replace-field {
  display: flex;
  margin-top: var(--theia-ui-padding);
  gap: var(--theia-ui-padding);
}

.replace-field.hidden {
  display: none;
}

.replace-all-button-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.result-node-buttons .replace-result {
  background-image: var(--theia-icon-replace);
}
.result-node-buttons .replace-all-result {
  background-image: var(--theia-icon-replace-all);
}

.replace-all-button-container .action-label.disabled {
  opacity: var(--theia-mod-disabled-opacity);
  background: transparent;
  cursor: default;
}

.highlighted-count-container {
  background-color: var(--theia-list-activeSelectionBackground);
  color: var(--theia-list-activeSelectionForeground);
}

.t-siw-search-container .searchHeader .search-info {
  color: var(--theia-descriptionForeground);
  margin-left: 18px;
  margin-top: 10px;
}

.theia-siw-lineNumber {
  opacity: 0.7;
  padding-right: 4px;
}

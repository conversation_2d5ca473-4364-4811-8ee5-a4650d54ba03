import { NextApiRequest, NextApiResponse } from 'next';
import mysql from 'mysql2/promise';

// Database connection configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'school_management',
  port: parseInt(process.env.DB_PORT || '3306'),
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const connection = await mysql.createConnection(dbConfig);

    try {
      // Insert departments
      await connection.execute(`
        INSERT IGNORE INTO departments (id, name, color, innovation) VALUES
        (1, 'AI & Robotics', 'bg-blue-500/20 text-blue-400', 'Neural Networks Lab'),
        (2, 'Quantum Computing', 'bg-green-500/20 text-green-400', 'Quantum Simulators'),
        (3, 'Bioengineering', 'bg-purple-500/20 text-purple-400', 'Gene Editing Lab'),
        (4, 'Space Sciences', 'bg-pink-500/20 text-pink-400', 'Mars Simulation'),
        (5, 'Metaverse Studies', 'bg-cyan-500/20 text-cyan-400', 'Virtual Reality Campus'),
        (6, 'Environmental Tech', 'bg-emerald-500/20 text-emerald-400', 'Carbon Neutral Systems')
      `);

      // Insert teachers
      await connection.execute(`
        INSERT IGNORE INTO teachers (id, name, role, department_id, ai_partner, efficiency_score) VALUES
        (1, 'Dr. Sarah Chen', 'AI Quantum Physics Director', 1, 'Einstein AI', 97.2),
        (2, 'Prof. Marcus Johnson', 'Neural Network Coordinator', 1, 'Turing AI', 94.8),
        (3, 'Dr. Elena Rodriguez', 'Bioengineering Head', 3, 'Darwin AI', 96.1),
        (4, 'Dr. James Wilson', 'Space Colonization Lead', 4, 'Hawking AI', 98.3),
        (5, 'Prof. Lisa Wang', 'Quantum Computing Director', 2, 'Schrödinger AI', 95.7),
        (6, 'Dr. Ahmed Hassan', 'Environmental Tech Lead', 6, 'Tesla AI', 93.4)
      `);

      // Insert students
      await connection.execute(`
        INSERT IGNORE INTO students (id, name, grade, gpa, ai_mentor, learning_style, emotional_state, neural_pathways) VALUES
        (1, 'Alex Chen', 'Grade 11', 3.94, 'Socrates AI', 'Visual-Kinesthetic', 'Motivated', 847),
        (2, 'Maya Patel', 'Grade 12', 3.87, 'Aristotle AI', 'Auditory-Logical', 'Focused', 923),
        (3, 'Jordan Smith', 'Grade 10', 3.76, 'Plato AI', 'Kinesthetic-Social', 'Curious', 756),
        (4, 'Zara Ahmed', 'Grade 11', 3.91, 'Confucius AI', 'Visual-Logical', 'Determined', 892),
        (5, 'Kai Nakamura', 'Grade 12', 3.98, 'Da Vinci AI', 'Creative-Analytical', 'Inspired', 1024)
      `);

      // Insert classes
      await connection.execute(`
        INSERT IGNORE INTO classes (id, name, department_id, teacher_id, ai_tutor, max_students) VALUES
        (1, 'Quantum Physics VR', 2, 5, 'Einstein AI', 28),
        (2, 'Neural Network Programming', 1, 2, 'Turing AI', 24),
        (3, 'Bioengineering Lab', 3, 3, 'Darwin AI', 32),
        (4, 'Space Colonization Design', 4, 4, 'Hawking AI', 20),
        (5, 'AI Ethics & Philosophy', 1, 1, 'Socrates AI', 30),
        (6, 'Environmental Quantum Systems', 6, 6, 'Tesla AI', 25)
      `);

      // Insert innovations
      await connection.execute(`
        INSERT IGNORE INTO innovations (id, title, icon, status, impact) VALUES
        (1, 'AI-Powered Personalized Learning', '🤖', 'Active', '84% improvement in learning outcomes'),
        (2, 'Holographic Classrooms', '🎭', 'Beta', '87% engagement increase'),
        (3, 'Blockchain Credentials', '⛓️', 'Live', '100% fraud prevention'),
        (4, 'Emotion Recognition System', '😊', 'Testing', '92% accuracy in mood detection'),
        (5, 'Quantum Computing Lab', '⚛️', 'Active', '340% faster problem solving'),
        (6, 'Neural Interface Learning', '🧠', 'Beta', 'Direct knowledge transfer')
      `);

      // Insert events
      await connection.execute(`
        INSERT IGNORE INTO events (id, title, date, type, participants) VALUES
        (1, 'Global VR Science Fair', '2024-03-25', 'Virtual Reality Meeting', 50000),
        (2, 'AI Ethics Symposium', '2024-03-30', 'Family Workshop', 25000),
        (3, 'Quantum Computing Workshop', '2024-04-05', 'Student Presentation', 15000),
        (4, 'Holographic Teacher Training', '2024-04-10', 'Professional Development', 5000),
        (5, 'Mars Colony Simulation', '2024-04-15', 'Immersive Experience', 8000)
      `);

      // Insert AI tutor sessions for today
      const today = new Date().toISOString().split('T')[0];
      await connection.execute(`
        INSERT IGNORE INTO ai_tutor_sessions (student_id, ai_tutor, subject, duration_minutes, effectiveness_score) VALUES
        (1, 'Einstein AI', 'Quantum Physics', 45, 9.2),
        (2, 'Turing AI', 'Neural Networks', 60, 8.8),
        (3, 'Darwin AI', 'Bioengineering', 50, 9.1),
        (4, 'Hawking AI', 'Space Sciences', 40, 9.5),
        (5, 'Socrates AI', 'Philosophy', 35, 8.9)
      `);

      // Insert student-department relationships
      await connection.execute(`
        INSERT IGNORE INTO student_departments (student_id, department_id) VALUES
        (1, 1), (1, 2), (2, 1), (2, 3), (3, 4), (3, 6), (4, 2), (4, 3), (5, 1), (5, 4)
      `);

      // Insert teacher-department relationships
      await connection.execute(`
        INSERT IGNORE INTO teacher_departments (teacher_id, department_id) VALUES
        (1, 1), (2, 1), (3, 3), (4, 4), (5, 2), (6, 6)
      `);

      res.status(200).json({ 
        message: 'Database seeded successfully with futuristic school data',
        data: {
          departments: 6,
          teachers: 6,
          students: 5,
          classes: 6,
          innovations: 6,
          events: 5,
          ai_sessions: 5
        }
      });

    } finally {
      await connection.end();
    }

  } catch (error) {
    console.error('Database seeding error:', error);
    res.status(500).json({ 
      message: 'Failed to seed database',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}

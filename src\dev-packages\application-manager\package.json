{"name": "@theia/application-manager", "version": "1.63.0", "description": "Theia application manager API.", "publishConfig": {"access": "public"}, "license": "EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0", "repository": {"type": "git", "url": "https://github.com/eclipse-theia/theia.git"}, "bugs": {"url": "https://github.com/eclipse-theia/theia/issues"}, "homepage": "https://github.com/eclipse-theia/theia", "files": ["lib", "src"], "main": "lib/index.js", "typings": "lib/index.d.ts", "scripts": {"build": "theiaext build", "clean": "theiaext clean", "compile": "theiaext compile", "lint": "theiaext lint", "test": "theiaext test", "watch": "theiaext watch"}, "dependencies": {"@babel/core": "^7.10.0", "@babel/plugin-transform-classes": "^7.10.0", "@babel/plugin-transform-runtime": "^7.10.0", "@babel/preset-env": "^7.10.0", "@theia/application-package": "1.63.0", "@theia/ffmpeg": "1.63.0", "@theia/native-webpack-plugin": "1.63.0", "@types/fs-extra": "^4.0.2", "@types/semver": "^7.5.0", "babel-loader": "^8.2.2", "buffer": "^6.0.3", "compression-webpack-plugin": "^9.0.0", "copy-webpack-plugin": "^8.1.1", "css-loader": "^6.2.0", "@electron/rebuild": "^3.7.2", "fs-extra": "^4.0.2", "http-server": "^14.1.1", "ignore-loader": "^0.1.2", "less": "^3.0.3", "mini-css-extract-plugin": "^2.6.1", "node-loader": "^2.0.0", "path-browserify": "^1.0.1", "semver": "^7.5.4", "source-map": "^0.6.1", "source-map-loader": "^2.0.1", "source-map-support": "^0.5.19", "style-loader": "^2.0.0", "tslib": "^2.6.2", "umd-compat-loader": "^2.1.2", "webpack": "^5.76.0", "webpack-cli": "4.7.0", "worker-loader": "^3.0.8", "yargs": "^15.3.1"}, "peerDependencies": {"@theia/electron": "*"}, "peerDependenciesMeta": {"@theia/electron": {"optional": true}}, "devDependencies": {"@theia/ext-scripts": "1.63.0"}, "nyc": {"extends": "../../configs/nyc.json"}, "gitHead": "21358137e41342742707f660b8e222f940a27652"}
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: function() { return /* binding */ Footer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CloudIcon_CogIcon_HomeIcon_ShieldCheckIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CloudIcon,CogIcon,HomeIcon,ShieldCheckIcon!=!@heroicons/react/24/solid */ \"__barrel_optimize__?names=BoltIcon,ChartBarIcon,CloudIcon,CogIcon,HomeIcon,ShieldCheckIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/solid/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\nconst Footer = ()=>{\n    _s();\n    const [activeButton, setActiveButton] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const footerButtons = [\n        {\n            id: \"home\",\n            icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CloudIcon_CogIcon_HomeIcon_ShieldCheckIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__.HomeIcon,\n            label: \"Home\",\n            color: \"text-blue-400\"\n        },\n        {\n            id: \"analytics\",\n            icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CloudIcon_CogIcon_HomeIcon_ShieldCheckIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__.ChartBarIcon,\n            label: \"Analytics\",\n            color: \"text-green-400\"\n        },\n        {\n            id: \"security\",\n            icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CloudIcon_CogIcon_HomeIcon_ShieldCheckIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__.ShieldCheckIcon,\n            label: \"Security\",\n            color: \"text-cyan-400\"\n        },\n        {\n            id: \"cloud\",\n            icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CloudIcon_CogIcon_HomeIcon_ShieldCheckIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__.CloudIcon,\n            label: \"Cloud\",\n            color: \"text-purple-400\"\n        },\n        {\n            id: \"performance\",\n            icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CloudIcon_CogIcon_HomeIcon_ShieldCheckIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__.BoltIcon,\n            label: \"Performance\",\n            color: \"text-yellow-400\"\n        },\n        {\n            id: \"settings\",\n            icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CloudIcon_CogIcon_HomeIcon_ShieldCheckIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__.CogIcon,\n            label: \"Settings\",\n            color: \"text-red-400\"\n        }\n    ];\n    const handleButtonClick = (buttonId)=>{\n        setActiveButton(buttonId);\n        // Add haptic feedback simulation\n        if (navigator.vibrate) {\n            navigator.vibrate(50);\n        }\n        // Reset active state after animation\n        setTimeout(()=>setActiveButton(null), 200);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-panel-bg border border-gray-700/50 responsive-border-radius flex items-center justify-between w-full h-full\",\n        style: {\n            padding: \"calc(var(--base-spacing) * 1.25)\",\n            fontSize: \"calc(var(--base-font-size) * 0.875)\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                style: {\n                    gap: \"calc(var(--base-gap) * 0.75)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        style: {\n                            gap: \"calc(var(--base-gap) * 0.5)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-400 rounded-full animate-pulse\",\n                                style: {\n                                    width: \"calc(var(--base-icon-size) * 0.25)\",\n                                    height: \"calc(var(--base-icon-size) * 0.25)\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                style: {\n                                    fontSize: \"calc(var(--base-font-size) * 0.7)\"\n                                },\n                                children: \"SYSTEM ONLINE\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        style: {\n                            gap: \"calc(var(--base-gap) * 0.5)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-400 rounded-full\",\n                                style: {\n                                    width: \"calc(var(--base-icon-size) * 0.25)\",\n                                    height: \"calc(var(--base-icon-size) * 0.25)\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                style: {\n                                    fontSize: \"calc(var(--base-font-size) * 0.7)\"\n                                },\n                                children: \"SECURE CONNECTION\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                style: {\n                    gap: \"calc(var(--base-gap) * 0.5)\"\n                },\n                children: footerButtons.map((button)=>{\n                    const IconComponent = button.icon;\n                    const isActive = activeButton === button.id;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>handleButtonClick(button.id),\n                        className: \"\\n                relative group flex flex-col items-center justify-center\\n                responsive-border-radius transition-all duration-200 ease-out\\n                \".concat(isActive ? \"bg-gray-700/80 scale-95 shadow-inner\" : \"bg-gray-800/40 hover:bg-gray-700/60 hover:scale-105\", \"\\n                border border-gray-600/30 hover:border-gray-500/50\\n                backdrop-blur-sm\\n              \"),\n                        style: {\n                            width: \"calc(var(--base-button-height) * 1.1)\",\n                            height: \"calc(var(--base-button-height) * 1.1)\"\n                        },\n                        title: button.label,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\\n                absolute inset-0 responsive-border-radius opacity-0 group-hover:opacity-20 transition-opacity duration-300\\n                bg-gradient-to-br from-white to-transparent\\n              \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                className: \"\\n                  transition-all duration-200\\n                  \".concat(isActive ? \"scale-90\" : \"group-hover:scale-110\", \"\\n                  \").concat(button.color, \"\\n                \"),\n                                style: {\n                                    width: \"calc(var(--base-icon-size) * 0.6)\",\n                                    height: \"calc(var(--base-icon-size) * 0.6)\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 15\n                            }, undefined),\n                            isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -bottom-1 w-1 h-1 bg-cyan-400 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, button.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: [\n                            \"CPU: \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-cyan-400\",\n                                children: \"45%\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 16\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: [\n                            \"RAM: \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-green-400\",\n                                children: \"68%\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 16\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: new Date().toLocaleTimeString([], {\n                            hour: \"2-digit\",\n                            minute: \"2-digit\"\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Footer, \"2IlxFF4hDMq20XkKpqpiCEEIzsY=\");\n_c = Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Footer.tsx\n"));

/***/ })

});
import { FrontendApplicationConfig } from '@theia/core/lib/browser/frontend-application';
import { PreferenceScope } from '@theia/core/lib/browser/preferences/preference-scope';

export const theiaConfig: FrontendApplicationConfig = {
  applicationName: '<PERSON><PERSON> IDE',
  defaultTheme: 'dark',
  defaultIconTheme: 'vs-seti',
  
  // Preferences configuration
  preferences: {
    'workbench.colorTheme': 'dark',
    'workbench.iconTheme': 'vs-seti',
    'editor.fontSize': 14,
    'editor.fontFamily': 'Monaco, Menlo, "Ubuntu Mono", monospace',
    'editor.tabSize': 2,
    'editor.insertSpaces': true,
    'editor.wordWrap': 'on',
    'editor.minimap.enabled': true,
    'editor.lineNumbers': 'on',
    'editor.renderWhitespace': 'selection',
    'editor.bracketPairColorization.enabled': true,
    'editor.guides.bracketPairs': true,
    'editor.guides.indentation': true,
    'editor.suggest.showKeywords': true,
    'editor.suggest.showSnippets': true,
    'editor.suggest.showFunctions': true,
    'editor.suggest.showConstructors': true,
    'editor.suggest.showFields': true,
    'editor.suggest.showVariables': true,
    'editor.suggest.showClasses': true,
    'editor.suggest.showStructs': true,
    'editor.suggest.showInterfaces': true,
    'editor.suggest.showModules': true,
    'editor.suggest.showProperties': true,
    'editor.suggest.showEvents': true,
    'editor.suggest.showOperators': true,
    'editor.suggest.showUnits': true,
    'editor.suggest.showValues': true,
    'editor.suggest.showConstants': true,
    'editor.suggest.showEnums': true,
    'editor.suggest.showEnumMembers': true,
    'editor.suggest.showColors': true,
    'editor.suggest.showFiles': true,
    'editor.suggest.showReferences': true,
    'editor.suggest.showFolders': true,
    'editor.suggest.showTypeParameters': true,
    
    // Terminal preferences
    'terminal.fontSize': 14,
    'terminal.fontFamily': 'Monaco, Menlo, "Ubuntu Mono", monospace',
    'terminal.cursorStyle': 'block',
    'terminal.cursorBlink': true,
    
    // File explorer preferences
    'files.enableTrash': false,
    'files.autoSave': 'afterDelay',
    'files.autoSaveDelay': 1000,
    'files.exclude': {
      '**/.git': true,
      '**/.svn': true,
      '**/.hg': true,
      '**/CVS': true,
      '**/.DS_Store': true,
      '**/node_modules': true,
      '**/.next': true,
      '**/dist': true,
      '**/build': true
    },
    
    // Search preferences
    'search.exclude': {
      '**/node_modules': true,
      '**/bower_components': true,
      '**/.next': true,
      '**/dist': true,
      '**/build': true
    },
    
    // Git preferences
    'git.enabled': true,
    'git.autorefresh': true,
    'git.autofetch': true,
    
    // Debug preferences
    'debug.console.fontSize': 14,
    'debug.console.fontFamily': 'Monaco, Menlo, "Ubuntu Mono", monospace',
    
    // AI preferences
    'ai.chat.enabled': true,
    'ai.codeCompletion.enabled': true,
    'ai.editor.enabled': true,
    'ai.terminal.enabled': true,
    
    // Security preferences
    'security.workspace.trust.enabled': false,
    
    // Performance preferences
    'workbench.editor.limit.enabled': true,
    'workbench.editor.limit.value': 10,
    'workbench.editor.limit.perEditorGroup': true,
    
    // Layout preferences
    'workbench.activityBar.visible': true,
    'workbench.statusBar.visible': true,
    'workbench.sideBar.location': 'left',
    'workbench.panel.defaultLocation': 'bottom',
    'workbench.editor.showTabs': true,
    'workbench.editor.tabCloseButton': 'right',
    'workbench.editor.tabSizing': 'fit',
    
    // Extension preferences
    'extensions.autoUpdate': false,
    'extensions.autoCheckUpdates': false
  },
  
  // Backend configuration
  backend: {
    config: {
      frontendConnectionTimeout: 3000,
      startupTimeout: 30000
    }
  },
  
  // Plugin configuration
  plugins: {
    local: true,
    remote: false
  },
  
  // Workspace configuration
  workspace: {
    supportMultiRootWorkspace: true,
    maxRecentWorkspaces: 10
  }
};

export const theiaModules = [
  // Core modules
  '@theia/core/lib/browser/frontend-application-module',
  '@theia/core/lib/browser/messaging/messaging-frontend-module',
  '@theia/core/lib/browser/logger-frontend-module',
  '@theia/core/lib/browser/menu/menu-frontend-module',
  '@theia/keymaps/lib/browser/keymaps-frontend-module',
  
  // Editor modules
  '@theia/monaco/lib/browser/monaco-frontend-module',
  '@theia/editor/lib/browser/editor-frontend-module',
  '@theia/editor-preview/lib/browser/editor-preview-frontend-module',
  
  // File system modules
  '@theia/filesystem/lib/browser/filesystem-frontend-module',
  '@theia/workspace/lib/browser/workspace-frontend-module',
  '@theia/navigator/lib/browser/navigator-frontend-module',
  
  // Terminal module
  '@theia/terminal/lib/browser/terminal-frontend-module',
  
  // UI modules
  '@theia/outline-view/lib/browser/outline-view-frontend-module',
  '@theia/preferences/lib/browser/preferences-frontend-module',
  '@theia/toolbar/lib/browser/toolbar-frontend-module',
  '@theia/property-view/lib/browser/property-view-frontend-module',
  '@theia/messages/lib/browser/messages-frontend-module',
  '@theia/output/lib/browser/output-frontend-module',
  '@theia/markers/lib/browser/markers-frontend-module',
  
  // Search modules
  '@theia/file-search/lib/browser/file-search-frontend-module',
  '@theia/search-in-workspace/lib/browser/search-in-workspace-frontend-module',
  
  // Version control modules
  '@theia/scm/lib/browser/scm-frontend-module',
  '@theia/scm-extra/lib/browser/scm-extra-frontend-module',
  
  // Debug and task modules
  '@theia/debug/lib/browser/debug-frontend-module',
  '@theia/task/lib/browser/task-frontend-module',
  
  // Plugin modules
  '@theia/plugin-ext/lib/browser/plugin-ext-frontend-module',
  '@theia/plugin-ext-vscode/lib/browser/plugin-ext-vscode-frontend-module',
  
  // AI modules
  '@theia/ai-core/lib/browser/ai-core-frontend-module',
  '@theia/ai-chat/lib/browser/ai-chat-frontend-module',
  '@theia/ai-chat-ui/lib/browser/ai-chat-ui-frontend-module',
  '@theia/ai-code-completion/lib/browser/ai-code-completion-frontend-module',
  '@theia/ai-editor/lib/browser/ai-editor-frontend-module',
  '@theia/ai-terminal/lib/browser/ai-terminal-frontend-module',
  '@theia/ai-openai/lib/browser/ai-openai-frontend-module',
  '@theia/ai-anthropic/lib/browser/ai-anthropic-frontend-module',
  '@theia/ai-google/lib/browser/ai-google-frontend-module',
  
  // Additional modules
  '@theia/getting-started/lib/browser/getting-started-frontend-module',
  '@theia/mini-browser/lib/browser/mini-browser-frontend-module',
  '@theia/preview/lib/browser/preview-frontend-module',
  '@theia/callhierarchy/lib/browser/callhierarchy-frontend-module',
  '@theia/typehierarchy/lib/browser/typehierarchy-frontend-module',
  '@theia/timeline/lib/browser/timeline-frontend-module',
  '@theia/notebook/lib/browser/notebook-frontend-module',
  '@theia/memory-inspector/lib/browser/memory-inspector-frontend-module',
  '@theia/metrics/lib/browser/metrics-frontend-module',
  '@theia/bulk-edit/lib/browser/bulk-edit-frontend-module',
  '@theia/collaboration/lib/browser/collaboration-frontend-module',
  '@theia/console/lib/browser/console-frontend-module',
  '@theia/dev-container/lib/browser/dev-container-frontend-module',
  '@theia/remote/lib/browser/remote-frontend-module',
  '@theia/scanoss/lib/browser/scanoss-frontend-module',
  '@theia/secondary-window/lib/browser/secondary-window-frontend-module',
  '@theia/test/lib/browser/test-frontend-module',
  '@theia/userstorage/lib/browser/userstorage-frontend-module',
  '@theia/variable-resolver/lib/browser/variable-resolver-frontend-module',
  '@theia/vsx-registry/lib/browser/vsx-registry-frontend-module'
];

export default theiaConfig;


export type FeaturedProductData = {
    image: string;
    category: string;
    name: string;
    price: string;
};

export type MarketStatsData = {
    marketCap: string;
    volume24h: string;
    topMover: {
        name: string;
        change: string;
        isUp: boolean;
    };
};

export const getMarketData = () => {
    const featuredProduct: FeaturedProductData = {
        image: "https://picsum.photos/seed/gadget/400/200",
        category: "Featured Item",
        name: "Cybernetic Arm MK. IV",
        price: "Ƀ 1.25"
    };

    const marketStats: MarketStatsData = {
        marketCap: "Ƀ 1.2T",
        volume24h: "Ƀ 84.5B",
        topMover: {
            name: "NEURO",
            change: "+18%",
            isUp: true
        }
    };
    
    return { featuredProduct, marketStats };
};

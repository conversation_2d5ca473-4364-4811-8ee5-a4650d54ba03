{"name": "@theia/terminal", "version": "1.63.0", "description": "Theia - Terminal Extension", "dependencies": {"@theia/core": "1.63.0", "@theia/editor": "1.63.0", "@theia/file-search": "1.63.0", "@theia/filesystem": "1.63.0", "@theia/process": "1.63.0", "@theia/variable-resolver": "1.63.0", "@theia/workspace": "1.63.0", "tslib": "^2.6.2", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-search": "^0.13.0"}, "publishConfig": {"access": "public"}, "theiaExtensions": [{"frontend": "lib/browser/terminal-frontend-module", "secondaryWindow": "lib/browser/terminal-frontend-module", "backend": "lib/node/terminal-backend-module"}], "keywords": ["theia-extension"], "license": "EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0", "repository": {"type": "git", "url": "https://github.com/eclipse-theia/theia.git"}, "bugs": {"url": "https://github.com/eclipse-theia/theia/issues"}, "homepage": "https://github.com/eclipse-theia/theia", "files": ["lib", "src"], "scripts": {"build": "theiaext build", "clean": "theiaext clean", "compile": "theiaext compile", "lint": "theiaext lint", "test": "theiaext test", "watch": "theiaext watch"}, "devDependencies": {"@theia/ext-scripts": "1.63.0"}, "nyc": {"extends": "../../configs/nyc.json"}, "gitHead": "21358137e41342742707f660b8e222f940a27652"}
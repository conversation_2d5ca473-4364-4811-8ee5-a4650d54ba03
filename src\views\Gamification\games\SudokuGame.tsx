import React, { useEffect, useRef } from 'react';
import Phaser from 'phaser';

interface SudokuGameProps {
  onBack: () => void;
}

class SudokuScene extends Phaser.Scene {
  private grid: number[][];
  private solution: number[][];
  private cells: Phaser.GameObjects.Rectangle[][];
  private texts: Phaser.GameObjects.Text[][];
  private selectedCell: { row: number; col: number } | null = null;

  constructor() {
    super({ key: 'SudokuScene' });
    this.grid = [];
    this.solution = [];
    this.cells = [];
    this.texts = [];
  }

  preload() {
    // No assets needed for basic Sudoku
  }

  create() {
    this.createGrid();
    this.generatePuzzle();
    this.drawGrid();
    this.createNumberButtons();
    this.createControls();
  }

  createGrid() {
    // Initialize 9x9 grid
    for (let i = 0; i < 9; i++) {
      this.grid[i] = [];
      this.solution[i] = [];
      this.cells[i] = [];
      this.texts[i] = [];
      for (let j = 0; j < 9; j++) {
        this.grid[i][j] = 0;
        this.solution[i][j] = 0;
      }
    }
  }

  generatePuzzle() {
    // Simple puzzle generation (in real implementation, use proper algorithm)
    const puzzle = [
      [5, 3, 0, 0, 7, 0, 0, 0, 0],
      [6, 0, 0, 1, 9, 5, 0, 0, 0],
      [0, 9, 8, 0, 0, 0, 0, 6, 0],
      [8, 0, 0, 0, 6, 0, 0, 0, 3],
      [4, 0, 0, 8, 0, 3, 0, 0, 1],
      [7, 0, 0, 0, 2, 0, 0, 0, 6],
      [0, 6, 0, 0, 0, 0, 2, 8, 0],
      [0, 0, 0, 4, 1, 9, 0, 0, 5],
      [0, 0, 0, 0, 8, 0, 0, 7, 9]
    ];

    const solution = [
      [5, 3, 4, 6, 7, 8, 9, 1, 2],
      [6, 7, 2, 1, 9, 5, 3, 4, 8],
      [1, 9, 8, 3, 4, 2, 5, 6, 7],
      [8, 5, 9, 7, 6, 1, 4, 2, 3],
      [4, 2, 6, 8, 5, 3, 7, 9, 1],
      [7, 1, 3, 9, 2, 4, 8, 5, 6],
      [9, 6, 1, 5, 3, 7, 2, 8, 4],
      [2, 8, 7, 4, 1, 9, 6, 3, 5],
      [3, 4, 5, 2, 8, 6, 1, 7, 9]
    ];

    this.grid = puzzle.map(row => [...row]);
    this.solution = solution.map(row => [...row]);
  }

  drawGrid() {
    const cellSize = 50;
    const startX = 100;
    const startY = 50;

    for (let row = 0; row < 9; row++) {
      for (let col = 0; col < 9; col++) {
        const x = startX + col * cellSize;
        const y = startY + row * cellSize;

        // Cell background
        const cell = this.add.rectangle(x, y, cellSize - 2, cellSize - 2, 0x2a2a3a);
        cell.setStrokeStyle(2, 0x4a5568);
        cell.setInteractive();
        
        // Thicker borders for 3x3 boxes
        if (row % 3 === 0) cell.setStrokeStyle(3, 0x64748b, 1, 0.5);
        if (col % 3 === 0) cell.setStrokeStyle(3, 0x64748b, 0.5, 1);
        if (row % 3 === 0 && col % 3 === 0) cell.setStrokeStyle(3, 0x64748b);

        this.cells[row][col] = cell;

        // Cell text
        const text = this.add.text(x, y, '', {
          fontSize: '24px',
          color: this.grid[row][col] === 0 ? '#60a5fa' : '#f8fafc',
          fontFamily: 'Arial'
        });
        text.setOrigin(0.5);
        
        if (this.grid[row][col] !== 0) {
          text.setText(this.grid[row][col].toString());
        }

        this.texts[row][col] = text;

        // Cell click handler
        cell.on('pointerdown', () => {
          this.selectCell(row, col);
        });
      }
    }
  }

  selectCell(row: number, col: number) {
    // Clear previous selection
    if (this.selectedCell) {
      this.cells[this.selectedCell.row][this.selectedCell.col].setFillStyle(0x2a2a3a);
    }

    // Select new cell (only if it's empty in original puzzle)
    if (this.grid[row][col] === 0) {
      this.selectedCell = { row, col };
      this.cells[row][col].setFillStyle(0x3b82f6);
    } else {
      this.selectedCell = null;
    }
  }

  createNumberButtons() {
    const buttonY = 550;
    const buttonSize = 40;
    const startX = 150;

    for (let num = 1; num <= 9; num++) {
      const x = startX + (num - 1) * (buttonSize + 10);
      
      const button = this.add.rectangle(x, buttonY, buttonSize, buttonSize, 0x374151);
      button.setStrokeStyle(2, 0x6b7280);
      button.setInteractive();

      const text = this.add.text(x, buttonY, num.toString(), {
        fontSize: '20px',
        color: '#f8fafc',
        fontFamily: 'Arial'
      });
      text.setOrigin(0.5);

      button.on('pointerdown', () => {
        this.placeNumber(num);
      });

      button.on('pointerover', () => {
        button.setFillStyle(0x4b5563);
      });

      button.on('pointerout', () => {
        button.setFillStyle(0x374151);
      });
    }

    // Clear button
    const clearButton = this.add.rectangle(startX + 9 * 50, buttonY, 60, buttonSize, 0x7f1d1d);
    clearButton.setStrokeStyle(2, 0x991b1b);
    clearButton.setInteractive();

    const clearText = this.add.text(startX + 9 * 50, buttonY, 'Clear', {
      fontSize: '16px',
      color: '#f8fafc',
      fontFamily: 'Arial'
    });
    clearText.setOrigin(0.5);

    clearButton.on('pointerdown', () => {
      this.clearCell();
    });
  }

  createControls() {
    // Check solution button
    const checkButton = this.add.rectangle(400, 600, 120, 40, 0x059669);
    checkButton.setStrokeStyle(2, 0x047857);
    checkButton.setInteractive();

    const checkText = this.add.text(400, 600, 'Check Solution', {
      fontSize: '16px',
      color: '#f8fafc',
      fontFamily: 'Arial'
    });
    checkText.setOrigin(0.5);

    checkButton.on('pointerdown', () => {
      this.checkSolution();
    });

    // New game button
    const newButton = this.add.rectangle(550, 600, 100, 40, 0x7c3aed);
    newButton.setStrokeStyle(2, 0x6d28d9);
    newButton.setInteractive();

    const newText = this.add.text(550, 600, 'New Game', {
      fontSize: '16px',
      color: '#f8fafc',
      fontFamily: 'Arial'
    });
    newText.setOrigin(0.5);

    newButton.on('pointerdown', () => {
      this.scene.restart();
    });
  }

  placeNumber(num: number) {
    if (this.selectedCell) {
      const { row, col } = this.selectedCell;
      
      // Update grid
      this.grid[row][col] = num;
      
      // Update display
      this.texts[row][col].setText(num.toString());
      this.texts[row][col].setColor('#60a5fa');
    }
  }

  clearCell() {
    if (this.selectedCell) {
      const { row, col } = this.selectedCell;
      
      // Clear grid
      this.grid[row][col] = 0;
      
      // Clear display
      this.texts[row][col].setText('');
    }
  }

  checkSolution() {
    let isComplete = true;
    let isCorrect = true;

    for (let row = 0; row < 9; row++) {
      for (let col = 0; col < 9; col++) {
        if (this.grid[row][col] === 0) {
          isComplete = false;
        } else if (this.grid[row][col] !== this.solution[row][col]) {
          isCorrect = false;
          // Highlight incorrect cells
          this.cells[row][col].setFillStyle(0xdc2626);
        }
      }
    }

    if (isComplete && isCorrect) {
      this.add.text(400, 50, 'Congratulations! Puzzle Solved!', {
        fontSize: '24px',
        color: '#10b981',
        fontFamily: 'Arial'
      }).setOrigin(0.5);
    } else if (!isCorrect) {
      this.add.text(400, 50, 'Some numbers are incorrect!', {
        fontSize: '20px',
        color: '#ef4444',
        fontFamily: 'Arial'
      }).setOrigin(0.5);
    } else {
      this.add.text(400, 50, 'Puzzle not complete yet!', {
        fontSize: '20px',
        color: '#f59e0b',
        fontFamily: 'Arial'
      }).setOrigin(0.5);
    }
  }
}

export const SudokuGame: React.FC<SudokuGameProps> = ({ onBack }) => {
  const gameRef = useRef<HTMLDivElement>(null);
  const phaserGameRef = useRef<Phaser.Game | null>(null);

  useEffect(() => {
    if (gameRef.current && !phaserGameRef.current) {
      const config: Phaser.Types.Core.GameConfig = {
        type: Phaser.AUTO,
        width: 800,
        height: 650,
        parent: gameRef.current,
        backgroundColor: '#1a1f2e',
        scene: SudokuScene,
        physics: {
          default: 'arcade',
          arcade: {
            debug: false
          }
        }
      };

      phaserGameRef.current = new Phaser.Game(config);
    }

    return () => {
      if (phaserGameRef.current) {
        phaserGameRef.current.destroy(true);
        phaserGameRef.current = null;
      }
    };
  }, []);

  return (
    <div className="h-full bg-panel-bg rounded-xl p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-white text-2xl font-bold">🔢 Sudoku Master</h2>
        <button
          onClick={onBack}
          className="bg-gray-800/60 hover:bg-gray-700/60 text-white px-4 py-2 rounded-lg transition-colors duration-200"
        >
          ← Back to Games
        </button>
      </div>
      
      <div className="flex justify-center">
        <div ref={gameRef} className="border border-gray-700/50 rounded-lg overflow-hidden" />
      </div>
    </div>
  );
};

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/mood-analysis";
exports.ids = ["pages/api/mood-analysis"];
exports.modules = {

/***/ "@google/genai":
/*!********************************!*\
  !*** external "@google/genai" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("@google/genai");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fmood-analysis&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cmood-analysis.ts&middlewareConfigBase64=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fmood-analysis&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cmood-analysis.ts&middlewareConfigBase64=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_mood_analysis_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\mood-analysis.ts */ \"(api)/./pages/api/mood-analysis.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_mood_analysis_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_mood_analysis_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/mood-analysis\",\n        pathname: \"/api/mood-analysis\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_mood_analysis_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fmood-analysis&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cmood-analysis.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./backend/lib/gemini.ts":
/*!*******************************!*\
  !*** ./backend/lib/gemini.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Type: () => (/* reexport safe */ _google_genai__WEBPACK_IMPORTED_MODULE_0__.Type),\n/* harmony export */   generateStructuredData: () => (/* binding */ generateStructuredData),\n/* harmony export */   generateText: () => (/* binding */ generateText)\n/* harmony export */ });\n/* harmony import */ var _google_genai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/genai */ \"@google/genai\");\n/* harmony import */ var _google_genai__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_google_genai__WEBPACK_IMPORTED_MODULE_0__);\n\nconst hasValidApiKey =  false && 0;\nlet ai = null;\nif (hasValidApiKey) {\n    ai = new _google_genai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenAI({\n        apiKey: \"your_actual_gemini_api_key_here\"\n    });\n} else {\n    console.warn(\"⚠️  No valid Gemini API key found. Using fallback mock data. Set GEMINI_API_KEY in .env.local to use real AI features.\");\n}\nasync function generateText(prompt) {\n    if (!ai) {\n        // Fallback mock response for mood analysis\n        return \"System analysis indicates elevated stress levels detected. Recommend implementing relaxation protocols and scheduling wellness check-in within 2 hours.\";\n    }\n    try {\n        const response = await ai.models.generateContent({\n            model: \"gemini-2.5-flash\",\n            contents: prompt\n        });\n        return response.text;\n    } catch (e) {\n        console.error(\"Error generating text from Gemini:\", e);\n        // Return fallback data instead of throwing error\n        return \"System analysis temporarily unavailable. Default wellness protocols active. Please check back later for detailed analysis.\";\n    }\n}\nasync function generateStructuredData(prompt, schema) {\n    if (!ai) {\n        // Return fallback mock data for dashboard widgets\n        const fallbackData = {\n            contacts: [\n                {\n                    name: \"Dr. Aris Thorne\",\n                    detail: \"Chief Scientist\",\n                    avatar: \"aris\"\n                },\n                {\n                    name: \"Commander Zara Vex\",\n                    detail: \"Security Director\",\n                    avatar: \"zara\"\n                },\n                {\n                    name: \"Engineer Kai Nexus\",\n                    detail: \"Systems Architect\",\n                    avatar: \"kai\"\n                }\n            ],\n            safetyItems: [\n                {\n                    label: \"Firewall Active\",\n                    color: \"#00FFFF\"\n                },\n                {\n                    label: \"Quantum Encryption\",\n                    color: \"#00FFFF\"\n                },\n                {\n                    label: \"Bio-Scanner Online\",\n                    color: \"#3b82f6\"\n                },\n                {\n                    label: \"Sub-routine Anomaly\",\n                    color: \"#ef4444\"\n                },\n                {\n                    label: \"Neural Link Stable\",\n                    color: \"#3b82f6\"\n                },\n                {\n                    label: \"Core Temperature\",\n                    color: \"#3b82f6\"\n                }\n            ],\n            safetyChartData: [\n                85,\n                92,\n                78,\n                95,\n                88,\n                91\n            ],\n            systemRequests: [\n                {\n                    text: \"Security Patch Update\"\n                },\n                {\n                    text: \"Cryo-chamber diagnostics\"\n                },\n                {\n                    text: \"Neural interface calibration\"\n                }\n            ],\n            systemStats: [\n                {\n                    icon: \"BuildingOfficeIcon\",\n                    value: \"1597\",\n                    label: \"Processes\"\n                },\n                {\n                    icon: \"PuzzlePieceIcon\",\n                    value: \"84%\",\n                    label: \"RAM Usage\"\n                },\n                {\n                    icon: \"SnowflakeIcon\",\n                    value: \"24\\xb0C\",\n                    label: \"Core Temp\"\n                },\n                {\n                    icon: \"MemoryChipIcon\",\n                    value: \"2.4TB\",\n                    label: \"Storage\"\n                },\n                {\n                    icon: \"CogIcon\",\n                    value: \"99.7%\",\n                    label: \"Uptime\"\n                }\n            ]\n        };\n        return fallbackData;\n    }\n    try {\n        const response = await ai.models.generateContent({\n            model: \"gemini-2.5-flash\",\n            contents: prompt,\n            config: {\n                responseMimeType: \"application/json\",\n                responseSchema: schema\n            }\n        });\n        if (!response.text) {\n            throw new Error(\"Received an empty response from the AI.\");\n        }\n        const cleanedText = response.text.trim().replace(/^```json\\s*|```\\s*$/g, \"\");\n        if (!cleanedText) {\n            throw new Error(\"Received an empty JSON response from the AI after cleaning.\");\n        }\n        return JSON.parse(cleanedText);\n    } catch (e) {\n        console.error(\"Failed to generate or parse Gemini JSON response:\", e);\n        // Return fallback data instead of throwing error\n        const fallbackData = {\n            contacts: [\n                {\n                    name: \"Dr. Aris Thorne\",\n                    detail: \"Chief Scientist\",\n                    avatar: \"aris\"\n                },\n                {\n                    name: \"Commander Zara Vex\",\n                    detail: \"Security Director\",\n                    avatar: \"zara\"\n                },\n                {\n                    name: \"Engineer Kai Nexus\",\n                    detail: \"Systems Architect\",\n                    avatar: \"kai\"\n                }\n            ],\n            safetyItems: [\n                {\n                    label: \"Firewall Active\",\n                    color: \"#00FFFF\"\n                },\n                {\n                    label: \"Quantum Encryption\",\n                    color: \"#00FFFF\"\n                },\n                {\n                    label: \"Bio-Scanner Online\",\n                    color: \"#3b82f6\"\n                },\n                {\n                    label: \"Sub-routine Anomaly\",\n                    color: \"#ef4444\"\n                },\n                {\n                    label: \"Neural Link Stable\",\n                    color: \"#3b82f6\"\n                },\n                {\n                    label: \"Core Temperature\",\n                    color: \"#3b82f6\"\n                }\n            ],\n            safetyChartData: [\n                85,\n                92,\n                78,\n                95,\n                88,\n                91\n            ],\n            systemRequests: [\n                {\n                    text: \"Security Patch Update\"\n                },\n                {\n                    text: \"Cryo-chamber diagnostics\"\n                },\n                {\n                    text: \"Neural interface calibration\"\n                }\n            ],\n            systemStats: [\n                {\n                    icon: \"BuildingOfficeIcon\",\n                    value: \"1597\",\n                    label: \"Processes\"\n                },\n                {\n                    icon: \"PuzzlePieceIcon\",\n                    value: \"84%\",\n                    label: \"RAM Usage\"\n                },\n                {\n                    icon: \"SnowflakeIcon\",\n                    value: \"24\\xb0C\",\n                    label: \"Core Temp\"\n                },\n                {\n                    icon: \"MemoryChipIcon\",\n                    value: \"2.4TB\",\n                    label: \"Storage\"\n                },\n                {\n                    icon: \"CogIcon\",\n                    value: \"99.7%\",\n                    label: \"Uptime\"\n                }\n            ]\n        };\n        return fallbackData;\n    }\n}\n // Re-export Type for convenience\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./backend/lib/gemini.ts\n");

/***/ }),

/***/ "(api)/./pages/api/mood-analysis.ts":
/*!************************************!*\
  !*** ./pages/api/mood-analysis.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../backend/lib/gemini */ \"(api)/./backend/lib/gemini.ts\");\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        res.setHeader(\"Allow\", [\n            \"POST\"\n        ]);\n        return res.status(405).end(`Method ${req.method} Not Allowed`);\n    }\n    try {\n        const mockDashboardData = `\n      - Past Prelesss Gauge: High Stress\n      - Wellness Tracker: High activity, low recovery\n      - Safety Checklist: Reports 'Anxiety' and a 'Missed Meeting'\n    `;\n        const prompt = `You are a futuristic AI assistant named 'FAYLUR' providing real-time status updates inside a high-tech UI. Based on the following user data, provide a concise, one-sentence analysis of their current wellness and mood state. Keep the tone slightly robotic but helpful. Data: ${mockDashboardData}`;\n        const analysis = await (0,_backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.generateText)(prompt);\n        res.status(200).json({\n            analysis\n        });\n    } catch (error) {\n        console.error(error);\n        const errorMessage = error instanceof Error ? error.message : \"An unknown error occurred.\";\n        res.status(500).json({\n            error: `Analysis failed: ${errorMessage}`\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/mood-analysis.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0"], () => (__webpack_exec__("(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fmood-analysis&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cmood-analysis.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();
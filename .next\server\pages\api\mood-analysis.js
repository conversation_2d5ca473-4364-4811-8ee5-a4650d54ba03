"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/mood-analysis";
exports.ids = ["pages/api/mood-analysis"];
exports.modules = {

/***/ "@google/genai":
/*!********************************!*\
  !*** external "@google/genai" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("@google/genai");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fmood-analysis&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cmood-analysis.ts&middlewareConfigBase64=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fmood-analysis&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cmood-analysis.ts&middlewareConfigBase64=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_mood_analysis_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\mood-analysis.ts */ \"(api)/./pages/api/mood-analysis.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_mood_analysis_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_mood_analysis_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/mood-analysis\",\n        pathname: \"/api/mood-analysis\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_mood_analysis_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fmood-analysis&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cmood-analysis.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./backend/lib/gemini.ts":
/*!*******************************!*\
  !*** ./backend/lib/gemini.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Type: () => (/* reexport safe */ _google_genai__WEBPACK_IMPORTED_MODULE_0__.Type),\n/* harmony export */   generateStructuredData: () => (/* binding */ generateStructuredData),\n/* harmony export */   generateText: () => (/* binding */ generateText)\n/* harmony export */ });\n/* harmony import */ var _google_genai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/genai */ \"@google/genai\");\n/* harmony import */ var _google_genai__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_google_genai__WEBPACK_IMPORTED_MODULE_0__);\n\nif (!process.env.API_KEY) {\n    throw new Error(\"API key not found. Please set the API_KEY environment variable.\");\n}\nconst ai = new _google_genai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenAI({\n    apiKey: process.env.API_KEY\n});\nasync function generateText(prompt) {\n    try {\n        const response = await ai.models.generateContent({\n            model: \"gemini-2.5-flash\",\n            contents: prompt\n        });\n        return response.text;\n    } catch (e) {\n        console.error(\"Error generating text from Gemini:\", e);\n        throw new Error(\"Failed to generate text from AI.\");\n    }\n}\nasync function generateStructuredData(prompt, schema) {\n    try {\n        const response = await ai.models.generateContent({\n            model: \"gemini-2.5-flash\",\n            contents: prompt,\n            config: {\n                responseMimeType: \"application/json\",\n                responseSchema: schema\n            }\n        });\n        if (!response.text) {\n            throw new Error(\"Received an empty response from the AI.\");\n        }\n        const cleanedText = response.text.trim().replace(/^```json\\s*|```\\s*$/g, \"\");\n        if (!cleanedText) {\n            throw new Error(\"Received an empty JSON response from the AI after cleaning.\");\n        }\n        return JSON.parse(cleanedText);\n    } catch (e) {\n        console.error(\"Failed to generate or parse Gemini JSON response:\", e);\n        if (e instanceof Error && e.message.startsWith(\"Received an empty\")) {\n            throw e;\n        }\n        throw new Error(\"Failed to process structured data from AI.\");\n    }\n}\n // Re-export Type for convenience\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9iYWNrZW5kL2xpYi9nZW1pbmkudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFDaUY7QUFFakYsSUFBSSxDQUFDRSxRQUFRQyxHQUFHLENBQUNDLE9BQU8sRUFBRTtJQUN4QixNQUFNLElBQUlDLE1BQU07QUFDbEI7QUFFQSxNQUFNQyxLQUFLLElBQUlOLHNEQUFXQSxDQUFDO0lBQUVPLFFBQVFMLFFBQVFDLEdBQUcsQ0FBQ0MsT0FBTztBQUFDO0FBRWxELGVBQWVJLGFBQWFDLE1BQWM7SUFDL0MsSUFBSTtRQUNGLE1BQU1DLFdBQW9DLE1BQU1KLEdBQUdLLE1BQU0sQ0FBQ0MsZUFBZSxDQUFDO1lBQ3hFQyxPQUFPO1lBQ1BDLFVBQVVMO1FBQ1o7UUFDQSxPQUFPQyxTQUFTSyxJQUFJO0lBQ3RCLEVBQUUsT0FBTUMsR0FBRztRQUNUQyxRQUFRQyxLQUFLLENBQUMsc0NBQXNDRjtRQUNwRCxNQUFNLElBQUlYLE1BQU07SUFDbEI7QUFDRjtBQUVPLGVBQWVjLHVCQUEwQlYsTUFBYyxFQUFFVyxNQUFXO0lBQ3pFLElBQUk7UUFDRixNQUFNVixXQUFvQyxNQUFNSixHQUFHSyxNQUFNLENBQUNDLGVBQWUsQ0FBQztZQUN4RUMsT0FBTztZQUNQQyxVQUFVTDtZQUNWWSxRQUFRO2dCQUNOQyxrQkFBa0I7Z0JBQ2xCQyxnQkFBZ0JIO1lBQ2xCO1FBQ0Y7UUFFQSxJQUFJLENBQUNWLFNBQVNLLElBQUksRUFBRTtZQUNoQixNQUFNLElBQUlWLE1BQU07UUFDcEI7UUFFQSxNQUFNbUIsY0FBY2QsU0FBU0ssSUFBSSxDQUFDVSxJQUFJLEdBQUdDLE9BQU8sQ0FBQyx3QkFBd0I7UUFFekUsSUFBSSxDQUFDRixhQUFhO1lBQ2QsTUFBTSxJQUFJbkIsTUFBTTtRQUNwQjtRQUVBLE9BQU9zQixLQUFLQyxLQUFLLENBQUNKO0lBQ3BCLEVBQUUsT0FBT1IsR0FBRztRQUNWQyxRQUFRQyxLQUFLLENBQUMscURBQXFERjtRQUNsRSxJQUFJQSxhQUFhWCxTQUFTVyxFQUFFYSxPQUFPLENBQUNDLFVBQVUsQ0FBQyxzQkFBc0I7WUFDbEUsTUFBTWQ7UUFDVjtRQUNBLE1BQU0sSUFBSVgsTUFBTTtJQUNsQjtBQUNGO0FBRWdCLENBQUMsaUNBQWlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXllcy1zaGllbGQtZnV0dXJpc3RpYy11aS8uL2JhY2tlbmQvbGliL2dlbWluaS50cz9jMzBmIl0sInNvdXJjZXNDb250ZW50IjpbIlxuaW1wb3J0IHsgR29vZ2xlR2VuQUksIEdlbmVyYXRlQ29udGVudFJlc3BvbnNlLCBUeXBlLCBQYXJ0IH0gZnJvbSAnQGdvb2dsZS9nZW5haSc7XG5cbmlmICghcHJvY2Vzcy5lbnYuQVBJX0tFWSkge1xuICB0aHJvdyBuZXcgRXJyb3IoXCJBUEkga2V5IG5vdCBmb3VuZC4gUGxlYXNlIHNldCB0aGUgQVBJX0tFWSBlbnZpcm9ubWVudCB2YXJpYWJsZS5cIik7XG59XG5cbmNvbnN0IGFpID0gbmV3IEdvb2dsZUdlbkFJKHsgYXBpS2V5OiBwcm9jZXNzLmVudi5BUElfS0VZIH0pO1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2VuZXJhdGVUZXh0KHByb21wdDogc3RyaW5nKTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCByZXNwb25zZTogR2VuZXJhdGVDb250ZW50UmVzcG9uc2UgPSBhd2FpdCBhaS5tb2RlbHMuZ2VuZXJhdGVDb250ZW50KHtcbiAgICAgIG1vZGVsOiAnZ2VtaW5pLTIuNS1mbGFzaCcsXG4gICAgICBjb250ZW50czogcHJvbXB0LFxuICAgIH0pO1xuICAgIHJldHVybiByZXNwb25zZS50ZXh0O1xuICB9IGNhdGNoKGUpIHtcbiAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZ2VuZXJhdGluZyB0ZXh0IGZyb20gR2VtaW5pOlwiLCBlKTtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJGYWlsZWQgdG8gZ2VuZXJhdGUgdGV4dCBmcm9tIEFJLlwiKTtcbiAgfVxufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2VuZXJhdGVTdHJ1Y3R1cmVkRGF0YTxUPihwcm9tcHQ6IHN0cmluZywgc2NoZW1hOiBhbnkpOiBQcm9taXNlPFQ+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCByZXNwb25zZTogR2VuZXJhdGVDb250ZW50UmVzcG9uc2UgPSBhd2FpdCBhaS5tb2RlbHMuZ2VuZXJhdGVDb250ZW50KHtcbiAgICAgIG1vZGVsOiBcImdlbWluaS0yLjUtZmxhc2hcIixcbiAgICAgIGNvbnRlbnRzOiBwcm9tcHQsXG4gICAgICBjb25maWc6IHtcbiAgICAgICAgcmVzcG9uc2VNaW1lVHlwZTogXCJhcHBsaWNhdGlvbi9qc29uXCIsXG4gICAgICAgIHJlc3BvbnNlU2NoZW1hOiBzY2hlbWEsXG4gICAgICB9LFxuICAgIH0pO1xuXG4gICAgaWYgKCFyZXNwb25zZS50ZXh0KSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIlJlY2VpdmVkIGFuIGVtcHR5IHJlc3BvbnNlIGZyb20gdGhlIEFJLlwiKTtcbiAgICB9XG4gICAgXG4gICAgY29uc3QgY2xlYW5lZFRleHQgPSByZXNwb25zZS50ZXh0LnRyaW0oKS5yZXBsYWNlKC9eYGBganNvblxccyp8YGBgXFxzKiQvZywgJycpO1xuXG4gICAgaWYgKCFjbGVhbmVkVGV4dCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJSZWNlaXZlZCBhbiBlbXB0eSBKU09OIHJlc3BvbnNlIGZyb20gdGhlIEFJIGFmdGVyIGNsZWFuaW5nLlwiKTtcbiAgICB9XG5cbiAgICByZXR1cm4gSlNPTi5wYXJzZShjbGVhbmVkVGV4dCkgYXMgVDtcbiAgfSBjYXRjaCAoZSkge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJGYWlsZWQgdG8gZ2VuZXJhdGUgb3IgcGFyc2UgR2VtaW5pIEpTT04gcmVzcG9uc2U6XCIsIGUpO1xuICAgICBpZiAoZSBpbnN0YW5jZW9mIEVycm9yICYmIGUubWVzc2FnZS5zdGFydHNXaXRoKFwiUmVjZWl2ZWQgYW4gZW1wdHlcIikpIHtcbiAgICAgICAgdGhyb3cgZTtcbiAgICB9XG4gICAgdGhyb3cgbmV3IEVycm9yKFwiRmFpbGVkIHRvIHByb2Nlc3Mgc3RydWN0dXJlZCBkYXRhIGZyb20gQUkuXCIpO1xuICB9XG59XG5cbmV4cG9ydCB7IFR5cGUgfTsgLy8gUmUtZXhwb3J0IFR5cGUgZm9yIGNvbnZlbmllbmNlXG4iXSwibmFtZXMiOlsiR29vZ2xlR2VuQUkiLCJUeXBlIiwicHJvY2VzcyIsImVudiIsIkFQSV9LRVkiLCJFcnJvciIsImFpIiwiYXBpS2V5IiwiZ2VuZXJhdGVUZXh0IiwicHJvbXB0IiwicmVzcG9uc2UiLCJtb2RlbHMiLCJnZW5lcmF0ZUNvbnRlbnQiLCJtb2RlbCIsImNvbnRlbnRzIiwidGV4dCIsImUiLCJjb25zb2xlIiwiZXJyb3IiLCJnZW5lcmF0ZVN0cnVjdHVyZWREYXRhIiwic2NoZW1hIiwiY29uZmlnIiwicmVzcG9uc2VNaW1lVHlwZSIsInJlc3BvbnNlU2NoZW1hIiwiY2xlYW5lZFRleHQiLCJ0cmltIiwicmVwbGFjZSIsIkpTT04iLCJwYXJzZSIsIm1lc3NhZ2UiLCJzdGFydHNXaXRoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./backend/lib/gemini.ts\n");

/***/ }),

/***/ "(api)/./pages/api/mood-analysis.ts":
/*!************************************!*\
  !*** ./pages/api/mood-analysis.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../backend/lib/gemini */ \"(api)/./backend/lib/gemini.ts\");\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        res.setHeader(\"Allow\", [\n            \"POST\"\n        ]);\n        return res.status(405).end(`Method ${req.method} Not Allowed`);\n    }\n    try {\n        const mockDashboardData = `\n      - Past Prelesss Gauge: High Stress\n      - Wellness Tracker: High activity, low recovery\n      - Safety Checklist: Reports 'Anxiety' and a 'Missed Meeting'\n    `;\n        const prompt = `You are a futuristic AI assistant named 'FAYLUR' providing real-time status updates inside a high-tech UI. Based on the following user data, provide a concise, one-sentence analysis of their current wellness and mood state. Keep the tone slightly robotic but helpful. Data: ${mockDashboardData}`;\n        const analysis = await (0,_backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.generateText)(prompt);\n        res.status(200).json({\n            analysis\n        });\n    } catch (error) {\n        console.error(error);\n        const errorMessage = error instanceof Error ? error.message : \"An unknown error occurred.\";\n        res.status(500).json({\n            error: `Analysis failed: ${errorMessage}`\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9wYWdlcy9hcGkvbW9vZC1hbmFseXNpcy50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUV3RDtBQU96QyxlQUFlQyxRQUM1QkMsR0FBbUIsRUFDbkJDLEdBQWtDO0lBRWxDLElBQUlELElBQUlFLE1BQU0sS0FBSyxRQUFRO1FBQ3pCRCxJQUFJRSxTQUFTLENBQUMsU0FBUztZQUFDO1NBQU87UUFDL0IsT0FBT0YsSUFBSUcsTUFBTSxDQUFDLEtBQUtDLEdBQUcsQ0FBQyxDQUFDLE9BQU8sRUFBRUwsSUFBSUUsTUFBTSxDQUFDLFlBQVksQ0FBQztJQUMvRDtJQUVBLElBQUk7UUFDRixNQUFNSSxvQkFBb0IsQ0FBQzs7OztJQUkzQixDQUFDO1FBQ0QsTUFBTUMsU0FBUyxDQUFDLGtSQUFrUixFQUFFRCxrQkFBa0IsQ0FBQztRQUV2VCxNQUFNRSxXQUFXLE1BQU1WLGlFQUFZQSxDQUFDUztRQUNwQ04sSUFBSUcsTUFBTSxDQUFDLEtBQUtLLElBQUksQ0FBQztZQUFFRDtRQUFTO0lBQ2xDLEVBQUUsT0FBT0UsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUNBO1FBQ2QsTUFBTUUsZUFBZUYsaUJBQWlCRyxRQUFRSCxNQUFNSSxPQUFPLEdBQUc7UUFDOURiLElBQUlHLE1BQU0sQ0FBQyxLQUFLSyxJQUFJLENBQUM7WUFBRUMsT0FBTyxDQUFDLGlCQUFpQixFQUFFRSxhQUFhLENBQUM7UUFBQztJQUNuRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXllcy1zaGllbGQtZnV0dXJpc3RpYy11aS8uL3BhZ2VzL2FwaS9tb29kLWFuYWx5c2lzLnRzP2U5YTEiXSwic291cmNlc0NvbnRlbnQiOlsiXG5pbXBvcnQgdHlwZSB7IE5leHRBcGlSZXF1ZXN0LCBOZXh0QXBpUmVzcG9uc2UgfSBmcm9tICduZXh0JztcbmltcG9ydCB7IGdlbmVyYXRlVGV4dCB9IGZyb20gJy4uLy4uL2JhY2tlbmQvbGliL2dlbWluaSc7XG5cbnR5cGUgUmVzcG9uc2VEYXRhID0ge1xuICBhbmFseXNpcz86IHN0cmluZztcbiAgZXJyb3I/OiBzdHJpbmc7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBhc3luYyBmdW5jdGlvbiBoYW5kbGVyKFxuICByZXE6IE5leHRBcGlSZXF1ZXN0LFxuICByZXM6IE5leHRBcGlSZXNwb25zZTxSZXNwb25zZURhdGE+XG4pIHtcbiAgaWYgKHJlcS5tZXRob2QgIT09ICdQT1NUJykge1xuICAgIHJlcy5zZXRIZWFkZXIoJ0FsbG93JywgWydQT1NUJ10pO1xuICAgIHJldHVybiByZXMuc3RhdHVzKDQwNSkuZW5kKGBNZXRob2QgJHtyZXEubWV0aG9kfSBOb3QgQWxsb3dlZGApO1xuICB9XG5cbiAgdHJ5IHtcbiAgICBjb25zdCBtb2NrRGFzaGJvYXJkRGF0YSA9IGBcbiAgICAgIC0gUGFzdCBQcmVsZXNzcyBHYXVnZTogSGlnaCBTdHJlc3NcbiAgICAgIC0gV2VsbG5lc3MgVHJhY2tlcjogSGlnaCBhY3Rpdml0eSwgbG93IHJlY292ZXJ5XG4gICAgICAtIFNhZmV0eSBDaGVja2xpc3Q6IFJlcG9ydHMgJ0FueGlldHknIGFuZCBhICdNaXNzZWQgTWVldGluZydcbiAgICBgO1xuICAgIGNvbnN0IHByb21wdCA9IGBZb3UgYXJlIGEgZnV0dXJpc3RpYyBBSSBhc3Npc3RhbnQgbmFtZWQgJ0ZBWUxVUicgcHJvdmlkaW5nIHJlYWwtdGltZSBzdGF0dXMgdXBkYXRlcyBpbnNpZGUgYSBoaWdoLXRlY2ggVUkuIEJhc2VkIG9uIHRoZSBmb2xsb3dpbmcgdXNlciBkYXRhLCBwcm92aWRlIGEgY29uY2lzZSwgb25lLXNlbnRlbmNlIGFuYWx5c2lzIG9mIHRoZWlyIGN1cnJlbnQgd2VsbG5lc3MgYW5kIG1vb2Qgc3RhdGUuIEtlZXAgdGhlIHRvbmUgc2xpZ2h0bHkgcm9ib3RpYyBidXQgaGVscGZ1bC4gRGF0YTogJHttb2NrRGFzaGJvYXJkRGF0YX1gO1xuICAgIFxuICAgIGNvbnN0IGFuYWx5c2lzID0gYXdhaXQgZ2VuZXJhdGVUZXh0KHByb21wdCk7XG4gICAgcmVzLnN0YXR1cygyMDApLmpzb24oeyBhbmFseXNpcyB9KTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKGVycm9yKTtcbiAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdBbiB1bmtub3duIGVycm9yIG9jY3VycmVkLic7XG4gICAgcmVzLnN0YXR1cyg1MDApLmpzb24oeyBlcnJvcjogYEFuYWx5c2lzIGZhaWxlZDogJHtlcnJvck1lc3NhZ2V9YCB9KTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbImdlbmVyYXRlVGV4dCIsImhhbmRsZXIiLCJyZXEiLCJyZXMiLCJtZXRob2QiLCJzZXRIZWFkZXIiLCJzdGF0dXMiLCJlbmQiLCJtb2NrRGFzaGJvYXJkRGF0YSIsInByb21wdCIsImFuYWx5c2lzIiwianNvbiIsImVycm9yIiwiY29uc29sZSIsImVycm9yTWVzc2FnZSIsIkVycm9yIiwibWVzc2FnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./pages/api/mood-analysis.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0"], () => (__webpack_exec__("(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fmood-analysis&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cmood-analysis.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();
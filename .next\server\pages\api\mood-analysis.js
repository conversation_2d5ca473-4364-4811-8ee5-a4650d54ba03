"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/mood-analysis";
exports.ids = ["pages/api/mood-analysis"];
exports.modules = {

/***/ "@google/genai":
/*!********************************!*\
  !*** external "@google/genai" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("@google/genai");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fmood-analysis&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cmood-analysis.ts&middlewareConfigBase64=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fmood-analysis&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cmood-analysis.ts&middlewareConfigBase64=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_mood_analysis_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\mood-analysis.ts */ \"(api)/./pages/api/mood-analysis.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_mood_analysis_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_mood_analysis_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/mood-analysis\",\n        pathname: \"/api/mood-analysis\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_mood_analysis_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fmood-analysis&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cmood-analysis.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./backend/lib/gemini.ts":
/*!*******************************!*\
  !*** ./backend/lib/gemini.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Type: () => (/* reexport safe */ _google_genai__WEBPACK_IMPORTED_MODULE_0__.Type),\n/* harmony export */   generateStructuredData: () => (/* binding */ generateStructuredData),\n/* harmony export */   generateText: () => (/* binding */ generateText)\n/* harmony export */ });\n/* harmony import */ var _google_genai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/genai */ \"@google/genai\");\n/* harmony import */ var _google_genai__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_google_genai__WEBPACK_IMPORTED_MODULE_0__);\n\nif (false) {}\nconst ai = new _google_genai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenAI({\n    apiKey: \"your_actual_gemini_api_key_here\"\n});\nasync function generateText(prompt) {\n    try {\n        const response = await ai.models.generateContent({\n            model: \"gemini-2.5-flash\",\n            contents: prompt\n        });\n        return response.text;\n    } catch (e) {\n        console.error(\"Error generating text from Gemini:\", e);\n        throw new Error(\"Failed to generate text from AI.\");\n    }\n}\nasync function generateStructuredData(prompt, schema) {\n    try {\n        const response = await ai.models.generateContent({\n            model: \"gemini-2.5-flash\",\n            contents: prompt,\n            config: {\n                responseMimeType: \"application/json\",\n                responseSchema: schema\n            }\n        });\n        if (!response.text) {\n            throw new Error(\"Received an empty response from the AI.\");\n        }\n        const cleanedText = response.text.trim().replace(/^```json\\s*|```\\s*$/g, \"\");\n        if (!cleanedText) {\n            throw new Error(\"Received an empty JSON response from the AI after cleaning.\");\n        }\n        return JSON.parse(cleanedText);\n    } catch (e) {\n        console.error(\"Failed to generate or parse Gemini JSON response:\", e);\n        if (e instanceof Error && e.message.startsWith(\"Received an empty\")) {\n            throw e;\n        }\n        throw new Error(\"Failed to process structured data from AI.\");\n    }\n}\n // Re-export Type for convenience\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9iYWNrZW5kL2xpYi9nZW1pbmkudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFDaUY7QUFFakYsSUFBSSxLQUEyQixFQUFFLEVBRWhDO0FBRUQsTUFBTU0sS0FBSyxJQUFJTixzREFBV0EsQ0FBQztJQUFFTyxRQUFRTCxpQ0FBMEI7QUFBQztBQUV6RCxlQUFlTSxhQUFhQyxNQUFjO0lBQy9DLElBQUk7UUFDRixNQUFNQyxXQUFvQyxNQUFNSixHQUFHSyxNQUFNLENBQUNDLGVBQWUsQ0FBQztZQUN4RUMsT0FBTztZQUNQQyxVQUFVTDtRQUNaO1FBQ0EsT0FBT0MsU0FBU0ssSUFBSTtJQUN0QixFQUFFLE9BQU1DLEdBQUc7UUFDVEMsUUFBUUMsS0FBSyxDQUFDLHNDQUFzQ0Y7UUFDcEQsTUFBTSxJQUFJWCxNQUFNO0lBQ2xCO0FBQ0Y7QUFFTyxlQUFlYyx1QkFBMEJWLE1BQWMsRUFBRVcsTUFBVztJQUN6RSxJQUFJO1FBQ0YsTUFBTVYsV0FBb0MsTUFBTUosR0FBR0ssTUFBTSxDQUFDQyxlQUFlLENBQUM7WUFDeEVDLE9BQU87WUFDUEMsVUFBVUw7WUFDVlksUUFBUTtnQkFDTkMsa0JBQWtCO2dCQUNsQkMsZ0JBQWdCSDtZQUNsQjtRQUNGO1FBRUEsSUFBSSxDQUFDVixTQUFTSyxJQUFJLEVBQUU7WUFDaEIsTUFBTSxJQUFJVixNQUFNO1FBQ3BCO1FBRUEsTUFBTW1CLGNBQWNkLFNBQVNLLElBQUksQ0FBQ1UsSUFBSSxHQUFHQyxPQUFPLENBQUMsd0JBQXdCO1FBRXpFLElBQUksQ0FBQ0YsYUFBYTtZQUNkLE1BQU0sSUFBSW5CLE1BQU07UUFDcEI7UUFFQSxPQUFPc0IsS0FBS0MsS0FBSyxDQUFDSjtJQUNwQixFQUFFLE9BQU9SLEdBQUc7UUFDVkMsUUFBUUMsS0FBSyxDQUFDLHFEQUFxREY7UUFDbEUsSUFBSUEsYUFBYVgsU0FBU1csRUFBRWEsT0FBTyxDQUFDQyxVQUFVLENBQUMsc0JBQXNCO1lBQ2xFLE1BQU1kO1FBQ1Y7UUFDQSxNQUFNLElBQUlYLE1BQU07SUFDbEI7QUFDRjtBQUVnQixDQUFDLGlDQUFpQyIsInNvdXJjZXMiOlsid2VicGFjazovL2V5ZXMtc2hpZWxkLWZ1dHVyaXN0aWMtdWkvLi9iYWNrZW5kL2xpYi9nZW1pbmkudHM/YzMwZiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmltcG9ydCB7IEdvb2dsZUdlbkFJLCBHZW5lcmF0ZUNvbnRlbnRSZXNwb25zZSwgVHlwZSwgUGFydCB9IGZyb20gJ0Bnb29nbGUvZ2VuYWknO1xuXG5pZiAoIXByb2Nlc3MuZW52LkdFTUlOSV9BUElfS0VZKSB7XG4gIHRocm93IG5ldyBFcnJvcihcIkFQSSBrZXkgbm90IGZvdW5kLiBQbGVhc2Ugc2V0IHRoZSBHRU1JTklfQVBJX0tFWSBlbnZpcm9ubWVudCB2YXJpYWJsZS5cIik7XG59XG5cbmNvbnN0IGFpID0gbmV3IEdvb2dsZUdlbkFJKHsgYXBpS2V5OiBwcm9jZXNzLmVudi5HRU1JTklfQVBJX0tFWSB9KTtcblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdlbmVyYXRlVGV4dChwcm9tcHQ6IHN0cmluZyk6IFByb21pc2U8c3RyaW5nPiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzcG9uc2U6IEdlbmVyYXRlQ29udGVudFJlc3BvbnNlID0gYXdhaXQgYWkubW9kZWxzLmdlbmVyYXRlQ29udGVudCh7XG4gICAgICBtb2RlbDogJ2dlbWluaS0yLjUtZmxhc2gnLFxuICAgICAgY29udGVudHM6IHByb21wdCxcbiAgICB9KTtcbiAgICByZXR1cm4gcmVzcG9uc2UudGV4dDtcbiAgfSBjYXRjaChlKSB7XG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIGdlbmVyYXRpbmcgdGV4dCBmcm9tIEdlbWluaTpcIiwgZSk7XG4gICAgdGhyb3cgbmV3IEVycm9yKFwiRmFpbGVkIHRvIGdlbmVyYXRlIHRleHQgZnJvbSBBSS5cIik7XG4gIH1cbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdlbmVyYXRlU3RydWN0dXJlZERhdGE8VD4ocHJvbXB0OiBzdHJpbmcsIHNjaGVtYTogYW55KTogUHJvbWlzZTxUPiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzcG9uc2U6IEdlbmVyYXRlQ29udGVudFJlc3BvbnNlID0gYXdhaXQgYWkubW9kZWxzLmdlbmVyYXRlQ29udGVudCh7XG4gICAgICBtb2RlbDogXCJnZW1pbmktMi41LWZsYXNoXCIsXG4gICAgICBjb250ZW50czogcHJvbXB0LFxuICAgICAgY29uZmlnOiB7XG4gICAgICAgIHJlc3BvbnNlTWltZVR5cGU6IFwiYXBwbGljYXRpb24vanNvblwiLFxuICAgICAgICByZXNwb25zZVNjaGVtYTogc2NoZW1hLFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIGlmICghcmVzcG9uc2UudGV4dCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJSZWNlaXZlZCBhbiBlbXB0eSByZXNwb25zZSBmcm9tIHRoZSBBSS5cIik7XG4gICAgfVxuICAgIFxuICAgIGNvbnN0IGNsZWFuZWRUZXh0ID0gcmVzcG9uc2UudGV4dC50cmltKCkucmVwbGFjZSgvXmBgYGpzb25cXHMqfGBgYFxccyokL2csICcnKTtcblxuICAgIGlmICghY2xlYW5lZFRleHQpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiUmVjZWl2ZWQgYW4gZW1wdHkgSlNPTiByZXNwb25zZSBmcm9tIHRoZSBBSSBhZnRlciBjbGVhbmluZy5cIik7XG4gICAgfVxuXG4gICAgcmV0dXJuIEpTT04ucGFyc2UoY2xlYW5lZFRleHQpIGFzIFQ7XG4gIH0gY2F0Y2ggKGUpIHtcbiAgICBjb25zb2xlLmVycm9yKFwiRmFpbGVkIHRvIGdlbmVyYXRlIG9yIHBhcnNlIEdlbWluaSBKU09OIHJlc3BvbnNlOlwiLCBlKTtcbiAgICAgaWYgKGUgaW5zdGFuY2VvZiBFcnJvciAmJiBlLm1lc3NhZ2Uuc3RhcnRzV2l0aChcIlJlY2VpdmVkIGFuIGVtcHR5XCIpKSB7XG4gICAgICAgIHRocm93IGU7XG4gICAgfVxuICAgIHRocm93IG5ldyBFcnJvcihcIkZhaWxlZCB0byBwcm9jZXNzIHN0cnVjdHVyZWQgZGF0YSBmcm9tIEFJLlwiKTtcbiAgfVxufVxuXG5leHBvcnQgeyBUeXBlIH07IC8vIFJlLWV4cG9ydCBUeXBlIGZvciBjb252ZW5pZW5jZVxuIl0sIm5hbWVzIjpbIkdvb2dsZUdlbkFJIiwiVHlwZSIsInByb2Nlc3MiLCJlbnYiLCJHRU1JTklfQVBJX0tFWSIsIkVycm9yIiwiYWkiLCJhcGlLZXkiLCJnZW5lcmF0ZVRleHQiLCJwcm9tcHQiLCJyZXNwb25zZSIsIm1vZGVscyIsImdlbmVyYXRlQ29udGVudCIsIm1vZGVsIiwiY29udGVudHMiLCJ0ZXh0IiwiZSIsImNvbnNvbGUiLCJlcnJvciIsImdlbmVyYXRlU3RydWN0dXJlZERhdGEiLCJzY2hlbWEiLCJjb25maWciLCJyZXNwb25zZU1pbWVUeXBlIiwicmVzcG9uc2VTY2hlbWEiLCJjbGVhbmVkVGV4dCIsInRyaW0iLCJyZXBsYWNlIiwiSlNPTiIsInBhcnNlIiwibWVzc2FnZSIsInN0YXJ0c1dpdGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api)/./backend/lib/gemini.ts\n");

/***/ }),

/***/ "(api)/./pages/api/mood-analysis.ts":
/*!************************************!*\
  !*** ./pages/api/mood-analysis.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../backend/lib/gemini */ \"(api)/./backend/lib/gemini.ts\");\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        res.setHeader(\"Allow\", [\n            \"POST\"\n        ]);\n        return res.status(405).end(`Method ${req.method} Not Allowed`);\n    }\n    try {\n        const mockDashboardData = `\n      - Past Prelesss Gauge: High Stress\n      - Wellness Tracker: High activity, low recovery\n      - Safety Checklist: Reports 'Anxiety' and a 'Missed Meeting'\n    `;\n        const prompt = `You are a futuristic AI assistant named 'FAYLUR' providing real-time status updates inside a high-tech UI. Based on the following user data, provide a concise, one-sentence analysis of their current wellness and mood state. Keep the tone slightly robotic but helpful. Data: ${mockDashboardData}`;\n        const analysis = await (0,_backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.generateText)(prompt);\n        res.status(200).json({\n            analysis\n        });\n    } catch (error) {\n        console.error(error);\n        const errorMessage = error instanceof Error ? error.message : \"An unknown error occurred.\";\n        res.status(500).json({\n            error: `Analysis failed: ${errorMessage}`\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9wYWdlcy9hcGkvbW9vZC1hbmFseXNpcy50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUV3RDtBQU96QyxlQUFlQyxRQUM1QkMsR0FBbUIsRUFDbkJDLEdBQWtDO0lBRWxDLElBQUlELElBQUlFLE1BQU0sS0FBSyxRQUFRO1FBQ3pCRCxJQUFJRSxTQUFTLENBQUMsU0FBUztZQUFDO1NBQU87UUFDL0IsT0FBT0YsSUFBSUcsTUFBTSxDQUFDLEtBQUtDLEdBQUcsQ0FBQyxDQUFDLE9BQU8sRUFBRUwsSUFBSUUsTUFBTSxDQUFDLFlBQVksQ0FBQztJQUMvRDtJQUVBLElBQUk7UUFDRixNQUFNSSxvQkFBb0IsQ0FBQzs7OztJQUkzQixDQUFDO1FBQ0QsTUFBTUMsU0FBUyxDQUFDLGtSQUFrUixFQUFFRCxrQkFBa0IsQ0FBQztRQUV2VCxNQUFNRSxXQUFXLE1BQU1WLGlFQUFZQSxDQUFDUztRQUNwQ04sSUFBSUcsTUFBTSxDQUFDLEtBQUtLLElBQUksQ0FBQztZQUFFRDtRQUFTO0lBQ2xDLEVBQUUsT0FBT0UsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUNBO1FBQ2QsTUFBTUUsZUFBZUYsaUJBQWlCRyxRQUFRSCxNQUFNSSxPQUFPLEdBQUc7UUFDOURiLElBQUlHLE1BQU0sQ0FBQyxLQUFLSyxJQUFJLENBQUM7WUFBRUMsT0FBTyxDQUFDLGlCQUFpQixFQUFFRSxhQUFhLENBQUM7UUFBQztJQUNuRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXllcy1zaGllbGQtZnV0dXJpc3RpYy11aS8uL3BhZ2VzL2FwaS9tb29kLWFuYWx5c2lzLnRzP2U5YTEiXSwic291cmNlc0NvbnRlbnQiOlsiXG5pbXBvcnQgdHlwZSB7IE5leHRBcGlSZXF1ZXN0LCBOZXh0QXBpUmVzcG9uc2UgfSBmcm9tICduZXh0JztcbmltcG9ydCB7IGdlbmVyYXRlVGV4dCB9IGZyb20gJy4uLy4uL2JhY2tlbmQvbGliL2dlbWluaSc7XG5cbnR5cGUgUmVzcG9uc2VEYXRhID0ge1xuICBhbmFseXNpcz86IHN0cmluZztcbiAgZXJyb3I/OiBzdHJpbmc7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBhc3luYyBmdW5jdGlvbiBoYW5kbGVyKFxuICByZXE6IE5leHRBcGlSZXF1ZXN0LFxuICByZXM6IE5leHRBcGlSZXNwb25zZTxSZXNwb25zZURhdGE+XG4pIHtcbiAgaWYgKHJlcS5tZXRob2QgIT09ICdQT1NUJykge1xuICAgIHJlcy5zZXRIZWFkZXIoJ0FsbG93JywgWydQT1NUJ10pO1xuICAgIHJldHVybiByZXMuc3RhdHVzKDQwNSkuZW5kKGBNZXRob2QgJHtyZXEubWV0aG9kfSBOb3QgQWxsb3dlZGApO1xuICB9XG5cbiAgdHJ5IHtcbiAgICBjb25zdCBtb2NrRGFzaGJvYXJkRGF0YSA9IGBcbiAgICAgIC0gUGFzdCBQcmVsZXNzcyBHYXVnZTogSGlnaCBTdHJlc3NcbiAgICAgIC0gV2VsbG5lc3MgVHJhY2tlcjogSGlnaCBhY3Rpdml0eSwgbG93IHJlY292ZXJ5XG4gICAgICAtIFNhZmV0eSBDaGVja2xpc3Q6IFJlcG9ydHMgJ0FueGlldHknIGFuZCBhICdNaXNzZWQgTWVldGluZydcbiAgICBgO1xuICAgIGNvbnN0IHByb21wdCA9IGBZb3UgYXJlIGEgZnV0dXJpc3RpYyBBSSBhc3Npc3RhbnQgbmFtZWQgJ0ZBWUxVUicgcHJvdmlkaW5nIHJlYWwtdGltZSBzdGF0dXMgdXBkYXRlcyBpbnNpZGUgYSBoaWdoLXRlY2ggVUkuIEJhc2VkIG9uIHRoZSBmb2xsb3dpbmcgdXNlciBkYXRhLCBwcm92aWRlIGEgY29uY2lzZSwgb25lLXNlbnRlbmNlIGFuYWx5c2lzIG9mIHRoZWlyIGN1cnJlbnQgd2VsbG5lc3MgYW5kIG1vb2Qgc3RhdGUuIEtlZXAgdGhlIHRvbmUgc2xpZ2h0bHkgcm9ib3RpYyBidXQgaGVscGZ1bC4gRGF0YTogJHttb2NrRGFzaGJvYXJkRGF0YX1gO1xuICAgIFxuICAgIGNvbnN0IGFuYWx5c2lzID0gYXdhaXQgZ2VuZXJhdGVUZXh0KHByb21wdCk7XG4gICAgcmVzLnN0YXR1cygyMDApLmpzb24oeyBhbmFseXNpcyB9KTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKGVycm9yKTtcbiAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdBbiB1bmtub3duIGVycm9yIG9jY3VycmVkLic7XG4gICAgcmVzLnN0YXR1cyg1MDApLmpzb24oeyBlcnJvcjogYEFuYWx5c2lzIGZhaWxlZDogJHtlcnJvck1lc3NhZ2V9YCB9KTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbImdlbmVyYXRlVGV4dCIsImhhbmRsZXIiLCJyZXEiLCJyZXMiLCJtZXRob2QiLCJzZXRIZWFkZXIiLCJzdGF0dXMiLCJlbmQiLCJtb2NrRGFzaGJvYXJkRGF0YSIsInByb21wdCIsImFuYWx5c2lzIiwianNvbiIsImVycm9yIiwiY29uc29sZSIsImVycm9yTWVzc2FnZSIsIkVycm9yIiwibWVzc2FnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./pages/api/mood-analysis.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0"], () => (__webpack_exec__("(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fmood-analysis&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cmood-analysis.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();
// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Numeric input widget styles differently on mobile: mobile render 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        <span
          style="white-space: nowrap;"
        >
          <span />
          <span
            class="mock-TeX"
          >
            5008 \\div 4 =
          </span>
          <span />
        </span>
         
        <div
          class="perseus-widget-container widget-nohighlight widget-inline-block"
        >
          <div>
            <div
              aria-label="Math input box Tap with one or two fingers to open keyboard"
              class="initial_4qg14c-o_O-input_1n1c032"
              role="textbox"
            >
              <div
                class="keypad-input"
                tabindex="0"
              >
                <div
                  class="mq-editable-field mq-math-mode"
                  style="background-color: white; min-height: 44px; min-width: 64px; max-width: 128px; box-sizing: border-box; position: relative; border-style: solid; border-color: rgba(33,36,44,0.50); border-radius: 4px; color: rgb(33, 36, 44); border-width: 1px;"
                >
                  <span
                    class="mq-textarea"
                  >
                    <span
                      aria-label="Math Input:"
                    />
                  </span>
                  <span
                    aria-hidden="true"
                    class="mq-root-block mq-empty"
                    style="padding: 10px 13px 8px 13px; font-size: 18pt;"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
         
      </div>
    </div>
  </div>
</div>
`;

exports[`numeric-input widget Should render predictably: after interaction 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        <span
          style="white-space: nowrap;"
        >
          <span />
          <span
            class="mock-TeX"
          >
            5008 \\div 4 =
          </span>
          <span />
        </span>
         
        <div
          class="perseus-widget-container widget-nohighlight widget-inline-block"
        >
          <input
            aria-disabled="false"
            aria-invalid="false"
            aria-required="false"
            autocapitalize="off"
            autocomplete="off"
            autocorrect="off"
            class="input_ttwki7-o_O-BodyTextMediumMediumWeight_ijclyr-o_O-default_8fhii8-o_O-inputWithExamples_ylhnsi"
            data-testid="input-with-examples"
            id=":r6:"
            tabindex="0"
            type="text"
            value="1252"
          />
          <span
            id="aria-for-:r6:"
            style="display: none;"
          />
        </div>
         
      </div>
    </div>
  </div>
</div>
`;

exports[`numeric-input widget Should render predictably: first render 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        <span
          style="white-space: nowrap;"
        >
          <span />
          <span
            class="mock-TeX"
          >
            5008 \\div 4 =
          </span>
          <span />
        </span>
         
        <div
          class="perseus-widget-container widget-nohighlight widget-inline-block"
        >
          <input
            aria-disabled="false"
            aria-invalid="false"
            aria-required="false"
            autocapitalize="off"
            autocomplete="off"
            autocorrect="off"
            class="input_ttwki7-o_O-BodyTextMediumMediumWeight_ijclyr-o_O-default_8fhii8-o_O-inputWithExamples_1y6ajxo"
            data-testid="input-with-examples"
            id=":r6:"
            tabindex="0"
            type="text"
            value=""
          />
          <span
            id="aria-for-:r6:"
            style="display: none;"
          />
        </div>
         
      </div>
    </div>
  </div>
</div>
`;

exports[`numeric-input widget Should render tooltip as list when multiple format options are given: render with format list tooltip 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        <span
          style="white-space: nowrap;"
        >
          <span />
          <span
            class="mock-TeX"
          >
            5008 \\div 4 =
          </span>
          <span />
        </span>
         
        <div
          class="perseus-widget-container widget-nohighlight widget-inline-block"
        >
          <input
            aria-describedby="aria-for-:rf:"
            aria-disabled="false"
            aria-invalid="false"
            aria-required="false"
            autocapitalize="off"
            autocomplete="off"
            autocorrect="off"
            class="input_ttwki7-o_O-BodyTextMediumMediumWeight_ijclyr-o_O-default_8fhii8-o_O-inputWithExamples_1y6ajxo"
            data-testid="input-with-examples"
            id=":rf:"
            tabindex="0"
            type="text"
            value=""
          />
          <span
            id="aria-for-:rf:"
            style="display: none;"
          >
            Your answer should be 
                   a simplified proper fraction, like 3/5, or
a simplified improper fraction, like 7/4, or
a mixed number, like 1 and 3/4
          </span>
        </div>
         
      </div>
    </div>
  </div>
</div>
`;

exports[`numeric-input widget Should render tooltip using only correct answer formats: render tooltip only with correct answers 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        <span
          style="white-space: nowrap;"
        >
          <span />
          <span
            class="mock-TeX"
          >
            5008 \\div 4 =
          </span>
          <span />
        </span>
         
        <div
          class="perseus-widget-container widget-nohighlight widget-inline-block"
        >
          <input
            aria-describedby="aria-for-:ri:"
            aria-disabled="false"
            aria-invalid="false"
            aria-label="What's the answer?"
            aria-required="false"
            autocapitalize="off"
            autocomplete="off"
            autocorrect="off"
            class="input_ttwki7-o_O-BodyTextMediumMediumWeight_ijclyr-o_O-default_8fhii8-o_O-inputWithExamples_1y6ajxo"
            data-testid="input-with-examples"
            id=":ri:"
            tabindex="0"
            type="text"
            value=""
          />
          <span
            id="aria-for-:ri:"
            style="display: none;"
          >
            Your answer should be 
                   a simplified proper fraction, like 3/5
          </span>
        </div>
         
      </div>
    </div>
  </div>
</div>
`;

exports[`numeric-input widget Should render tooltip when format option is given: render with format tooltip 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        <span
          style="white-space: nowrap;"
        >
          <span />
          <span
            class="mock-TeX"
          >
            5008 \\div 4 =
          </span>
          <span />
        </span>
         
        <div
          class="perseus-widget-container widget-nohighlight widget-inline-block"
        >
          <input
            aria-describedby="aria-for-:r9:"
            aria-disabled="false"
            aria-invalid="false"
            aria-required="false"
            autocapitalize="off"
            autocomplete="off"
            autocorrect="off"
            class="input_ttwki7-o_O-BodyTextMediumMediumWeight_ijclyr-o_O-default_8fhii8-o_O-inputWithExamples_1y6ajxo"
            data-testid="input-with-examples"
            id=":r9:"
            tabindex="0"
            type="text"
            value=""
          />
          <span
            id="aria-for-:r9:"
            style="display: none;"
          >
            Your answer should be 
                   a simplified proper fraction, like 3/5
          </span>
        </div>
         
      </div>
    </div>
  </div>
</div>
`;

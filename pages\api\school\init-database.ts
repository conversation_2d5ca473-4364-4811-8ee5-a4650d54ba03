import { NextApiRequest, NextApiResponse } from 'next';
import mysql from 'mysql2/promise';

// Database connection configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'school_management',
  port: parseInt(process.env.DB_PORT || '3306'),
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const connection = await mysql.createConnection(dbConfig);

    try {
      // Create students table
      await connection.execute(`
        CREATE TABLE IF NOT EXISTS students (
          id INT AUTO_INCREMENT PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          grade VARCHAR(50) NOT NULL,
          gpa DECIMAL(3,2) DEFAULT 0.00,
          ai_mentor VARCHAR(100),
          learning_style VARCHAR(100),
          emotional_state VARCHAR(50),
          neural_pathways INT DEFAULT 0,
          status ENUM('active', 'inactive', 'graduated') DEFAULT 'active',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `);

      // Create teachers table
      await connection.execute(`
        CREATE TABLE IF NOT EXISTS teachers (
          id INT AUTO_INCREMENT PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          role VARCHAR(255) NOT NULL,
          department_id INT,
          ai_partner VARCHAR(100),
          efficiency_score DECIMAL(4,1) DEFAULT 0.0,
          status ENUM('active', 'inactive', 'on_leave') DEFAULT 'active',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `);

      // Create departments table
      await connection.execute(`
        CREATE TABLE IF NOT EXISTS departments (
          id INT AUTO_INCREMENT PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          color VARCHAR(100) NOT NULL,
          innovation VARCHAR(255),
          status ENUM('active', 'inactive') DEFAULT 'active',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `);

      // Create classes table
      await connection.execute(`
        CREATE TABLE IF NOT EXISTS classes (
          id INT AUTO_INCREMENT PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          department_id INT,
          teacher_id INT,
          ai_tutor VARCHAR(100),
          max_students INT DEFAULT 30,
          status ENUM('active', 'inactive', 'completed') DEFAULT 'active',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (department_id) REFERENCES departments(id),
          FOREIGN KEY (teacher_id) REFERENCES teachers(id)
        )
      `);

      // Create innovations table
      await connection.execute(`
        CREATE TABLE IF NOT EXISTS innovations (
          id INT AUTO_INCREMENT PRIMARY KEY,
          title VARCHAR(255) NOT NULL,
          icon VARCHAR(10) NOT NULL,
          status ENUM('Active', 'Live', 'Beta', 'Testing', 'Inactive') DEFAULT 'Testing',
          impact VARCHAR(255) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `);

      // Create events table
      await connection.execute(`
        CREATE TABLE IF NOT EXISTS events (
          id INT AUTO_INCREMENT PRIMARY KEY,
          title VARCHAR(255) NOT NULL,
          date DATE NOT NULL,
          type VARCHAR(100) NOT NULL,
          participants INT DEFAULT 0,
          status ENUM('active', 'cancelled', 'completed') DEFAULT 'active',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `);

      // Create ai_tutor_sessions table
      await connection.execute(`
        CREATE TABLE IF NOT EXISTS ai_tutor_sessions (
          id INT AUTO_INCREMENT PRIMARY KEY,
          student_id INT,
          ai_tutor VARCHAR(100) NOT NULL,
          subject VARCHAR(100) NOT NULL,
          duration_minutes INT DEFAULT 0,
          effectiveness_score DECIMAL(3,1) DEFAULT 0.0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (student_id) REFERENCES students(id)
        )
      `);

      // Create junction tables
      await connection.execute(`
        CREATE TABLE IF NOT EXISTS student_departments (
          id INT AUTO_INCREMENT PRIMARY KEY,
          student_id INT,
          department_id INT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (student_id) REFERENCES students(id),
          FOREIGN KEY (department_id) REFERENCES departments(id),
          UNIQUE KEY unique_student_department (student_id, department_id)
        )
      `);

      await connection.execute(`
        CREATE TABLE IF NOT EXISTS teacher_departments (
          id INT AUTO_INCREMENT PRIMARY KEY,
          teacher_id INT,
          department_id INT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (teacher_id) REFERENCES teachers(id),
          FOREIGN KEY (department_id) REFERENCES departments(id),
          UNIQUE KEY unique_teacher_department (teacher_id, department_id)
        )
      `);

      res.status(200).json({ 
        message: 'Database tables created successfully',
        tables: [
          'students', 'teachers', 'departments', 'classes', 
          'innovations', 'events', 'ai_tutor_sessions',
          'student_departments', 'teacher_departments'
        ]
      });

    } finally {
      await connection.end();
    }

  } catch (error) {
    console.error('Database initialization error:', error);
    res.status(500).json({ 
      message: 'Failed to initialize database',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}

{"name": "eyes-shield-futuristic-ui", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@google/genai": "^0.10.0", "@heroicons/react": "^2.1.3", "@khanacademy/kas": "^2.0.9", "@khanacademy/kmath": "^2.0.27", "@khanacademy/perseus-core": "^18.2.0", "@khanacademy/simple-markdown": "^2.0.10", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@react-three/drei": "^10.5.1", "@react-three/fiber": "^9.2.0", "@tremor/react": "^3.18.7", "css-loader": "^7.1.2", "framer-motion": "^12.23.6", "lucide-react": "^0.525.0", "monaco-editor": "^0.52.2", "monaco-editor-webpack-plugin": "^7.1.0", "mysql2": "^3.14.2", "next": "14.2.3", "phaser": "^3.90.0", "react": "18.2.0", "react-dom": "18.2.0", "react-flow-renderer": "^10.3.17", "react-organizational-chart": "^2.2.1", "recharts": "^2.12.7", "style-loader": "^4.0.0", "three": "^0.178.0", "@theia/ai-anthropic": "1.63.0", "@theia/ai-chat": "1.63.0", "@theia/ai-chat-ui": "1.63.0", "@theia/ai-code-completion": "1.63.0", "@theia/ai-core": "1.63.0", "@theia/ai-editor": "1.63.0", "@theia/ai-google": "1.63.0", "@theia/ai-history": "1.63.0", "@theia/ai-huggingface": "1.63.0", "@theia/ai-ide": "1.63.0", "@theia/ai-llamafile": "1.63.0", "@theia/ai-mcp": "1.63.0", "@theia/ai-ollama": "1.63.0", "@theia/ai-openai": "1.63.0", "@theia/ai-scanoss": "1.63.0", "@theia/ai-terminal": "1.63.0", "@theia/ai-vercel-ai": "1.63.0", "@theia/bulk-edit": "1.63.0", "@theia/callhierarchy": "1.63.0", "@theia/collaboration": "1.63.0", "@theia/console": "1.63.0", "@theia/core": "1.63.0", "@theia/debug": "1.63.0", "@theia/dev-container": "1.63.0", "@theia/editor": "1.63.0", "@theia/editor-preview": "1.63.0", "@theia/file-search": "1.63.0", "@theia/filesystem": "1.63.0", "@theia/getting-started": "1.63.0", "@theia/keymaps": "1.63.0", "@theia/markers": "1.63.0", "@theia/memory-inspector": "1.63.0", "@theia/messages": "1.63.0", "@theia/metrics": "1.63.0", "@theia/mini-browser": "1.63.0", "@theia/monaco": "1.63.0", "@theia/navigator": "1.63.0", "@theia/notebook": "1.63.0", "@theia/outline-view": "1.63.0", "@theia/output": "1.63.0", "@theia/plugin-dev": "1.63.0", "@theia/plugin-ext": "1.63.0", "@theia/plugin-ext-headless": "1.63.0", "@theia/plugin-ext-vscode": "1.63.0", "@theia/plugin-metrics": "1.63.0", "@theia/preferences": "1.63.0", "@theia/preview": "1.63.0", "@theia/process": "1.63.0", "@theia/property-view": "1.63.0", "@theia/remote": "1.63.0", "@theia/scanoss": "1.63.0", "@theia/scm": "1.63.0", "@theia/scm-extra": "1.63.0", "@theia/search-in-workspace": "1.63.0", "@theia/secondary-window": "1.63.0", "@theia/task": "1.63.0", "@theia/terminal": "1.63.0", "@theia/test": "1.63.0", "@theia/timeline": "1.63.0", "@theia/toolbar": "1.63.0", "@theia/typehierarchy": "1.63.0", "@theia/userstorage": "1.63.0", "@theia/variable-resolver": "1.63.0", "@theia/vsx-registry": "1.63.0", "@theia/workspace": "1.63.0"}, "devDependencies": {"@types/node": "^20.12.12", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-config-next": "14.2.3", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "typescript": "^5.4.5", "@theia/cli": "1.63.0", "@theia/native-webpack-plugin": "1.63.0"}}
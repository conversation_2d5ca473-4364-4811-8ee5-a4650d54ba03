
import React, { useState } from 'react';
import { Card } from '../../components/Card';

interface ServiceRequestProps {
    onRequestSubmitted: () => void;
}

export const ServiceRequest: React.FC<ServiceRequestProps> = ({ onRequestSubmitted }) => {
  const [submitting, setSubmitting] = useState(false);
  const [message, setMessage] = useState('');

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setSubmitting(true);
    setMessage('');

    const formData = new FormData(event.currentTarget);
    const data = {
        serviceType: formData.get('service-type') as string,
        details: formData.get('details') as string,
    };

    try {
        const response = await fetch('/api/concierge', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data),
        });

        if (!response.ok) {
            throw new Error('Failed to submit request');
        }
        
        setMessage('Request submitted successfully!');
        (event.target as HTMLFormElement).reset();
        onRequestSubmitted(); // Notify parent to refetch data
        setTimeout(() => setMessage(''), 3000); // Clear message after 3s

    } catch (error) {
        console.error(error);
        setMessage('Submission failed. Please try again.');
    } finally {
        setSubmitting(false);
    }
  };

  return (
    <Card title="Request a Service">
      <form onSubmit={handleSubmit} className="flex flex-col h-full">
        <div className="flex-grow space-y-4">
            <div>
                <label htmlFor="service-type" className="text-xs text-gray-400">Service Type</label>
                <select id="service-type" name="service-type" required className="w-full mt-1 p-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white">
                    <option>Transport</option>
                    <option>Dining Reservation</option>
                    <option>Maintenance</option>
                    <option>Other</option>
                </select>
            </div>
             <div>
                <label htmlFor="details" className="text-xs text-gray-400">Details</label>
                <textarea id="details" name="details" rows={3} required className="w-full mt-1 p-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white" placeholder="Provide details for your request..."></textarea>
            </div>
        </div>
        {message && <p className="text-center text-sm text-cyan-300 mt-2">{message}</p>}
        <button type="submit" disabled={submitting} className="w-full mt-4 py-2 text-sm font-semibold text-white bg-cyan-500/30 border border-cyan-500/50 rounded-lg hover:bg-cyan-500/50 transition-colors disabled:opacity-50 disabled:cursor-wait">
          {submitting ? 'SUBMITTING...' : 'Submit Request'}
        </button>
      </form>
    </Card>
  );
};

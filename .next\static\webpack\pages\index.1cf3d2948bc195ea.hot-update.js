"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/views/School/departments/SchoolDashboard.tsx":
/*!**********************************************************!*\
  !*** ./src/views/School/departments/SchoolDashboard.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SchoolDashboard: function() { return /* binding */ SchoolDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../components/Card */ \"./src/components/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BuildingLibraryIcon,CalendarIcon,ChartBarIcon,UserGroupIcon!=!@heroicons/react/24/solid */ \"__barrel_optimize__?names=AcademicCapIcon,BuildingLibraryIcon,CalendarIcon,ChartBarIcon,UserGroupIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/solid/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst SchoolDashboard = (param)=>{\n    let { activeSubSection } = param;\n    _s();\n    const [schoolData, setSchoolData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [overviewData, setOverviewData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [departmentsData, setDepartmentsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            try {\n                setLoading(true);\n                // Fetch dashboard data\n                const dashboardResponse = await fetch(\"/api/school/dashboard\");\n                if (dashboardResponse.ok) {\n                    const dashboardData = await dashboardResponse.json();\n                    setSchoolData(dashboardData);\n                }\n                // Fetch overview data\n                const overviewResponse = await fetch(\"/api/school/overview\");\n                if (overviewResponse.ok) {\n                    const overviewDataResult = await overviewResponse.json();\n                    setOverviewData(overviewDataResult);\n                }\n                // Fetch departments data\n                const departmentsResponse = await fetch(\"/api/school/departments\");\n                if (departmentsResponse.ok) {\n                    const departmentsDataResult = await departmentsResponse.json();\n                    setDepartmentsData(departmentsDataResult);\n                }\n            } catch (error) {\n                console.error(\"Failed to fetch data:\", error);\n                // Fallback to mock data if API fails\n                const mockData = {\n                    overview: {\n                        totalStudents: 1247,\n                        totalTeachers: 89,\n                        totalClasses: 156,\n                        averageGPA: 3.67,\n                        aiTutorSessions: 2847,\n                        virtualRealityClasses: 23,\n                        blockchainCertificates: 156,\n                        carbonFootprint: 2.3\n                    },\n                    departments: [\n                        {\n                            name: \"AI & Robotics\",\n                            students: 312,\n                            teachers: 18,\n                            color: \"bg-blue-500/20 text-blue-400\",\n                            innovation: \"Neural Networks Lab\"\n                        },\n                        {\n                            name: \"Quantum Computing\",\n                            students: 298,\n                            teachers: 15,\n                            color: \"bg-green-500/20 text-green-400\",\n                            innovation: \"Quantum Simulator\"\n                        }\n                    ],\n                    innovations: [\n                        {\n                            title: \"AI-Powered Personalized Learning\",\n                            status: \"Active\",\n                            impact: \"94% improvement\",\n                            icon: \"\\uD83E\\uDD16\"\n                        },\n                        {\n                            title: \"Holographic Classrooms\",\n                            status: \"Beta\",\n                            impact: \"87% engagement\",\n                            icon: \"\\uD83D\\uDD2E\"\n                        }\n                    ],\n                    recentEvents: [\n                        {\n                            title: \"Global Virtual Science Fair\",\n                            date: \"March 15\",\n                            type: \"Innovation\",\n                            participants: 47\n                        },\n                        {\n                            title: \"AI Ethics Symposium\",\n                            date: \"March 20\",\n                            type: \"Academic\",\n                            participants: 234\n                        }\n                    ]\n                };\n                setSchoolData(mockData);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchData();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-cyan-400\",\n                children: \"Loading School Dashboard...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, undefined);\n    }\n    const renderContent = ()=>{\n        switch(activeSubSection){\n            case \"overview\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: overviewData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                title: \"\\uD83C\\uDFEB School Information\",\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-white mb-2\",\n                                                        children: overviewData.schoolInfo.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-cyan-400\",\n                                                                        children: \"Established:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                        lineNumber: 107,\n                                                                        columnNumber: 56\n                                                                    }, undefined),\n                                                                    \" \",\n                                                                    overviewData.schoolInfo.established\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 107,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-cyan-400\",\n                                                                        children: \"Motto:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                        lineNumber: 108,\n                                                                        columnNumber: 56\n                                                                    }, undefined),\n                                                                    \" \",\n                                                                    overviewData.schoolInfo.motto\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 108,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-cyan-400\",\n                                                                        children: \"Accreditation:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                        lineNumber: 109,\n                                                                        columnNumber: 56\n                                                                    }, undefined),\n                                                                    \" \",\n                                                                    overviewData.schoolInfo.accreditation\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 109,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-cyan-400\",\n                                                                        children: \"Ranking:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                        lineNumber: 110,\n                                                                        columnNumber: 56\n                                                                    }, undefined),\n                                                                    \" \",\n                                                                    overviewData.schoolInfo.ranking\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 110,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-lg font-semibold text-white mb-2\",\n                                                        children: \"Campus Details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-purple-400\",\n                                                                        children: \"Type:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                        lineNumber: 116,\n                                                                        columnNumber: 56\n                                                                    }, undefined),\n                                                                    \" \",\n                                                                    overviewData.schoolInfo.campusType\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 116,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-purple-400\",\n                                                                        children: \"Access:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                        lineNumber: 117,\n                                                                        columnNumber: 56\n                                                                    }, undefined),\n                                                                    \" \",\n                                                                    overviewData.schoolInfo.timeZone\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 117,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                title: \"\\uD83D\\uDCCA Key Statistics\",\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-blue-400\",\n                                                        children: overviewData.statistics.totalStudents.toLocaleString()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: \"Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-green-400\",\n                                                        children: overviewData.statistics.totalTeachers\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: \"Teachers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-purple-400\",\n                                                        children: overviewData.statistics.virtualClassrooms\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: \"Virtual Classrooms\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-cyan-400\",\n                                                        children: overviewData.statistics.aiTutors\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: \"AI Tutors\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                title: \"\\uD83C\\uDFC6 Recent Achievements\",\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: overviewData.achievements.slice(0, 6).map((achievement, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg bg-gray-800/40 border border-gray-600/30\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl\",\n                                                            children: achievement.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 29\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-sm font-medium text-white\",\n                                                                    children: achievement.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                    lineNumber: 157,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-400 mt-1\",\n                                                                    children: achievement.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                    lineNumber: 158,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 rounded-full text-xs bg-blue-500/20 text-blue-400\",\n                                                                            children: achievement.category\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                            lineNumber: 160,\n                                                                            columnNumber: 33\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: achievement.year\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                            lineNumber: 163,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                    lineNumber: 159,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 27\n                                                }, undefined)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 25\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-400\",\n                        children: \"Loading overview data...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 11\n                }, undefined);\n            case \"dashboard\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"\\uD83D\\uDE80 Future School Overview\",\n                            className: \"lg:col-span-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-4 p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.UserGroupIcon, {\n                                                    className: \"w-6 h-6 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: schoolData.overview.totalStudents\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Global Students\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.AcademicCapIcon, {\n                                                    className: \"w-6 h-6 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: schoolData.overview.totalTeachers\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"AI-Enhanced Teachers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.BuildingLibraryIcon, {\n                                                    className: \"w-6 h-6 text-purple-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: schoolData.overview.virtualRealityClasses\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"VR Classrooms\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.ChartBarIcon, {\n                                                    className: \"w-6 h-6 text-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: schoolData.overview.aiTutorSessions\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"AI Tutor Sessions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"\\uD83D\\uDD2C Innovation Hub\",\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 p-4\",\n                                children: schoolData.innovations.map((innovation, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg bg-gray-800/40 border border-gray-600/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg\",\n                                                                children: innovation.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 223,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-white\",\n                                                                children: innovation.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs \".concat(innovation.status === \"Active\" ? \"bg-green-500/20 text-green-400\" : innovation.status === \"Live\" ? \"bg-blue-500/20 text-blue-400\" : innovation.status === \"Beta\" ? \"bg-yellow-500/20 text-yellow-400\" : \"bg-purple-500/20 text-purple-400\"),\n                                                        children: innovation.status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: [\n                                                    \"Impact: \",\n                                                    innovation.impact\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"\\uD83C\\uDF1F Next-Gen Departments\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 p-4\",\n                                children: schoolData.departments.map((dept, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg \".concat(dept.color.split(\" \")[0], \" border border-gray-600/30\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold \".concat(dept.color.split(\" \").slice(1).join(\" \")),\n                                                            children: dept.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: [\n                                                                dept.students,\n                                                                \" students • \",\n                                                                dept.teachers,\n                                                                \" teachers\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-cyan-400 mt-1\",\n                                                            children: [\n                                                                \"\\uD83D\\uDE80 \",\n                                                                dept.innovation\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 rounded-lg \".concat(dept.color.split(\" \")[0], \" flex items-center justify-center\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.BuildingLibraryIcon, {\n                                                        className: \"w-4 h-4 \".concat(dept.color.split(\" \").slice(1).join(\" \"))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"\\uD83C\\uDF0D Global Events\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 p-4\",\n                                children: schoolData.recentEvents.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-3 p-3 rounded-lg hover:bg-gray-800/40 border border-gray-700/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.CalendarIcon, {\n                                                    className: \"w-5 h-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-white\",\n                                                        children: event.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: [\n                                                            event.date,\n                                                            \" • \",\n                                                            event.type\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mt-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-cyan-400\",\n                                                                children: [\n                                                                    \"\\uD83D\\uDC65 \",\n                                                                    event.participants,\n                                                                    \" participants\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"w-1 h-1 bg-gray-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-green-400\",\n                                                                children: \"\\uD83C\\uDF10 Global\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 11\n                }, undefined);\n            case \"overview\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"School Overview\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Comprehensive School Overview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Detailed school statistics and performance metrics will be displayed here.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 11\n                }, undefined);\n            case \"departments\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Department Management\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Department Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Manage all school departments, their staff, and resources.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 11\n                }, undefined);\n            case \"facilities\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"School Facilities\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Facilities Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Monitor and manage school facilities, maintenance, and resources.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 307,\n                    columnNumber: 11\n                }, undefined);\n            case \"events\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"School Events\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Event Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Plan, organize, and track school events and activities.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 11\n                }, undefined);\n            case \"announcements\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Announcements\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"School Announcements\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Create and manage school-wide announcements and communications.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 327,\n                    columnNumber: 11\n                }, undefined);\n            case \"performance\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Performance Analytics\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"School Performance\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Analyze school performance metrics and academic achievements.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 11\n                }, undefined);\n            case \"settings\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"School Settings\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"School Configuration\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Configure school settings, policies, and system preferences.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 347,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"School Dashboard\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Welcome to School Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Select a section from the navigation to get started.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 357,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full p-2\",\n        children: renderContent()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n        lineNumber: 368,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SchoolDashboard, \"wiOFhxGD2LTGEE6wKvDhVOgNeXI=\");\n_c = SchoolDashboard;\nvar _c;\n$RefreshReg$(_c, \"SchoolDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/School/departments/SchoolDashboard.tsx\n"));

/***/ })

});
import type { NextApiRequest, NextApiResponse } from 'next';
import { executeQuerySingle, initializeDatabase, seedDatabase } from '../../backend/lib/database';

type SchoolOverview = {
  gpa: number;
  credits: number;
  attendance: number;
  assignmentsCompleted: number;
  assignmentsTotal: number;
};

type SchoolData = {
  overview: SchoolOverview;
};

type DatabaseSchoolData = {
  id: number;
  gpa: number;
  credits: number;
  attendance: number;
  assignments_completed: number;
  assignments_total: number;
  created_at: string;
  updated_at: string;
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<SchoolData | { error: string }>
) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  try {
    // Initialize database if needed
    await initializeDatabase();
    await seedDatabase();

    // Get latest school data from database
    const schoolData = await executeQuerySingle<DatabaseSchoolData>(`
      SELECT * FROM school_data 
      ORDER BY updated_at DESC 
      LIMIT 1
    `);

    if (schoolData) {
      const response: SchoolData = {
        overview: {
          gpa: schoolData.gpa,
          credits: schoolData.credits,
          attendance: schoolData.attendance,
          assignmentsCompleted: schoolData.assignments_completed,
          assignmentsTotal: schoolData.assignments_total
        }
      };
      res.status(200).json(response);
    } else {
      // Fallback data if no data exists
      res.status(200).json({
        overview: {
          gpa: 3.85,
          credits: 120,
          attendance: 94.5,
          assignmentsCompleted: 28,
          assignmentsTotal: 32
        }
      });
    }
  } catch (error) {
    console.error('School data API error:', error);
    
    // Fallback data if database fails
    res.status(200).json({
      overview: {
        gpa: 3.85,
        credits: 120,
        attendance: 94.5,
        assignmentsCompleted: 28,
        assignmentsTotal: 32
      }
    });
  }
}

{"name": "@theia/remote-wsl", "version": "1.63.0", "description": "Theia - Remote WSL", "dependencies": {"@theia/core": "1.63.0", "@theia/remote": "1.63.0", "@theia/workspace": "1.63.0", "tslib": "^2.6.2"}, "publishConfig": {"access": "public"}, "theiaExtensions": [{"frontendElectron": "lib/electron-browser/remote-wsl-frontend-module", "backendElectron": "lib/electron-node/remote-wsl-backend-module"}], "keywords": ["theia-extension"], "license": "EPL-2.0 OR GPL-2.0 WITH Classpath-exception-2.0", "repository": {"type": "git", "url": "https://github.com/eclipse-theia/theia.git"}, "bugs": {"url": "https://github.com/theia-ide/theia/issues"}, "homepage": "https://github.com/theia-ide/theia", "files": ["lib", "src"], "scripts": {"build": "theiaext build", "clean": "theiaext clean", "compile": "theiaext compile", "lint": "theiaext lint", "test": "theiaext test", "watch": "theiaext watch"}, "nyc": {"extends": "../../configs/nyc.json"}, "gitHead": "21358137e41342742707f660b8e222f940a27652"}
<div align='center'>

<br />

<img src='https://raw.githubusercontent.com/eclipse-theia/theia/master/logo/theia.svg?sanitize=true' alt='theia-ext-logo' width='100px' />

<h2>ECLIPSE THEIA - SEARCH-IN-WORKSPACE EXTENSION</h2>

<hr />

</div>

## Description

The `@theia/search-in-workspace` extension provides the ability to perform searches over all files in a given workspace using different search techniques.

## Search Widget

The `@theia/search-in-workspace` extension contributes the `Search` widget which is capable of performing different types of searches include the possibility to:

- Perform standard searches
- Perform searches using regular expressions
- Perform searches within an `include` list (search for specific types of files (ex: `*.ts`))
- Perform searches excluding files or directories (using `exclude`)
- Perform searches ignoring hidden or excluded files/folders
- Perform search and replace (to quickly update multiple occurrences of a search term)

## Additional Information

- [API documentation for `@theia/search-in-workspace`](https://eclipse-theia.github.io/theia/docs/next/modules/search_in_workspace.html)
- [Theia - GitHub](https://github.com/eclipse-theia/theia)
- [Theia - Website](https://theia-ide.org/)

## License

- [Eclipse Public License 2.0](http://www.eclipse.org/legal/epl-2.0/)
- [一 (Secondary) GNU General Public License, version 2 with the GNU Classpath Exception](https://projects.eclipse.org/license/secondary-gpl-2.0-cp)

## Trademark

"Theia" is a trademark of the Eclipse Foundation
<https://www.eclipse.org/theia>

import type { NextApiRequest, NextApiResponse } from 'next';
import { executeQuery, executeQuerySingle, initializeDatabase, seedDatabase } from '../../backend/lib/database';

type SystemDiagnostics = {
  cpuLoad: number;
  ramUsage: number;
  diskUsage: number;
  networkStatus: string;
};

type DatabaseDiagnostics = {
  id: number;
  cpu_load: number;
  ram_usage: number;
  disk_usage: number;
  network_status: string;
  created_at: string;
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<SystemDiagnostics | { error: string }>
) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  try {
    // Initialize database if needed
    await initializeDatabase();
    await seedDatabase();

    // Get latest diagnostics from database
    const diagnostics = await executeQuerySingle<DatabaseDiagnostics>(`
      SELECT * FROM system_diagnostics 
      ORDER BY created_at DESC 
      LIMIT 1
    `);

    if (diagnostics) {
      const response: SystemDiagnostics = {
        cpuLoad: diagnostics.cpu_load,
        ramUsage: diagnostics.ram_usage,
        diskUsage: diagnostics.disk_usage,
        networkStatus: diagnostics.network_status
      };
      res.status(200).json(response);
    } else {
      // Insert new random data if none exists
      const newCpuLoad = Math.floor(Math.random() * 40) + 30; // 30-70%
      const newRamUsage = Math.floor(Math.random() * 30) + 50; // 50-80%
      const newDiskUsage = Math.floor(Math.random() * 20) + 15; // 15-35%
      
      await executeQuery(`
        INSERT INTO system_diagnostics (cpu_load, ram_usage, disk_usage, network_status) 
        VALUES (?, ?, ?, 'active')
      `, [newCpuLoad, newRamUsage, newDiskUsage]);

      res.status(200).json({
        cpuLoad: newCpuLoad,
        ramUsage: newRamUsage,
        diskUsage: newDiskUsage,
        networkStatus: 'active'
      });
    }
  } catch (error) {
    console.error('System diagnostics API error:', error);
    
    // Fallback data if database fails
    res.status(200).json({
      cpuLoad: 45.2,
      ramUsage: 67.8,
      diskUsage: 23.1,
      networkStatus: 'active'
    });
  }
}

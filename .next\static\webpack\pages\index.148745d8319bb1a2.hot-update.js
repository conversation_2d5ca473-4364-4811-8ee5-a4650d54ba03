"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/views/School/SchoolView.tsx":
/*!*****************************************!*\
  !*** ./src/views/School/SchoolView.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SchoolView: function() { return /* binding */ SchoolView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SchoolSubNav__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/SchoolSubNav */ \"./src/components/SchoolSubNav.tsx\");\n/* harmony import */ var _SchoolContent__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SchoolContent */ \"./src/views/School/SchoolContent.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst SchoolView = (param)=>{\n    let { activeDepartment, setActiveDepartment, activeSubSection: propActiveSubSection, setActiveSubSection: propSetActiveSubSection } = param;\n    _s();\n    const [localActiveSubSection, setLocalActiveSubSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"dashboard\");\n    // Use prop values if provided, otherwise use local state\n    const activeSubSection = propActiveSubSection || localActiveSubSection;\n    const setActiveSubSection = propSetActiveSubSection || setLocalActiveSubSection;\n    // Reset sub-section when department changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setActiveSubSection(\"dashboard\");\n    }, [\n        activeDepartment\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex gap-3 h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SchoolSubNav__WEBPACK_IMPORTED_MODULE_2__.SchoolSubNav, {\n                department: activeDepartment,\n                activeSubSection: activeSubSection,\n                setActiveSubSection: setActiveSubSection\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\SchoolView.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 min-h-0 overflow-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SchoolContent__WEBPACK_IMPORTED_MODULE_3__.SchoolContent, {\n                    activeDepartment: activeDepartment,\n                    activeSubSection: activeSubSection\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\SchoolView.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\SchoolView.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\SchoolView.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SchoolView, \"XbfApYB5qZivss/mjhGaIPNQ7ic=\");\n_c = SchoolView;\nvar _c;\n$RefreshReg$(_c, \"SchoolView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/School/SchoolView.tsx\n"));

/***/ })

});
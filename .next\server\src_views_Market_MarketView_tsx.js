"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_views_Market_MarketView_tsx";
exports.ids = ["src_views_Market_MarketView_tsx"];
exports.modules = {

/***/ "__barrel_optimize__?names=ShoppingCartIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ShoppingCartIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ShoppingCartIcon: () => (/* reexport safe */ _ShoppingCartIcon_js__WEBPACK_IMPORTED_MODULE_0__["default"])
/* harmony export */ });
/* harmony import */ var _ShoppingCartIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ShoppingCartIcon.js */ "./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/outline/esm/ShoppingCartIcon.js");



/***/ }),

/***/ "./src/components/Card.tsx":
/*!*********************************!*\
  !*** ./src/components/Card.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Card = ({ title, children, className = \"\", headerIcon, titleClassName = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `bg-panel-bg border border-gray-700/50 rounded-xl p-4 flex flex-col h-full ${className}`,\n        children: [\n            title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: `text-xs uppercase text-[#A0A0B0] font-semibold tracking-wider ${titleClassName}`,\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Card.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 11\n                    }, undefined),\n                    headerIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-[#A0A0B0]\",\n                        children: headerIcon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Card.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 26\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Card.tsx\",\n                lineNumber: 15,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-grow flex flex-col\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Card.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Card.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Card.tsx\n");

/***/ }),

/***/ "./src/views/Market/FeaturedProduct.tsx":
/*!**********************************************!*\
  !*** ./src/views/Market/FeaturedProduct.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FeaturedProduct: () => (/* binding */ FeaturedProduct)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/Card */ \"./src/components/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_ShoppingCartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ShoppingCartIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ShoppingCartIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/outline/esm/index.js\");\n\n\n\n\nconst FeaturedProduct = ({ product })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                src: product.image,\n                alt: product.name,\n                className: \"rounded-lg mb-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\FeaturedProduct.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-400\",\n                                children: product.category\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\FeaturedProduct.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg font-bold text-white\",\n                                children: product.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\FeaturedProduct.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\FeaturedProduct.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl font-semibold text-cyan-400\",\n                        children: product.price\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\FeaturedProduct.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\FeaturedProduct.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"w-full mt-4 py-2 text-sm font-semibold text-white bg-pink-500/80 border border-pink-500/90 rounded-lg hover:bg-pink-500/90 transition-colors flex items-center justify-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ShoppingCartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ShoppingCartIcon, {\n                        className: \"w-5 h-5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\FeaturedProduct.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, undefined),\n                    \"Add to Cart\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\FeaturedProduct.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\FeaturedProduct.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/Market/FeaturedProduct.tsx\n");

/***/ }),

/***/ "./src/views/Market/MarketView.tsx":
/*!*****************************************!*\
  !*** ./src/views/Market/MarketView.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarketView: () => (/* binding */ MarketView)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _FeaturedProduct__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./FeaturedProduct */ \"./src/views/Market/FeaturedProduct.tsx\");\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/Card */ \"./src/components/Card.tsx\");\n\n\n\n\nconst MarketView = ()=>{\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            try {\n                setLoading(true);\n                const response = await fetch(\"/api/market\");\n                const result = await response.json();\n                setData(result);\n            } catch (error) {\n                console.error(\"Failed to fetch market data\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchData();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-full text-cyan-400\",\n            children: \"Loading Market Data...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n            lineNumber: 42,\n            columnNumber: 16\n        }, undefined);\n    }\n    if (!data) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-full text-red-400\",\n            children: \"Failed to load market data.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n            lineNumber: 46,\n            columnNumber: 16\n        }, undefined);\n    }\n    const { featuredProduct, marketStats } = data;\n    const moverColor = marketStats.topMover.isUp ? \"text-green-400\" : \"text-red-400\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-grow grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FeaturedProduct__WEBPACK_IMPORTED_MODULE_2__.FeaturedProduct, {\n                    product: featuredProduct\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                title: \"Market Stats\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col justify-center h-full gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Market Cap\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: marketStats.marketCap\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"24h Volume\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: marketStats.volume24h\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Top Mover\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: `text-xl font-bold ${moverColor}`,\n                                    children: [\n                                        marketStats.topMover.name,\n                                        \" \",\n                                        marketStats.topMover.change\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n        lineNumber: 53,\n        columnNumber: 9\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/Market/MarketView.tsx\n");

/***/ })

};
;
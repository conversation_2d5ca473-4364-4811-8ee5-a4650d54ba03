// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`iframe widget should snapshot on mobile: first mobile render 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        Try matching the target image

        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <iframe
            allowfullscreen=""
            sandbox="allow-same-origin allow-scripts allow-top-navigation"
            src="https://www.khanacademy.org/computer-programming/program/4960944252/embedded?buttons=no&embed=yes&editor=no&author=no&width=410&height=410&origin=origin-test-interface&settings=%7B%22hue%22%3A%22210%22%2C%22subdivisions%22%3A%220%22%2C%22zoom%22%3A%222%22%2C%22seed%22%3A%226%22%7D"
            style="width: 410px; height: 410px;"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`iframe widget should snapshot: first render 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        Try matching the target image

        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <iframe
            allowfullscreen=""
            sandbox="allow-same-origin allow-scripts allow-top-navigation"
            src="https://www.khanacademy.org/computer-programming/program/4960944252/embedded?buttons=no&embed=yes&editor=no&author=no&width=410&height=410&origin=origin-test-interface&settings=%7B%22hue%22%3A%22210%22%2C%22subdivisions%22%3A%220%22%2C%22zoom%22%3A%222%22%2C%22seed%22%3A%226%22%7D"
            style="width: 410px; height: 410px;"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

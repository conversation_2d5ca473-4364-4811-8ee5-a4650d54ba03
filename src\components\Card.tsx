import React from 'react';

interface CardProps {
  title?: string;
  headerIcon?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  titleClassName?: string;
}

export const Card: React.FC<CardProps> = ({ title, children, className = '', headerIcon, titleClassName = '' }) => {
  return (
    <div className={`bg-panel-bg border border-gray-700/50 rounded-xl p-4 flex flex-col h-full ${className}`}>
      {title && (
        <div className="flex justify-between items-center mb-3">
          <h3 className={`text-xs uppercase text-[#A0A0B0] font-semibold tracking-wider ${titleClassName}`}>{title}</h3>
          {headerIcon && <div className="text-[#A0A0B0]">{headerIcon}</div>}
        </div>
      )}
      <div className="flex-grow flex flex-col">
        {children}
      </div>
    </div>
  );
};
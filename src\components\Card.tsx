import React from 'react';

interface CardProps {
  title?: string;
  headerIcon?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  titleClassName?: string;
}

export const Card: React.FC<CardProps> = ({ title, children, className = '', headerIcon, titleClassName = '' }) => {
  return (
    <div className={`bg-panel-bg border border-gray-700/50 responsive-border-radius flex flex-col h-full overflow-hidden ${className}`} style={{ padding: 'var(--base-card-padding)' }}>
      {title && (
        <div className="flex justify-between items-center" style={{ marginBottom: 'calc(var(--base-gap) * 0.5)' }}>
          <h3 className={`uppercase text-[#A0A0B0] font-semibold tracking-wider ${titleClassName}`} style={{ fontSize: 'calc(var(--base-font-size) * 0.75)' }}>{title}</h3>
          {headerIcon && <div className="text-[#A0A0B0]" style={{ width: 'calc(var(--base-icon-size) * 0.75)', height: 'calc(var(--base-icon-size) * 0.75)' }}>{headerIcon}</div>}
        </div>
      )}
      <div className="flex-grow flex flex-col min-h-0 overflow-hidden">
        {children}
      </div>
    </div>
  );
};
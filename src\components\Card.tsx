import React from 'react';

interface CardProps {
  title?: string;
  headerIcon?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  titleClassName?: string;
}

export const Card: React.FC<CardProps> = ({ title, children, className = '', headerIcon, titleClassName = '' }) => {
  const isHudCard = className.includes('hud-card');

  return (
    <div
      className={`
        ${isHudCard
          ? 'hud-card'
          : 'bg-panel-bg border border-gray-700/50'
        }
        responsive-border-radius flex flex-col h-full overflow-hidden ${className}
      `}
      style={{ padding: isHudCard ? 'calc(var(--base-card-padding) * 0.75)' : 'var(--base-card-padding)' }}
    >
      {title && (
        <div className="flex justify-between items-center" style={{ marginBottom: 'calc(var(--base-gap) * 0.75)' }}>
          <h3
            className={`
              uppercase font-semibold tracking-wider ${titleClassName}
              ${isHudCard
                ? 'text-white'
                : 'text-[#A0A0B0]'
              }
            `}
            style={{
              fontSize: 'calc(var(--base-font-size) * 0.8)',
              textShadow: isHudCard ? '0 0 10px rgba(0, 207, 255, 0.6)' : 'none'
            }}
          >
            {title}
          </h3>
          {headerIcon && (
            <div
              className={isHudCard ? 'text-cyan-400' : 'text-[#A0A0B0]'}
              style={{
                width: 'calc(var(--base-icon-size) * 0.75)',
                height: 'calc(var(--base-icon-size) * 0.75)',
                filter: isHudCard ? 'drop-shadow(0 0 6px rgba(0, 207, 255, 0.6))' : 'none'
              }}
            >
              {headerIcon}
            </div>
          )}
        </div>
      )}
      <div className="flex-grow flex flex-col min-h-0 overflow-hidden card-content">
        {children}
      </div>
    </div>
  );
};
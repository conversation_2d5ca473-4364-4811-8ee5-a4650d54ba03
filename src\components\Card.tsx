import React from 'react';

interface CardProps {
  title?: string;
  headerIcon?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  titleClassName?: string;
}

export const Card: React.FC<CardProps> = ({ title, children, className = '', headerIcon, titleClassName = '' }) => {
  return (
    <div className={`bg-panel-bg border border-gray-700/50 responsive-border-radius responsive-card flex flex-col h-full ${className}`}>
      {title && (
        <div className="flex justify-between items-center responsive-gap-sm" style={{ marginBottom: 'calc(var(--base-gap) * 0.75)' }}>
          <h3 className={`responsive-text-xs uppercase text-[#A0A0B0] font-semibold tracking-wider ${titleClassName}`}>{title}</h3>
          {headerIcon && <div className="text-[#A0A0B0] responsive-icon" style={{ width: 'calc(var(--base-icon-size) * 0.8)', height: 'calc(var(--base-icon-size) * 0.8)' }}>{headerIcon}</div>}
        </div>
      )}
      <div className="flex-grow flex flex-col">
        {children}
      </div>
    </div>
  );
};
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/views/Coder/CoderView.tsx":
/*!***************************************!*\
  !*** ./src/views/Coder/CoderView.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CoderView: function() { return /* binding */ CoderView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"./node_modules/.pnpm/@monaco-editor+react@4.7.0__1ce2f918ff59bd31eee90d0f34fee1f7/node_modules/@monaco-editor/react/dist/index.mjs\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst FileTreeNode = (param)=>{\n    let { item, level, onFileClick } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(item.isOpen || false);\n    const handleClick = ()=>{\n        if (item.type === \"folder\") {\n            setIsOpen(!isOpen);\n        } else {\n            onFileClick(item.name);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2 px-2 py-1 hover:bg-gray-700/50 cursor-pointer text-sm\",\n                style: {\n                    paddingLeft: \"\".concat(level * 16 + 8, \"px\")\n                },\n                onClick: handleClick,\n                children: [\n                    item.type === \"folder\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-400\",\n                        children: isOpen ? \"\\uD83D\\uDCC2\" : \"\\uD83D\\uDCC1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, undefined),\n                    item.type === \"file\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-blue-400\",\n                        children: \"\\uD83D\\uDCC4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-300\",\n                        children: item.name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined),\n            item.type === \"folder\" && isOpen && item.children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: item.children.map((child, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileTreeNode, {\n                        item: child,\n                        level: level + 1,\n                        onFileClick: onFileClick\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 44,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FileTreeNode, \"1NLt9oXF2DSJYlKhLhMlvItPqek=\");\n_c = FileTreeNode;\nconst CoderView = ()=>{\n    var _terminals_find;\n    _s1();\n    const [activeFile, setActiveFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"App.tsx\");\n    const [openFiles, setOpenFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"App.tsx\"\n    ]);\n    const [showTerminal, setShowTerminal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sidebarTab, setSidebarTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"files\");\n    const [showNewFileDialog, setShowNewFileDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNewProjectDialog, setShowNewProjectDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newFileName, setNewFileName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [newProjectName, setNewProjectName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [terminals, setTerminals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"main\",\n            name: \"Terminal 1\",\n            output: [\n                \"Welcome to React IDE Terminal\",\n                \"$ npm install\",\n                \"✓ Dependencies installed successfully\",\n                \"$ npm start\",\n                \"Starting development server...\",\n                \"Local: http://localhost:3000\",\n                \"webpack compiled successfully\",\n                \"$ \"\n            ]\n        }\n    ]);\n    const [activeTerminal, setActiveTerminal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"main\");\n    const [terminalInput, setTerminalInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [files, setFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"App.tsx\": \"import React, { useState } from 'react';\\nimport './App.css';\\n\\nfunction App() {\\n  const [count, setCount] = useState(0);\\n\\n  return (\\n    <div className=\\\"App\\\">\\n      <header className=\\\"App-header\\\">\\n        <h1>Welcome to React IDE</h1>\\n        <p>Count: {count}</p>\\n        <button onClick={() => setCount(count + 1)}>\\n          Increment\\n        </button>\\n        <button onClick={() => setCount(count - 1)}>\\n          Decrement\\n        </button>\\n      </header>\\n    </div>\\n  );\\n}\\n\\nexport default App;\",\n        \"App.css\": \"body {\\n  margin: 0;\\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\\n    sans-serif;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n.App {\\n  text-align: center;\\n}\\n\\n.App-header {\\n  background-color: #282c34;\\n  padding: 20px;\\n  color: white;\\n  min-height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: calc(10px + 2vmin);\\n}\\n\\nbutton {\\n  background-color: #61dafb;\\n  border: none;\\n  padding: 10px 20px;\\n  margin: 10px;\\n  border-radius: 5px;\\n  cursor: pointer;\\n  font-size: 16px;\\n}\\n\\nbutton:hover {\\n  background-color: #21a1c4;\\n}\",\n        \"package.json\": '{\\n  \"name\": \"react-ide-project\",\\n  \"version\": \"1.0.0\",\\n  \"private\": true,\\n  \"dependencies\": {\\n    \"react\": \"^18.2.0\",\\n    \"react-dom\": \"^18.2.0\",\\n    \"typescript\": \"^4.9.5\"\\n  },\\n  \"scripts\": {\\n    \"start\": \"react-scripts start\",\\n    \"build\": \"react-scripts build\",\\n    \"test\": \"react-scripts test\",\\n    \"eject\": \"react-scripts eject\"\\n  },\\n  \"eslintConfig\": {\\n    \"extends\": [\\n      \"react-app\",\\n      \"react-app/jest\"\\n    ]\\n  },\\n  \"browserslist\": {\\n    \"production\": [\\n      \">0.2%\",\\n      \"not dead\",\\n      \"not op_mini all\"\\n    ],\\n    \"development\": [\\n      \"last 1 chrome version\",\\n      \"last 1 firefox version\",\\n      \"last 1 safari version\"\\n    ]\\n  }\\n}',\n        \"README.md\": \"# React IDE Project\\n\\nThis is a sample React project created in the IDE.\\n\\n## Available Scripts\\n\\n- `npm start` - Runs the app in development mode\\n- `npm build` - Builds the app for production\\n- `npm test` - Launches the test runner\\n\\n## Features\\n\\n- React with TypeScript\\n- Modern CSS styling\\n- Interactive components\\n- Hot reloading\\n\\n## Getting Started\\n\\n1. Install dependencies: `npm install`\\n2. Start the development server: `npm start`\\n3. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.\\n\"\n    });\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileTree = [\n        {\n            name: \"src\",\n            type: \"folder\",\n            isOpen: true,\n            children: [\n                {\n                    name: \"App.tsx\",\n                    type: \"file\"\n                },\n                {\n                    name: \"App.css\",\n                    type: \"file\"\n                },\n                {\n                    name: \"index.tsx\",\n                    type: \"file\"\n                }\n            ]\n        },\n        {\n            name: \"public\",\n            type: \"folder\",\n            children: [\n                {\n                    name: \"index.html\",\n                    type: \"file\"\n                },\n                {\n                    name: \"favicon.ico\",\n                    type: \"file\"\n                }\n            ]\n        },\n        {\n            name: \"package.json\",\n            type: \"file\"\n        },\n        {\n            name: \"README.md\",\n            type: \"file\"\n        }\n    ];\n    const handleEditorDidMount = (editor, monaco)=>{\n        editorRef.current = editor;\n        // Configure Monaco Editor\n        monaco.editor.defineTheme(\"vs-dark-custom\", {\n            base: \"vs-dark\",\n            inherit: true,\n            rules: [],\n            colors: {\n                \"editor.background\": \"#1e1e1e\",\n                \"editor.foreground\": \"#d4d4d4\",\n                \"editorLineNumber.foreground\": \"#858585\",\n                \"editor.selectionBackground\": \"#264f78\",\n                \"editor.inactiveSelectionBackground\": \"#3a3d41\"\n            }\n        });\n        monaco.editor.setTheme(\"vs-dark-custom\");\n    };\n    const handleEditorChange = (value)=>{\n        if (value !== undefined && activeFile) {\n            setFiles((prev)=>({\n                    ...prev,\n                    [activeFile]: value\n                }));\n        }\n    };\n    const handleFileClick = (fileName)=>{\n        // Only open files that exist in our files object\n        if (files[fileName]) {\n            setActiveFile(fileName);\n            if (!openFiles.includes(fileName)) {\n                setOpenFiles([\n                    ...openFiles,\n                    fileName\n                ]);\n            }\n        }\n    };\n    const closeFile = (fileName)=>{\n        const newOpenFiles = openFiles.filter((f)=>f !== fileName);\n        setOpenFiles(newOpenFiles);\n        if (activeFile === fileName && newOpenFiles.length > 0) {\n            setActiveFile(newOpenFiles[newOpenFiles.length - 1]);\n        } else if (newOpenFiles.length === 0) {\n            setActiveFile(\"\");\n        }\n    };\n    const getFileLanguage = (fileName)=>{\n        const ext = fileName.split(\".\").pop();\n        switch(ext){\n            case \"tsx\":\n            case \"ts\":\n                return \"typescript\";\n            case \"jsx\":\n            case \"js\":\n                return \"javascript\";\n            case \"css\":\n                return \"css\";\n            case \"json\":\n                return \"json\";\n            case \"md\":\n                return \"markdown\";\n            case \"html\":\n                return \"html\";\n            default:\n                return \"plaintext\";\n        }\n    };\n    // File Management Functions\n    const createNewFile = ()=>{\n        if (newFileName.trim()) {\n            const language = getFileLanguage(newFileName);\n            const template = getFileTemplate(language, newFileName);\n            setFiles((prev)=>({\n                    ...prev,\n                    [newFileName]: template\n                }));\n            setActiveFile(newFileName);\n            if (!openFiles.includes(newFileName)) {\n                setOpenFiles([\n                    ...openFiles,\n                    newFileName\n                ]);\n            }\n            setNewFileName(\"\");\n            setShowNewFileDialog(false);\n        }\n    };\n    const getFileTemplate = (language, fileName)=>{\n        switch(language){\n            case \"typescript\":\n                if (fileName.endsWith(\".tsx\")) {\n                    return \"import React from 'react';\\n\\ninterface \".concat(fileName.replace(\".tsx\", \"\"), \"Props {\\n  // Define your props here\\n}\\n\\nconst \").concat(fileName.replace(\".tsx\", \"\"), \": React.FC<\").concat(fileName.replace(\".tsx\", \"\"), \"Props> = () => {\\n  return (\\n    <div>\\n      <h1>Hello from \").concat(fileName.replace(\".tsx\", \"\"), \"</h1>\\n    </div>\\n  );\\n};\\n\\nexport default \").concat(fileName.replace(\".tsx\", \"\"), \";\");\n                }\n                return \"// \".concat(fileName, \"\\nexport interface Example {\\n  id: number;\\n  name: string;\\n}\\n\\nexport const exampleFunction = (): Example => {\\n  return {\\n    id: 1,\\n    name: 'Example'\\n  };\\n};\");\n            case \"javascript\":\n                return \"// \".concat(fileName, \"\\nfunction exampleFunction() {\\n  console.log('Hello from \").concat(fileName, \"');\\n}\\n\\nexport default exampleFunction;\");\n            case \"css\":\n                return \"/* \".concat(fileName, \" */\\n.container {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n\\n.title {\\n  font-size: 2rem;\\n  color: white;\\n  margin-bottom: 1rem;\\n}\");\n            case \"html\":\n                return '<!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>'.concat(fileName.replace(\".html\", \"\"), \"</title>\\n</head>\\n<body>\\n    <h1>Hello World</h1>\\n    <p>Welcome to \").concat(fileName, \"</p>\\n</body>\\n</html>\");\n            case \"json\":\n                return '{\\n  \"name\": \"'.concat(fileName.replace(\".json\", \"\"), '\",\\n  \"version\": \"1.0.0\",\\n  \"description\": \"Generated JSON file\"\\n}');\n            case \"markdown\":\n                return \"# \".concat(fileName.replace(\".md\", \"\"), \"\\n\\nThis is a new markdown file.\\n\\n## Features\\n\\n- Feature 1\\n- Feature 2\\n- Feature 3\\n\\n## Usage\\n\\n```javascript\\nconsole.log('Hello World');\\n```\");\n            default:\n                return \"// \".concat(fileName, \"\\n// Start coding here...\");\n        }\n    };\n    // Project Management Functions\n    const createNewProject = ()=>{\n        if (newProjectName.trim()) {\n            const projectFiles = {\n                [\"\".concat(newProjectName, \"/src/App.tsx\")]: \"import React from 'react';\\nimport './App.css';\\n\\nfunction App() {\\n  return (\\n    <div className=\\\"App\\\">\\n      <header className=\\\"App-header\\\">\\n        <h1>Welcome to \".concat(newProjectName, \"</h1>\\n        <p>Your new React project is ready!</p>\\n      </header>\\n    </div>\\n  );\\n}\\n\\nexport default App;\"),\n                [\"\".concat(newProjectName, \"/src/App.css\")]: \".App {\\n  text-align: center;\\n}\\n\\n.App-header {\\n  background-color: #282c34;\\n  padding: 20px;\\n  color: white;\\n  min-height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: calc(10px + 2vmin);\\n}\",\n                [\"\".concat(newProjectName, \"/package.json\")]: '{\\n  \"name\": \"'.concat(newProjectName.toLowerCase(), '\",\\n  \"version\": \"0.1.0\",\\n  \"private\": true,\\n  \"dependencies\": {\\n    \"react\": \"^18.2.0\",\\n    \"react-dom\": \"^18.2.0\",\\n    \"typescript\": \"^4.9.5\"\\n  },\\n  \"scripts\": {\\n    \"start\": \"react-scripts start\",\\n    \"build\": \"react-scripts build\",\\n    \"test\": \"react-scripts test\",\\n    \"eject\": \"react-scripts eject\"\\n  }\\n}'),\n                [\"\".concat(newProjectName, \"/README.md\")]: \"# \".concat(newProjectName, \"\\n\\nThis project was created with React IDE.\\n\\n## Available Scripts\\n\\n- `npm start` - Runs the app in development mode\\n- `npm build` - Builds the app for production\\n- `npm test` - Launches the test runner\\n\\n## Getting Started\\n\\n1. Install dependencies: `npm install`\\n2. Start the development server: `npm start`\\n3. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.\\n\")\n            };\n            setFiles((prev)=>({\n                    ...prev,\n                    ...projectFiles\n                }));\n            setActiveFile(\"\".concat(newProjectName, \"/src/App.tsx\"));\n            setOpenFiles([\n                \"\".concat(newProjectName, \"/src/App.tsx\")\n            ]);\n            setNewProjectName(\"\");\n            setShowNewProjectDialog(false);\n        }\n    };\n    // Terminal Management Functions\n    const createNewTerminal = ()=>{\n        const newTerminalId = \"terminal-\".concat(Date.now());\n        const newTerminal = {\n            id: newTerminalId,\n            name: \"Terminal \".concat(terminals.length + 1),\n            output: [\n                \"Welcome to Terminal \".concat(terminals.length + 1),\n                \"$ \"\n            ]\n        };\n        setTerminals([\n            ...terminals,\n            newTerminal\n        ]);\n        setActiveTerminal(newTerminalId);\n    };\n    const executeCommand = (command)=>{\n        const currentTerminal = terminals.find((t)=>t.id === activeTerminal);\n        if (!currentTerminal) return;\n        const updatedTerminals = terminals.map((terminal)=>{\n            if (terminal.id === activeTerminal) {\n                const newOutput = [\n                    ...terminal.output\n                ];\n                newOutput[newOutput.length - 1] = \"$ \".concat(command);\n                // Simulate command execution\n                const response = simulateCommand(command);\n                newOutput.push(...response);\n                newOutput.push(\"$ \");\n                return {\n                    ...terminal,\n                    output: newOutput\n                };\n            }\n            return terminal;\n        });\n        setTerminals(updatedTerminals);\n        setTerminalInput(\"\");\n    };\n    const simulateCommand = (command)=>{\n        const cmd = command.toLowerCase().trim();\n        if (cmd === \"ls\" || cmd === \"dir\") {\n            return [\n                \"App.tsx\",\n                \"App.css\",\n                \"package.json\",\n                \"README.md\"\n            ];\n        } else if (cmd === \"pwd\") {\n            return [\n                \"/workspace/my-project\"\n            ];\n        } else if (cmd.startsWith(\"npm install\")) {\n            return [\n                \"Installing dependencies...\",\n                \"✓ Dependencies installed successfully\"\n            ];\n        } else if (cmd === \"npm start\") {\n            return [\n                \"Starting development server...\",\n                \"Local: http://localhost:3000\",\n                \"webpack compiled successfully\"\n            ];\n        } else if (cmd === \"npm build\") {\n            return [\n                \"Building for production...\",\n                \"✓ Build completed successfully\"\n            ];\n        } else if (cmd === \"git status\") {\n            return [\n                \"On branch main\",\n                \"Your branch is up to date with origin/main\",\n                \"nothing to commit, working tree clean\"\n            ];\n        } else if (cmd.startsWith(\"git add\")) {\n            return [\n                \"Files staged for commit\"\n            ];\n        } else if (cmd.startsWith(\"git commit\")) {\n            return [\n                \"[main abc123] Your commit message\",\n                \"2 files changed, 10 insertions(+), 5 deletions(-)\"\n            ];\n        } else if (cmd === \"clear\" || cmd === \"cls\") {\n            return [\n                \"\"\n            ];\n        } else if (cmd === \"help\") {\n            return [\n                \"Available commands:\",\n                \"  ls/dir     - List files\",\n                \"  pwd        - Show current directory\",\n                \"  npm install - Install dependencies\",\n                \"  npm start  - Start development server\",\n                \"  npm build  - Build for production\",\n                \"  git status - Show git status\",\n                \"  git add    - Stage files\",\n                \"  git commit - Commit changes\",\n                \"  clear/cls  - Clear terminal\",\n                \"  help       - Show this help\"\n            ];\n        } else if (cmd === \"\") {\n            return [];\n        } else {\n            return [\n                \"Command not found: \".concat(command),\n                'Type \"help\" for available commands'\n            ];\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-panel-bg rounded-xl overflow-hidden flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-64 bg-gray-900/50 border-r border-gray-700/50 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex border-b border-gray-700/50\",\n                        children: [\n                            {\n                                id: \"files\",\n                                icon: \"\\uD83D\\uDCC1\",\n                                label: \"Files\"\n                            },\n                            {\n                                id: \"git\",\n                                icon: \"\\uD83D\\uDD00\",\n                                label: \"Git\"\n                            },\n                            {\n                                id: \"extensions\",\n                                icon: \"\\uD83E\\uDDE9\",\n                                label: \"Extensions\"\n                            }\n                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSidebarTab(tab.id),\n                                className: \"flex-1 p-3 text-sm font-medium transition-colors duration-200 \".concat(sidebarTab === tab.id ? \"bg-blue-500/20 text-blue-400 border-b-2 border-blue-400\" : \"text-gray-400 hover:text-white hover:bg-gray-800/60\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-2\",\n                                        children: tab.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 580,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    tab.label\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 571,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 565,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: [\n                            sidebarTab === \"files\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 p-2 border-b border-gray-700/50 bg-gray-800/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowNewFileDialog(true),\n                                                className: \"flex items-center gap-1 px-3 py-1.5 text-xs bg-blue-600/20 text-blue-400 rounded hover:bg-blue-600/30 transition-colors\",\n                                                title: \"New File\",\n                                                children: \"\\uD83D\\uDCC4 New File\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 592,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowNewProjectDialog(true),\n                                                className: \"flex items-center gap-1 px-3 py-1.5 text-xs bg-green-600/20 text-green-400 rounded hover:bg-green-600/30 transition-colors\",\n                                                title: \"New Project\",\n                                                children: \"\\uD83D\\uDCC1 New Project\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 599,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 591,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-gray-400 text-xs uppercase font-semibold mb-2 px-2\",\n                                                children: \"Explorer\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            fileTree.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileTreeNode, {\n                                                    item: item,\n                                                    level: 0,\n                                                    onFileClick: handleFileClick\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                    lineNumber: 611,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 608,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 13\n                            }, undefined),\n                            sidebarTab === \"git\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-white font-semibold mb-4\",\n                                        children: \"Source Control\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 624,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-green-400 text-sm\",\n                                                children: \"✓ 3 files staged\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-yellow-400 text-sm\",\n                                                children: \"⚠ 2 files modified\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-red-400 text-sm\",\n                                                children: \"✗ 1 file deleted\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 623,\n                                columnNumber: 13\n                            }, undefined),\n                            sidebarTab === \"extensions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-white font-semibold mb-4\",\n                                        children: \"Extensions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 635,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-blue-500 rounded flex items-center justify-center text-xs\",\n                                                        children: \"TS\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 638,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white text-sm\",\n                                                                children: \"TypeScript\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                                lineNumber: 640,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-400 text-xs\",\n                                                                children: \"Installed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                                lineNumber: 641,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 639,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-green-500 rounded flex items-center justify-center text-xs\",\n                                                        children: \"ES\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white text-sm\",\n                                                                children: \"ESLint\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                                lineNumber: 647,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-400 text-xs\",\n                                                                children: \"Installed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                                lineNumber: 648,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 644,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 634,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 587,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 563,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-800/40 border-b border-gray-700/50\",\n                        children: openFiles.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 px-4 py-2 border-r border-gray-700/50 cursor-pointer \".concat(activeFile === file ? \"bg-panel-bg text-white\" : \"text-gray-400 hover:text-white hover:bg-gray-800/60\"),\n                                onClick: ()=>setActiveFile(file),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: file\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 671,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            closeFile(file);\n                                        },\n                                        className: \"text-gray-500 hover:text-white\",\n                                        children: \"\\xd7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 672,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, file, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 660,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: activeFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            height: \"100%\",\n                            language: getFileLanguage(activeFile),\n                            value: files[activeFile] || \"\",\n                            onChange: handleEditorChange,\n                            theme: \"vs-dark\",\n                            options: {\n                                fontSize: 14,\n                                fontFamily: \"Fira Code, Monaco, Consolas, monospace\",\n                                minimap: {\n                                    enabled: true\n                                },\n                                scrollBeyondLastLine: false,\n                                automaticLayout: true,\n                                tabSize: 2,\n                                insertSpaces: true,\n                                wordWrap: \"on\",\n                                lineNumbers: \"on\",\n                                renderWhitespace: \"selection\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                            lineNumber: 688,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-full bg-gray-900/30\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-6xl mb-4\",\n                                        children: \"\\uD83D\\uDCBB\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 710,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-white text-xl mb-2\",\n                                        children: \"Welcome to the IDE\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 711,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Select a file from the explorer to start coding\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 712,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 709,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                            lineNumber: 708,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 686,\n                        columnNumber: 9\n                    }, undefined),\n                    showTerminal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-64 bg-black border-t border-gray-700/50 flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between px-4 py-2 bg-gray-800/60 border-b border-gray-700/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-semibold text-sm\",\n                                                        children: \"Terminal\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 725,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400 text-xs\",\n                                                        children: \"bash\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 726,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 724,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    terminals.map((terminal)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setActiveTerminal(terminal.id),\n                                                            className: \"px-3 py-1 text-xs rounded-t \".concat(activeTerminal === terminal.id ? \"bg-black text-green-400 border-t border-l border-r border-gray-600\" : \"bg-gray-700/50 text-gray-400 hover:text-white hover:bg-gray-600/50\"),\n                                                            children: terminal.name\n                                                        }, terminal.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                            lineNumber: 732,\n                                                            columnNumber: 21\n                                                        }, undefined)),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: createNewTerminal,\n                                                        className: \"px-2 py-1 text-xs bg-blue-600/20 text-blue-400 rounded hover:bg-blue-600/30 transition-colors ml-2\",\n                                                        title: \"New Terminal\",\n                                                        children: \"+\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 744,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 730,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 723,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowTerminal(false),\n                                            className: \"text-gray-400 hover:text-white text-sm px-2 py-1 rounded hover:bg-gray-700/50\",\n                                            children: \"\\xd7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 755,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 754,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 722,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-4 overflow-y-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-green-400 font-mono text-sm\",\n                                    children: [\n                                        (_terminals_find = terminals.find((t)=>t.id === activeTerminal)) === null || _terminals_find === void 0 ? void 0 : _terminals_find.output.map((line, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: line.startsWith(\"$\") ? \"text-green-400\" : \"text-gray-300\",\n                                                children: line\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 768,\n                                                columnNumber: 19\n                                            }, undefined)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400\",\n                                                    children: \"$ \"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                    lineNumber: 775,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: terminalInput,\n                                                    onChange: (e)=>setTerminalInput(e.target.value),\n                                                    onKeyDown: (e)=>{\n                                                        if (e.key === \"Enter\") {\n                                                            executeCommand(terminalInput);\n                                                        }\n                                                    },\n                                                    className: \"bg-transparent border-none outline-none text-green-400 ml-1 flex-1 font-mono\",\n                                                    placeholder: \"Type a command...\",\n                                                    autoFocus: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                    lineNumber: 776,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 774,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                    lineNumber: 766,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 765,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 720,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 658,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-80 bg-gray-900/50 border-l border-gray-700/50 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-white font-semibold mb-4\",\n                        children: \"\\uD83E\\uDD16 AI Assistant\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 798,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800/40 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-400 text-sm font-medium mb-1\",\n                                        children: \"Code Suggestion\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 801,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Consider adding error handling to your React component.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 802,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 800,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800/40 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-green-400 text-sm font-medium mb-1\",\n                                        children: \"Performance Tip\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 807,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Use React.memo() to optimize component re-renders.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 808,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 806,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 799,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowTerminal(!showTerminal),\n                            className: \"w-full bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 py-2 px-4 rounded-lg transition-colors duration-200\",\n                            children: showTerminal ? \"Hide Terminal\" : \"Show Terminal\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                            lineNumber: 815,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 814,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 797,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n        lineNumber: 561,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CoderView, \"4uPe9AN0uU32KrKp+cFQwxJnCow=\");\n_c1 = CoderView;\nvar _c, _c1;\n$RefreshReg$(_c, \"FileTreeNode\");\n$RefreshReg$(_c1, \"CoderView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/Coder/CoderView.tsx\n"));

/***/ })

});
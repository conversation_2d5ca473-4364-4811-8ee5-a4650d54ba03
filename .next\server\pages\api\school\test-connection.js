"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/school/test-connection";
exports.ids = ["pages/api/school/test-connection"];
exports.modules = {

/***/ "mysql2/promise":
/*!*********************************!*\
  !*** external "mysql2/promise" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("mysql2/promise");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool%2Ftest-connection&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool%5Ctest-connection.ts&middlewareConfigBase64=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool%2Ftest-connection&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool%5Ctest-connection.ts&middlewareConfigBase64=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_school_test_connection_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\school\\test-connection.ts */ \"(api)/./pages/api/school/test-connection.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_school_test_connection_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_school_test_connection_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/school/test-connection\",\n        pathname: \"/api/school/test-connection\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_school_test_connection_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool%2Ftest-connection&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool%5Ctest-connection.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./pages/api/school/test-connection.ts":
/*!*********************************************!*\
  !*** ./pages/api/school/test-connection.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mysql2/promise */ \"mysql2/promise\");\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mysql2_promise__WEBPACK_IMPORTED_MODULE_0__);\n\n// Database connection configuration\nconst dbConfig = {\n    host: process.env.DB_HOST || \"localhost\",\n    user: process.env.DB_USER || \"root\",\n    password: process.env.DB_PASSWORD || \"\",\n    database: process.env.DB_NAME || \"school_management\",\n    port: parseInt(process.env.DB_PORT || \"3306\")\n};\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        console.log(\"Testing database connection with config:\", {\n            host: dbConfig.host,\n            user: dbConfig.user,\n            database: dbConfig.database,\n            port: dbConfig.port\n        });\n        const connection = await mysql2_promise__WEBPACK_IMPORTED_MODULE_0___default().createConnection(dbConfig);\n        try {\n            // Test the connection\n            const [rows] = await connection.execute(\"SELECT 1 as test\");\n            // Get database info\n            const [dbInfo] = await connection.execute(\"SELECT DATABASE() as current_db, VERSION() as version\");\n            res.status(200).json({\n                message: \"Database connection successful\",\n                config: {\n                    host: dbConfig.host,\n                    user: dbConfig.user,\n                    database: dbConfig.database,\n                    port: dbConfig.port\n                },\n                info: dbInfo[0],\n                test: rows[0]\n            });\n        } finally{\n            await connection.end();\n        }\n    } catch (error) {\n        console.error(\"Database connection error:\", error);\n        res.status(500).json({\n            message: \"Database connection failed\",\n            error: error.message,\n            config: {\n                host: dbConfig.host,\n                user: dbConfig.user,\n                database: dbConfig.database,\n                port: dbConfig.port\n            }\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/school/test-connection.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0"], () => (__webpack_exec__("(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool%2Ftest-connection&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool%5Ctest-connection.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();
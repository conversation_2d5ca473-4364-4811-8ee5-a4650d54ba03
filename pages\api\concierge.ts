
import type { NextApiRequest, NextApiResponse } from 'next';
import { generateStructuredData, Type } from '../../backend/lib/gemini';

export type Notification = {
    id: string;
    message: string;
    time: string;
};

export type ServiceRequestData = {
    serviceType: string;
    details: string;
};


const notificationSchema = {
  type: Type.ARRAY,
  description: "A list of 3 recent notifications for a futuristic concierge service.",
  items: {
    type: Type.OBJECT,
    properties: {
      message: { type: Type.STRING, description: "The content of the notification. Be creative and futuristic." },
      time: { type: Type.STRING, description: "A relative time, e.g., '5m ago' or '1h ago'."}
    },
    required: ["message", "time"]
  }
};


export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method === 'GET') {
    try {
        const prompt = "Generate a list of 3 recent, varied notifications for a user of a futuristic concierge service. Examples: 'Your auto-valet transport is arriving', 'Dinner reservation at 'The Orbit' confirmed', 'Quantum-courier package has been delivered to docking bay 7'.";
        const generatedNotifications = await generateStructuredData<{message: string, time: string}[]>(prompt, notificationSchema);
        const notifications = generatedNotifications.map((n, i) => ({ ...n, id: `${Date.now()}-${i}`}));
        return res.status(200).json(notifications);
    } catch(e) {
        console.error("Failed to fetch notifications from AI:", e);
        return res.status(500).json({ error: "Failed to fetch notifications" });
    }
  }
  
  if (req.method === 'POST') {
    const requestData: ServiceRequestData = req.body;
    
    if (!requestData.serviceType || !requestData.details) {
      return res.status(400).json({ error: 'Missing serviceType or details in request body.' });
    }

    // In a real application, this is where you would process the request:
    // - Save it to a database
    // - Send it to a message queue
    // - Trigger other services
    // Since we have no database, we will just acknowledge the request.
    console.log("Received stateless service request:", requestData);

    return res.status(201).json({ message: 'Request submitted successfully' });
  }
  
  res.setHeader('Allow', ['GET', 'POST']);
  res.status(405).end(`Method ${req.method} Not Allowed`);
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/school";
exports.ids = ["pages/api/school"];
exports.modules = {

/***/ "@google/genai":
/*!********************************!*\
  !*** external "@google/genai" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("@google/genai");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool.ts&middlewareConfigBase64=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool.ts&middlewareConfigBase64=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_school_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\school.ts */ \"(api)/./pages/api/school.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_school_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_school_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/school\",\n        pathname: \"/api/school\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_school_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./backend/lib/gemini.ts":
/*!*******************************!*\
  !*** ./backend/lib/gemini.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Type: () => (/* reexport safe */ _google_genai__WEBPACK_IMPORTED_MODULE_0__.Type),\n/* harmony export */   generateStructuredData: () => (/* binding */ generateStructuredData),\n/* harmony export */   generateText: () => (/* binding */ generateText)\n/* harmony export */ });\n/* harmony import */ var _google_genai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/genai */ \"@google/genai\");\n/* harmony import */ var _google_genai__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_google_genai__WEBPACK_IMPORTED_MODULE_0__);\n\nconst hasValidApiKey =  false && 0;\nlet ai = null;\nif (hasValidApiKey) {\n    ai = new _google_genai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenAI({\n        apiKey: \"your_actual_gemini_api_key_here\"\n    });\n} else {\n    console.warn(\"⚠️  No valid Gemini API key found. Using fallback mock data. Set GEMINI_API_KEY in .env.local to use real AI features.\");\n}\nasync function generateText(prompt) {\n    if (!ai) {\n        // Fallback mock response for mood analysis\n        return \"System analysis indicates elevated stress levels detected. Recommend implementing relaxation protocols and scheduling wellness check-in within 2 hours.\";\n    }\n    try {\n        const response = await ai.models.generateContent({\n            model: \"gemini-2.5-flash\",\n            contents: prompt\n        });\n        return response.text;\n    } catch (e) {\n        console.error(\"Error generating text from Gemini:\", e);\n        // Return fallback data instead of throwing error\n        return \"System analysis temporarily unavailable. Default wellness protocols active. Please check back later for detailed analysis.\";\n    }\n}\nasync function generateStructuredData(prompt, schema) {\n    if (!ai) {\n        // Return fallback mock data for dashboard widgets\n        const fallbackData = {\n            contacts: [\n                {\n                    name: \"Dr. Aris Thorne\",\n                    detail: \"Chief Scientist\",\n                    avatar: \"aris\"\n                },\n                {\n                    name: \"Commander Zara Vex\",\n                    detail: \"Security Director\",\n                    avatar: \"zara\"\n                },\n                {\n                    name: \"Engineer Kai Nexus\",\n                    detail: \"Systems Architect\",\n                    avatar: \"kai\"\n                }\n            ],\n            safetyItems: [\n                {\n                    label: \"Firewall Active\",\n                    color: \"#00FFFF\"\n                },\n                {\n                    label: \"Quantum Encryption\",\n                    color: \"#00FFFF\"\n                },\n                {\n                    label: \"Bio-Scanner Online\",\n                    color: \"#3b82f6\"\n                },\n                {\n                    label: \"Sub-routine Anomaly\",\n                    color: \"#ef4444\"\n                },\n                {\n                    label: \"Neural Link Stable\",\n                    color: \"#3b82f6\"\n                },\n                {\n                    label: \"Core Temperature\",\n                    color: \"#3b82f6\"\n                }\n            ],\n            safetyChartData: [\n                85,\n                92,\n                78,\n                95,\n                88,\n                91\n            ],\n            systemRequests: [\n                {\n                    text: \"Security Patch Update\"\n                },\n                {\n                    text: \"Cryo-chamber diagnostics\"\n                },\n                {\n                    text: \"Neural interface calibration\"\n                }\n            ],\n            systemStats: [\n                {\n                    icon: \"BuildingOfficeIcon\",\n                    value: \"1597\",\n                    label: \"Processes\"\n                },\n                {\n                    icon: \"PuzzlePieceIcon\",\n                    value: \"84%\",\n                    label: \"RAM Usage\"\n                },\n                {\n                    icon: \"SnowflakeIcon\",\n                    value: \"24\\xb0C\",\n                    label: \"Core Temp\"\n                },\n                {\n                    icon: \"MemoryChipIcon\",\n                    value: \"2.4TB\",\n                    label: \"Storage\"\n                },\n                {\n                    icon: \"CogIcon\",\n                    value: \"99.7%\",\n                    label: \"Uptime\"\n                }\n            ]\n        };\n        return fallbackData;\n    }\n    try {\n        const response = await ai.models.generateContent({\n            model: \"gemini-2.5-flash\",\n            contents: prompt,\n            config: {\n                responseMimeType: \"application/json\",\n                responseSchema: schema\n            }\n        });\n        if (!response.text) {\n            throw new Error(\"Received an empty response from the AI.\");\n        }\n        const cleanedText = response.text.trim().replace(/^```json\\s*|```\\s*$/g, \"\");\n        if (!cleanedText) {\n            throw new Error(\"Received an empty JSON response from the AI after cleaning.\");\n        }\n        return JSON.parse(cleanedText);\n    } catch (e) {\n        console.error(\"Failed to generate or parse Gemini JSON response:\", e);\n        // Return fallback data instead of throwing error\n        const fallbackData = {\n            contacts: [\n                {\n                    name: \"Dr. Aris Thorne\",\n                    detail: \"Chief Scientist\",\n                    avatar: \"aris\"\n                },\n                {\n                    name: \"Commander Zara Vex\",\n                    detail: \"Security Director\",\n                    avatar: \"zara\"\n                },\n                {\n                    name: \"Engineer Kai Nexus\",\n                    detail: \"Systems Architect\",\n                    avatar: \"kai\"\n                }\n            ],\n            safetyItems: [\n                {\n                    label: \"Firewall Active\",\n                    color: \"#00FFFF\"\n                },\n                {\n                    label: \"Quantum Encryption\",\n                    color: \"#00FFFF\"\n                },\n                {\n                    label: \"Bio-Scanner Online\",\n                    color: \"#3b82f6\"\n                },\n                {\n                    label: \"Sub-routine Anomaly\",\n                    color: \"#ef4444\"\n                },\n                {\n                    label: \"Neural Link Stable\",\n                    color: \"#3b82f6\"\n                },\n                {\n                    label: \"Core Temperature\",\n                    color: \"#3b82f6\"\n                }\n            ],\n            safetyChartData: [\n                85,\n                92,\n                78,\n                95,\n                88,\n                91\n            ],\n            systemRequests: [\n                {\n                    text: \"Security Patch Update\"\n                },\n                {\n                    text: \"Cryo-chamber diagnostics\"\n                },\n                {\n                    text: \"Neural interface calibration\"\n                }\n            ],\n            systemStats: [\n                {\n                    icon: \"BuildingOfficeIcon\",\n                    value: \"1597\",\n                    label: \"Processes\"\n                },\n                {\n                    icon: \"PuzzlePieceIcon\",\n                    value: \"84%\",\n                    label: \"RAM Usage\"\n                },\n                {\n                    icon: \"SnowflakeIcon\",\n                    value: \"24\\xb0C\",\n                    label: \"Core Temp\"\n                },\n                {\n                    icon: \"MemoryChipIcon\",\n                    value: \"2.4TB\",\n                    label: \"Storage\"\n                },\n                {\n                    icon: \"CogIcon\",\n                    value: \"99.7%\",\n                    label: \"Uptime\"\n                }\n            ]\n        };\n        return fallbackData;\n    }\n}\n // Re-export Type for convenience\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./backend/lib/gemini.ts\n");

/***/ }),

/***/ "(api)/./pages/api/school.ts":
/*!*****************************!*\
  !*** ./pages/api/school.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../backend/lib/gemini */ \"(api)/./backend/lib/gemini.ts\");\n\nconst schoolSchema = {\n    type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.OBJECT,\n    properties: {\n        overview: {\n            type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.OBJECT,\n            properties: {\n                gpa: {\n                    type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                    description: \"The student's GPA as a string, e.g., '3.85'.\"\n                },\n                credits: {\n                    type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.INTEGER,\n                    description: \"Total credits for the semester (e.g., 12-18).\"\n                },\n                assignmentsDue: {\n                    type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.INTEGER,\n                    description: \"Number of assignments due this week (e.g., 2-5).\"\n                }\n            },\n            required: [\n                \"gpa\",\n                \"credits\",\n                \"assignmentsDue\"\n            ]\n        },\n        courses: {\n            type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.ARRAY,\n            description: \"A list of 3 futuristic university courses.\",\n            items: {\n                type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.OBJECT,\n                properties: {\n                    name: {\n                        type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                        description: \"Name of a futuristic university course (e.g., 'Quantum Mechanics', 'Astrobiology').\"\n                    },\n                    progress: {\n                        type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.INTEGER,\n                        description: \"Progress percentage (0-100).\"\n                    },\n                    color: {\n                        type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                        enum: [\n                            \"cyan\",\n                            \"pink\",\n                            \"yellow\"\n                        ]\n                    }\n                },\n                required: [\n                    \"name\",\n                    \"progress\",\n                    \"color\"\n                ]\n            }\n        },\n        assignments: {\n            type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.ARRAY,\n            description: \"A list of 3 upcoming assignments.\",\n            items: {\n                type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.OBJECT,\n                properties: {\n                    name: {\n                        type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                        description: \"Name of a futuristic assignment.\"\n                    },\n                    due: {\n                        type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                        description: \"A relative due date, e.g., '3 days' or '1 week'.\"\n                    },\n                    course: {\n                        type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                        description: \"The name of the course this assignment belongs to.\"\n                    }\n                },\n                required: [\n                    \"name\",\n                    \"due\",\n                    \"course\"\n                ]\n            }\n        }\n    },\n    required: [\n        \"overview\",\n        \"courses\",\n        \"assignments\"\n    ]\n};\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        res.setHeader(\"Allow\", [\n            \"GET\"\n        ]);\n        return res.status(405).end(`Method ${req.method} Not Allowed`);\n    }\n    try {\n        const prompt = \"Generate mock data for a student's dashboard in a futuristic university. The student is doing well. Provide an overview, 3 courses, and 3 upcoming assignments. Course names should sound advanced and sci-fi. Match the assignment courses to the generated course names.\";\n        const data = await (0,_backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.generateStructuredData)(prompt, schoolSchema);\n        res.status(200).json(data);\n    } catch (error) {\n        console.error(error);\n        res.status(500).json({\n            error: \"Failed to fetch school data from AI.\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/school.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0"], () => (__webpack_exec__("(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();
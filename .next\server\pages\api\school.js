"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/school";
exports.ids = ["pages/api/school"];
exports.modules = {

/***/ "mysql2/promise":
/*!*********************************!*\
  !*** external "mysql2/promise" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("mysql2/promise");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool.ts&middlewareConfigBase64=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool.ts&middlewareConfigBase64=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_school_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\school.ts */ \"(api)/./pages/api/school.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_school_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_school_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/school\",\n        pathname: \"/api/school\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_school_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./backend/lib/database.ts":
/*!*********************************!*\
  !*** ./backend/lib/database.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   executeQuery: () => (/* binding */ executeQuery),\n/* harmony export */   executeQuerySingle: () => (/* binding */ executeQuerySingle),\n/* harmony export */   getConnection: () => (/* binding */ getConnection),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase),\n/* harmony export */   seedDatabase: () => (/* binding */ seedDatabase)\n/* harmony export */ });\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mysql2/promise */ \"mysql2/promise\");\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mysql2_promise__WEBPACK_IMPORTED_MODULE_0__);\n\nconst config = {\n    host: process.env.DB_HOST || \"localhost\",\n    port: parseInt(process.env.DB_PORT || \"3306\"),\n    user: process.env.DB_USER || \"\",\n    password: process.env.DB_PASSWORD || \"\",\n    database: process.env.DB_NAME || \"\",\n    ssl: process.env.DB_SSL === \"true\"\n};\nlet connection = null;\nasync function getConnection() {\n    if (!connection) {\n        try {\n            connection = await mysql2_promise__WEBPACK_IMPORTED_MODULE_0___default().createConnection(config);\n            console.log(\"✅ Database connected successfully\");\n        } catch (error) {\n            console.error(\"❌ Database connection failed:\", error);\n            throw error;\n        }\n    }\n    return connection;\n}\nasync function executeQuery(query, params = []) {\n    try {\n        const conn = await getConnection();\n        const [rows] = await conn.execute(query, params);\n        return rows;\n    } catch (error) {\n        console.error(\"Database query error:\", error);\n        throw error;\n    }\n}\nasync function executeQuerySingle(query, params = []) {\n    const results = await executeQuery(query, params);\n    return results.length > 0 ? results[0] : null;\n}\n// Initialize database tables if they don't exist\nasync function initializeDatabase() {\n    try {\n        const conn = await getConnection();\n        // Create analytics table\n        await conn.execute(`\n      CREATE TABLE IF NOT EXISTS analytics (\n        id INT AUTO_INCREMENT PRIMARY KEY,\n        metric_name VARCHAR(100) NOT NULL,\n        value DECIMAL(10,2) NOT NULL,\n        unit VARCHAR(20),\n        icon VARCHAR(50),\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n      )\n    `);\n        // Create system_diagnostics table\n        await conn.execute(`\n      CREATE TABLE IF NOT EXISTS system_diagnostics (\n        id INT AUTO_INCREMENT PRIMARY KEY,\n        cpu_load DECIMAL(5,2) NOT NULL,\n        ram_usage DECIMAL(5,2) NOT NULL,\n        disk_usage DECIMAL(5,2) DEFAULT 0,\n        network_status VARCHAR(20) DEFAULT 'active',\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create school_data table\n        await conn.execute(`\n      CREATE TABLE IF NOT EXISTS school_data (\n        id INT AUTO_INCREMENT PRIMARY KEY,\n        gpa DECIMAL(3,2) NOT NULL,\n        credits INT NOT NULL,\n        attendance DECIMAL(5,2) NOT NULL,\n        assignments_completed INT DEFAULT 0,\n        assignments_total INT DEFAULT 0,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n      )\n    `);\n        // Create market_data table\n        await conn.execute(`\n      CREATE TABLE IF NOT EXISTS market_data (\n        id INT AUTO_INCREMENT PRIMARY KEY,\n        featured_product_name VARCHAR(200) NOT NULL,\n        featured_product_price DECIMAL(10,2) NOT NULL,\n        featured_product_image VARCHAR(500),\n        top_mover_symbol VARCHAR(10) NOT NULL,\n        top_mover_change DECIMAL(10,2) NOT NULL,\n        top_mover_is_up BOOLEAN NOT NULL,\n        volume BIGINT DEFAULT 0,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n      )\n    `);\n        // Create books table\n        await conn.execute(`\n      CREATE TABLE IF NOT EXISTS books (\n        id INT AUTO_INCREMENT PRIMARY KEY,\n        title VARCHAR(200) NOT NULL,\n        author VARCHAR(100) NOT NULL,\n        price DECIMAL(8,2) NOT NULL,\n        cover_image VARCHAR(500),\n        genre VARCHAR(50),\n        rating DECIMAL(2,1) DEFAULT 0,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create notifications table\n        await conn.execute(`\n      CREATE TABLE IF NOT EXISTS notifications (\n        id INT AUTO_INCREMENT PRIMARY KEY,\n        title VARCHAR(200) NOT NULL,\n        message TEXT NOT NULL,\n        type VARCHAR(50) DEFAULT 'info',\n        is_read BOOLEAN DEFAULT FALSE,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        console.log(\"✅ Database tables initialized successfully\");\n    } catch (error) {\n        console.error(\"❌ Database initialization failed:\", error);\n        throw error;\n    }\n}\n// Seed initial data\nasync function seedDatabase() {\n    try {\n        // Check if data already exists\n        const analyticsCount = await executeQuerySingle(\"SELECT COUNT(*) as count FROM analytics\");\n        if (analyticsCount && analyticsCount.count === 0) {\n            // Seed analytics data\n            await executeQuery(`\n        INSERT INTO analytics (metric_name, value, unit, icon) VALUES\n        ('Revenue', 125000.50, 'USD', 'CurrencyDollarIcon'),\n        ('Users', 15420, 'count', 'UsersIcon'),\n        ('Growth', 23.5, '%', 'TrendingUpIcon'),\n        ('Conversion', 4.2, '%', 'ChartBarIcon')\n      `);\n            // Seed system diagnostics\n            await executeQuery(`\n        INSERT INTO system_diagnostics (cpu_load, ram_usage, disk_usage) VALUES\n        (45.2, 67.8, 23.1)\n      `);\n            // Seed school data\n            await executeQuery(`\n        INSERT INTO school_data (gpa, credits, attendance, assignments_completed, assignments_total) VALUES\n        (3.85, 120, 94.5, 28, 32)\n      `);\n            // Seed market data\n            await executeQuery(`\n        INSERT INTO market_data (featured_product_name, featured_product_price, top_mover_symbol, top_mover_change, top_mover_is_up, volume) VALUES\n        ('Quantum Computing Module', 2499.99, 'QTECH', 15.75, TRUE, 1250000)\n      `);\n            // Seed books\n            await executeQuery(`\n        INSERT INTO books (title, author, price, genre, rating) VALUES\n        ('Neural Networks Fundamentals', 'Dr. Sarah Chen', 89.99, 'Technology', 4.8),\n        ('Quantum Physics Made Simple', 'Prof. Michael Torres', 75.50, 'Science', 4.6),\n        ('AI Ethics in Practice', 'Dr. Aisha Patel', 65.00, 'Technology', 4.9),\n        ('Future of Computing', 'James Rodriguez', 55.99, 'Technology', 4.4),\n        ('Data Science Handbook', 'Lisa Wang', 79.99, 'Technology', 4.7),\n        ('Cybersecurity Essentials', 'Mark Thompson', 69.99, 'Technology', 4.5)\n      `);\n            // Seed notifications\n            await executeQuery(`\n        INSERT INTO notifications (title, message, type) VALUES\n        ('System Update Available', 'A new system update is ready for installation. Please schedule maintenance window.', 'info'),\n        ('Security Alert', 'Unusual login activity detected from new location. Please verify your account.', 'warning'),\n        ('Backup Completed', 'Daily backup process completed successfully. All data is secure.', 'success'),\n        ('Performance Alert', 'CPU usage has been above 80% for the last 30 minutes. Consider scaling resources.', 'warning'),\n        ('New Feature Released', 'Advanced analytics dashboard is now available in your control panel.', 'info')\n      `);\n            console.log(\"✅ Database seeded with initial data\");\n        }\n    } catch (error) {\n        console.error(\"❌ Database seeding failed:\", error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./backend/lib/database.ts\n");

/***/ }),

/***/ "(api)/./pages/api/school.ts":
/*!*****************************!*\
  !*** ./pages/api/school.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _backend_lib_database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../backend/lib/database */ \"(api)/./backend/lib/database.ts\");\n\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        res.setHeader(\"Allow\", [\n            \"GET\"\n        ]);\n        return res.status(405).end(`Method ${req.method} Not Allowed`);\n    }\n    try {\n        // Initialize database if needed\n        await (0,_backend_lib_database__WEBPACK_IMPORTED_MODULE_0__.initializeDatabase)();\n        await (0,_backend_lib_database__WEBPACK_IMPORTED_MODULE_0__.seedDatabase)();\n        // Get school data from database\n        const schoolData = await (0,_backend_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuerySingle)(`\n      SELECT * FROM school_data\n      ORDER BY updated_at DESC\n      LIMIT 1\n    `);\n        if (schoolData) {\n            const assignmentsDue = schoolData.assignments_total - schoolData.assignments_completed;\n            const data = {\n                overview: {\n                    gpa: schoolData.gpa.toFixed(2),\n                    credits: schoolData.credits,\n                    assignmentsDue: assignmentsDue\n                },\n                courses: [\n                    {\n                        name: \"Quantum Computing Theory\",\n                        progress: 85,\n                        color: \"cyan\"\n                    },\n                    {\n                        name: \"Neural Network Architecture\",\n                        progress: 92,\n                        color: \"pink\"\n                    },\n                    {\n                        name: \"Cybersecurity Protocols\",\n                        progress: 78,\n                        color: \"yellow\"\n                    }\n                ],\n                assignments: [\n                    {\n                        name: \"Quantum Algorithm Implementation\",\n                        due: \"2 days\",\n                        course: \"Quantum Computing Theory\"\n                    },\n                    {\n                        name: \"Deep Learning Model Training\",\n                        due: \"5 days\",\n                        course: \"Neural Network Architecture\"\n                    },\n                    {\n                        name: \"Security Audit Report\",\n                        due: \"1 week\",\n                        course: \"Cybersecurity Protocols\"\n                    },\n                    {\n                        name: \"Research Paper: AI Ethics\",\n                        due: \"2 weeks\",\n                        course: \"Neural Network Architecture\"\n                    }\n                ]\n            };\n            res.status(200).json(data);\n        } else {\n            // Fallback data if no school data exists\n            const fallbackData = {\n                overview: {\n                    gpa: \"3.85\",\n                    credits: 120,\n                    assignmentsDue: 4\n                },\n                courses: [\n                    {\n                        name: \"Quantum Computing Theory\",\n                        progress: 85,\n                        color: \"cyan\"\n                    },\n                    {\n                        name: \"Neural Network Architecture\",\n                        progress: 92,\n                        color: \"pink\"\n                    },\n                    {\n                        name: \"Cybersecurity Protocols\",\n                        progress: 78,\n                        color: \"yellow\"\n                    }\n                ],\n                assignments: [\n                    {\n                        name: \"Quantum Algorithm Implementation\",\n                        due: \"2 days\",\n                        course: \"Quantum Computing Theory\"\n                    },\n                    {\n                        name: \"Deep Learning Model Training\",\n                        due: \"5 days\",\n                        course: \"Neural Network Architecture\"\n                    },\n                    {\n                        name: \"Security Audit Report\",\n                        due: \"1 week\",\n                        course: \"Cybersecurity Protocols\"\n                    },\n                    {\n                        name: \"Research Paper: AI Ethics\",\n                        due: \"2 weeks\",\n                        course: \"Neural Network Architecture\"\n                    }\n                ]\n            };\n            res.status(200).json(fallbackData);\n        }\n    } catch (error) {\n        console.error(\"School API error:\", error);\n        // Fallback data if database fails\n        const fallbackData = {\n            overview: {\n                gpa: \"3.85\",\n                credits: 120,\n                assignmentsDue: 4\n            },\n            courses: [\n                {\n                    name: \"Quantum Computing Theory\",\n                    progress: 85,\n                    color: \"cyan\"\n                },\n                {\n                    name: \"Neural Network Architecture\",\n                    progress: 92,\n                    color: \"pink\"\n                },\n                {\n                    name: \"Cybersecurity Protocols\",\n                    progress: 78,\n                    color: \"yellow\"\n                }\n            ],\n            assignments: [\n                {\n                    name: \"Quantum Algorithm Implementation\",\n                    due: \"2 days\",\n                    course: \"Quantum Computing Theory\"\n                },\n                {\n                    name: \"Deep Learning Model Training\",\n                    due: \"5 days\",\n                    course: \"Neural Network Architecture\"\n                },\n                {\n                    name: \"Security Audit Report\",\n                    due: \"1 week\",\n                    course: \"Cybersecurity Protocols\"\n                },\n                {\n                    name: \"Research Paper: AI Ethics\",\n                    due: \"2 weeks\",\n                    course: \"Neural Network Architecture\"\n                }\n            ]\n        };\n        res.status(200).json(fallbackData);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9wYWdlcy9hcGkvc2Nob29sLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBRWtHO0FBb0NuRixlQUFlRyxRQUM1QkMsR0FBbUIsRUFDbkJDLEdBQW9EO0lBRXBELElBQUlELElBQUlFLE1BQU0sS0FBSyxPQUFPO1FBQ3hCRCxJQUFJRSxTQUFTLENBQUMsU0FBUztZQUFDO1NBQU07UUFDOUIsT0FBT0YsSUFBSUcsTUFBTSxDQUFDLEtBQUtDLEdBQUcsQ0FBQyxDQUFDLE9BQU8sRUFBRUwsSUFBSUUsTUFBTSxDQUFDLFlBQVksQ0FBQztJQUMvRDtJQUVBLElBQUk7UUFDRixnQ0FBZ0M7UUFDaEMsTUFBTUwseUVBQWtCQTtRQUN4QixNQUFNQyxtRUFBWUE7UUFFbEIsZ0NBQWdDO1FBQ2hDLE1BQU1RLGFBQWEsTUFBTVYseUVBQWtCQSxDQUFxQixDQUFDOzs7O0lBSWpFLENBQUM7UUFFRCxJQUFJVSxZQUFZO1lBQ2QsTUFBTUMsaUJBQWlCRCxXQUFXRSxpQkFBaUIsR0FBR0YsV0FBV0cscUJBQXFCO1lBRXRGLE1BQU1DLE9BQW1CO2dCQUN2QkMsVUFBVTtvQkFDUkMsS0FBS04sV0FBV00sR0FBRyxDQUFDQyxPQUFPLENBQUM7b0JBQzVCQyxTQUFTUixXQUFXUSxPQUFPO29CQUMzQlAsZ0JBQWdCQTtnQkFDbEI7Z0JBQ0FRLFNBQVM7b0JBQ1A7d0JBQUVDLE1BQU07d0JBQTRCQyxVQUFVO3dCQUFJQyxPQUFPO29CQUFPO29CQUNoRTt3QkFBRUYsTUFBTTt3QkFBK0JDLFVBQVU7d0JBQUlDLE9BQU87b0JBQU87b0JBQ25FO3dCQUFFRixNQUFNO3dCQUEyQkMsVUFBVTt3QkFBSUMsT0FBTztvQkFBUztpQkFDbEU7Z0JBQ0RDLGFBQWE7b0JBQ1g7d0JBQUVILE1BQU07d0JBQW9DSSxLQUFLO3dCQUFVQyxRQUFRO29CQUEyQjtvQkFDOUY7d0JBQUVMLE1BQU07d0JBQWdDSSxLQUFLO3dCQUFVQyxRQUFRO29CQUE4QjtvQkFDN0Y7d0JBQUVMLE1BQU07d0JBQXlCSSxLQUFLO3dCQUFVQyxRQUFRO29CQUEwQjtvQkFDbEY7d0JBQUVMLE1BQU07d0JBQTZCSSxLQUFLO3dCQUFXQyxRQUFRO29CQUE4QjtpQkFDNUY7WUFDSDtZQUVBcEIsSUFBSUcsTUFBTSxDQUFDLEtBQUtrQixJQUFJLENBQUNaO1FBQ3ZCLE9BQU87WUFDTCx5Q0FBeUM7WUFDekMsTUFBTWEsZUFBMkI7Z0JBQy9CWixVQUFVO29CQUNSQyxLQUFLO29CQUNMRSxTQUFTO29CQUNUUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBUSxTQUFTO29CQUNQO3dCQUFFQyxNQUFNO3dCQUE0QkMsVUFBVTt3QkFBSUMsT0FBTztvQkFBTztvQkFDaEU7d0JBQUVGLE1BQU07d0JBQStCQyxVQUFVO3dCQUFJQyxPQUFPO29CQUFPO29CQUNuRTt3QkFBRUYsTUFBTTt3QkFBMkJDLFVBQVU7d0JBQUlDLE9BQU87b0JBQVM7aUJBQ2xFO2dCQUNEQyxhQUFhO29CQUNYO3dCQUFFSCxNQUFNO3dCQUFvQ0ksS0FBSzt3QkFBVUMsUUFBUTtvQkFBMkI7b0JBQzlGO3dCQUFFTCxNQUFNO3dCQUFnQ0ksS0FBSzt3QkFBVUMsUUFBUTtvQkFBOEI7b0JBQzdGO3dCQUFFTCxNQUFNO3dCQUF5QkksS0FBSzt3QkFBVUMsUUFBUTtvQkFBMEI7b0JBQ2xGO3dCQUFFTCxNQUFNO3dCQUE2QkksS0FBSzt3QkFBV0MsUUFBUTtvQkFBOEI7aUJBQzVGO1lBQ0g7WUFDQXBCLElBQUlHLE1BQU0sQ0FBQyxLQUFLa0IsSUFBSSxDQUFDQztRQUN2QjtJQUNGLEVBQUUsT0FBT0MsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMscUJBQXFCQTtRQUVuQyxrQ0FBa0M7UUFDbEMsTUFBTUQsZUFBMkI7WUFDL0JaLFVBQVU7Z0JBQ1JDLEtBQUs7Z0JBQ0xFLFNBQVM7Z0JBQ1RQLGdCQUFnQjtZQUNsQjtZQUNBUSxTQUFTO2dCQUNQO29CQUFFQyxNQUFNO29CQUE0QkMsVUFBVTtvQkFBSUMsT0FBTztnQkFBTztnQkFDaEU7b0JBQUVGLE1BQU07b0JBQStCQyxVQUFVO29CQUFJQyxPQUFPO2dCQUFPO2dCQUNuRTtvQkFBRUYsTUFBTTtvQkFBMkJDLFVBQVU7b0JBQUlDLE9BQU87Z0JBQVM7YUFDbEU7WUFDREMsYUFBYTtnQkFDWDtvQkFBRUgsTUFBTTtvQkFBb0NJLEtBQUs7b0JBQVVDLFFBQVE7Z0JBQTJCO2dCQUM5RjtvQkFBRUwsTUFBTTtvQkFBZ0NJLEtBQUs7b0JBQVVDLFFBQVE7Z0JBQThCO2dCQUM3RjtvQkFBRUwsTUFBTTtvQkFBeUJJLEtBQUs7b0JBQVVDLFFBQVE7Z0JBQTBCO2dCQUNsRjtvQkFBRUwsTUFBTTtvQkFBNkJJLEtBQUs7b0JBQVdDLFFBQVE7Z0JBQThCO2FBQzVGO1FBQ0g7UUFDQXBCLElBQUlHLE1BQU0sQ0FBQyxLQUFLa0IsSUFBSSxDQUFDQztJQUN2QjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXllcy1zaGllbGQtZnV0dXJpc3RpYy11aS8uL3BhZ2VzL2FwaS9zY2hvb2wudHM/NDUzMyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmltcG9ydCB0eXBlIHsgTmV4dEFwaVJlcXVlc3QsIE5leHRBcGlSZXNwb25zZSB9IGZyb20gJ25leHQnO1xuaW1wb3J0IHsgZXhlY3V0ZVF1ZXJ5U2luZ2xlLCBpbml0aWFsaXplRGF0YWJhc2UsIHNlZWREYXRhYmFzZSB9IGZyb20gJy4uLy4uL2JhY2tlbmQvbGliL2RhdGFiYXNlJztcblxudHlwZSBDb3Vyc2UgPSB7XG4gIG5hbWU6IHN0cmluZztcbiAgcHJvZ3Jlc3M6IG51bWJlcjtcbiAgY29sb3I6ICdjeWFuJyB8ICdwaW5rJyB8ICd5ZWxsb3cnO1xufTtcbnR5cGUgQXNzaWdubWVudCA9IHtcbiAgbmFtZTogc3RyaW5nO1xuICBkdWU6IHN0cmluZztcbiAgY291cnNlOiBzdHJpbmc7XG59O1xudHlwZSBBY2FkZW1pY092ZXJ2aWV3ID0ge1xuICAgIGdwYTogc3RyaW5nO1xuICAgIGNyZWRpdHM6IG51bWJlcjtcbiAgICBhc3NpZ25tZW50c0R1ZTogbnVtYmVyO1xufTtcbnR5cGUgU2Nob29sRGF0YSA9IHtcbiAgb3ZlcnZpZXc6IEFjYWRlbWljT3ZlcnZpZXc7XG4gIGNvdXJzZXM6IENvdXJzZVtdO1xuICBhc3NpZ25tZW50czogQXNzaWdubWVudFtdO1xufTtcblxudHlwZSBEYXRhYmFzZVNjaG9vbERhdGEgPSB7XG4gIGlkOiBudW1iZXI7XG4gIGdwYTogbnVtYmVyO1xuICBjcmVkaXRzOiBudW1iZXI7XG4gIGF0dGVuZGFuY2U6IG51bWJlcjtcbiAgYXNzaWdubWVudHNfY29tcGxldGVkOiBudW1iZXI7XG4gIGFzc2lnbm1lbnRzX3RvdGFsOiBudW1iZXI7XG4gIGNyZWF0ZWRfYXQ6IHN0cmluZztcbiAgdXBkYXRlZF9hdDogc3RyaW5nO1xufTtcblxuXG5cbmV4cG9ydCBkZWZhdWx0IGFzeW5jIGZ1bmN0aW9uIGhhbmRsZXIoXG4gIHJlcTogTmV4dEFwaVJlcXVlc3QsXG4gIHJlczogTmV4dEFwaVJlc3BvbnNlPFNjaG9vbERhdGEgfCB7IGVycm9yOiBzdHJpbmcgfT5cbikge1xuICBpZiAocmVxLm1ldGhvZCAhPT0gJ0dFVCcpIHtcbiAgICByZXMuc2V0SGVhZGVyKCdBbGxvdycsIFsnR0VUJ10pO1xuICAgIHJldHVybiByZXMuc3RhdHVzKDQwNSkuZW5kKGBNZXRob2QgJHtyZXEubWV0aG9kfSBOb3QgQWxsb3dlZGApO1xuICB9XG5cbiAgdHJ5IHtcbiAgICAvLyBJbml0aWFsaXplIGRhdGFiYXNlIGlmIG5lZWRlZFxuICAgIGF3YWl0IGluaXRpYWxpemVEYXRhYmFzZSgpO1xuICAgIGF3YWl0IHNlZWREYXRhYmFzZSgpO1xuXG4gICAgLy8gR2V0IHNjaG9vbCBkYXRhIGZyb20gZGF0YWJhc2VcbiAgICBjb25zdCBzY2hvb2xEYXRhID0gYXdhaXQgZXhlY3V0ZVF1ZXJ5U2luZ2xlPERhdGFiYXNlU2Nob29sRGF0YT4oYFxuICAgICAgU0VMRUNUICogRlJPTSBzY2hvb2xfZGF0YVxuICAgICAgT1JERVIgQlkgdXBkYXRlZF9hdCBERVNDXG4gICAgICBMSU1JVCAxXG4gICAgYCk7XG5cbiAgICBpZiAoc2Nob29sRGF0YSkge1xuICAgICAgY29uc3QgYXNzaWdubWVudHNEdWUgPSBzY2hvb2xEYXRhLmFzc2lnbm1lbnRzX3RvdGFsIC0gc2Nob29sRGF0YS5hc3NpZ25tZW50c19jb21wbGV0ZWQ7XG5cbiAgICAgIGNvbnN0IGRhdGE6IFNjaG9vbERhdGEgPSB7XG4gICAgICAgIG92ZXJ2aWV3OiB7XG4gICAgICAgICAgZ3BhOiBzY2hvb2xEYXRhLmdwYS50b0ZpeGVkKDIpLFxuICAgICAgICAgIGNyZWRpdHM6IHNjaG9vbERhdGEuY3JlZGl0cyxcbiAgICAgICAgICBhc3NpZ25tZW50c0R1ZTogYXNzaWdubWVudHNEdWVcbiAgICAgICAgfSxcbiAgICAgICAgY291cnNlczogW1xuICAgICAgICAgIHsgbmFtZTogXCJRdWFudHVtIENvbXB1dGluZyBUaGVvcnlcIiwgcHJvZ3Jlc3M6IDg1LCBjb2xvcjogJ2N5YW4nIH0sXG4gICAgICAgICAgeyBuYW1lOiBcIk5ldXJhbCBOZXR3b3JrIEFyY2hpdGVjdHVyZVwiLCBwcm9ncmVzczogOTIsIGNvbG9yOiAncGluaycgfSxcbiAgICAgICAgICB7IG5hbWU6IFwiQ3liZXJzZWN1cml0eSBQcm90b2NvbHNcIiwgcHJvZ3Jlc3M6IDc4LCBjb2xvcjogJ3llbGxvdycgfVxuICAgICAgICBdLFxuICAgICAgICBhc3NpZ25tZW50czogW1xuICAgICAgICAgIHsgbmFtZTogXCJRdWFudHVtIEFsZ29yaXRobSBJbXBsZW1lbnRhdGlvblwiLCBkdWU6IFwiMiBkYXlzXCIsIGNvdXJzZTogXCJRdWFudHVtIENvbXB1dGluZyBUaGVvcnlcIiB9LFxuICAgICAgICAgIHsgbmFtZTogXCJEZWVwIExlYXJuaW5nIE1vZGVsIFRyYWluaW5nXCIsIGR1ZTogXCI1IGRheXNcIiwgY291cnNlOiBcIk5ldXJhbCBOZXR3b3JrIEFyY2hpdGVjdHVyZVwiIH0sXG4gICAgICAgICAgeyBuYW1lOiBcIlNlY3VyaXR5IEF1ZGl0IFJlcG9ydFwiLCBkdWU6IFwiMSB3ZWVrXCIsIGNvdXJzZTogXCJDeWJlcnNlY3VyaXR5IFByb3RvY29sc1wiIH0sXG4gICAgICAgICAgeyBuYW1lOiBcIlJlc2VhcmNoIFBhcGVyOiBBSSBFdGhpY3NcIiwgZHVlOiBcIjIgd2Vla3NcIiwgY291cnNlOiBcIk5ldXJhbCBOZXR3b3JrIEFyY2hpdGVjdHVyZVwiIH1cbiAgICAgICAgXVxuICAgICAgfTtcblxuICAgICAgcmVzLnN0YXR1cygyMDApLmpzb24oZGF0YSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIEZhbGxiYWNrIGRhdGEgaWYgbm8gc2Nob29sIGRhdGEgZXhpc3RzXG4gICAgICBjb25zdCBmYWxsYmFja0RhdGE6IFNjaG9vbERhdGEgPSB7XG4gICAgICAgIG92ZXJ2aWV3OiB7XG4gICAgICAgICAgZ3BhOiBcIjMuODVcIixcbiAgICAgICAgICBjcmVkaXRzOiAxMjAsXG4gICAgICAgICAgYXNzaWdubWVudHNEdWU6IDRcbiAgICAgICAgfSxcbiAgICAgICAgY291cnNlczogW1xuICAgICAgICAgIHsgbmFtZTogXCJRdWFudHVtIENvbXB1dGluZyBUaGVvcnlcIiwgcHJvZ3Jlc3M6IDg1LCBjb2xvcjogJ2N5YW4nIH0sXG4gICAgICAgICAgeyBuYW1lOiBcIk5ldXJhbCBOZXR3b3JrIEFyY2hpdGVjdHVyZVwiLCBwcm9ncmVzczogOTIsIGNvbG9yOiAncGluaycgfSxcbiAgICAgICAgICB7IG5hbWU6IFwiQ3liZXJzZWN1cml0eSBQcm90b2NvbHNcIiwgcHJvZ3Jlc3M6IDc4LCBjb2xvcjogJ3llbGxvdycgfVxuICAgICAgICBdLFxuICAgICAgICBhc3NpZ25tZW50czogW1xuICAgICAgICAgIHsgbmFtZTogXCJRdWFudHVtIEFsZ29yaXRobSBJbXBsZW1lbnRhdGlvblwiLCBkdWU6IFwiMiBkYXlzXCIsIGNvdXJzZTogXCJRdWFudHVtIENvbXB1dGluZyBUaGVvcnlcIiB9LFxuICAgICAgICAgIHsgbmFtZTogXCJEZWVwIExlYXJuaW5nIE1vZGVsIFRyYWluaW5nXCIsIGR1ZTogXCI1IGRheXNcIiwgY291cnNlOiBcIk5ldXJhbCBOZXR3b3JrIEFyY2hpdGVjdHVyZVwiIH0sXG4gICAgICAgICAgeyBuYW1lOiBcIlNlY3VyaXR5IEF1ZGl0IFJlcG9ydFwiLCBkdWU6IFwiMSB3ZWVrXCIsIGNvdXJzZTogXCJDeWJlcnNlY3VyaXR5IFByb3RvY29sc1wiIH0sXG4gICAgICAgICAgeyBuYW1lOiBcIlJlc2VhcmNoIFBhcGVyOiBBSSBFdGhpY3NcIiwgZHVlOiBcIjIgd2Vla3NcIiwgY291cnNlOiBcIk5ldXJhbCBOZXR3b3JrIEFyY2hpdGVjdHVyZVwiIH1cbiAgICAgICAgXVxuICAgICAgfTtcbiAgICAgIHJlcy5zdGF0dXMoMjAwKS5qc29uKGZhbGxiYWNrRGF0YSk7XG4gICAgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ1NjaG9vbCBBUEkgZXJyb3I6JywgZXJyb3IpO1xuXG4gICAgLy8gRmFsbGJhY2sgZGF0YSBpZiBkYXRhYmFzZSBmYWlsc1xuICAgIGNvbnN0IGZhbGxiYWNrRGF0YTogU2Nob29sRGF0YSA9IHtcbiAgICAgIG92ZXJ2aWV3OiB7XG4gICAgICAgIGdwYTogXCIzLjg1XCIsXG4gICAgICAgIGNyZWRpdHM6IDEyMCxcbiAgICAgICAgYXNzaWdubWVudHNEdWU6IDRcbiAgICAgIH0sXG4gICAgICBjb3Vyc2VzOiBbXG4gICAgICAgIHsgbmFtZTogXCJRdWFudHVtIENvbXB1dGluZyBUaGVvcnlcIiwgcHJvZ3Jlc3M6IDg1LCBjb2xvcjogJ2N5YW4nIH0sXG4gICAgICAgIHsgbmFtZTogXCJOZXVyYWwgTmV0d29yayBBcmNoaXRlY3R1cmVcIiwgcHJvZ3Jlc3M6IDkyLCBjb2xvcjogJ3BpbmsnIH0sXG4gICAgICAgIHsgbmFtZTogXCJDeWJlcnNlY3VyaXR5IFByb3RvY29sc1wiLCBwcm9ncmVzczogNzgsIGNvbG9yOiAneWVsbG93JyB9XG4gICAgICBdLFxuICAgICAgYXNzaWdubWVudHM6IFtcbiAgICAgICAgeyBuYW1lOiBcIlF1YW50dW0gQWxnb3JpdGhtIEltcGxlbWVudGF0aW9uXCIsIGR1ZTogXCIyIGRheXNcIiwgY291cnNlOiBcIlF1YW50dW0gQ29tcHV0aW5nIFRoZW9yeVwiIH0sXG4gICAgICAgIHsgbmFtZTogXCJEZWVwIExlYXJuaW5nIE1vZGVsIFRyYWluaW5nXCIsIGR1ZTogXCI1IGRheXNcIiwgY291cnNlOiBcIk5ldXJhbCBOZXR3b3JrIEFyY2hpdGVjdHVyZVwiIH0sXG4gICAgICAgIHsgbmFtZTogXCJTZWN1cml0eSBBdWRpdCBSZXBvcnRcIiwgZHVlOiBcIjEgd2Vla1wiLCBjb3Vyc2U6IFwiQ3liZXJzZWN1cml0eSBQcm90b2NvbHNcIiB9LFxuICAgICAgICB7IG5hbWU6IFwiUmVzZWFyY2ggUGFwZXI6IEFJIEV0aGljc1wiLCBkdWU6IFwiMiB3ZWVrc1wiLCBjb3Vyc2U6IFwiTmV1cmFsIE5ldHdvcmsgQXJjaGl0ZWN0dXJlXCIgfVxuICAgICAgXVxuICAgIH07XG4gICAgcmVzLnN0YXR1cygyMDApLmpzb24oZmFsbGJhY2tEYXRhKTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbImV4ZWN1dGVRdWVyeVNpbmdsZSIsImluaXRpYWxpemVEYXRhYmFzZSIsInNlZWREYXRhYmFzZSIsImhhbmRsZXIiLCJyZXEiLCJyZXMiLCJtZXRob2QiLCJzZXRIZWFkZXIiLCJzdGF0dXMiLCJlbmQiLCJzY2hvb2xEYXRhIiwiYXNzaWdubWVudHNEdWUiLCJhc3NpZ25tZW50c190b3RhbCIsImFzc2lnbm1lbnRzX2NvbXBsZXRlZCIsImRhdGEiLCJvdmVydmlldyIsImdwYSIsInRvRml4ZWQiLCJjcmVkaXRzIiwiY291cnNlcyIsIm5hbWUiLCJwcm9ncmVzcyIsImNvbG9yIiwiYXNzaWdubWVudHMiLCJkdWUiLCJjb3Vyc2UiLCJqc29uIiwiZmFsbGJhY2tEYXRhIiwiZXJyb3IiLCJjb25zb2xlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./pages/api/school.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0"], () => (__webpack_exec__("(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();
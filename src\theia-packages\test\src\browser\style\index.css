/******************************************************************************
* Copyright (C) 2023 STMicroelectronics and others.
* This program and the accompanying materials are made available under the
* terms of the Eclipse Public License v. 2.0 which is available at
* http://www.eclipse.org/legal/epl-2.0.
*
* This Source Code may also be made available under the following Secondary
* Licenses when the conditions for such availability set forth in the Eclipse
* Public License v. 2.0 are satisfied: GNU General Public License, version 2
* with the GNU Classpath Exception which is available at
* https://www.gnu.org/software/classpath/license.html.
*
* SPDX-License-Identifier: EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0
********************************************************************************/

.theia-test-view {
    height: 100%
}

.theia-test-view .passed,
.theia-test-run-view .passed {
    color: var(--theia-successBackground);
}

.theia-test-view .failed,
.theia-test-run-view .failed {
    color: var(--theia-editorError-foreground);
}

.theia-test-view .errored,
.theia-test-run-view .errored {
    color: var(--theia-editorError-foreground);
}

.theia-test-view .queued,
.theia-test-run-view .queued {
    color: var(--theia-editorWarning-foreground);
}

.theia-test-result-view .debug-frame {
    white-space: pre;
}

.theia-test-view .theia-TreeNode:not(:hover):not(.theia-mod-selected) .theia-test-tree-inline-action {
    display: none;
}
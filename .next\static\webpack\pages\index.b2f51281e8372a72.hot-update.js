"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/views/School/departments/SchoolDashboard.tsx":
/*!**********************************************************!*\
  !*** ./src/views/School/departments/SchoolDashboard.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SchoolDashboard: function() { return /* binding */ SchoolDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../components/Card */ \"./src/components/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BuildingLibraryIcon,CalendarIcon,ChartBarIcon,UserGroupIcon!=!@heroicons/react/24/solid */ \"__barrel_optimize__?names=AcademicCapIcon,BuildingLibraryIcon,CalendarIcon,ChartBarIcon,UserGroupIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/solid/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst SchoolDashboard = (param)=>{\n    let { activeSubSection } = param;\n    _s();\n    const [schoolData, setSchoolData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [overviewData, setOverviewData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            try {\n                setLoading(true);\n                // Fetch dashboard data\n                const dashboardResponse = await fetch(\"/api/school/dashboard\");\n                if (dashboardResponse.ok) {\n                    const dashboardData = await dashboardResponse.json();\n                    setSchoolData(dashboardData);\n                }\n                // Fetch overview data\n                const overviewResponse = await fetch(\"/api/school/overview\");\n                if (overviewResponse.ok) {\n                    const overviewDataResult = await overviewResponse.json();\n                    setOverviewData(overviewDataResult);\n                }\n            } catch (error) {\n                console.error(\"Failed to fetch data:\", error);\n                // Fallback to mock data if API fails\n                const mockData = {\n                    overview: {\n                        totalStudents: 1247,\n                        totalTeachers: 89,\n                        totalClasses: 156,\n                        averageGPA: 3.67,\n                        aiTutorSessions: 2847,\n                        virtualRealityClasses: 23,\n                        blockchainCertificates: 156,\n                        carbonFootprint: 2.3\n                    },\n                    departments: [\n                        {\n                            name: \"AI & Robotics\",\n                            students: 312,\n                            teachers: 18,\n                            color: \"bg-blue-500/20 text-blue-400\",\n                            innovation: \"Neural Networks Lab\"\n                        },\n                        {\n                            name: \"Quantum Computing\",\n                            students: 298,\n                            teachers: 15,\n                            color: \"bg-green-500/20 text-green-400\",\n                            innovation: \"Quantum Simulator\"\n                        }\n                    ],\n                    innovations: [\n                        {\n                            title: \"AI-Powered Personalized Learning\",\n                            status: \"Active\",\n                            impact: \"94% improvement\",\n                            icon: \"\\uD83E\\uDD16\"\n                        },\n                        {\n                            title: \"Holographic Classrooms\",\n                            status: \"Beta\",\n                            impact: \"87% engagement\",\n                            icon: \"\\uD83D\\uDD2E\"\n                        }\n                    ],\n                    recentEvents: [\n                        {\n                            title: \"Global Virtual Science Fair\",\n                            date: \"March 15\",\n                            type: \"Innovation\",\n                            participants: 47\n                        },\n                        {\n                            title: \"AI Ethics Symposium\",\n                            date: \"March 20\",\n                            type: \"Academic\",\n                            participants: 234\n                        }\n                    ]\n                };\n                setSchoolData(mockData);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchData();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-cyan-400\",\n                children: \"Loading School Dashboard...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, undefined);\n    }\n    const renderContent = ()=>{\n        switch(activeSubSection){\n            case \"overview\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: overviewData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                title: \"\\uD83C\\uDFEB School Information\",\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-white mb-2\",\n                                                        children: overviewData.schoolInfo.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-cyan-400\",\n                                                                        children: \"Established:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                        lineNumber: 99,\n                                                                        columnNumber: 56\n                                                                    }, undefined),\n                                                                    \" \",\n                                                                    overviewData.schoolInfo.established\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 99,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-cyan-400\",\n                                                                        children: \"Motto:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                        lineNumber: 100,\n                                                                        columnNumber: 56\n                                                                    }, undefined),\n                                                                    \" \",\n                                                                    overviewData.schoolInfo.motto\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 100,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-cyan-400\",\n                                                                        children: \"Accreditation:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                        lineNumber: 101,\n                                                                        columnNumber: 56\n                                                                    }, undefined),\n                                                                    \" \",\n                                                                    overviewData.schoolInfo.accreditation\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 101,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-cyan-400\",\n                                                                        children: \"Ranking:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                        lineNumber: 102,\n                                                                        columnNumber: 56\n                                                                    }, undefined),\n                                                                    \" \",\n                                                                    overviewData.schoolInfo.ranking\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 102,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-lg font-semibold text-white mb-2\",\n                                                        children: \"Campus Details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-purple-400\",\n                                                                        children: \"Type:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                        lineNumber: 108,\n                                                                        columnNumber: 56\n                                                                    }, undefined),\n                                                                    \" \",\n                                                                    overviewData.schoolInfo.campusType\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 108,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-purple-400\",\n                                                                        children: \"Access:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                        lineNumber: 109,\n                                                                        columnNumber: 56\n                                                                    }, undefined),\n                                                                    \" \",\n                                                                    overviewData.schoolInfo.timeZone\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 109,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 107,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                title: \"\\uD83D\\uDCCA Key Statistics\",\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-blue-400\",\n                                                        children: overviewData.statistics.totalStudents.toLocaleString()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: \"Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-green-400\",\n                                                        children: overviewData.statistics.totalTeachers\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: \"Teachers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 126,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-purple-400\",\n                                                        children: overviewData.statistics.virtualClassrooms\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: \"Virtual Classrooms\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-cyan-400\",\n                                                        children: overviewData.statistics.aiTutors\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: \"AI Tutors\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                title: \"\\uD83C\\uDFC6 Recent Achievements\",\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: overviewData.achievements.slice(0, 6).map((achievement, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg bg-gray-800/40 border border-gray-600/30\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl\",\n                                                            children: achievement.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 29\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-sm font-medium text-white\",\n                                                                    children: achievement.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                    lineNumber: 149,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-400 mt-1\",\n                                                                    children: achievement.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                    lineNumber: 150,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 rounded-full text-xs bg-blue-500/20 text-blue-400\",\n                                                                            children: achievement.category\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                            lineNumber: 152,\n                                                                            columnNumber: 33\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: achievement.year\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                            lineNumber: 155,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                    lineNumber: 151,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 27\n                                                }, undefined)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 25\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-400\",\n                        children: \"Loading overview data...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 11\n                }, undefined);\n            case \"dashboard\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"\\uD83D\\uDE80 Future School Overview\",\n                            className: \"lg:col-span-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-4 p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.UserGroupIcon, {\n                                                    className: \"w-6 h-6 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: schoolData.overview.totalStudents\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Global Students\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.AcademicCapIcon, {\n                                                    className: \"w-6 h-6 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: schoolData.overview.totalTeachers\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"AI-Enhanced Teachers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.BuildingLibraryIcon, {\n                                                    className: \"w-6 h-6 text-purple-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: schoolData.overview.virtualRealityClasses\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"VR Classrooms\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.ChartBarIcon, {\n                                                    className: \"w-6 h-6 text-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: schoolData.overview.aiTutorSessions\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"AI Tutor Sessions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"\\uD83D\\uDD2C Innovation Hub\",\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 p-4\",\n                                children: schoolData.innovations.map((innovation, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg bg-gray-800/40 border border-gray-600/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg\",\n                                                                children: innovation.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 215,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-white\",\n                                                                children: innovation.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 216,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs \".concat(innovation.status === \"Active\" ? \"bg-green-500/20 text-green-400\" : innovation.status === \"Live\" ? \"bg-blue-500/20 text-blue-400\" : innovation.status === \"Beta\" ? \"bg-yellow-500/20 text-yellow-400\" : \"bg-purple-500/20 text-purple-400\"),\n                                                        children: innovation.status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: [\n                                                    \"Impact: \",\n                                                    innovation.impact\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"\\uD83C\\uDF1F Next-Gen Departments\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 p-4\",\n                                children: schoolData.departments.map((dept, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg \".concat(dept.color.split(\" \")[0], \" border border-gray-600/30\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold \".concat(dept.color.split(\" \").slice(1).join(\" \")),\n                                                            children: dept.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: [\n                                                                dept.students,\n                                                                \" students • \",\n                                                                dept.teachers,\n                                                                \" teachers\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-cyan-400 mt-1\",\n                                                            children: [\n                                                                \"\\uD83D\\uDE80 \",\n                                                                dept.innovation\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 rounded-lg \".concat(dept.color.split(\" \")[0], \" flex items-center justify-center\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.BuildingLibraryIcon, {\n                                                        className: \"w-4 h-4 \".concat(dept.color.split(\" \").slice(1).join(\" \"))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"\\uD83C\\uDF0D Global Events\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 p-4\",\n                                children: schoolData.recentEvents.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-3 p-3 rounded-lg hover:bg-gray-800/40 border border-gray-700/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.CalendarIcon, {\n                                                    className: \"w-5 h-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-white\",\n                                                        children: event.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: [\n                                                            event.date,\n                                                            \" • \",\n                                                            event.type\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mt-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-cyan-400\",\n                                                                children: [\n                                                                    \"\\uD83D\\uDC65 \",\n                                                                    event.participants,\n                                                                    \" participants\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"w-1 h-1 bg-gray-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-green-400\",\n                                                                children: \"\\uD83C\\uDF10 Global\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 11\n                }, undefined);\n            case \"overview\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"School Overview\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Comprehensive School Overview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Detailed school statistics and performance metrics will be displayed here.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 11\n                }, undefined);\n            case \"departments\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Department Management\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Department Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Manage all school departments, their staff, and resources.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 11\n                }, undefined);\n            case \"facilities\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"School Facilities\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Facilities Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Monitor and manage school facilities, maintenance, and resources.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 11\n                }, undefined);\n            case \"events\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"School Events\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Event Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Plan, organize, and track school events and activities.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 11\n                }, undefined);\n            case \"announcements\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Announcements\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"School Announcements\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Create and manage school-wide announcements and communications.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 319,\n                    columnNumber: 11\n                }, undefined);\n            case \"performance\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Performance Analytics\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"School Performance\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Analyze school performance metrics and academic achievements.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 329,\n                    columnNumber: 11\n                }, undefined);\n            case \"settings\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"School Settings\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"School Configuration\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Configure school settings, policies, and system preferences.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"School Dashboard\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Welcome to School Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Select a section from the navigation to get started.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full p-2\",\n        children: renderContent()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n        lineNumber: 360,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SchoolDashboard, \"THCBmSJmDMs6vK9PGUmEFl7QChc=\");\n_c = SchoolDashboard;\nvar _c;\n$RefreshReg$(_c, \"SchoolDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/School/departments/SchoolDashboard.tsx\n"));

/***/ })

});
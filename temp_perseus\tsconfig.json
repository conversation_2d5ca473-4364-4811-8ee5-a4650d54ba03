// This file is used by the `typecheck` command in package.json
/* Visit https://aka.ms/tsconfig to read more about this file */
{
    "exclude": [
        "**/dist",
        "coverage",
        "coverage-ts",
        "cypress.config.ts",
        "config/cypress/**",
        "**/*.cypress.ts",
        "**/*.cypress.tsx"
    ],
    "extends": "./tsconfig-common.json",
    "compilerOptions": {
        "noEmit": true,
        "paths": {
            "jsdiff": ["./vendor/jsdiff/jsdiff.js"],
            "raphael": ["./vendor/raphael/raphael.js"],
            "@khanacademy/kas": ["./packages/kas/src"],
            "@khanacademy/kmath": ["./packages/kmath/src"],
            "@khanacademy/keypad-context": ["./packages/keypad-context/src"],
            "@khanacademy/math-input": ["./packages/math-input/src"],
            "@khanacademy/math-input/strings": ["./packages/math-input/src/strings"],
            "@khanacademy/perseus": ["./packages/perseus/src"],
            "@khanacademy/perseus/strings": ["./packages/perseus/src/strings"],
            "@khanacademy/perseus-*": ["./packages/perseus-*/src"],
            "@khanacademy/pure-markdown": ["./packages/pure-markdown/src"],
            "@khanacademy/simple-markdown": ["./packages/simple-markdown/src"]
        },
        "plugins": [
            {
                "name": "typescript-plugin-css-modules",
                "options": {
                    "classnameTransform": "camelCase",
                    "goToDefinition": true
                },
            }
        ],

        /* Preference */
        // If this is disabled, TS will sometimes just delete dead code, which
        // can be confusing!
        "allowUnreachableCode": true
    }
}

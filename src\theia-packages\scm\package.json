{"name": "@theia/scm", "version": "1.63.0", "description": "Theia - Source control Extension", "dependencies": {"@theia/core": "1.63.0", "@theia/editor": "1.63.0", "@theia/filesystem": "1.63.0", "@theia/monaco": "1.63.0", "@theia/monaco-editor-core": "1.96.302", "@types/diff": "^5.2.1", "diff": "^5.2.0", "p-debounce": "^2.1.0", "react-textarea-autosize": "^8.5.5", "ts-md5": "^1.2.2", "tslib": "^2.6.2"}, "publishConfig": {"access": "public"}, "theiaExtensions": [{"frontend": "lib/browser/scm-frontend-module"}], "keywords": ["theia-extension"], "license": "EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0", "repository": {"type": "git", "url": "https://github.com/eclipse-theia/theia.git"}, "bugs": {"url": "https://github.com/eclipse-theia/theia/issues"}, "homepage": "https://github.com/eclipse-theia/theia", "files": ["lib", "src"], "scripts": {"build": "theiaext build", "clean": "theiaext clean", "compile": "theiaext compile", "docs": "theiaext docs", "lint": "theiaext lint", "test": "theiaext test", "watch": "theiaext watch"}, "devDependencies": {"@theia/ext-scripts": "1.63.0"}, "nyc": {"extends": "../../configs/nyc.json"}, "gitHead": "21358137e41342742707f660b8e222f940a27652"}

import React, { useState, useEffect } from 'react';
import { SystemDiagnostics, SystemDiagnosticsData } from './SystemDiagnostics';
import { NetworkTool, NetworkStatusData } from './NetworkTool';

type ToolData = {
    diagnostics: SystemDiagnosticsData;
    network: NetworkStatusData;
};

export const ToolView: React.FC = () => {
    const [data, setData] = useState<ToolData | null>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);
                const response = await fetch('/api/tools');
                const result = await response.json();
                setData(result);
            } catch (error) {
                console.error("Failed to fetch tool data", error);
            } finally {
                setLoading(false);
            }
        };
        fetchData();
    }, []);

    if (loading) {
        return <div className="flex justify-center items-center h-full text-cyan-400">Loading Tools...</div>;
    }
    
    if (!data) {
        return <div className="flex justify-center items-center h-full text-red-400">Failed to load tool data.</div>;
    }

    return (
        <div className="flex-grow grid grid-cols-1 md:grid-cols-2 gap-4">
        <SystemDiagnostics diagnostics={data.diagnostics} />
        <NetworkTool initialStatus={data.network} />
        </div>
    );
};

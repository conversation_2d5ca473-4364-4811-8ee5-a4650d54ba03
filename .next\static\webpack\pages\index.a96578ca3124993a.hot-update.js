"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _icons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons */ \"./src/components/icons.tsx\");\n\nvar _s = $RefreshSig$();\n\n\nconst NavItem = (param)=>{\n    let { icon, label, active, onClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        \"aria-label\": label,\n        \"aria-pressed\": active,\n        className: \"w-full flex items-center justify-center responsive-border-radius transition-colors duration-200 relative \".concat(active ? \"bg-brand-cyan/20 text-white\" : \"text-gray-400 hover:bg-gray-700/50 hover:text-white\"),\n        style: {\n            padding: \"calc(var(--base-spacing) * 0.5)\",\n            height: \"var(--base-button-height)\"\n        },\n        children: [\n            active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-0 top-1/2 -translate-y-1/2 bg-brand-cyan rounded-r-full\",\n                style: {\n                    height: \"calc(var(--base-button-height) * 0.6)\",\n                    width: \"3px\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, undefined),\n            icon\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n_c = NavItem;\nconst Sidebar = (param)=>{\n    let { activeView, setActiveView, activeApp, setActiveApp } = param;\n    _s();\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isPinned, setIsPinned] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Main function buttons for the collapsible sidebar\n    const functionButtons = [\n        {\n            id: \"gamification\",\n            label: \"Gamification\",\n            icon: \"\\uD83C\\uDFAE\",\n            description: \"Learning Games\"\n        },\n        {\n            id: \"coder\",\n            label: \"Coder\",\n            icon: \"\\uD83D\\uDCBB\",\n            description: \"IDE View\"\n        },\n        {\n            id: \"media\",\n            label: \"Media\",\n            icon: \"\\uD83C\\uDFA5\",\n            description: \"Media Hub\"\n        },\n        {\n            id: \"studio\",\n            label: \"Studio\",\n            icon: \"\\uD83E\\uDDD1‍\\uD83C\\uDFA8\",\n            description: \"Creative Workspace\"\n        }\n    ];\n    // Original navigation items (when collapsed)\n    const navItems = [\n        {\n            id: \"Dashboard\",\n            label: \"Home\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.HomeIcon, {\n                className: \"responsive-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 99,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: \"Analytic\",\n            label: \"Analytics\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.ChartBarIcon, {\n                className: \"responsive-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 104,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: \"Tool\",\n            label: \"Modules\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.GridIcon, {\n                className: \"responsive-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 109,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: \"Setting\",\n            label: \"Profile\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.UserCircleIcon, {\n                className: \"responsive-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 114,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: \"Setting\",\n            label: \"Settings\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.SettingsIcon, {\n                className: \"responsive-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 119,\n                columnNumber: 13\n            }, undefined)\n        }\n    ];\n    const handleFunctionButtonClick = (appId)=>{\n        if (setActiveApp) {\n            setActiveApp(appId);\n        }\n        // Auto-collapse unless pinned\n        if (!isPinned) {\n            setIsCollapsed(true);\n        }\n    };\n    const toggleSidebar = ()=>{\n        setIsCollapsed(!isCollapsed);\n    };\n    const togglePin = ()=>{\n        setIsPinned(!isPinned);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: toggleSidebar,\n                className: \"fixed top-4 left-4 z-50 bg-panel-bg border border-gray-700/50 backdrop-blur-xl rounded-lg p-2 hover:bg-gray-800/60 transition-all duration-300 hover:scale-105\",\n                style: {\n                    boxShadow: \"var(--glass-shadow)\",\n                    width: \"calc(var(--base-button-height) * 1.2)\",\n                    height: \"calc(var(--base-button-height) * 1.2)\"\n                },\n                children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.Bars3Icon, {\n                    className: \"w-6 h-6 text-accent-blue\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.XMarkIcon, {\n                    className: \"w-6 h-6 text-accent-blue\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"fixed left-0 top-0 h-full bg-panel-bg border-r border-gray-700/50 backdrop-blur-xl z-40 transition-all duration-300 \".concat(isCollapsed ? \"-translate-x-full\" : \"translate-x-0\"),\n                style: {\n                    width: \"calc(280px * var(--content-scale))\",\n                    boxShadow: \"var(--glass-shadow)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center text-white w-10 h-10\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.ShieldCheckIcon, {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-white font-semibold text-lg\",\n                                                    children: \"Functions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Choose your workspace\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: togglePin,\n                                    className: \"p-2 rounded-lg transition-all duration-200 \".concat(isPinned ? \"bg-blue-500/20 text-blue-400\" : \"text-gray-400 hover:text-white hover:bg-gray-800/60\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.PinIcon, {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 mb-6\",\n                            children: functionButtons.map((button)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleFunctionButtonClick(button.id),\n                                    className: \"w-full flex items-center gap-4 p-4 rounded-xl transition-all duration-300 group \".concat(activeApp === button.id ? \"bg-blue-500/20 border border-blue-400/30 shadow-lg shadow-blue-500/10\" : \"bg-gray-800/40 border border-gray-700/50 hover:bg-blue-500/10 hover:border-blue-400/20\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl\",\n                                            children: button.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold \".concat(activeApp === button.id ? \"text-blue-400\" : \"text-white\"),\n                                                    children: button.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: button.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 rounded-full transition-all duration-300 \".concat(activeApp === button.id ? \"bg-accent-blue\" : \"bg-transparent\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, button.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-glass-border mb-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-text-muted text-sm font-medium mb-3\",\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, undefined),\n                                navItems.slice(0, 3).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setActiveView(item.id);\n                                            if (setActiveApp) setActiveApp(\"\");\n                                            if (!isPinned) setIsCollapsed(true);\n                                        },\n                                        className: \"w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200 \".concat(activeView === item.id && !activeApp ? \"bg-accent-blue/20 text-accent-blue\" : \"text-text-secondary hover:text-text-primary hover:bg-glass-bg-light\"),\n                                        children: [\n                                            item.icon,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: item.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, item.label, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, undefined))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-auto space-y-2\",\n                            children: navItems.slice(3).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setActiveView(item.id);\n                                        if (setActiveApp) setActiveApp(\"\");\n                                        if (!isPinned) setIsCollapsed(true);\n                                    },\n                                    className: \"w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200 \".concat(activeView === item.id && item.label === \"Settings\" && !activeApp ? \"bg-accent-blue/20 text-accent-blue\" : \"text-text-secondary hover:text-text-primary hover:bg-glass-bg-light\"),\n                                    children: [\n                                        item.icon,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, item.label, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, undefined),\n            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/20 backdrop-blur-sm z-30\",\n                onClick: ()=>setIsCollapsed(true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 274,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"bg-panel-bg border border-gray-700/50 responsive-border-radius sidebar-left flex flex-col items-center justify-between transition-all duration-300 \".concat(isCollapsed ? \"opacity-100\" : \"opacity-0 pointer-events-none\"),\n                style: {\n                    padding: \"calc(var(--base-spacing) * 0.75)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center w-full\",\n                        style: {\n                            gap: \"calc(var(--base-gap) * 0.75)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-cyan-400 to-fuchsia-500 rounded-full flex items-center justify-center text-white\",\n                                style: {\n                                    width: \"calc(var(--base-icon-size) * 1.25)\",\n                                    height: \"calc(var(--base-icon-size) * 1.25)\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.ShieldCheckIcon, {\n                                    style: {\n                                        width: \"calc(var(--base-icon-size) * 1)\",\n                                        height: \"calc(var(--base-icon-size) * 1)\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full border-t border-gray-700\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, undefined),\n                            navItems.slice(0, 3).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavItem, {\n                                    icon: item.icon,\n                                    label: item.label,\n                                    active: activeView === item.id && !activeApp,\n                                    onClick: ()=>{\n                                        setActiveView(item.id);\n                                        if (setActiveApp) setActiveApp(\"\");\n                                    }\n                                }, item.label, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, undefined))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 w-full\",\n                        children: navItems.slice(3).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavItem, {\n                                icon: item.icon,\n                                label: item.label,\n                                active: activeView === item.id && item.label === \"Settings\" && !activeApp,\n                                onClick: ()=>{\n                                    setActiveView(item.id);\n                                    if (setActiveApp) setActiveApp(\"\");\n                                }\n                            }, item.label, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(Sidebar, \"04YrB/mgoahSJlsQvMs++y5tQMc=\");\n_c1 = Sidebar;\nvar _c, _c1;\n$RefreshReg$(_c, \"NavItem\");\n$RefreshReg$(_c1, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9TaWRlYmFyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUN3QztBQVd2QjtBQVNqQixNQUFNVyxVQUFVO1FBQUMsRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUVDLE1BQU0sRUFBRUMsT0FBTyxFQUFnQjt5QkFDM0QsOERBQUNDO1FBQ0dELFNBQVNBO1FBQ1RFLGNBQVlKO1FBQ1pLLGdCQUFjSjtRQUNkSyxXQUFXLDRHQUlWLE9BSEdMLFNBQ0UsZ0NBQ0E7UUFFTk0sT0FBTztZQUNMQyxTQUFTO1lBQ1RDLFFBQVE7UUFDVjs7WUFFSFIsd0JBQ0MsOERBQUNTO2dCQUNDSixXQUFVO2dCQUNWQyxPQUFPO29CQUNMRSxRQUFRO29CQUNSRSxPQUFPO2dCQUNUOzs7Ozs7WUFHSFo7Ozs7Ozs7O0tBeEJDRDtBQW1DQyxNQUFNYyxVQUFrQztRQUFDLEVBQzlDQyxVQUFVLEVBQ1ZDLGFBQWEsRUFDYkMsU0FBUyxFQUNUQyxZQUFZLEVBQ2I7O0lBQ0MsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUc5QiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUMrQixVQUFVQyxZQUFZLEdBQUdoQywrQ0FBUUEsQ0FBQztJQUV6QyxvREFBb0Q7SUFDcEQsTUFBTWlDLGtCQUFrQjtRQUN0QjtZQUNFQyxJQUFJO1lBQ0p0QixPQUFPO1lBQ1BELE1BQU07WUFDTndCLGFBQWE7UUFDZjtRQUNBO1lBQ0VELElBQUk7WUFDSnRCLE9BQU87WUFDUEQsTUFBTTtZQUNOd0IsYUFBYTtRQUNmO1FBQ0E7WUFDRUQsSUFBSTtZQUNKdEIsT0FBTztZQUNQRCxNQUFNO1lBQ053QixhQUFhO1FBQ2Y7UUFDQTtZQUNFRCxJQUFJO1lBQ0p0QixPQUFPO1lBQ1BELE1BQU07WUFDTndCLGFBQWE7UUFDZjtLQUNEO0lBRUQsNkNBQTZDO0lBQzdDLE1BQU1DLFdBQVc7UUFDZjtZQUNFRixJQUFJO1lBQ0p0QixPQUFPO1lBQ1BELG9CQUFNLDhEQUFDViw0Q0FBUUE7Z0JBQUNpQixXQUFVOzs7Ozs7UUFDNUI7UUFDQTtZQUNFZ0IsSUFBSTtZQUNKdEIsT0FBTztZQUNQRCxvQkFBTSw4REFBQ1QsZ0RBQVlBO2dCQUFDZ0IsV0FBVTs7Ozs7O1FBQ2hDO1FBQ0E7WUFDRWdCLElBQUk7WUFDSnRCLE9BQU87WUFDUEQsb0JBQU0sOERBQUNSLDRDQUFRQTtnQkFBQ2UsV0FBVTs7Ozs7O1FBQzVCO1FBQ0E7WUFDRWdCLElBQUk7WUFDSnRCLE9BQU87WUFDUEQsb0JBQU0sOERBQUNOLGtEQUFjQTtnQkFBQ2EsV0FBVTs7Ozs7O1FBQ2xDO1FBQ0E7WUFDRWdCLElBQUk7WUFDSnRCLE9BQU87WUFDUEQsb0JBQU0sOERBQUNQLGdEQUFZQTtnQkFBQ2MsV0FBVTs7Ozs7O1FBQ2hDO0tBQ0Q7SUFFRCxNQUFNbUIsNEJBQTRCLENBQUNDO1FBQ2pDLElBQUlWLGNBQWM7WUFDaEJBLGFBQWFVO1FBQ2Y7UUFDQSw4QkFBOEI7UUFDOUIsSUFBSSxDQUFDUCxVQUFVO1lBQ2JELGVBQWU7UUFDakI7SUFDRjtJQUVBLE1BQU1TLGdCQUFnQjtRQUNwQlQsZUFBZSxDQUFDRDtJQUNsQjtJQUVBLE1BQU1XLFlBQVk7UUFDaEJSLFlBQVksQ0FBQ0Q7SUFDZjtJQUVBLHFCQUNFOzswQkFFRSw4REFBQ2hCO2dCQUNDRCxTQUFTeUI7Z0JBQ1RyQixXQUFVO2dCQUNWQyxPQUFPO29CQUNMc0IsV0FBVztvQkFDWGxCLE9BQU87b0JBQ1BGLFFBQVE7Z0JBQ1Y7MEJBRUNRLDRCQUNDLDhEQUFDdEIsNkNBQVNBO29CQUFDVyxXQUFVOzs7Ozs4Q0FFckIsOERBQUNWLDZDQUFTQTtvQkFBQ1UsV0FBVTs7Ozs7Ozs7Ozs7MEJBS3pCLDhEQUFDd0I7Z0JBQ0N4QixXQUFXLHVIQUVWLE9BRENXLGNBQWMsc0JBQXNCO2dCQUV0Q1YsT0FBTztvQkFDTEksT0FBTztvQkFDUGtCLFdBQVc7Z0JBQ2I7MEJBRUEsNEVBQUNFO29CQUFJekIsV0FBVTs7c0NBRWIsOERBQUN5Qjs0QkFBSXpCLFdBQVU7OzhDQUNiLDhEQUFDeUI7b0NBQUl6QixXQUFVOztzREFDYiw4REFBQ3lCOzRDQUFJekIsV0FBVTtzREFDYiw0RUFBQ1osbURBQWVBO2dEQUFDWSxXQUFVOzs7Ozs7Ozs7OztzREFFN0IsOERBQUN5Qjs7OERBQ0MsOERBQUNDO29EQUFHMUIsV0FBVTs4REFBbUM7Ozs7Ozs4REFDakQsOERBQUMyQjtvREFBRTNCLFdBQVU7OERBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBR3pDLDhEQUFDSDtvQ0FDQ0QsU0FBUzBCO29DQUNUdEIsV0FBVyw4Q0FJVixPQUhDYSxXQUNJLGlDQUNBOzhDQUdOLDRFQUFDdEIsMkNBQU9BO3dDQUFDUyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLdkIsOERBQUN5Qjs0QkFBSXpCLFdBQVU7c0NBQ1plLGdCQUFnQmEsR0FBRyxDQUFDLENBQUMvQix1QkFDcEIsOERBQUNBO29DQUVDRCxTQUFTLElBQU11QiwwQkFBMEJ0QixPQUFPbUIsRUFBRTtvQ0FDbERoQixXQUFXLG1GQUlWLE9BSENTLGNBQWNaLE9BQU9tQixFQUFFLEdBQ25CLDBFQUNBOztzREFHTiw4REFBQ1M7NENBQUl6QixXQUFVO3NEQUFZSCxPQUFPSixJQUFJOzs7Ozs7c0RBQ3RDLDhEQUFDZ0M7NENBQUl6QixXQUFVOzs4REFDYiw4REFBQ3lCO29EQUFJekIsV0FBVyxpQkFFZixPQURDUyxjQUFjWixPQUFPbUIsRUFBRSxHQUFHLGtCQUFrQjs4REFFM0NuQixPQUFPSCxLQUFLOzs7Ozs7OERBRWYsOERBQUMrQjtvREFBSXpCLFdBQVU7OERBQXlCSCxPQUFPb0IsV0FBVzs7Ozs7Ozs7Ozs7O3NEQUU1RCw4REFBQ1E7NENBQUl6QixXQUFXLG9EQUVmLE9BRENTLGNBQWNaLE9BQU9tQixFQUFFLEdBQUcsbUJBQW1COzs7Ozs7O21DQWxCMUNuQixPQUFPbUIsRUFBRTs7Ozs7Ozs7OztzQ0F5QnBCLDhEQUFDUzs0QkFBSXpCLFdBQVU7Ozs7OztzQ0FHZiw4REFBQ3lCOzRCQUFJekIsV0FBVTs7OENBQ2IsOERBQUM2QjtvQ0FBRzdCLFdBQVU7OENBQTJDOzs7Ozs7Z0NBQ3hEa0IsU0FBU1ksS0FBSyxDQUFDLEdBQUcsR0FBR0YsR0FBRyxDQUFDRyxDQUFBQSxxQkFDeEIsOERBQUNsQzt3Q0FFQ0QsU0FBUzs0Q0FDUFksY0FBY3VCLEtBQUtmLEVBQUU7NENBQ3JCLElBQUlOLGNBQWNBLGFBQWE7NENBQy9CLElBQUksQ0FBQ0csVUFBVUQsZUFBZTt3Q0FDaEM7d0NBQ0FaLFdBQVcsNkVBSVYsT0FIQ08sZUFBZXdCLEtBQUtmLEVBQUUsSUFBSSxDQUFDUCxZQUN2Qix1Q0FDQTs7NENBR0xzQixLQUFLdEMsSUFBSTswREFDViw4REFBQ1c7Z0RBQUtKLFdBQVU7MERBQWUrQixLQUFLckMsS0FBSzs7Ozs7Ozt1Q0FicENxQyxLQUFLckMsS0FBSzs7Ozs7Ozs7Ozs7c0NBbUJyQiw4REFBQytCOzRCQUFJekIsV0FBVTtzQ0FDWmtCLFNBQVNZLEtBQUssQ0FBQyxHQUFHRixHQUFHLENBQUNHLENBQUFBLHFCQUNyQiw4REFBQ2xDO29DQUVDRCxTQUFTO3dDQUNQWSxjQUFjdUIsS0FBS2YsRUFBRTt3Q0FDckIsSUFBSU4sY0FBY0EsYUFBYTt3Q0FDL0IsSUFBSSxDQUFDRyxVQUFVRCxlQUFlO29DQUNoQztvQ0FDQVosV0FBVyw2RUFJVixPQUhDTyxlQUFld0IsS0FBS2YsRUFBRSxJQUFJZSxLQUFLckMsS0FBSyxLQUFLLGNBQWMsQ0FBQ2UsWUFDcEQsdUNBQ0E7O3dDQUdMc0IsS0FBS3RDLElBQUk7c0RBQ1YsOERBQUNXOzRDQUFLSixXQUFVO3NEQUFlK0IsS0FBS3JDLEtBQUs7Ozs7Ozs7bUNBYnBDcUMsS0FBS3JDLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQXFCeEIsQ0FBQ2lCLDZCQUNBLDhEQUFDYztnQkFDQ3pCLFdBQVU7Z0JBQ1ZKLFNBQVMsSUFBTWdCLGVBQWU7Ozs7OzswQkFLbEMsOERBQUNZO2dCQUNDeEIsV0FBVyxzSkFFVixPQURDVyxjQUFjLGdCQUFnQjtnQkFFaENWLE9BQU87b0JBQUVDLFNBQVM7Z0JBQW1DOztrQ0FFckQsOERBQUN1Qjt3QkFBSXpCLFdBQVU7d0JBQW9DQyxPQUFPOzRCQUFFK0IsS0FBSzt3QkFBK0I7OzBDQUM5Riw4REFBQ1A7Z0NBQ0N6QixXQUFVO2dDQUNWQyxPQUFPO29DQUNMSSxPQUFPO29DQUNQRixRQUFRO2dDQUNWOzBDQUVBLDRFQUFDZixtREFBZUE7b0NBQ2RhLE9BQU87d0NBQ0xJLE9BQU87d0NBQ1BGLFFBQVE7b0NBQ1Y7Ozs7Ozs7Ozs7OzBDQUdKLDhEQUFDc0I7Z0NBQUl6QixXQUFVOzs7Ozs7NEJBQ2RrQixTQUFTWSxLQUFLLENBQUMsR0FBRyxHQUFHRixHQUFHLENBQUNHLENBQUFBLHFCQUN4Qiw4REFBQ3ZDO29DQUVDQyxNQUFNc0MsS0FBS3RDLElBQUk7b0NBQ2ZDLE9BQU9xQyxLQUFLckMsS0FBSztvQ0FDakJDLFFBQVFZLGVBQWV3QixLQUFLZixFQUFFLElBQUksQ0FBQ1A7b0NBQ25DYixTQUFTO3dDQUNQWSxjQUFjdUIsS0FBS2YsRUFBRTt3Q0FDckIsSUFBSU4sY0FBY0EsYUFBYTtvQ0FDakM7bUNBUEtxQixLQUFLckMsS0FBSzs7Ozs7Ozs7Ozs7a0NBV3JCLDhEQUFDK0I7d0JBQUl6QixXQUFVO2tDQUNaa0IsU0FBU1ksS0FBSyxDQUFDLEdBQUdGLEdBQUcsQ0FBQ0csQ0FBQUEscUJBQ3JCLDhEQUFDdkM7Z0NBRUNDLE1BQU1zQyxLQUFLdEMsSUFBSTtnQ0FDZkMsT0FBT3FDLEtBQUtyQyxLQUFLO2dDQUNqQkMsUUFBUVksZUFBZXdCLEtBQUtmLEVBQUUsSUFBSWUsS0FBS3JDLEtBQUssS0FBSyxjQUFjLENBQUNlO2dDQUNoRWIsU0FBUztvQ0FDUFksY0FBY3VCLEtBQUtmLEVBQUU7b0NBQ3JCLElBQUlOLGNBQWNBLGFBQWE7Z0NBQ2pDOytCQVBLcUIsS0FBS3JDLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQWM3QixFQUFFO0dBcFJXWTtNQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9TaWRlYmFyLnRzeD81NTUxIl0sInNvdXJjZXNDb250ZW50IjpbIlxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHtcbiAgSG9tZUljb24sXG4gIENoYXJ0QmFySWNvbixcbiAgR3JpZEljb24sXG4gIFNldHRpbmdzSWNvbixcbiAgVXNlckNpcmNsZUljb24sXG4gIFNoaWVsZENoZWNrSWNvbixcbiAgQmFyczNJY29uLFxuICBYTWFya0ljb24sXG4gIFBpbkljb25cbn0gZnJvbSAnLi9pY29ucyc7XG5cbmludGVyZmFjZSBOYXZJdGVtUHJvcHMge1xuICAgIGljb246IFJlYWN0LlJlYWN0Tm9kZTtcbiAgICBsYWJlbDogc3RyaW5nO1xuICAgIGFjdGl2ZTogYm9vbGVhbjtcbiAgICBvbkNsaWNrOiAoKSA9PiB2b2lkO1xufVxuXG5jb25zdCBOYXZJdGVtID0gKHsgaWNvbiwgbGFiZWwsIGFjdGl2ZSwgb25DbGljayB9OiBOYXZJdGVtUHJvcHMpID0+IChcbiAgICA8YnV0dG9uXG4gICAgICAgIG9uQ2xpY2s9e29uQ2xpY2t9XG4gICAgICAgIGFyaWEtbGFiZWw9e2xhYmVsfVxuICAgICAgICBhcmlhLXByZXNzZWQ9e2FjdGl2ZX1cbiAgICAgICAgY2xhc3NOYW1lPXtgdy1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHJlc3BvbnNpdmUtYm9yZGVyLXJhZGl1cyB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDAgcmVsYXRpdmUgJHtcbiAgICAgICAgICAgIGFjdGl2ZVxuICAgICAgICAgICAgPyAnYmctYnJhbmQtY3lhbi8yMCB0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgOiAndGV4dC1ncmF5LTQwMCBob3ZlcjpiZy1ncmF5LTcwMC81MCBob3Zlcjp0ZXh0LXdoaXRlJ1xuICAgICAgICB9YH1cbiAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICBwYWRkaW5nOiAnY2FsYyh2YXIoLS1iYXNlLXNwYWNpbmcpICogMC41KScsXG4gICAgICAgICAgaGVpZ2h0OiAndmFyKC0tYmFzZS1idXR0b24taGVpZ2h0KSdcbiAgICAgICAgfX1cbiAgICA+XG4gICAge2FjdGl2ZSAmJiAoXG4gICAgICA8c3BhblxuICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTAgdG9wLTEvMiAtdHJhbnNsYXRlLXktMS8yIGJnLWJyYW5kLWN5YW4gcm91bmRlZC1yLWZ1bGxcIlxuICAgICAgICBzdHlsZT17e1xuICAgICAgICAgIGhlaWdodDogJ2NhbGModmFyKC0tYmFzZS1idXR0b24taGVpZ2h0KSAqIDAuNiknLFxuICAgICAgICAgIHdpZHRoOiAnM3B4J1xuICAgICAgICB9fVxuICAgICAgLz5cbiAgICApfVxuICAgIHtpY29ufVxuICA8L2J1dHRvbj5cbik7XG5cbmludGVyZmFjZSBTaWRlYmFyUHJvcHMge1xuICAgIGFjdGl2ZVZpZXc6IHN0cmluZztcbiAgICBzZXRBY3RpdmVWaWV3OiAodmlldzogc3RyaW5nKSA9PiB2b2lkO1xuICAgIGFjdGl2ZUFwcD86IHN0cmluZztcbiAgICBzZXRBY3RpdmVBcHA/OiAoYXBwOiBzdHJpbmcpID0+IHZvaWQ7XG59XG5cbmV4cG9ydCBjb25zdCBTaWRlYmFyOiBSZWFjdC5GQzxTaWRlYmFyUHJvcHM+ID0gKHtcbiAgYWN0aXZlVmlldyxcbiAgc2V0QWN0aXZlVmlldyxcbiAgYWN0aXZlQXBwLFxuICBzZXRBY3RpdmVBcHBcbn0pID0+IHtcbiAgY29uc3QgW2lzQ29sbGFwc2VkLCBzZXRJc0NvbGxhcHNlZF0gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW2lzUGlubmVkLCBzZXRJc1Bpbm5lZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8gTWFpbiBmdW5jdGlvbiBidXR0b25zIGZvciB0aGUgY29sbGFwc2libGUgc2lkZWJhclxuICBjb25zdCBmdW5jdGlvbkJ1dHRvbnMgPSBbXG4gICAge1xuICAgICAgaWQ6ICdnYW1pZmljYXRpb24nLFxuICAgICAgbGFiZWw6ICdHYW1pZmljYXRpb24nLFxuICAgICAgaWNvbjogJ/Cfjq4nLFxuICAgICAgZGVzY3JpcHRpb246ICdMZWFybmluZyBHYW1lcydcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnY29kZXInLFxuICAgICAgbGFiZWw6ICdDb2RlcicsXG4gICAgICBpY29uOiAn8J+SuycsXG4gICAgICBkZXNjcmlwdGlvbjogJ0lERSBWaWV3J1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdtZWRpYScsXG4gICAgICBsYWJlbDogJ01lZGlhJyxcbiAgICAgIGljb246ICfwn46lJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnTWVkaWEgSHViJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdzdHVkaW8nLFxuICAgICAgbGFiZWw6ICdTdHVkaW8nLFxuICAgICAgaWNvbjogJ/Cfp5HigI3wn46oJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnQ3JlYXRpdmUgV29ya3NwYWNlJ1xuICAgIH1cbiAgXTtcblxuICAvLyBPcmlnaW5hbCBuYXZpZ2F0aW9uIGl0ZW1zICh3aGVuIGNvbGxhcHNlZClcbiAgY29uc3QgbmF2SXRlbXMgPSBbXG4gICAge1xuICAgICAgaWQ6ICdEYXNoYm9hcmQnLFxuICAgICAgbGFiZWw6ICdIb21lJyxcbiAgICAgIGljb246IDxIb21lSWNvbiBjbGFzc05hbWU9XCJyZXNwb25zaXZlLWljb25cIiAvPlxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdBbmFseXRpYycsXG4gICAgICBsYWJlbDogJ0FuYWx5dGljcycsXG4gICAgICBpY29uOiA8Q2hhcnRCYXJJY29uIGNsYXNzTmFtZT1cInJlc3BvbnNpdmUtaWNvblwiIC8+XG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJ1Rvb2wnLFxuICAgICAgbGFiZWw6ICdNb2R1bGVzJyxcbiAgICAgIGljb246IDxHcmlkSWNvbiBjbGFzc05hbWU9XCJyZXNwb25zaXZlLWljb25cIiAvPlxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdTZXR0aW5nJyxcbiAgICAgIGxhYmVsOiAnUHJvZmlsZScsXG4gICAgICBpY29uOiA8VXNlckNpcmNsZUljb24gY2xhc3NOYW1lPVwicmVzcG9uc2l2ZS1pY29uXCIgLz5cbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnU2V0dGluZycsXG4gICAgICBsYWJlbDogJ1NldHRpbmdzJyxcbiAgICAgIGljb246IDxTZXR0aW5nc0ljb24gY2xhc3NOYW1lPVwicmVzcG9uc2l2ZS1pY29uXCIgLz5cbiAgICB9LFxuICBdO1xuXG4gIGNvbnN0IGhhbmRsZUZ1bmN0aW9uQnV0dG9uQ2xpY2sgPSAoYXBwSWQ6IHN0cmluZykgPT4ge1xuICAgIGlmIChzZXRBY3RpdmVBcHApIHtcbiAgICAgIHNldEFjdGl2ZUFwcChhcHBJZCk7XG4gICAgfVxuICAgIC8vIEF1dG8tY29sbGFwc2UgdW5sZXNzIHBpbm5lZFxuICAgIGlmICghaXNQaW5uZWQpIHtcbiAgICAgIHNldElzQ29sbGFwc2VkKHRydWUpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCB0b2dnbGVTaWRlYmFyID0gKCkgPT4ge1xuICAgIHNldElzQ29sbGFwc2VkKCFpc0NvbGxhcHNlZCk7XG4gIH07XG5cbiAgY29uc3QgdG9nZ2xlUGluID0gKCkgPT4ge1xuICAgIHNldElzUGlubmVkKCFpc1Bpbm5lZCk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8PlxuICAgICAgey8qIFRvZ2dsZSBCdXR0b24gLSBBbHdheXMgdmlzaWJsZSBpbiB0b3AtbGVmdCAqL31cbiAgICAgIDxidXR0b25cbiAgICAgICAgb25DbGljaz17dG9nZ2xlU2lkZWJhcn1cbiAgICAgICAgY2xhc3NOYW1lPVwiZml4ZWQgdG9wLTQgbGVmdC00IHotNTAgYmctcGFuZWwtYmcgYm9yZGVyIGJvcmRlci1ncmF5LTcwMC81MCBiYWNrZHJvcC1ibHVyLXhsIHJvdW5kZWQtbGcgcC0yIGhvdmVyOmJnLWdyYXktODAwLzYwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBob3ZlcjpzY2FsZS0xMDVcIlxuICAgICAgICBzdHlsZT17e1xuICAgICAgICAgIGJveFNoYWRvdzogJ3ZhcigtLWdsYXNzLXNoYWRvdyknLFxuICAgICAgICAgIHdpZHRoOiAnY2FsYyh2YXIoLS1iYXNlLWJ1dHRvbi1oZWlnaHQpICogMS4yKScsXG4gICAgICAgICAgaGVpZ2h0OiAnY2FsYyh2YXIoLS1iYXNlLWJ1dHRvbi1oZWlnaHQpICogMS4yKSdcbiAgICAgICAgfX1cbiAgICAgID5cbiAgICAgICAge2lzQ29sbGFwc2VkID8gKFxuICAgICAgICAgIDxCYXJzM0ljb24gY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LWFjY2VudC1ibHVlXCIgLz5cbiAgICAgICAgKSA6IChcbiAgICAgICAgICA8WE1hcmtJY29uIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC1hY2NlbnQtYmx1ZVwiIC8+XG4gICAgICAgICl9XG4gICAgICA8L2J1dHRvbj5cblxuICAgICAgey8qIENvbGxhcHNpYmxlIFNpZGViYXIgKi99XG4gICAgICA8YXNpZGVcbiAgICAgICAgY2xhc3NOYW1lPXtgZml4ZWQgbGVmdC0wIHRvcC0wIGgtZnVsbCBiZy1wYW5lbC1iZyBib3JkZXItciBib3JkZXItZ3JheS03MDAvNTAgYmFja2Ryb3AtYmx1ci14bCB6LTQwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCAke1xuICAgICAgICAgIGlzQ29sbGFwc2VkID8gJy10cmFuc2xhdGUteC1mdWxsJyA6ICd0cmFuc2xhdGUteC0wJ1xuICAgICAgICB9YH1cbiAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICB3aWR0aDogJ2NhbGMoMjgwcHggKiB2YXIoLS1jb250ZW50LXNjYWxlKSknLFxuICAgICAgICAgIGJveFNoYWRvdzogJ3ZhcigtLWdsYXNzLXNoYWRvdyknXG4gICAgICAgIH19XG4gICAgICA+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBoLWZ1bGwgcC02XCI+XG4gICAgICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi02XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibHVlLTUwMCB0by1jeWFuLTUwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdGV4dC13aGl0ZSB3LTEwIGgtMTBcIj5cbiAgICAgICAgICAgICAgICA8U2hpZWxkQ2hlY2tJY29uIGNsYXNzTmFtZT1cInctNiBoLTZcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LXNlbWlib2xkIHRleHQtbGdcIj5GdW5jdGlvbnM8L2gyPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbVwiPkNob29zZSB5b3VyIHdvcmtzcGFjZTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17dG9nZ2xlUGlufVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwLTIgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgJHtcbiAgICAgICAgICAgICAgICBpc1Bpbm5lZFxuICAgICAgICAgICAgICAgICAgPyAnYmctYmx1ZS01MDAvMjAgdGV4dC1ibHVlLTQwMCdcbiAgICAgICAgICAgICAgICAgIDogJ3RleHQtZ3JheS00MDAgaG92ZXI6dGV4dC13aGl0ZSBob3ZlcjpiZy1ncmF5LTgwMC82MCdcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxQaW5JY29uIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogRnVuY3Rpb24gQnV0dG9ucyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMyBtYi02XCI+XG4gICAgICAgICAgICB7ZnVuY3Rpb25CdXR0b25zLm1hcCgoYnV0dG9uKSA9PiAoXG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBrZXk9e2J1dHRvbi5pZH1cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVGdW5jdGlvbkJ1dHRvbkNsaWNrKGJ1dHRvbi5pZCl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGdhcC00IHAtNCByb3VuZGVkLXhsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBncm91cCAke1xuICAgICAgICAgICAgICAgICAgYWN0aXZlQXBwID09PSBidXR0b24uaWRcbiAgICAgICAgICAgICAgICAgICAgPyAnYmctYmx1ZS01MDAvMjAgYm9yZGVyIGJvcmRlci1ibHVlLTQwMC8zMCBzaGFkb3ctbGcgc2hhZG93LWJsdWUtNTAwLzEwJ1xuICAgICAgICAgICAgICAgICAgICA6ICdiZy1ncmF5LTgwMC80MCBib3JkZXIgYm9yZGVyLWdyYXktNzAwLzUwIGhvdmVyOmJnLWJsdWUtNTAwLzEwIGhvdmVyOmJvcmRlci1ibHVlLTQwMC8yMCdcbiAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGxcIj57YnV0dG9uLmljb259PC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1sZWZ0XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZvbnQtc2VtaWJvbGQgJHtcbiAgICAgICAgICAgICAgICAgICAgYWN0aXZlQXBwID09PSBidXR0b24uaWQgPyAndGV4dC1ibHVlLTQwMCcgOiAndGV4dC13aGl0ZSdcbiAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAge2J1dHRvbi5sYWJlbH1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtc21cIj57YnV0dG9uLmRlc2NyaXB0aW9ufTwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdy0yIGgtMiByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwICR7XG4gICAgICAgICAgICAgICAgICBhY3RpdmVBcHAgPT09IGJ1dHRvbi5pZCA/ICdiZy1hY2NlbnQtYmx1ZScgOiAnYmctdHJhbnNwYXJlbnQnXG4gICAgICAgICAgICAgICAgfWB9IC8+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgKSl9XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogRGl2aWRlciAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci10IGJvcmRlci1nbGFzcy1ib3JkZXIgbWItNlwiPjwvZGl2PlxuXG4gICAgICAgICAgey8qIE9yaWdpbmFsIE5hdmlnYXRpb24gKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXRleHQtbXV0ZWQgdGV4dC1zbSBmb250LW1lZGl1bSBtYi0zXCI+RGFzaGJvYXJkPC9oMz5cbiAgICAgICAgICAgIHtuYXZJdGVtcy5zbGljZSgwLCAzKS5tYXAoaXRlbSA9PiAoXG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBrZXk9e2l0ZW0ubGFiZWx9XG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgc2V0QWN0aXZlVmlldyhpdGVtLmlkKTtcbiAgICAgICAgICAgICAgICAgIGlmIChzZXRBY3RpdmVBcHApIHNldEFjdGl2ZUFwcCgnJyk7XG4gICAgICAgICAgICAgICAgICBpZiAoIWlzUGlubmVkKSBzZXRJc0NvbGxhcHNlZCh0cnVlKTtcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMyBwLTMgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgJHtcbiAgICAgICAgICAgICAgICAgIGFjdGl2ZVZpZXcgPT09IGl0ZW0uaWQgJiYgIWFjdGl2ZUFwcFxuICAgICAgICAgICAgICAgICAgICA/ICdiZy1hY2NlbnQtYmx1ZS8yMCB0ZXh0LWFjY2VudC1ibHVlJ1xuICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LXRleHQtc2Vjb25kYXJ5IGhvdmVyOnRleHQtdGV4dC1wcmltYXJ5IGhvdmVyOmJnLWdsYXNzLWJnLWxpZ2h0J1xuICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2l0ZW0uaWNvbn1cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntpdGVtLmxhYmVsfTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBCb3R0b20gTmF2aWdhdGlvbiAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LWF1dG8gc3BhY2UteS0yXCI+XG4gICAgICAgICAgICB7bmF2SXRlbXMuc2xpY2UoMykubWFwKGl0ZW0gPT4gKFxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAga2V5PXtpdGVtLmxhYmVsfVxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgIHNldEFjdGl2ZVZpZXcoaXRlbS5pZCk7XG4gICAgICAgICAgICAgICAgICBpZiAoc2V0QWN0aXZlQXBwKSBzZXRBY3RpdmVBcHAoJycpO1xuICAgICAgICAgICAgICAgICAgaWYgKCFpc1Bpbm5lZCkgc2V0SXNDb2xsYXBzZWQodHJ1ZSk7XG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTMgcC0zIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwICR7XG4gICAgICAgICAgICAgICAgICBhY3RpdmVWaWV3ID09PSBpdGVtLmlkICYmIGl0ZW0ubGFiZWwgPT09ICdTZXR0aW5ncycgJiYgIWFjdGl2ZUFwcFxuICAgICAgICAgICAgICAgICAgICA/ICdiZy1hY2NlbnQtYmx1ZS8yMCB0ZXh0LWFjY2VudC1ibHVlJ1xuICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LXRleHQtc2Vjb25kYXJ5IGhvdmVyOnRleHQtdGV4dC1wcmltYXJ5IGhvdmVyOmJnLWdsYXNzLWJnLWxpZ2h0J1xuICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2l0ZW0uaWNvbn1cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntpdGVtLmxhYmVsfTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2FzaWRlPlxuXG4gICAgICB7LyogT3ZlcmxheSB3aGVuIHNpZGViYXIgaXMgb3BlbiAqL31cbiAgICAgIHshaXNDb2xsYXBzZWQgJiYgKFxuICAgICAgICA8ZGl2XG4gICAgICAgICAgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ibGFjay8yMCBiYWNrZHJvcC1ibHVyLXNtIHotMzBcIlxuICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzQ29sbGFwc2VkKHRydWUpfVxuICAgICAgICAvPlxuICAgICAgKX1cblxuICAgICAgey8qIE9yaWdpbmFsIENvbGxhcHNlZCBTaWRlYmFyICh3aGVuIHNpZGViYXIgaXMgY29sbGFwc2VkKSAqL31cbiAgICAgIDxhc2lkZVxuICAgICAgICBjbGFzc05hbWU9e2BiZy1wYW5lbC1iZyBib3JkZXIgYm9yZGVyLWdyYXktNzAwLzUwIHJlc3BvbnNpdmUtYm9yZGVyLXJhZGl1cyBzaWRlYmFyLWxlZnQgZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCAke1xuICAgICAgICAgIGlzQ29sbGFwc2VkID8gJ29wYWNpdHktMTAwJyA6ICdvcGFjaXR5LTAgcG9pbnRlci1ldmVudHMtbm9uZSdcbiAgICAgICAgfWB9XG4gICAgICAgIHN0eWxlPXt7IHBhZGRpbmc6ICdjYWxjKHZhcigtLWJhc2Utc3BhY2luZykgKiAwLjc1KScgfX1cbiAgICAgID5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlciB3LWZ1bGxcIiBzdHlsZT17eyBnYXA6ICdjYWxjKHZhcigtLWJhc2UtZ2FwKSAqIDAuNzUpJyB9fT5cbiAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1iciBmcm9tLWN5YW4tNDAwIHRvLWZ1Y2hzaWEtNTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgIHdpZHRoOiAnY2FsYyh2YXIoLS1iYXNlLWljb24tc2l6ZSkgKiAxLjI1KScsXG4gICAgICAgICAgICAgIGhlaWdodDogJ2NhbGModmFyKC0tYmFzZS1pY29uLXNpemUpICogMS4yNSknXG4gICAgICAgICAgICB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTaGllbGRDaGVja0ljb25cbiAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICB3aWR0aDogJ2NhbGModmFyKC0tYmFzZS1pY29uLXNpemUpICogMSknLFxuICAgICAgICAgICAgICAgIGhlaWdodDogJ2NhbGModmFyKC0tYmFzZS1pY29uLXNpemUpICogMSknXG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGJvcmRlci10IGJvcmRlci1ncmF5LTcwMFwiPjwvZGl2PlxuICAgICAgICAgIHtuYXZJdGVtcy5zbGljZSgwLCAzKS5tYXAoaXRlbSA9PiAoXG4gICAgICAgICAgICA8TmF2SXRlbVxuICAgICAgICAgICAgICBrZXk9e2l0ZW0ubGFiZWx9XG4gICAgICAgICAgICAgIGljb249e2l0ZW0uaWNvbn1cbiAgICAgICAgICAgICAgbGFiZWw9e2l0ZW0ubGFiZWx9XG4gICAgICAgICAgICAgIGFjdGl2ZT17YWN0aXZlVmlldyA9PT0gaXRlbS5pZCAmJiAhYWN0aXZlQXBwfVxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgc2V0QWN0aXZlVmlldyhpdGVtLmlkKTtcbiAgICAgICAgICAgICAgICBpZiAoc2V0QWN0aXZlQXBwKSBzZXRBY3RpdmVBcHAoJycpO1xuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIgZ2FwLTQgdy1mdWxsXCI+XG4gICAgICAgICAge25hdkl0ZW1zLnNsaWNlKDMpLm1hcChpdGVtID0+IChcbiAgICAgICAgICAgIDxOYXZJdGVtXG4gICAgICAgICAgICAgIGtleT17aXRlbS5sYWJlbH1cbiAgICAgICAgICAgICAgaWNvbj17aXRlbS5pY29ufVxuICAgICAgICAgICAgICBsYWJlbD17aXRlbS5sYWJlbH1cbiAgICAgICAgICAgICAgYWN0aXZlPXthY3RpdmVWaWV3ID09PSBpdGVtLmlkICYmIGl0ZW0ubGFiZWwgPT09ICdTZXR0aW5ncycgJiYgIWFjdGl2ZUFwcH1cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgIHNldEFjdGl2ZVZpZXcoaXRlbS5pZCk7XG4gICAgICAgICAgICAgICAgaWYgKHNldEFjdGl2ZUFwcCkgc2V0QWN0aXZlQXBwKCcnKTtcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9hc2lkZT5cbiAgICA8Lz5cbiAgKTtcbn07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsIkhvbWVJY29uIiwiQ2hhcnRCYXJJY29uIiwiR3JpZEljb24iLCJTZXR0aW5nc0ljb24iLCJVc2VyQ2lyY2xlSWNvbiIsIlNoaWVsZENoZWNrSWNvbiIsIkJhcnMzSWNvbiIsIlhNYXJrSWNvbiIsIlBpbkljb24iLCJOYXZJdGVtIiwiaWNvbiIsImxhYmVsIiwiYWN0aXZlIiwib25DbGljayIsImJ1dHRvbiIsImFyaWEtbGFiZWwiLCJhcmlhLXByZXNzZWQiLCJjbGFzc05hbWUiLCJzdHlsZSIsInBhZGRpbmciLCJoZWlnaHQiLCJzcGFuIiwid2lkdGgiLCJTaWRlYmFyIiwiYWN0aXZlVmlldyIsInNldEFjdGl2ZVZpZXciLCJhY3RpdmVBcHAiLCJzZXRBY3RpdmVBcHAiLCJpc0NvbGxhcHNlZCIsInNldElzQ29sbGFwc2VkIiwiaXNQaW5uZWQiLCJzZXRJc1Bpbm5lZCIsImZ1bmN0aW9uQnV0dG9ucyIsImlkIiwiZGVzY3JpcHRpb24iLCJuYXZJdGVtcyIsImhhbmRsZUZ1bmN0aW9uQnV0dG9uQ2xpY2siLCJhcHBJZCIsInRvZ2dsZVNpZGViYXIiLCJ0b2dnbGVQaW4iLCJib3hTaGFkb3ciLCJhc2lkZSIsImRpdiIsImgyIiwicCIsIm1hcCIsImgzIiwic2xpY2UiLCJpdGVtIiwiZ2FwIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/Sidebar.tsx\n"));

/***/ })

});
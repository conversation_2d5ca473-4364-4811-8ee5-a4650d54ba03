// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`number-line widget snapshots all tick labels show when "Style" is "decimal ticks" (deprecated option): show decimal ticks 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph perseus-paragraph-centered"
      data-perseus-paragraph-index="0"
    >
      <div
        class="perseus-block-math"
      >
        <div
          class="perseus-block-math-inner"
          style="overflow-x: auto; overflow-y: hidden; padding-top: 10px; padding-bottom: 10px; margin-top: -10px; margin-bottom: -10px;"
        >
          <span
            class="mock-TeX"
          >
            E=2.5
          </span>
        </div>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="1"
    >
      <div
        class="paragraph"
      >
        <strong>
          Move the dot to 
          <span
            style="white-space: nowrap;"
          >
            <span />
            <span
              class="mock-TeX"
            >
              -E
            </span>
            <span />
          </span>
           on the number line.
        </strong>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="2"
    >
      <div
        class="paragraph"
      >
        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="perseus-widget perseus-widget-interactive-number-line"
          >
            <div
              class="graphie-container"
            >
              <div
                aria-hidden="true"
                class="graphie"
                style="position: relative; width: 460px; height: 80px;"
              >
                <svg
                  height="80"
                  style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); z-index: 2;"
                  version="1.1"
                  width="460"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <desc
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  >
                    Created with Raphaël
                  </desc>
                  <defs
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  />
                  <rect
                    fill="#000000"
                    height="80"
                    opacity="0"
                    r="0"
                    rx="0"
                    ry="0"
                    stroke="#000"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); opacity: 0;"
                    width="460"
                    x="0"
                    y="0"
                  />
                </svg>
                <svg
                  height="80"
                  style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  version="1.1"
                  width="460"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <desc
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  >
                    Created with Raphaël
                  </desc>
                  <defs
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  />
                  <path
                    d="M454.45,45.6C454.8,43.5,458.65,40.35,459.7,40C458.65,39.65,454.8,36.5,454.45,34.4"
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2; stroke-linejoin: round; stroke-linecap: round;"
                    transform=""
                  />
                  <path
                    d="M230,40C230,40,230,40,458.95,40"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M-3.4499999999999886,45.6C-3.0999999999999885,43.5,0.7500000000000115,40.35,1.8000000000000114,40C0.7500000000000113,39.65,-3.099999999999989,36.5,-3.4499999999999886,34.4"
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2; stroke-linejoin: round; stroke-linecap: round;"
                    transform="rotate(180 1.8000000000000114 40)"
                  />
                  <path
                    d="M230,40C230,40,230,40,1.0500000000000114,40"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M30,48L30,32"
                    fill="none"
                    stroke="#1865f2"
                    stroke-width="3.5"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 3.5;"
                  />
                  <path
                    d="M50,48L50,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M70,48L70,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M90,48L90,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M110,48L110,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M130,48L130,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M150,48L150,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M170,48L170,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M190,48L190,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M210,48L210,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M230,48L230,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M250,48L250,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M270,48L270,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M290,48L290,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M310,48L310,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M330,48L330,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M350,48L350,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M370,48L370,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M390,48L390,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M410,48L410,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M430,48L430,32"
                    fill="none"
                    stroke="#1865f2"
                    stroke-width="3.5"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 3.5;"
                  />
                </svg>
                <div
                  style="position: absolute; left: 0px; top: 0px;"
                >
                  <div
                    style="position: absolute; width: 34px; height: 34px; left: 13px; top: 23px; transform: translateX(0px) translateY(0px) translateZ(0);"
                  >
                    <svg
                      height="34"
                      style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      version="1.1"
                      width="34"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <desc
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      >
                        Created with Raphaël
                      </desc>
                      <defs
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      />
                      <ellipse
                        cx="NaN"
                        cy="NaN"
                        fill="#00a60e"
                        r="NaN"
                        rx="6"
                        ry="6"
                        stroke="#00a60e"
                        stroke-width="1"
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 1;"
                        transform=""
                      />
                    </svg>
                  </div>
                </div>
                <div
                  style="position: absolute; left: 0px; top: 0px; z-index: 2;"
                >
                  <div
                    data-interactive-kind-for-testing="movable-point"
                    style="position: absolute; width: 48px; height: 48px; left: 6px; top: 16px; transform: translateX(0px) translateY(0px) translateZ(0);"
                  >
                    <svg
                      height="48"
                      style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      version="1.1"
                      width="48"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <desc
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      >
                        Created with Raphaël
                      </desc>
                      <defs
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      />
                      <ellipse
                        cx="24"
                        cy="24"
                        fill="#000000"
                        opacity="0"
                        rx="24"
                        ry="24"
                        stroke="#000"
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); opacity: 0; cursor: move;"
                      />
                    </svg>
                  </div>
                </div>
                <span
                  class="graphie-label"
                  data-math-formula="-10"
                  style="position: absolute; padding: 7px; color: rgb(24, 101, 242); left: 30px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-9"
                  style="position: absolute; padding: 7px; color: black; left: 50px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-8"
                  style="position: absolute; padding: 7px; color: black; left: 70px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-7"
                  style="position: absolute; padding: 7px; color: black; left: 90px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-6"
                  style="position: absolute; padding: 7px; color: black; left: 110px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-5"
                  style="position: absolute; padding: 7px; color: black; left: 130px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-4"
                  style="position: absolute; padding: 7px; color: black; left: 150px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-3"
                  style="position: absolute; padding: 7px; color: black; left: 170px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-2"
                  style="position: absolute; padding: 7px; color: black; left: 190px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-1"
                  style="position: absolute; padding: 7px; color: black; left: 210px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="0"
                  style="position: absolute; padding: 7px; color: black; left: 230px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="1"
                  style="position: absolute; padding: 7px; color: black; left: 250px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="2"
                  style="position: absolute; padding: 7px; color: black; left: 270px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="3"
                  style="position: absolute; padding: 7px; color: black; left: 290px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="4"
                  style="position: absolute; padding: 7px; color: black; left: 310px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="5"
                  style="position: absolute; padding: 7px; color: black; left: 330px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="6"
                  style="position: absolute; padding: 7px; color: black; left: 350px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="7"
                  style="position: absolute; padding: 7px; color: black; left: 370px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="8"
                  style="position: absolute; padding: 7px; color: black; left: 390px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="9"
                  style="position: absolute; padding: 7px; color: black; left: 410px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="10"
                  style="position: absolute; padding: 7px; color: rgb(24, 101, 242); left: 430px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`number-line widget snapshots can handle fractions: show fractions 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph perseus-paragraph-centered"
      data-perseus-paragraph-index="0"
    >
      <div
        class="perseus-block-math"
      >
        <div
          class="perseus-block-math-inner"
          style="overflow-x: auto; overflow-y: hidden; padding-top: 10px; padding-bottom: 10px; margin-top: -10px; margin-bottom: -10px;"
        >
          <span
            class="mock-TeX"
          >
            E=2.5
          </span>
        </div>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="1"
    >
      <div
        class="paragraph"
      >
        <strong>
          Move the dot to 
          <span
            style="white-space: nowrap;"
          >
            <span />
            <span
              class="mock-TeX"
            >
              -E
            </span>
            <span />
          </span>
           on the number line.
        </strong>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="2"
    >
      <div
        class="paragraph"
      >
        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="perseus-widget perseus-widget-interactive-number-line"
          >
            <div
              class="graphie-container"
            >
              <div
                aria-hidden="true"
                class="graphie"
                style="position: relative; width: 459.99999999999994px; height: 100px;"
              >
                <svg
                  height="80"
                  style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); z-index: 2;"
                  version="1.1"
                  width="460"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <desc
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  >
                    Created with Raphaël
                  </desc>
                  <defs
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  />
                  <rect
                    fill="#000000"
                    height="80"
                    opacity="0"
                    r="0"
                    rx="0"
                    ry="0"
                    stroke="#000"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); opacity: 0;"
                    width="460"
                    x="0"
                    y="0"
                  />
                </svg>
                <svg
                  height="100"
                  style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  version="1.1"
                  width="459.99999999999994"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <desc
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  >
                    Created with Raphaël
                  </desc>
                  <defs
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  />
                  <path
                    d="M454.44999999999993,45.6C454.79999999999995,43.5,458.6499999999999,40.35,459.69999999999993,40C458.6499999999999,39.65,454.79999999999995,36.5,454.44999999999993,34.4"
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2; stroke-linejoin: round; stroke-linecap: round;"
                    transform=""
                  />
                  <path
                    d="M230,40C230,40,230,40,458.94999999999993,40"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M-3.4499999999999886,45.6C-3.0999999999999885,43.5,0.7500000000000115,40.35,1.8000000000000114,40C0.7500000000000113,39.65,-3.099999999999989,36.5,-3.4499999999999886,34.4"
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2; stroke-linejoin: round; stroke-linecap: round;"
                    transform="rotate(180 1.8000000000000114 40)"
                  />
                  <path
                    d="M230,40C230,40,230,40,1.0500000000000114,40"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M30,48L30,32"
                    fill="none"
                    stroke="#1865f2"
                    stroke-width="3.5"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 3.5;"
                  />
                  <path
                    d="M87.14285714285712,48L87.14285714285712,32"
                    fill="none"
                    stroke="#1865f2"
                    stroke-width="3.5"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 3.5;"
                  />
                  <path
                    d="M144.28571428571428,48L144.28571428571428,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M201.42857142857142,48L201.42857142857142,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M258.57142857142856,48L258.57142857142856,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M315.71428571428567,48L315.71428571428567,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M372.8571428571428,48L372.8571428571428,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M429.99999999999994,48L429.99999999999994,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                </svg>
                <div
                  style="position: absolute; left: 0px; top: 0px;"
                >
                  <div
                    style="position: absolute; width: 34px; height: 34px; left: 13px; top: 23px; transform: translateX(0px) translateY(0px) translateZ(0);"
                  >
                    <svg
                      height="34"
                      style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      version="1.1"
                      width="34"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <desc
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      >
                        Created with Raphaël
                      </desc>
                      <defs
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      />
                      <ellipse
                        cx="NaN"
                        cy="NaN"
                        fill="#00a60e"
                        r="NaN"
                        rx="6"
                        ry="6"
                        stroke="#00a60e"
                        stroke-width="1"
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 1;"
                        transform=""
                      />
                    </svg>
                  </div>
                </div>
                <div
                  style="position: absolute; left: 0px; top: 0px; z-index: 2;"
                >
                  <div
                    data-interactive-kind-for-testing="movable-point"
                    style="position: absolute; width: 48px; height: 48px; left: 6px; top: 16px; transform: translateX(0px) translateY(0px) translateZ(0);"
                  >
                    <svg
                      height="48"
                      style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      version="1.1"
                      width="48"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <desc
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      >
                        Created with Raphaël
                      </desc>
                      <defs
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      />
                      <ellipse
                        cx="24"
                        cy="24"
                        fill="#000000"
                        opacity="0"
                        rx="24"
                        ry="24"
                        stroke="#000"
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); opacity: 0; cursor: move;"
                      />
                    </svg>
                  </div>
                </div>
                <span
                  class="graphie-label"
                  data-math-formula="0"
                  style="position: absolute; padding: 7px; color: rgb(24, 101, 242); left: 30px; top: 46.8px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="\\dfrac{1}{6}"
                  style="position: absolute; padding: 7px; color: rgb(24, 101, 242); left: 87.14285714285712px; top: 46.8px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="\\dfrac{1}{3}"
                  style="position: absolute; padding: 7px; color: black; left: 144.28571428571428px; top: 46.8px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="\\dfrac{1}{2}"
                  style="position: absolute; padding: 7px; color: black; left: 201.42857142857142px; top: 46.8px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="\\dfrac{2}{3}"
                  style="position: absolute; padding: 7px; color: black; left: 258.57142857142856px; top: 46.8px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="\\dfrac{5}{6}"
                  style="position: absolute; padding: 7px; color: black; left: 315.71428571428567px; top: 46.8px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="1"
                  style="position: absolute; padding: 7px; color: black; left: 372.8571428571428px; top: 46.8px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="\\dfrac{7}{6}"
                  style="position: absolute; padding: 7px; color: black; left: 429.99999999999994px; top: 46.8px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`number-line widget snapshots default: first render 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph perseus-paragraph-centered"
      data-perseus-paragraph-index="0"
    >
      <div
        class="perseus-block-math"
      >
        <div
          class="perseus-block-math-inner"
          style="overflow-x: auto; overflow-y: hidden; padding-top: 10px; padding-bottom: 10px; margin-top: -10px; margin-bottom: -10px;"
        >
          <span
            class="mock-TeX"
          >
            E=2.5
          </span>
        </div>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="1"
    >
      <div
        class="paragraph"
      >
        <strong>
          Move the dot to 
          <span
            style="white-space: nowrap;"
          >
            <span />
            <span
              class="mock-TeX"
            >
              -E
            </span>
            <span />
          </span>
           on the number line.
        </strong>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="2"
    >
      <div
        class="paragraph"
      >
        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="perseus-widget perseus-widget-interactive-number-line"
          >
            <div
              class="graphie-container"
            >
              <div
                aria-hidden="true"
                class="graphie"
                style="position: relative; width: 459.99999999999994px; height: 80px;"
              >
                <svg
                  height="80"
                  style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); z-index: 2;"
                  version="1.1"
                  width="460"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <desc
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  >
                    Created with Raphaël
                  </desc>
                  <defs
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  />
                  <rect
                    fill="#000000"
                    height="80"
                    opacity="0"
                    r="0"
                    rx="0"
                    ry="0"
                    stroke="#000"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); opacity: 0;"
                    width="460"
                    x="0"
                    y="0"
                  />
                </svg>
                <svg
                  height="80"
                  style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  version="1.1"
                  width="459.99999999999994"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <desc
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  >
                    Created with Raphaël
                  </desc>
                  <defs
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  />
                  <path
                    d="M454.44999999999993,45.6C454.79999999999995,43.5,458.6499999999999,40.35,459.69999999999993,40C458.6499999999999,39.65,454.79999999999995,36.5,454.44999999999993,34.4"
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2; stroke-linejoin: round; stroke-linecap: round;"
                    transform=""
                  />
                  <path
                    d="M229.99999999999997,40C229.99999999999997,40,229.99999999999997,40,458.94999999999993,40"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M-3.4499999999999886,45.6C-3.0999999999999885,43.5,0.7500000000000115,40.35,1.8000000000000114,40C0.7500000000000113,39.65,-3.099999999999989,36.5,-3.4499999999999886,34.4"
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2; stroke-linejoin: round; stroke-linecap: round;"
                    transform="rotate(180 1.8000000000000114 40)"
                  />
                  <path
                    d="M229.99999999999997,40C229.99999999999997,40,229.99999999999997,40,1.0500000000000114,40"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M29.999999999999982,48L29.999999999999982,32"
                    fill="none"
                    stroke="#1865f2"
                    stroke-width="3.5"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 3.5;"
                  />
                  <path
                    d="M79.99999999999999,48L79.99999999999999,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M129.99999999999997,48L129.99999999999997,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M179.99999999999997,48L179.99999999999997,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M229.99999999999997,48L229.99999999999997,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M280,48L280,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M330,48L330,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M380,48L380,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M430,48L430,32"
                    fill="none"
                    stroke="#1865f2"
                    stroke-width="3.5"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 3.5;"
                  />
                </svg>
                <div
                  style="position: absolute; left: 0px; top: 0px;"
                >
                  <div
                    style="position: absolute; width: 34px; height: 34px; left: 12.999999999999982px; top: 23px; transform: translateX(0px) translateY(0px) translateZ(0);"
                  >
                    <svg
                      height="34"
                      style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      version="1.1"
                      width="34"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <desc
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      >
                        Created with Raphaël
                      </desc>
                      <defs
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      />
                      <ellipse
                        cx="NaN"
                        cy="NaN"
                        fill="#00a60e"
                        r="NaN"
                        rx="6"
                        ry="6"
                        stroke="#00a60e"
                        stroke-width="1"
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 1;"
                        transform=""
                      />
                    </svg>
                  </div>
                </div>
                <div
                  style="position: absolute; left: 0px; top: 0px; z-index: 2;"
                >
                  <div
                    data-interactive-kind-for-testing="movable-point"
                    style="position: absolute; width: 48px; height: 48px; left: 5.999999999999982px; top: 16px; transform: translateX(0px) translateY(0px) translateZ(0);"
                  >
                    <svg
                      height="48"
                      style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      version="1.1"
                      width="48"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <desc
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      >
                        Created with Raphaël
                      </desc>
                      <defs
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      />
                      <ellipse
                        cx="24"
                        cy="24"
                        fill="#000000"
                        opacity="0"
                        rx="24"
                        ry="24"
                        stroke="#000"
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); opacity: 0; cursor: move;"
                      />
                    </svg>
                  </div>
                </div>
                <span
                  class="graphie-label"
                  data-math-formula="-4"
                  style="position: absolute; padding: 7px; color: rgb(24, 101, 242); left: 29.999999999999982px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-3"
                  style="position: absolute; padding: 7px; color: black; left: 79.99999999999999px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-2"
                  style="position: absolute; padding: 7px; color: black; left: 129.99999999999997px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-1"
                  style="position: absolute; padding: 7px; color: black; left: 179.99999999999997px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="0"
                  style="position: absolute; padding: 7px; color: black; left: 229.99999999999997px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="1"
                  style="position: absolute; padding: 7px; color: black; left: 280px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="2"
                  style="position: absolute; padding: 7px; color: black; left: 330px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="3"
                  style="position: absolute; padding: 7px; color: black; left: 380px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="4"
                  style="position: absolute; padding: 7px; color: rgb(24, 101, 242); left: 430px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`number-line widget snapshots endpoints are highlighted when labels are NOT indicated: left endpoint highlighted 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph perseus-paragraph-centered"
      data-perseus-paragraph-index="0"
    >
      <div
        class="perseus-block-math"
      >
        <div
          class="perseus-block-math-inner"
          style="overflow-x: auto; overflow-y: hidden; padding-top: 10px; padding-bottom: 10px; margin-top: -10px; margin-bottom: -10px;"
        >
          <span
            class="mock-TeX"
          >
            E=2.5
          </span>
        </div>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="1"
    >
      <div
        class="paragraph"
      >
        <strong>
          Move the dot to 
          <span
            style="white-space: nowrap;"
          >
            <span />
            <span
              class="mock-TeX"
            >
              -E
            </span>
            <span />
          </span>
           on the number line.
        </strong>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="2"
    >
      <div
        class="paragraph"
      >
        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="perseus-widget perseus-widget-interactive-number-line"
          >
            <div
              class="graphie-container"
            >
              <div
                aria-hidden="true"
                class="graphie"
                style="position: relative; width: 460px; height: 80px;"
              >
                <svg
                  height="80"
                  style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); z-index: 2;"
                  version="1.1"
                  width="460"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <desc
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  >
                    Created with Raphaël
                  </desc>
                  <defs
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  />
                  <rect
                    fill="#000000"
                    height="80"
                    opacity="0"
                    r="0"
                    rx="0"
                    ry="0"
                    stroke="#000"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); opacity: 0;"
                    width="460"
                    x="0"
                    y="0"
                  />
                </svg>
                <svg
                  height="80"
                  style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  version="1.1"
                  width="460"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <desc
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  >
                    Created with Raphaël
                  </desc>
                  <defs
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  />
                  <path
                    d="M454.45,45.6C454.8,43.5,458.65,40.35,459.7,40C458.65,39.65,454.8,36.5,454.45,34.4"
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2; stroke-linejoin: round; stroke-linecap: round;"
                    transform=""
                  />
                  <path
                    d="M230,40C230,40,230,40,458.95,40"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M-3.4499999999999886,45.6C-3.0999999999999885,43.5,0.7500000000000115,40.35,1.8000000000000114,40C0.7500000000000113,39.65,-3.099999999999989,36.5,-3.4499999999999886,34.4"
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2; stroke-linejoin: round; stroke-linecap: round;"
                    transform="rotate(180 1.8000000000000114 40)"
                  />
                  <path
                    d="M230,40C230,40,230,40,1.0500000000000114,40"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M30,48L30,32"
                    fill="none"
                    stroke="#1865f2"
                    stroke-width="3.5"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 3.5;"
                  />
                  <path
                    d="M50,48L50,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M70,48L70,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M90,48L90,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M110,48L110,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M130,48L130,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M150,48L150,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M170,48L170,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M190,48L190,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M210,48L210,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M230,48L230,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M250,48L250,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M270,48L270,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M290,48L290,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M310,48L310,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M330,48L330,32"
                    fill="none"
                    stroke="#1865f2"
                    stroke-width="3.5"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 3.5;"
                  />
                  <path
                    d="M350,48L350,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M370,48L370,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M390,48L390,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M410,48L410,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M430,48L430,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                </svg>
                <div
                  style="position: absolute; left: 0px; top: 0px;"
                >
                  <div
                    style="position: absolute; width: 34px; height: 34px; left: 13px; top: 23px; transform: translateX(0px) translateY(0px) translateZ(0);"
                  >
                    <svg
                      height="34"
                      style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      version="1.1"
                      width="34"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <desc
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      >
                        Created with Raphaël
                      </desc>
                      <defs
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      />
                      <ellipse
                        cx="NaN"
                        cy="NaN"
                        fill="#00a60e"
                        r="NaN"
                        rx="6"
                        ry="6"
                        stroke="#00a60e"
                        stroke-width="1"
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 1;"
                        transform=""
                      />
                    </svg>
                  </div>
                </div>
                <div
                  style="position: absolute; left: 0px; top: 0px; z-index: 2;"
                >
                  <div
                    data-interactive-kind-for-testing="movable-point"
                    style="position: absolute; width: 48px; height: 48px; left: 6px; top: 16px; transform: translateX(0px) translateY(0px) translateZ(0);"
                  >
                    <svg
                      height="48"
                      style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      version="1.1"
                      width="48"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <desc
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      >
                        Created with Raphaël
                      </desc>
                      <defs
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      />
                      <ellipse
                        cx="24"
                        cy="24"
                        fill="#000000"
                        opacity="0"
                        rx="24"
                        ry="24"
                        stroke="#000"
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); opacity: 0; cursor: move;"
                      />
                    </svg>
                  </div>
                </div>
                <span
                  class="graphie-label"
                  data-math-formula="-10"
                  style="position: absolute; padding: 7px; color: rgb(24, 101, 242); left: 30px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-9"
                  style="position: absolute; padding: 7px; color: black; left: 50px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-8"
                  style="position: absolute; padding: 7px; color: black; left: 70px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-7"
                  style="position: absolute; padding: 7px; color: black; left: 90px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-6"
                  style="position: absolute; padding: 7px; color: black; left: 110px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-5"
                  style="position: absolute; padding: 7px; color: black; left: 130px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-4"
                  style="position: absolute; padding: 7px; color: black; left: 150px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-3"
                  style="position: absolute; padding: 7px; color: black; left: 170px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-2"
                  style="position: absolute; padding: 7px; color: black; left: 190px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-1"
                  style="position: absolute; padding: 7px; color: black; left: 210px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="0"
                  style="position: absolute; padding: 7px; color: black; left: 230px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="1"
                  style="position: absolute; padding: 7px; color: black; left: 250px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="2"
                  style="position: absolute; padding: 7px; color: black; left: 270px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="3"
                  style="position: absolute; padding: 7px; color: black; left: 290px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="4"
                  style="position: absolute; padding: 7px; color: black; left: 310px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="5"
                  style="position: absolute; padding: 7px; color: rgb(24, 101, 242); left: 330px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="6"
                  style="position: absolute; padding: 7px; color: black; left: 350px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="7"
                  style="position: absolute; padding: 7px; color: black; left: 370px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="8"
                  style="position: absolute; padding: 7px; color: black; left: 390px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="9"
                  style="position: absolute; padding: 7px; color: black; left: 410px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="10"
                  style="position: absolute; padding: 7px; color: black; left: 430px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`number-line widget snapshots endpoints are highlighted when labels are NOT indicated: right endpoint highlighted 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph perseus-paragraph-centered"
      data-perseus-paragraph-index="0"
    >
      <div
        class="perseus-block-math"
      >
        <div
          class="perseus-block-math-inner"
          style="overflow-x: auto; overflow-y: hidden; padding-top: 10px; padding-bottom: 10px; margin-top: -10px; margin-bottom: -10px;"
        >
          <span
            class="mock-TeX"
          >
            E=2.5
          </span>
        </div>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="1"
    >
      <div
        class="paragraph"
      >
        <strong>
          Move the dot to 
          <span
            style="white-space: nowrap;"
          >
            <span />
            <span
              class="mock-TeX"
            >
              -E
            </span>
            <span />
          </span>
           on the number line.
        </strong>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="2"
    >
      <div
        class="paragraph"
      >
        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="perseus-widget perseus-widget-interactive-number-line"
          >
            <div
              class="graphie-container"
            >
              <div
                aria-hidden="true"
                class="graphie"
                style="position: relative; width: 460px; height: 80px;"
              >
                <svg
                  height="80"
                  style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); z-index: 2;"
                  version="1.1"
                  width="460"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <desc
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  >
                    Created with Raphaël
                  </desc>
                  <defs
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  />
                  <rect
                    fill="#000000"
                    height="80"
                    opacity="0"
                    r="0"
                    rx="0"
                    ry="0"
                    stroke="#000"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); opacity: 0;"
                    width="460"
                    x="0"
                    y="0"
                  />
                </svg>
                <svg
                  height="80"
                  style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  version="1.1"
                  width="460"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <desc
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  >
                    Created with Raphaël
                  </desc>
                  <defs
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  />
                  <path
                    d="M454.45,45.6C454.8,43.5,458.65,40.35,459.7,40C458.65,39.65,454.8,36.5,454.45,34.4"
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2; stroke-linejoin: round; stroke-linecap: round;"
                    transform=""
                  />
                  <path
                    d="M230,40C230,40,230,40,458.95,40"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M-3.4499999999999886,45.6C-3.0999999999999885,43.5,0.7500000000000115,40.35,1.8000000000000114,40C0.7500000000000113,39.65,-3.099999999999989,36.5,-3.4499999999999886,34.4"
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2; stroke-linejoin: round; stroke-linecap: round;"
                    transform="rotate(180 1.8000000000000114 40)"
                  />
                  <path
                    d="M230,40C230,40,230,40,1.0500000000000114,40"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M30,48L30,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M50,48L50,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M70,48L70,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M90,48L90,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M110,48L110,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M130,48L130,32"
                    fill="none"
                    stroke="#1865f2"
                    stroke-width="3.5"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 3.5;"
                  />
                  <path
                    d="M150,48L150,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M170,48L170,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M190,48L190,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M210,48L210,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M230,48L230,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M250,48L250,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M270,48L270,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M290,48L290,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M310,48L310,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M330,48L330,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M350,48L350,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M370,48L370,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M390,48L390,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M410,48L410,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M430,48L430,32"
                    fill="none"
                    stroke="#1865f2"
                    stroke-width="3.5"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 3.5;"
                  />
                </svg>
                <div
                  style="position: absolute; left: 0px; top: 0px;"
                >
                  <div
                    style="position: absolute; width: 34px; height: 34px; left: 13px; top: 23px; transform: translateX(0px) translateY(0px) translateZ(0);"
                  >
                    <svg
                      height="34"
                      style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      version="1.1"
                      width="34"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <desc
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      >
                        Created with Raphaël
                      </desc>
                      <defs
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      />
                      <ellipse
                        cx="NaN"
                        cy="NaN"
                        fill="#00a60e"
                        r="NaN"
                        rx="6"
                        ry="6"
                        stroke="#00a60e"
                        stroke-width="1"
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 1;"
                        transform=""
                      />
                    </svg>
                  </div>
                </div>
                <div
                  style="position: absolute; left: 0px; top: 0px; z-index: 2;"
                >
                  <div
                    data-interactive-kind-for-testing="movable-point"
                    style="position: absolute; width: 48px; height: 48px; left: 6px; top: 16px; transform: translateX(0px) translateY(0px) translateZ(0);"
                  >
                    <svg
                      height="48"
                      style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      version="1.1"
                      width="48"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <desc
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      >
                        Created with Raphaël
                      </desc>
                      <defs
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      />
                      <ellipse
                        cx="24"
                        cy="24"
                        fill="#000000"
                        opacity="0"
                        rx="24"
                        ry="24"
                        stroke="#000"
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); opacity: 0; cursor: move;"
                      />
                    </svg>
                  </div>
                </div>
                <span
                  class="graphie-label"
                  data-math-formula="-10"
                  style="position: absolute; padding: 7px; color: black; left: 30px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-9"
                  style="position: absolute; padding: 7px; color: black; left: 50px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-8"
                  style="position: absolute; padding: 7px; color: black; left: 70px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-7"
                  style="position: absolute; padding: 7px; color: black; left: 90px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-6"
                  style="position: absolute; padding: 7px; color: black; left: 110px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-5"
                  style="position: absolute; padding: 7px; color: rgb(24, 101, 242); left: 130px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-4"
                  style="position: absolute; padding: 7px; color: black; left: 150px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-3"
                  style="position: absolute; padding: 7px; color: black; left: 170px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-2"
                  style="position: absolute; padding: 7px; color: black; left: 190px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-1"
                  style="position: absolute; padding: 7px; color: black; left: 210px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="0"
                  style="position: absolute; padding: 7px; color: black; left: 230px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="1"
                  style="position: absolute; padding: 7px; color: black; left: 250px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="2"
                  style="position: absolute; padding: 7px; color: black; left: 270px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="3"
                  style="position: absolute; padding: 7px; color: black; left: 290px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="4"
                  style="position: absolute; padding: 7px; color: black; left: 310px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="5"
                  style="position: absolute; padding: 7px; color: black; left: 330px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="6"
                  style="position: absolute; padding: 7px; color: black; left: 350px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="7"
                  style="position: absolute; padding: 7px; color: black; left: 370px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="8"
                  style="position: absolute; padding: 7px; color: black; left: 390px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="9"
                  style="position: absolute; padding: 7px; color: black; left: 410px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="10"
                  style="position: absolute; padding: 7px; color: rgb(24, 101, 242); left: 430px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`number-line widget snapshots labels are highlighted when part of the tick step: show highlighted labels 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph perseus-paragraph-centered"
      data-perseus-paragraph-index="0"
    >
      <div
        class="perseus-block-math"
      >
        <div
          class="perseus-block-math-inner"
          style="overflow-x: auto; overflow-y: hidden; padding-top: 10px; padding-bottom: 10px; margin-top: -10px; margin-bottom: -10px;"
        >
          <span
            class="mock-TeX"
          >
            E=2.5
          </span>
        </div>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="1"
    >
      <div
        class="paragraph"
      >
        <strong>
          Move the dot to 
          <span
            style="white-space: nowrap;"
          >
            <span />
            <span
              class="mock-TeX"
            >
              -E
            </span>
            <span />
          </span>
           on the number line.
        </strong>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="2"
    >
      <div
        class="paragraph"
      >
        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="perseus-widget perseus-widget-interactive-number-line"
          >
            <div
              class="graphie-container"
            >
              <div
                aria-hidden="true"
                class="graphie"
                style="position: relative; width: 460px; height: 80px;"
              >
                <svg
                  height="80"
                  style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); z-index: 2;"
                  version="1.1"
                  width="460"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <desc
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  >
                    Created with Raphaël
                  </desc>
                  <defs
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  />
                  <rect
                    fill="#000000"
                    height="80"
                    opacity="0"
                    r="0"
                    rx="0"
                    ry="0"
                    stroke="#000"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); opacity: 0;"
                    width="460"
                    x="0"
                    y="0"
                  />
                </svg>
                <svg
                  height="80"
                  style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  version="1.1"
                  width="460"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <desc
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  >
                    Created with Raphaël
                  </desc>
                  <defs
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  />
                  <path
                    d="M454.45,45.6C454.8,43.5,458.65,40.35,459.7,40C458.65,39.65,454.8,36.5,454.45,34.4"
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2; stroke-linejoin: round; stroke-linecap: round;"
                    transform=""
                  />
                  <path
                    d="M230,40C230,40,230,40,458.95,40"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M-3.4499999999999886,45.6C-3.0999999999999885,43.5,0.7500000000000115,40.35,1.8000000000000114,40C0.7500000000000113,39.65,-3.099999999999989,36.5,-3.4499999999999886,34.4"
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2; stroke-linejoin: round; stroke-linecap: round;"
                    transform="rotate(180 1.8000000000000114 40)"
                  />
                  <path
                    d="M230,40C230,40,230,40,1.0500000000000114,40"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M30,48L30,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M70,48L70,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M110,48L110,32"
                    fill="none"
                    stroke="#1865f2"
                    stroke-width="3.5"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 3.5;"
                  />
                  <path
                    d="M150,48L150,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M190,48L190,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M230,48L230,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M270,48L270,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M310,48L310,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M350,48L350,32"
                    fill="none"
                    stroke="#1865f2"
                    stroke-width="3.5"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 3.5;"
                  />
                  <path
                    d="M390,48L390,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M430,48L430,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                </svg>
                <div
                  style="position: absolute; left: 0px; top: 0px;"
                >
                  <div
                    style="position: absolute; width: 34px; height: 34px; left: 13px; top: 23px; transform: translateX(0px) translateY(0px) translateZ(0);"
                  >
                    <svg
                      height="34"
                      style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      version="1.1"
                      width="34"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <desc
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      >
                        Created with Raphaël
                      </desc>
                      <defs
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      />
                      <ellipse
                        cx="NaN"
                        cy="NaN"
                        fill="#00a60e"
                        r="NaN"
                        rx="6"
                        ry="6"
                        stroke="#00a60e"
                        stroke-width="1"
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 1;"
                        transform=""
                      />
                    </svg>
                  </div>
                </div>
                <div
                  style="position: absolute; left: 0px; top: 0px; z-index: 2;"
                >
                  <div
                    data-interactive-kind-for-testing="movable-point"
                    style="position: absolute; width: 48px; height: 48px; left: 6px; top: 16px; transform: translateX(0px) translateY(0px) translateZ(0);"
                  >
                    <svg
                      height="48"
                      style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      version="1.1"
                      width="48"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <desc
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      >
                        Created with Raphaël
                      </desc>
                      <defs
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      />
                      <ellipse
                        cx="24"
                        cy="24"
                        fill="#000000"
                        opacity="0"
                        rx="24"
                        ry="24"
                        stroke="#000"
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); opacity: 0; cursor: move;"
                      />
                    </svg>
                  </div>
                </div>
                <span
                  class="graphie-label"
                  data-math-formula="-10"
                  style="position: absolute; padding: 7px; color: black; left: 30px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-8"
                  style="position: absolute; padding: 7px; color: black; left: 70px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-6"
                  style="position: absolute; padding: 7px; color: rgb(24, 101, 242); left: 110px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-4"
                  style="position: absolute; padding: 7px; color: black; left: 150px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-2"
                  style="position: absolute; padding: 7px; color: black; left: 190px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="0"
                  style="position: absolute; padding: 7px; color: black; left: 230px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="2"
                  style="position: absolute; padding: 7px; color: black; left: 270px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="4"
                  style="position: absolute; padding: 7px; color: black; left: 310px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="6"
                  style="position: absolute; padding: 7px; color: rgb(24, 101, 242); left: 350px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="8"
                  style="position: absolute; padding: 7px; color: black; left: 390px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="10"
                  style="position: absolute; padding: 7px; color: black; left: 430px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`number-line widget snapshots labels are inserted when NOT part of the tick step: show inserted labels 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph perseus-paragraph-centered"
      data-perseus-paragraph-index="0"
    >
      <div
        class="perseus-block-math"
      >
        <div
          class="perseus-block-math-inner"
          style="overflow-x: auto; overflow-y: hidden; padding-top: 10px; padding-bottom: 10px; margin-top: -10px; margin-bottom: -10px;"
        >
          <span
            class="mock-TeX"
          >
            E=2.5
          </span>
        </div>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="1"
    >
      <div
        class="paragraph"
      >
        <strong>
          Move the dot to 
          <span
            style="white-space: nowrap;"
          >
            <span />
            <span
              class="mock-TeX"
            >
              -E
            </span>
            <span />
          </span>
           on the number line.
        </strong>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="2"
    >
      <div
        class="paragraph"
      >
        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="perseus-widget perseus-widget-interactive-number-line"
          >
            <div
              class="graphie-container"
            >
              <div
                aria-hidden="true"
                class="graphie"
                style="position: relative; width: 460px; height: 80px;"
              >
                <svg
                  height="80"
                  style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); z-index: 2;"
                  version="1.1"
                  width="460"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <desc
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  >
                    Created with Raphaël
                  </desc>
                  <defs
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  />
                  <rect
                    fill="#000000"
                    height="80"
                    opacity="0"
                    r="0"
                    rx="0"
                    ry="0"
                    stroke="#000"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); opacity: 0;"
                    width="460"
                    x="0"
                    y="0"
                  />
                </svg>
                <svg
                  height="80"
                  style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  version="1.1"
                  width="460"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <desc
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  >
                    Created with Raphaël
                  </desc>
                  <defs
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  />
                  <path
                    d="M454.45,45.6C454.8,43.5,458.65,40.35,459.7,40C458.65,39.65,454.8,36.5,454.45,34.4"
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2; stroke-linejoin: round; stroke-linecap: round;"
                    transform=""
                  />
                  <path
                    d="M230,40C230,40,230,40,458.95,40"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M-3.4499999999999886,45.6C-3.0999999999999885,43.5,0.7500000000000115,40.35,1.8000000000000114,40C0.7500000000000113,39.65,-3.099999999999989,36.5,-3.4499999999999886,34.4"
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2; stroke-linejoin: round; stroke-linecap: round;"
                    transform="rotate(180 1.8000000000000114 40)"
                  />
                  <path
                    d="M230,40C230,40,230,40,1.0500000000000114,40"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M30,48L30,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M70,48L70,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M110,48L110,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M130,48L130,32"
                    fill="none"
                    stroke="#1865f2"
                    stroke-width="3.5"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 3.5;"
                  />
                  <path
                    d="M150,48L150,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M190,48L190,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M230,48L230,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M270,48L270,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M310,48L310,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M330,48L330,32"
                    fill="none"
                    stroke="#1865f2"
                    stroke-width="3.5"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 3.5;"
                  />
                  <path
                    d="M350,48L350,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M390,48L390,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M430,48L430,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                </svg>
                <div
                  style="position: absolute; left: 0px; top: 0px;"
                >
                  <div
                    style="position: absolute; width: 34px; height: 34px; left: 13px; top: 23px; transform: translateX(0px) translateY(0px) translateZ(0);"
                  >
                    <svg
                      height="34"
                      style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      version="1.1"
                      width="34"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <desc
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      >
                        Created with Raphaël
                      </desc>
                      <defs
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      />
                      <ellipse
                        cx="NaN"
                        cy="NaN"
                        fill="#00a60e"
                        r="NaN"
                        rx="6"
                        ry="6"
                        stroke="#00a60e"
                        stroke-width="1"
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 1;"
                        transform=""
                      />
                    </svg>
                  </div>
                </div>
                <div
                  style="position: absolute; left: 0px; top: 0px; z-index: 2;"
                >
                  <div
                    data-interactive-kind-for-testing="movable-point"
                    style="position: absolute; width: 48px; height: 48px; left: 6px; top: 16px; transform: translateX(0px) translateY(0px) translateZ(0);"
                  >
                    <svg
                      height="48"
                      style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      version="1.1"
                      width="48"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <desc
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      >
                        Created with Raphaël
                      </desc>
                      <defs
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      />
                      <ellipse
                        cx="24"
                        cy="24"
                        fill="#000000"
                        opacity="0"
                        rx="24"
                        ry="24"
                        stroke="#000"
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); opacity: 0; cursor: move;"
                      />
                    </svg>
                  </div>
                </div>
                <span
                  class="graphie-label"
                  data-math-formula="-10"
                  style="position: absolute; padding: 7px; color: black; left: 30px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-8"
                  style="position: absolute; padding: 7px; color: black; left: 70px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-6"
                  style="position: absolute; padding: 7px; color: black; left: 110px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-5"
                  style="position: absolute; padding: 7px; color: rgb(24, 101, 242); left: 130px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-4"
                  style="position: absolute; padding: 7px; color: black; left: 150px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-2"
                  style="position: absolute; padding: 7px; color: black; left: 190px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="0"
                  style="position: absolute; padding: 7px; color: black; left: 230px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="2"
                  style="position: absolute; padding: 7px; color: black; left: 270px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="4"
                  style="position: absolute; padding: 7px; color: black; left: 310px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="5"
                  style="position: absolute; padding: 7px; color: rgb(24, 101, 242); left: 330px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="6"
                  style="position: absolute; padding: 7px; color: black; left: 350px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="8"
                  style="position: absolute; padding: 7px; color: black; left: 390px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="10"
                  style="position: absolute; padding: 7px; color: black; left: 430px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`number-line widget snapshots mobile: first mobile render 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph perseus-paragraph-centered"
      data-perseus-paragraph-index="0"
    >
      <div
        class="perseus-block-math"
        style="margin-left: -16px; margin-right: -16px;"
      >
        <div
          class="perseus-block-math-inner"
          style="overflow-x: auto; overflow-y: hidden; margin-top: -10px; margin-bottom: -10px; transform: translate3d(0,0,0); padding: 10px 16px 10px 16px;"
        >
          <span
            style="display: block; width: 100%; transform: scale(1, 1); transform-origin: 0 0; opacity: 1;"
          >
            <span
              class="mock-TeX"
            >
              E=2.5
            </span>
          </span>
        </div>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="1"
    >
      <div
        class="paragraph"
      >
        <strong>
          Move the dot to 
          <span
            style="white-space: nowrap;"
          >
            <span />
            <span
              class="mock-TeX"
            >
              -E
            </span>
            <span />
          </span>
           on the number line.
        </strong>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="2"
    >
      <div
        class="paragraph"
      >
        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="perseus-widget perseus-widget-interactive-number-line"
          >
            <div
              class="graphie-container"
            >
              <div
                aria-hidden="true"
                class="graphie"
                style="position: relative; width: 288px; height: 80px;"
              >
                <svg
                  height="80"
                  style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); z-index: 2;"
                  version="1.1"
                  width="288"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <desc
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  >
                    Created with Raphaël
                  </desc>
                  <defs
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  />
                  <rect
                    fill="#000000"
                    height="80"
                    opacity="0"
                    r="0"
                    rx="0"
                    ry="0"
                    stroke="#000"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); opacity: 0;"
                    width="288"
                    x="0"
                    y="0"
                  />
                </svg>
                <svg
                  height="80"
                  style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  version="1.1"
                  width="288"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <desc
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  >
                    Created with Raphaël
                  </desc>
                  <defs
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  />
                  <path
                    d="M281.05,45.6C281.05,45.6,286.3,40,286.3,40C286.3,40,281.05,34.4,281.05,34.4"
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2; stroke-linejoin: round; stroke-linecap: round;"
                    transform=""
                  />
                  <path
                    d="M144,40C144,40,144,40,286.95,40"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M-4.849999999999988,45.6C-4.849999999999988,45.6,0.4000000000000119,40,0.4000000000000119,40C0.4000000000000119,40,-4.849999999999988,34.4,-4.849999999999988,34.4"
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2; stroke-linejoin: round; stroke-linecap: round;"
                    transform="rotate(180 1.0500100000000114 40)"
                  />
                  <path
                    d="M144,40C144,40,144,40,1.0500000000000114,40"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M29.999999999999993,48L29.999999999999993,32"
                    fill="none"
                    stroke="#1865f2"
                    stroke-width="3.5"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); shape-rendering: crispEdges; stroke-width: 3.5;"
                  />
                  <path
                    d="M58.49999999999999,48L58.49999999999999,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); shape-rendering: crispEdges; stroke-width: 2;"
                  />
                  <path
                    d="M86.99999999999999,48L86.99999999999999,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); shape-rendering: crispEdges; stroke-width: 2;"
                  />
                  <path
                    d="M115.49999999999999,48L115.49999999999999,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); shape-rendering: crispEdges; stroke-width: 2;"
                  />
                  <path
                    d="M144,48L144,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); shape-rendering: crispEdges; stroke-width: 2;"
                  />
                  <path
                    d="M172.5,48L172.5,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); shape-rendering: crispEdges; stroke-width: 2;"
                  />
                  <path
                    d="M201,48L201,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); shape-rendering: crispEdges; stroke-width: 2;"
                  />
                  <path
                    d="M229.5,48L229.5,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); shape-rendering: crispEdges; stroke-width: 2;"
                  />
                  <path
                    d="M258,48L258,32"
                    fill="none"
                    stroke="#1865f2"
                    stroke-width="3.5"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); shape-rendering: crispEdges; stroke-width: 3.5;"
                  />
                </svg>
                <div
                  style="position: absolute; left: 0px; top: 0px;"
                >
                  <div
                    style="position: absolute; width: 24px; height: 24px; left: 17.999999999999993px; top: 28px; filter: drop-shadow(0px 0px 5px rgba(0, 0, 0, 0.5)); transform: translateX(0px) translateY(0px) translateZ(0);"
                  >
                    <svg
                      height="24"
                      style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      version="1.1"
                      width="24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <desc
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      >
                        Created with Raphaël
                      </desc>
                      <defs
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      />
                      <ellipse
                        cx="NaN"
                        cy="NaN"
                        fill="#00a60e"
                        r="NaN"
                        rx="7"
                        ry="7"
                        stroke="#ffffff"
                        stroke-width="3"
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 3;"
                        transform=""
                      />
                    </svg>
                  </div>
                </div>
                <div
                  style="position: absolute; left: 0px; top: 0px; z-index: 2;"
                >
                  <div
                    data-interactive-kind-for-testing="movable-point"
                    style="position: absolute; width: 48px; height: 48px; left: 5.999999999999993px; top: 16px; transform: translateX(0px) translateY(0px) translateZ(0);"
                  >
                    <svg
                      height="48"
                      style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      version="1.1"
                      width="48"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <desc
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      >
                        Created with Raphaël
                      </desc>
                      <defs
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      />
                      <ellipse
                        cx="24"
                        cy="24"
                        fill="#000000"
                        opacity="0"
                        rx="24"
                        ry="24"
                        stroke="#000"
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); opacity: 0; cursor: move;"
                      />
                    </svg>
                  </div>
                </div>
                <span
                  class="graphie-label"
                  data-math-formula="-4"
                  style="position: absolute; padding: 7px; color: rgb(24, 101, 242); left: 29.999999999999993px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-3"
                  style="position: absolute; padding: 7px; color: black; left: 58.49999999999999px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-2"
                  style="position: absolute; padding: 7px; color: black; left: 86.99999999999999px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="-1"
                  style="position: absolute; padding: 7px; color: black; left: 115.49999999999999px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="0"
                  style="position: absolute; padding: 7px; color: black; left: 144px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="1"
                  style="position: absolute; padding: 7px; color: black; left: 172.5px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="2"
                  style="position: absolute; padding: 7px; color: black; left: 201px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="3"
                  style="position: absolute; padding: 7px; color: black; left: 229.5px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="4"
                  style="position: absolute; padding: 7px; color: rgb(24, 101, 242); left: 258px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`number-line widget snapshots only endpoints/labels show when "Show label ticks" is off: show label ticks off (endpoints) 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph perseus-paragraph-centered"
      data-perseus-paragraph-index="0"
    >
      <div
        class="perseus-block-math"
      >
        <div
          class="perseus-block-math-inner"
          style="overflow-x: auto; overflow-y: hidden; padding-top: 10px; padding-bottom: 10px; margin-top: -10px; margin-bottom: -10px;"
        >
          <span
            class="mock-TeX"
          >
            E=2.5
          </span>
        </div>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="1"
    >
      <div
        class="paragraph"
      >
        <strong>
          Move the dot to 
          <span
            style="white-space: nowrap;"
          >
            <span />
            <span
              class="mock-TeX"
            >
              -E
            </span>
            <span />
          </span>
           on the number line.
        </strong>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="2"
    >
      <div
        class="paragraph"
      >
        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="perseus-widget perseus-widget-interactive-number-line"
          >
            <div
              class="graphie-container"
            >
              <div
                aria-hidden="true"
                class="graphie"
                style="position: relative; width: 460px; height: 80px;"
              >
                <svg
                  height="80"
                  style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); z-index: 2;"
                  version="1.1"
                  width="460"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <desc
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  >
                    Created with Raphaël
                  </desc>
                  <defs
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  />
                  <rect
                    fill="#000000"
                    height="80"
                    opacity="0"
                    r="0"
                    rx="0"
                    ry="0"
                    stroke="#000"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); opacity: 0;"
                    width="460"
                    x="0"
                    y="0"
                  />
                </svg>
                <svg
                  height="80"
                  style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  version="1.1"
                  width="460"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <desc
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  >
                    Created with Raphaël
                  </desc>
                  <defs
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  />
                  <path
                    d="M454.45,45.6C454.8,43.5,458.65,40.35,459.7,40C458.65,39.65,454.8,36.5,454.45,34.4"
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2; stroke-linejoin: round; stroke-linecap: round;"
                    transform=""
                  />
                  <path
                    d="M230,40C230,40,230,40,458.95,40"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M-3.4499999999999886,45.6C-3.0999999999999885,43.5,0.7500000000000115,40.35,1.8000000000000114,40C0.7500000000000113,39.65,-3.099999999999989,36.5,-3.4499999999999886,34.4"
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2; stroke-linejoin: round; stroke-linecap: round;"
                    transform="rotate(180 1.8000000000000114 40)"
                  />
                  <path
                    d="M230,40C230,40,230,40,1.0500000000000114,40"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M30,48L30,32"
                    fill="none"
                    stroke="#1865f2"
                    stroke-width="3.5"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 3.5;"
                  />
                  <path
                    d="M50,48L50,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M70,48L70,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M90,48L90,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M110,48L110,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M130,48L130,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M150,48L150,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M170,48L170,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M190,48L190,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M210,48L210,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M230,48L230,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M250,48L250,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M270,48L270,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M290,48L290,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M310,48L310,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M330,48L330,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M350,48L350,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M370,48L370,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M390,48L390,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M410,48L410,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M430,48L430,32"
                    fill="none"
                    stroke="#1865f2"
                    stroke-width="3.5"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 3.5;"
                  />
                </svg>
                <div
                  style="position: absolute; left: 0px; top: 0px;"
                >
                  <div
                    style="position: absolute; width: 34px; height: 34px; left: 13px; top: 23px; transform: translateX(0px) translateY(0px) translateZ(0);"
                  >
                    <svg
                      height="34"
                      style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      version="1.1"
                      width="34"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <desc
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      >
                        Created with Raphaël
                      </desc>
                      <defs
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      />
                      <ellipse
                        cx="NaN"
                        cy="NaN"
                        fill="#00a60e"
                        r="NaN"
                        rx="6"
                        ry="6"
                        stroke="#00a60e"
                        stroke-width="1"
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 1;"
                        transform=""
                      />
                    </svg>
                  </div>
                </div>
                <div
                  style="position: absolute; left: 0px; top: 0px; z-index: 2;"
                >
                  <div
                    data-interactive-kind-for-testing="movable-point"
                    style="position: absolute; width: 48px; height: 48px; left: 6px; top: 16px; transform: translateX(0px) translateY(0px) translateZ(0);"
                  >
                    <svg
                      height="48"
                      style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      version="1.1"
                      width="48"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <desc
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      >
                        Created with Raphaël
                      </desc>
                      <defs
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      />
                      <ellipse
                        cx="24"
                        cy="24"
                        fill="#000000"
                        opacity="0"
                        rx="24"
                        ry="24"
                        stroke="#000"
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); opacity: 0; cursor: move;"
                      />
                    </svg>
                  </div>
                </div>
                <span
                  class="graphie-label"
                  data-math-formula="-10"
                  style="position: absolute; padding: 7px; color: rgb(24, 101, 242); left: 30px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="10"
                  style="position: absolute; padding: 7px; color: rgb(24, 101, 242); left: 430px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`number-line widget snapshots only endpoints/labels show when "Show label ticks" is off: show label ticks off (labels) 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph perseus-paragraph-centered"
      data-perseus-paragraph-index="0"
    >
      <div
        class="perseus-block-math"
      >
        <div
          class="perseus-block-math-inner"
          style="overflow-x: auto; overflow-y: hidden; padding-top: 10px; padding-bottom: 10px; margin-top: -10px; margin-bottom: -10px;"
        >
          <span
            class="mock-TeX"
          >
            E=2.5
          </span>
        </div>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="1"
    >
      <div
        class="paragraph"
      >
        <strong>
          Move the dot to 
          <span
            style="white-space: nowrap;"
          >
            <span />
            <span
              class="mock-TeX"
            >
              -E
            </span>
            <span />
          </span>
           on the number line.
        </strong>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="2"
    >
      <div
        class="paragraph"
      >
        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="perseus-widget perseus-widget-interactive-number-line"
          >
            <div
              class="graphie-container"
            >
              <div
                aria-hidden="true"
                class="graphie"
                style="position: relative; width: 460px; height: 80px;"
              >
                <svg
                  height="80"
                  style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); z-index: 2;"
                  version="1.1"
                  width="460"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <desc
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  >
                    Created with Raphaël
                  </desc>
                  <defs
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  />
                  <rect
                    fill="#000000"
                    height="80"
                    opacity="0"
                    r="0"
                    rx="0"
                    ry="0"
                    stroke="#000"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); opacity: 0;"
                    width="460"
                    x="0"
                    y="0"
                  />
                </svg>
                <svg
                  height="80"
                  style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  version="1.1"
                  width="460"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <desc
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  >
                    Created with Raphaël
                  </desc>
                  <defs
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  />
                  <path
                    d="M454.45,45.6C454.8,43.5,458.65,40.35,459.7,40C458.65,39.65,454.8,36.5,454.45,34.4"
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2; stroke-linejoin: round; stroke-linecap: round;"
                    transform=""
                  />
                  <path
                    d="M230,40C230,40,230,40,458.95,40"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M-3.4499999999999886,45.6C-3.0999999999999885,43.5,0.7500000000000115,40.35,1.8000000000000114,40C0.7500000000000113,39.65,-3.099999999999989,36.5,-3.4499999999999886,34.4"
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2; stroke-linejoin: round; stroke-linecap: round;"
                    transform="rotate(180 1.8000000000000114 40)"
                  />
                  <path
                    d="M230,40C230,40,230,40,1.0500000000000114,40"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M30,48L30,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M50,48L50,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M70,48L70,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M90,48L90,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M110,48L110,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M130,48L130,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M150,48L150,32"
                    fill="none"
                    stroke="#1865f2"
                    stroke-width="3.5"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 3.5;"
                  />
                  <path
                    d="M170,48L170,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M190,48L190,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M210,48L210,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M230,48L230,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M250,48L250,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M270,48L270,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M290,48L290,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M310,48L310,32"
                    fill="none"
                    stroke="#1865f2"
                    stroke-width="3.5"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 3.5;"
                  />
                  <path
                    d="M330,48L330,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M350,48L350,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M370,48L370,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M390,48L390,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M410,48L410,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                  <path
                    d="M430,48L430,32"
                    fill="none"
                    stroke="#000000"
                    stroke-width="2"
                    style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 2;"
                  />
                </svg>
                <div
                  style="position: absolute; left: 0px; top: 0px;"
                >
                  <div
                    style="position: absolute; width: 34px; height: 34px; left: 13px; top: 23px; transform: translateX(0px) translateY(0px) translateZ(0);"
                  >
                    <svg
                      height="34"
                      style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      version="1.1"
                      width="34"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <desc
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      >
                        Created with Raphaël
                      </desc>
                      <defs
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      />
                      <ellipse
                        cx="NaN"
                        cy="NaN"
                        fill="#00a60e"
                        r="NaN"
                        rx="6"
                        ry="6"
                        stroke="#00a60e"
                        stroke-width="1"
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); stroke-width: 1;"
                        transform=""
                      />
                    </svg>
                  </div>
                </div>
                <div
                  style="position: absolute; left: 0px; top: 0px; z-index: 2;"
                >
                  <div
                    data-interactive-kind-for-testing="movable-point"
                    style="position: absolute; width: 48px; height: 48px; left: 6px; top: 16px; transform: translateX(0px) translateY(0px) translateZ(0);"
                  >
                    <svg
                      height="48"
                      style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      version="1.1"
                      width="48"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <desc
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      >
                        Created with Raphaël
                      </desc>
                      <defs
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                      />
                      <ellipse
                        cx="24"
                        cy="24"
                        fill="#000000"
                        opacity="0"
                        rx="24"
                        ry="24"
                        stroke="#000"
                        style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); opacity: 0; cursor: move;"
                      />
                    </svg>
                  </div>
                </div>
                <span
                  class="graphie-label"
                  data-math-formula="-4"
                  style="position: absolute; padding: 7px; color: rgb(24, 101, 242); left: 150px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
                <span
                  class="graphie-label"
                  data-math-formula="4"
                  style="position: absolute; padding: 7px; color: rgb(24, 101, 242); left: 310px; top: 61.2px; fill: none;"
                >
                  <span
                    class="tex-holder"
                  />
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

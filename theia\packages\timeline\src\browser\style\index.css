/********************************************************************************
 * Copyright (C) 2020 RedHat and others.
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License v. 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * This Source Code may also be made available under the following Secondary
 * Licenses when the conditions for such availability set forth in the Eclipse
 * Public License v. 2.0 are satisfied: GNU General Public License, version 2
 * with the GNU Classpath Exception which is available at
 * https://www.gnu.org/software/classpath/license.html.
 *
 * SPDX-License-Identifier: EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0
 ********************************************************************************/

.theia-timeline {
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.theia-timeline .timeline-outer-container {
  overflow-y: auto;
  width: 100%;
  flex-grow: 1;
}

.theia-timeline .timeline-item {
  font-size: var(--theia-ui-font-size1);
  display: flex;
  overflow: hidden;
  align-items: center;
  justify-content: space-between;
  height: var(--theia-content-line-height);
  line-height: var(--theia-content-line-height);
  padding: 0px calc(var(--theia-ui-padding) / 2);
}

.theia-timeline .timeline-item:hover {
  cursor: pointer;
}

.theia-timeline .timeline-item .label {
  font-size: var(--theia-ui-font-size0);
  margin-left: var(--theia-ui-padding);
  opacity: 0.7;
}

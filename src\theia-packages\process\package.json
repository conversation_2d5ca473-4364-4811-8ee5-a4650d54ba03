{"name": "@theia/process", "version": "1.63.0", "description": "Theia process support.", "dependencies": {"@theia/core": "1.63.0", "node-pty": "1.1.0-beta27", "string-argv": "^0.1.1", "tslib": "^2.6.2"}, "publishConfig": {"access": "public"}, "theiaExtensions": [{"backend": "lib/common/process-common-module", "frontend": "lib/common/process-common-module"}, {"backend": "lib/node/process-backend-module"}], "keywords": ["theia-extension"], "license": "EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0", "repository": {"type": "git", "url": "https://github.com/eclipse-theia/theia.git"}, "bugs": {"url": "https://github.com/eclipse-theia/theia/issues"}, "homepage": "https://github.com/eclipse-theia/theia", "files": ["lib", "src"], "scripts": {"build": "theiaext build", "clean": "theiaext clean", "compile": "theiaext compile", "lint": "theiaext lint", "test": "theiaext test", "watch": "theiaext watch"}, "devDependencies": {"@theia/ext-scripts": "1.63.0"}, "nyc": {"extends": "../../configs/nyc.json"}, "gitHead": "21358137e41342742707f660b8e222f940a27652"}
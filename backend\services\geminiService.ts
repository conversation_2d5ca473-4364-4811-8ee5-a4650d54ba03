import { GoogleGenAI, GenerateContentResponse } from '@google/genai';

if (!process.env.API_KEY) {
  throw new Error("API key not found. Please set the API_KEY environment variable.");
}

const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });

/**
 * Analyzes mood based on dashboard data using the Gemini API.
 * @returns {Promise<string>} The AI-generated analysis text.
 */
export async function getMoodAnalysis(): Promise<string> {
  // In a real app, this data could be passed as an argument
  const mockDashboardData = `
    - Past Prelesss Gauge: High Stress
    - Wellness Tracker: High activity, low recovery
    - Safety Checklist: Reports 'Anxiety' and a 'Missed Meeting'
  `;

  const prompt = `You are a futuristic AI assistant named '<PERSON><PERSON><PERSON><PERSON>' providing real-time status updates inside a high-tech UI. Based on the following user data, provide a concise, one-sentence analysis of their current wellness and mood state. Keep the tone slightly robotic but helpful. Data: ${mockDashboardData}`;

  const response: GenerateContentResponse = await ai.models.generateContent({
    model: 'gemini-2.5-flash',
    contents: prompt,
  });

  return response.text;
}

import type { NextApiRequest, NextApiResponse } from 'next';
import { executeQuery, initializeDatabase, seedDatabase } from '../../backend/lib/database';

export type Notification = {
  id: number;
  title: string;
  message: string;
  type: string;
  isRead: boolean;
  createdAt: string;
};

type DatabaseNotification = {
  id: number;
  title: string;
  message: string;
  type: string;
  is_read: boolean;
  created_at: string;
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<Notification[] | { error: string }>
) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  try {
    // Initialize database if needed
    await initializeDatabase();
    await seedDatabase();

    // Get notifications from database
    const dbNotifications = await executeQuery<DatabaseNotification>(`
      SELECT * FROM notifications 
      ORDER BY created_at DESC 
      LIMIT 10
    `);

    if (dbNotifications.length > 0) {
      const notifications: Notification[] = dbNotifications.map(notification => ({
        id: notification.id,
        title: notification.title,
        message: notification.message,
        type: notification.type,
        isRead: notification.is_read,
        createdAt: notification.created_at
      }));
      res.status(200).json(notifications);
    } else {
      // Fallback data if no notifications exist
      const fallbackNotifications: Notification[] = [
        {
          id: 1,
          title: "System Update Available",
          message: "A new system update is ready for installation. Please schedule maintenance window.",
          type: "info",
          isRead: false,
          createdAt: new Date().toISOString()
        },
        {
          id: 2,
          title: "Security Alert",
          message: "Unusual login activity detected from new location. Please verify your account.",
          type: "warning",
          isRead: false,
          createdAt: new Date(Date.now() - 3600000).toISOString()
        },
        {
          id: 3,
          title: "Backup Completed",
          message: "Daily backup process completed successfully. All data is secure.",
          type: "success",
          isRead: true,
          createdAt: new Date(Date.now() - 7200000).toISOString()
        }
      ];
      res.status(200).json(fallbackNotifications);
    }
  } catch (error) {
    console.error('Notifications API error:', error);
    
    // Fallback data if database fails
    const fallbackNotifications: Notification[] = [
      {
        id: 1,
        title: "System Update Available",
        message: "A new system update is ready for installation. Please schedule maintenance window.",
        type: "info",
        isRead: false,
        createdAt: new Date().toISOString()
      },
      {
        id: 2,
        title: "Security Alert",
        message: "Unusual login activity detected from new location. Please verify your account.",
        type: "warning",
        isRead: false,
        createdAt: new Date(Date.now() - 3600000).toISOString()
      },
      {
        id: 3,
        title: "Backup Completed",
        message: "Daily backup process completed successfully. All data is secure.",
        type: "success",
        isRead: true,
        createdAt: new Date(Date.now() - 7200000).toISOString()
      }
    ];
    res.status(200).json(fallbackNotifications);
  }
}

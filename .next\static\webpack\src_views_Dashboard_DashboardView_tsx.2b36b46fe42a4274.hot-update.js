"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src_views_Dashboard_DashboardView_tsx",{

/***/ "./src/views/Dashboard/DashboardView.tsx":
/*!***********************************************!*\
  !*** ./src/views/Dashboard/DashboardView.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardView: function() { return /* binding */ DashboardView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_MainMetricChart__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/MainMetricChart */ \"./src/views/Dashboard/components/MainMetricChart.tsx\");\n/* harmony import */ var _components_MoodAlerts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/MoodAlerts */ \"./src/views/Dashboard/components/MoodAlerts.tsx\");\n/* harmony import */ var _components_AullOrgesChart__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/AullOrgesChart */ \"./src/views/Dashboard/components/AullOrgesChart.tsx\");\n/* harmony import */ var _components_PastPrelesssGauge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/PastPrelesssGauge */ \"./src/views/Dashboard/components/PastPrelesssGauge.tsx\");\n/* harmony import */ var _components_WellnessTracker__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/WellnessTracker */ \"./src/views/Dashboard/components/WellnessTracker.tsx\");\n/* harmony import */ var _components_SafetyTents__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/SafetyTents */ \"./src/views/Dashboard/components/SafetyTents.tsx\");\n/* harmony import */ var _components_EcafeAcces__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/EcafeAcces */ \"./src/views/Dashboard/components/EcafeAcces.tsx\");\n/* harmony import */ var _components_WellnessRequests__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./components/WellnessRequests */ \"./src/views/Dashboard/components/WellnessRequests.tsx\");\n/* harmony import */ var _components_FamilyDashboardCTA__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./components/FamilyDashboardCTA */ \"./src/views/Dashboard/components/FamilyDashboardCTA.tsx\");\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../components/Card */ \"./src/components/Card.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst aullOrgesChartData = [\n    {\n        name: \"CPU\",\n        value: 400\n    },\n    {\n        name: \"GPU\",\n        value: 300\n    },\n    {\n        name: \"RAM\",\n        value: 600\n    },\n    {\n        name: \"IO\",\n        value: 280\n    },\n    {\n        name: \"NET\",\n        value: 450\n    }\n];\nconst aullOrgesChartColors = [\n    \"#00FFFF\",\n    \"#00EFFF\",\n    \"#00DFFF\",\n    \"#00CFFF\",\n    \"#00BFFF\"\n];\nconst wellnessTrackerData = [\n    {\n        name: \"A\",\n        uv: 400,\n        pv: 240,\n        amt: 240,\n        line: 300\n    },\n    {\n        name: \"B\",\n        uv: 300,\n        pv: 139,\n        amt: 221,\n        line: 450\n    },\n    {\n        name: \"C\",\n        uv: 200,\n        pv: 980,\n        amt: 229,\n        line: 200\n    },\n    {\n        name: \"D\",\n        uv: 278,\n        pv: 390,\n        amt: 200,\n        line: 500\n    },\n    {\n        name: \"E\",\n        uv: 189,\n        pv: 480,\n        amt: 218,\n        line: 150\n    },\n    {\n        name: \"F\",\n        uv: 239,\n        pv: 380,\n        amt: 250,\n        line: 350\n    },\n    {\n        name: \"G\",\n        uv: 349,\n        pv: 430,\n        amt: 210,\n        line: 420\n    }\n];\nconst WidgetLoader = (param)=>{\n    let { title } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_11__.Card, {\n        title: title,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full text-gray-400 text-sm animate-pulse\",\n            children: \"Loading data...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Dashboard\\\\DashboardView.tsx\",\n            lineNumber: 42,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Dashboard\\\\DashboardView.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, undefined);\n};\n_c = WidgetLoader;\nconst DashboardView = ()=>{\n    _s();\n    const [widgetsData, setWidgetsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            try {\n                setLoading(true);\n                const response = await fetch(\"/api/dashboard-widgets\");\n                const data = await response.json();\n                if (response.ok) {\n                    setWidgetsData(data);\n                } else {\n                    throw new Error(data.error || \"Failed to fetch widget data\");\n                }\n            } catch (error) {\n                console.error(error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchData();\n    }, []);\n    var _widgetsData_safetyItems, _widgetsData_safetyChartData, _widgetsData_contacts, _widgetsData_systemRequests, _widgetsData_systemStats;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card-grid card-grid-4 h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card-standard\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MainMetricChart__WEBPACK_IMPORTED_MODULE_2__.MainMetricChart, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Dashboard\\\\DashboardView.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Dashboard\\\\DashboardView.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card-standard\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MoodAlerts__WEBPACK_IMPORTED_MODULE_3__.MoodAlerts, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Dashboard\\\\DashboardView.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Dashboard\\\\DashboardView.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card-standard\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AullOrgesChart__WEBPACK_IMPORTED_MODULE_4__.AullOrgesChart, {\n                    data: aullOrgesChartData,\n                    colors: aullOrgesChartColors\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Dashboard\\\\DashboardView.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Dashboard\\\\DashboardView.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card-standard\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PastPrelesssGauge__WEBPACK_IMPORTED_MODULE_5__.PastPrelesssGauge, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Dashboard\\\\DashboardView.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Dashboard\\\\DashboardView.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-span-2 card-large\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WellnessTracker__WEBPACK_IMPORTED_MODULE_6__.WellnessTracker, {\n                    data: wellnessTrackerData\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Dashboard\\\\DashboardView.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Dashboard\\\\DashboardView.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card-standard\",\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WidgetLoader, {\n                    title: \"Safety Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Dashboard\\\\DashboardView.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 20\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SafetyTents__WEBPACK_IMPORTED_MODULE_7__.SafetyTents, {\n                    items: (_widgetsData_safetyItems = widgetsData === null || widgetsData === void 0 ? void 0 : widgetsData.safetyItems) !== null && _widgetsData_safetyItems !== void 0 ? _widgetsData_safetyItems : [],\n                    chartData: (_widgetsData_safetyChartData = widgetsData === null || widgetsData === void 0 ? void 0 : widgetsData.safetyChartData) !== null && _widgetsData_safetyChartData !== void 0 ? _widgetsData_safetyChartData : []\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Dashboard\\\\DashboardView.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 61\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Dashboard\\\\DashboardView.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card-standard\",\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WidgetLoader, {\n                    title: \"Direct Access\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Dashboard\\\\DashboardView.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 20\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_EcafeAcces__WEBPACK_IMPORTED_MODULE_8__.EcafeAcces, {\n                    contacts: (_widgetsData_contacts = widgetsData === null || widgetsData === void 0 ? void 0 : widgetsData.contacts) !== null && _widgetsData_contacts !== void 0 ? _widgetsData_contacts : []\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Dashboard\\\\DashboardView.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 61\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Dashboard\\\\DashboardView.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-span-2 card-large\",\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WidgetLoader, {\n                    title: \"System Status & Requests\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Dashboard\\\\DashboardView.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 20\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WellnessRequests__WEBPACK_IMPORTED_MODULE_9__.WellnessRequests, {\n                    requests: (_widgetsData_systemRequests = widgetsData === null || widgetsData === void 0 ? void 0 : widgetsData.systemRequests) !== null && _widgetsData_systemRequests !== void 0 ? _widgetsData_systemRequests : [],\n                    stats: (_widgetsData_systemStats = widgetsData === null || widgetsData === void 0 ? void 0 : widgetsData.systemStats) !== null && _widgetsData_systemStats !== void 0 ? _widgetsData_systemStats : []\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Dashboard\\\\DashboardView.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 72\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Dashboard\\\\DashboardView.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-span-2 card-large\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FamilyDashboardCTA__WEBPACK_IMPORTED_MODULE_10__.FamilyDashboardCTA, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Dashboard\\\\DashboardView.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Dashboard\\\\DashboardView.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Dashboard\\\\DashboardView.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DashboardView, \"5LN2d1yG7Wf9TbmPGW+RFOjw1Ts=\");\n_c1 = DashboardView;\nvar _c, _c1;\n$RefreshReg$(_c, \"WidgetLoader\");\n$RefreshReg$(_c1, \"DashboardView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/Dashboard/DashboardView.tsx\n"));

/***/ })

});
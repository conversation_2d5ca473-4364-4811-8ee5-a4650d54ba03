import React from 'react';
import { Card } from './Card';
import { MoreHorizIcon } from './icons';

const SquareIcon = ({ color }: { color: string }) => (
    <div className={`w-3 h-3 sm:w-4 sm:h-4 rounded-sm`} style={{ backgroundColor: color }}></div>
);

const listItems = [
    { label: 'Cazmhertnley', color: '#3b82f6' }, // blue
    { label: 'Aokseaiy', color: '#ef4444' }, // red
    { label: 'Meents yce', color: '#00FFFF' }, // cyan
    { label: 'Mkeating', color: '#ef4444' }, // red
    { label: 'Aztot bvere', color: '#3b82f6' }, // blue
    { label: 'Proeeing', color: '#00FFFF' }, // cyan
];

export const SafetyTents: React.FC = () => {
    return (
        <Card title="Safety Tents" headerIcon={<MoreHorizIcon className="w-5 h-5"/>}>
            <div className="flex gap-4">
                <div className="flex-grow">
                    <div className="bg-gray-700/50 p-3 rounded-lg flex items-center gap-3">
                        <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-cyan-400/20 flex items-center justify-center animate-pulse">
                            <div className="w-5 h-5 sm:w-6 sm:h-6 rounded-full bg-cyan-400/50 flex items-center justify-center">
                                <div className="w-2 h-2 sm:w-3 sm:h-3 rounded-full bg-cyan-400"></div>
                            </div>
                        </div>
                        <div>
                            <p className="text-white font-semibold text-sm sm:text-base">Wallness</p>
                            <p className="text-xs text-gray-400">ferfal apptent</p>
                        </div>
                    </div>
                    <ul className="space-y-2 mt-3 text-xs sm:text-sm">
                        {listItems.map((item, index) => (
                            <li key={index} className="flex items-center gap-3">
                                <SquareIcon color={item.color} />
                                <span className="text-gray-300">{item.label}</span>
                            </li>
                        ))}
                    </ul>
                </div>
                <div className="w-1/4 flex flex-col justify-end gap-1">
                    { [70, 40, 80, 50, 60, 90].map((h, i) => (
                        <div key={i} className="bg-cyan-500/50 rounded-sm" style={{height: `${h/4}%`}}></div>
                    ))}
                </div>
            </div>
        </Card>
    );
};
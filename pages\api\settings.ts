
import type { NextApiRequest, NextApiResponse } from 'next';
import { generateStructuredData, Type } from '../../backend/lib/gemini';

export type ProfileData = {
    username: string;
    email: string;
    avatar: string;
};

export type NotificationSettings = {
    emailAlerts: boolean;
    pushNotifications: boolean;
    systemUpdates: boolean;
};

export type SecuritySettings = {
    twoFactorAuth: boolean;
    biometricLock: boolean;
};

export type SettingsData = {
    profile: ProfileData;
    notifications: NotificationSettings;
    security: SecuritySettings;
};

const settingsSchema = {
    type: Type.OBJECT,
    properties: {
        profile: {
            type: Type.OBJECT,
            properties: {
                username: { type: Type.STRING, description: "A cool, futuristic username." },
                email: { type: Type.STRING, description: "A futuristic corporate email address." },
                avatar: { type: Type.STRING, description: "A unique, single-word seed for a placeholder avatar URL, e.g., 'cyber' or 'neo'." }
            },
            required: ["username", "email", "avatar"]
        },
        notifications: {
            type: Type.OBJECT,
            properties: {
                emailAlerts: { type: Type.BOOLEAN },
                pushNotifications: { type: Type.BOOLEAN },
                systemUpdates: { type: Type.BOOLEAN }
            },
            required: ["emailAlerts", "pushNotifications", "systemUpdates"]
        },
        security: {
            type: Type.OBJECT,
            properties: {
                twoFactorAuth: { type: Type.BOOLEAN },
                biometricLock: { type: Type.BOOLEAN }
            },
            required: ["twoFactorAuth", "biometricLock"]
        }
    },
    required: ["profile", "notifications", "security"]
};


export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method === 'GET') {
    try {
        const prompt = "Generate plausible data for a user settings page in a high-tech, futuristic dashboard. Provide a user profile, notification toggles, and security settings.";
        const data = await generateStructuredData<Omit<SettingsData, 'profile.avatar'> & { profile: { avatar: string } }>(prompt, settingsSchema);
        
        // Construct the full avatar URL from the seed word
        data.profile.avatar = `https://i.pravatar.cc/150?u=${data.profile.avatar}`;

        return res.status(200).json(data);
    } catch(error) {
        console.error("Failed to fetch settings from AI:", error);
        return res.status(500).json({ error: "Failed to fetch settings" });
    }
  }
  
  if (req.method === 'POST') {
    const newSettings: Partial<SettingsData> = req.body;
    // In a real application with a database, you would validate and save the newSettings here.
    // For this stateless implementation, we simply acknowledge the request was received.
    console.log("Received stateless settings update:", newSettings);
    return res.status(200).json({ message: 'Settings updated successfully' });
  }
  
  res.setHeader('Allow', ['GET', 'POST']);
  res.status(405).end(`Method ${req.method} Not Allowed`);
}


import React, { useState } from 'react';
import { Card } from '../../components/Card';

export type ProfileData = {
    username: string;
    email: string;
    avatar: string;
};

interface ProfileSettingsProps {
    initialProfile: ProfileData;
    onProfileUpdate: () => void;
}

export const ProfileSettings: React.FC<ProfileSettingsProps> = ({ initialProfile, onProfileUpdate }) => {
    const [profile, setProfile] = useState<ProfileData>(initialProfile);
    const [submitting, setSubmitting] = useState(false);
    const [message, setMessage] = useState('');

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setProfile({ ...profile, [e.target.id]: e.target.value });
    }

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setSubmitting(true);
        setMessage('');

        try {
            const response = await fetch('/api/settings', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ profile })
            });
            if (!response.ok) throw new Error('Failed to save changes');
            
            setMessage('Changes saved successfully!');
            onProfileUpdate(); // Notify parent
            setTimeout(() => setMessage(''), 3000);
        } catch (error) {
            console.error(error);
            setMessage('Failed to save. Please try again.');
        } finally {
            setSubmitting(false);
        }
    };

    return (
        <Card title="Profile Information">
        <form onSubmit={handleSubmit} className="space-y-4">
            <div>
            <label htmlFor="username" className="text-xs text-gray-400">Username</label>
            <input type="text" id="username" value={profile.username} onChange={handleChange} className="w-full mt-1 p-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white" />
            </div>
            <div>
            <label htmlFor="email" className="text-xs text-gray-400">Email Address</label>
            <input type="email" id="email" value={profile.email} onChange={handleChange} className="w-full mt-1 p-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white" />
            </div>
            <div>
            <label htmlFor="avatar" className="text-xs text-gray-400">Avatar URL</label>
            <input type="text" id="avatar" value={profile.avatar} onChange={handleChange} className="w-full mt-1 p-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white" />
            </div>
            {message && <p className="text-center text-sm text-cyan-300 pt-2">{message}</p>}
            <button type="submit" disabled={submitting} className="w-full !mt-6 py-2 text-sm font-semibold text-white bg-cyan-500/30 border border-cyan-500/50 rounded-lg hover:bg-cyan-500/50 transition-colors disabled:opacity-50 disabled:cursor-wait">
                {submitting ? 'SAVING...' : 'Save Changes'}
            </button>
        </form>
        </Card>
    );
};

import React, { useState, useRef, useEffect } from 'react';
import Editor from '@monaco-editor/react';
import styles from './Editor.module.css';

interface FileItem {
  name: string;
  content: string;
  language: string;
}

export const CodeEditor: React.FC = () => {
  const [files, setFiles] = useState<FileItem[]>([
    {
      name: 'App.tsx',
      language: 'typescript',
      content: `import React, { useState } from 'react';
import './App.css';

interface User {
  id: number;
  name: string;
  email: string;
}

function App() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/users');
      const data = await response.json();
      setUsers(data);
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>User Management System</h1>
        <button onClick={fetchUsers} disabled={loading}>
          {loading ? 'Loading...' : 'Fetch Users'}
        </button>

        <div className="user-list">
          {users.map(user => (
            <div key={user.id} className="user-card">
              <h3>{user.name}</h3>
              <p>{user.email}</p>
            </div>
          ))}
        </div>
      </header>
    </div>
  );
}

export default App;`
    },
    {
      name: 'App.css',
      language: 'css',
      content: `.App {
  text-align: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.App-header {
  padding: 20px;
  color: white;
}

.App-header h1 {
  font-size: 2.5rem;
  margin-bottom: 2rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

button {
  background: linear-gradient(45deg, #FE6B8B 30%, #FF8E53 90%);
  border: none;
  border-radius: 25px;
  color: white;
  padding: 12px 30px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 3px 5px 2px rgba(255, 105, 135, .3);
}

button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 10px 4px rgba(255, 105, 135, .3);
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.user-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 30px;
  padding: 0 20px;
}

.user-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease;
}

.user-card:hover {
  transform: scale(1.05);
}

.user-card h3 {
  margin: 0 0 10px 0;
  font-size: 1.2rem;
}

.user-card p {
  margin: 0;
  opacity: 0.8;
}`
    }
  ]);

  const [activeFileIndex, setActiveFileIndex] = useState(0);
  const [theme, setTheme] = useState<'vs-dark' | 'light'>('vs-dark');
  const editorRef = useRef<any>(null);

  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = editor;

    // Configure Monaco Editor
    monaco.editor.defineTheme('custom-dark', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '6A9955' },
        { token: 'keyword', foreground: '569CD6' },
        { token: 'string', foreground: 'CE9178' },
        { token: 'number', foreground: 'B5CEA8' },
      ],
      colors: {
        'editor.background': '#1e1e1e',
        'editor.foreground': '#d4d4d4',
        'editorLineNumber.foreground': '#858585',
        'editor.selectionBackground': '#264f78',
        'editor.inactiveSelectionBackground': '#3a3d41'
      }
    });

    monaco.editor.setTheme('custom-dark');
  };

  const handleEditorChange = (value: string | undefined) => {
    if (value !== undefined) {
      const updatedFiles = [...files];
      updatedFiles[activeFileIndex].content = value;
      setFiles(updatedFiles);
    }
  };

  const activeFile = files[activeFileIndex];

  return (
    <div className={styles.editorContainer}>
      <div className={styles.toolbar}>
        <div className={styles.fileList}>
          {files.map((file, index) => (
            <button
              key={file.name}
              className={`${styles.fileTab} ${index === activeFileIndex ? styles.active : ''}`}
              onClick={() => setActiveFileIndex(index)}
            >
              {file.name}
            </button>
          ))}
        </div>
        <div className={styles.controls}>
          <button
            className={styles.themeToggle}
            onClick={() => setTheme(theme === 'vs-dark' ? 'light' : 'vs-dark')}
          >
            {theme === 'vs-dark' ? '☀️' : '🌙'}
          </button>
        </div>
      </div>

      <div className={styles.editorWrapper}>
        <Editor
          height="100%"
          language={activeFile.language}
          value={activeFile.content}
          onChange={handleEditorChange}
          onMount={handleEditorDidMount}
          theme={theme}
          options={{
            fontSize: 14,
            fontFamily: 'Fira Code, Monaco, Consolas, monospace',
            minimap: { enabled: true },
            scrollBeyondLastLine: false,
            automaticLayout: true,
            tabSize: 2,
            insertSpaces: true,
            wordWrap: 'on',
            lineNumbers: 'on',
            renderWhitespace: 'selection',
            bracketPairColorization: { enabled: true },
            guides: {
              bracketPairs: true,
              indentation: true
            }
          }}
        />
      </div>
    </div>
  );
};

/********************************************************************************
 * Copyright (C) 2024 TypeFox and others.
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License v. 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * This Source Code may also be made available under the following Secondary
 * Licenses when the conditions for such availability set forth in the Eclipse
 * Public License v. 2.0 are satisfied: GNU General Public License, version 2
 * with the GNU Classpath Exception which is available at
 * https://www.gnu.org/software/classpath/license.html.
 *
 * SPDX-License-Identifier: EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0
 ********************************************************************************/

.port-table {
  width: 100%;
  margin: calc(var(--theia-ui-padding) * 2);
  table-layout: fixed;
}

.port-table-header {
  text-align: left;
}

.forward-port-button {
  margin-left: 0;
  width: 100%;
}

.button-cell {
  display: flex;
  padding-right: var(--theia-ui-padding);
}

.forwarded-address:hover {
  cursor: pointer;
  text-decoration: underline;
}

.port-edit-input-error {
  outline-color: var(--theia-inputValidation-errorBorder);
}

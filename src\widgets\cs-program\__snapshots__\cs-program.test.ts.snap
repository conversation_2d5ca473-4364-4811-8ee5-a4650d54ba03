// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`cs-program widget should snapshot on mobile: first mobile render 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="widthOverride_1vdzrqd-o_O-container_1v2yujs"
          >
            <iframe
              allowfullscreen=""
              class="perseus-scratchpad"
              sandbox="allow-popups allow-same-origin allow-scripts allow-top-navigation"
              src="http://localhost:8081/computer-programming/program/6293105639817216/embedded?embed=yes&author=no&editor=no&width=688&buttons=no&settings=%7B%7D"
              style="height: 540px; width: 100%;"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`cs-program widget should snapshot: first render 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="widthOverride_1vdzrqd-o_O-container_1v2yujs"
          >
            <iframe
              allowfullscreen=""
              class="perseus-scratchpad"
              sandbox="allow-popups allow-same-origin allow-scripts allow-top-navigation"
              src="http://localhost:8081/computer-programming/program/6293105639817216/embedded?embed=yes&author=no&editor=no&width=688&buttons=no&settings=%7B%7D"
              style="height: 540px; width: 100%;"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

name: Publish Website

on:
  schedule:
    - cron: 0 23 * * *
  workflow_dispatch: {}

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

# Allow only one concurrent deployment, skipping runs queued between the run in-progress and latest queued.
# However, do NOT cancel in-progress runs as we want to allow these production deployments to complete.
concurrency:
  group: 'pages'
  cancel-in-progress: false

jobs:
  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - uses: actions/setup-node@1f8c6b94b26d0feae1e387ca63ccbdc44d27b561 # pin@v2
        with:
          node-version: 20
      - name: Cache node modules
        id: cacheNodeModules
        uses: actions/cache@v4
        with:
          path: '**/node_modules'
          key: ${{ runner.os }}-cacheNodeModules2-${{ hashFiles('**/package-lock.json') }}
          restore-keys: ${{ runner.os }}-cacheNodeModules2-
      - name: execute `npm ci` (1)
        if: ${{ steps.cacheNodeModules.outputs.cache-hit != 'true' }}
        run: npm ci
      - name: Build
        run: npm run build-monaco-editor

      - name: Install website node modules
        working-directory: website
        run: yarn install --frozen-lockfile

      - name: Install most recent version of monaco-editor
        working-directory: website
        run: yarn add monaco-editor

      - name: Build website
        working-directory: website
        run: yarn run build

      - name: Setup Pages
        uses: actions/configure-pages@v5
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          # Upload entire repository
          path: './website/dist'
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4


export type Notification = {
    id: string;
    message: string;
    time: string;
};

export type ServiceRequestData = {
    serviceType: string;
    details: string;
};

// In-memory store for mock data
let notifications: Notification[] = [
    { id: '1', message: "Your transport is arriving in 5 minutes.", time: "1m ago" },
    { id: '2', message: "Dinner reservation at 'The Orbit' confirmed.", time: "1h ago" },
    { id: '3', message: "Package delivered to docking bay 7.", time: "4h ago" },
];

let serviceRequests: ServiceRequestData[] = [];

export const getNotifications = (): Notification[] => {
    return notifications;
};

export const addServiceRequest = (request: ServiceRequestData) => {
    console.log("New service request received:", request);
    serviceRequests.push(request);
    // You could add a new notification here as well
    notifications.unshift({
        id: new Date().toISOString(),
        message: `Your '${request.serviceType}' request has been submitted.`,
        time: 'just now'
    });
};

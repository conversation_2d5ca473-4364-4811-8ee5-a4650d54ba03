"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/views/School/departments/TeacherDashboard.tsx":
/*!***********************************************************!*\
  !*** ./src/views/School/departments/TeacherDashboard.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TeacherDashboard: function() { return /* binding */ TeacherDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../components/Card */ \"./src/components/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BookOpenIcon_ChartBarIcon_ClipboardDocumentListIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BookOpenIcon,ChartBarIcon,ClipboardDocumentListIcon,UserGroupIcon!=!@heroicons/react/24/solid */ \"__barrel_optimize__?names=AcademicCapIcon,BookOpenIcon,ChartBarIcon,ClipboardDocumentListIcon,UserGroupIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/solid/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst TeacherDashboard = (param)=>{\n    let { activeSubSection } = param;\n    _s();\n    const [teacherData, setTeacherData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchTeacherData = async ()=>{\n            try {\n                setLoading(true);\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                const mockData = {\n                    overview: {\n                        totalClasses: 6,\n                        totalStudents: 142,\n                        avgGrade: 87.5,\n                        pendingAssignments: 23,\n                        aiAssistantInteractions: 847,\n                        emotionalWellnessScore: 94,\n                        adaptiveLearningPaths: 156,\n                        hologramLessons: 12\n                    },\n                    classes: [\n                        {\n                            name: \"Quantum Physics VR\",\n                            students: 28,\n                            grade: \"Grade 11\",\n                            color: \"bg-blue-500/20 text-blue-400\",\n                            aiTutor: \"Einstein AI\",\n                            engagement: 96\n                        },\n                        {\n                            name: \"Neural Network Programming\",\n                            students: 24,\n                            grade: \"Grade 12\",\n                            color: \"bg-green-500/20 text-green-400\",\n                            aiTutor: \"Turing AI\",\n                            engagement: 94\n                        },\n                        {\n                            name: \"Bioengineering Lab\",\n                            students: 32,\n                            grade: \"Grade 10\",\n                            color: \"bg-purple-500/20 text-purple-400\",\n                            aiTutor: \"Darwin AI\",\n                            engagement: 92\n                        }\n                    ],\n                    aiInsights: [\n                        {\n                            student: \"Alex Chen\",\n                            insight: \"Shows exceptional pattern recognition in quantum mechanics\",\n                            confidence: 97,\n                            action: \"Advanced track recommended\"\n                        },\n                        {\n                            student: \"Maya Patel\",\n                            insight: \"Emotional stress detected during complex problems\",\n                            confidence: 89,\n                            action: \"Wellness check scheduled\"\n                        },\n                        {\n                            student: \"Jordan Smith\",\n                            insight: \"Learning style: Visual-Kinesthetic hybrid\",\n                            confidence: 94,\n                            action: \"VR modules assigned\"\n                        }\n                    ],\n                    recentAssignments: [\n                        {\n                            title: \"Quantum Entanglement Simulation\",\n                            class: \"Quantum Physics VR\",\n                            dueDate: \"March 18\",\n                            submitted: 24,\n                            total: 28,\n                            aiGraded: 18,\n                            avgTime: \"2.3h\"\n                        },\n                        {\n                            title: \"Neural Network Architecture\",\n                            class: \"Neural Network Programming\",\n                            dueDate: \"March 20\",\n                            submitted: 20,\n                            total: 24,\n                            aiGraded: 15,\n                            avgTime: \"3.1h\"\n                        },\n                        {\n                            title: \"Gene Editing Ethics Debate\",\n                            class: \"Bioengineering Lab\",\n                            dueDate: \"March 25\",\n                            submitted: 15,\n                            total: 32,\n                            aiGraded: 8,\n                            avgTime: \"1.8h\"\n                        }\n                    ]\n                };\n                setTeacherData(mockData);\n            } catch (error) {\n                console.error(\"Failed to fetch teacher data:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchTeacherData();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-cyan-400\",\n                children: \"Loading Teacher Dashboard...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, undefined);\n    }\n    const renderContent = ()=>{\n        switch(activeSubSection){\n            case \"dashboard\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"\\uD83E\\uDD16 AI-Enhanced Teaching Hub\",\n                            className: \"lg:col-span-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-4 p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BookOpenIcon_ChartBarIcon_ClipboardDocumentListIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.AcademicCapIcon, {\n                                                    className: \"w-6 h-6 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: teacherData.overview.hologramLessons\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Hologram Lessons\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BookOpenIcon_ChartBarIcon_ClipboardDocumentListIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.UserGroupIcon, {\n                                                    className: \"w-6 h-6 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: teacherData.overview.aiAssistantInteractions\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"AI Interactions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BookOpenIcon_ChartBarIcon_ClipboardDocumentListIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.ChartBarIcon, {\n                                                    className: \"w-6 h-6 text-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: [\n                                                    teacherData.overview.emotionalWellnessScore,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Wellness Score\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BookOpenIcon_ChartBarIcon_ClipboardDocumentListIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.ClipboardDocumentListIcon, {\n                                                    className: \"w-6 h-6 text-purple-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: teacherData.overview.adaptiveLearningPaths\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Learning Paths\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"\\uD83E\\uDDE0 AI Student Insights\",\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 p-4\",\n                                children: teacherData.aiInsights.map((insight, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg bg-gradient-to-r from-purple-900/20 to-blue-900/20 border border-purple-500/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-white\",\n                                                        children: insight.student\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs bg-green-500/20 text-green-400\",\n                                                        children: [\n                                                            insight.confidence,\n                                                            \"% confidence\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-300 mb-2\",\n                                                children: insight.insight\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-cyan-400\",\n                                                children: [\n                                                    \"\\uD83D\\uDCA1 \",\n                                                    insight.action\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"My Classes\",\n                            className: \"md:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 p-4\",\n                                children: teacherData.classes.map((cls, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg \".concat(cls.color.split(\" \")[0], \" border border-gray-600/30\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold \".concat(cls.color.split(\" \").slice(1).join(\" \")),\n                                                            children: cls.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: [\n                                                                cls.grade,\n                                                                \" • \",\n                                                                cls.students,\n                                                                \" students\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 rounded-lg \".concat(cls.color.split(\" \")[0], \" flex items-center justify-center\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BookOpenIcon_ChartBarIcon_ClipboardDocumentListIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.BookOpenIcon, {\n                                                        className: \"w-4 h-4 \".concat(cls.color.split(\" \").slice(1).join(\" \"))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"Recent Assignments\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 p-4\",\n                                children: teacherData.recentAssignments.map((assignment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg bg-gray-800/40 border border-gray-600/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-white mb-1\",\n                                                children: assignment.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-400 mb-2\",\n                                                children: [\n                                                    assignment.class,\n                                                    \" • Due: \",\n                                                    assignment.dueDate\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 bg-gray-700 rounded-full h-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-cyan-400 h-2 rounded-full\",\n                                                            style: {\n                                                                width: \"\".concat(assignment.submitted / assignment.total * 100, \"%\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: [\n                                                            assignment.submitted,\n                                                            \"/\",\n                                                            assignment.total\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, undefined);\n            case \"classes\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"My Classes\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Class Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Manage your classes, view student rosters, and track attendance.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 11\n                }, undefined);\n            case \"students\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Student Management\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Student Overview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"View student profiles, track progress, and manage communications.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 11\n                }, undefined);\n            case \"curriculum\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Curriculum Planning\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Curriculum Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Plan lessons, manage curriculum standards, and track learning objectives.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 11\n                }, undefined);\n            case \"assignments\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Assignment Center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Assignment Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Create, distribute, and grade assignments across all your classes.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 11\n                }, undefined);\n            case \"grades\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Grade Book\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Grade Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Record grades, generate reports, and track student performance.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 11\n                }, undefined);\n            case \"resources\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Teaching Resources\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Resource Library\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Access teaching materials, lesson plans, and educational resources.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 11\n                }, undefined);\n            case \"settings\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Teacher Settings\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Personal Settings\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Configure your teaching preferences and account settings.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Teacher Dashboard\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Welcome to Teacher Portal\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Select a section from the navigation to get started.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 h-full\",\n        children: renderContent()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n        lineNumber: 255,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TeacherDashboard, \"i1gheQWXW7purOEwZqF0fYPpwyw=\");\n_c = TeacherDashboard;\nvar _c;\n$RefreshReg$(_c, \"TeacherDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/School/departments/TeacherDashboard.tsx\n"));

/***/ })

});
import React from 'react';
import { Card } from '../../../components/Card';

interface StudentDashboardProps {
  activeSubSection: string;
}

export const StudentDashboard: React.FC<StudentDashboardProps> = ({ activeSubSection }) => {
  return (
    <div className="p-4 h-full">
      <Card title="Student Portal">
        <div className="p-6 text-center">
          <h3 className="text-xl font-bold text-white mb-4">Student Dashboard</h3>
          <p className="text-gray-400">Access your courses, assignments, and grades.</p>
        </div>
      </Card>
    </div>
  );
};

import React, { useState, useEffect } from 'react';
import { Card } from '../../../components/Card';
import {
  BookOpenIcon,
  ChartBarIcon,
  CalendarIcon,
  AcademicCapIcon,
  BuildingLibraryIcon,
  UserIcon,
  StarIcon,
  FireIcon
} from '@heroicons/react/24/solid';

interface StudentDashboardProps {
  activeSubSection: string;
}

export const StudentDashboard: React.FC<StudentDashboardProps> = ({ activeSubSection }) => {
  const [studentData, setStudentData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStudentData = async () => {
      try {
        setLoading(true);
        await new Promise(resolve => setTimeout(resolve, 500));

        const mockData = {
          profile: {
            name: 'Alex <PERSON>',
            grade: 'Grade 11',
            gpa: 3.94,
            rank: 12,
            totalStudents: 342,
            aiMentor: 'Socrates AI',
            learningStyle: 'Visual-Kinesthetic',
            emotionalState: 'Motivated',
            neuralPathways: 847
          },
          courses: [
            { name: 'Quantum Physics VR', progress: 87, grade: 'A-', nextClass: 'Hologram Lab', color: 'bg-blue-500/20 text-blue-400', aiTutor: 'Einstein AI' },
            { name: 'Neural Network Design', progress: 92, grade: 'A', nextClass: 'AI Ethics', color: 'bg-green-500/20 text-green-400', aiTutor: 'Turing AI' },
            { name: 'Bioengineering Lab', progress: 78, grade: 'B+', nextClass: 'Gene Editing', color: 'bg-purple-500/20 text-purple-400', aiTutor: 'Darwin AI' },
            { name: 'Space Colonization', progress: 95, grade: 'A+', nextClass: 'Mars Simulation', color: 'bg-pink-500/20 text-pink-400', aiTutor: 'Hawking AI' }
          ],
          achievements: [
            { title: 'Quantum Entanglement Master', icon: '⚛️', rarity: 'Legendary', date: '2024-03-15' },
            { title: 'AI Ethics Champion', icon: '🤖', rarity: 'Epic', date: '2024-03-10' },
            { title: 'Gene Editing Pioneer', icon: '🧬', rarity: 'Rare', date: '2024-03-05' },
            { title: 'Mars Colony Architect', icon: '🚀', rarity: 'Legendary', date: '2024-03-01' }
          ],
          aiInsights: [
            { type: 'Learning Pattern', insight: 'Peak performance during morning VR sessions', confidence: 94 },
            { type: 'Emotional State', insight: 'Stress levels elevated before quantum physics exams', confidence: 87 },
            { type: 'Career Path', insight: 'Strong aptitude for quantum computing research', confidence: 96 }
          ],
          upcomingEvents: [
            { title: 'Holographic Presentation', course: 'Quantum Physics VR', date: 'March 18', type: 'Assessment' },
            { title: 'AI Ethics Debate', course: 'Neural Network Design', date: 'March 20', type: 'Discussion' },
            { title: 'Mars Colony Design Challenge', course: 'Space Colonization', date: 'March 25', type: 'Project' }
          ]
        };

        setStudentData(mockData);
      } catch (error) {
        console.error('Failed to fetch student data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStudentData();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-cyan-400">Loading Student Portal...</div>
      </div>
    );
  }

  const renderContent = () => {
    switch (activeSubSection) {
      case 'dashboard':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Student Profile */}
            <Card title="🎓 My AI-Enhanced Profile" className="lg:col-span-3">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4">
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <AcademicCapIcon className="w-6 h-6 text-white" />
                  </div>
                  <p className="text-2xl font-bold text-white">{studentData.profile.gpa}</p>
                  <p className="text-sm text-gray-400">Neural GPA</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-cyan-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <ChartBarIcon className="w-6 h-6 text-white" />
                  </div>
                  <p className="text-2xl font-bold text-white">#{studentData.profile.rank}</p>
                  <p className="text-sm text-gray-400">Global Rank</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <UserIcon className="w-6 h-6 text-white" />
                  </div>
                  <p className="text-2xl font-bold text-white">{studentData.profile.neuralPathways}</p>
                  <p className="text-sm text-gray-400">Neural Pathways</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <FireIcon className="w-6 h-6 text-white" />
                  </div>
                  <p className="text-2xl font-bold text-white">{studentData.profile.emotionalState}</p>
                  <p className="text-sm text-gray-400">AI Mood Analysis</p>
                </div>
              </div>
              <div className="border-t border-gray-700/50 pt-4 mt-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">AI Mentor:</span>
                  <span className="text-cyan-400">🤖 {studentData.profile.aiMentor}</span>
                  <span className="text-gray-400">Learning Style:</span>
                  <span className="text-purple-400">🧠 {studentData.profile.learningStyle}</span>
                </div>
              </div>
            </Card>

            {/* Future Courses */}
            <Card title="🚀 My Quantum Courses" className="lg:col-span-2">
              <div className="space-y-3 p-4">
                {studentData.courses.map((course: any, index: number) => (
                  <div key={index} className={`p-3 rounded-lg ${course.color.split(' ')[0]} border border-gray-600/30`}>
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1">
                        <h4 className={`font-semibold ${course.color.split(' ').slice(1).join(' ')}`}>{course.name}</h4>
                        <p className="text-sm text-gray-400">Next: {course.nextClass}</p>
                        <p className="text-xs text-cyan-400 mt-1">🤖 {course.aiTutor}</p>
                      </div>
                      <div className="text-right">
                        <span className="text-lg font-bold text-white">{course.grade}</span>
                        <p className="text-xs text-gray-400">{course.progress}%</p>
                      </div>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${course.color.split(' ')[0].replace('bg-', 'bg-gradient-to-r from-').replace('/20', '/60 to-').replace('500', '400')}`}
                        style={{ width: `${course.progress}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Achievement Gallery */}
            <Card title="🏆 Achievement Gallery">
              <div className="space-y-3 p-4">
                {studentData.achievements.map((achievement: any, index: number) => (
                  <div key={index} className={`p-3 rounded-lg border ${
                    achievement.rarity === 'Legendary' ? 'bg-gradient-to-r from-yellow-900/20 to-orange-900/20 border-yellow-500/30' :
                    achievement.rarity === 'Epic' ? 'bg-gradient-to-r from-purple-900/20 to-pink-900/20 border-purple-500/30' :
                    'bg-gradient-to-r from-blue-900/20 to-cyan-900/20 border-blue-500/30'
                  }`}>
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{achievement.icon}</span>
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-white">{achievement.title}</h4>
                        <div className="flex items-center gap-2 mt-1">
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            achievement.rarity === 'Legendary' ? 'bg-yellow-500/20 text-yellow-400' :
                            achievement.rarity === 'Epic' ? 'bg-purple-500/20 text-purple-400' :
                            'bg-blue-500/20 text-blue-400'
                          }`}>
                            {achievement.rarity}
                          </span>
                          <span className="text-xs text-gray-400">{achievement.date}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </div>
        );

      default:
        return (
          <Card title="Student Portal">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Welcome to the Future of Learning</h3>
              <p className="text-gray-400">Select a section from the navigation to explore your AI-enhanced education journey.</p>
            </div>
          </Card>
        );
    }
  };

  return (
    <div className="p-4 h-full">
      {renderContent()}
    </div>
  );
};

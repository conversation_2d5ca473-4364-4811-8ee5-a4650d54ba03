{"name": "@theia/plugin-metrics", "version": "1.63.0", "description": "Theia - <PERSON><PERSON><PERSON>", "dependencies": {"@theia/core": "1.63.0", "@theia/metrics": "1.63.0", "@theia/monaco-editor-core": "1.96.302", "@theia/plugin": "1.63.0", "@theia/plugin-ext": "1.63.0", "tslib": "^2.6.2"}, "publishConfig": {"access": "public"}, "theiaExtensions": [{"frontend": "lib/browser/plugin-metrics-frontend-module", "backend": "lib/node/plugin-metrics-backend-module"}], "keywords": ["theia-extension"], "license": "EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0", "repository": {"type": "git", "url": "https://github.com/eclipse-theia/theia.git"}, "bugs": {"url": "https://github.com/theia-ide/theia/issues"}, "homepage": "https://github.com/theia-ide/theia", "files": ["lib", "src"], "scripts": {"build": "theiaext build", "clean": "theiaext clean", "compile": "theiaext compile", "lint": "theiaext lint", "test": "theiaext test", "watch": "theiaext watch"}, "devDependencies": {"@theia/ext-scripts": "1.63.0"}, "nyc": {"extends": "../../configs/nyc.json"}, "gitHead": "21358137e41342742707f660b8e222f940a27652"}
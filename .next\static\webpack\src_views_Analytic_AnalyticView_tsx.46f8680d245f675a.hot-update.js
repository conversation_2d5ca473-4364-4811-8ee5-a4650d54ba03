"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src_views_Analytic_AnalyticView_tsx",{

/***/ "./src/views/Analytic/AnalyticView.tsx":
/*!*********************************************!*\
  !*** ./src/views/Analytic/AnalyticView.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnalyticView: function() { return /* binding */ AnalyticView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CloudArrowUpIcon,ShieldCheckIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ChartBarIcon,CloudArrowUpIcon,ShieldCheckIcon,UserGroupIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _StatCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./StatCard */ \"./src/views/Analytic/StatCard.tsx\");\n/* harmony import */ var _DataUsageChart__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./DataUsageChart */ \"./src/views/Analytic/DataUsageChart.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst iconMap = {\n    UserGroupIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.UserGroupIcon, {\n        className: \"w-6 h-6\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Analytic\\\\AnalyticView.tsx\",\n        lineNumber: 15,\n        columnNumber: 18\n    }, undefined),\n    CloudArrowUpIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.CloudArrowUpIcon, {\n        className: \"w-6 h-6\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Analytic\\\\AnalyticView.tsx\",\n        lineNumber: 16,\n        columnNumber: 21\n    }, undefined),\n    ShieldCheckIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ShieldCheckIcon, {\n        className: \"w-6 h-6\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Analytic\\\\AnalyticView.tsx\",\n        lineNumber: 17,\n        columnNumber: 20\n    }, undefined),\n    ChartBarIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ChartBarIcon, {\n        className: \"w-6 h-6\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Analytic\\\\AnalyticView.tsx\",\n        lineNumber: 18,\n        columnNumber: 17\n    }, undefined)\n};\nconst AnalyticView = ()=>{\n    _s();\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            try {\n                setLoading(true);\n                const response = await fetch(\"/api/analytics\");\n                if (!response.ok) throw new Error(\"Network response was not ok\");\n                const result = await response.json();\n                setData(result);\n            } catch (error) {\n                console.error(\"Failed to fetch analytics data\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchData();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-full text-cyan-400\",\n            children: \"Loading Analytics...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Analytic\\\\AnalyticView.tsx\",\n            lineNumber: 43,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (!data || !data.stats) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-full text-red-400\",\n            children: \"Failed to load analytics data.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Analytic\\\\AnalyticView.tsx\",\n            lineNumber: 47,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-grow grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 animate-fadeIn\",\n        children: [\n            data.stats.map((stat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatCard__WEBPACK_IMPORTED_MODULE_2__.StatCard, {\n                    icon: iconMap[stat.icon],\n                    label: stat.label,\n                    value: stat.value,\n                    change: stat.change,\n                    changeType: stat.changeType\n                }, stat.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Analytic\\\\AnalyticView.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, undefined)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:col-span-2 lg:col-span-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DataUsageChart__WEBPACK_IMPORTED_MODULE_3__.DataUsageChart, {\n                    data: data.chartData\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Analytic\\\\AnalyticView.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Analytic\\\\AnalyticView.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Analytic\\\\AnalyticView.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, undefined);\n}; // Add this to your globals.css or a style tag if it's not there\n // @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }\n // .animate-fadeIn { animation: fadeIn 0.5s ease-in-out; }\n_s(AnalyticView, \"Zn4cs3026OJRBhxLd0Oqj+bUOXY=\");\n_c = AnalyticView;\nvar _c;\n$RefreshReg$(_c, \"AnalyticView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/Analytic/AnalyticView.tsx\n"));

/***/ })

});
import { NextApiRequest, NextApiResponse } from 'next';

// In-memory data store for School Departments
const departmentsData = {
  departments: [
    {
      id: 1,
      name: "AI & Robotics",
      description: "Artificial Intelligence, Machine Learning, and Advanced Robotics",
      head: "Dr. <PERSON>",
      color: "bg-blue-500/20 text-blue-400",
      icon: "🤖",
      students: 412,
      teachers: 18,
      courses: 24,
      labs: 8,
      innovation: "Neural Networks Lab",
      budget: 2400000,
      achievements: [
        "First high school to develop AGI prototype",
        "Winner of Global AI Ethics Competition 2024",
        "Published 47 research papers in top AI journals"
      ],
      facilities: [
        "Quantum Neural Network Lab",
        "Humanoid Robot Workshop", 
        "AI Ethics Research Center",
        "Machine Learning Supercomputer Cluster"
      ],
      courses_offered: [
        { name: "Introduction to AI", level: "Beginner", students: 89, ai_tutor: "Turing AI" },
        { name: "Neural Network Architecture", level: "Advanced", students: 67, ai_tutor: "LeCun AI" },
        { name: "Robotics Engineering", level: "Intermediate", students: 78, ai_tutor: "Asimov AI" },
        { name: "AI Ethics & Philosophy", level: "Advanced", students: 45, ai_tutor: "Russell AI" }
      ],
      recent_projects: [
        { name: "Conscious AI Development", status: "Active", completion: 67 },
        { name: "Emotional Intelligence Robots", status: "Testing", completion: 89 },
        { name: "AI Teacher Assistant Program", status: "Deployed", completion: 100 }
      ]
    },
    {
      id: 2,
      name: "Quantum Computing",
      description: "Quantum Physics, Quantum Algorithms, and Quantum Information Science",
      head: "Prof. Lisa Wang",
      color: "bg-green-500/20 text-green-400",
      icon: "⚛️",
      students: 298,
      teachers: 16,
      courses: 18,
      labs: 4,
      innovation: "Quantum Simulators",
      budget: 3200000,
      achievements: [
        "Built first educational quantum computer",
        "Solved NP-complete problems in polynomial time",
        "Quantum teleportation successful over 1000km"
      ],
      facilities: [
        "2048-qubit Quantum Computer",
        "Quantum Entanglement Laboratory",
        "Cryogenic Cooling Systems",
        "Quantum Algorithm Development Center"
      ],
      courses_offered: [
        { name: "Quantum Mechanics Fundamentals", level: "Beginner", students: 76, ai_tutor: "Schrödinger AI" },
        { name: "Quantum Algorithm Design", level: "Advanced", students: 54, ai_tutor: "Shor AI" },
        { name: "Quantum Cryptography", level: "Expert", students: 32, ai_tutor: "Bennett AI" },
        { name: "Quantum Error Correction", level: "Expert", students: 28, ai_tutor: "Preskill AI" }
      ],
      recent_projects: [
        { name: "Quantum Internet Protocol", status: "Research", completion: 34 },
        { name: "Room Temperature Quantum Computing", status: "Active", completion: 78 },
        { name: "Quantum Machine Learning", status: "Testing", completion: 92 }
      ]
    },
    {
      id: 3,
      name: "Bioengineering",
      description: "Genetic Engineering, Synthetic Biology, and Biotechnology",
      head: "Dr. Elena Rodriguez",
      color: "bg-purple-500/20 text-purple-400",
      icon: "🧬",
      students: 267,
      teachers: 12,
      courses: 16,
      labs: 6,
      innovation: "Gene Editing Lab",
      budget: 1800000,
      achievements: [
        "Developed CRISPR 3.0 gene editing system",
        "Created synthetic organisms for carbon capture",
        "Reversed aging in laboratory organisms"
      ],
      facilities: [
        "CRISPR Gene Editing Suite",
        "Synthetic Biology Laboratory",
        "Bioreactor Manufacturing Center",
        "DNA Sequencing Facility"
      ],
      courses_offered: [
        { name: "Genetic Engineering Basics", level: "Beginner", students: 67, ai_tutor: "Watson AI" },
        { name: "Synthetic Biology Design", level: "Advanced", students: 45, ai_tutor: "Venter AI" },
        { name: "Bioethics & Regulation", level: "Intermediate", students: 56, ai_tutor: "Bioethics AI" },
        { name: "Longevity Research", level: "Expert", students: 23, ai_tutor: "Sinclair AI" }
      ],
      recent_projects: [
        { name: "Immortality Gene Therapy", status: "Research", completion: 23 },
        { name: "Synthetic Blood Production", status: "Active", completion: 67 },
        { name: "Organ 3D Printing", status: "Testing", completion: 84 }
      ]
    },
    {
      id: 4,
      name: "Space Sciences",
      description: "Space Exploration, Colonization, and Aerospace Technology",
      head: "Dr. James Wilson",
      color: "bg-pink-500/20 text-pink-400",
      icon: "🚀",
      students: 189,
      teachers: 11,
      courses: 14,
      labs: 3,
      innovation: "Mars Simulation",
      budget: 2800000,
      achievements: [
        "Designed Mars colony architecture",
        "Developed fusion propulsion system",
        "Established lunar mining protocols"
      ],
      facilities: [
        "Mars Colony Simulation Chamber",
        "Zero Gravity Training Center",
        "Rocket Propulsion Laboratory",
        "Space Suit Testing Facility"
      ],
      courses_offered: [
        { name: "Space Physics", level: "Intermediate", students: 45, ai_tutor: "Hawking AI" },
        { name: "Mars Colonization Planning", level: "Advanced", students: 34, ai_tutor: "Musk AI" },
        { name: "Rocket Engineering", level: "Expert", students: 28, ai_tutor: "Von Braun AI" },
        { name: "Astrobiology", level: "Advanced", students: 31, ai_tutor: "Sagan AI" }
      ],
      recent_projects: [
        { name: "Interstellar Travel Engine", status: "Research", completion: 12 },
        { name: "Mars Terraforming Plan", status: "Active", completion: 56 },
        { name: "Space Elevator Design", status: "Planning", completion: 34 }
      ]
    },
    {
      id: 5,
      name: "Metaverse Studies",
      description: "Virtual Reality, Augmented Reality, and Digital Worlds",
      head: "Prof. Alex Kim",
      color: "bg-cyan-500/20 text-cyan-400",
      icon: "🌐",
      students: 345,
      teachers: 14,
      courses: 20,
      labs: 7,
      innovation: "Virtual Reality Campus",
      budget: 1600000,
      achievements: [
        "Created first fully immersive virtual school",
        "Developed haptic feedback learning system",
        "Built global metaverse education network"
      ],
      facilities: [
        "360° VR Learning Pods",
        "Haptic Feedback Laboratory",
        "Metaverse Development Studio",
        "Neural Interface Testing Center"
      ],
      courses_offered: [
        { name: "VR World Design", level: "Beginner", students: 89, ai_tutor: "Carmack AI" },
        { name: "Metaverse Economics", level: "Intermediate", students: 67, ai_tutor: "Zuckerberg AI" },
        { name: "Neural Interface Programming", level: "Expert", students: 34, ai_tutor: "Neuralink AI" },
        { name: "Digital Identity & Ethics", level: "Advanced", students: 45, ai_tutor: "Stephenson AI" }
      ],
      recent_projects: [
        { name: "Consciousness Upload System", status: "Research", completion: 8 },
        { name: "Global Virtual Campus", status: "Active", completion: 78 },
        { name: "Metaverse Learning Analytics", status: "Deployed", completion: 100 }
      ]
    },
    {
      id: 6,
      name: "Environmental Technology",
      description: "Climate Science, Renewable Energy, and Sustainability",
      head: "Dr. Ahmed Hassan",
      color: "bg-emerald-500/20 text-emerald-400",
      icon: "🌱",
      students: 223,
      teachers: 9,
      courses: 12,
      labs: 5,
      innovation: "Carbon Neutral Systems",
      budget: 1400000,
      achievements: [
        "Achieved campus carbon neutrality",
        "Developed atmospheric carbon capture system",
        "Created renewable energy microgrid"
      ],
      facilities: [
        "Solar Panel Manufacturing Lab",
        "Wind Turbine Testing Center",
        "Carbon Capture Research Facility",
        "Sustainable Materials Laboratory"
      ],
      courses_offered: [
        { name: "Climate Science", level: "Beginner", students: 56, ai_tutor: "Hansen AI" },
        { name: "Renewable Energy Systems", level: "Intermediate", students: 67, ai_tutor: "Tesla AI" },
        { name: "Carbon Capture Technology", level: "Advanced", students: 34, ai_tutor: "Gates AI" },
        { name: "Sustainable Engineering", level: "Advanced", students: 45, ai_tutor: "Lovins AI" }
      ],
      recent_projects: [
        { name: "Atmospheric CO2 Reversal", status: "Active", completion: 67 },
        { name: "Ocean Plastic Cleanup System", status: "Testing", completion: 89 },
        { name: "Fusion Power Plant Design", status: "Research", completion: 45 }
      ]
    }
  ],
  summary: {
    totalDepartments: 6,
    totalStudents: 1734,
    totalTeachers: 80,
    totalCourses: 104,
    totalLabs: 33,
    totalBudget: 13200000,
    averageStudentsPerDepartment: 289,
    averageTeachersPerDepartment: 13
  }
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Simulate some processing time
    await new Promise(resolve => setTimeout(resolve, 120));
    
    res.status(200).json(departmentsData);
  } catch (error) {
    console.error('API error:', error);
    res.status(500).json({ 
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/ResponsiveWrapper.tsx":
/*!**********************************************!*\
  !*** ./src/components/ResponsiveWrapper.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResponsiveWrapper: function() { return /* binding */ ResponsiveWrapper; },\n/* harmony export */   useResponsiveCSS: function() { return /* binding */ useResponsiveCSS; },\n/* harmony export */   useResponsiveValue: function() { return /* binding */ useResponsiveValue; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\nconst ResponsiveWrapper = (param)=>{\n    let { children, className = \"\", targetWidth = 1920, targetHeight = 1080, minScale = 0.5, maxScale = 1.0, enableAutoScale = true } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [scale, setScale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!enableAutoScale) return;\n        const calculateScale = ()=>{\n            if (!containerRef.current) return;\n            const viewportWidth = window.innerWidth;\n            const viewportHeight = window.innerHeight;\n            // Calculate scale factors for both dimensions\n            const scaleX = viewportWidth / targetWidth;\n            const scaleY = viewportHeight / targetHeight;\n            // Use the smaller scale to ensure content fits in both dimensions\n            const calculatedScale = Math.min(scaleX, scaleY);\n            // Clamp the scale within the specified bounds\n            const finalScale = Math.max(minScale, Math.min(maxScale, calculatedScale));\n            setScale(finalScale);\n            // Apply the scale to the container to fill viewport completely\n            if (containerRef.current) {\n                containerRef.current.style.transform = \"scale(\".concat(finalScale, \")\");\n                containerRef.current.style.transformOrigin = \"top left\";\n                containerRef.current.style.width = \"\".concat(viewportWidth / finalScale, \"px\");\n                containerRef.current.style.height = \"\".concat(viewportHeight / finalScale, \"px\");\n                containerRef.current.style.left = \"0\";\n                containerRef.current.style.top = \"0\";\n                containerRef.current.style.position = \"absolute\";\n            }\n        };\n        // Calculate initial scale\n        calculateScale();\n        // Recalculate on window resize\n        const handleResize = ()=>{\n            calculateScale();\n        };\n        window.addEventListener(\"resize\", handleResize);\n        // Cleanup\n        return ()=>{\n            window.removeEventListener(\"resize\", handleResize);\n        };\n    }, [\n        targetWidth,\n        targetHeight,\n        minScale,\n        maxScale,\n        enableAutoScale\n    ]);\n    const containerStyle = {\n        position: enableAutoScale ? \"absolute\" : \"relative\",\n        width: \"100vw\",\n        height: \"100vh\",\n        transformOrigin: \"top left\",\n        transition: \"transform 0.3s ease-out\",\n        overflow: \"hidden\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"responsive-container \".concat(className),\n        style: containerStyle,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\ResponsiveWrapper.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ResponsiveWrapper, \"Kq0ecimAaoiQT7LBlV3z4oI1F3M=\");\n_c = ResponsiveWrapper;\n// Hook for responsive values\nconst useResponsiveValue = function(baseValue) {\n    let unit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"px\";\n    _s1();\n    const [responsiveValue, setResponsiveValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\".concat(baseValue).concat(unit));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const updateValue = ()=>{\n            const root = document.documentElement;\n            const scaleFactor = parseFloat(getComputedStyle(root).getPropertyValue(\"--scale-factor\")) || 1;\n            const scaledValue = baseValue * scaleFactor;\n            setResponsiveValue(\"\".concat(scaledValue).concat(unit));\n        };\n        updateValue();\n        window.addEventListener(\"resize\", updateValue);\n        return ()=>{\n            window.removeEventListener(\"resize\", updateValue);\n        };\n    }, [\n        baseValue,\n        unit\n    ]);\n    return responsiveValue;\n};\n_s1(useResponsiveValue, \"HUu9XsWyMJPiV9dbbawl8gV5+Zs=\");\n// Hook for responsive CSS variables\nconst useResponsiveCSS = ()=>{\n    _s2();\n    const [cssVars, setCssVars] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        fontSize: \"var(--base-font-size)\",\n        spacing: \"var(--base-spacing)\",\n        borderRadius: \"var(--base-border-radius)\",\n        iconSize: \"var(--base-icon-size)\",\n        buttonHeight: \"var(--base-button-height)\",\n        cardPadding: \"var(--base-card-padding)\",\n        gap: \"var(--base-gap)\"\n    });\n    return cssVars;\n};\n_s2(useResponsiveCSS, \"uw0v8C85FiaegJ3CMhW/HXPInUo=\");\nvar _c;\n$RefreshReg$(_c, \"ResponsiveWrapper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ResponsiveWrapper.tsx\n"));

/***/ })

});
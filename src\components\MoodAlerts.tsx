import React, { useState } from 'react';
import { Card } from './Card';

export const MoodAlerts: React.FC = () => {
  const [analysis, setAnalysis] = useState('FLUNIDARP PUEEL');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleAnalysis = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/mood-analysis', {
        method: 'POST',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch analysis');
      }

      setAnalysis(data.analysis);

    } catch (e) {
      console.error(e);
      setError('Analysis failed. Please try again.');
      setAnalysis('ANALYSIS UNAVAILABLE');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card title="Mood Alerts">
      <div className="flex flex-col justify-between flex-grow h-full">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full border-2 border-cyan-400 flex items-center justify-center text-cyan-400 font-bold text-lg bg-cyan-400/10">
            AR
          </div>
          <div>
            <p className="text-sm text-gray-300">Real-time stsisant</p>
            <p className="text-base sm:text-lg font-semibold text-white">assistant</p>
          </div>
        </div>

        <div className="flex flex-col justify-end flex-grow mt-4">
            <div className="flex items-end justify-between">
              <div>
                <p className="text-xs text-gray-400">AI WELLNESS ANALYSIS</p>
                <p className={`text-xs sm:text-sm font-semibold text-white h-12 sm:h-10 flex items-center ${isLoading ? 'animate-pulse' : ''}`}>
                  {isLoading ? 'Analyzing...' : error || analysis}
                </p>
              </div>
              <div className="relative w-14 h-14 sm:w-16 sm:h-16">
                <svg viewBox="0 0 36 36" className={`w-full h-full ${isLoading ? 'animate-spin' : ''}`} style={{animationDuration: '2s'}}>
                    <path d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" fill="none" stroke="#FF007F" strokeWidth="3"/>
                    <path d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831" fill="none" stroke="#00FFFF" strokeWidth="3" strokeDasharray="60, 100" />
                    <path d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831" fill="none" stroke="#FFFF00" strokeWidth="3" strokeDasharray="30, 100" strokeDashoffset="-25" />
                </svg>
              </div>
            </div>
             <button
                onClick={handleAnalysis}
                disabled={isLoading}
                className="w-full mt-2 px-3 py-1.5 sm:px-4 sm:py-2 text-xs sm:text-sm font-semibold text-white bg-cyan-500/30 border border-cyan-500/50 rounded-lg hover:bg-cyan-500/50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
             >
                {isLoading ? 'ANALYZING...' : 'GET AI MOOD ANALYSIS'}
             </button>
        </div>
      </div>
    </Card>
  );
};
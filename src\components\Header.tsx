import React from 'react';
import {
  AcademicCapIcon,
  BuildingOfficeIcon,
  UserGroupIcon,
  CurrencyDollarIcon,
  MegaphoneIcon,
  HeartIcon,
  UserIcon,
  CogIcon
} from '@heroicons/react/24/solid';

export type SchoolDepartment =
  | 'school'
  | 'administration'
  | 'teacher'
  | 'finance'
  | 'marketing'
  | 'parent'
  | 'student'
  | 'setting';

interface HeaderProps {
  activeDepartment?: SchoolDepartment;
  setActiveDepartment?: (department: SchoolDepartment) => void;
  showSchoolButtons?: boolean;
}

export const Header: React.FC<HeaderProps> = ({
  activeDepartment = 'school',
  setActiveDepartment,
  showSchoolButtons = false
}) => {
  const departments = [
    {
      id: 'school' as SchoolDepartment,
      name: 'School',
      icon: AcademicCapIcon,
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/20',
      borderColor: 'border-blue-400/30'
    },
    {
      id: 'administration' as SchoolDepartment,
      name: 'Administration',
      icon: BuildingOfficeIcon,
      color: 'text-purple-400',
      bgColor: 'bg-purple-500/20',
      borderColor: 'border-purple-400/30'
    },
    {
      id: 'teacher' as SchoolDepartment,
      name: 'Teacher',
      icon: UserGroupIcon,
      color: 'text-green-400',
      bgColor: 'bg-green-500/20',
      borderColor: 'border-green-400/30'
    },
    {
      id: 'finance' as SchoolDepartment,
      name: 'Finance',
      icon: CurrencyDollarIcon,
      color: 'text-yellow-400',
      bgColor: 'bg-yellow-500/20',
      borderColor: 'border-yellow-400/30'
    },
    {
      id: 'marketing' as SchoolDepartment,
      name: 'Marketing',
      icon: MegaphoneIcon,
      color: 'text-pink-400',
      bgColor: 'bg-pink-500/20',
      borderColor: 'border-pink-400/30'
    },
    {
      id: 'parent' as SchoolDepartment,
      name: 'Parent',
      icon: HeartIcon,
      color: 'text-red-400',
      bgColor: 'bg-red-500/20',
      borderColor: 'border-red-400/30'
    },
    {
      id: 'student' as SchoolDepartment,
      name: 'Student',
      icon: UserIcon,
      color: 'text-cyan-400',
      bgColor: 'bg-cyan-500/20',
      borderColor: 'border-cyan-400/30'
    },
    {
      id: 'setting' as SchoolDepartment,
      name: 'Settings',
      icon: CogIcon,
      color: 'text-gray-400',
      bgColor: 'bg-gray-500/20',
      borderColor: 'border-gray-400/30'
    }
  ];

  if (!showSchoolButtons) {
    return (
      <header className="bg-panel-bg border border-gray-700/50 responsive-border-radius responsive-spacing flex items-center justify-center">
        <div className="flex items-center responsive-gap">
          <div className="bg-gradient-to-br from-blue-500 to-purple-600 responsive-border-radius flex items-center justify-center"
               style={{ width: 'var(--base-icon-size)', height: 'var(--base-icon-size)' }}>
            <AcademicCapIcon className="text-white responsive-icon" style={{ width: '80%', height: '80%' }} />
          </div>
          <div>
            <div className="uppercase text-gray-400 tracking-wider responsive-text-xs">
              EDUCATION
            </div>
            <div className="font-bold text-white responsive-text-lg">
              Eyes Shield Dashboard
            </div>
          </div>
        </div>
      </header>
    );
  }

  return (
    <header className="bg-panel-bg border border-gray-700/50 responsive-border-radius responsive-spacing-sm flex items-center justify-between w-full">
      {/* Left Section - School System Title */}
      <div className="flex items-center responsive-gap-sm">
        <div className="bg-gradient-to-br from-blue-500 to-purple-600 responsive-border-radius flex items-center justify-center"
             style={{ width: 'var(--base-icon-size)', height: 'var(--base-icon-size)' }}>
          <AcademicCapIcon className="text-white responsive-icon" style={{ width: '80%', height: '80%' }} />
        </div>
        <div>
          <div className="uppercase text-gray-400 tracking-wider responsive-text-xs">
            EDUCATION
          </div>
          <div className="font-bold text-white responsive-text-lg">
            School Management
          </div>
        </div>
      </div>

      {/* Center Section - Department Navigation */}
      <div className="flex items-center flex-1 justify-center responsive-gap-sm">
        {departments.map((dept) => {
          const IconComponent = dept.icon;
          const isActive = activeDepartment === dept.id;

          return (
            <button
              key={dept.id}
              onClick={() => setActiveDepartment && setActiveDepartment(dept.id)}
              className={`
                relative group flex items-center responsive-border-radius responsive-button
                transition-all duration-300 ease-out
                ${isActive
                  ? `${dept.bgColor} ${dept.borderColor} border shadow-lg scale-105`
                  : 'bg-gray-800/40 border border-gray-600/30 hover:bg-gray-700/60 hover:scale-102'
                }
                backdrop-blur-sm responsive-gap-sm
              `}
              style={{
                height: 'calc(var(--base-button-height) * 0.8)',
                padding: '0 calc(var(--base-spacing) * 0.75)'
              }}
            >
              {/* Glow effect for active */}
              {isActive && (
                <div className={`absolute inset-0 responsive-border-radius ${dept.bgColor} opacity-30 blur-sm`} />
              )}

              {/* Icon */}
              <IconComponent
                className={`
                  transition-all duration-200 relative z-10
                  ${isActive ? dept.color : 'text-gray-400 group-hover:text-white'}
                `}
                style={{
                  width: 'calc(var(--base-icon-size) * 0.7)',
                  height: 'calc(var(--base-icon-size) * 0.7)'
                }}
              />

              {/* Label */}
              <span
                className={`
                  font-medium transition-all duration-200 relative z-10 responsive-text-sm
                  ${isActive ? 'text-white' : 'text-gray-400 group-hover:text-white'}
                `}
              >
                {dept.name}
              </span>

              {/* Active indicator */}
              {isActive && (
                <div
                  className={`absolute -bottom-1 left-1/2 transform -translate-x-1/2 ${dept.color.replace('text-', 'bg-')} rounded-full`}
                  style={{
                    width: 'calc(var(--base-spacing) * 0.5)',
                    height: '2px'
                  }}
                />
              )}
            </button>
          );
        })}
      </div>

      {/* Right Section - Status */}
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span className="text-xs text-gray-400">ONLINE</span>
        </div>
        <div className="text-xs text-gray-400">
          {new Date().toLocaleDateString()}
        </div>
      </div>
    </header>
  );
};

import React, { useState } from 'react';
import { UserCircleIcon, CheckCircleIcon } from '@heroicons/react/24/solid';
import { ShieldCheckIcon } from './icons';
import { Modal } from './Modal';

export const Header: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalContent, setModalContent] = useState({ title: '', body: '' });

  const openModal = (title: string, body: string) => {
    setModalContent({ title, body });
    setIsModalOpen(true);
  };

  return (
    <>
      <header className="bg-panel-bg border border-gray-700/50 rounded-xl p-3 flex items-center justify-between text-sm w-full">
        {/* Left Section */}
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center text-cyan-400">
                <ShieldCheckIcon className="w-6 h-6" />
              </div>
              <div>
                  <div className="text-xs uppercase text-gray-400">SYSTEM</div>
                  <div className="text-lg font-semibold text-white">Eyes Shield</div>
              </div>
          </div>
        </div>

        {/* Center Section */}
        <div className="hidden lg:flex items-center gap-4">
          <span className="bg-gray-600/50 text-gray-300 px-3 py-1 rounded-full text-xs">Feature Alert</span>
          <span className="bg-cyan-500/30 text-cyan-300 px-3 py-1 rounded-full text-xs">1h 22:51.3</span>
          <div className="flex items-baseline gap-2">
            <span className="text-gray-400 text-xs">SYSTEM UPTIME</span>
            <span className="text-white text-2xl font-mono tracking-wider">01 1231:26</span>
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center gap-2">
          <button
            onClick={() => openModal('Server Status', 'Server status: OFFLINE. Remote link has been severed due to anomalous activity.')}
            className="bg-gray-800/80 hover:bg-gray-700/80 transition-colors text-gray-300 px-3 py-1.5 rounded-full text-xs flex items-center gap-2 focus:outline-none focus-visible:ring-2 focus-visible:ring-brand-cyan focus-visible:ring-offset-2 focus-visible:ring-offset-panel-bg"
          >
            <UserCircleIcon className="w-4 h-4 text-cyan-400" />
            SERVER
            <span className="text-red-500">X</span>
          </button>
          <button
            onClick={() => openModal('Connection Status', 'Connection status: CONNECTED. A secure link has been established with the primary node.')}
            className="bg-gray-800/80 hover:bg-gray-700/80 transition-colors text-gray-300 px-3 py-1.5 rounded-full text-xs flex items-center gap-2 focus:outline-none focus-visible:ring-2 focus-visible:ring-brand-cyan focus-visible:ring-offset-2 focus-visible:ring-offset-panel-bg"
          >
            <CheckCircleIcon className="w-4 h-4 text-green-500" />
            CONNECTED
          </button>
        </div>
      </header>
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={modalContent.title}
      >
        <p className="text-gray-300 text-sm">{modalContent.body}</p>
      </Modal>
    </>
  );
};

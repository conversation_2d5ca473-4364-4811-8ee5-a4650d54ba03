import React from 'react';
import {
  AcademicCapIcon,
  BuildingOfficeIcon,
  UserGroupIcon,
  CurrencyDollarIcon,
  MegaphoneIcon,
  HeartIcon,
  UserIcon,
  CogIcon
} from '@heroicons/react/24/solid';

export type SchoolDepartment =
  | 'school'
  | 'administration'
  | 'teacher'
  | 'finance'
  | 'marketing'
  | 'parent'
  | 'student'
  | 'setting';

interface HeaderProps {
  activeDepartment?: SchoolDepartment;
  setActiveDepartment?: (department: SchoolDepartment) => void;
  showSchoolButtons?: boolean;
}

export const Header: React.FC<HeaderProps> = ({
  activeDepartment = 'school',
  setActiveDepartment,
  showSchoolButtons = false
}) => {
  const departments = [
    {
      id: 'school' as SchoolDepartment,
      name: 'School',
      icon: AcademicCapIcon,
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/20',
      borderColor: 'border-blue-400/30'
    },
    {
      id: 'administration' as SchoolDepartment,
      name: 'Administration',
      icon: BuildingOfficeIcon,
      color: 'text-purple-400',
      bgColor: 'bg-purple-500/20',
      borderColor: 'border-purple-400/30'
    },
    {
      id: 'teacher' as SchoolDepartment,
      name: 'Teacher',
      icon: UserGroupIcon,
      color: 'text-green-400',
      bgColor: 'bg-green-500/20',
      borderColor: 'border-green-400/30'
    },
    {
      id: 'finance' as SchoolDepartment,
      name: 'Finance',
      icon: CurrencyDollarIcon,
      color: 'text-yellow-400',
      bgColor: 'bg-yellow-500/20',
      borderColor: 'border-yellow-400/30'
    },
    {
      id: 'marketing' as SchoolDepartment,
      name: 'Marketing',
      icon: MegaphoneIcon,
      color: 'text-pink-400',
      bgColor: 'bg-pink-500/20',
      borderColor: 'border-pink-400/30'
    },
    {
      id: 'parent' as SchoolDepartment,
      name: 'Parent',
      icon: HeartIcon,
      color: 'text-red-400',
      bgColor: 'bg-red-500/20',
      borderColor: 'border-red-400/30'
    },
    {
      id: 'student' as SchoolDepartment,
      name: 'Student',
      icon: UserIcon,
      color: 'text-cyan-400',
      bgColor: 'bg-cyan-500/20',
      borderColor: 'border-cyan-400/30'
    },
    {
      id: 'setting' as SchoolDepartment,
      name: 'Settings',
      icon: CogIcon,
      color: 'text-gray-400',
      bgColor: 'bg-gray-500/20',
      borderColor: 'border-gray-400/30'
    }
  ];

  if (!showSchoolButtons) {
    return (
      <header
        className="bg-panel-bg border border-gray-700/50 rounded-xl flex items-center justify-center"
        style={{
          padding: 'clamp(8px, 0.8vw, 16px)',
          fontSize: 'clamp(10px, 0.8vw, 14px)'
        }}
      >
        <div
          className="flex items-center"
          style={{ gap: 'clamp(8px, 0.6vw, 12px)' }}
        >
          <div
            className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center"
            style={{
              width: 'clamp(24px, 2vw, 32px)',
              height: 'clamp(24px, 2vw, 32px)'
            }}
          >
            <AcademicCapIcon
              className="text-white"
              style={{ width: 'clamp(16px, 1.2vw, 20px)', height: 'clamp(16px, 1.2vw, 20px)' }}
            />
          </div>
          <div>
            <div
              className="uppercase text-gray-400 tracking-wider"
              style={{ fontSize: 'clamp(8px, 0.6vw, 10px)' }}
            >
              EDUCATION
            </div>
            <div
              className="font-bold text-white"
              style={{ fontSize: 'clamp(12px, 1vw, 18px)' }}
            >
              Eyes Shield Dashboard
            </div>
          </div>
        </div>
      </header>
    );
  }

  return (
    <header
      className="bg-panel-bg border border-gray-700/50 rounded-xl flex items-center justify-between w-full"
      style={{
        padding: 'clamp(6px, 0.6vw, 12px)',
        fontSize: 'clamp(10px, 0.8vw, 14px)'
      }}
    >
      {/* Left Section - School System Title */}
      <div
        className="flex items-center"
        style={{ gap: 'clamp(8px, 0.6vw, 12px)' }}
      >
        <div
          className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center"
          style={{
            width: 'clamp(24px, 2vw, 32px)',
            height: 'clamp(24px, 2vw, 32px)'
          }}
        >
          <AcademicCapIcon
            className="text-white"
            style={{ width: 'clamp(16px, 1.2vw, 20px)', height: 'clamp(16px, 1.2vw, 20px)' }}
          />
        </div>
        <div>
          <div
            className="uppercase text-gray-400 tracking-wider"
            style={{ fontSize: 'clamp(8px, 0.6vw, 10px)' }}
          >
            EDUCATION
          </div>
          <div
            className="font-bold text-white"
            style={{ fontSize: 'clamp(12px, 1vw, 18px)' }}
          >
            School Management
          </div>
        </div>
      </div>

      {/* Center Section - Department Navigation */}
      <div
        className="flex items-center flex-1 justify-center"
        style={{ gap: 'clamp(4px, 0.4vw, 8px)' }}
      >
        {departments.map((dept) => {
          const IconComponent = dept.icon;
          const isActive = activeDepartment === dept.id;

          return (
            <button
              key={dept.id}
              onClick={() => setActiveDepartment && setActiveDepartment(dept.id)}
              className={`
                relative group flex items-center rounded-lg
                transition-all duration-300 ease-out
                ${isActive
                  ? `${dept.bgColor} ${dept.borderColor} border shadow-lg scale-105`
                  : 'bg-gray-800/40 border border-gray-600/30 hover:bg-gray-700/60 hover:scale-102'
                }
                backdrop-blur-sm
              `}
              style={{
                gap: 'clamp(4px, 0.4vw, 8px)',
                padding: 'clamp(4px, 0.4vw, 8px) clamp(8px, 0.8vw, 16px)'
              }}
            >
              {/* Glow effect for active */}
              {isActive && (
                <div className={`absolute inset-0 rounded-lg ${dept.bgColor} opacity-30 blur-sm`} />
              )}

              {/* Icon */}
              <IconComponent
                className={`
                  transition-all duration-200 relative z-10
                  ${isActive ? dept.color : 'text-gray-400 group-hover:text-white'}
                `}
                style={{
                  width: 'clamp(12px, 1vw, 16px)',
                  height: 'clamp(12px, 1vw, 16px)'
                }}
              />

              {/* Label */}
              <span
                className={`
                  font-medium transition-all duration-200 relative z-10
                  ${isActive ? 'text-white' : 'text-gray-400 group-hover:text-white'}
                `}
                style={{ fontSize: 'clamp(9px, 0.7vw, 12px)' }}
              >
                {dept.name}
              </span>

              {/* Active indicator */}
              {isActive && (
                <div
                  className={`absolute -bottom-1 left-1/2 transform -translate-x-1/2 ${dept.color.replace('text-', 'bg-')} rounded-full`}
                  style={{
                    width: 'clamp(6px, 0.5vw, 8px)',
                    height: 'clamp(1px, 0.1vw, 2px)'
                  }}
                />
              )}
            </button>
          );
        })}
      </div>

      {/* Right Section - Status */}
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span className="text-xs text-gray-400">ONLINE</span>
        </div>
        <div className="text-xs text-gray-400">
          {new Date().toLocaleDateString()}
        </div>
      </div>
    </header>
  );
};

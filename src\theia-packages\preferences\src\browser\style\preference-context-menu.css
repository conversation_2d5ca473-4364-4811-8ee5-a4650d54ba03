/********************************************************************************
 * Copyright (C) 2020 <PERSON> and others.
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License v. 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * This Source Code may also be made available under the following Secondary
 * Licenses when the conditions for such availability set forth in the Eclipse
 * Public License v. 2.0 are satisfied: GNU General Public License, version 2
 * with the GNU Classpath Exception which is available at
 * https://www.gnu.org/software/classpath/license.html.
 *
 * SPDX-License-Identifier: EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0
 ********************************************************************************/

.theia-settings-container .settings-context-menu-container {
  position: relative;
  padding-left: var(--theia-ui-padding);
}

.theia-settings-container .settings-context-menu-btn {
  cursor: pointer;
}

.theia-settings-container .settings-context-menu {
  position: absolute;
  width: var(--theia-settingsSidebar-width);
  list-style: none;
  padding: var(--theia-ui-padding);
  bottom: calc(100% + 10px);
  left: -10px;
  z-index: 9999;
  background-color: var(--theia-menu-background);
}

.theia-settings-container .settings-context-menu:before {
  content: "";
  position: absolute;
  left: 10px;
  bottom: -10px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid var(--theia-menu-background);
}

.theia-settings-container .settings-context-menu li {
  padding: var(--theia-ui-padding);
}

.theia-settings-container .settings-context-menu li:hover {
  background-color: var(--theia-menu-selectionBackground);
}

.theia-settings-container .settings-context-menu i {
  padding-right: var(--theia-ui-padding);
  width: var(--theia-icon-size);
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.theia-settings-container .pref-context-menu-btn {
  margin-left: 5px;
}

.theia-settings-container .pref-context-menu-btn:hover {
  background-color: rgba(50%, 50%, 50%, 0.1);
}

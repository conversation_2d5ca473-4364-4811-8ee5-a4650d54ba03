"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_views_Setting_SettingView_tsx";
exports.ids = ["src_views_Setting_SettingView_tsx"];
exports.modules = {

/***/ "./src/views/Setting/ProfileSettings.tsx":
/*!***********************************************!*\
  !*** ./src/views/Setting/ProfileSettings.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProfileSettings: () => (/* binding */ ProfileSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/Card */ \"./src/components/Card.tsx\");\n\n\n\nconst ProfileSettings = ({ initialProfile, onProfileUpdate })=>{\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialProfile);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleChange = (e)=>{\n        setProfile({\n            ...profile,\n            [e.target.id]: e.target.value\n        });\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setSubmitting(true);\n        setMessage(\"\");\n        try {\n            const response = await fetch(\"/api/settings\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    profile\n                })\n            });\n            if (!response.ok) throw new Error(\"Failed to save changes\");\n            setMessage(\"Changes saved successfully!\");\n            onProfileUpdate(); // Notify parent\n            setTimeout(()=>setMessage(\"\"), 3000);\n        } catch (error) {\n            console.error(error);\n            setMessage(\"Failed to save. Please try again.\");\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        title: \"Profile Information\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"username\",\n                            className: \"text-xs text-gray-400\",\n                            children: \"Username\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            id: \"username\",\n                            value: profile.username,\n                            onChange: handleChange,\n                            className: \"w-full mt-1 p-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"email\",\n                            className: \"text-xs text-gray-400\",\n                            children: \"Email Address\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"email\",\n                            id: \"email\",\n                            value: profile.email,\n                            onChange: handleChange,\n                            className: \"w-full mt-1 p-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"avatar\",\n                            className: \"text-xs text-gray-400\",\n                            children: \"Avatar URL\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            id: \"avatar\",\n                            value: profile.avatar,\n                            onChange: handleChange,\n                            className: \"w-full mt-1 p-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 13\n                }, undefined),\n                message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-center text-sm text-cyan-300 pt-2\",\n                    children: message\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 25\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    type: \"submit\",\n                    disabled: submitting,\n                    className: \"w-full !mt-6 py-2 text-sm font-semibold text-white bg-cyan-500/30 border border-cyan-500/50 rounded-lg hover:bg-cyan-500/50 transition-colors disabled:opacity-50 disabled:cursor-wait\",\n                    children: submitting ? \"SAVING...\" : \"Save Changes\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n            lineNumber: 51,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n        lineNumber: 50,\n        columnNumber: 9\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/Setting/ProfileSettings.tsx\n");

/***/ }),

/***/ "./src/views/Setting/SettingView.tsx":
/*!*******************************************!*\
  !*** ./src/views/Setting/SettingView.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SettingView: () => (/* binding */ SettingView)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/Card */ \"./src/components/Card.tsx\");\n/* harmony import */ var _ProfileSettings__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ProfileSettings */ \"./src/views/Setting/ProfileSettings.tsx\");\n\n\n\n\nconst Toggle = ({ label, enabled, onChange })=>{\n    const uniqueId = label.replace(/\\s+/g, \"-\").toLowerCase();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-between items-center bg-gray-700/50 p-3 rounded-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                id: `${uniqueId}-label`,\n                className: \"text-white text-sm\",\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                lineNumber: 26,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onChange,\n                role: \"switch\",\n                \"aria-checked\": enabled,\n                \"aria-labelledby\": `${uniqueId}-label`,\n                className: `relative w-12 h-6 rounded-full flex items-center p-1 transition-colors duration-300 focus:outline-none focus-visible:ring-2 focus-visible:ring-brand-cyan focus-visible:ring-offset-2 focus-visible:ring-offset-gray-800 ${enabled ? \"bg-cyan-500\" : \"bg-gray-600\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: `inline-block w-5 h-5 bg-white rounded-full shadow-md transform transition-transform duration-300 ease-in-out ${enabled ? \"translate-x-5\" : \"translate-x-0\"}`\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                lineNumber: 27,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n        lineNumber: 25,\n        columnNumber: 9\n    }, undefined);\n};\nconst SettingView = ()=>{\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchSettings = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/settings\");\n            const data = await response.json();\n            setSettings(data);\n        } catch (error) {\n            console.error(\"Failed to fetch settings\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchSettings();\n    }, []);\n    const handleToggleChange = async (category, key)=>{\n        if (!settings) return;\n        const newCategoryState = {\n            ...settings[category],\n            [key]: !settings[category][key]\n        };\n        const newSettings = {\n            ...settings,\n            [category]: newCategoryState\n        };\n        setSettings(newSettings); // Optimistic update\n        try {\n            await fetch(\"/api/settings\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    [category]: newCategoryState\n                })\n            });\n        } catch (error) {\n            console.error(\"Failed to update settings\", error);\n            fetchSettings(); // Revert on error\n        }\n    };\n    if (loading || !settings) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-full text-cyan-400\",\n            children: \"Loading Settings...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n            lineNumber: 81,\n            columnNumber: 12\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-grow grid grid-cols-1 md:grid-cols-2 gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProfileSettings__WEBPACK_IMPORTED_MODULE_3__.ProfileSettings, {\n                initialProfile: settings.profile,\n                onProfileUpdate: fetchSettings\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        title: \"Notifications\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Toggle, {\n                                    label: \"Email Alerts\",\n                                    enabled: settings.notifications.emailAlerts,\n                                    onChange: ()=>handleToggleChange(\"notifications\", \"emailAlerts\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Toggle, {\n                                    label: \"Push Notifications\",\n                                    enabled: settings.notifications.pushNotifications,\n                                    onChange: ()=>handleToggleChange(\"notifications\", \"pushNotifications\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Toggle, {\n                                    label: \"System Updates\",\n                                    enabled: settings.notifications.systemUpdates,\n                                    onChange: ()=>handleToggleChange(\"notifications\", \"systemUpdates\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        title: \"Security\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Toggle, {\n                                    label: \"Two-Factor Auth\",\n                                    enabled: settings.security.twoFactorAuth,\n                                    onChange: ()=>handleToggleChange(\"security\", \"twoFactorAuth\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 18\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Toggle, {\n                                    label: \"Biometric Lock\",\n                                    enabled: settings.security.biometricLock,\n                                    onChange: ()=>handleToggleChange(\"security\", \"biometricLock\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 18\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 10\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/Setting/SettingView.tsx\n");

/***/ })

};
;
// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Interactive Graph A none-type graph renders predictably: first render 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="default_xu2jcg mafs-graph-container"
          >
            <div
              class="default_xu2jcg-o_O-inlineStyles_1t4cnlh mafs-graph"
              tabindex="0"
            >
              <div
                class="default_xu2jcg-o_O-inlineStyles_16155wj"
              >
                <span
                  aria-hidden="true"
                  style="position: absolute; left: 400px; top: 200px; font-size: 14px; transform: translate(7px, -50%);"
                >
                  <span
                    class="mock-TeX"
                  >
                    \\text{$x$}
                  </span>
                </span>
                <span
                  aria-hidden="true"
                  style="position: absolute; left: 200px; top: -28px; font-size: 14px; transform: translate(-50%, 0px);"
                >
                  <span
                    class="mock-TeX"
                  >
                    \\text{$y$}
                  </span>
                </span>
                <div
                  aria-hidden="true"
                  class="default_xu2jcg"
                >
                  <div
                    class="MafsView"
                    style="width: 400px; height: 400px;"
                    tabindex="-1"
                  >
                    <svg
                      height="400"
                      preserveAspectRatio="xMidYMin"
                      style="width: 400px; --mafs-view-transform: matrix(20, 0, 0, -20, 0, 0); --mafs-user-transform: translate(0, 0);"
                      viewBox="-200 -200 400 400"
                      width="400"
                    >
                      <defs>
                        <filter
                          height="100%"
                          id="background"
                          width="110%"
                          x="-5%"
                          y="0%"
                        >
                          <feflood
                            flood-color="#FFF"
                            flood-opacity="0.64"
                          />
                          <fecomposite
                            in="SourceGraphic"
                            operator="over"
                          />
                        </filter>
                      </defs>
                      <svg
                        height="400"
                        preserveAspectRatio="xMidYMin"
                        viewBox="-200 -200 400 400"
                        width="400"
                        x="-200"
                        y="-200"
                      >
                        <g
                          fill="none"
                        >
                          <pattern
                            height="20"
                            id="cartesian-0"
                            patternUnits="userSpaceOnUse"
                            width="20"
                            x="0"
                            y="0"
                          >
                            <pattern
                              height="20"
                              id="cartesian-0-subdivision"
                              patternUnits="userSpaceOnUse"
                              width="20"
                            >
                              <g
                                stroke="var(--grid-line-subdivision-color)"
                              />
                            </pattern>
                            <rect
                              fill="url(#cartesian-0-subdivision)"
                              height="20"
                              width="20"
                            />
                            <g
                              stroke="var(--mafs-line-color)"
                            >
                              <line
                                x1="0"
                                x2="20"
                                y1="0"
                                y2="0"
                              />
                              <line
                                x1="0"
                                x2="20"
                                y1="20"
                                y2="20"
                              />
                              <line
                                x1="0"
                                x2="0"
                                y1="0"
                                y2="20"
                              />
                              <line
                                x1="20"
                                x2="20"
                                y1="0"
                                y2="20"
                              />
                            </g>
                          </pattern>
                          <rect
                            fill="url(#cartesian-0)"
                            height="640"
                            width="640"
                            x="-320"
                            y="-320"
                          />
                          <g
                            stroke="var(--mafs-origin-color)"
                            stroke-width="var(--mafs-axis-stroke-width)"
                          >
                            <line
                              x1="-320"
                              x2="320"
                              y1="0"
                              y2="0"
                            />
                            <line
                              x1="0"
                              x2="0"
                              y1="320"
                              y2="-320"
                            />
                          </g>
                          <g
                            class="mafs-shadow"
                          />
                        </g>
                      </svg>
                      <g
                        class="axis-ticks"
                        role="presentation"
                      >
                        <g
                          class="y-axis-ticks"
                        >
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-5"
                              x2="5"
                              y1="-20"
                              y2="-20"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="end"
                              x="-15.400000000000002"
                              y="-16.5"
                            >
                              1
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-5"
                              x2="5"
                              y1="-40"
                              y2="-40"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="end"
                              x="-15.400000000000002"
                              y="-36.5"
                            >
                              2
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-5"
                              x2="5"
                              y1="-60"
                              y2="-60"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="end"
                              x="-15.400000000000002"
                              y="-56.5"
                            >
                              3
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-5"
                              x2="5"
                              y1="-80"
                              y2="-80"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="end"
                              x="-15.400000000000002"
                              y="-76.5"
                            >
                              4
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-5"
                              x2="5"
                              y1="-100"
                              y2="-100"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="end"
                              x="-15.400000000000002"
                              y="-96.5"
                            >
                              5
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-5"
                              x2="5"
                              y1="-120"
                              y2="-120"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="end"
                              x="-15.400000000000002"
                              y="-116.5"
                            >
                              6
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-5"
                              x2="5"
                              y1="-140"
                              y2="-140"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="end"
                              x="-15.400000000000002"
                              y="-136.5"
                            >
                              7
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-5"
                              x2="5"
                              y1="-160"
                              y2="-160"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="end"
                              x="-15.400000000000002"
                              y="-156.5"
                            >
                              8
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-5"
                              x2="5"
                              y1="-180"
                              y2="-180"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="end"
                              x="-15.400000000000002"
                              y="-176.5"
                            >
                              9
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-5"
                              x2="5"
                              y1="20"
                              y2="20"
                            />
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-5"
                              x2="5"
                              y1="40"
                              y2="40"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="end"
                              x="-15.400000000000002"
                              y="43.5"
                            >
                              -2
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-5"
                              x2="5"
                              y1="60"
                              y2="60"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="end"
                              x="-15.400000000000002"
                              y="63.5"
                            >
                              -3
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-5"
                              x2="5"
                              y1="80"
                              y2="80"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="end"
                              x="-15.400000000000002"
                              y="83.5"
                            >
                              -4
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-5"
                              x2="5"
                              y1="100"
                              y2="100"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="end"
                              x="-15.400000000000002"
                              y="103.5"
                            >
                              -5
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-5"
                              x2="5"
                              y1="120"
                              y2="120"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="end"
                              x="-15.400000000000002"
                              y="123.5"
                            >
                              -6
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-5"
                              x2="5"
                              y1="140"
                              y2="140"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="end"
                              x="-15.400000000000002"
                              y="143.5"
                            >
                              -7
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-5"
                              x2="5"
                              y1="160"
                              y2="160"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="end"
                              x="-15.400000000000002"
                              y="163.5"
                            >
                              -8
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-5"
                              x2="5"
                              y1="180"
                              y2="180"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="end"
                              x="-15.400000000000002"
                              y="183.5"
                            >
                              -9
                            </text>
                          </g>
                        </g>
                        <g
                          class="x-axis-ticks"
                        >
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="20"
                              x2="20"
                              y1="5"
                              y2="-5"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="middle"
                              x="20"
                              y="24.5"
                            >
                              1
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="40"
                              x2="40"
                              y1="5"
                              y2="-5"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="middle"
                              x="40"
                              y="24.5"
                            >
                              2
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="60"
                              x2="60"
                              y1="5"
                              y2="-5"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="middle"
                              x="60"
                              y="24.5"
                            >
                              3
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="80"
                              x2="80"
                              y1="5"
                              y2="-5"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="middle"
                              x="80"
                              y="24.5"
                            >
                              4
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="100"
                              x2="100"
                              y1="5"
                              y2="-5"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="middle"
                              x="100"
                              y="24.5"
                            >
                              5
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="120"
                              x2="120"
                              y1="5"
                              y2="-5"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="middle"
                              x="120"
                              y="24.5"
                            >
                              6
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="140"
                              x2="140"
                              y1="5"
                              y2="-5"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="middle"
                              x="140"
                              y="24.5"
                            >
                              7
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="160"
                              x2="160"
                              y1="5"
                              y2="-5"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="middle"
                              x="160"
                              y="24.5"
                            >
                              8
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="180"
                              x2="180"
                              y1="5"
                              y2="-5"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="middle"
                              x="180"
                              y="24.5"
                            >
                              9
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-20"
                              x2="-20"
                              y1="5"
                              y2="-5"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="middle"
                              x="-22"
                              y="24.5"
                            >
                              -1
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-40"
                              x2="-40"
                              y1="5"
                              y2="-5"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="middle"
                              x="-42"
                              y="24.5"
                            >
                              -2
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-60"
                              x2="-60"
                              y1="5"
                              y2="-5"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="middle"
                              x="-62"
                              y="24.5"
                            >
                              -3
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-80"
                              x2="-80"
                              y1="5"
                              y2="-5"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="middle"
                              x="-82"
                              y="24.5"
                            >
                              -4
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-100"
                              x2="-100"
                              y1="5"
                              y2="-5"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="middle"
                              x="-102"
                              y="24.5"
                            >
                              -5
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-120"
                              x2="-120"
                              y1="5"
                              y2="-5"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="middle"
                              x="-122"
                              y="24.5"
                            >
                              -6
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-140"
                              x2="-140"
                              y1="5"
                              y2="-5"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="middle"
                              x="-142"
                              y="24.5"
                            >
                              -7
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-160"
                              x2="-160"
                              y1="5"
                              y2="-5"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="middle"
                              x="-162"
                              y="24.5"
                            >
                              -8
                            </text>
                          </g>
                          <g
                            aria-hidden="true"
                            class="tick"
                          >
                            <line
                              class="axis-tick"
                              x1="-180"
                              x2="-180"
                              y1="5"
                              y2="-5"
                            />
                            <text
                              class="axis-tick-label"
                              style="font-size: 14px;"
                              text-anchor="middle"
                              x="-182"
                              y="24.5"
                            >
                              -9
                            </text>
                          </g>
                        </g>
                      </g>
                      <g
                        aria-hidden="true"
                        class="interactive-graph-arrowhead"
                        transform="translate(-200 0) rotate(180)"
                      >
                        <g
                          transform="translate(-1.5)"
                        >
                          <path
                            d="M-4.199999999999999 5.6C-3.8499999999999996 3.5 0 0.35 1.0499999999999998 0C0 -0.35 -3.8499999999999996 -3.5 -4.199999999999999 -5.6"
                            fill="none"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2px"
                            style="stroke: var(--mafs-fg);"
                          />
                        </g>
                      </g>
                      <g
                        aria-hidden="true"
                        class="interactive-graph-arrowhead"
                        transform="translate(200 0) rotate(0)"
                      >
                        <g
                          transform="translate(-1.5)"
                        >
                          <path
                            d="M-4.199999999999999 5.6C-3.8499999999999996 3.5 0 0.35 1.0499999999999998 0C0 -0.35 -3.8499999999999996 -3.5 -4.199999999999999 -5.6"
                            fill="none"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2px"
                            style="stroke: var(--mafs-fg);"
                          />
                        </g>
                      </g>
                      <g
                        aria-hidden="true"
                        class="interactive-graph-arrowhead"
                        transform="translate(0 200) rotate(90)"
                      >
                        <g
                          transform="translate(-1.5)"
                        >
                          <path
                            d="M-4.199999999999999 5.6C-3.8499999999999996 3.5 0 0.35 1.0499999999999998 0C0 -0.35 -3.8499999999999996 -3.5 -4.199999999999999 -5.6"
                            fill="none"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2px"
                            style="stroke: var(--mafs-fg);"
                          />
                        </g>
                      </g>
                      <g
                        aria-hidden="true"
                        class="interactive-graph-arrowhead"
                        transform="translate(0 -200) rotate(270)"
                      >
                        <g
                          transform="translate(-1.5)"
                        >
                          <path
                            d="M-4.199999999999999 5.6C-3.8499999999999996 3.5 0 0.35 1.0499999999999998 0C0 -0.35 -3.8499999999999996 -3.5 -4.199999999999999 -5.6"
                            fill="none"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2px"
                            style="stroke: var(--mafs-fg);"
                          />
                        </g>
                      </g>
                    </svg>
                  </div>
                </div>
                <div
                  class="default_xu2jcg-o_O-inlineStyles_ay4wjb"
                >
                  <div
                    class="MafsView"
                    style="width: 400px; height: 400px;"
                    tabindex="-1"
                  >
                    <svg
                      height="400"
                      preserveAspectRatio="xMidYMin"
                      style="width: 400px; --mafs-view-transform: matrix(20, 0, 0, -20, 0, 0); --mafs-user-transform: translate(0, 0);"
                      viewBox="-200 -200 400 400"
                      width="400"
                    >
                      <svg
                        height="400"
                        preserveAspectRatio="xMidYMin"
                        viewBox="-200 -200 400 400"
                        width="400"
                        x="-200"
                        y="-200"
                      />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Interactive Graph question Should render predictably: after interaction 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        <strong>
          Sides shown
        </strong>
         Drag the vertices of the triangle below to draw a right triangle with side lengths 
        <span
          style="white-space: nowrap;"
        >
          <span />
          <span
            class="mock-TeX"
          >
            3
          </span>
          <span />
        </span>
        , 
        <span
          style="white-space: nowrap;"
        >
          <span />
          <span
            class="mock-TeX"
          >
            4
          </span>
          <span />
        </span>
        , and 
        <span
          style="white-space: nowrap;"
        >
          <span />
          <span
            class="mock-TeX"
          >
            5
          </span>
          <span />
        </span>
        . 

        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="default_xu2jcg mafs-graph-container"
          >
            <div
              aria-describedby="interactive-graph-interactive-elements-description-:r2: instructions-:r2:"
              class="default_xu2jcg-o_O-inlineStyles_1t4cnlh mafs-graph"
              tabindex="0"
            >
              <div
                class="default_xu2jcg mafs-sr-only"
                id="interactive-graph-interactive-elements-description-:r2:"
                tabindex="-1"
              >
                Interactive elements: A polygon with 3 points. 
              </div>
              <div
                class="default_xu2jcg mafs-sr-only"
                id="instructions-:r2:"
                tabindex="-1"
              >
                Use the Tab key to move through the interactive elements in the graph. When an interactive element has focus, use Control + Shift + Arrows to move it.
              </div>
              <div
                class="default_xu2jcg-o_O-inlineStyles_16155wj"
              >
                <div
                  aria-hidden="true"
                  class="default_xu2jcg"
                >
                  <div
                    class="MafsView"
                    style="width: 400px; height: 400px;"
                    tabindex="-1"
                  >
                    <svg
                      height="400"
                      preserveAspectRatio="xMidYMin"
                      style="width: 400px; --mafs-view-transform: matrix(57.14286, 0, 0, -57.14286, 0, 0); --mafs-user-transform: translate(0, 0);"
                      viewBox="-57.1428571429 -342.8571428571 400 400"
                      width="400"
                    >
                      <defs>
                        <filter
                          height="100%"
                          id="background"
                          width="110%"
                          x="-5%"
                          y="0%"
                        >
                          <feflood
                            flood-color="#FFF"
                            flood-opacity="0.64"
                          />
                          <fecomposite
                            in="SourceGraphic"
                            operator="over"
                          />
                        </filter>
                      </defs>
                      <svg
                        height="400"
                        preserveAspectRatio="xMidYMin"
                        viewBox="-57.142857142857146 -342.85714285714283 400 400"
                        width="400"
                        x="-57.142857142857146"
                        y="-342.85714285714283"
                      />
                    </svg>
                  </div>
                </div>
                <div
                  class="default_xu2jcg-o_O-inlineStyles_ay4wjb"
                >
                  <div
                    class="MafsView"
                    style="width: 400px; height: 400px;"
                    tabindex="-1"
                  >
                    <svg
                      height="400"
                      preserveAspectRatio="xMidYMin"
                      style="width: 400px; --mafs-view-transform: matrix(57.14286, 0, 0, -57.14286, 0, 0); --mafs-user-transform: translate(0, 0);"
                      viewBox="-57.1428571429 -342.8571428571 400 400"
                      width="400"
                    >
                      <svg
                        height="400"
                        preserveAspectRatio="xMidYMin"
                        viewBox="-57.142857142857146 -342.85714285714283 400 400"
                        width="400"
                        x="-57.142857142857146"
                        y="-342.85714285714283"
                      >
                        <g
                          aria-describedby=":r3:-points-num :r3:-points"
                          aria-label="A polygon."
                        >
                          <polygon
                            aria-hidden="true"
                            fill-opacity="0.15"
                            points="3.5 2 2.5 4 1.5 2"
                            stroke-linejoin="round"
                            stroke-width="var(--movable-line-stroke-weight)"
                            style="fill: transparent; fill-opacity: 0.15; stroke: var(--mafs-blue); stroke-opacity: 1; vector-effect: non-scaling-stroke; transform: var(--mafs-view-transform);"
                          />
                          <text
                            class="mafs-shadow"
                            dominant-baseline="middle"
                            filter="url(#background)"
                            font-size="16"
                            font-weight="bold"
                            style="fill: var(--mafs-fg); vector-effect: non-scaling-stroke;"
                            text-anchor="middle"
                            x="171.42858"
                            y="-171.42858"
                          >
                            ≈ 2.2
                          </text>
                          <text
                            class="mafs-shadow"
                            dominant-baseline="middle"
                            filter="url(#background)"
                            font-size="16"
                            font-weight="bold"
                            style="fill: var(--mafs-fg); vector-effect: non-scaling-stroke;"
                            text-anchor="middle"
                            x="114.28572"
                            y="-171.42858"
                          >
                            ≈ 2.2
                          </text>
                          <text
                            class="mafs-shadow"
                            dominant-baseline="middle"
                            filter="url(#background)"
                            font-size="16"
                            font-weight="bold"
                            style="fill: var(--mafs-fg); vector-effect: non-scaling-stroke;"
                            text-anchor="middle"
                            x="142.85715"
                            y="-114.28572"
                          >
                            2
                          </text>
                          <polygon
                            aria-disabled="false"
                            aria-label="A polygon with 3 points."
                            aria-live="off"
                            class="movable-polygon"
                            fill-opacity="0.15"
                            points="3.5 2 2.5 4 1.5 2"
                            role="button"
                            stroke-linejoin="round"
                            stroke-width="44"
                            style="fill: transparent; fill-opacity: 0.15; stroke: transparent; stroke-opacity: 1; vector-effect: non-scaling-stroke; transform: var(--mafs-view-transform); cursor: grab;"
                            tabindex="0"
                          />
                          <g>
                            <g
                              aria-describedby=":r3:-angle-0 :r3:-point-0-side-1 :r3:-point-0-side-2"
                              aria-disabled="false"
                              aria-label="Point 1 at 3.5 comma 2."
                              aria-live="off"
                              class="movable-point__focusable-handle"
                              data-testid="movable-point__focusable-handle"
                              role="button"
                              tabindex="0"
                            />
                            <g
                              aria-hidden="true"
                              class="movable-point"
                              data-testid="movable-point"
                              style="--movable-point-color: var(--mafs-blue);"
                            >
                              <circle
                                class="movable-point-hitbox"
                                cx="200"
                                cy="-114.28571428571429"
                                r="24"
                              />
                              <circle
                                class="movable-point-halo"
                                cx="200"
                                cy="-114.28571428571429"
                              />
                              <circle
                                class="movable-point-ring"
                                cx="200"
                                cy="-114.28571428571429"
                              />
                              <circle
                                class="movable-point-focus-outline"
                                cx="200"
                                cy="-114.28571428571429"
                              />
                              <circle
                                class="movable-point-center"
                                cx="200"
                                cy="-114.28571428571429"
                                data-testid="movable-point__center"
                                style="fill: var(--mafs-blue);"
                              />
                            </g>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":r3:-angle-0"
                              >
                                Angle approximately equal to 63.4 degrees.
                              </span>
                            </foreignobject>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":r3:-point-0-side-1"
                              >
                                A line segment, length equal to 2 units, connects to point 3.
                              </span>
                            </foreignobject>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":r3:-point-0-side-2"
                              >
                                A line segment, length approximately equal to 2.2 units, connects to point 2.
                              </span>
                            </foreignobject>
                          </g>
                          <g>
                            <g
                              aria-describedby=":r3:-angle-1 :r3:-point-1-side-1 :r3:-point-1-side-2"
                              aria-disabled="false"
                              aria-label="Point 2 at 2.5 comma 4."
                              aria-live="off"
                              class="movable-point__focusable-handle"
                              data-testid="movable-point__focusable-handle"
                              role="button"
                              tabindex="0"
                            />
                            <g
                              aria-hidden="true"
                              class="movable-point"
                              data-testid="movable-point"
                              style="--movable-point-color: var(--mafs-blue);"
                            >
                              <circle
                                class="movable-point-hitbox"
                                cx="142.85714285714286"
                                cy="-228.57142857142858"
                                r="24"
                              />
                              <circle
                                class="movable-point-halo"
                                cx="142.85714285714286"
                                cy="-228.57142857142858"
                              />
                              <circle
                                class="movable-point-ring"
                                cx="142.85714285714286"
                                cy="-228.57142857142858"
                              />
                              <circle
                                class="movable-point-focus-outline"
                                cx="142.85714285714286"
                                cy="-228.57142857142858"
                              />
                              <circle
                                class="movable-point-center"
                                cx="142.85714285714286"
                                cy="-228.57142857142858"
                                data-testid="movable-point__center"
                                style="fill: var(--mafs-blue);"
                              />
                            </g>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":r3:-angle-1"
                              >
                                Angle approximately equal to 53.1 degrees.
                              </span>
                            </foreignobject>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":r3:-point-1-side-1"
                              >
                                A line segment, length approximately equal to 2.2 units, connects to point 1.
                              </span>
                            </foreignobject>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":r3:-point-1-side-2"
                              >
                                A line segment, length approximately equal to 2.2 units, connects to point 3.
                              </span>
                            </foreignobject>
                          </g>
                          <g>
                            <g
                              aria-describedby=":r3:-angle-2 :r3:-point-2-side-1 :r3:-point-2-side-2"
                              aria-disabled="false"
                              aria-label="Point 3 at 1.5 comma 2."
                              aria-live="off"
                              class="movable-point__focusable-handle"
                              data-testid="movable-point__focusable-handle"
                              role="button"
                              tabindex="0"
                            />
                            <g
                              aria-hidden="true"
                              class="movable-point"
                              data-testid="movable-point"
                              style="--movable-point-color: var(--mafs-blue);"
                            >
                              <circle
                                class="movable-point-hitbox"
                                cx="85.71428571428572"
                                cy="-114.28571428571429"
                                r="24"
                              />
                              <circle
                                class="movable-point-halo"
                                cx="85.71428571428572"
                                cy="-114.28571428571429"
                              />
                              <circle
                                class="movable-point-ring"
                                cx="85.71428571428572"
                                cy="-114.28571428571429"
                              />
                              <circle
                                class="movable-point-focus-outline"
                                cx="85.71428571428572"
                                cy="-114.28571428571429"
                              />
                              <circle
                                class="movable-point-center"
                                cx="85.71428571428572"
                                cy="-114.28571428571429"
                                data-testid="movable-point__center"
                                style="fill: var(--mafs-blue);"
                              />
                            </g>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":r3:-angle-2"
                              >
                                Angle approximately equal to 63.4 degrees.
                              </span>
                            </foreignobject>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":r3:-point-2-side-1"
                              >
                                A line segment, length approximately equal to 2.2 units, connects to point 2.
                              </span>
                            </foreignobject>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":r3:-point-2-side-2"
                              >
                                A line segment, length equal to 2 units, connects to point 1.
                              </span>
                            </foreignobject>
                          </g>
                          <foreignobject>
                            <span
                              aria-hidden="true"
                              id=":r3:-points-num"
                            >
                              The polygon has 3 points.
                            </span>
                          </foreignobject>
                        </g>
                      </svg>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
         
      </div>
    </div>
  </div>
</div>
`;

exports[`Interactive Graph question Should render predictably: after interaction 2`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        <strong>
          Plot the image of triangle 
          <span
            style="white-space: nowrap;"
          >
            <span />
            <span
              class="mock-TeX"
            >
              \\triangle ABC
            </span>
            <span />
          </span>
           under a reflection across line 
          <span
            style="white-space: nowrap;"
          >
            <span />
            <span
              class="mock-TeX"
            >
              \\ell
            </span>
            <span />
          </span>
          .
        </strong>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="1"
    >
      <div
        class="paragraph"
      >
        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="default_xu2jcg mafs-graph-container"
          >
            <div
              aria-describedby="interactive-graph-interactive-elements-description-:ra: instructions-:ra:"
              class="default_xu2jcg-o_O-inlineStyles_1t4cnlh mafs-graph"
              tabindex="0"
            >
              <div
                class="default_xu2jcg mafs-sr-only"
                id="interactive-graph-interactive-elements-description-:ra:"
                tabindex="-1"
              >
                Interactive elements: A polygon with 3 points. 
              </div>
              <div
                class="default_xu2jcg mafs-sr-only"
                id="instructions-:ra:"
                tabindex="-1"
              >
                Use the Tab key to move through the interactive elements in the graph. When an interactive element has focus, use Control + Shift + Arrows to move it.
              </div>
              <div
                class="default_xu2jcg-o_O-inlineStyles_16155wj"
              >
                <div
                  class="unresponsive-svg-image"
                  style="width: 400px; height: 400px;"
                >
                  <span
                    style="top: 0px; left: 0px; width: 100%; height: 100%; position: absolute; min-width: 20px; display: flex; justify-content: center; align-content: center;"
                  >
                    <div
                      class="default_xu2jcg-o_O-spinnerContainer_agrn11"
                    >
                      <svg
                        height="48"
                        viewBox="0 0 48 48"
                        width="48"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          class="loadingSpinner_1dvgeb3-o_O-inlineStyles_8njutv"
                          d="M44.19 23.455a1.91 1.91 0 1 1 3.801 0h.003c.004.18.006.363.006.545 0 13.255-10.745 24-24 24S0 37.255 0 24 10.745 0 24 0c.182 0 .364.002.545.006V.01a1.91 1.91 0 1 1 0 3.801v.015A20.564 20.564 0 0 0 24 3.818C12.854 3.818 3.818 12.854 3.818 24c0 11.146 9.036 20.182 20.182 20.182 11.146 0 20.182-9.036 20.182-20.182 0-.182-.003-.364-.007-.545h.015z"
                          fill-rule="nonzero"
                        />
                      </svg>
                    </div>
                  </span>
                </div>
              </div>
              <div
                class="default_xu2jcg-o_O-inlineStyles_16155wj"
              >
                <div
                  aria-hidden="true"
                  class="default_xu2jcg"
                >
                  <div
                    class="MafsView"
                    style="width: 400px; height: 400px;"
                    tabindex="-1"
                  >
                    <svg
                      height="400"
                      preserveAspectRatio="xMidYMin"
                      style="width: 400px; --mafs-view-transform: matrix(25, 0, 0, -25, 0, 0); --mafs-user-transform: translate(0, 0);"
                      viewBox="-200 -200 400 400"
                      width="400"
                    >
                      <defs>
                        <filter
                          height="100%"
                          id="background"
                          width="110%"
                          x="-5%"
                          y="0%"
                        >
                          <feflood
                            flood-color="#FFF"
                            flood-opacity="0.64"
                          />
                          <fecomposite
                            in="SourceGraphic"
                            operator="over"
                          />
                        </filter>
                      </defs>
                      <svg
                        height="400"
                        preserveAspectRatio="xMidYMin"
                        viewBox="-200 -200 400 400"
                        width="400"
                        x="-200"
                        y="-200"
                      />
                    </svg>
                  </div>
                </div>
                <div
                  class="default_xu2jcg-o_O-inlineStyles_ay4wjb"
                >
                  <div
                    class="MafsView"
                    style="width: 400px; height: 400px;"
                    tabindex="-1"
                  >
                    <svg
                      height="400"
                      preserveAspectRatio="xMidYMin"
                      style="width: 400px; --mafs-view-transform: matrix(25, 0, 0, -25, 0, 0); --mafs-user-transform: translate(0, 0);"
                      viewBox="-200 -200 400 400"
                      width="400"
                    >
                      <svg
                        height="400"
                        preserveAspectRatio="xMidYMin"
                        viewBox="-200 -200 400 400"
                        width="400"
                        x="-200"
                        y="-200"
                      >
                        <g
                          aria-describedby=":rb:-points-num :rb:-points"
                          aria-label="A polygon."
                        >
                          <polygon
                            aria-hidden="true"
                            fill-opacity="0.15"
                            points="3 -2 0 3 -3 -2"
                            stroke-linejoin="round"
                            stroke-width="var(--movable-line-stroke-weight)"
                            style="fill: transparent; fill-opacity: 0.15; stroke: var(--mafs-blue); stroke-opacity: 1; vector-effect: non-scaling-stroke; transform: var(--mafs-view-transform);"
                          />
                          <polygon
                            aria-disabled="false"
                            aria-label="A polygon with 3 points."
                            aria-live="off"
                            class="movable-polygon"
                            fill-opacity="0.15"
                            points="3 -2 0 3 -3 -2"
                            role="button"
                            stroke-linejoin="round"
                            stroke-width="44"
                            style="fill: transparent; fill-opacity: 0.15; stroke: transparent; stroke-opacity: 1; vector-effect: non-scaling-stroke; transform: var(--mafs-view-transform); cursor: grab;"
                            tabindex="0"
                          />
                          <g>
                            <g
                              aria-describedby=":rb:-angle-0 :rb:-point-0-side-1 :rb:-point-0-side-2"
                              aria-disabled="false"
                              aria-label="Point 1 at 3 comma -2."
                              aria-live="off"
                              class="movable-point__focusable-handle"
                              data-testid="movable-point__focusable-handle"
                              role="button"
                              tabindex="0"
                            />
                            <g
                              aria-hidden="true"
                              class="movable-point"
                              data-testid="movable-point"
                              style="--movable-point-color: var(--mafs-blue);"
                            >
                              <circle
                                class="movable-point-hitbox"
                                cx="75"
                                cy="50"
                                r="24"
                              />
                              <circle
                                class="movable-point-halo"
                                cx="75"
                                cy="50"
                              />
                              <circle
                                class="movable-point-ring"
                                cx="75"
                                cy="50"
                              />
                              <circle
                                class="movable-point-focus-outline"
                                cx="75"
                                cy="50"
                              />
                              <circle
                                class="movable-point-center"
                                cx="75"
                                cy="50"
                                data-testid="movable-point__center"
                                style="fill: var(--mafs-blue);"
                              />
                            </g>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":rb:-angle-0"
                              >
                                Angle approximately equal to 59 degrees.
                              </span>
                            </foreignobject>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":rb:-point-0-side-1"
                              >
                                A line segment, length equal to 6 units, connects to point 3.
                              </span>
                            </foreignobject>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":rb:-point-0-side-2"
                              >
                                A line segment, length approximately equal to 5.8 units, connects to point 2.
                              </span>
                            </foreignobject>
                          </g>
                          <g>
                            <g
                              aria-describedby=":rb:-angle-1 :rb:-point-1-side-1 :rb:-point-1-side-2"
                              aria-disabled="false"
                              aria-label="Point 2 at 0 comma 3."
                              aria-live="off"
                              class="movable-point__focusable-handle"
                              data-testid="movable-point__focusable-handle"
                              role="button"
                              tabindex="0"
                            />
                            <g
                              aria-hidden="true"
                              class="movable-point"
                              data-testid="movable-point"
                              style="--movable-point-color: var(--mafs-blue);"
                            >
                              <circle
                                class="movable-point-hitbox"
                                cx="0"
                                cy="-75"
                                r="24"
                              />
                              <circle
                                class="movable-point-halo"
                                cx="0"
                                cy="-75"
                              />
                              <circle
                                class="movable-point-ring"
                                cx="0"
                                cy="-75"
                              />
                              <circle
                                class="movable-point-focus-outline"
                                cx="0"
                                cy="-75"
                              />
                              <circle
                                class="movable-point-center"
                                cx="0"
                                cy="-75"
                                data-testid="movable-point__center"
                                style="fill: var(--mafs-blue);"
                              />
                            </g>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":rb:-angle-1"
                              >
                                Angle approximately equal to 61.9 degrees.
                              </span>
                            </foreignobject>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":rb:-point-1-side-1"
                              >
                                A line segment, length approximately equal to 5.8 units, connects to point 1.
                              </span>
                            </foreignobject>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":rb:-point-1-side-2"
                              >
                                A line segment, length approximately equal to 5.8 units, connects to point 3.
                              </span>
                            </foreignobject>
                          </g>
                          <g>
                            <g
                              aria-describedby=":rb:-angle-2 :rb:-point-2-side-1 :rb:-point-2-side-2"
                              aria-disabled="false"
                              aria-label="Point 3 at -3 comma -2."
                              aria-live="off"
                              class="movable-point__focusable-handle"
                              data-testid="movable-point__focusable-handle"
                              role="button"
                              tabindex="0"
                            />
                            <g
                              aria-hidden="true"
                              class="movable-point"
                              data-testid="movable-point"
                              style="--movable-point-color: var(--mafs-blue);"
                            >
                              <circle
                                class="movable-point-hitbox"
                                cx="-75"
                                cy="50"
                                r="24"
                              />
                              <circle
                                class="movable-point-halo"
                                cx="-75"
                                cy="50"
                              />
                              <circle
                                class="movable-point-ring"
                                cx="-75"
                                cy="50"
                              />
                              <circle
                                class="movable-point-focus-outline"
                                cx="-75"
                                cy="50"
                              />
                              <circle
                                class="movable-point-center"
                                cx="-75"
                                cy="50"
                                data-testid="movable-point__center"
                                style="fill: var(--mafs-blue);"
                              />
                            </g>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":rb:-angle-2"
                              >
                                Angle approximately equal to 59 degrees.
                              </span>
                            </foreignobject>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":rb:-point-2-side-1"
                              >
                                A line segment, length approximately equal to 5.8 units, connects to point 2.
                              </span>
                            </foreignobject>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":rb:-point-2-side-2"
                              >
                                A line segment, length equal to 6 units, connects to point 1.
                              </span>
                            </foreignobject>
                          </g>
                          <foreignobject>
                            <span
                              aria-hidden="true"
                              id=":rb:-points-num"
                            >
                              The polygon has 3 points.
                            </span>
                          </foreignobject>
                        </g>
                      </svg>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Interactive Graph question Should render predictably: after interaction 3`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        We want to find the zeros of this polynomial:
      </div>
    </div>
    <div
      class="paragraph perseus-paragraph-centered"
      data-perseus-paragraph-index="1"
    >
      <div
        class="perseus-block-math"
      >
        <div
          class="perseus-block-math-inner"
          style="overflow-x: auto; overflow-y: hidden; padding-top: 10px; padding-bottom: 10px; margin-top: -10px; margin-bottom: -10px;"
        >
          <span
            class="mock-TeX"
          >
            p(x)=x(2x+5)(x+1)
          </span>
        </div>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="2"
    >
      <div
        class="paragraph"
      >
        <strong>
          Plot all the zeros (
          <span
            style="white-space: nowrap;"
          >
            <span />
            <span
              class="mock-TeX"
            >
              x
            </span>
            <span />
          </span>
          -intercepts) of the polynomial in the interactive graph.
        </strong>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="3"
    >
      <div
        class="paragraph"
      >
        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="default_xu2jcg mafs-graph-container"
          >
            <div
              aria-describedby="interactive-graph-interactive-elements-description-:rh: unlimited-graph-keyboard-prompt-:rh: instructions-:rh:"
              class="default_xu2jcg-o_O-inlineStyles_1t4cnlh mafs-graph"
              tabindex="0"
            >
              <div
                class="default_xu2jcg mafs-sr-only"
                id="interactive-graph-interactive-elements-description-:rh:"
                tabindex="-1"
              >
                No interactive elements
              </div>
              <div
                class="default_xu2jcg mafs-sr-only"
                id="instructions-:rh:"
                tabindex="-1"
              >
                Press Shift + Enter to interact with the graph. Use the Tab key to move through the interactive elements in the graph and access the graph Action Bar. When an interactive element has focus, use Control + Shift + Arrows to move it or use the Delete key to remove it from the graph. Use the buttons in the Action Bar to add or adjust elements within the graph.
              </div>
              <div
                class="default_xu2jcg-o_O-inlineStyles_16155wj"
              >
                <div
                  class="unresponsive-svg-image"
                  style="width: 425px; height: 425px;"
                >
                  <span
                    style="top: 0px; left: 0px; width: 100%; height: 100%; position: absolute; min-width: 20px; display: flex; justify-content: center; align-content: center;"
                  >
                    <div
                      class="default_xu2jcg-o_O-spinnerContainer_agrn11"
                    >
                      <svg
                        height="48"
                        viewBox="0 0 48 48"
                        width="48"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          class="loadingSpinner_1dvgeb3-o_O-inlineStyles_8njutv"
                          d="M44.19 23.455a1.91 1.91 0 1 1 3.801 0h.003c.004.18.006.363.006.545 0 13.255-10.745 24-24 24S0 37.255 0 24 10.745 0 24 0c.182 0 .364.002.545.006V.01a1.91 1.91 0 1 1 0 3.801v.015A20.564 20.564 0 0 0 24 3.818C12.854 3.818 3.818 12.854 3.818 24c0 11.146 9.036 20.182 20.182 20.182 11.146 0 20.182-9.036 20.182-20.182 0-.182-.003-.364-.007-.545h.015z"
                          fill-rule="nonzero"
                        />
                      </svg>
                    </div>
                  </span>
                </div>
              </div>
              <div
                class="default_xu2jcg-o_O-inlineStyles_16155wj"
              >
                <div
                  aria-hidden="true"
                  class="default_xu2jcg"
                >
                  <div
                    class="MafsView"
                    style="width: 400px; height: 400px;"
                    tabindex="-1"
                  >
                    <svg
                      height="400"
                      preserveAspectRatio="xMidYMin"
                      style="width: 400px; --mafs-view-transform: matrix(50, 0, 0, -50, 0, 0); --mafs-user-transform: translate(0, 0);"
                      viewBox="-200 -200 400 400"
                      width="400"
                    >
                      <defs>
                        <filter
                          height="100%"
                          id="background"
                          width="110%"
                          x="-5%"
                          y="0%"
                        >
                          <feflood
                            flood-color="#FFF"
                            flood-opacity="0.64"
                          />
                          <fecomposite
                            in="SourceGraphic"
                            operator="over"
                          />
                        </filter>
                      </defs>
                      <svg
                        height="400"
                        preserveAspectRatio="xMidYMin"
                        viewBox="-200 -200 400 400"
                        width="400"
                        x="-200"
                        y="-200"
                      />
                    </svg>
                  </div>
                </div>
                <div
                  class="default_xu2jcg-o_O-inlineStyles_ay4wjb"
                >
                  <div
                    class="MafsView"
                    style="width: 400px; height: 400px;"
                    tabindex="-1"
                  >
                    <svg
                      height="400"
                      preserveAspectRatio="xMidYMin"
                      style="width: 400px; --mafs-view-transform: matrix(50, 0, 0, -50, 0, 0); --mafs-user-transform: translate(0, 0);"
                      viewBox="-200 -200 400 400"
                      width="400"
                    >
                      <svg
                        height="400"
                        preserveAspectRatio="xMidYMin"
                        viewBox="-200 -200 400 400"
                        width="400"
                        x="-200"
                        y="-200"
                      >
                        <rect
                          height="400"
                          style="fill: rgba(0,0,0,0); cursor: crosshair;"
                          width="400"
                          x="-200"
                          y="-200"
                        />
                      </svg>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="default_xu2jcg-o_O-inlineStyles_1ufx1c5"
            >
              <button
                aria-disabled="false"
                class="button_vr44p2-o_O-reset_1ck4g21-o_O-shared_1jpsfmg-o_O-default_qlqm6z-o_O-inlineStyles_16y2sco"
                id="perseus_mafs_remove_button"
                role="button"
                tabindex="-1"
                type="button"
              >
                <span
                  class="text_f1191h-o_O-LabelLarge_16mkhxv-o_O-text_1kowsup"
                >
                  Remove Point
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Interactive Graph question Should render predictably: after interaction 4`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        The graph below contains quadrilateral 
        <span
          style="white-space: nowrap;"
        >
          <span />
          <span
            class="mock-TeX"
          >
            TREK
          </span>
          <span />
        </span>
         and the point 
        <span
          style="white-space: nowrap;"
        >
          <span />
          <span
            class="mock-TeX"
          >
            P(-8,-6)
          </span>
          <span />
        </span>
        .  
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="1"
    >
      <div
        class="paragraph"
      >
        <strong>
          Draw the image of quadrilateral 
          <span
            style="white-space: nowrap;"
          >
            <span />
            <span
              class="mock-TeX"
            >
              TREK
            </span>
            <span />
          </span>
           under a dilation whose center is 
          <span
            style="white-space: nowrap;"
          >
            <span />
            <span
              class="mock-TeX"
            >
              P
            </span>
            <span />
          </span>
           and scale factor is 
          <span
            style="white-space: nowrap;"
          >
            <span />
            <span
              class="mock-TeX"
            >
              3
            </span>
            <span />
          </span>
          .
        </strong>
          
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="2"
    >
      <div
        class="paragraph"
      >
        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="default_xu2jcg mafs-graph-container"
          >
            <div
              aria-describedby="unlimited-graph-keyboard-prompt-:rm: instructions-:rm:"
              class="default_xu2jcg-o_O-inlineStyles_1t4cnlh mafs-graph"
              tabindex="0"
            >
              <div
                class="default_xu2jcg mafs-sr-only"
                id="instructions-:rm:"
                tabindex="-1"
              >
                Press Shift + Enter to interact with the graph. Use the Tab key to move through the interactive elements in the graph and access the graph Action Bar. When an interactive element has focus, use Control + Shift + Arrows to move it or use the Delete key to remove it from the graph. Use the buttons in the Action Bar to add or adjust elements within the graph.
              </div>
              <div
                class="default_xu2jcg-o_O-inlineStyles_16155wj"
              >
                <div
                  class="unresponsive-svg-image"
                  style="width: 425px; height: 425px;"
                >
                  <span
                    style="top: 0px; left: 0px; width: 100%; height: 100%; position: absolute; min-width: 20px; display: flex; justify-content: center; align-content: center;"
                  >
                    <div
                      class="default_xu2jcg-o_O-spinnerContainer_agrn11"
                    >
                      <svg
                        height="48"
                        viewBox="0 0 48 48"
                        width="48"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          class="loadingSpinner_1dvgeb3-o_O-inlineStyles_8njutv"
                          d="M44.19 23.455a1.91 1.91 0 1 1 3.801 0h.003c.004.18.006.363.006.545 0 13.255-10.745 24-24 24S0 37.255 0 24 10.745 0 24 0c.182 0 .364.002.545.006V.01a1.91 1.91 0 1 1 0 3.801v.015A20.564 20.564 0 0 0 24 3.818C12.854 3.818 3.818 12.854 3.818 24c0 11.146 9.036 20.182 20.182 20.182 11.146 0 20.182-9.036 20.182-20.182 0-.182-.003-.364-.007-.545h.015z"
                          fill-rule="nonzero"
                        />
                      </svg>
                    </div>
                  </span>
                </div>
              </div>
              <div
                class="default_xu2jcg-o_O-inlineStyles_16155wj"
              >
                <div
                  aria-hidden="true"
                  class="default_xu2jcg"
                >
                  <div
                    class="MafsView"
                    style="width: 400px; height: 400px;"
                    tabindex="-1"
                  >
                    <svg
                      height="400"
                      preserveAspectRatio="xMidYMin"
                      style="width: 400px; --mafs-view-transform: matrix(16.66667, 0, 0, -16.66667, 0, 0); --mafs-user-transform: translate(0, 0);"
                      viewBox="-200 -266.6666666667 400 400"
                      width="400"
                    >
                      <defs>
                        <filter
                          height="100%"
                          id="background"
                          width="110%"
                          x="-5%"
                          y="0%"
                        >
                          <feflood
                            flood-color="#FFF"
                            flood-opacity="0.64"
                          />
                          <fecomposite
                            in="SourceGraphic"
                            operator="over"
                          />
                        </filter>
                      </defs>
                      <svg
                        height="400"
                        preserveAspectRatio="xMidYMin"
                        viewBox="-200 -266.66666666666663 400 400"
                        width="400"
                        x="-200"
                        y="-266.66666666666663"
                      />
                    </svg>
                  </div>
                </div>
                <div
                  class="default_xu2jcg-o_O-inlineStyles_ay4wjb"
                >
                  <div
                    class="MafsView"
                    style="width: 400px; height: 400px;"
                    tabindex="-1"
                  >
                    <svg
                      height="400"
                      preserveAspectRatio="xMidYMin"
                      style="width: 400px; --mafs-view-transform: matrix(16.66667, 0, 0, -16.66667, 0, 0); --mafs-user-transform: translate(0, 0);"
                      viewBox="-200 -266.6666666667 400 400"
                      width="400"
                    >
                      <svg
                        height="400"
                        preserveAspectRatio="xMidYMin"
                        viewBox="-200 -266.66666666666663 400 400"
                        width="400"
                        x="-200"
                        y="-266.66666666666663"
                      >
                        <g
                          aria-describedby=":rn:-points-num :rn:-points"
                          aria-label="An empty coordinate plane."
                        >
                          <polyline
                            aria-hidden="true"
                            fill-opacity="0"
                            points=""
                            stroke-linejoin="round"
                            stroke-width="var(--movable-line-stroke-weight)"
                            style="fill: transparent; fill-opacity: 0; stroke: var(--mafs-blue); stroke-opacity: 1; vector-effect: non-scaling-stroke; transform: var(--mafs-view-transform);"
                          />
                          <rect
                            aria-hidden="true"
                            height="400"
                            style="fill: rgba(0,0,0,0); cursor: crosshair;"
                            width="400"
                            x="-200"
                            y="-266.6666666666667"
                          />
                        </g>
                      </svg>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="default_xu2jcg-o_O-inlineStyles_1ufx1c5"
            >
              <button
                aria-disabled="true"
                class="button_vr44p2-o_O-reset_1ck4g21-o_O-shared_1jpsfmg-o_O-default_qlqm6z-o_O-disabled_1y6fils-o_O-inlineStyles_1kc2paq"
                id="perseus_mafs_remove_button"
                role="button"
                tabindex="-1"
                type="button"
              >
                <span
                  class="text_f1191h-o_O-LabelLarge_16mkhxv-o_O-text_1kowsup"
                >
                  Remove Point
                </span>
              </button>
              <button
                aria-disabled="true"
                class="button_vr44p2-o_O-reset_1ck4g21-o_O-shared_1jpsfmg-o_O-default_1l1gmds-o_O-disabled_1y6fils-o_O-inlineStyles_1kc2paq"
                role="button"
                tabindex="-1"
                type="button"
              >
                <span
                  class="text_f1191h-o_O-LabelLarge_16mkhxv-o_O-text_1kowsup"
                >
                  Close shape
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Interactive Graph question Should render predictably: first render 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        <strong>
          Sides shown
        </strong>
         Drag the vertices of the triangle below to draw a right triangle with side lengths 
        <span
          style="white-space: nowrap;"
        >
          <span />
          <span
            class="mock-TeX"
          >
            3
          </span>
          <span />
        </span>
        , 
        <span
          style="white-space: nowrap;"
        >
          <span />
          <span
            class="mock-TeX"
          >
            4
          </span>
          <span />
        </span>
        , and 
        <span
          style="white-space: nowrap;"
        >
          <span />
          <span
            class="mock-TeX"
          >
            5
          </span>
          <span />
        </span>
        . 

        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="default_xu2jcg mafs-graph-container"
          >
            <div
              aria-describedby="interactive-graph-interactive-elements-description-:r2: instructions-:r2:"
              class="default_xu2jcg-o_O-inlineStyles_1t4cnlh mafs-graph"
              tabindex="0"
            >
              <div
                class="default_xu2jcg mafs-sr-only"
                id="interactive-graph-interactive-elements-description-:r2:"
                tabindex="-1"
              >
                Interactive elements: A polygon with 3 points. 
              </div>
              <div
                class="default_xu2jcg mafs-sr-only"
                id="instructions-:r2:"
                tabindex="-1"
              >
                Use the Tab key to move through the interactive elements in the graph. When an interactive element has focus, use Control + Shift + Arrows to move it.
              </div>
              <div
                class="default_xu2jcg-o_O-inlineStyles_16155wj"
              >
                <div
                  aria-hidden="true"
                  class="default_xu2jcg"
                >
                  <div
                    class="MafsView"
                    style="width: 400px; height: 400px;"
                    tabindex="-1"
                  >
                    <svg
                      height="400"
                      preserveAspectRatio="xMidYMin"
                      style="width: 400px; --mafs-view-transform: matrix(57.14286, 0, 0, -57.14286, 0, 0); --mafs-user-transform: translate(0, 0);"
                      viewBox="-57.1428571429 -342.8571428571 400 400"
                      width="400"
                    >
                      <defs>
                        <filter
                          height="100%"
                          id="background"
                          width="110%"
                          x="-5%"
                          y="0%"
                        >
                          <feflood
                            flood-color="#FFF"
                            flood-opacity="0.64"
                          />
                          <fecomposite
                            in="SourceGraphic"
                            operator="over"
                          />
                        </filter>
                      </defs>
                      <svg
                        height="400"
                        preserveAspectRatio="xMidYMin"
                        viewBox="-57.142857142857146 -342.85714285714283 400 400"
                        width="400"
                        x="-57.142857142857146"
                        y="-342.85714285714283"
                      />
                    </svg>
                  </div>
                </div>
                <div
                  class="default_xu2jcg-o_O-inlineStyles_ay4wjb"
                >
                  <div
                    class="MafsView"
                    style="width: 400px; height: 400px;"
                    tabindex="-1"
                  >
                    <svg
                      height="400"
                      preserveAspectRatio="xMidYMin"
                      style="width: 400px; --mafs-view-transform: matrix(57.14286, 0, 0, -57.14286, 0, 0); --mafs-user-transform: translate(0, 0);"
                      viewBox="-57.1428571429 -342.8571428571 400 400"
                      width="400"
                    >
                      <svg
                        height="400"
                        preserveAspectRatio="xMidYMin"
                        viewBox="-57.142857142857146 -342.85714285714283 400 400"
                        width="400"
                        x="-57.142857142857146"
                        y="-342.85714285714283"
                      >
                        <g
                          aria-describedby=":r3:-points-num :r3:-points"
                          aria-label="A polygon."
                        >
                          <polygon
                            aria-hidden="true"
                            fill-opacity="0.15"
                            points="3.5 2 2.5 4 1.5 2"
                            stroke-linejoin="round"
                            stroke-width="var(--movable-line-stroke-weight)"
                            style="fill: transparent; fill-opacity: 0.15; stroke: var(--mafs-blue); stroke-opacity: 1; vector-effect: non-scaling-stroke; transform: var(--mafs-view-transform);"
                          />
                          <text
                            class="mafs-shadow"
                            dominant-baseline="middle"
                            filter="url(#background)"
                            font-size="16"
                            font-weight="bold"
                            style="fill: var(--mafs-fg); vector-effect: non-scaling-stroke;"
                            text-anchor="middle"
                            x="171.42858"
                            y="-171.42858"
                          >
                            ≈ 2.2
                          </text>
                          <text
                            class="mafs-shadow"
                            dominant-baseline="middle"
                            filter="url(#background)"
                            font-size="16"
                            font-weight="bold"
                            style="fill: var(--mafs-fg); vector-effect: non-scaling-stroke;"
                            text-anchor="middle"
                            x="114.28572"
                            y="-171.42858"
                          >
                            ≈ 2.2
                          </text>
                          <text
                            class="mafs-shadow"
                            dominant-baseline="middle"
                            filter="url(#background)"
                            font-size="16"
                            font-weight="bold"
                            style="fill: var(--mafs-fg); vector-effect: non-scaling-stroke;"
                            text-anchor="middle"
                            x="142.85715"
                            y="-114.28572"
                          >
                            2
                          </text>
                          <polygon
                            aria-disabled="false"
                            aria-label="A polygon with 3 points."
                            aria-live="off"
                            class="movable-polygon"
                            fill-opacity="0.15"
                            points="3.5 2 2.5 4 1.5 2"
                            role="button"
                            stroke-linejoin="round"
                            stroke-width="44"
                            style="fill: transparent; fill-opacity: 0.15; stroke: transparent; stroke-opacity: 1; vector-effect: non-scaling-stroke; transform: var(--mafs-view-transform); cursor: grab;"
                            tabindex="0"
                          />
                          <g>
                            <g
                              aria-describedby=":r3:-angle-0 :r3:-point-0-side-1 :r3:-point-0-side-2"
                              aria-disabled="false"
                              aria-label="Point 1 at 3.5 comma 2."
                              aria-live="off"
                              class="movable-point__focusable-handle"
                              data-testid="movable-point__focusable-handle"
                              role="button"
                              tabindex="0"
                            />
                            <g
                              aria-hidden="true"
                              class="movable-point"
                              data-testid="movable-point"
                              style="--movable-point-color: var(--mafs-blue);"
                            >
                              <circle
                                class="movable-point-hitbox"
                                cx="200"
                                cy="-114.28571428571429"
                                r="24"
                              />
                              <circle
                                class="movable-point-halo"
                                cx="200"
                                cy="-114.28571428571429"
                              />
                              <circle
                                class="movable-point-ring"
                                cx="200"
                                cy="-114.28571428571429"
                              />
                              <circle
                                class="movable-point-focus-outline"
                                cx="200"
                                cy="-114.28571428571429"
                              />
                              <circle
                                class="movable-point-center"
                                cx="200"
                                cy="-114.28571428571429"
                                data-testid="movable-point__center"
                                style="fill: var(--mafs-blue);"
                              />
                            </g>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":r3:-angle-0"
                              >
                                Angle approximately equal to 63.4 degrees.
                              </span>
                            </foreignobject>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":r3:-point-0-side-1"
                              >
                                A line segment, length equal to 2 units, connects to point 3.
                              </span>
                            </foreignobject>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":r3:-point-0-side-2"
                              >
                                A line segment, length approximately equal to 2.2 units, connects to point 2.
                              </span>
                            </foreignobject>
                          </g>
                          <g>
                            <g
                              aria-describedby=":r3:-angle-1 :r3:-point-1-side-1 :r3:-point-1-side-2"
                              aria-disabled="false"
                              aria-label="Point 2 at 2.5 comma 4."
                              aria-live="off"
                              class="movable-point__focusable-handle"
                              data-testid="movable-point__focusable-handle"
                              role="button"
                              tabindex="0"
                            />
                            <g
                              aria-hidden="true"
                              class="movable-point"
                              data-testid="movable-point"
                              style="--movable-point-color: var(--mafs-blue);"
                            >
                              <circle
                                class="movable-point-hitbox"
                                cx="142.85714285714286"
                                cy="-228.57142857142858"
                                r="24"
                              />
                              <circle
                                class="movable-point-halo"
                                cx="142.85714285714286"
                                cy="-228.57142857142858"
                              />
                              <circle
                                class="movable-point-ring"
                                cx="142.85714285714286"
                                cy="-228.57142857142858"
                              />
                              <circle
                                class="movable-point-focus-outline"
                                cx="142.85714285714286"
                                cy="-228.57142857142858"
                              />
                              <circle
                                class="movable-point-center"
                                cx="142.85714285714286"
                                cy="-228.57142857142858"
                                data-testid="movable-point__center"
                                style="fill: var(--mafs-blue);"
                              />
                            </g>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":r3:-angle-1"
                              >
                                Angle approximately equal to 53.1 degrees.
                              </span>
                            </foreignobject>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":r3:-point-1-side-1"
                              >
                                A line segment, length approximately equal to 2.2 units, connects to point 1.
                              </span>
                            </foreignobject>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":r3:-point-1-side-2"
                              >
                                A line segment, length approximately equal to 2.2 units, connects to point 3.
                              </span>
                            </foreignobject>
                          </g>
                          <g>
                            <g
                              aria-describedby=":r3:-angle-2 :r3:-point-2-side-1 :r3:-point-2-side-2"
                              aria-disabled="false"
                              aria-label="Point 3 at 1.5 comma 2."
                              aria-live="off"
                              class="movable-point__focusable-handle"
                              data-testid="movable-point__focusable-handle"
                              role="button"
                              tabindex="0"
                            />
                            <g
                              aria-hidden="true"
                              class="movable-point"
                              data-testid="movable-point"
                              style="--movable-point-color: var(--mafs-blue);"
                            >
                              <circle
                                class="movable-point-hitbox"
                                cx="85.71428571428572"
                                cy="-114.28571428571429"
                                r="24"
                              />
                              <circle
                                class="movable-point-halo"
                                cx="85.71428571428572"
                                cy="-114.28571428571429"
                              />
                              <circle
                                class="movable-point-ring"
                                cx="85.71428571428572"
                                cy="-114.28571428571429"
                              />
                              <circle
                                class="movable-point-focus-outline"
                                cx="85.71428571428572"
                                cy="-114.28571428571429"
                              />
                              <circle
                                class="movable-point-center"
                                cx="85.71428571428572"
                                cy="-114.28571428571429"
                                data-testid="movable-point__center"
                                style="fill: var(--mafs-blue);"
                              />
                            </g>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":r3:-angle-2"
                              >
                                Angle approximately equal to 63.4 degrees.
                              </span>
                            </foreignobject>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":r3:-point-2-side-1"
                              >
                                A line segment, length approximately equal to 2.2 units, connects to point 2.
                              </span>
                            </foreignobject>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":r3:-point-2-side-2"
                              >
                                A line segment, length equal to 2 units, connects to point 1.
                              </span>
                            </foreignobject>
                          </g>
                          <foreignobject>
                            <span
                              aria-hidden="true"
                              id=":r3:-points-num"
                            >
                              The polygon has 3 points.
                            </span>
                          </foreignobject>
                        </g>
                      </svg>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
         
      </div>
    </div>
  </div>
</div>
`;

exports[`Interactive Graph question Should render predictably: first render 2`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        <strong>
          Plot the image of triangle 
          <span
            style="white-space: nowrap;"
          >
            <span />
            <span
              class="mock-TeX"
            >
              \\triangle ABC
            </span>
            <span />
          </span>
           under a reflection across line 
          <span
            style="white-space: nowrap;"
          >
            <span />
            <span
              class="mock-TeX"
            >
              \\ell
            </span>
            <span />
          </span>
          .
        </strong>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="1"
    >
      <div
        class="paragraph"
      >
        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="default_xu2jcg mafs-graph-container"
          >
            <div
              aria-describedby="interactive-graph-interactive-elements-description-:ra: instructions-:ra:"
              class="default_xu2jcg-o_O-inlineStyles_1t4cnlh mafs-graph"
              tabindex="0"
            >
              <div
                class="default_xu2jcg mafs-sr-only"
                id="interactive-graph-interactive-elements-description-:ra:"
                tabindex="-1"
              >
                Interactive elements: A polygon with 3 points. 
              </div>
              <div
                class="default_xu2jcg mafs-sr-only"
                id="instructions-:ra:"
                tabindex="-1"
              >
                Use the Tab key to move through the interactive elements in the graph. When an interactive element has focus, use Control + Shift + Arrows to move it.
              </div>
              <div
                class="default_xu2jcg-o_O-inlineStyles_16155wj"
              >
                <div
                  class="unresponsive-svg-image"
                  style="width: 400px; height: 400px;"
                >
                  <span
                    style="top: 0px; left: 0px; width: 100%; height: 100%; position: absolute; min-width: 20px; display: flex; justify-content: center; align-content: center;"
                  >
                    <div
                      class="default_xu2jcg-o_O-spinnerContainer_agrn11"
                    >
                      <svg
                        height="48"
                        viewBox="0 0 48 48"
                        width="48"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          class="loadingSpinner_1dvgeb3-o_O-inlineStyles_8njutv"
                          d="M44.19 23.455a1.91 1.91 0 1 1 3.801 0h.003c.004.18.006.363.006.545 0 13.255-10.745 24-24 24S0 37.255 0 24 10.745 0 24 0c.182 0 .364.002.545.006V.01a1.91 1.91 0 1 1 0 3.801v.015A20.564 20.564 0 0 0 24 3.818C12.854 3.818 3.818 12.854 3.818 24c0 11.146 9.036 20.182 20.182 20.182 11.146 0 20.182-9.036 20.182-20.182 0-.182-.003-.364-.007-.545h.015z"
                          fill-rule="nonzero"
                        />
                      </svg>
                    </div>
                  </span>
                </div>
              </div>
              <div
                class="default_xu2jcg-o_O-inlineStyles_16155wj"
              >
                <div
                  aria-hidden="true"
                  class="default_xu2jcg"
                >
                  <div
                    class="MafsView"
                    style="width: 400px; height: 400px;"
                    tabindex="-1"
                  >
                    <svg
                      height="400"
                      preserveAspectRatio="xMidYMin"
                      style="width: 400px; --mafs-view-transform: matrix(25, 0, 0, -25, 0, 0); --mafs-user-transform: translate(0, 0);"
                      viewBox="-200 -200 400 400"
                      width="400"
                    >
                      <defs>
                        <filter
                          height="100%"
                          id="background"
                          width="110%"
                          x="-5%"
                          y="0%"
                        >
                          <feflood
                            flood-color="#FFF"
                            flood-opacity="0.64"
                          />
                          <fecomposite
                            in="SourceGraphic"
                            operator="over"
                          />
                        </filter>
                      </defs>
                      <svg
                        height="400"
                        preserveAspectRatio="xMidYMin"
                        viewBox="-200 -200 400 400"
                        width="400"
                        x="-200"
                        y="-200"
                      />
                    </svg>
                  </div>
                </div>
                <div
                  class="default_xu2jcg-o_O-inlineStyles_ay4wjb"
                >
                  <div
                    class="MafsView"
                    style="width: 400px; height: 400px;"
                    tabindex="-1"
                  >
                    <svg
                      height="400"
                      preserveAspectRatio="xMidYMin"
                      style="width: 400px; --mafs-view-transform: matrix(25, 0, 0, -25, 0, 0); --mafs-user-transform: translate(0, 0);"
                      viewBox="-200 -200 400 400"
                      width="400"
                    >
                      <svg
                        height="400"
                        preserveAspectRatio="xMidYMin"
                        viewBox="-200 -200 400 400"
                        width="400"
                        x="-200"
                        y="-200"
                      >
                        <g
                          aria-describedby=":rb:-points-num :rb:-points"
                          aria-label="A polygon."
                        >
                          <polygon
                            aria-hidden="true"
                            fill-opacity="0.15"
                            points="3 -2 0 3 -3 -2"
                            stroke-linejoin="round"
                            stroke-width="var(--movable-line-stroke-weight)"
                            style="fill: transparent; fill-opacity: 0.15; stroke: var(--mafs-blue); stroke-opacity: 1; vector-effect: non-scaling-stroke; transform: var(--mafs-view-transform);"
                          />
                          <polygon
                            aria-disabled="false"
                            aria-label="A polygon with 3 points."
                            aria-live="off"
                            class="movable-polygon"
                            fill-opacity="0.15"
                            points="3 -2 0 3 -3 -2"
                            role="button"
                            stroke-linejoin="round"
                            stroke-width="44"
                            style="fill: transparent; fill-opacity: 0.15; stroke: transparent; stroke-opacity: 1; vector-effect: non-scaling-stroke; transform: var(--mafs-view-transform); cursor: grab;"
                            tabindex="0"
                          />
                          <g>
                            <g
                              aria-describedby=":rb:-angle-0 :rb:-point-0-side-1 :rb:-point-0-side-2"
                              aria-disabled="false"
                              aria-label="Point 1 at 3 comma -2."
                              aria-live="off"
                              class="movable-point__focusable-handle"
                              data-testid="movable-point__focusable-handle"
                              role="button"
                              tabindex="0"
                            />
                            <g
                              aria-hidden="true"
                              class="movable-point"
                              data-testid="movable-point"
                              style="--movable-point-color: var(--mafs-blue);"
                            >
                              <circle
                                class="movable-point-hitbox"
                                cx="75"
                                cy="50"
                                r="24"
                              />
                              <circle
                                class="movable-point-halo"
                                cx="75"
                                cy="50"
                              />
                              <circle
                                class="movable-point-ring"
                                cx="75"
                                cy="50"
                              />
                              <circle
                                class="movable-point-focus-outline"
                                cx="75"
                                cy="50"
                              />
                              <circle
                                class="movable-point-center"
                                cx="75"
                                cy="50"
                                data-testid="movable-point__center"
                                style="fill: var(--mafs-blue);"
                              />
                            </g>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":rb:-angle-0"
                              >
                                Angle approximately equal to 59 degrees.
                              </span>
                            </foreignobject>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":rb:-point-0-side-1"
                              >
                                A line segment, length equal to 6 units, connects to point 3.
                              </span>
                            </foreignobject>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":rb:-point-0-side-2"
                              >
                                A line segment, length approximately equal to 5.8 units, connects to point 2.
                              </span>
                            </foreignobject>
                          </g>
                          <g>
                            <g
                              aria-describedby=":rb:-angle-1 :rb:-point-1-side-1 :rb:-point-1-side-2"
                              aria-disabled="false"
                              aria-label="Point 2 at 0 comma 3."
                              aria-live="off"
                              class="movable-point__focusable-handle"
                              data-testid="movable-point__focusable-handle"
                              role="button"
                              tabindex="0"
                            />
                            <g
                              aria-hidden="true"
                              class="movable-point"
                              data-testid="movable-point"
                              style="--movable-point-color: var(--mafs-blue);"
                            >
                              <circle
                                class="movable-point-hitbox"
                                cx="0"
                                cy="-75"
                                r="24"
                              />
                              <circle
                                class="movable-point-halo"
                                cx="0"
                                cy="-75"
                              />
                              <circle
                                class="movable-point-ring"
                                cx="0"
                                cy="-75"
                              />
                              <circle
                                class="movable-point-focus-outline"
                                cx="0"
                                cy="-75"
                              />
                              <circle
                                class="movable-point-center"
                                cx="0"
                                cy="-75"
                                data-testid="movable-point__center"
                                style="fill: var(--mafs-blue);"
                              />
                            </g>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":rb:-angle-1"
                              >
                                Angle approximately equal to 61.9 degrees.
                              </span>
                            </foreignobject>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":rb:-point-1-side-1"
                              >
                                A line segment, length approximately equal to 5.8 units, connects to point 1.
                              </span>
                            </foreignobject>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":rb:-point-1-side-2"
                              >
                                A line segment, length approximately equal to 5.8 units, connects to point 3.
                              </span>
                            </foreignobject>
                          </g>
                          <g>
                            <g
                              aria-describedby=":rb:-angle-2 :rb:-point-2-side-1 :rb:-point-2-side-2"
                              aria-disabled="false"
                              aria-label="Point 3 at -3 comma -2."
                              aria-live="off"
                              class="movable-point__focusable-handle"
                              data-testid="movable-point__focusable-handle"
                              role="button"
                              tabindex="0"
                            />
                            <g
                              aria-hidden="true"
                              class="movable-point"
                              data-testid="movable-point"
                              style="--movable-point-color: var(--mafs-blue);"
                            >
                              <circle
                                class="movable-point-hitbox"
                                cx="-75"
                                cy="50"
                                r="24"
                              />
                              <circle
                                class="movable-point-halo"
                                cx="-75"
                                cy="50"
                              />
                              <circle
                                class="movable-point-ring"
                                cx="-75"
                                cy="50"
                              />
                              <circle
                                class="movable-point-focus-outline"
                                cx="-75"
                                cy="50"
                              />
                              <circle
                                class="movable-point-center"
                                cx="-75"
                                cy="50"
                                data-testid="movable-point__center"
                                style="fill: var(--mafs-blue);"
                              />
                            </g>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":rb:-angle-2"
                              >
                                Angle approximately equal to 59 degrees.
                              </span>
                            </foreignobject>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":rb:-point-2-side-1"
                              >
                                A line segment, length approximately equal to 5.8 units, connects to point 2.
                              </span>
                            </foreignobject>
                            <foreignobject>
                              <span
                                aria-hidden="true"
                                id=":rb:-point-2-side-2"
                              >
                                A line segment, length equal to 6 units, connects to point 1.
                              </span>
                            </foreignobject>
                          </g>
                          <foreignobject>
                            <span
                              aria-hidden="true"
                              id=":rb:-points-num"
                            >
                              The polygon has 3 points.
                            </span>
                          </foreignobject>
                        </g>
                      </svg>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Interactive Graph question Should render predictably: first render 3`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        We want to find the zeros of this polynomial:
      </div>
    </div>
    <div
      class="paragraph perseus-paragraph-centered"
      data-perseus-paragraph-index="1"
    >
      <div
        class="perseus-block-math"
      >
        <div
          class="perseus-block-math-inner"
          style="overflow-x: auto; overflow-y: hidden; padding-top: 10px; padding-bottom: 10px; margin-top: -10px; margin-bottom: -10px;"
        >
          <span
            class="mock-TeX"
          >
            p(x)=x(2x+5)(x+1)
          </span>
        </div>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="2"
    >
      <div
        class="paragraph"
      >
        <strong>
          Plot all the zeros (
          <span
            style="white-space: nowrap;"
          >
            <span />
            <span
              class="mock-TeX"
            >
              x
            </span>
            <span />
          </span>
          -intercepts) of the polynomial in the interactive graph.
        </strong>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="3"
    >
      <div
        class="paragraph"
      >
        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="default_xu2jcg mafs-graph-container"
          >
            <div
              aria-describedby="interactive-graph-interactive-elements-description-:rh: unlimited-graph-keyboard-prompt-:rh: instructions-:rh:"
              class="default_xu2jcg-o_O-inlineStyles_1t4cnlh mafs-graph"
              tabindex="0"
            >
              <div
                class="default_xu2jcg mafs-sr-only"
                id="interactive-graph-interactive-elements-description-:rh:"
                tabindex="-1"
              >
                No interactive elements
              </div>
              <div
                class="default_xu2jcg mafs-sr-only"
                id="instructions-:rh:"
                tabindex="-1"
              >
                Press Shift + Enter to interact with the graph. Use the Tab key to move through the interactive elements in the graph and access the graph Action Bar. When an interactive element has focus, use Control + Shift + Arrows to move it or use the Delete key to remove it from the graph. Use the buttons in the Action Bar to add or adjust elements within the graph.
              </div>
              <div
                class="default_xu2jcg-o_O-inlineStyles_16155wj"
              >
                <div
                  class="unresponsive-svg-image"
                  style="width: 425px; height: 425px;"
                >
                  <span
                    style="top: 0px; left: 0px; width: 100%; height: 100%; position: absolute; min-width: 20px; display: flex; justify-content: center; align-content: center;"
                  >
                    <div
                      class="default_xu2jcg-o_O-spinnerContainer_agrn11"
                    >
                      <svg
                        height="48"
                        viewBox="0 0 48 48"
                        width="48"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          class="loadingSpinner_1dvgeb3-o_O-inlineStyles_8njutv"
                          d="M44.19 23.455a1.91 1.91 0 1 1 3.801 0h.003c.004.18.006.363.006.545 0 13.255-10.745 24-24 24S0 37.255 0 24 10.745 0 24 0c.182 0 .364.002.545.006V.01a1.91 1.91 0 1 1 0 3.801v.015A20.564 20.564 0 0 0 24 3.818C12.854 3.818 3.818 12.854 3.818 24c0 11.146 9.036 20.182 20.182 20.182 11.146 0 20.182-9.036 20.182-20.182 0-.182-.003-.364-.007-.545h.015z"
                          fill-rule="nonzero"
                        />
                      </svg>
                    </div>
                  </span>
                </div>
              </div>
              <div
                class="default_xu2jcg-o_O-inlineStyles_16155wj"
              >
                <div
                  aria-hidden="true"
                  class="default_xu2jcg"
                >
                  <div
                    class="MafsView"
                    style="width: 400px; height: 400px;"
                    tabindex="-1"
                  >
                    <svg
                      height="400"
                      preserveAspectRatio="xMidYMin"
                      style="width: 400px; --mafs-view-transform: matrix(50, 0, 0, -50, 0, 0); --mafs-user-transform: translate(0, 0);"
                      viewBox="-200 -200 400 400"
                      width="400"
                    >
                      <defs>
                        <filter
                          height="100%"
                          id="background"
                          width="110%"
                          x="-5%"
                          y="0%"
                        >
                          <feflood
                            flood-color="#FFF"
                            flood-opacity="0.64"
                          />
                          <fecomposite
                            in="SourceGraphic"
                            operator="over"
                          />
                        </filter>
                      </defs>
                      <svg
                        height="400"
                        preserveAspectRatio="xMidYMin"
                        viewBox="-200 -200 400 400"
                        width="400"
                        x="-200"
                        y="-200"
                      />
                    </svg>
                  </div>
                </div>
                <div
                  class="default_xu2jcg-o_O-inlineStyles_ay4wjb"
                >
                  <div
                    class="MafsView"
                    style="width: 400px; height: 400px;"
                    tabindex="-1"
                  >
                    <svg
                      height="400"
                      preserveAspectRatio="xMidYMin"
                      style="width: 400px; --mafs-view-transform: matrix(50, 0, 0, -50, 0, 0); --mafs-user-transform: translate(0, 0);"
                      viewBox="-200 -200 400 400"
                      width="400"
                    >
                      <svg
                        height="400"
                        preserveAspectRatio="xMidYMin"
                        viewBox="-200 -200 400 400"
                        width="400"
                        x="-200"
                        y="-200"
                      >
                        <rect
                          height="400"
                          style="fill: rgba(0,0,0,0); cursor: crosshair;"
                          width="400"
                          x="-200"
                          y="-200"
                        />
                      </svg>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="default_xu2jcg-o_O-inlineStyles_1ufx1c5"
            >
              <button
                aria-disabled="false"
                class="button_vr44p2-o_O-reset_1ck4g21-o_O-shared_1jpsfmg-o_O-default_qlqm6z-o_O-inlineStyles_16y2sco"
                id="perseus_mafs_remove_button"
                role="button"
                tabindex="-1"
                type="button"
              >
                <span
                  class="text_f1191h-o_O-LabelLarge_16mkhxv-o_O-text_1kowsup"
                >
                  Remove Point
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Interactive Graph question Should render predictably: first render 4`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        The graph below contains quadrilateral 
        <span
          style="white-space: nowrap;"
        >
          <span />
          <span
            class="mock-TeX"
          >
            TREK
          </span>
          <span />
        </span>
         and the point 
        <span
          style="white-space: nowrap;"
        >
          <span />
          <span
            class="mock-TeX"
          >
            P(-8,-6)
          </span>
          <span />
        </span>
        .  
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="1"
    >
      <div
        class="paragraph"
      >
        <strong>
          Draw the image of quadrilateral 
          <span
            style="white-space: nowrap;"
          >
            <span />
            <span
              class="mock-TeX"
            >
              TREK
            </span>
            <span />
          </span>
           under a dilation whose center is 
          <span
            style="white-space: nowrap;"
          >
            <span />
            <span
              class="mock-TeX"
            >
              P
            </span>
            <span />
          </span>
           and scale factor is 
          <span
            style="white-space: nowrap;"
          >
            <span />
            <span
              class="mock-TeX"
            >
              3
            </span>
            <span />
          </span>
          .
        </strong>
          
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="2"
    >
      <div
        class="paragraph"
      >
        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="default_xu2jcg mafs-graph-container"
          >
            <div
              aria-describedby="unlimited-graph-keyboard-prompt-:rm: instructions-:rm:"
              class="default_xu2jcg-o_O-inlineStyles_1t4cnlh mafs-graph"
              tabindex="0"
            >
              <div
                class="default_xu2jcg mafs-sr-only"
                id="instructions-:rm:"
                tabindex="-1"
              >
                Press Shift + Enter to interact with the graph. Use the Tab key to move through the interactive elements in the graph and access the graph Action Bar. When an interactive element has focus, use Control + Shift + Arrows to move it or use the Delete key to remove it from the graph. Use the buttons in the Action Bar to add or adjust elements within the graph.
              </div>
              <div
                class="default_xu2jcg-o_O-inlineStyles_16155wj"
              >
                <div
                  class="unresponsive-svg-image"
                  style="width: 425px; height: 425px;"
                >
                  <span
                    style="top: 0px; left: 0px; width: 100%; height: 100%; position: absolute; min-width: 20px; display: flex; justify-content: center; align-content: center;"
                  >
                    <div
                      class="default_xu2jcg-o_O-spinnerContainer_agrn11"
                    >
                      <svg
                        height="48"
                        viewBox="0 0 48 48"
                        width="48"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          class="loadingSpinner_1dvgeb3-o_O-inlineStyles_8njutv"
                          d="M44.19 23.455a1.91 1.91 0 1 1 3.801 0h.003c.004.18.006.363.006.545 0 13.255-10.745 24-24 24S0 37.255 0 24 10.745 0 24 0c.182 0 .364.002.545.006V.01a1.91 1.91 0 1 1 0 3.801v.015A20.564 20.564 0 0 0 24 3.818C12.854 3.818 3.818 12.854 3.818 24c0 11.146 9.036 20.182 20.182 20.182 11.146 0 20.182-9.036 20.182-20.182 0-.182-.003-.364-.007-.545h.015z"
                          fill-rule="nonzero"
                        />
                      </svg>
                    </div>
                  </span>
                </div>
              </div>
              <div
                class="default_xu2jcg-o_O-inlineStyles_16155wj"
              >
                <div
                  aria-hidden="true"
                  class="default_xu2jcg"
                >
                  <div
                    class="MafsView"
                    style="width: 400px; height: 400px;"
                    tabindex="-1"
                  >
                    <svg
                      height="400"
                      preserveAspectRatio="xMidYMin"
                      style="width: 400px; --mafs-view-transform: matrix(16.66667, 0, 0, -16.66667, 0, 0); --mafs-user-transform: translate(0, 0);"
                      viewBox="-200 -266.6666666667 400 400"
                      width="400"
                    >
                      <defs>
                        <filter
                          height="100%"
                          id="background"
                          width="110%"
                          x="-5%"
                          y="0%"
                        >
                          <feflood
                            flood-color="#FFF"
                            flood-opacity="0.64"
                          />
                          <fecomposite
                            in="SourceGraphic"
                            operator="over"
                          />
                        </filter>
                      </defs>
                      <svg
                        height="400"
                        preserveAspectRatio="xMidYMin"
                        viewBox="-200 -266.66666666666663 400 400"
                        width="400"
                        x="-200"
                        y="-266.66666666666663"
                      />
                    </svg>
                  </div>
                </div>
                <div
                  class="default_xu2jcg-o_O-inlineStyles_ay4wjb"
                >
                  <div
                    class="MafsView"
                    style="width: 400px; height: 400px;"
                    tabindex="-1"
                  >
                    <svg
                      height="400"
                      preserveAspectRatio="xMidYMin"
                      style="width: 400px; --mafs-view-transform: matrix(16.66667, 0, 0, -16.66667, 0, 0); --mafs-user-transform: translate(0, 0);"
                      viewBox="-200 -266.6666666667 400 400"
                      width="400"
                    >
                      <svg
                        height="400"
                        preserveAspectRatio="xMidYMin"
                        viewBox="-200 -266.66666666666663 400 400"
                        width="400"
                        x="-200"
                        y="-266.66666666666663"
                      >
                        <g
                          aria-describedby=":rn:-points-num :rn:-points"
                          aria-label="An empty coordinate plane."
                        >
                          <polyline
                            aria-hidden="true"
                            fill-opacity="0"
                            points=""
                            stroke-linejoin="round"
                            stroke-width="var(--movable-line-stroke-weight)"
                            style="fill: transparent; fill-opacity: 0; stroke: var(--mafs-blue); stroke-opacity: 1; vector-effect: non-scaling-stroke; transform: var(--mafs-view-transform);"
                          />
                          <rect
                            aria-hidden="true"
                            height="400"
                            style="fill: rgba(0,0,0,0); cursor: crosshair;"
                            width="400"
                            x="-200"
                            y="-266.6666666666667"
                          />
                        </g>
                      </svg>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="default_xu2jcg-o_O-inlineStyles_1ufx1c5"
            >
              <button
                aria-disabled="true"
                class="button_vr44p2-o_O-reset_1ck4g21-o_O-shared_1jpsfmg-o_O-default_qlqm6z-o_O-disabled_1y6fils-o_O-inlineStyles_1kc2paq"
                id="perseus_mafs_remove_button"
                role="button"
                tabindex="-1"
                type="button"
              >
                <span
                  class="text_f1191h-o_O-LabelLarge_16mkhxv-o_O-text_1kowsup"
                >
                  Remove Point
                </span>
              </button>
              <button
                aria-disabled="true"
                class="button_vr44p2-o_O-reset_1ck4g21-o_O-shared_1jpsfmg-o_O-default_1l1gmds-o_O-disabled_1y6fils-o_O-inlineStyles_1kc2paq"
                role="button"
                tabindex="-1"
                type="button"
              >
                <span
                  class="text_f1191h-o_O-LabelLarge_16mkhxv-o_O-text_1kowsup"
                >
                  Close shape
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

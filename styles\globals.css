@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Custom Properties for Responsive Scaling */
:root {
  --scale-factor: 1;
  --content-scale: 1;
  --base-font-size: calc(16px * var(--content-scale));
  --base-spacing: calc(16px * var(--content-scale));
  --base-border-radius: calc(8px * var(--content-scale));
  --base-icon-size: calc(24px * var(--content-scale));
  --base-button-height: calc(40px * var(--content-scale));
  --base-card-padding: calc(24px * var(--content-scale));
  --base-gap: calc(16px * var(--content-scale));
}

/* Responsive scaling variables - now handled by --content-scale */
@media (max-width: 1920px) and (min-width: 1600px) {
  :root {
    --scale-factor: 0.9;
    --content-scale: 0.9;
  }
}

@media (max-width: 1599px) and (min-width: 1400px) {
  :root {
    --scale-factor: 0.8;
    --content-scale: 0.8;
  }
}

@media (max-width: 1399px) and (min-width: 1200px) {
  :root {
    --scale-factor: 0.7;
    --content-scale: 0.7;
  }
}

@media (max-width: 1199px) and (min-width: 1000px) {
  :root {
    --scale-factor: 0.6;
    --content-scale: 0.6;
  }
}

@media (max-width: 999px) {
  :root {
    --scale-factor: 0.5;
    --content-scale: 0.5;
  }
}

/* Ensure base styles are applied */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Poppins', sans-serif;
  font-size: var(--base-font-size);
  line-height: 1.5;
}

#__next {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

.main-background {
  background-color: #0a1423;
  background-image: radial-gradient(ellipse at center, rgba(17, 36, 63, 0.9) 0%, #0a1423 70%);
}

/* Global Responsive Classes */
.responsive-text {
  font-size: var(--base-font-size);
}

.responsive-text-sm {
  font-size: calc(var(--base-font-size) * 0.875);
}

.responsive-text-xs {
  font-size: calc(var(--base-font-size) * 0.75);
}

.responsive-text-lg {
  font-size: calc(var(--base-font-size) * 1.125);
}

.responsive-text-xl {
  font-size: calc(var(--base-font-size) * 1.25);
}

.responsive-text-2xl {
  font-size: calc(var(--base-font-size) * 1.5);
}

.responsive-spacing {
  padding: var(--base-spacing);
}

.responsive-spacing-sm {
  padding: calc(var(--base-spacing) * 0.5);
}

.responsive-spacing-lg {
  padding: calc(var(--base-spacing) * 1.5);
}

.responsive-gap {
  gap: var(--base-gap);
}

.responsive-gap-sm {
  gap: calc(var(--base-gap) * 0.5);
}

.responsive-gap-lg {
  gap: calc(var(--base-gap) * 1.5);
}

.responsive-border-radius {
  border-radius: var(--base-border-radius);
}

.responsive-icon {
  width: var(--base-icon-size);
  height: var(--base-icon-size);
}

.responsive-button {
  height: var(--base-button-height);
  padding: 0 var(--base-spacing);
  font-size: var(--base-font-size);
  border-radius: var(--base-border-radius);
}

.responsive-card {
  padding: var(--base-card-padding);
  border-radius: calc(var(--base-border-radius) * 1.5);
}

/* Dashboard Auto-Scale - Scales ALL content including text, icons, spacing */
.dashboard-auto-scale {
  transform-origin: top left;
  width: 100vw !important;
  height: 100vh !important;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
  transition: transform 0.3s ease-out;
}

/* Scale everything - content, text, icons, spacing */
@media (max-width: 1920px) and (min-width: 1600px) {
  .dashboard-auto-scale {
    transform: scale(0.9);
    width: calc(100vw / 0.9) !important;
    height: calc(100vh / 0.9) !important;
  }
  :root {
    --content-scale: 0.9;
  }
}

@media (max-width: 1599px) and (min-width: 1400px) {
  .dashboard-auto-scale {
    transform: scale(0.8);
    width: calc(100vw / 0.8) !important;
    height: calc(100vh / 0.8) !important;
  }
  :root {
    --content-scale: 0.8;
  }
}

@media (max-width: 1399px) and (min-width: 1200px) {
  .dashboard-auto-scale {
    transform: scale(0.7);
    width: calc(100vw / 0.7) !important;
    height: calc(100vh / 0.7) !important;
  }
  :root {
    --content-scale: 0.7;
  }
}

@media (max-width: 1199px) and (min-width: 1000px) {
  .dashboard-auto-scale {
    transform: scale(0.6);
    width: calc(100vw / 0.6) !important;
    height: calc(100vh / 0.6) !important;
  }
  :root {
    --content-scale: 0.6;
  }
}

@media (max-width: 999px) {
  .dashboard-auto-scale {
    transform: scale(0.5);
    width: calc(100vw / 0.5) !important;
    height: calc(100vh / 0.5) !important;
  }
  :root {
    --content-scale: 0.5;
  }
}

/* Default content scale */
:root {
  --content-scale: 1;
}

/* Universal content scaling - applies to ALL elements */
* {
  font-size: inherit;
}

/* Scale all text elements */
h1, h2, h3, h4, h5, h6, p, span, div, button, input, textarea, select, label, a {
  font-size: calc(1em * var(--content-scale));
}

/* Scale all icons and images */
svg, img, .icon {
  width: calc(var(--base-icon-size));
  height: calc(var(--base-icon-size));
}

/* Scale all spacing */
.p-1 { padding: calc(4px * var(--content-scale)) !important; }
.p-2 { padding: calc(8px * var(--content-scale)) !important; }
.p-3 { padding: calc(12px * var(--content-scale)) !important; }
.p-4 { padding: calc(16px * var(--content-scale)) !important; }
.p-5 { padding: calc(20px * var(--content-scale)) !important; }
.p-6 { padding: calc(24px * var(--content-scale)) !important; }

.m-1 { margin: calc(4px * var(--content-scale)) !important; }
.m-2 { margin: calc(8px * var(--content-scale)) !important; }
.m-3 { margin: calc(12px * var(--content-scale)) !important; }
.m-4 { margin: calc(16px * var(--content-scale)) !important; }
.m-5 { margin: calc(20px * var(--content-scale)) !important; }
.m-6 { margin: calc(24px * var(--content-scale)) !important; }

/* Scale gaps */
.gap-1 { gap: calc(4px * var(--content-scale)) !important; }
.gap-2 { gap: calc(8px * var(--content-scale)) !important; }
.gap-3 { gap: calc(12px * var(--content-scale)) !important; }
.gap-4 { gap: calc(16px * var(--content-scale)) !important; }
.gap-5 { gap: calc(20px * var(--content-scale)) !important; }
.gap-6 { gap: calc(24px * var(--content-scale)) !important; }

/* Scale border radius */
.rounded { border-radius: calc(4px * var(--content-scale)) !important; }
.rounded-md { border-radius: calc(6px * var(--content-scale)) !important; }
.rounded-lg { border-radius: calc(8px * var(--content-scale)) !important; }
.rounded-xl { border-radius: calc(12px * var(--content-scale)) !important; }
.rounded-2xl { border-radius: calc(16px * var(--content-scale)) !important; }

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.3s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

/* Override Tailwind text sizes with content scaling */
.text-xs { font-size: calc(12px * var(--content-scale)) !important; }
.text-sm { font-size: calc(14px * var(--content-scale)) !important; }
.text-base { font-size: calc(16px * var(--content-scale)) !important; }
.text-lg { font-size: calc(18px * var(--content-scale)) !important; }
.text-xl { font-size: calc(20px * var(--content-scale)) !important; }
.text-2xl { font-size: calc(24px * var(--content-scale)) !important; }
.text-3xl { font-size: calc(30px * var(--content-scale)) !important; }
.text-4xl { font-size: calc(36px * var(--content-scale)) !important; }

/* Override Tailwind width/height for icons */
.w-3 { width: calc(12px * var(--content-scale)) !important; }
.w-4 { width: calc(16px * var(--content-scale)) !important; }
.w-5 { width: calc(20px * var(--content-scale)) !important; }
.w-6 { width: calc(24px * var(--content-scale)) !important; }
.w-8 { width: calc(32px * var(--content-scale)) !important; }
.w-10 { width: calc(40px * var(--content-scale)) !important; }
.w-12 { width: calc(48px * var(--content-scale)) !important; }

.h-3 { height: calc(12px * var(--content-scale)) !important; }
.h-4 { height: calc(16px * var(--content-scale)) !important; }
.h-5 { height: calc(20px * var(--content-scale)) !important; }
.h-6 { height: calc(24px * var(--content-scale)) !important; }
.h-8 { height: calc(32px * var(--content-scale)) !important; }
.h-10 { height: calc(40px * var(--content-scale)) !important; }
.h-12 { height: calc(48px * var(--content-scale)) !important; }

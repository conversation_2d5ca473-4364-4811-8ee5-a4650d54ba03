@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Custom Properties for Responsive Scaling - Optimized for Content Fit */
:root {
  --scale-factor: 1;
  --content-scale: 3;
  --base-font-size: calc(14px * var(--content-scale));
  --base-spacing: calc(6px * var(--content-scale));
  --base-border-radius: calc(8px * var(--content-scale));
  --base-icon-size: calc(20px * var(--content-scale));
  --base-button-height: calc(36px * var(--content-scale));
  --base-card-padding: calc(1px * var(--content-scale));
  --base-gap: calc(6px * var(--content-scale));
  --header-height: calc(60px * var(--content-scale));
  --footer-height: calc(50px * var(--content-scale));
  --sidebar-width: calc(80px * var(--content-scale));

  /* Unified Dark Theme Color Palette */
  --primary-bg: #0f1419;
  --secondary-bg: #1a1f2e;
  --tertiary-bg: #252b3d;

  /* Glassmorphism Dark Theme */
  --glass-bg: rgba(26, 31, 46, 0.7);
  --glass-bg-light: rgba(37, 43, 61, 0.6);
  --glass-border: rgba(100, 116, 139, 0.2);
  --glass-border-glow: rgba(100, 116, 139, 0.4);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 2px 8px rgba(0, 0, 0, 0.2);

  /* Consistent Text Colors */
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --text-accent: #64748b;

  /* Unified Accent Colors */
  --accent-blue: #3b82f6;
  --accent-cyan: #06b6d4;
  --accent-green: #10b981;
  --accent-yellow: #f59e0b;
  --accent-orange: #f97316;
  --accent-red: #ef4444;
  --accent-purple: #8b5cf6;
  --accent-pink: #ec4899;
}

/* Responsive scaling variables - now handled by --content-scale */
@media (max-width: 1920px) and (min-width: 1600px) {
  :root {
    --scale-factor: 0.9;
    --content-scale: 0.9;
  }
}

@media (max-width: 1599px) and (min-width: 1400px) {
  :root {
    --scale-factor: 0.8;
    --content-scale: 0.8;
  }
}

@media (max-width: 1399px) and (min-width: 1200px) {
  :root {
    --scale-factor: 0.7;
    --content-scale: 0.7;
  }
}

@media (max-width: 1199px) and (min-width: 1000px) {
  :root {
    --scale-factor: 0.6;
    --content-scale: 0.6;
  }
}

@media (max-width: 999px) {
  :root {
    --scale-factor: 0.5;
    --content-scale: 0.5;
  }
}

/* Ensure base styles are applied */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  margin: 0;
  padding: 0;
  background: var(--primary-bg);
  color: var(--text-primary);
  font-family: 'Poppins', sans-serif;
}

body {
  font-family: 'Poppins', sans-serif;
  font-size: var(--base-font-size);
  line-height: 1.5;
  background: var(--primary-bg);
  color: var(--text-primary);
}

#__next {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

.main-background {
  background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 50%, var(--tertiary-bg) 100%);
  background-attachment: fixed;
  position: relative;
}

.main-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at 20% 30%, rgba(209, 160, 255, 0.1) 0%, transparent 50%),
              radial-gradient(ellipse at 80% 70%, rgba(188, 19, 254, 0.08) 0%, transparent 50%),
              radial-gradient(ellipse at 50% 50%, rgba(41, 52, 95, 0.05) 0%, transparent 70%);
  pointer-events: none;
}

/* Global Responsive Classes */
.responsive-text {
  font-size: var(--base-font-size);
}

.responsive-text-sm {
  font-size: calc(var(--base-font-size) * 0.875);
}

.responsive-text-xs {
  font-size: calc(var(--base-font-size) * 0.75);
}

.responsive-text-lg {
  font-size: calc(var(--base-font-size) * 1.125);
}

.responsive-text-xl {
  font-size: calc(var(--base-font-size) * 1.25);
}

.responsive-text-2xl {
  font-size: calc(var(--base-font-size) * 1.5);
}

.responsive-spacing {
  padding: var(--base-spacing);
}

.responsive-spacing-sm {
  padding: calc(var(--base-spacing) * 0.5);
}

.responsive-spacing-lg {
  padding: calc(var(--base-spacing) * 1.5);
}

.responsive-gap {
  gap: var(--base-gap);
}

.responsive-gap-sm {
  gap: calc(var(--base-gap) * 0.5);
}

.responsive-gap-lg {
  gap: calc(var(--base-gap) * 1.5);
}

.responsive-border-radius {
  border-radius: var(--base-border-radius);
}

.responsive-icon {
  width: var(--base-icon-size);
  height: var(--base-icon-size);
}

.responsive-button {
  height: var(--base-button-height);
  padding: 0 var(--base-spacing);
  font-size: var(--base-font-size);
  border-radius: var(--base-border-radius);
}

.responsive-card {
  padding: var(--base-card-padding);
  border-radius: calc(var(--base-border-radius) * 1.5);
}

/* Enhanced HUD Card System */
.hud-card {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: calc(var(--base-border-radius) * 1.5);
  backdrop-filter: blur(20px);
  box-shadow: var(--glass-shadow);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.hud-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--accent-blue), transparent);
  opacity: 0.6;
}

.hud-card:hover {
  border-color: var(--glass-border-glow);
  box-shadow: 0 8px 40px rgba(59, 130, 246, 0.15), var(--glass-shadow);
  transform: translateY(-2px);
}

/* Compact HUD card padding */
.hud-card .ant-card-head {
  padding: calc(var(--base-spacing) * 0.75) calc(var(--base-spacing) * 1.5) !important;
  min-height: auto !important;
}

.hud-card .ant-card-body {
  padding: calc(var(--base-spacing) * 1) !important;
}

/* Compact metric cards */
.metric-card {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--base-border-radius);
  padding: calc(var(--base-spacing) * 0.3);
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 60px;
  max-height: 80px;
  height: fit-content;
  backdrop-filter: blur(20px);
  box-shadow: var(--glass-shadow);
}

.metric-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 207, 255, 0.05) 0%, rgba(0, 255, 189, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.metric-card:hover::after {
  opacity: 1;
}

.metric-card:hover {
  border-color: var(--accent-blue);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
  transform: scale(1.02);
}

/* Compact metric card text */
.metric-card p {
  margin-bottom: calc(var(--base-spacing) * 0.1) !important;
  line-height: 1.1;
  font-size: calc(var(--base-font-size) * 0.7) !important;
  color: var(--text-secondary) !important;
}

.metric-card .text-xl {
  font-size: calc(var(--base-font-size) * 1.1) !important;
  line-height: 1.0;
  margin-bottom: calc(var(--base-spacing) * 0.1) !important;
  color: var(--text-primary) !important;
  font-weight: 700 !important;
}

/* Compact metric card icons */
.metric-card .w-10.h-10 {
  width: calc(var(--base-icon-size) * 1.5) !important;
  height: calc(var(--base-icon-size) * 1.5) !important;
  margin-bottom: calc(var(--base-spacing) * 0.5) !important;
}

/* Beautiful gradient animation */
@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Status badges with glow effects */
.status-badge {
  padding: calc(var(--base-spacing) * 0.5) calc(var(--base-spacing) * 1);
  border-radius: calc(var(--base-border-radius) * 2);
  font-size: calc(10px * var(--content-scale));
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.status-badge.active {
  background: rgba(16, 185, 129, 0.2);
  color: var(--accent-green);
  border-color: var(--accent-green);
  box-shadow: 0 0 10px rgba(16, 185, 129, 0.3);
}

.status-badge.beta {
  background: rgba(245, 158, 11, 0.2);
  color: var(--accent-yellow);
  border-color: var(--accent-yellow);
  box-shadow: 0 0 10px rgba(245, 158, 11, 0.3);
}

.status-badge.live {
  background: rgba(59, 130, 246, 0.2);
  color: var(--accent-blue);
  border-color: var(--accent-blue);
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
}

.status-badge.testing {
  background: rgba(139, 92, 246, 0.2);
  color: var(--accent-purple);
  border-color: var(--accent-purple);
  box-shadow: 0 0 10px rgba(139, 92, 246, 0.3);
}

/* Enhanced Header Styling */
.header-hud {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  position: relative;
}

.header-hud::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--accent-blue), transparent);
  opacity: 0.4;
}

/* Enhanced Department Buttons */
.dept-button {
  background: var(--glass-bg-light);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.dept-button:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: var(--glass-border-glow);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
  transform: scale(1.05);
}

.dept-button.active {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(6, 182, 212, 0.1) 100%);
  border-color: var(--accent-blue);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Panel Background Utilities */
.bg-panel-bg {
  background: var(--glass-bg) !important;
}

.bg-container-bg {
  background: var(--secondary-bg) !important;
}

.dept-button.active::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

/* Dashboard Auto-Scale - Scales ALL content including text, icons, spacing */
.dashboard-auto-scale {
  transform-origin: top left;
  width: 100vw !important;
  height: 100vh !important;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
  transition: transform 0.3s ease-out;
}

/* Scale everything - content, text, icons, spacing */
@media (max-width: 1920px) and (min-width: 1600px) {
  .dashboard-auto-scale {
    transform: scale(0.9);
    width: calc(100vw / 0.9) !important;
    height: calc(100vh / 0.9) !important;
  }
  :root {
    --content-scale: 0.9;
  }
}

@media (max-width: 1599px) and (min-width: 1400px) {
  .dashboard-auto-scale {
    transform: scale(0.8);
    width: calc(100vw / 0.8) !important;
    height: calc(100vh / 0.8) !important;
  }
  :root {
    --content-scale: 0.8;
  }
}

@media (max-width: 1399px) and (min-width: 1200px) {
  .dashboard-auto-scale {
    transform: scale(0.7);
    width: calc(100vw / 0.7) !important;
    height: calc(100vh / 0.7) !important;
  }
  :root {
    --content-scale: 0.7;
  }
}

@media (max-width: 1199px) and (min-width: 1000px) {
  .dashboard-auto-scale {
    transform: scale(0.6);
    width: calc(100vw / 0.6) !important;
    height: calc(100vh / 0.6) !important;
  }
  :root {
    --content-scale: 0.6;
  }
}

@media (max-width: 999px) {
  .dashboard-auto-scale {
    transform: scale(0.5);
    width: calc(100vw / 0.5) !important;
    height: calc(100vh / 0.5) !important;
  }
  :root {
    --content-scale: 0.5;
  }
}

/* Default content scale */
:root {
  --content-scale: 1;
}

/* Universal content scaling - applies to ALL elements */
* {
  font-size: inherit;
}

/* Scale all text elements in main content */
.main-section h1, .main-section h2, .main-section h3, .main-section h4, .main-section h5, .main-section h6,
.main-section p, .main-section span, .main-section div, .main-section button,
.main-section input, .main-section textarea, .main-section select, .main-section label, .main-section a {
  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;
}

/* Scale card content specifically */
.card-content * {
  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;
}

/* Scale chart text and numbers */
.recharts-text, .recharts-label, .chart-text, .metric-value, .metric-label {
  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;
}

/* Scale all icons and images */
svg, img, .icon {
  width: calc(var(--base-icon-size));
  height: calc(var(--base-icon-size));
}

/* Scale all spacing */
.p-1 { padding: calc(4px * var(--content-scale)) !important; }
.p-2 { padding: calc(8px * var(--content-scale)) !important; }
.p-3 { padding: calc(12px * var(--content-scale)) !important; }
.p-4 { padding: calc(16px * var(--content-scale)) !important; }
.p-5 { padding: calc(20px * var(--content-scale)) !important; }
.p-6 { padding: calc(24px * var(--content-scale)) !important; }

.m-1 { margin: calc(4px * var(--content-scale)) !important; }
.m-2 { margin: calc(8px * var(--content-scale)) !important; }
.m-3 { margin: calc(12px * var(--content-scale)) !important; }
.m-4 { margin: calc(16px * var(--content-scale)) !important; }
.m-5 { margin: calc(20px * var(--content-scale)) !important; }
.m-6 { margin: calc(24px * var(--content-scale)) !important; }

/* Scale gaps */
.gap-1 { gap: calc(4px * var(--content-scale)) !important; }
.gap-2 { gap: calc(8px * var(--content-scale)) !important; }
.gap-3 { gap: calc(12px * var(--content-scale)) !important; }
.gap-4 { gap: calc(16px * var(--content-scale)) !important; }
.gap-5 { gap: calc(20px * var(--content-scale)) !important; }
.gap-6 { gap: calc(24px * var(--content-scale)) !important; }

/* Scale border radius */
.rounded { border-radius: calc(4px * var(--content-scale)) !important; }
.rounded-md { border-radius: calc(6px * var(--content-scale)) !important; }
.rounded-lg { border-radius: calc(8px * var(--content-scale)) !important; }
.rounded-xl { border-radius: calc(12px * var(--content-scale)) !important; }
.rounded-2xl { border-radius: calc(16px * var(--content-scale)) !important; }

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.3s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

/* Override Tailwind text sizes with content scaling */
.text-xs { font-size: calc(12px * var(--content-scale)) !important; }
.text-sm { font-size: calc(14px * var(--content-scale)) !important; }
.text-base { font-size: calc(16px * var(--content-scale)) !important; }
.text-lg { font-size: calc(18px * var(--content-scale)) !important; }
.text-xl { font-size: calc(20px * var(--content-scale)) !important; }
.text-2xl { font-size: calc(24px * var(--content-scale)) !important; }
.text-3xl { font-size: calc(30px * var(--content-scale)) !important; }
.text-4xl { font-size: calc(36px * var(--content-scale)) !important; }

/* Override Tailwind width/height for icons */
.w-3 { width: calc(12px * var(--content-scale)) !important; }
.w-4 { width: calc(16px * var(--content-scale)) !important; }
.w-5 { width: calc(20px * var(--content-scale)) !important; }
.w-6 { width: calc(24px * var(--content-scale)) !important; }
.w-8 { width: calc(32px * var(--content-scale)) !important; }
.w-10 { width: calc(40px * var(--content-scale)) !important; }
.w-12 { width: calc(48px * var(--content-scale)) !important; }

.h-3 { height: calc(12px * var(--content-scale)) !important; }
.h-4 { height: calc(16px * var(--content-scale)) !important; }
.h-5 { height: calc(20px * var(--content-scale)) !important; }
.h-6 { height: calc(24px * var(--content-scale)) !important; }
.h-8 { height: calc(32px * var(--content-scale)) !important; }
.h-10 { height: calc(40px * var(--content-scale)) !important; }
.h-12 { height: calc(48px * var(--content-scale)) !important; }

/* Optimized Layout Classes for Perfect Content Fit */
.main-layout {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content-area {
  flex: 1;
  display: flex;
  min-height: 0;
  overflow: hidden;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

.header-section {
  height: var(--header-height);
  flex-shrink: 0;
  padding: calc(var(--base-spacing) * 0.5);
}

.main-section {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  overflow-x: hidden;
  padding: calc(var(--base-spacing) * 0.5);
}

.footer-section {
  height: calc(var(--footer-height) * 1.5);
  flex-shrink: 0;
  padding: calc(var(--base-spacing) * 0.5);
}

.sidebar-left {
  width: var(--sidebar-width);
  flex-shrink: 0;
}

.sidebar-right {
  width: var(--sidebar-width);
  flex-shrink: 0;
}

/* Enhanced Dashboard Grid for No-Scroll Layout */
.dashboard-grid {
  display: grid;
  gap: calc(var(--base-gap) * 0.75);
  height: 100%;
  overflow: hidden;
  padding: calc(var(--base-spacing) * 0.5);
}

.dashboard-grid-teacher {
  grid-template-rows: auto 1fr auto;
  grid-template-columns: 1fr;
}

.dashboard-grid-school {
  grid-template-rows: auto 1fr;
  grid-template-columns: 1fr 1fr;
  gap: calc(var(--base-gap) * 1);
}

/* Compact content areas */
.content-compact {
  max-height: calc(100vh - var(--header-height) - var(--footer-height) - calc(var(--base-spacing) * 4));
  overflow-y: auto;
}

.content-no-scroll {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Enhanced Animations */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(0, 207, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 207, 255, 0.6), 0 0 30px rgba(0, 207, 255, 0.4);
  }
}

@keyframes slide-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-scale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-slide-in-up {
  animation: slide-in-up 0.5s ease-out;
}

.animate-fade-in-scale {
  animation: fade-in-scale 0.3s ease-out;
}

/* Hover Effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(59, 130, 246, 0.2);
}

/* Progress Bar Animations */
.progress-bar {
  position: relative;
  overflow: hidden;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Optimized Card Grid */
.card-grid {
  display: grid;
  gap: var(--base-gap);
  height: 100%;
  overflow: hidden;
}

.card-grid-2 {
  grid-template-columns: 1fr 1fr;
}

.card-grid-3 {
  grid-template-columns: 1fr 1fr 1fr;
}

.card-grid-4 {
  grid-template-columns: 1fr 1fr 1fr 1fr;
}

.card-grid-auto {
  grid-template-columns: repeat(auto-fit, minmax(calc(250px * var(--content-scale)), 1fr));
}

/* Optimized Card Sizing */
.card-compact {
  padding: calc(var(--base-card-padding) * 0.75);
  min-height: calc(120px * var(--content-scale));
}

.card-standard {
  padding: var(--base-card-padding);
  min-height: calc(160px * var(--content-scale));
}

.card-large {
  padding: calc(var(--base-card-padding) * 1.25);
  min-height: calc(200px * var(--content-scale));
}

/* Neumorphic HUD Subnav Styles */
.subnav-hud {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: calc(var(--base-spacing) * 1.5) 0;
  margin: 0 auto;
  max-width: calc(72px * 8 + var(--base-gap) * 7); /* 8 buttons with gaps */
}

.subnav-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: calc(72px * var(--content-scale));
  height: calc(72px * var(--content-scale));
  margin: 0 calc(var(--base-gap) * 0.5);
  border-radius: calc(16px * var(--content-scale));
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  box-shadow: var(--glass-shadow);
  border: 1px solid var(--glass-border);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.subnav-button:hover {
  transform: translateY(calc(-2px * var(--content-scale)));
  background: var(--glass-bg-light);
  border-color: var(--glass-border-glow);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 2px 8px rgba(59, 130, 246, 0.2);
}

.subnav-button.active {
  background: var(--glass-bg-light);
  border: 1px solid var(--accent-blue);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3), var(--glass-shadow);
}

.subnav-icon {
  width: calc(24px * var(--content-scale));
  height: calc(24px * var(--content-scale));
  margin-bottom: calc(4px * var(--content-scale));
  color: var(--text-muted);
  transition: color 0.3s ease;
}

.subnav-button:hover .subnav-icon,
.subnav-button.active .subnav-icon {
  color: var(--accent-blue);
}

.subnav-label {
  font-size: calc(10px * var(--content-scale));
  font-weight: 500;
  color: var(--text-muted);
  text-align: center;
  line-height: 1.2;
  transition: color 0.3s ease;
}

.subnav-button:hover .subnav-label,
.subnav-button.active .subnav-label {
  color: var(--accent-blue);
}

/* HUD Glow Effect */
.subnav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: inherit;
}

.subnav-button:hover::before,
.subnav-button.active::before {
  opacity: 1;
}

/* Collapsible Sidebar Styles */
.sidebar-toggle {
  position: fixed;
  top: calc(var(--base-spacing) * 2);
  left: calc(var(--base-spacing) * 2);
  z-index: 50;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(20px);
  border-radius: var(--base-border-radius);
  padding: calc(var(--base-spacing) * 0.75);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--glass-shadow);
}

.sidebar-toggle:hover {
  background: var(--glass-bg-light);
  transform: scale(1.05);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 2px 8px rgba(59, 130, 246, 0.2);
}

.collapsible-sidebar {
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  background: var(--glass-bg);
  border-right: 1px solid var(--glass-border);
  backdrop-filter: blur(20px);
  z-index: 40;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--glass-shadow);
  width: calc(280px * var(--content-scale));
}

.collapsible-sidebar.collapsed {
  transform: translateX(-100%);
}

.collapsible-sidebar.expanded {
  transform: translateX(0);
}

.sidebar-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(2px);
  z-index: 30;
}

/* Function Button Styles */
.function-button {
  width: 100%;
  display: flex;
  align-items: center;
  gap: calc(var(--base-spacing) * 2);
  padding: calc(var(--base-spacing) * 2);
  border-radius: calc(var(--base-border-radius) * 1.5);
  background: var(--glass-bg-light);
  border: 1px solid var(--glass-border);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.function-button:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: var(--glass-border-glow);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 2px 8px rgba(59, 130, 246, 0.2);
}

.function-button.active {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(6, 182, 212, 0.1) 100%);
  border-color: var(--accent-blue);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.function-button-icon {
  font-size: calc(24px * var(--content-scale));
  line-height: 1;
}

.function-button-content {
  flex: 1;
  text-align: left;
}

.function-button-title {
  font-weight: 600;
  font-size: calc(16px * var(--content-scale));
  color: var(--text-primary);
  margin-bottom: calc(var(--base-spacing) * 0.25);
}

.function-button-description {
  font-size: calc(12px * var(--content-scale));
  color: var(--text-muted);
}

.function-button.active .function-button-title {
  color: var(--accent-blue);
}

.function-button-indicator {
  width: calc(8px * var(--content-scale));
  height: calc(8px * var(--content-scale));
  border-radius: 50%;
  background: transparent;
  transition: background-color 0.3s ease;
}

.function-button.active .function-button-indicator {
  background: var(--accent-blue);
}

/* Enhanced HUD Vertical Layout for Left Sidebar */
.subnav-hud-vertical {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  gap: calc(var(--base-spacing) * 0.5);
  padding: calc(var(--base-spacing) * 0.75);
  height: 100%;
  overflow: hidden;
}

/* Enhanced subnav button with glassmorphism */
.subnav-hud-vertical .subnav-button {
  width: 100%;
  height: calc(36px * var(--content-scale));
  margin: 0;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: calc(var(--base-spacing) * 0.75) calc(var(--base-spacing) * 1);
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--base-border-radius);
  backdrop-filter: blur(20px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: var(--glass-shadow);
}

.subnav-hud-vertical .subnav-button:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: var(--glass-border-glow);
  transform: translateX(4px);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
}

.subnav-hud-vertical .subnav-button.active {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(6, 182, 212, 0.1) 100%);
  border-color: var(--accent-blue);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Enhanced subnav icon */
.subnav-hud-vertical .subnav-icon {
  width: calc(18px * var(--content-scale));
  height: calc(18px * var(--content-scale));
  margin-right: calc(var(--base-spacing) * 1.5);
  color: var(--text-secondary);
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.subnav-hud-vertical .subnav-button:hover .subnav-icon {
  color: var(--accent-blue);
  filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.6));
}

.subnav-hud-vertical .subnav-button.active .subnav-icon {
  color: var(--text-primary);
  filter: drop-shadow(0 0 12px rgba(59, 130, 246, 0.8));
}

/* Enhanced subnav label - full text display */
.subnav-hud-vertical .subnav-label {
  color: var(--text-primary);
  font-size: calc(12px * var(--content-scale));
  font-weight: 500;
  line-height: 1.2;
  white-space: nowrap;
  transition: all 0.3s ease;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);
}

.subnav-hud-vertical .subnav-button:hover .subnav-label {
  color: var(--text-primary);
  text-shadow: 0 0 8px rgba(59, 130, 246, 0.6);
}

.subnav-hud-vertical .subnav-button.active .subnav-label {
  color: var(--text-primary);
  font-weight: 600;
  text-shadow: 0 0 12px rgba(59, 130, 246, 0.8);
}

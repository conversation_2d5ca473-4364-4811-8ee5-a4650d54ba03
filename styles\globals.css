@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Custom Properties for Responsive Scaling */
:root {
  --scale-factor: 1;
  --base-font-size: 16px;
  --base-spacing: 16px;
  --base-border-radius: 8px;
  --base-icon-size: 24px;
  --base-button-height: 40px;
  --base-card-padding: 24px;
  --base-gap: 16px;
}

/* Responsive scaling variables */
@media (max-width: 1920px) and (min-width: 1600px) {
  :root {
    --scale-factor: 0.9;
    --base-font-size: 14px;
    --base-spacing: 14px;
    --base-border-radius: 7px;
    --base-icon-size: 22px;
    --base-button-height: 36px;
    --base-card-padding: 20px;
    --base-gap: 14px;
  }
}

@media (max-width: 1599px) and (min-width: 1400px) {
  :root {
    --scale-factor: 0.8;
    --base-font-size: 13px;
    --base-spacing: 12px;
    --base-border-radius: 6px;
    --base-icon-size: 20px;
    --base-button-height: 32px;
    --base-card-padding: 18px;
    --base-gap: 12px;
  }
}

@media (max-width: 1399px) and (min-width: 1200px) {
  :root {
    --scale-factor: 0.7;
    --base-font-size: 12px;
    --base-spacing: 10px;
    --base-border-radius: 5px;
    --base-icon-size: 18px;
    --base-button-height: 28px;
    --base-card-padding: 16px;
    --base-gap: 10px;
  }
}

@media (max-width: 1199px) and (min-width: 1000px) {
  :root {
    --scale-factor: 0.6;
    --base-font-size: 11px;
    --base-spacing: 8px;
    --base-border-radius: 4px;
    --base-icon-size: 16px;
    --base-button-height: 24px;
    --base-card-padding: 12px;
    --base-gap: 8px;
  }
}

@media (max-width: 999px) {
  :root {
    --scale-factor: 0.5;
    --base-font-size: 10px;
    --base-spacing: 6px;
    --base-border-radius: 3px;
    --base-icon-size: 14px;
    --base-button-height: 20px;
    --base-card-padding: 10px;
    --base-gap: 6px;
  }
}

/* Ensure base styles are applied */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100vh;
  width: 100vw;
  overflow-x: hidden;
  overflow-y: auto;
}

body {
  font-family: 'Poppins', sans-serif;
  font-size: var(--base-font-size);
  line-height: 1.5;
}

.main-background {
  background-color: #0a1423;
  background-image: radial-gradient(ellipse at center, rgba(17, 36, 63, 0.9) 0%, #0a1423 70%);
}

/* Global Responsive Classes */
.responsive-text {
  font-size: var(--base-font-size);
}

.responsive-text-sm {
  font-size: calc(var(--base-font-size) * 0.875);
}

.responsive-text-xs {
  font-size: calc(var(--base-font-size) * 0.75);
}

.responsive-text-lg {
  font-size: calc(var(--base-font-size) * 1.125);
}

.responsive-text-xl {
  font-size: calc(var(--base-font-size) * 1.25);
}

.responsive-text-2xl {
  font-size: calc(var(--base-font-size) * 1.5);
}

.responsive-spacing {
  padding: var(--base-spacing);
}

.responsive-spacing-sm {
  padding: calc(var(--base-spacing) * 0.5);
}

.responsive-spacing-lg {
  padding: calc(var(--base-spacing) * 1.5);
}

.responsive-gap {
  gap: var(--base-gap);
}

.responsive-gap-sm {
  gap: calc(var(--base-gap) * 0.5);
}

.responsive-gap-lg {
  gap: calc(var(--base-gap) * 1.5);
}

.responsive-border-radius {
  border-radius: var(--base-border-radius);
}

.responsive-icon {
  width: var(--base-icon-size);
  height: var(--base-icon-size);
}

.responsive-button {
  height: var(--base-button-height);
  padding: 0 var(--base-spacing);
  font-size: var(--base-font-size);
  border-radius: var(--base-border-radius);
}

.responsive-card {
  padding: var(--base-card-padding);
  border-radius: calc(var(--base-border-radius) * 1.5);
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.3s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

/* Auto-scaling dashboard to fit viewport */
.dashboard-container {
  transform-origin: top left;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

/* Responsive scaling based on viewport dimensions */
@media (max-width: 1920px) and (min-width: 1600px) {
  .dashboard-container {
    transform: scale(0.9);
  }
}

@media (max-width: 1599px) and (min-width: 1400px) {
  .dashboard-container {
    transform: scale(0.8);
  }
}

@media (max-width: 1399px) and (min-width: 1200px) {
  .dashboard-container {
    transform: scale(0.7);
  }
}

@media (max-width: 1199px) and (min-width: 1000px) {
  .dashboard-container {
    transform: scale(0.6);
  }
}

@media (max-width: 999px) {
  .dashboard-container {
    transform: scale(0.5);
  }
}

/* Ensure content fits without scrolling */
.auto-fit-content {
  max-height: 100vh;
  overflow: hidden;
}

/* Smooth scaling transitions */
.dashboard-container {
  transition: transform 0.3s ease-out;
}

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Ensure base styles are applied */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100vh;
  width: 100vw;
  overflow-x: hidden;
  overflow-y: auto;
}

body {
  font-family: 'Poppins', sans-serif;
  font-size: clamp(12px, 0.8vw, 16px);
}

/* Responsive scaling for different screen sizes */
@media (max-width: 1920px) {
  body {
    font-size: clamp(11px, 0.7vw, 14px);
  }
}

@media (max-width: 1600px) {
  body {
    font-size: clamp(10px, 0.65vw, 13px);
  }
}

@media (max-width: 1400px) {
  body {
    font-size: clamp(9px, 0.6vw, 12px);
  }
}

@media (max-width: 1200px) {
  body {
    font-size: clamp(8px, 0.55vw, 11px);
  }
}

.main-background {
  background-color: #0a1423;
  background-image: radial-gradient(ellipse at center, rgba(17, 36, 63, 0.9) 0%, #0a1423 70%);
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.3s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

/* Auto-scaling dashboard to fit viewport */
.dashboard-container {
  transform-origin: top left;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

/* Responsive scaling based on viewport dimensions */
@media (max-width: 1920px) and (min-width: 1600px) {
  .dashboard-container {
    transform: scale(0.9);
  }
}

@media (max-width: 1599px) and (min-width: 1400px) {
  .dashboard-container {
    transform: scale(0.8);
  }
}

@media (max-width: 1399px) and (min-width: 1200px) {
  .dashboard-container {
    transform: scale(0.7);
  }
}

@media (max-width: 1199px) and (min-width: 1000px) {
  .dashboard-container {
    transform: scale(0.6);
  }
}

@media (max-width: 999px) {
  .dashboard-container {
    transform: scale(0.5);
  }
}

/* Ensure content fits without scrolling */
.auto-fit-content {
  max-height: 100vh;
  overflow: hidden;
}

/* Smooth scaling transitions */
.dashboard-container {
  transition: transform 0.3s ease-out;
}

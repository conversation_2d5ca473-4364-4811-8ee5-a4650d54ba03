
import React, { useState, useEffect } from 'react';
import { SchoolDepartment } from '../../components/Header';
import { SchoolSubNav } from '../../components/SchoolSubNav';
import { SchoolContent } from './SchoolContent';

interface SchoolViewProps {
  activeDepartment: SchoolDepartment;
  setActiveDepartment: (department: SchoolDepartment) => void;
}

export const SchoolView: React.FC<SchoolViewProps> = ({
  activeDepartment,
  setActiveDepartment
}) => {
  const [activeSubSection, setActiveSubSection] = useState<string>('dashboard');

  // Reset sub-section when department changes
  useEffect(() => {
    setActiveSubSection('dashboard');
  }, [activeDepartment]);

  return (
    <div className="flex gap-3 h-full">
      {/* Left Sub-navigation */}
      <SchoolSubNav
        department={activeDepartment}
        activeSubSection={activeSubSection}
        setActiveSubSection={setActiveSubSection}
      />

      {/* Main Content */}
      <div className="flex-1 min-h-0 overflow-auto">
        <SchoolContent
          activeDepartment={activeDepartment}
          activeSubSection={activeSubSection}
        />
      </div>
    </div>
  );
};


import React, { useState, useEffect } from 'react';
import { SchoolDepartment } from '../../components/Header';
import { SchoolSubNav } from '../../components/SchoolSubNav';
import { SchoolContent } from './SchoolContent';

interface SchoolViewProps {
  activeDepartment: SchoolDepartment;
  setActiveDepartment: (department: SchoolDepartment) => void;
  activeSubSection?: string;
  setActiveSubSection?: (section: string) => void;
}

export const SchoolView: React.FC<SchoolViewProps> = ({
  activeDepartment,
  setActiveDepartment,
  activeSubSection: propActiveSubSection,
  setActiveSubSection: propSetActiveSubSection
}) => {
  const [localActiveSubSection, setLocalActiveSubSection] = useState<string>('dashboard');

  // Use prop values if provided, otherwise use local state
  const activeSubSection = propActiveSubSection || localActiveSubSection;
  const setActiveSubSection = propSetActiveSubSection || setLocalActiveSubSection;

  // Reset sub-section when department changes
  useEffect(() => {
    if (propSetActiveSubSection) {
      propSetActiveSubSection('dashboard');
    } else {
      setLocalActiveSubSection('dashboard');
    }
  }, [activeDepartment, propSetActiveSubSection]);

  return (
    <div className="flex gap-3 h-full">
      {/* Left Sub-navigation */}
      <SchoolSubNav
        department={activeDepartment}
        activeSubSection={activeSubSection}
        setActiveSubSection={setActiveSubSection}
      />

      {/* Main Content */}
      <div className="flex-1 min-h-0 overflow-auto">
        <SchoolContent
          activeDepartment={activeDepartment}
          activeSubSection={activeSubSection}
        />
      </div>
    </div>
  );
};

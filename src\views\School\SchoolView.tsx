
import React, { useState, useEffect } from 'react';
import { SchoolHeader, SchoolDepartment } from '../../components/SchoolHeader';
import { SchoolSubNav } from '../../components/SchoolSubNav';
import { SchoolContent } from './SchoolContent';

export const SchoolView: React.FC = () => {
  const [activeDepartment, setActiveDepartment] = useState<SchoolDepartment>('school');
  const [activeSubSection, setActiveSubSection] = useState<string>('dashboard');

  // Reset sub-section when department changes
  useEffect(() => {
    setActiveSubSection('dashboard');
  }, [activeDepartment]);

  return (
    <div className="flex flex-col gap-3 h-full">
      {/* Department Header Navigation */}
      <SchoolHeader
        activeDepartment={activeDepartment}
        setActiveDepartment={setActiveDepartment}
      />

      {/* Main Content Area with Sub-navigation */}
      <div className="flex gap-3 flex-1 min-h-0">
        {/* Left Sub-navigation */}
        <SchoolSubNav
          department={activeDepartment}
          activeSubSection={activeSubSection}
          setActiveSubSection={setActiveSubSection}
        />

        {/* Main Content */}
        <div className="flex-1 min-h-0 overflow-auto">
          <SchoolContent
            activeDepartment={activeDepartment}
            activeSubSection={activeSubSection}
          />
        </div>
      </div>
    </div>
  );
};

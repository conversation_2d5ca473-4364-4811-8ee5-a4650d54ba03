
import React, { useState, useEffect } from 'react';
import { CourseProgress, Course } from './CourseProgress';
import { UpcomingAssignments, Assignment } from './UpcomingAssignments';
import { Card } from '../../components/Card';

type AcademicOverview = {
  gpa: string;
  credits: number;
  assignmentsDue: number;
}

type SchoolData = {
  overview: AcademicOverview;
  courses: Course[];
  assignments: Assignment[];
}

export const SchoolView: React.FC = () => {
  const [data, setData] = useState<SchoolData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/school');
        const result = await response.json();
        setData(result);
      } catch (error) {
        console.error("Failed to fetch school data", error);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  if (loading) {
    return <div className="flex justify-center items-center h-full text-cyan-400">Loading Academic Data...</div>;
  }

  if (!data || !data.overview) {
    return <div className="flex justify-center items-center h-full text-red-400">Failed to load school data.</div>;
  }

  return (
    <div className="flex-grow grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="md:col-span-2">
        <Card title="Academic Overview">
          <div className="flex justify-around p-4 text-center">
            <div>
              <p className="text-2xl font-bold text-white">{data.overview.gpa}</p>
              <p className="text-sm text-gray-400">GPA</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-white">{data.overview.credits}</p>
              <p className="text-sm text-gray-400">Credits</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-white">{data.overview.assignmentsDue}</p>
              <p className="text-sm text-gray-400">Assignments Due</p>
            </div>
          </div>
        </Card>
      </div>
      <CourseProgress courses={data.courses} />
      <UpcomingAssignments assignments={data.assignments} />
    </div>
  );
};

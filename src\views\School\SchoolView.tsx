
import React, { useState, useEffect } from 'react';
import { Card } from '../../components/Card';
import { SchoolHeader, SchoolDepartment } from '../../components/SchoolHeader';
import { SchoolSubNav } from '../../components/SchoolSubNav';

// Import department content components
import { SchoolDashboard } from './departments/SchoolDashboard';
import { AdministrationDashboard } from './departments/AdministrationDashboard';
import { TeacherDashboard } from './departments/TeacherDashboard';
import { FinanceDashboard } from './departments/FinanceDashboard';
import { MarketingDashboard } from './departments/MarketingDashboard';
import { ParentDashboard } from './departments/ParentDashboard';
import { StudentDashboard } from './departments/StudentDashboard';
import { SettingDashboard } from './departments/SettingDashboard';

export const SchoolView: React.FC = () => {
  const [activeDepartment, setActiveDepartment] = useState<SchoolDepartment>('school');
  const [activeSubSection, setActiveSubSection] = useState<string>('dashboard');

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/school');
        const result = await response.json();
        setData(result);
      } catch (error) {
        console.error("Failed to fetch school data", error);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  if (loading) {
    return <div className="flex justify-center items-center h-full text-cyan-400">Loading Academic Data...</div>;
  }

  if (!data || !data.overview) {
    return <div className="flex justify-center items-center h-full text-red-400">Failed to load school data.</div>;
  }

  return (
    <div className="flex-grow grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="md:col-span-2">
        <Card title="Academic Overview">
          <div className="flex justify-around p-4 text-center">
            <div>
              <p className="text-2xl font-bold text-white">{data.overview.gpa}</p>
              <p className="text-sm text-gray-400">GPA</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-white">{data.overview.credits}</p>
              <p className="text-sm text-gray-400">Credits</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-white">{data.overview.assignmentsDue}</p>
              <p className="text-sm text-gray-400">Assignments Due</p>
            </div>
          </div>
        </Card>
      </div>
      <CourseProgress courses={data.courses} />
      <UpcomingAssignments assignments={data.assignments} />
    </div>
  );
};

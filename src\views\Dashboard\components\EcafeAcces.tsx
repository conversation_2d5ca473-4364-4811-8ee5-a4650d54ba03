
import React, { useState } from 'react';
import { Card } from '../../../components/Card';
import { ChevronRightIcon, MoreHorizIcon } from '../../../components/icons';

type Contact = {
    name: string;
    detail: string;
    avatar: string;
};

interface EcafeAccesProps {
    contacts: Contact[];
}

export const EcafeAcces: React.FC<EcafeAccesProps> = ({ contacts }) => {
    const [selectedContact, setSelectedContact] = useState(contacts.length > 0 ? contacts[0].name : '');

    if (!contacts || contacts.length === 0) {
        return <Card title="Direct Access" headerIcon={<MoreHorizIcon className="w-5 h-5"/>}><p className="text-gray-400 text-sm">No contacts available.</p></Card>;
    }

    return (
        <Card title="Direct Access" headerIcon={<MoreHorizIcon className="w-5 h-5"/>}>
            <ul className="space-y-1">
                {contacts.map((contact) => (
                    <li key={contact.name}>
                        <button 
                            onClick={() => setSelectedContact(contact.name)} 
                            className={`w-full flex items-center p-2 rounded-lg transition-colors text-left ${selectedContact === contact.name ? 'bg-cyan-400/20' : 'hover:bg-gray-700/30'}`}
                            aria-pressed={selectedContact === contact.name}
                        >
                            <img src={`https://i.pravatar.cc/150?u=${contact.avatar}`} alt={contact.name} className="w-8 h-8 sm:w-10 sm:h-10 rounded-full mr-2 sm:mr-3 border-2 border-gray-600" />
                            <div className="flex-grow">
                                <p className="font-semibold text-white text-sm sm:text-base">{contact.name}</p>
                                <p className="text-xs text-gray-400">{contact.detail}</p>
                            </div>
                            {selectedContact !== contact.name && <ChevronRightIcon className="w-5 h-5 text-gray-500 flex-shrink-0" />}
                        </button>
                    </li>
                ))}
            </ul>
        </Card>
    );
};

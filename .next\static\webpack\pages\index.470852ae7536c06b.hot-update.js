"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/views/School/departments/SchoolDashboard.tsx":
/*!**********************************************************!*\
  !*** ./src/views/School/departments/SchoolDashboard.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SchoolDashboard: function() { return /* binding */ SchoolDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../components/Card */ \"./src/components/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BuildingLibraryIcon,CalendarIcon,ChartBarIcon,UserGroupIcon!=!@heroicons/react/24/solid */ \"__barrel_optimize__?names=AcademicCapIcon,BuildingLibraryIcon,CalendarIcon,ChartBarIcon,UserGroupIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/solid/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst SchoolDashboard = (param)=>{\n    let { activeSubSection } = param;\n    _s();\n    const [schoolData, setSchoolData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [overviewData, setOverviewData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSchoolData = async ()=>{\n            try {\n                setLoading(true);\n                const response = await fetch(\"/api/school/dashboard\");\n                if (!response.ok) {\n                    throw new Error(\"Failed to fetch school data\");\n                }\n                const schoolData = await response.json();\n                setSchoolData(schoolData);\n            } catch (error) {\n                console.error(\"Failed to fetch school data:\", error);\n                // Fallback to mock data if API fails\n                const mockData = {\n                    overview: {\n                        totalStudents: 1247,\n                        totalTeachers: 89,\n                        totalClasses: 156,\n                        averageGPA: 3.67,\n                        aiTutorSessions: 2847,\n                        virtualRealityClasses: 23,\n                        blockchainCertificates: 156,\n                        carbonFootprint: 2.3\n                    },\n                    departments: [\n                        {\n                            name: \"AI & Robotics\",\n                            students: 312,\n                            teachers: 18,\n                            color: \"bg-blue-500/20 text-blue-400\",\n                            innovation: \"Neural Networks Lab\"\n                        },\n                        {\n                            name: \"Quantum Computing\",\n                            students: 298,\n                            teachers: 15,\n                            color: \"bg-green-500/20 text-green-400\",\n                            innovation: \"Quantum Simulator\"\n                        },\n                        {\n                            name: \"Bioengineering\",\n                            students: 267,\n                            teachers: 12,\n                            color: \"bg-purple-500/20 text-purple-400\",\n                            innovation: \"Gene Editing Lab\"\n                        },\n                        {\n                            name: \"Space Sciences\",\n                            students: 189,\n                            teachers: 11,\n                            color: \"bg-pink-500/20 text-pink-400\",\n                            innovation: \"Mars Simulation\"\n                        }\n                    ],\n                    innovations: [\n                        {\n                            title: \"AI-Powered Personalized Learning\",\n                            status: \"Active\",\n                            impact: \"94% improvement\",\n                            icon: \"\\uD83E\\uDD16\"\n                        },\n                        {\n                            title: \"Holographic Classrooms\",\n                            status: \"Beta\",\n                            impact: \"87% engagement\",\n                            icon: \"\\uD83D\\uDD2E\"\n                        },\n                        {\n                            title: \"Blockchain Credentials\",\n                            status: \"Live\",\n                            impact: \"100% verified\",\n                            icon: \"⛓️\"\n                        },\n                        {\n                            title: \"Emotion Recognition System\",\n                            status: \"Testing\",\n                            impact: \"92% accuracy\",\n                            icon: \"\\uD83D\\uDE0A\"\n                        }\n                    ],\n                    recentEvents: [\n                        {\n                            title: \"Global Virtual Science Fair\",\n                            date: \"March 15\",\n                            type: \"Innovation\",\n                            participants: 47\n                        },\n                        {\n                            title: \"AI Ethics Symposium\",\n                            date: \"March 20\",\n                            type: \"Academic\",\n                            participants: 234\n                        },\n                        {\n                            title: \"Metaverse Graduation Ceremony\",\n                            date: \"March 25\",\n                            type: \"Celebration\",\n                            participants: 1200\n                        }\n                    ]\n                };\n                setSchoolData(mockData);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchSchoolData();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-cyan-400\",\n                children: \"Loading School Dashboard...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                lineNumber: 78,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, undefined);\n    }\n    const renderContent = ()=>{\n        switch(activeSubSection){\n            case \"dashboard\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"\\uD83D\\uDE80 Future School Overview\",\n                            className: \"lg:col-span-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-4 p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.UserGroupIcon, {\n                                                    className: \"w-6 h-6 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: schoolData.overview.totalStudents\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Global Students\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.AcademicCapIcon, {\n                                                    className: \"w-6 h-6 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: schoolData.overview.totalTeachers\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"AI-Enhanced Teachers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.BuildingLibraryIcon, {\n                                                    className: \"w-6 h-6 text-purple-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: schoolData.overview.virtualRealityClasses\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"VR Classrooms\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.ChartBarIcon, {\n                                                    className: \"w-6 h-6 text-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: schoolData.overview.aiTutorSessions\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"AI Tutor Sessions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"\\uD83D\\uDD2C Innovation Hub\",\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 p-4\",\n                                children: schoolData.innovations.map((innovation, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg bg-gray-800/40 border border-gray-600/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg\",\n                                                                children: innovation.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 129,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-white\",\n                                                                children: innovation.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 130,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs \".concat(innovation.status === \"Active\" ? \"bg-green-500/20 text-green-400\" : innovation.status === \"Live\" ? \"bg-blue-500/20 text-blue-400\" : innovation.status === \"Beta\" ? \"bg-yellow-500/20 text-yellow-400\" : \"bg-purple-500/20 text-purple-400\"),\n                                                        children: innovation.status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: [\n                                                    \"Impact: \",\n                                                    innovation.impact\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"\\uD83C\\uDF1F Next-Gen Departments\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 p-4\",\n                                children: schoolData.departments.map((dept, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg \".concat(dept.color.split(\" \")[0], \" border border-gray-600/30\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold \".concat(dept.color.split(\" \").slice(1).join(\" \")),\n                                                            children: dept.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: [\n                                                                dept.students,\n                                                                \" students • \",\n                                                                dept.teachers,\n                                                                \" teachers\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-cyan-400 mt-1\",\n                                                            children: [\n                                                                \"\\uD83D\\uDE80 \",\n                                                                dept.innovation\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 rounded-lg \".concat(dept.color.split(\" \")[0], \" flex items-center justify-center\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.BuildingLibraryIcon, {\n                                                        className: \"w-4 h-4 \".concat(dept.color.split(\" \").slice(1).join(\" \"))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"\\uD83C\\uDF0D Global Events\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 p-4\",\n                                children: schoolData.recentEvents.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-3 p-3 rounded-lg hover:bg-gray-800/40 border border-gray-700/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.CalendarIcon, {\n                                                    className: \"w-5 h-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-white\",\n                                                        children: event.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: [\n                                                            event.date,\n                                                            \" • \",\n                                                            event.type\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mt-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-cyan-400\",\n                                                                children: [\n                                                                    \"\\uD83D\\uDC65 \",\n                                                                    event.participants,\n                                                                    \" participants\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 179,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"w-1 h-1 bg-gray-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-green-400\",\n                                                                children: \"\\uD83C\\uDF10 Global\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 11\n                }, undefined);\n            case \"overview\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"School Overview\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Comprehensive School Overview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Detailed school statistics and performance metrics will be displayed here.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 11\n                }, undefined);\n            case \"departments\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Department Management\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Department Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Manage all school departments, their staff, and resources.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 11\n                }, undefined);\n            case \"facilities\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"School Facilities\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Facilities Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Monitor and manage school facilities, maintenance, and resources.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 11\n                }, undefined);\n            case \"events\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"School Events\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Event Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Plan, organize, and track school events and activities.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 11\n                }, undefined);\n            case \"announcements\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Announcements\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"School Announcements\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Create and manage school-wide announcements and communications.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 11\n                }, undefined);\n            case \"performance\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Performance Analytics\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"School Performance\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Analyze school performance metrics and academic achievements.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 11\n                }, undefined);\n            case \"settings\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"School Settings\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"School Configuration\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Configure school settings, policies, and system preferences.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"School Dashboard\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Welcome to School Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Select a section from the navigation to get started.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full p-2\",\n        children: renderContent()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n        lineNumber: 274,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SchoolDashboard, \"THCBmSJmDMs6vK9PGUmEFl7QChc=\");\n_c = SchoolDashboard;\nvar _c;\n$RefreshReg$(_c, \"SchoolDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/School/departments/SchoolDashboard.tsx\n"));

/***/ })

});
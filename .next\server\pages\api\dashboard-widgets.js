"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/dashboard-widgets";
exports.ids = ["pages/api/dashboard-widgets"];
exports.modules = {

/***/ "@google/genai":
/*!********************************!*\
  !*** external "@google/genai" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("@google/genai");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdashboard-widgets&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdashboard-widgets.ts&middlewareConfigBase64=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdashboard-widgets&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdashboard-widgets.ts&middlewareConfigBase64=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_dashboard_widgets_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\dashboard-widgets.ts */ \"(api)/./pages/api/dashboard-widgets.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_dashboard_widgets_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_dashboard_widgets_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/dashboard-widgets\",\n        pathname: \"/api/dashboard-widgets\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_dashboard_widgets_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdashboard-widgets&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdashboard-widgets.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./backend/lib/gemini.ts":
/*!*******************************!*\
  !*** ./backend/lib/gemini.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Type: () => (/* reexport safe */ _google_genai__WEBPACK_IMPORTED_MODULE_0__.Type),\n/* harmony export */   generateStructuredData: () => (/* binding */ generateStructuredData),\n/* harmony export */   generateText: () => (/* binding */ generateText)\n/* harmony export */ });\n/* harmony import */ var _google_genai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/genai */ \"@google/genai\");\n/* harmony import */ var _google_genai__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_google_genai__WEBPACK_IMPORTED_MODULE_0__);\n\nconst hasValidApiKey =  false && 0;\nlet ai = null;\nif (hasValidApiKey) {\n    ai = new _google_genai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenAI({\n        apiKey: \"your_actual_gemini_api_key_here\"\n    });\n} else {\n    console.warn(\"⚠️  No valid Gemini API key found. Using fallback mock data. Set GEMINI_API_KEY in .env.local to use real AI features.\");\n}\nasync function generateText(prompt) {\n    if (!ai) {\n        // Fallback mock response for mood analysis\n        return \"System analysis indicates elevated stress levels detected. Recommend implementing relaxation protocols and scheduling wellness check-in within 2 hours.\";\n    }\n    try {\n        const response = await ai.models.generateContent({\n            model: \"gemini-2.5-flash\",\n            contents: prompt\n        });\n        return response.text;\n    } catch (e) {\n        console.error(\"Error generating text from Gemini:\", e);\n        // Return fallback data instead of throwing error\n        return \"System analysis temporarily unavailable. Default wellness protocols active. Please check back later for detailed analysis.\";\n    }\n}\nasync function generateStructuredData(prompt, schema) {\n    if (!ai) {\n        // Return fallback mock data for dashboard widgets\n        const fallbackData = {\n            contacts: [\n                {\n                    name: \"Dr. Aris Thorne\",\n                    detail: \"Chief Scientist\",\n                    avatar: \"aris\"\n                },\n                {\n                    name: \"Commander Zara Vex\",\n                    detail: \"Security Director\",\n                    avatar: \"zara\"\n                },\n                {\n                    name: \"Engineer Kai Nexus\",\n                    detail: \"Systems Architect\",\n                    avatar: \"kai\"\n                }\n            ],\n            safetyItems: [\n                {\n                    label: \"Firewall Active\",\n                    color: \"#00FFFF\"\n                },\n                {\n                    label: \"Quantum Encryption\",\n                    color: \"#00FFFF\"\n                },\n                {\n                    label: \"Bio-Scanner Online\",\n                    color: \"#3b82f6\"\n                },\n                {\n                    label: \"Sub-routine Anomaly\",\n                    color: \"#ef4444\"\n                },\n                {\n                    label: \"Neural Link Stable\",\n                    color: \"#3b82f6\"\n                },\n                {\n                    label: \"Core Temperature\",\n                    color: \"#3b82f6\"\n                }\n            ],\n            safetyChartData: [\n                85,\n                92,\n                78,\n                95,\n                88,\n                91\n            ],\n            systemRequests: [\n                {\n                    text: \"Security Patch Update\"\n                },\n                {\n                    text: \"Cryo-chamber diagnostics\"\n                },\n                {\n                    text: \"Neural interface calibration\"\n                }\n            ],\n            systemStats: [\n                {\n                    icon: \"BuildingOfficeIcon\",\n                    value: \"1597\",\n                    label: \"Processes\"\n                },\n                {\n                    icon: \"PuzzlePieceIcon\",\n                    value: \"84%\",\n                    label: \"RAM Usage\"\n                },\n                {\n                    icon: \"SnowflakeIcon\",\n                    value: \"24\\xb0C\",\n                    label: \"Core Temp\"\n                },\n                {\n                    icon: \"MemoryChipIcon\",\n                    value: \"2.4TB\",\n                    label: \"Storage\"\n                },\n                {\n                    icon: \"CogIcon\",\n                    value: \"99.7%\",\n                    label: \"Uptime\"\n                }\n            ]\n        };\n        return fallbackData;\n    }\n    try {\n        const response = await ai.models.generateContent({\n            model: \"gemini-2.5-flash\",\n            contents: prompt,\n            config: {\n                responseMimeType: \"application/json\",\n                responseSchema: schema\n            }\n        });\n        if (!response.text) {\n            throw new Error(\"Received an empty response from the AI.\");\n        }\n        const cleanedText = response.text.trim().replace(/^```json\\s*|```\\s*$/g, \"\");\n        if (!cleanedText) {\n            throw new Error(\"Received an empty JSON response from the AI after cleaning.\");\n        }\n        return JSON.parse(cleanedText);\n    } catch (e) {\n        console.error(\"Failed to generate or parse Gemini JSON response:\", e);\n        // Return fallback data instead of throwing error\n        const fallbackData = {\n            contacts: [\n                {\n                    name: \"Dr. Aris Thorne\",\n                    detail: \"Chief Scientist\",\n                    avatar: \"aris\"\n                },\n                {\n                    name: \"Commander Zara Vex\",\n                    detail: \"Security Director\",\n                    avatar: \"zara\"\n                },\n                {\n                    name: \"Engineer Kai Nexus\",\n                    detail: \"Systems Architect\",\n                    avatar: \"kai\"\n                }\n            ],\n            safetyItems: [\n                {\n                    label: \"Firewall Active\",\n                    color: \"#00FFFF\"\n                },\n                {\n                    label: \"Quantum Encryption\",\n                    color: \"#00FFFF\"\n                },\n                {\n                    label: \"Bio-Scanner Online\",\n                    color: \"#3b82f6\"\n                },\n                {\n                    label: \"Sub-routine Anomaly\",\n                    color: \"#ef4444\"\n                },\n                {\n                    label: \"Neural Link Stable\",\n                    color: \"#3b82f6\"\n                },\n                {\n                    label: \"Core Temperature\",\n                    color: \"#3b82f6\"\n                }\n            ],\n            safetyChartData: [\n                85,\n                92,\n                78,\n                95,\n                88,\n                91\n            ],\n            systemRequests: [\n                {\n                    text: \"Security Patch Update\"\n                },\n                {\n                    text: \"Cryo-chamber diagnostics\"\n                },\n                {\n                    text: \"Neural interface calibration\"\n                }\n            ],\n            systemStats: [\n                {\n                    icon: \"BuildingOfficeIcon\",\n                    value: \"1597\",\n                    label: \"Processes\"\n                },\n                {\n                    icon: \"PuzzlePieceIcon\",\n                    value: \"84%\",\n                    label: \"RAM Usage\"\n                },\n                {\n                    icon: \"SnowflakeIcon\",\n                    value: \"24\\xb0C\",\n                    label: \"Core Temp\"\n                },\n                {\n                    icon: \"MemoryChipIcon\",\n                    value: \"2.4TB\",\n                    label: \"Storage\"\n                },\n                {\n                    icon: \"CogIcon\",\n                    value: \"99.7%\",\n                    label: \"Uptime\"\n                }\n            ]\n        };\n        return fallbackData;\n    }\n}\n // Re-export Type for convenience\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./backend/lib/gemini.ts\n");

/***/ }),

/***/ "(api)/./pages/api/dashboard-widgets.ts":
/*!****************************************!*\
  !*** ./pages/api/dashboard-widgets.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../backend/lib/gemini */ \"(api)/./backend/lib/gemini.ts\");\n\nconst dashboardWidgetsSchema = {\n    type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.OBJECT,\n    properties: {\n        contacts: {\n            type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.ARRAY,\n            description: \"A list of exactly 3 high-level contacts for direct access in a futuristic corporation.\",\n            items: {\n                type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.OBJECT,\n                properties: {\n                    name: {\n                        type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                        description: \"Full name of the contact, e.g., 'Dr. Aris Thorne'.\"\n                    },\n                    detail: {\n                        type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                        description: \"Job title or role, e.g., 'Chief Scientist'.\"\n                    },\n                    avatar: {\n                        type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                        description: \"A unique, single-word seed for a placeholder avatar URL, e.g., 'aris'.\"\n                    }\n                },\n                required: [\n                    \"name\",\n                    \"detail\",\n                    \"avatar\"\n                ]\n            }\n        },\n        safetyItems: {\n            type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.ARRAY,\n            description: \"A list of exactly 6 system safety status items with appropriate sci-fi terminology.\",\n            items: {\n                type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.OBJECT,\n                properties: {\n                    label: {\n                        type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                        description: \"The safety status label, e.g., 'Firewall Active', 'Sub-routine Anomaly'.\"\n                    },\n                    color: {\n                        type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                        enum: [\n                            \"#3b82f6\",\n                            \"#ef4444\",\n                            \"#00FFFF\"\n                        ],\n                        description: \"Color code: blue (#3b82f6) for status, red (#ef4444) for warning, cyan (#00FFFF) for secure.\"\n                    }\n                },\n                required: [\n                    \"label\",\n                    \"color\"\n                ]\n            }\n        },\n        safetyChartData: {\n            type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.ARRAY,\n            description: \"A list of exactly 6 numbers between 30 and 100 representing bar chart heights.\",\n            items: {\n                type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.NUMBER\n            }\n        },\n        systemRequests: {\n            type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.ARRAY,\n            description: \"A list of exactly 3 recent system status updates or requests, with sci-fi themes.\",\n            items: {\n                type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.OBJECT,\n                properties: {\n                    text: {\n                        type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                        description: \"The description of the request, e.g., 'Security Patch Update', 'Cryo-chamber diagnostics'.\"\n                    }\n                },\n                required: [\n                    \"text\"\n                ]\n            }\n        },\n        systemStats: {\n            type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.ARRAY,\n            description: \"A list of exactly 5 system performance statistics.\",\n            items: {\n                type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.OBJECT,\n                properties: {\n                    icon: {\n                        type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                        enum: [\n                            \"BuildingOfficeIcon\",\n                            \"PuzzlePieceIcon\",\n                            \"SnowflakeIcon\",\n                            \"MemoryChipIcon\",\n                            \"CogIcon\"\n                        ],\n                        description: \"The icon representing the stat.\"\n                    },\n                    value: {\n                        type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                        description: \"The value of the stat, e.g., '1597' or '84%' or '24\\xb0C'.\"\n                    },\n                    label: {\n                        type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                        description: \"The label for the stat, e.g., 'Processes' or 'RAM Usage'.\"\n                    }\n                },\n                required: [\n                    \"icon\",\n                    \"value\",\n                    \"label\"\n                ]\n            }\n        }\n    },\n    required: [\n        \"contacts\",\n        \"safetyItems\",\n        \"safetyChartData\",\n        \"systemRequests\",\n        \"systemStats\"\n    ]\n};\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        res.setHeader(\"Allow\", [\n            \"GET\"\n        ]);\n        return res.status(405).end(`Method ${req.method} Not Allowed`);\n    }\n    try {\n        const prompt = \"Generate plausible data for several widgets on a futuristic system dashboard. Create 3 contacts, 6 safety status items, 6 data points for a small bar chart, 3 system requests/updates, and 5 system performance stats. Use creative, sci-fi-themed names and labels.\";\n        const data = await (0,_backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.generateStructuredData)(prompt, dashboardWidgetsSchema);\n        res.status(200).json(data);\n    } catch (error) {\n        console.error(error);\n        res.status(500).json({\n            error: \"Failed to fetch dashboard widget data from AI.\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/dashboard-widgets.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0"], () => (__webpack_exec__("(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdashboard-widgets&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdashboard-widgets.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();
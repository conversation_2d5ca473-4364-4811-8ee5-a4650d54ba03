"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/dashboard-widgets";
exports.ids = ["pages/api/dashboard-widgets"];
exports.modules = {

/***/ "@google/genai":
/*!********************************!*\
  !*** external "@google/genai" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("@google/genai");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdashboard-widgets&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdashboard-widgets.ts&middlewareConfigBase64=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdashboard-widgets&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdashboard-widgets.ts&middlewareConfigBase64=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_dashboard_widgets_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\dashboard-widgets.ts */ \"(api)/./pages/api/dashboard-widgets.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_dashboard_widgets_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_dashboard_widgets_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/dashboard-widgets\",\n        pathname: \"/api/dashboard-widgets\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_dashboard_widgets_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdashboard-widgets&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdashboard-widgets.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./backend/lib/gemini.ts":
/*!*******************************!*\
  !*** ./backend/lib/gemini.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Type: () => (/* reexport safe */ _google_genai__WEBPACK_IMPORTED_MODULE_0__.Type),\n/* harmony export */   generateStructuredData: () => (/* binding */ generateStructuredData),\n/* harmony export */   generateText: () => (/* binding */ generateText)\n/* harmony export */ });\n/* harmony import */ var _google_genai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/genai */ \"@google/genai\");\n/* harmony import */ var _google_genai__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_google_genai__WEBPACK_IMPORTED_MODULE_0__);\n\nif (!process.env.API_KEY) {\n    throw new Error(\"API key not found. Please set the API_KEY environment variable.\");\n}\nconst ai = new _google_genai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenAI({\n    apiKey: process.env.API_KEY\n});\nasync function generateText(prompt) {\n    try {\n        const response = await ai.models.generateContent({\n            model: \"gemini-2.5-flash\",\n            contents: prompt\n        });\n        return response.text;\n    } catch (e) {\n        console.error(\"Error generating text from Gemini:\", e);\n        throw new Error(\"Failed to generate text from AI.\");\n    }\n}\nasync function generateStructuredData(prompt, schema) {\n    try {\n        const response = await ai.models.generateContent({\n            model: \"gemini-2.5-flash\",\n            contents: prompt,\n            config: {\n                responseMimeType: \"application/json\",\n                responseSchema: schema\n            }\n        });\n        if (!response.text) {\n            throw new Error(\"Received an empty response from the AI.\");\n        }\n        const cleanedText = response.text.trim().replace(/^```json\\s*|```\\s*$/g, \"\");\n        if (!cleanedText) {\n            throw new Error(\"Received an empty JSON response from the AI after cleaning.\");\n        }\n        return JSON.parse(cleanedText);\n    } catch (e) {\n        console.error(\"Failed to generate or parse Gemini JSON response:\", e);\n        if (e instanceof Error && e.message.startsWith(\"Received an empty\")) {\n            throw e;\n        }\n        throw new Error(\"Failed to process structured data from AI.\");\n    }\n}\n // Re-export Type for convenience\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9iYWNrZW5kL2xpYi9nZW1pbmkudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFDaUY7QUFFakYsSUFBSSxDQUFDRSxRQUFRQyxHQUFHLENBQUNDLE9BQU8sRUFBRTtJQUN4QixNQUFNLElBQUlDLE1BQU07QUFDbEI7QUFFQSxNQUFNQyxLQUFLLElBQUlOLHNEQUFXQSxDQUFDO0lBQUVPLFFBQVFMLFFBQVFDLEdBQUcsQ0FBQ0MsT0FBTztBQUFDO0FBRWxELGVBQWVJLGFBQWFDLE1BQWM7SUFDL0MsSUFBSTtRQUNGLE1BQU1DLFdBQW9DLE1BQU1KLEdBQUdLLE1BQU0sQ0FBQ0MsZUFBZSxDQUFDO1lBQ3hFQyxPQUFPO1lBQ1BDLFVBQVVMO1FBQ1o7UUFDQSxPQUFPQyxTQUFTSyxJQUFJO0lBQ3RCLEVBQUUsT0FBTUMsR0FBRztRQUNUQyxRQUFRQyxLQUFLLENBQUMsc0NBQXNDRjtRQUNwRCxNQUFNLElBQUlYLE1BQU07SUFDbEI7QUFDRjtBQUVPLGVBQWVjLHVCQUEwQlYsTUFBYyxFQUFFVyxNQUFXO0lBQ3pFLElBQUk7UUFDRixNQUFNVixXQUFvQyxNQUFNSixHQUFHSyxNQUFNLENBQUNDLGVBQWUsQ0FBQztZQUN4RUMsT0FBTztZQUNQQyxVQUFVTDtZQUNWWSxRQUFRO2dCQUNOQyxrQkFBa0I7Z0JBQ2xCQyxnQkFBZ0JIO1lBQ2xCO1FBQ0Y7UUFFQSxJQUFJLENBQUNWLFNBQVNLLElBQUksRUFBRTtZQUNoQixNQUFNLElBQUlWLE1BQU07UUFDcEI7UUFFQSxNQUFNbUIsY0FBY2QsU0FBU0ssSUFBSSxDQUFDVSxJQUFJLEdBQUdDLE9BQU8sQ0FBQyx3QkFBd0I7UUFFekUsSUFBSSxDQUFDRixhQUFhO1lBQ2QsTUFBTSxJQUFJbkIsTUFBTTtRQUNwQjtRQUVBLE9BQU9zQixLQUFLQyxLQUFLLENBQUNKO0lBQ3BCLEVBQUUsT0FBT1IsR0FBRztRQUNWQyxRQUFRQyxLQUFLLENBQUMscURBQXFERjtRQUNsRSxJQUFJQSxhQUFhWCxTQUFTVyxFQUFFYSxPQUFPLENBQUNDLFVBQVUsQ0FBQyxzQkFBc0I7WUFDbEUsTUFBTWQ7UUFDVjtRQUNBLE1BQU0sSUFBSVgsTUFBTTtJQUNsQjtBQUNGO0FBRWdCLENBQUMsaUNBQWlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXllcy1zaGllbGQtZnV0dXJpc3RpYy11aS8uL2JhY2tlbmQvbGliL2dlbWluaS50cz9jMzBmIl0sInNvdXJjZXNDb250ZW50IjpbIlxuaW1wb3J0IHsgR29vZ2xlR2VuQUksIEdlbmVyYXRlQ29udGVudFJlc3BvbnNlLCBUeXBlLCBQYXJ0IH0gZnJvbSAnQGdvb2dsZS9nZW5haSc7XG5cbmlmICghcHJvY2Vzcy5lbnYuQVBJX0tFWSkge1xuICB0aHJvdyBuZXcgRXJyb3IoXCJBUEkga2V5IG5vdCBmb3VuZC4gUGxlYXNlIHNldCB0aGUgQVBJX0tFWSBlbnZpcm9ubWVudCB2YXJpYWJsZS5cIik7XG59XG5cbmNvbnN0IGFpID0gbmV3IEdvb2dsZUdlbkFJKHsgYXBpS2V5OiBwcm9jZXNzLmVudi5BUElfS0VZIH0pO1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2VuZXJhdGVUZXh0KHByb21wdDogc3RyaW5nKTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCByZXNwb25zZTogR2VuZXJhdGVDb250ZW50UmVzcG9uc2UgPSBhd2FpdCBhaS5tb2RlbHMuZ2VuZXJhdGVDb250ZW50KHtcbiAgICAgIG1vZGVsOiAnZ2VtaW5pLTIuNS1mbGFzaCcsXG4gICAgICBjb250ZW50czogcHJvbXB0LFxuICAgIH0pO1xuICAgIHJldHVybiByZXNwb25zZS50ZXh0O1xuICB9IGNhdGNoKGUpIHtcbiAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZ2VuZXJhdGluZyB0ZXh0IGZyb20gR2VtaW5pOlwiLCBlKTtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJGYWlsZWQgdG8gZ2VuZXJhdGUgdGV4dCBmcm9tIEFJLlwiKTtcbiAgfVxufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2VuZXJhdGVTdHJ1Y3R1cmVkRGF0YTxUPihwcm9tcHQ6IHN0cmluZywgc2NoZW1hOiBhbnkpOiBQcm9taXNlPFQ+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCByZXNwb25zZTogR2VuZXJhdGVDb250ZW50UmVzcG9uc2UgPSBhd2FpdCBhaS5tb2RlbHMuZ2VuZXJhdGVDb250ZW50KHtcbiAgICAgIG1vZGVsOiBcImdlbWluaS0yLjUtZmxhc2hcIixcbiAgICAgIGNvbnRlbnRzOiBwcm9tcHQsXG4gICAgICBjb25maWc6IHtcbiAgICAgICAgcmVzcG9uc2VNaW1lVHlwZTogXCJhcHBsaWNhdGlvbi9qc29uXCIsXG4gICAgICAgIHJlc3BvbnNlU2NoZW1hOiBzY2hlbWEsXG4gICAgICB9LFxuICAgIH0pO1xuXG4gICAgaWYgKCFyZXNwb25zZS50ZXh0KSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIlJlY2VpdmVkIGFuIGVtcHR5IHJlc3BvbnNlIGZyb20gdGhlIEFJLlwiKTtcbiAgICB9XG4gICAgXG4gICAgY29uc3QgY2xlYW5lZFRleHQgPSByZXNwb25zZS50ZXh0LnRyaW0oKS5yZXBsYWNlKC9eYGBganNvblxccyp8YGBgXFxzKiQvZywgJycpO1xuXG4gICAgaWYgKCFjbGVhbmVkVGV4dCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJSZWNlaXZlZCBhbiBlbXB0eSBKU09OIHJlc3BvbnNlIGZyb20gdGhlIEFJIGFmdGVyIGNsZWFuaW5nLlwiKTtcbiAgICB9XG5cbiAgICByZXR1cm4gSlNPTi5wYXJzZShjbGVhbmVkVGV4dCkgYXMgVDtcbiAgfSBjYXRjaCAoZSkge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJGYWlsZWQgdG8gZ2VuZXJhdGUgb3IgcGFyc2UgR2VtaW5pIEpTT04gcmVzcG9uc2U6XCIsIGUpO1xuICAgICBpZiAoZSBpbnN0YW5jZW9mIEVycm9yICYmIGUubWVzc2FnZS5zdGFydHNXaXRoKFwiUmVjZWl2ZWQgYW4gZW1wdHlcIikpIHtcbiAgICAgICAgdGhyb3cgZTtcbiAgICB9XG4gICAgdGhyb3cgbmV3IEVycm9yKFwiRmFpbGVkIHRvIHByb2Nlc3Mgc3RydWN0dXJlZCBkYXRhIGZyb20gQUkuXCIpO1xuICB9XG59XG5cbmV4cG9ydCB7IFR5cGUgfTsgLy8gUmUtZXhwb3J0IFR5cGUgZm9yIGNvbnZlbmllbmNlXG4iXSwibmFtZXMiOlsiR29vZ2xlR2VuQUkiLCJUeXBlIiwicHJvY2VzcyIsImVudiIsIkFQSV9LRVkiLCJFcnJvciIsImFpIiwiYXBpS2V5IiwiZ2VuZXJhdGVUZXh0IiwicHJvbXB0IiwicmVzcG9uc2UiLCJtb2RlbHMiLCJnZW5lcmF0ZUNvbnRlbnQiLCJtb2RlbCIsImNvbnRlbnRzIiwidGV4dCIsImUiLCJjb25zb2xlIiwiZXJyb3IiLCJnZW5lcmF0ZVN0cnVjdHVyZWREYXRhIiwic2NoZW1hIiwiY29uZmlnIiwicmVzcG9uc2VNaW1lVHlwZSIsInJlc3BvbnNlU2NoZW1hIiwiY2xlYW5lZFRleHQiLCJ0cmltIiwicmVwbGFjZSIsIkpTT04iLCJwYXJzZSIsIm1lc3NhZ2UiLCJzdGFydHNXaXRoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./backend/lib/gemini.ts\n");

/***/ }),

/***/ "(api)/./pages/api/dashboard-widgets.ts":
/*!****************************************!*\
  !*** ./pages/api/dashboard-widgets.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../backend/lib/gemini */ \"(api)/./backend/lib/gemini.ts\");\n\nconst dashboardWidgetsSchema = {\n    type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.OBJECT,\n    properties: {\n        contacts: {\n            type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.ARRAY,\n            description: \"A list of exactly 3 high-level contacts for direct access in a futuristic corporation.\",\n            items: {\n                type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.OBJECT,\n                properties: {\n                    name: {\n                        type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                        description: \"Full name of the contact, e.g., 'Dr. Aris Thorne'.\"\n                    },\n                    detail: {\n                        type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                        description: \"Job title or role, e.g., 'Chief Scientist'.\"\n                    },\n                    avatar: {\n                        type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                        description: \"A unique, single-word seed for a placeholder avatar URL, e.g., 'aris'.\"\n                    }\n                },\n                required: [\n                    \"name\",\n                    \"detail\",\n                    \"avatar\"\n                ]\n            }\n        },\n        safetyItems: {\n            type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.ARRAY,\n            description: \"A list of exactly 6 system safety status items with appropriate sci-fi terminology.\",\n            items: {\n                type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.OBJECT,\n                properties: {\n                    label: {\n                        type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                        description: \"The safety status label, e.g., 'Firewall Active', 'Sub-routine Anomaly'.\"\n                    },\n                    color: {\n                        type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                        enum: [\n                            \"#3b82f6\",\n                            \"#ef4444\",\n                            \"#00FFFF\"\n                        ],\n                        description: \"Color code: blue (#3b82f6) for status, red (#ef4444) for warning, cyan (#00FFFF) for secure.\"\n                    }\n                },\n                required: [\n                    \"label\",\n                    \"color\"\n                ]\n            }\n        },\n        safetyChartData: {\n            type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.ARRAY,\n            description: \"A list of exactly 6 numbers between 30 and 100 representing bar chart heights.\",\n            items: {\n                type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.NUMBER\n            }\n        },\n        systemRequests: {\n            type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.ARRAY,\n            description: \"A list of exactly 3 recent system status updates or requests, with sci-fi themes.\",\n            items: {\n                type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.OBJECT,\n                properties: {\n                    text: {\n                        type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                        description: \"The description of the request, e.g., 'Security Patch Update', 'Cryo-chamber diagnostics'.\"\n                    }\n                },\n                required: [\n                    \"text\"\n                ]\n            }\n        },\n        systemStats: {\n            type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.ARRAY,\n            description: \"A list of exactly 5 system performance statistics.\",\n            items: {\n                type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.OBJECT,\n                properties: {\n                    icon: {\n                        type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                        enum: [\n                            \"BuildingOfficeIcon\",\n                            \"PuzzlePieceIcon\",\n                            \"SnowflakeIcon\",\n                            \"MemoryChipIcon\",\n                            \"CogIcon\"\n                        ],\n                        description: \"The icon representing the stat.\"\n                    },\n                    value: {\n                        type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                        description: \"The value of the stat, e.g., '1597' or '84%' or '24\\xb0C'.\"\n                    },\n                    label: {\n                        type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                        description: \"The label for the stat, e.g., 'Processes' or 'RAM Usage'.\"\n                    }\n                },\n                required: [\n                    \"icon\",\n                    \"value\",\n                    \"label\"\n                ]\n            }\n        }\n    },\n    required: [\n        \"contacts\",\n        \"safetyItems\",\n        \"safetyChartData\",\n        \"systemRequests\",\n        \"systemStats\"\n    ]\n};\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        res.setHeader(\"Allow\", [\n            \"GET\"\n        ]);\n        return res.status(405).end(`Method ${req.method} Not Allowed`);\n    }\n    try {\n        const prompt = \"Generate plausible data for several widgets on a futuristic system dashboard. Create 3 contacts, 6 safety status items, 6 data points for a small bar chart, 3 system requests/updates, and 5 system performance stats. Use creative, sci-fi-themed names and labels.\";\n        const data = await (0,_backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.generateStructuredData)(prompt, dashboardWidgetsSchema);\n        res.status(200).json(data);\n    } catch (error) {\n        console.error(error);\n        res.status(500).json({\n            error: \"Failed to fetch dashboard widget data from AI.\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/dashboard-widgets.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0"], () => (__webpack_exec__("(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdashboard-widgets&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdashboard-widgets.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _icons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons */ \"./src/components/icons.tsx\");\n\nvar _s = $RefreshSig$();\n\n\nconst NavItem = (param)=>{\n    let { icon, label, active, onClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        \"aria-label\": label,\n        \"aria-pressed\": active,\n        className: \"w-full flex items-center justify-center responsive-border-radius transition-colors duration-200 relative \".concat(active ? \"bg-brand-cyan/20 text-white\" : \"text-gray-400 hover:bg-gray-700/50 hover:text-white\"),\n        style: {\n            padding: \"calc(var(--base-spacing) * 0.5)\",\n            height: \"var(--base-button-height)\"\n        },\n        children: [\n            active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-0 top-1/2 -translate-y-1/2 bg-brand-cyan rounded-r-full\",\n                style: {\n                    height: \"calc(var(--base-button-height) * 0.6)\",\n                    width: \"3px\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined),\n            icon\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n_c = NavItem;\nconst Sidebar = (param)=>{\n    let { activeView, setActiveView, activeApp, setActiveApp } = param;\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Main function buttons\n    const functionButtons = [\n        {\n            id: \"gamification\",\n            label: \"Gamification\",\n            icon: \"\\uD83C\\uDFAE\",\n            description: \"Learning Games\"\n        },\n        {\n            id: \"coder\",\n            label: \"Coder\",\n            icon: \"\\uD83D\\uDCBB\",\n            description: \"IDE View\"\n        },\n        {\n            id: \"studio\",\n            label: \"Studio\",\n            icon: \"\\uD83E\\uDDD1‍\\uD83C\\uDFA8\",\n            description: \"Creative Workspace\"\n        },\n        {\n            id: \"design\",\n            label: \"Design\",\n            icon: \"\\uD83C\\uDFA8\",\n            description: \"Design Tools\"\n        }\n    ];\n    // Original navigation items\n    const navItems = [\n        {\n            id: \"Dashboard\",\n            label: \"Home\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.HomeIcon, {\n                className: \"responsive-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 87,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: \"Analytic\",\n            label: \"Analytics\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.ChartBarIcon, {\n                className: \"responsive-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 92,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: \"Tool\",\n            label: \"Modules\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.GridIcon, {\n                className: \"responsive-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 97,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: \"Setting\",\n            label: \"Profile\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.UserCircleIcon, {\n                className: \"responsive-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 102,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: \"Setting\",\n            label: \"Settings\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.SettingsIcon, {\n                className: \"responsive-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 107,\n                columnNumber: 13\n            }, undefined)\n        }\n    ];\n    const handleFunctionButtonClick = (appId)=>{\n        if (setActiveApp) {\n            setActiveApp(appId);\n        }\n        setIsExpanded(false); // Auto-collapse after selection\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black/20 backdrop-blur-sm z-30\",\n                        onClick: ()=>setIsExpanded(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed left-20 top-0 h-full bg-panel-bg border border-gray-700/50 backdrop-blur-xl z-40 p-6\",\n                        style: {\n                            width: \"calc(240px * var(--content-scale))\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-white font-semibold text-lg mb-2\",\n                                        children: \"Functions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Choose your workspace\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: functionButtons.map((button)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleFunctionButtonClick(button.id),\n                                        className: \"w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200 \".concat(activeApp === button.id ? \"bg-blue-500/20 text-blue-400 border border-blue-400/30\" : \"text-gray-300 hover:text-white hover:bg-gray-800/60\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl\",\n                                                children: button.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: button.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: button.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, button.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"bg-panel-bg border border-gray-700/50 responsive-border-radius sidebar-left flex flex-col items-center justify-between\",\n                style: {\n                    padding: \"calc(var(--base-spacing) * 0.75)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center w-full\",\n                        style: {\n                            gap: \"calc(var(--base-gap) * 0.75)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsExpanded(!isExpanded),\n                                className: \"bg-gradient-to-br from-cyan-400 to-fuchsia-500 rounded-full flex items-center justify-center text-white hover:scale-105 transition-transform duration-200\",\n                                style: {\n                                    width: \"calc(var(--base-icon-size) * 1.25)\",\n                                    height: \"calc(var(--base-icon-size) * 1.25)\"\n                                },\n                                children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.Bars3Icon, {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.ShieldCheckIcon, {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full border-t border-gray-700\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, undefined),\n                            navItems.slice(0, 3).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavItem, {\n                                    icon: item.icon,\n                                    label: item.label,\n                                    active: activeView === item.id && !activeApp,\n                                    onClick: ()=>{\n                                        setActiveView(item.id);\n                                        if (setActiveApp) setActiveApp(\"\");\n                                    }\n                                }, item.label, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, undefined))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 w-full\",\n                        children: navItems.slice(3).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavItem, {\n                                icon: item.icon,\n                                label: item.label,\n                                active: activeView === item.id && item.label === \"Settings\" && !activeApp,\n                                onClick: ()=>{\n                                    setActiveView(item.id);\n                                    if (setActiveApp) setActiveApp(\"\");\n                                }\n                            }, item.label, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(Sidebar, \"FPNvbbHVlWWR4LKxxNntSxiIS38=\");\n_c1 = Sidebar;\nvar _c, _c1;\n$RefreshReg$(_c, \"NavItem\");\n$RefreshReg$(_c1, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Sidebar.tsx\n"));

/***/ })

});
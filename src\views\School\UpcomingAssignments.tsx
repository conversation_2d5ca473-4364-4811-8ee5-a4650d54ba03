
import React from 'react';
import { Card } from '../../components/Card';
import { BookOpenIcon } from '@heroicons/react/24/outline';

export type Assignment = {
  name: string;
  due: string;
  course: string;
};

interface UpcomingAssignmentsProps {
  assignments: Assignment[];
}

export const UpcomingAssignments: React.FC<UpcomingAssignmentsProps> = ({ assignments }) => {
  return (
    <Card title="Upcoming Assignments">
      <ul className="space-y-3">
        {assignments.map(assignment => (
          <li key={assignment.name} className="flex items-start gap-3">
            <div className="mt-1 text-cyan-400">
              <BookOpenIcon className="w-5 h-5" />
            </div>
            <div>
              <p className="text-white font-medium text-sm">{assignment.name}</p>
              <p className="text-xs text-gray-400">{assignment.course} - Due in {assignment.due}</p>
            </div>
          </li>
        ))}
      </ul>
    </Card>
  );
};

.framework-perseus code {
    font-family: Courier, monospace;
}
.framework-perseus pre {
    background-color: #f0f1f2;
    border-radius: 4px;
    color: #21242c;
    font-size: 18px;
    padding: 16px;
    white-space: pre;
    overflow: auto;
}
.framework-perseus.perseus-article:not(.perseus-mobile)
    .perseus-widget-container:not(.perseus-widget__definition) {
    font-size: 14px;
    line-height: 19.6px;
}
.framework-perseus.perseus-article:not(.perseus-mobile)
    .perseus-widget-container.widget-float-left,
.framework-perseus.perseus-article:not(.perseus-mobile)
    .perseus-widget-container.widget-float-right {
    max-width: 50%;
    padding-top: 32px;
    width: 100%;
}
.framework-perseus.perseus-article:not(.perseus-mobile)
    .perseus-widget-container.widget-float-left
    .perseus-image-caption
    .paragraph
    .paragraph,
.framework-perseus.perseus-article:not(.perseus-mobile)
    .perseus-widget-container.widget-float-right
    .perseus-image-caption
    .paragraph
    .paragraph {
    margin-bottom: 0;
}
.framework-perseus.perseus-article:not(.perseus-mobile)
    .perseus-widget-container.widget-float-left {
    float: left;
    padding-right: 32px;
}
.framework-perseus.perseus-article:not(.perseus-mobile)
    .perseus-widget-container.widget-float-right {
    float: right;
    padding-left: 32px;
}
.framework-perseus.perseus-article:not(.perseus-mobile)
    .perseus-renderer
    > .paragraph {
    color: #21242c;
    font-size: 20px;
    line-height: 30px;
    margin: 0 auto;
    max-width: 688px;
}
.framework-perseus.perseus-article:not(.perseus-mobile)
    .perseus-renderer
    > .paragraph
    .paragraph {
    color: #21242c;
    font-size: 20px;
    line-height: 30px;
    margin-bottom: 32px;
    margin-top: 0;
}
.framework-perseus.perseus-article:not(.perseus-mobile)
    .perseus-renderer
    > .paragraph
    mjx-container {
    font-size: 100%;
}
.framework-perseus.perseus-article:not(.perseus-mobile)
    .perseus-renderer
    > .paragraph
    ul:not(.perseus-widget-radio) {
    color: #21242c;
    font-size: 20px;
    line-height: 30px;
}
.framework-perseus.perseus-article:not(.perseus-mobile) table {
    color: #21242c;
    font-size: 20px;
    line-height: 30px;
    margin-bottom: 32px;
}
.framework-perseus.perseus-article:not(.perseus-mobile) h2 {
    font-family: inherit;
    font-size: 30px;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 16px;
    margin-top: 48px;
}
.framework-perseus.perseus-article:not(.perseus-mobile) h3 {
    font-family: inherit;
    font-size: 26px;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 16px;
    margin-top: 32px;
}
.framework-perseus.perseus-article:not(.perseus-mobile) h4,
.framework-perseus.perseus-article:not(.perseus-mobile) h5,
.framework-perseus.perseus-article:not(.perseus-mobile) h6 {
    font-family: inherit;
    font-size: 22px;
    font-weight: 700;
    line-height: 25px;
    margin-bottom: 16px;
    margin-top: 32px;
}
.framework-perseus.perseus-article:not(.perseus-mobile) blockquote {
    padding: 0 32px;
}
.framework-perseus.perseus-article:not(.perseus-mobile) .MathJax .math {
    color: inherit;
}
.framework-perseus.perseus-article:not(.perseus-mobile)
    .perseus-image-caption
    .perseus-renderer
    .paragraph
    .paragraph,
.framework-perseus.perseus-article:not(.perseus-mobile)
    .perseus-image-caption
    .perseus-renderer
    .paragraph
    ol,
.framework-perseus.perseus-article:not(.perseus-mobile)
    .perseus-image-caption
    .perseus-renderer
    .paragraph
    ul {
    color: rgba(33, 36, 44, 0.64);
    font-size: 14px;
    line-height: 19px;
    margin: 16px auto 42px;
    text-align: left;
}
.framework-perseus.perseus-article:not(.perseus-mobile)
    .paragraph.perseus-paragraph-full-width {
    margin-bottom: 32px;
    margin-left: 0;
    margin-right: 0;
    max-width: none;
}
.framework-perseus.perseus-article:not(.perseus-mobile)
    .paragraph.perseus-paragraph-full-width
    > .paragraph {
    margin: 0;
    max-width: none;
}
.framework-perseus.perseus-article:not(.perseus-mobile) .unresponsive-svg-image,
.framework-perseus.perseus-article:not(.perseus-mobile) .svg-image {
    font-size: 14px;
    line-height: 19.6px;
}
.framework-perseus.perseus-article:not(.perseus-mobile) .perseus-block-math {
    margin-bottom: 32px;
    position: relative;
}
.framework-perseus.perseus-article:not(.perseus-mobile)
    .perseus-block-math:before {
    bottom: 0;
    content: "";
    position: absolute;
    right: 0;
    top: 0;
    width: 30px;
}
.framework-perseus.perseus-article:not(.perseus-mobile)
    .perseus-block-math-inner {
    overflow-x: auto;
    padding-bottom: 8px;
    padding-right: 20px;
    padding-top: 8px;
}
.framework-perseus.perseus-article:not(.perseus-mobile)
    > .clearfix:first-child
    > .perseus-renderer:first-child
    > .paragraph:first-child
    h1:first-child,
.framework-perseus.perseus-article:not(.perseus-mobile)
    > .clearfix:first-child
    > .perseus-renderer:first-child
    > .paragraph:first-child
    h2:first-child,
.framework-perseus.perseus-article:not(.perseus-mobile)
    > .clearfix:first-child
    > .perseus-renderer:first-child
    > .paragraph:first-child
    h3:first-child,
.framework-perseus.perseus-article:not(.perseus-mobile)
    > .clearfix:first-child
    > .perseus-renderer:first-child
    > .paragraph:first-child
    h4:first-child,
.framework-perseus.perseus-article:not(.perseus-mobile)
    > .clearfix:first-child
    > .perseus-renderer:first-child
    > .paragraph:first-child
    h5:first-child,
.framework-perseus.perseus-article:not(.perseus-mobile)
    > .clearfix:first-child
    > .perseus-renderer:first-child
    > .paragraph:first-child
    h6:first-child {
    margin-top: 0;
}
.framework-perseus.perseus-article:not(.perseus-mobile)
    .perseus-renderer
    > .paragraph
    .perseus-formats-tooltip {
    padding: 8px 12px;
}
.framework-perseus.perseus-article:not(.perseus-mobile)
    .perseus-renderer
    > .paragraph
    .perseus-formats-tooltip
    .paragraph {
    margin-bottom: 0;
}
.framework-perseus.perseus-article:not(.perseus-mobile)
    .perseus-renderer
    > .paragraph
    .perseus-formats-tooltip
    .paragraph
    ul:not(.perseus-widget-radio) {
    font-size: 15px;
    line-height: 1.5;
    margin: 0;
}
.framework-perseus.perseus-article:not(.perseus-mobile) pre {
    margin: 0 -16px 32px -16px;
}
.framework-perseus:not(.perseus-article):not(.perseus-mobile)
    .perseus-radio-option-content
    .perseus-renderer
    > .paragraph {
    font-family: inherit;
    font-size: 14px;
    line-height: 1.25;
    color: #21242c;
}
.framework-perseus:not(.perseus-article):not(.perseus-mobile)
    .perseus-radio-option-content
    .perseus-renderer
    > .paragraph
    .paragraph {
    font-family: inherit;
    font-size: 14px;
    line-height: 1.25;
    color: #21242c;
}
.framework-perseus:not(.perseus-article):not(.perseus-mobile)
    .perseus-radio-rationale-content
    .perseus-renderer
    > .paragraph {
    font-family: inherit;
    font-size: 14px;
    line-height: 1.25;
    color: #21242c;
}
.framework-perseus:not(.perseus-article):not(.perseus-mobile)
    .perseus-radio-rationale-content
    .perseus-renderer
    > .paragraph
    .paragraph {
    font-family: inherit;
    font-size: 14px;
    line-height: 1.25;
    color: #21242c;
}
.framework-perseus:not(.perseus-article):not(.perseus-mobile)
    .perseus-radio-rationale-content
    .perseus-renderer
    > .paragraph
    .paragraph
    mjx-container {
    color: #21242c;
}
.framework-perseus.perseus-mobile
    .perseus-article
    .perseus-widget-container.widget-float-left {
    float: left;
    padding-right: 1em;
    max-width: 50%;
    width: 100%;
}
.framework-perseus.perseus-mobile
    .perseus-article
    .perseus-widget-container.widget-float-right {
    float: right;
    padding-left: 1em;
    max-width: 50%;
    width: 100%;
}
.framework-perseus.perseus-mobile
    .perseus-article
    .perseus-renderer
    > .paragraph {
    margin-left: auto;
    margin-right: auto;
    max-width: 700px;
}
.framework-perseus.perseus-mobile
    .perseus-article
    .paragraph.perseus-paragraph-full-width {
    margin-left: 0;
    margin-right: 0;
    max-width: none;
}
.framework-perseus.perseus-mobile
    .perseus-article
    .paragraph.perseus-paragraph-full-width
    > .paragraph {
    margin: 0;
    max-width: none;
}
.framework-perseus.perseus-mobile :not(blockquote) > div.paragraph {
    margin: 0;
}
.framework-perseus.perseus-mobile .perseus-renderer > .paragraph {
    margin: 0 auto;
}
.framework-perseus.perseus-mobile
    .perseus-renderer
    > .paragraph:not(:first-child) {
    margin-top: 32px;
}
.framework-perseus.perseus-mobile .perseus-renderer > .paragraph > .paragraph {
    margin: 0;
}
.framework-perseus.perseus-mobile .clearfix > .perseus-renderer {
    margin-bottom: 32px;
}
.framework-perseus.perseus-mobile
    .perseus-renderer
    > .paragraph
    ul:not(.perseus-widget-radio, .indicatorContainer) {
    margin: 0 0 0 1em;
    padding: 0;
}
.framework-perseus.perseus-mobile
    .perseus-renderer
    > .paragraph
    ul:not(.perseus-widget-radio, .indicatorContainer)
    > li {
    padding-left: 10px;
    margin-bottom: 24px;
}
.framework-perseus.perseus-mobile .perseus-renderer > .paragraph ol {
    margin: 0;
    padding-left: 32px;
}
.framework-perseus.perseus-mobile .perseus-renderer > .paragraph ol > li {
    list-style-type: decimal;
    margin-bottom: 24px;
}
.framework-perseus.perseus-mobile .perseus-renderer > .paragraph ol ol,
.framework-perseus.perseus-mobile
    .perseus-renderer
    > .paragraph
    ul:not(.perseus-widget-radio)
    ol,
.framework-perseus.perseus-mobile
    .perseus-renderer
    > .paragraph
    ol
    ul:not(.perseus-widget-radio),
.framework-perseus.perseus-mobile
    .perseus-renderer
    > .paragraph
    ul:not(.perseus-widget-radio)
    ul:not(.perseus-widget-radio) {
    padding-top: 24px;
}
.framework-perseus.perseus-mobile .perseus-block-math {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    -webkit-touch-callout: none;
}
@media (max-width: 767px) {
    .framework-perseus.perseus-mobile .perseus-renderer > .paragraph {
        max-width: none;
    }
    .framework-perseus.perseus-mobile h1 {
        font-weight: 700;
        padding-top: 0px;
        font-family: inherit;
        font-size: 24px;
        line-height: 1.2;
        color: #21242c;
    }
    .framework-perseus.perseus-mobile h2 {
        font-weight: 700;
        padding-top: 16px;
        font-family: inherit;
        font-size: 24px;
        line-height: 1.2;
        color: #3b3e40;
    }
    .framework-perseus.perseus-mobile h3,
    .framework-perseus.perseus-mobile h4 {
        font-weight: 700;
        padding-top: 0px;
        font-family: inherit;
        font-size: 22px;
        line-height: 1.1;
        color: #626569;
    }
    .framework-perseus.perseus-mobile .default-body-text {
        font-family: inherit;
        font-size: 18px;
        line-height: 1.4;
        color: #626569;
    }
    .framework-perseus.perseus-mobile .perseus-renderer > .paragraph {
        font-family: inherit;
        font-size: 18px;
        line-height: 1.4;
        color: #626569;
    }
    .framework-perseus.perseus-mobile
        .perseus-renderer
        > .paragraph
        .paragraph {
        font-family: inherit;
        font-size: 18px;
        line-height: 1.4;
        color: #626569;
    }
    .framework-perseus.perseus-mobile
        .perseus-renderer
        > .paragraph
        ul:not(.perseus-widget-radio) {
        font-family: inherit;
        font-size: 18px;
        line-height: 1.4;
        color: #626569;
    }
    .framework-perseus.perseus-mobile .perseus-renderer > .paragraph ol {
        font-family: inherit;
        font-size: 18px;
        line-height: 1.4;
        color: #626569;
    }
    .framework-perseus.perseus-mobile blockquote {
        font-family: inherit;
        font-size: 18px;
        line-height: 1.4;
        color: #626569;
        color: #888d93;
    }
    .framework-perseus.perseus-mobile table {
        font-family: inherit;
        font-size: 18px;
        line-height: 1.4;
        color: #626569;
    }
    .framework-perseus.perseus-mobile
        .perseus-radio-option-content
        .perseus-renderer
        > .paragraph {
        font-family: inherit;
        font-size: 16px;
        line-height: 1.25;
        color: #21242c;
    }
    .framework-perseus.perseus-mobile
        .perseus-radio-option-content
        .perseus-renderer
        > .paragraph
        .paragraph {
        font-family: inherit;
        font-size: 16px;
        line-height: 1.25;
        color: #21242c;
    }
    .framework-perseus.perseus-mobile
        .perseus-radio-rationale-content
        .perseus-renderer
        > .paragraph {
        font-family: inherit;
        font-size: 16px;
        line-height: 1.25;
        color: #21242c;
    }
    .framework-perseus.perseus-mobile
        .perseus-radio-rationale-content
        .perseus-renderer
        > .paragraph
        .paragraph {
        font-family: inherit;
        font-size: 16px;
        line-height: 1.25;
        color: #21242c;
    }
    .framework-perseus.perseus-mobile
        .perseus-radio-rationale-content
        .perseus-renderer
        > .paragraph
        .paragraph
        mjx-container {
        color: #21242c;
    }
    .framework-perseus.perseus-mobile
        .perseus-image-caption
        .paragraph
        .paragraph {
        color: #888d93;
        font-size: 14px;
        line-height: 1.3;
        text-align: left;
    }
    .framework-perseus.perseus-mobile
        .perseus-image-caption.has-title
        .paragraph
        .paragraph
        strong:first-child {
        color: #3b3e40;
    }
    .framework-perseus.perseus-mobile mjx-container:not(.mafs-graph *) {
        font-size: 21px;
        line-height: 1.2;
        color: #21242c;
    }
    .framework-perseus.perseus-mobile .perseus-block-math mjx-container {
        font-size: 21px;
        line-height: 1.5;
    }
    .framework-perseus.perseus-mobile .graphie-label mjx-container {
        font-size: 1.21em;
        line-height: 1.2;
    }
    .framework-perseus.perseus-mobile code {
        font-family: Courier, monospace;
    }
    .framework-perseus.perseus-mobile pre {
        background-color: #f0f1f2;
        border-radius: 4px;
        color: #21242c;
        font-size: 18px;
        line-height: 1.6;
        padding: 16px;
        white-space: pre;
        overflow: auto;
    }
    .framework-perseus.perseus-mobile blockquote {
        padding: 0 0 0 18px;
        border-left: 4px solid #d8d8d8;
    }
}
@media (min-width: 768px) and (max-width: 1199px) {
    .framework-perseus.perseus-mobile .perseus-renderer > .paragraph {
        max-width: 512px;
    }
    .framework-perseus.perseus-mobile h1 {
        font-weight: 700;
        padding-top: 0px;
        font-family: inherit;
        font-size: 30px;
        line-height: 1.1;
        color: #21242c;
    }
    .framework-perseus.perseus-mobile h2 {
        font-weight: 700;
        padding-top: 32px;
        font-family: inherit;
        font-size: 30px;
        line-height: 1.1;
        color: #3b3e40;
    }
    .framework-perseus.perseus-mobile h3,
    .framework-perseus.perseus-mobile h4 {
        font-weight: 700;
        padding-top: 16px;
        font-family: inherit;
        font-size: 28px;
        line-height: 1.1;
        color: #626569;
    }
    .framework-perseus.perseus-mobile .default-body-text {
        font-family: inherit;
        font-size: 20px;
        line-height: 1.5;
        color: #626569;
    }
    .framework-perseus.perseus-mobile .perseus-renderer > .paragraph {
        font-family: inherit;
        font-size: 20px;
        line-height: 1.5;
        color: #626569;
    }
    .framework-perseus.perseus-mobile
        .perseus-renderer
        > .paragraph
        .paragraph {
        font-family: inherit;
        font-size: 20px;
        line-height: 1.5;
        color: #626569;
    }
    .framework-perseus.perseus-mobile
        .perseus-renderer
        > .paragraph
        ul:not(.perseus-widget-radio) {
        font-family: inherit;
        font-size: 20px;
        line-height: 1.5;
        color: #626569;
    }
    .framework-perseus.perseus-mobile .perseus-renderer > .paragraph ol {
        font-family: inherit;
        font-size: 20px;
        line-height: 1.5;
        color: #626569;
    }
    .framework-perseus.perseus-mobile blockquote {
        font-family: inherit;
        font-size: 20px;
        line-height: 1.5;
        color: #626569;
        color: #888d93;
    }
    .framework-perseus.perseus-mobile table {
        font-family: inherit;
        font-size: 20px;
        line-height: 1.5;
        color: #626569;
    }
    .framework-perseus.perseus-mobile
        .perseus-radio-option-content
        .perseus-renderer
        > .paragraph {
        font-family: inherit;
        font-size: 18px;
        line-height: 1.25;
        color: #21242c;
    }
    .framework-perseus.perseus-mobile
        .perseus-radio-option-content
        .perseus-renderer
        > .paragraph
        .paragraph {
        font-family: inherit;
        font-size: 18px;
        line-height: 1.25;
        color: #21242c;
    }
    .framework-perseus.perseus-mobile
        .perseus-radio-rationale-content
        .perseus-renderer
        > .paragraph {
        font-family: inherit;
        font-size: 18px;
        line-height: 1.25;
        color: #21242c;
    }
    .framework-perseus.perseus-mobile
        .perseus-radio-rationale-content
        .perseus-renderer
        > .paragraph
        .paragraph {
        font-family: inherit;
        font-size: 18px;
        line-height: 1.25;
        color: #21242c;
    }
    .framework-perseus.perseus-mobile
        .perseus-radio-rationale-content
        .perseus-renderer
        > .paragraph
        .paragraph
        mjx-container {
        color: #21242c;
    }
    .framework-perseus.perseus-mobile
        .perseus-image-caption
        .paragraph
        .paragraph {
        color: #888d93;
        font-size: 17px;
        line-height: 1.4;
        text-align: left;
    }
    .framework-perseus.perseus-mobile
        .perseus-image-caption.has-title
        .paragraph
        .paragraph
        strong:first-child {
        color: #3b3e40;
    }
    .framework-perseus.perseus-mobile mjx-container:not(.mafs-graph *) {
        font-size: 23px;
        line-height: 1.3;
        color: #21242c;
    }
    .framework-perseus.perseus-mobile .perseus-block-math mjx-container {
        font-size: 30px;
        line-height: 1.3;
    }
    .framework-perseus.perseus-mobile .graphie-label mjx-container {
        font-size: 1.21em;
        line-height: 1.2;
    }
    .framework-perseus.perseus-mobile code {
        font-family: Courier, monospace;
    }
    .framework-perseus.perseus-mobile pre {
        background-color: #f0f1f2;
        border-radius: 4px;
        color: #21242c;
        font-size: 18px;
        line-height: 1.6;
        padding: 16px;
        white-space: pre;
        overflow: auto;
    }
    .framework-perseus.perseus-mobile blockquote {
        padding: 0 0 0 20px;
        border-left: 4px solid #d8d8d8;
    }
}
@media (min-width: 1200px) {
    .framework-perseus.perseus-mobile .perseus-renderer > .paragraph {
        max-width: 688px;
    }
    .framework-perseus.perseus-mobile h1 {
        font-weight: 700;
        padding-top: 0px;
        font-family: inherit;
        font-size: 35px;
        line-height: 1.1;
        color: #21242c;
    }
    .framework-perseus.perseus-mobile h2 {
        font-weight: 700;
        padding-top: 32px;
        font-family: inherit;
        font-size: 35px;
        line-height: 1.1;
        color: #3b3e40;
    }
    .framework-perseus.perseus-mobile h3,
    .framework-perseus.perseus-mobile h4 {
        font-weight: 700;
        padding-top: 16px;
        font-family: inherit;
        font-size: 30px;
        line-height: 1.1;
        color: #626569;
    }
    .framework-perseus.perseus-mobile .default-body-text {
        font-family: inherit;
        font-size: 22px;
        line-height: 1.4;
        color: #626569;
    }
    .framework-perseus.perseus-mobile .perseus-renderer > .paragraph {
        font-family: inherit;
        font-size: 22px;
        line-height: 1.4;
        color: #626569;
    }
    .framework-perseus.perseus-mobile
        .perseus-renderer
        > .paragraph
        .paragraph {
        font-family: inherit;
        font-size: 22px;
        line-height: 1.4;
        color: #626569;
    }
    .framework-perseus.perseus-mobile
        .perseus-renderer
        > .paragraph
        ul:not(.perseus-widget-radio) {
        font-family: inherit;
        font-size: 22px;
        line-height: 1.4;
        color: #626569;
    }
    .framework-perseus.perseus-mobile .perseus-renderer > .paragraph ol {
        font-family: inherit;
        font-size: 22px;
        line-height: 1.4;
        color: #626569;
    }
    .framework-perseus.perseus-mobile blockquote {
        font-family: inherit;
        font-size: 22px;
        line-height: 1.4;
        color: #626569;
        color: #888d93;
    }
    .framework-perseus.perseus-mobile table {
        font-family: inherit;
        font-size: 22px;
        line-height: 1.4;
        color: #626569;
    }
    .framework-perseus.perseus-mobile
        .perseus-radio-option-content
        .perseus-renderer
        > .paragraph {
        font-family: inherit;
        font-size: 20px;
        line-height: 1.25;
        color: #21242c;
    }
    .framework-perseus.perseus-mobile
        .perseus-radio-option-content
        .perseus-renderer
        > .paragraph
        .paragraph {
        font-family: inherit;
        font-size: 20px;
        line-height: 1.25;
        color: #21242c;
    }
    .framework-perseus.perseus-mobile
        .perseus-radio-rationale-content
        .perseus-renderer
        > .paragraph {
        font-family: inherit;
        font-size: 20px;
        line-height: 1.25;
        color: #21242c;
    }
    .framework-perseus.perseus-mobile
        .perseus-radio-rationale-content
        .perseus-renderer
        > .paragraph
        .paragraph {
        font-family: inherit;
        font-size: 20px;
        line-height: 1.25;
        color: #21242c;
    }
    .framework-perseus.perseus-mobile
        .perseus-radio-rationale-content
        .perseus-renderer
        > .paragraph
        .paragraph
        mjx-container {
        color: #21242c;
    }
    .framework-perseus.perseus-mobile
        .perseus-image-caption
        .paragraph
        .paragraph {
        color: #888d93;
        font-size: 20px;
        line-height: 1.4;
        text-align: left;
    }
    .framework-perseus.perseus-mobile
        .perseus-image-caption.has-title
        .paragraph
        .paragraph
        strong:first-child {
        color: #3b3e40;
    }
    .framework-perseus.perseus-mobile mjx-container:not(.mafs-graph *) {
        font-size: 25px;
        line-height: 1.2;
        color: #21242c;
    }
    .framework-perseus.perseus-mobile .perseus-block-math mjx-container {
        font-size: 30px;
        line-height: 1.3;
    }
    .framework-perseus.perseus-mobile .graphie-label mjx-container {
        font-size: 1.21em;
        line-height: 1.2;
    }
    .framework-perseus.perseus-mobile code {
        font-family: Courier, monospace;
    }
    .framework-perseus.perseus-mobile pre {
        background-color: #f0f1f2;
        border-radius: 4px;
        color: #21242c;
        font-size: 18px;
        line-height: 1.6;
        padding: 16px;
        white-space: pre;
        overflow: auto;
    }
    .framework-perseus.perseus-mobile blockquote {
        padding: 0 0 0 20px;
        border-left: 5px solid #d8d8d8;
    }
}
.framework-perseus.perseus-mobile .perseus-widget-container {
    font-size: 14px;
    line-height: 19.6px;
}
.framework-perseus.perseus-mobile .perseus-widget-container.widget-float-left,
.framework-perseus.perseus-mobile .perseus-widget-container.widget-float-right {
    max-width: 50%;
    padding-top: 32px;
    width: 100%;
}
.framework-perseus.perseus-mobile
    .perseus-widget-container.widget-float-left
    .perseus-image-caption
    .paragraph
    .paragraph,
.framework-perseus.perseus-mobile
    .perseus-widget-container.widget-float-right
    .perseus-image-caption
    .paragraph
    .paragraph {
    margin-bottom: 0;
}
.framework-perseus.perseus-mobile .perseus-widget-container.widget-float-left {
    float: left;
    padding-right: 32px;
}
.framework-perseus.perseus-mobile .perseus-widget-container.widget-float-right {
    float: right;
    padding-left: 32px;
}
.framework-perseus.perseus-mobile .MathJax .math {
    color: inherit;
}
.framework-perseus.perseus-mobile .perseus-image-widget {
    text-align: center;
}
.framework-perseus.perseus-mobile .perseus-block-math {
    padding-top: 16px;
    padding-bottom: 16px;
}
.framework-perseus.perseus-mobile .paragraph.perseus-paragraph-full-width {
    margin-left: 0;
    margin-right: 0;
    max-width: none;
}
.framework-perseus.perseus-mobile
    .paragraph.perseus-paragraph-full-width
    > .paragraph {
    margin: 0;
    max-width: none;
}
.framework-perseus.perseus-mobile .unresponsive-svg-image,
.framework-perseus.perseus-mobile .svg-image {
    font-size: 14px;
    line-height: 19.6px;
}
.framework-perseus.perseus-mobile
    .perseus-renderer
    > .paragraph
    .perseus-formats-tooltip {
    padding: 8px 12px;
}
.framework-perseus.perseus-mobile
    .perseus-renderer
    > .paragraph
    .perseus-formats-tooltip
    .paragraph {
    margin-bottom: 0;
}
.framework-perseus.perseus-mobile
    .perseus-renderer
    > .paragraph
    .perseus-formats-tooltip
    .paragraph
    ul:not(.perseus-widget-radio) {
    font-size: 15px;
    line-height: 1.5;
    margin: 0;
}
/* Derived from the MIT-licensed zoom.js:
   https://github.com/fat/zoom.js/blob/fd4f3e43153da7596da0bade198e99f98b47791e/
*/
:root {
    --perseus-zoom-overlay-z-index: 9000;
}
.zoomable {
    cursor: pointer;
    cursor: -webkit-zoom-in;
    cursor: -moz-zoom-in;
}
.zoom-img {
    background-color: white;
    position: absolute;
    z-index: calc(var(--perseus-zoom-overlay-z-index) + 1);
}
img.zoom-img {
    cursor: pointer;
    cursor: -webkit-zoom-out;
    cursor: -moz-zoom-out;
}
.zoom-transition {
    transition: transform 300ms ease;
}
.zoom-overlay {
    z-index: var(--perseus-zoom-overlay-z-index);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: scroll;
}
.zoom-overlay-open,
.zoom-overlay-transitioning {
    cursor: default;
}
.zoom-overlay-open {
    height: 100%;
    max-height: 100%;
    overflow: hidden;
}
.zoom-backdrop {
    z-index: calc(var(--perseus-zoom-overlay-z-index) - 1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    opacity: 0;
    transition: opacity 300ms;
}
.zoom-overlay-open > .zoom-backdrop {
    opacity: 0.9;
}

import React from 'react';
import { Card } from '../../../components/Card';

export const PastPrelesssGauge: React.FC = () => {
  return (
    <Card title="System Pressure" className="items-center justify-center">
        <div className="relative w-32 h-32 sm:w-36 sm:h-36 lg:w-40 lg:h-40">
            <svg viewBox="0 0 100 100" className="w-full h-full">
                {/* Gauge Background Arc */}
                <path d="M 20 80 A 40 40 0 1 1 80 80" fill="none" stroke="rgba(255,255,255,0.1)" strokeWidth="8" strokeLinecap="round" />
                {/* 'Y' shape icon */}
                <g transform="translate(50 50) scale(0.4)">
                    <path d="M 0 -20 V 0 M -17.3 10 L 0 0 L 17.3 10" stroke="#E0E0E0" strokeWidth="4" strokeLinecap="round"/>
                </g>
                 {/* Pointers */}
                <line x1="50" y1="50" x2="50" y2="15" stroke="white" strokeWidth="2" transform="rotate(135 50 50)" />
                <line x1="50" y1="50" x2="50" y2="25" stroke="#FF3333" strokeWidth="2.5" transform="rotate(225 50 50)" />
                
                {/* Central Readout */}
                <text x="50" y="55" textAnchor="middle" fill="#FFFFFF" fontSize="18" fontWeight="bold">4.2</text>
                <text x="50" y="70" textAnchor="middle" fill="#A0A0B0" fontSize="10">PSI</text>
            </svg>
        </div>
    </Card>
  );
};
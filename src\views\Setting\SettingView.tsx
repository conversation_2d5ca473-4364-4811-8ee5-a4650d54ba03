
import React, { useState, useEffect } from 'react';
import { Card } from '../../components/Card';
import { ProfileSettings, ProfileData } from './ProfileSettings';

type NotificationSettings = {
    emailAlerts: boolean;
    pushNotifications: boolean;
    systemUpdates: boolean;
};
type SecuritySettings = {
    twoFactorAuth: boolean;
    biometricLock: boolean;
};
type SettingsData = {
    profile: ProfileData;
    notifications: NotificationSettings;
    security: SecuritySettings;
};


const Toggle = ({ label, enabled, onChange }: { label: string, enabled: boolean, onChange: () => void }) => {
    const uniqueId = label.replace(/\s+/g, '-').toLowerCase();
    return (
        <div className="flex justify-between items-center bg-gray-700/50 p-3 rounded-lg">
            <span id={`${uniqueId}-label`} className="text-white text-sm">{label}</span>
            <button
                onClick={onChange}
                role="switch"
                aria-checked={enabled}
                aria-labelledby={`${uniqueId}-label`}
                className={`relative w-12 h-6 rounded-full flex items-center p-1 transition-colors duration-300 focus:outline-none focus-visible:ring-2 focus-visible:ring-brand-cyan focus-visible:ring-offset-2 focus-visible:ring-offset-gray-800 ${enabled ? 'bg-cyan-500' : 'bg-gray-600'}`}
            >
                <span className={`inline-block w-5 h-5 bg-white rounded-full shadow-md transform transition-transform duration-300 ease-in-out ${enabled ? 'translate-x-5' : 'translate-x-0'}`}></span>
            </button>
        </div>
    );
};

export const SettingView: React.FC = () => {
    const [settings, setSettings] = useState<SettingsData | null>(null);
    const [loading, setLoading] = useState(true);

    const fetchSettings = async () => {
        try {
            setLoading(true);
            const response = await fetch('/api/settings');
            const data = await response.json();
            setSettings(data);
        } catch (error) {
            console.error("Failed to fetch settings", error);
        } finally {
            setLoading(false);
        }
    };
    
    useEffect(() => {
        fetchSettings();
    }, []);

    const handleToggleChange = async (category: 'notifications' | 'security', key: string) => {
        if (!settings) return;

        const newCategoryState = { ...settings[category], [key]: !settings[category][key] };
        const newSettings = { ...settings, [category]: newCategoryState };
        setSettings(newSettings); // Optimistic update

        try {
            await fetch('/api/settings', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ [category]: newCategoryState })
            });
        } catch (error) {
            console.error("Failed to update settings", error);
            fetchSettings(); // Revert on error
        }
    };

  if (loading || !settings) {
    return <div className="flex justify-center items-center h-full text-cyan-400">Loading Settings...</div>;
  }

  return (
    <div className="flex-grow grid grid-cols-1 md:grid-cols-2 gap-4">
      <ProfileSettings initialProfile={settings.profile} onProfileUpdate={fetchSettings}/>
      <div className="flex flex-col gap-4">
        <Card title="Notifications">
            <div className="space-y-3">
                <Toggle label="Email Alerts" enabled={settings.notifications.emailAlerts} onChange={() => handleToggleChange('notifications', 'emailAlerts')} />
                <Toggle label="Push Notifications" enabled={settings.notifications.pushNotifications} onChange={() => handleToggleChange('notifications', 'pushNotifications')} />
                <Toggle label="System Updates" enabled={settings.notifications.systemUpdates} onChange={() => handleToggleChange('notifications', 'systemUpdates')} />
            </div>
        </Card>
         <Card title="Security">
            <div className="space-y-3">
                 <Toggle label="Two-Factor Auth" enabled={settings.security.twoFactorAuth} onChange={() => handleToggleChange('security', 'twoFactorAuth')} />
                 <Toggle label="Biometric Lock" enabled={settings.security.biometricLock} onChange={() => handleToggleChange('security', 'biometricLock')} />
            </div>
        </Card>
      </div>
    </div>
  );
};

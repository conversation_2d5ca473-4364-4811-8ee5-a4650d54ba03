// *****************************************************************************
// Copyright (C) 2022 <PERSON> and others.
//
// This program and the accompanying materials are made available under the
// terms of the Eclipse Public License v. 2.0 which is available at
// http://www.eclipse.org/legal/epl-2.0.
//
// This Source Code may also be made available under the following Secondary
// Licenses when the conditions for such availability set forth in the Eclipse
// Public License v. 2.0 are satisfied: GNU General Public License, version 2
// with the GNU Classpath Exception which is available at
// https://www.gnu.org/software/classpath/license.html.
//
// SPDX-License-Identifier: EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0
// *****************************************************************************

import '../../src/browser/style/toolbar.css';
import { ContainerModule, interfaces } from '@theia/core/shared/inversify';
import { bindToolbarApplicationShell } from './application-shell-with-toolbar-override';
import { bindToolbar } from './toolbar-command-contribution';
import { bindToolbarContentHoverWidgetPatcher } from './toolbar-content-hover-widget-patcher';

export default new ContainerModule((
    bind: interfaces.Bind,
    unbind: interfaces.Unbind,
    _isBound: interfaces.IsBound,
    rebind: interfaces.Rebind,
) => {
    bindToolbarApplicationShell(bind, rebind, unbind);
    bindToolbar(bind);
    bindToolbarContentHoverWidgetPatcher(bind, rebind, unbind);
});

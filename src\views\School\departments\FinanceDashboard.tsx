import React, { useState, useEffect } from 'react';
import { Card } from '../../../components/Card';
import {
  CurrencyDollarIcon,
  ChartBarIcon,
  DocumentTextIcon,
  UserGroupIcon,
  ShieldCheckIcon,
  GlobeAltIcon
} from '@heroicons/react/24/solid';

interface FinanceDashboardProps {
  activeSubSection: string;
}

export const FinanceDashboard: React.FC<FinanceDashboardProps> = ({ activeSubSection }) => {
  const [financeData, setFinanceData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchFinanceData = async () => {
      try {
        setLoading(true);
        await new Promise(resolve => setTimeout(resolve, 500));

        const mockData = {
          overview: {
            totalBudget: 2400000,
            cryptoHoldings: 847000,
            nftAssets: 156000,
            blockchainTransactions: 2847,
            carbonCredits: 1200,
            aiPredictedSavings: 340000,
            smartContractPayments: 89,
            defiYield: 12.4
          },
          cryptoPortfolio: [
            { name: '<PERSON><PERSON><PERSON><PERSON><PERSON> (EDU)', amount: 50000, value: 425000, change: '+12.4%', color: 'text-green-400' },
            { name: 'LearnToken (LEARN)', amount: 30000, value: 267000, change: '+8.7%', color: 'text-green-400' },
            { name: 'KnowledgeNFT', amount: 156, value: 156000, change: '+24.1%', color: 'text-green-400' },
            { name: 'Carbon Credits', amount: 1200, value: 48000, change: '+5.2%', color: 'text-green-400' }
          ],
          smartContracts: [
            { name: 'Teacher Salary Automation', status: 'Active', savings: '$45K/year', efficiency: '99.8%' },
            { name: 'Student Fee Collection', status: 'Active', savings: '$23K/year', efficiency: '100%' },
            { name: 'Supply Chain Tracking', status: 'Beta', savings: '$12K/year', efficiency: '97.2%' },
            { name: 'Energy Trading Bot', status: 'Live', savings: '$67K/year', efficiency: '94.5%' }
          ],
          aiPredictions: [
            { metric: 'Next Quarter Budget', prediction: '$2.8M', confidence: 94, trend: 'up' },
            { metric: 'Energy Costs', prediction: '-15%', confidence: 87, trend: 'down' },
            { metric: 'Crypto Portfolio', prediction: '+28%', confidence: 91, trend: 'up' },
            { metric: 'Operational Efficiency', prediction: '+12%', confidence: 96, trend: 'up' }
          ]
        };

        setFinanceData(mockData);
      } catch (error) {
        console.error('Failed to fetch finance data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchFinanceData();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-cyan-400">Loading Blockchain Finance...</div>
      </div>
    );
  }

  const renderContent = () => {
    switch (activeSubSection) {
      case 'dashboard':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Blockchain Financial Overview */}
            <Card title="⛓️ Blockchain Finance Hub" className="lg:col-span-3">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4">
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <CurrencyDollarIcon className="w-6 h-6 text-white" />
                  </div>
                  <p className="text-2xl font-bold text-white">${(financeData.overview.cryptoHoldings / 1000).toFixed(0)}K</p>
                  <p className="text-sm text-gray-400">Crypto Holdings</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <ShieldCheckIcon className="w-6 h-6 text-white" />
                  </div>
                  <p className="text-2xl font-bold text-white">{financeData.overview.blockchainTransactions}</p>
                  <p className="text-sm text-gray-400">Smart Contracts</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <GlobeAltIcon className="w-6 h-6 text-white" />
                  </div>
                  <p className="text-2xl font-bold text-white">{financeData.overview.defiYield}%</p>
                  <p className="text-sm text-gray-400">DeFi Yield</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <ChartBarIcon className="w-6 h-6 text-white" />
                  </div>
                  <p className="text-2xl font-bold text-white">${(financeData.overview.aiPredictedSavings / 1000).toFixed(0)}K</p>
                  <p className="text-sm text-gray-400">AI Savings</p>
                </div>
              </div>
            </Card>

            {/* Crypto Portfolio */}
            <Card title="💰 Crypto Portfolio" className="lg:col-span-2">
              <div className="space-y-3 p-4">
                {financeData.cryptoPortfolio.map((crypto: any, index: number) => (
                  <div key={index} className="p-3 rounded-lg bg-gradient-to-r from-gray-800/40 to-gray-700/40 border border-gray-600/30">
                    <div className="flex justify-between items-center">
                      <div>
                        <h4 className="font-semibold text-white">{crypto.name}</h4>
                        <p className="text-sm text-gray-400">{crypto.amount.toLocaleString()} tokens</p>
                      </div>
                      <div className="text-right">
                        <p className="text-lg font-bold text-white">${(crypto.value / 1000).toFixed(0)}K</p>
                        <p className={`text-sm ${crypto.color}`}>{crypto.change}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Smart Contracts */}
            <Card title="🤖 Smart Contracts">
              <div className="space-y-3 p-4">
                {financeData.smartContracts.map((contract: any, index: number) => (
                  <div key={index} className="p-3 rounded-lg bg-gradient-to-r from-blue-900/20 to-purple-900/20 border border-blue-500/30">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-sm font-medium text-white">{contract.name}</h4>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        contract.status === 'Active' ? 'bg-green-500/20 text-green-400' :
                        contract.status === 'Live' ? 'bg-blue-500/20 text-blue-400' :
                        'bg-yellow-500/20 text-yellow-400'
                      }`}>
                        {contract.status}
                      </span>
                    </div>
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-cyan-400">💰 {contract.savings}</span>
                      <span className="text-green-400">⚡ {contract.efficiency}</span>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </div>
        );

      case 'budget':
        return (
          <Card title="🧠 AI Budget Predictions">
            <div className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {financeData.aiPredictions.map((prediction: any, index: number) => (
                  <div key={index} className="p-4 rounded-lg bg-gradient-to-r from-purple-900/20 to-blue-900/20 border border-purple-500/30">
                    <h4 className="text-lg font-semibold text-white mb-2">{prediction.metric}</h4>
                    <div className="flex items-center justify-between">
                      <span className={`text-2xl font-bold ${prediction.trend === 'up' ? 'text-green-400' : 'text-red-400'}`}>
                        {prediction.prediction}
                      </span>
                      <span className="text-sm text-gray-400">{prediction.confidence}% confidence</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        );

      default:
        return (
          <Card title="Blockchain Finance">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Future Finance Department</h3>
              <p className="text-gray-400">Powered by blockchain, AI, and cryptocurrency innovations.</p>
            </div>
          </Card>
        );
    }
  };

  return (
    <div className="p-4 h-full">
      {renderContent()}
    </div>
  );
};

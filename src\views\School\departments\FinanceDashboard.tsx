import React from 'react';
import { Card } from '../../../components/Card';
import { 
  CurrencyDollarIcon,
  ChartBarIcon,
  DocumentTextIcon,
  UserGroupIcon
} from '@heroicons/react/24/solid';

interface FinanceDashboardProps {
  activeSubSection: string;
}

export const FinanceDashboard: React.FC<FinanceDashboardProps> = ({ activeSubSection }) => {
  const renderContent = () => {
    switch (activeSubSection) {
      case 'dashboard':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card title="Financial Overview" className="lg:col-span-3">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4">
                <div className="text-center">
                  <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <CurrencyDollarIcon className="w-6 h-6 text-green-400" />
                  </div>
                  <p className="text-2xl font-bold text-white">$2.4M</p>
                  <p className="text-sm text-gray-400">Annual Budget</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <ChartBarIcon className="w-6 h-6 text-blue-400" />
                  </div>
                  <p className="text-2xl font-bold text-white">$1.8M</p>
                  <p className="text-sm text-gray-400">Revenue YTD</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <DocumentTextIcon className="w-6 h-6 text-red-400" />
                  </div>
                  <p className="text-2xl font-bold text-white">$1.2M</p>
                  <p className="text-sm text-gray-400">Expenses YTD</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <UserGroupIcon className="w-6 h-6 text-purple-400" />
                  </div>
                  <p className="text-2xl font-bold text-white">$890K</p>
                  <p className="text-sm text-gray-400">Payroll</p>
                </div>
              </div>
            </Card>
          </div>
        );
      case 'budget':
        return (
          <Card title="Budget Management">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Budget Planning & Tracking</h3>
              <p className="text-gray-400">Manage school budget allocations and track spending across departments.</p>
            </div>
          </Card>
        );
      default:
        return (
          <Card title="Finance Dashboard">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Finance Department</h3>
              <p className="text-gray-400">Select a section from the navigation to get started.</p>
            </div>
          </Card>
        );
    }
  };

  return (
    <div className="p-4 h-full">
      {renderContent()}
    </div>
  );
};

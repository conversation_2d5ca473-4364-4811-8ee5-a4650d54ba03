
import React, { useState, useEffect } from 'react';
import { FeaturedProduct, FeaturedProductData } from './FeaturedProduct';
import { Card } from '../../components/Card';

type MarketStatsData = {
    marketCap: string;
    volume24h: string;
    topMover: {
        name: string;
        change: string;
        isUp: boolean;
    };
};

type MarketData = {
    featuredProduct: FeaturedProductData;
    marketStats: MarketStatsData;
};

export const MarketView: React.FC = () => {
    const [data, setData] = useState<MarketData | null>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);
                const response = await fetch('/api/market');
                const result = await response.json();
                setData(result);
            } catch (error) {
                console.error("Failed to fetch market data", error);
            } finally {
                setLoading(false);
            }
        };
        fetchData();
    }, []);

    if (loading) {
        return <div className="flex justify-center items-center h-full text-cyan-400">Loading Market Data...</div>;
    }
    
    if (!data) {
        return <div className="flex justify-center items-center h-full text-red-400">Failed to load market data.</div>;
    }

    const { featuredProduct, marketStats } = data;
    const moverColor = marketStats.topMover.isUp ? 'text-green-400' : 'text-red-400';

    return (
        <div className="flex-grow grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div className="lg:col-span-2">
            <FeaturedProduct product={featuredProduct} />
        </div>
        <Card title="Market Stats">
            <div className="flex flex-col justify-center h-full gap-4">
            <div>
                <p className="text-xs text-gray-400">Market Cap</p>
                <p className="text-2xl font-bold text-white">{marketStats.marketCap}</p>
            </div>
            <div>
                <p className="text-xs text-gray-400">24h Volume</p>
                <p className="text-2xl font-bold text-white">{marketStats.volume24h}</p>
            </div>
            <div>
                <p className="text-xs text-gray-400">Top Mover</p>
                <p className={`text-xl font-bold ${moverColor}`}>{marketStats.topMover.name} {marketStats.topMover.change}</p>
            </div>
            </div>
        </Card>
        </div>
    );
};

// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`graded-group-set should render all graded groups 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <h1>
        Section 1: Adding tenths less than one
      </h1>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="1"
    >
      <div
        class="paragraph"
      >
        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="container_14sgdtb"
          >
            <div
              class="perseus-graded-group"
            >
              <div
                class="title_1lagzkb"
              >
                Problem 1a
              </div>
              <div
                class="perseus-renderer perseus-renderer-responsive"
              >
                <div
                  class="paragraph"
                  data-perseus-paragraph-index="0"
                >
                  <div
                    class="paragraph"
                  >
                    <span
                      style="white-space: nowrap;"
                    >
                      <span />
                      <span
                        class="mock-TeX"
                      >
                        0.5 + 0.4 =
                      </span>
                      <span />
                    </span>
                       
                    <div
                      class="perseus-widget-container widget-nohighlight widget-inline-block"
                    >
                      <input
                        aria-disabled="false"
                        aria-invalid="false"
                        aria-required="false"
                        autocapitalize="off"
                        autocomplete="off"
                        autocorrect="off"
                        class="input_ttwki7-o_O-BodyTextMediumMediumWeight_ijclyr-o_O-default_8fhii8-o_O-inputWithExamples_1y6ajxo"
                        data-testid="input-with-examples"
                        id=":r0:"
                        tabindex="0"
                        type="text"
                        value=""
                      />
                      <span
                        id="aria-for-:r0:"
                        style="display: none;"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <p
                aria-live="polite"
                role="status"
              />
              <button
                aria-disabled="false"
                class="button_vr44p2-o_O-reset_1ck4g21-o_O-shared_1jpsfmg-o_O-default_1l1gmds"
                role="button"
                type="button"
              >
                <span
                  class="text_f1191h-o_O-LabelLarge_16mkhxv-o_O-text_1kowsup"
                >
                  Check
                </span>
              </button>
              <button
                class="showHintLink_oak3yy"
                tabindex="0"
              >
                Explain
              </button>
            </div>
            <div
              class="perseus-graded-group"
            >
              <div
                class="title_1lagzkb"
              >
                Problem 1b
              </div>
              <div
                class="perseus-renderer perseus-renderer-responsive"
              >
                <div
                  class="paragraph"
                  data-perseus-paragraph-index="0"
                >
                  <div
                    class="paragraph"
                  >
                    <span
                      style="white-space: nowrap;"
                    >
                      <span />
                      <span
                        class="mock-TeX"
                      >
                        0.6 + 0.4 =
                      </span>
                      <span />
                    </span>
                       
                    <div
                      class="perseus-widget-container widget-nohighlight widget-inline-block"
                    >
                      <input
                        aria-disabled="false"
                        aria-invalid="false"
                        aria-required="false"
                        autocapitalize="off"
                        autocomplete="off"
                        autocorrect="off"
                        class="input_ttwki7-o_O-BodyTextMediumMediumWeight_ijclyr-o_O-default_8fhii8-o_O-inputWithExamples_1y6ajxo"
                        data-testid="input-with-examples"
                        id=":r3:"
                        tabindex="0"
                        type="text"
                        value=""
                      />
                      <span
                        id="aria-for-:r3:"
                        style="display: none;"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <p
                aria-live="polite"
                role="status"
              />
              <button
                aria-disabled="false"
                class="button_vr44p2-o_O-reset_1ck4g21-o_O-shared_1jpsfmg-o_O-default_1l1gmds"
                role="button"
                type="button"
              >
                <span
                  class="text_f1191h-o_O-LabelLarge_16mkhxv-o_O-text_1kowsup"
                >
                  Check
                </span>
              </button>
              <button
                class="showHintLink_oak3yy"
                tabindex="0"
              >
                Explain
              </button>
            </div>
            <div
              class="perseus-graded-group"
            >
              <div
                class="title_1lagzkb"
              >
                Problem 1c
              </div>
              <div
                class="perseus-renderer perseus-renderer-responsive"
              >
                <div
                  class="paragraph"
                  data-perseus-paragraph-index="0"
                >
                  <div
                    class="paragraph"
                  >
                    <span
                      style="white-space: nowrap;"
                    >
                      <span />
                      <span
                        class="mock-TeX"
                      >
                        0.8 + 0.4 =
                      </span>
                      <span />
                    </span>
                       
                    <div
                      class="perseus-widget-container widget-nohighlight widget-inline-block"
                    >
                      <input
                        aria-disabled="false"
                        aria-invalid="false"
                        aria-required="false"
                        autocapitalize="off"
                        autocomplete="off"
                        autocorrect="off"
                        class="input_ttwki7-o_O-BodyTextMediumMediumWeight_ijclyr-o_O-default_8fhii8-o_O-inputWithExamples_1y6ajxo"
                        data-testid="input-with-examples"
                        id=":r6:"
                        tabindex="0"
                        type="text"
                        value=""
                      />
                      <span
                        id="aria-for-:r6:"
                        style="display: none;"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <p
                aria-live="polite"
                role="status"
              />
              <button
                aria-disabled="false"
                class="button_vr44p2-o_O-reset_1ck4g21-o_O-shared_1jpsfmg-o_O-default_1l1gmds"
                role="button"
                type="button"
              >
                <span
                  class="text_f1191h-o_O-LabelLarge_16mkhxv-o_O-text_1kowsup"
                >
                  Check
                </span>
              </button>
              <button
                class="showHintLink_oak3yy"
                tabindex="0"
              >
                Explain
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="2"
    >
      <div
        class="paragraph"
      >
        Beautiful, let's move on to problems with whole numbers and tenths.
      </div>
    </div>
  </div>
</div>
`;

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/PerseusRenderer.tsx":
/*!********************************************!*\
  !*** ./src/components/PerseusRenderer.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PerseusRenderer: function() { return /* binding */ PerseusRenderer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst PerseusRenderer = (param)=>{\n    let { widgetType, widgetData } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!containerRef.current) return;\n        // Clear previous content\n        containerRef.current.innerHTML = \"\";\n        // Render based on widget type\n        switch(widgetType){\n            case \"numeric-input\":\n                renderNumericInput(containerRef.current);\n                break;\n            case \"categorizer\":\n                renderCategorizer(containerRef.current);\n                break;\n            case \"matcher\":\n                renderMatcher(containerRef.current);\n                break;\n            case \"orderer\":\n                renderOrderer(containerRef.current);\n                break;\n            case \"radio\":\n                renderRadio(containerRef.current);\n                break;\n            case \"expression\":\n                renderExpression(containerRef.current);\n                break;\n            case \"grapher\":\n                renderGrapher(containerRef.current);\n                break;\n            case \"matrix\":\n                renderMatrix(containerRef.current);\n                break;\n            case \"molecule\":\n                renderMolecule(containerRef.current);\n                break;\n            case \"phet-simulation\":\n                renderPhetSimulation(containerRef.current);\n                break;\n            case \"interactive-graph\":\n                renderInteractiveGraph(containerRef.current);\n                break;\n            case \"passage\":\n                renderPassage(containerRef.current);\n                break;\n            case \"sorter\":\n                renderSorter(containerRef.current);\n                break;\n            case \"cs-program\":\n                renderCSProgram(containerRef.current);\n                break;\n            case \"python-program\":\n                renderPythonProgram(containerRef.current);\n                break;\n            default:\n                renderPlaceholder(containerRef.current, widgetType);\n        }\n    }, [\n        widgetType,\n        widgetData\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"perseus-widget bg-white rounded-lg p-6 min-h-[400px]\",\n        style: {\n            fontFamily: \"Arial, sans-serif\"\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\PerseusRenderer.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PerseusRenderer, \"8puyVO4ts1RhCfXUmci3vLI3Njw=\");\n_c = PerseusRenderer;\n// Numeric Input Widget\nfunction renderNumericInput(container) {\n    container.innerHTML = '\\n    <div class=\"numeric-input-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Math Problem</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">What is 15 + 27?</p>\\n      <input \\n        type=\"number\" \\n        placeholder=\"Enter your answer\"\\n        style=\"\\n          padding: 10px; \\n          border: 2px solid #ddd; \\n          border-radius: 5px; \\n          font-size: 16px;\\n          width: 200px;\\n          margin-right: 10px;\\n        \"\\n        id=\"numeric-answer\"\\n      />\\n      <button \\n        onclick=\"checkNumericAnswer()\"\\n        style=\"\\n          background: #1c4f82; \\n          color: white; \\n          padding: 10px 20px; \\n          border: none; \\n          border-radius: 5px;\\n          cursor: pointer;\\n          font-size: 16px;\\n        \"\\n      >Check Answer</button>\\n      <div id=\"numeric-feedback\" style=\"margin-top: 15px; font-weight: bold;\"></div>\\n    </div>\\n  ';\n    // Add event handler\n    window.checkNumericAnswer = ()=>{\n        const input = document.getElementById(\"numeric-answer\");\n        const feedback = document.getElementById(\"numeric-feedback\");\n        if (input && feedback) {\n            const answer = parseInt(input.value);\n            if (answer === 42) {\n                feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Correct! Great job!</span>';\n            } else {\n                feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Try again. Think about 15 + 27.</span>';\n            }\n        }\n    };\n}\n// Categorizer Widget\nfunction renderCategorizer(container) {\n    container.innerHTML = '\\n    <div class=\"categorizer-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Categorize Animals</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">Drag each animal to the correct category:</p>\\n      \\n      <div style=\"display: flex; gap: 20px; margin-bottom: 20px;\">\\n        <div class=\"category-box\" style=\"\\n          border: 2px dashed #1c4f82; \\n          padding: 15px; \\n          min-height: 100px; \\n          width: 150px;\\n          border-radius: 8px;\\n          background: #f8f9fa;\\n        \">\\n          <h4 style=\"margin: 0 0 10px 0; color: #1c4f82;\">Mammals</h4>\\n          <div id=\"mammals-drop\" class=\"drop-zone\"></div>\\n        </div>\\n        \\n        <div class=\"category-box\" style=\"\\n          border: 2px dashed #28a745; \\n          padding: 15px; \\n          min-height: 100px; \\n          width: 150px;\\n          border-radius: 8px;\\n          background: #f8f9fa;\\n        \">\\n          <h4 style=\"margin: 0 0 10px 0; color: #28a745;\">Birds</h4>\\n          <div id=\"birds-drop\" class=\"drop-zone\"></div>\\n        </div>\\n      </div>\\n      \\n      <div style=\"display: flex; gap: 10px; flex-wrap: wrap;\">\\n        <div class=\"draggable-item\" draggable=\"true\" data-category=\"mammals\" style=\"\\n          background: #e9ecef; \\n          padding: 8px 12px; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">\\uD83D\\uDC15 Dog</div>\\n        <div class=\"draggable-item\" draggable=\"true\" data-category=\"birds\" style=\"\\n          background: #e9ecef; \\n          padding: 8px 12px; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">\\uD83E\\uDD85 Eagle</div>\\n        <div class=\"draggable-item\" draggable=\"true\" data-category=\"mammals\" style=\"\\n          background: #e9ecef; \\n          padding: 8px 12px; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">\\uD83D\\uDC31 Cat</div>\\n        <div class=\"draggable-item\" draggable=\"true\" data-category=\"birds\" style=\"\\n          background: #e9ecef; \\n          padding: 8px 12px; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">\\uD83D\\uDC26 Sparrow</div>\\n      </div>\\n      \\n      <button \\n        onclick=\"checkCategorizer()\"\\n        style=\"\\n          background: #1c4f82; \\n          color: white; \\n          padding: 10px 20px; \\n          border: none; \\n          border-radius: 5px;\\n          cursor: pointer;\\n          font-size: 16px;\\n          margin-top: 20px;\\n        \"\\n      >Check Categories</button>\\n      <div id=\"categorizer-feedback\" style=\"margin-top: 15px; font-weight: bold;\"></div>\\n    </div>\\n  ';\n    // Add drag and drop functionality\n    setupDragAndDrop();\n}\nfunction setupDragAndDrop() {\n    const draggables = document.querySelectorAll(\".draggable-item\");\n    const dropZones = document.querySelectorAll(\".drop-zone\");\n    draggables.forEach((draggable)=>{\n        draggable.addEventListener(\"dragstart\", (e)=>{\n            var _e_dataTransfer, _e_dataTransfer1;\n            (_e_dataTransfer = e.dataTransfer) === null || _e_dataTransfer === void 0 ? void 0 : _e_dataTransfer.setData(\"text/plain\", draggable.outerHTML);\n            (_e_dataTransfer1 = e.dataTransfer) === null || _e_dataTransfer1 === void 0 ? void 0 : _e_dataTransfer1.setData(\"category\", draggable.dataset.category || \"\");\n        });\n    });\n    dropZones.forEach((zone)=>{\n        zone.addEventListener(\"dragover\", (e)=>{\n            e.preventDefault();\n        });\n        zone.addEventListener(\"drop\", (e)=>{\n            var _e_dataTransfer, _e_dataTransfer1;\n            e.preventDefault();\n            const html = (_e_dataTransfer = e.dataTransfer) === null || _e_dataTransfer === void 0 ? void 0 : _e_dataTransfer.getData(\"text/plain\");\n            const category = (_e_dataTransfer1 = e.dataTransfer) === null || _e_dataTransfer1 === void 0 ? void 0 : _e_dataTransfer1.getData(\"category\");\n            if (html && zone.id.includes(category || \"\")) {\n                zone.innerHTML += html;\n            }\n        });\n    });\n    window.checkCategorizer = ()=>{\n        const mammalsZone = document.getElementById(\"mammals-drop\");\n        const birdsZone = document.getElementById(\"birds-drop\");\n        const feedback = document.getElementById(\"categorizer-feedback\");\n        if (mammalsZone && birdsZone && feedback) {\n            const mammalsCount = mammalsZone.children.length;\n            const birdsCount = birdsZone.children.length;\n            if (mammalsCount === 2 && birdsCount === 2) {\n                feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Perfect categorization!</span>';\n            } else {\n                feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Some animals are in wrong categories. Try again!</span>';\n            }\n        }\n    };\n}\n// Matcher Widget\nfunction renderMatcher(container) {\n    container.innerHTML = '\\n    <div class=\"matcher-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Match Words with Definitions</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">Click on a word, then click on its matching definition:</p>\\n      \\n      <div style=\"display: flex; gap: 40px;\">\\n        <div>\\n          <h4 style=\"color: #1c4f82;\">Words</h4>\\n          <div class=\"match-item\" data-match=\"photosynthesis\" onclick=\"selectMatch(this)\" style=\"\\n            background: #e9ecef; \\n            padding: 10px; \\n            margin: 5px 0; \\n            border-radius: 5px; \\n            cursor: pointer;\\n            border: 2px solid transparent;\\n          \">Photosynthesis</div>\\n          <div class=\"match-item\" data-match=\"gravity\" onclick=\"selectMatch(this)\" style=\"\\n            background: #e9ecef; \\n            padding: 10px; \\n            margin: 5px 0; \\n            border-radius: 5px; \\n            cursor: pointer;\\n            border: 2px solid transparent;\\n          \">Gravity</div>\\n          <div class=\"match-item\" data-match=\"democracy\" onclick=\"selectMatch(this)\" style=\"\\n            background: #e9ecef; \\n            padding: 10px; \\n            margin: 5px 0; \\n            border-radius: 5px; \\n            cursor: pointer;\\n            border: 2px solid transparent;\\n          \">Democracy</div>\\n        </div>\\n        \\n        <div>\\n          <h4 style=\"color: #28a745;\">Definitions</h4>\\n          <div class=\"match-item\" data-match=\"gravity\" onclick=\"selectMatch(this)\" style=\"\\n            background: #e9ecef; \\n            padding: 10px; \\n            margin: 5px 0; \\n            border-radius: 5px; \\n            cursor: pointer;\\n            border: 2px solid transparent;\\n          \">Force that pulls objects toward Earth</div>\\n          <div class=\"match-item\" data-match=\"photosynthesis\" onclick=\"selectMatch(this)\" style=\"\\n            background: #e9ecef; \\n            padding: 10px; \\n            margin: 5px 0; \\n            border-radius: 5px; \\n            cursor: pointer;\\n            border: 2px solid transparent;\\n          \">Process plants use to make food from sunlight</div>\\n          <div class=\"match-item\" data-match=\"democracy\" onclick=\"selectMatch(this)\" style=\"\\n            background: #e9ecef; \\n            padding: 10px; \\n            margin: 5px 0; \\n            border-radius: 5px; \\n            cursor: pointer;\\n            border: 2px solid transparent;\\n          \">Government by the people</div>\\n        </div>\\n      </div>\\n      \\n      <div id=\"matcher-feedback\" style=\"margin-top: 20px; font-weight: bold;\"></div>\\n    </div>\\n  ';\n    let selectedItems = [];\n    window.selectMatch = (element)=>{\n        if (selectedItems.length === 0) {\n            element.style.border = \"2px solid #1c4f82\";\n            selectedItems.push(element);\n        } else if (selectedItems.length === 1) {\n            const first = selectedItems[0];\n            const feedback = document.getElementById(\"matcher-feedback\");\n            if (first.dataset.match === element.dataset.match) {\n                first.style.background = \"#d4edda\";\n                element.style.background = \"#d4edda\";\n                first.style.border = \"2px solid #28a745\";\n                element.style.border = \"2px solid #28a745\";\n                if (feedback) feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Correct match!</span>';\n            } else {\n                first.style.border = \"2px solid #dc3545\";\n                element.style.border = \"2px solid #dc3545\";\n                if (feedback) feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Try again!</span>';\n                setTimeout(()=>{\n                    first.style.border = \"2px solid transparent\";\n                    element.style.border = \"2px solid transparent\";\n                }, 1000);\n            }\n            selectedItems = [];\n        }\n    };\n}\n// Orderer Widget\nfunction renderOrderer(container) {\n    container.innerHTML = '\\n    <div class=\"orderer-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Put in Chronological Order</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">Drag to arrange these historical events in order:</p>\\n      \\n      <div id=\"sortable-list\" style=\"min-height: 200px;\">\\n        <div class=\"sortable-item\" draggable=\"true\" data-order=\"3\" style=\"\\n          background: #e9ecef; \\n          padding: 15px; \\n          margin: 10px 0; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">World War II ends (1945)</div>\\n        <div class=\"sortable-item\" draggable=\"true\" data-order=\"1\" style=\"\\n          background: #e9ecef; \\n          padding: 15px; \\n          margin: 10px 0; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">American Civil War begins (1861)</div>\\n        <div class=\"sortable-item\" draggable=\"true\" data-order=\"2\" style=\"\\n          background: #e9ecef; \\n          padding: 15px; \\n          margin: 10px 0; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">World War I begins (1914)</div>\\n        <div class=\"sortable-item\" draggable=\"true\" data-order=\"4\" style=\"\\n          background: #e9ecef; \\n          padding: 15px; \\n          margin: 10px 0; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">Moon landing (1969)</div>\\n      </div>\\n      \\n      <button \\n        onclick=\"checkOrder()\"\\n        style=\"\\n          background: #1c4f82; \\n          color: white; \\n          padding: 10px 20px; \\n          border: none; \\n          border-radius: 5px;\\n          cursor: pointer;\\n          font-size: 16px;\\n          margin-top: 20px;\\n        \"\\n      >Check Order</button>\\n      <div id=\"orderer-feedback\" style=\"margin-top: 15px; font-weight: bold;\"></div>\\n    </div>\\n  ';\n    setupSortable();\n}\nfunction setupSortable() {\n    const sortableList = document.getElementById(\"sortable-list\");\n    if (!sortableList) return;\n    let draggedElement = null;\n    sortableList.addEventListener(\"dragstart\", (e)=>{\n        draggedElement = e.target;\n        if (draggedElement) {\n            draggedElement.style.opacity = \"0.5\";\n        }\n    });\n    sortableList.addEventListener(\"dragend\", (e)=>{\n        if (draggedElement) {\n            draggedElement.style.opacity = \"1\";\n            draggedElement = null;\n        }\n    });\n    sortableList.addEventListener(\"dragover\", (e)=>{\n        e.preventDefault();\n    });\n    sortableList.addEventListener(\"drop\", (e)=>{\n        e.preventDefault();\n        const target = e.target;\n        if (target && target.classList.contains(\"sortable-item\") && draggedElement) {\n            const rect = target.getBoundingClientRect();\n            const midpoint = rect.top + rect.height / 2;\n            if (e.clientY < midpoint) {\n                sortableList.insertBefore(draggedElement, target);\n            } else {\n                sortableList.insertBefore(draggedElement, target.nextSibling);\n            }\n        }\n    });\n    window.checkOrder = ()=>{\n        const items = Array.from(document.querySelectorAll(\".sortable-item\"));\n        const feedback = document.getElementById(\"orderer-feedback\");\n        let isCorrect = true;\n        items.forEach((item, index)=>{\n            const expectedOrder = parseInt(item.dataset.order || \"0\");\n            if (expectedOrder !== index + 1) {\n                isCorrect = false;\n            }\n        });\n        if (feedback) {\n            if (isCorrect) {\n                feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Perfect chronological order!</span>';\n            } else {\n                feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Not quite right. Check the dates!</span>';\n            }\n        }\n    };\n}\n// Radio (Multiple Choice) Widget\nfunction renderRadio(container) {\n    container.innerHTML = '\\n    <div class=\"radio-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Multiple Choice Question</h3>\\n      <p style=\"color: #333; margin-bottom: 15px; font-size: 16px;\">What is the capital of France?</p>\\n      \\n      <div style=\"margin-bottom: 20px;\">\\n        <label style=\"display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 5px; background: #f8f9fa;\">\\n          <input type=\"radio\" name=\"capital\" value=\"london\" style=\"margin-right: 10px;\"> London\\n        </label>\\n        <label style=\"display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 5px; background: #f8f9fa;\">\\n          <input type=\"radio\" name=\"capital\" value=\"paris\" style=\"margin-right: 10px;\"> Paris\\n        </label>\\n        <label style=\"display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 5px; background: #f8f9fa;\">\\n          <input type=\"radio\" name=\"capital\" value=\"berlin\" style=\"margin-right: 10px;\"> Berlin\\n        </label>\\n        <label style=\"display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 5px; background: #f8f9fa;\">\\n          <input type=\"radio\" name=\"capital\" value=\"madrid\" style=\"margin-right: 10px;\"> Madrid\\n        </label>\\n      </div>\\n      \\n      <button \\n        onclick=\"checkRadio()\"\\n        style=\"\\n          background: #1c4f82; \\n          color: white; \\n          padding: 10px 20px; \\n          border: none; \\n          border-radius: 5px;\\n          cursor: pointer;\\n          font-size: 16px;\\n        \"\\n      >Submit Answer</button>\\n      <div id=\"radio-feedback\" style=\"margin-top: 15px; font-weight: bold;\"></div>\\n    </div>\\n  ';\n    window.checkRadio = ()=>{\n        const selected = document.querySelector('input[name=\"capital\"]:checked');\n        const feedback = document.getElementById(\"radio-feedback\");\n        if (!selected) {\n            if (feedback) feedback.innerHTML = '<span style=\"color: #ffc107;\">Please select an answer.</span>';\n            return;\n        }\n        if (feedback) {\n            if (selected.value === \"paris\") {\n                feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Correct! Paris is the capital of France.</span>';\n            } else {\n                feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Incorrect. The capital of France is Paris.</span>';\n            }\n        }\n    };\n}\n// Expression Widget\nfunction renderExpression(container) {\n    container.innerHTML = '\\n    <div class=\"expression-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Solve the Expression</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">Simplify: 2x + 3x - 5</p>\\n      \\n      <input \\n        type=\"text\" \\n        placeholder=\"Enter simplified expression\"\\n        style=\"\\n          padding: 10px; \\n          border: 2px solid #ddd; \\n          border-radius: 5px; \\n          font-size: 16px;\\n          width: 250px;\\n          margin-right: 10px;\\n        \"\\n        id=\"expression-answer\"\\n      />\\n      <button \\n        onclick=\"checkExpression()\"\\n        style=\"\\n          background: #1c4f82; \\n          color: white; \\n          padding: 10px 20px; \\n          border: none; \\n          border-radius: 5px;\\n          cursor: pointer;\\n          font-size: 16px;\\n        \"\\n      >Check Answer</button>\\n      \\n      <div style=\"margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;\">\\n        <p style=\"margin: 0; color: #666; font-size: 14px;\">\\n          <strong>Hint:</strong> Combine like terms (terms with the same variable)\\n        </p>\\n      </div>\\n      \\n      <div id=\"expression-feedback\" style=\"margin-top: 15px; font-weight: bold;\"></div>\\n    </div>\\n  ';\n    window.checkExpression = ()=>{\n        const input = document.getElementById(\"expression-answer\");\n        const feedback = document.getElementById(\"expression-feedback\");\n        if (input && feedback) {\n            const answer = input.value.toLowerCase().replace(/\\s/g, \"\");\n            const correctAnswers = [\n                \"5x-5\",\n                \"5x+-5\",\n                \"-5+5x\"\n            ];\n            if (correctAnswers.includes(answer)) {\n                feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Excellent! 2x + 3x - 5 = 5x - 5</span>';\n            } else {\n                feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Not quite. Remember to combine like terms: 2x + 3x = 5x</span>';\n            }\n        }\n    };\n}\n// Grapher Widget - Interactive Math Graphing\nfunction renderGrapher(container) {\n    container.innerHTML = '\\n    <div class=\"grapher-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Interactive Function Grapher</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">Enter a function and see it graphed in real-time:</p>\\n\\n      <div style=\"display: flex; gap: 20px; margin-bottom: 20px;\">\\n        <div>\\n          <label style=\"display: block; margin-bottom: 5px; font-weight: bold;\">Function:</label>\\n          <input\\n            type=\"text\"\\n            id=\"function-input\"\\n            placeholder=\"e.g., x^2, sin(x), 2*x+1\"\\n            style=\"padding: 8px; border: 2px solid #ddd; border-radius: 5px; width: 200px;\"\\n          />\\n          <button\\n            onclick=\"plotFunction()\"\\n            style=\"margin-left: 10px; background: #1c4f82; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;\"\\n          >Plot</button>\\n        </div>\\n      </div>\\n\\n      <canvas id=\"graph-canvas\" width=\"500\" height=\"400\" style=\"border: 2px solid #ddd; background: white; border-radius: 5px;\"></canvas>\\n\\n      <div style=\"margin-top: 15px;\">\\n        <p style=\"color: #666; font-size: 14px;\">\\n          <strong>Try these functions:</strong> x^2, sin(x), cos(x), x^3-2*x, abs(x), sqrt(x)\\n        </p>\\n      </div>\\n    </div>\\n  ';\n    window.plotFunction = ()=>{\n        const input = document.getElementById(\"function-input\");\n        const canvas = document.getElementById(\"graph-canvas\");\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx || !input.value) return;\n        // Clear canvas\n        ctx.clearRect(0, 0, canvas.width, canvas.height);\n        // Draw axes\n        ctx.strokeStyle = \"#ccc\";\n        ctx.lineWidth = 1;\n        // Grid\n        for(let i = 0; i <= 20; i++){\n            const x = i / 20 * canvas.width;\n            const y = i / 20 * canvas.height;\n            ctx.beginPath();\n            ctx.moveTo(x, 0);\n            ctx.lineTo(x, canvas.height);\n            ctx.stroke();\n            ctx.beginPath();\n            ctx.moveTo(0, y);\n            ctx.lineTo(canvas.width, y);\n            ctx.stroke();\n        }\n        // Main axes\n        ctx.strokeStyle = \"#333\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.moveTo(canvas.width / 2, 0);\n        ctx.lineTo(canvas.width / 2, canvas.height);\n        ctx.moveTo(0, canvas.height / 2);\n        ctx.lineTo(canvas.width, canvas.height / 2);\n        ctx.stroke();\n        // Plot function\n        ctx.strokeStyle = \"#1c4f82\";\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        const func = input.value.toLowerCase().replace(/\\^/g, \"**\").replace(/sin/g, \"Math.sin\").replace(/cos/g, \"Math.cos\").replace(/sqrt/g, \"Math.sqrt\").replace(/abs/g, \"Math.abs\");\n        let firstPoint = true;\n        for(let px = 0; px < canvas.width; px++){\n            const x = (px - canvas.width / 2) / 20; // Scale\n            try {\n                const y = eval(func.replace(/x/g, x.toString()));\n                const py = canvas.height / 2 - y * 20; // Scale and flip\n                if (py >= 0 && py <= canvas.height) {\n                    if (firstPoint) {\n                        ctx.moveTo(px, py);\n                        firstPoint = false;\n                    } else {\n                        ctx.lineTo(px, py);\n                    }\n                }\n            } catch (e) {\n            // Skip invalid points\n            }\n        }\n        ctx.stroke();\n    };\n}\n// Matrix Operations Widget\nfunction renderMatrix(container) {\n    container.innerHTML = '\\n    <div class=\"matrix-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Matrix Operations</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">Enter two 2x2 matrices and perform operations:</p>\\n\\n      <div style=\"display: flex; gap: 30px; align-items: center; margin-bottom: 20px;\">\\n        <div>\\n          <h4>Matrix A:</h4>\\n          <div style=\"display: grid; grid-template-columns: 1fr 1fr; gap: 5px; width: 120px;\">\\n            <input type=\"number\" id=\"a11\" placeholder=\"a11\" style=\"padding: 5px; border: 1px solid #ddd; text-align: center;\">\\n            <input type=\"number\" id=\"a12\" placeholder=\"a12\" style=\"padding: 5px; border: 1px solid #ddd; text-align: center;\">\\n            <input type=\"number\" id=\"a21\" placeholder=\"a21\" style=\"padding: 5px; border: 1px solid #ddd; text-align: center;\">\\n            <input type=\"number\" id=\"a22\" placeholder=\"a22\" style=\"padding: 5px; border: 1px solid #ddd; text-align: center;\">\\n          </div>\\n        </div>\\n\\n        <div>\\n          <h4>Matrix B:</h4>\\n          <div style=\"display: grid; grid-template-columns: 1fr 1fr; gap: 5px; width: 120px;\">\\n            <input type=\"number\" id=\"b11\" placeholder=\"b11\" style=\"padding: 5px; border: 1px solid #ddd; text-align: center;\">\\n            <input type=\"number\" id=\"b12\" placeholder=\"b12\" style=\"padding: 5px; border: 1px solid #ddd; text-align: center;\">\\n            <input type=\"number\" id=\"b21\" placeholder=\"b21\" style=\"padding: 5px; border: 1px solid #ddd; text-align: center;\">\\n            <input type=\"number\" id=\"b22\" placeholder=\"b22\" style=\"padding: 5px; border: 1px solid #ddd; text-align: center;\">\\n          </div>\\n        </div>\\n\\n        <div>\\n          <h4>Result:</h4>\\n          <div id=\"matrix-result\" style=\"display: grid; grid-template-columns: 1fr 1fr; gap: 5px; width: 120px;\">\\n            <div style=\"padding: 5px; border: 1px solid #ddd; text-align: center; background: #f8f9fa;\">-</div>\\n            <div style=\"padding: 5px; border: 1px solid #ddd; text-align: center; background: #f8f9fa;\">-</div>\\n            <div style=\"padding: 5px; border: 1px solid #ddd; text-align: center; background: #f8f9fa;\">-</div>\\n            <div style=\"padding: 5px; border: 1px solid #ddd; text-align: center; background: #f8f9fa;\">-</div>\\n          </div>\\n        </div>\\n      </div>\\n\\n      <div style=\"display: flex; gap: 10px; margin-bottom: 20px;\">\\n        <button onclick=\"matrixAdd()\" style=\"background: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;\">A + B</button>\\n        <button onclick=\"matrixSubtract()\" style=\"background: #dc3545; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;\">A - B</button>\\n        <button onclick=\"matrixMultiply()\" style=\"background: #1c4f82; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;\">A \\xd7 B</button>\\n        <button onclick=\"matrixDeterminant()\" style=\"background: #6f42c1; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;\">Det(A)</button>\\n      </div>\\n\\n      <div id=\"matrix-explanation\" style=\"background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #1c4f82;\">\\n        <p style=\"margin: 0; color: #333;\">Select an operation to see the calculation steps.</p>\\n      </div>\\n    </div>\\n  ';\n    const getMatrix = (prefix)=>{\n        return [\n            [\n                parseFloat(document.getElementById(\"\".concat(prefix, \"11\")).value) || 0,\n                parseFloat(document.getElementById(\"\".concat(prefix, \"12\")).value) || 0\n            ],\n            [\n                parseFloat(document.getElementById(\"\".concat(prefix, \"21\")).value) || 0,\n                parseFloat(document.getElementById(\"\".concat(prefix, \"22\")).value) || 0\n            ]\n        ];\n    };\n    const displayResult = (result)=>{\n        const resultDiv = document.getElementById(\"matrix-result\");\n        if (resultDiv) {\n            resultDiv.innerHTML = '\\n        <div style=\"padding: 5px; border: 1px solid #ddd; text-align: center; background: #d4edda;\">'.concat(result[0][0], '</div>\\n        <div style=\"padding: 5px; border: 1px solid #ddd; text-align: center; background: #d4edda;\">').concat(result[0][1], '</div>\\n        <div style=\"padding: 5px; border: 1px solid #ddd; text-align: center; background: #d4edda;\">').concat(result[1][0], '</div>\\n        <div style=\"padding: 5px; border: 1px solid #ddd; text-align: center; background: #d4edda;\">').concat(result[1][1], \"</div>\\n      \");\n        }\n    };\n    window.matrixAdd = ()=>{\n        const A = getMatrix(\"a\");\n        const B = getMatrix(\"b\");\n        const result = [\n            [\n                A[0][0] + B[0][0],\n                A[0][1] + B[0][1]\n            ],\n            [\n                A[1][0] + B[1][0],\n                A[1][1] + B[1][1]\n            ]\n        ];\n        displayResult(result);\n        const explanation = document.getElementById(\"matrix-explanation\");\n        if (explanation) {\n            explanation.innerHTML = '<p style=\"margin: 0; color: #333;\"><strong>Addition:</strong> Add corresponding elements: ['.concat(A[0][0], \"+\").concat(B[0][0], \", \").concat(A[0][1], \"+\").concat(B[0][1], \"], [\").concat(A[1][0], \"+\").concat(B[1][0], \", \").concat(A[1][1], \"+\").concat(B[1][1], \"]</p>\");\n        }\n    };\n    window.matrixSubtract = ()=>{\n        const A = getMatrix(\"a\");\n        const B = getMatrix(\"b\");\n        const result = [\n            [\n                A[0][0] - B[0][0],\n                A[0][1] - B[0][1]\n            ],\n            [\n                A[1][0] - B[1][0],\n                A[1][1] - B[1][1]\n            ]\n        ];\n        displayResult(result);\n        const explanation = document.getElementById(\"matrix-explanation\");\n        if (explanation) {\n            explanation.innerHTML = '<p style=\"margin: 0; color: #333;\"><strong>Subtraction:</strong> Subtract corresponding elements: ['.concat(A[0][0], \"-\").concat(B[0][0], \", \").concat(A[0][1], \"-\").concat(B[0][1], \"], [\").concat(A[1][0], \"-\").concat(B[1][0], \", \").concat(A[1][1], \"-\").concat(B[1][1], \"]</p>\");\n        }\n    };\n    window.matrixMultiply = ()=>{\n        const A = getMatrix(\"a\");\n        const B = getMatrix(\"b\");\n        const result = [\n            [\n                A[0][0] * B[0][0] + A[0][1] * B[1][0],\n                A[0][0] * B[0][1] + A[0][1] * B[1][1]\n            ],\n            [\n                A[1][0] * B[0][0] + A[1][1] * B[1][0],\n                A[1][0] * B[0][1] + A[1][1] * B[1][1]\n            ]\n        ];\n        displayResult(result);\n        const explanation = document.getElementById(\"matrix-explanation\");\n        if (explanation) {\n            explanation.innerHTML = '<p style=\"margin: 0; color: #333;\"><strong>Multiplication:</strong> Row \\xd7 Column: ['.concat(A[0][0], \"\\xd7\").concat(B[0][0], \"+\").concat(A[0][1], \"\\xd7\").concat(B[1][0], \", \").concat(A[0][0], \"\\xd7\").concat(B[0][1], \"+\").concat(A[0][1], \"\\xd7\").concat(B[1][1], \"]</p>\");\n        }\n    };\n    window.matrixDeterminant = ()=>{\n        const A = getMatrix(\"a\");\n        const det = A[0][0] * A[1][1] - A[0][1] * A[1][0];\n        const resultDiv = document.getElementById(\"matrix-result\");\n        if (resultDiv) {\n            resultDiv.innerHTML = '\\n        <div style=\"padding: 20px; border: 1px solid #ddd; text-align: center; background: #d4edda; grid-column: 1/3; font-size: 18px; font-weight: bold;\">Det = '.concat(det, '</div>\\n        <div style=\"padding: 5px; border: 1px solid #ddd; text-align: center; background: #f8f9fa;\">-</div>\\n        <div style=\"padding: 5px; border: 1px solid #ddd; text-align: center; background: #f8f9fa;\">-</div>\\n      ');\n        }\n        const explanation = document.getElementById(\"matrix-explanation\");\n        if (explanation) {\n            explanation.innerHTML = '<p style=\"margin: 0; color: #333;\"><strong>Determinant:</strong> ad - bc = '.concat(A[0][0], \"\\xd7\").concat(A[1][1], \" - \").concat(A[0][1], \"\\xd7\").concat(A[1][0], \" = \").concat(det, \"</p>\");\n        }\n    };\n}\n// Molecule Builder Widget\nfunction renderMolecule(container) {\n    container.innerHTML = '\\n    <div class=\"molecule-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Molecule Builder</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">Build molecules by connecting atoms:</p>\\n\\n      <div style=\"display: flex; gap: 20px;\">\\n        <div style=\"flex: 1;\">\\n          <h4>Available Atoms:</h4>\\n          <div style=\"display: flex; gap: 10px; margin-bottom: 20px;\">\\n            <button onclick=\"addAtom(\\'H\\')\" style=\"background: #ff6b6b; color: white; padding: 10px; border: none; border-radius: 50%; width: 50px; height: 50px; cursor: pointer; font-weight: bold;\">H</button>\\n            <button onclick=\"addAtom(\\'C\\')\" style=\"background: #4ecdc4; color: white; padding: 10px; border: none; border-radius: 50%; width: 50px; height: 50px; cursor: pointer; font-weight: bold;\">C</button>\\n            <button onclick=\"addAtom(\\'O\\')\" style=\"background: #45b7d1; color: white; padding: 10px; border: none; border-radius: 50%; width: 50px; height: 50px; cursor: pointer; font-weight: bold;\">O</button>\\n            <button onclick=\"addAtom(\\'N\\')\" style=\"background: #96ceb4; color: white; padding: 10px; border: none; border-radius: 50%; width: 50px; height: 50px; cursor: pointer; font-weight: bold;\">N</button>\\n          </div>\\n\\n          <canvas id=\"molecule-canvas\" width=\"400\" height=\"300\" style=\"border: 2px solid #ddd; background: white; border-radius: 5px; cursor: crosshair;\"></canvas>\\n\\n          <div style=\"margin-top: 10px;\">\\n            <button onclick=\"clearMolecule()\" style=\"background: #dc3545; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;\">Clear</button>\\n            <button onclick=\"identifyMolecule()\" style=\"background: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;\">Identify Molecule</button>\\n          </div>\\n        </div>\\n\\n        <div style=\"width: 200px;\">\\n          <h4>Molecule Info:</h4>\\n          <div id=\"molecule-info\" style=\"background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #1c4f82;\">\\n            <p style=\"margin: 0; color: #333;\">Click atoms to add them to the canvas, then click \"Identify Molecule\"</p>\\n          </div>\\n        </div>\\n      </div>\\n    </div>\\n  ';\n    let atoms = [];\n    let selectedAtomType = \"H\";\n    window.addAtom = (type)=>{\n        selectedAtomType = type;\n        const info = document.getElementById(\"molecule-info\");\n        if (info) {\n            info.innerHTML = '<p style=\"margin: 0; color: #333;\">Selected: <strong>'.concat(type, \"</strong><br>Click on canvas to place atom</p>\");\n        }\n    };\n    const canvas = document.getElementById(\"molecule-canvas\");\n    if (canvas) {\n        canvas.addEventListener(\"click\", (e)=>{\n            const rect = canvas.getBoundingClientRect();\n            const x = e.clientX - rect.left;\n            const y = e.clientY - rect.top;\n            atoms.push({\n                x,\n                y,\n                type: selectedAtomType\n            });\n            drawMolecule();\n        });\n    }\n    const drawMolecule = ()=>{\n        const canvas = document.getElementById(\"molecule-canvas\");\n        const ctx = canvas === null || canvas === void 0 ? void 0 : canvas.getContext(\"2d\");\n        if (!ctx) return;\n        ctx.clearRect(0, 0, canvas.width, canvas.height);\n        // Draw bonds (simple lines between nearby atoms)\n        ctx.strokeStyle = \"#333\";\n        ctx.lineWidth = 2;\n        for(let i = 0; i < atoms.length; i++){\n            for(let j = i + 1; j < atoms.length; j++){\n                const dist = Math.sqrt((atoms[i].x - atoms[j].x) ** 2 + (atoms[i].y - atoms[j].y) ** 2);\n                if (dist < 80) {\n                    ctx.beginPath();\n                    ctx.moveTo(atoms[i].x, atoms[i].y);\n                    ctx.lineTo(atoms[j].x, atoms[j].y);\n                    ctx.stroke();\n                }\n            }\n        }\n        // Draw atoms\n        atoms.forEach((atom)=>{\n            const colors = {\n                H: \"#ff6b6b\",\n                C: \"#4ecdc4\",\n                O: \"#45b7d1\",\n                N: \"#96ceb4\"\n            };\n            ctx.fillStyle = colors[atom.type] || \"#ccc\";\n            ctx.beginPath();\n            ctx.arc(atom.x, atom.y, 20, 0, 2 * Math.PI);\n            ctx.fill();\n            ctx.fillStyle = \"white\";\n            ctx.font = \"bold 14px Arial\";\n            ctx.textAlign = \"center\";\n            ctx.fillText(atom.type, atom.x, atom.y + 5);\n        });\n    };\n    window.clearMolecule = ()=>{\n        atoms = [];\n        drawMolecule();\n        const info = document.getElementById(\"molecule-info\");\n        if (info) {\n            info.innerHTML = '<p style=\"margin: 0; color: #333;\">Canvas cleared. Select an atom and start building!</p>';\n        }\n    };\n    window.identifyMolecule = ()=>{\n        const counts = atoms.reduce((acc, atom)=>{\n            acc[atom.type] = (acc[atom.type] || 0) + 1;\n            return acc;\n        }, {});\n        let moleculeName = \"Unknown\";\n        const formula = Object.entries(counts).map((param)=>{\n            let [type, count] = param;\n            return count > 1 ? \"\".concat(type).concat(count) : type;\n        }).join(\"\");\n        // Simple molecule identification\n        if (formula === \"H2O\") moleculeName = \"Water\";\n        else if (formula === \"CO2\") moleculeName = \"Carbon Dioxide\";\n        else if (formula === \"CH4\") moleculeName = \"Methane\";\n        else if (formula === \"NH3\") moleculeName = \"Ammonia\";\n        else if (formula === \"H2\") moleculeName = \"Hydrogen Gas\";\n        else if (formula === \"O2\") moleculeName = \"Oxygen Gas\";\n        const info = document.getElementById(\"molecule-info\");\n        if (info) {\n            info.innerHTML = '\\n        <p style=\"margin: 0; color: #333;\">\\n          <strong>Formula:</strong> '.concat(formula, \"<br>\\n          <strong>Name:</strong> \").concat(moleculeName, \"<br>\\n          <strong>Atoms:</strong> \").concat(atoms.length, \"\\n        </p>\\n      \");\n        }\n    };\n}\n// Reading Passage Widget\nfunction renderPassage(container) {\n    container.innerHTML = '\\n    <div class=\"passage-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Reading Comprehension</h3>\\n\\n      <div style=\"background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #1c4f82;\">\\n        <h4 style=\"color: #1c4f82; margin-top: 0;\">The Water Cycle</h4>\\n        <p style=\"line-height: 1.6; color: #333;\">\\n          The water cycle is the continuous movement of water on, above, and below the surface of the Earth.\\n          Water evaporates from oceans, lakes, and rivers due to heat from the sun. This water vapor rises into\\n          the atmosphere where it cools and condenses into tiny droplets, forming clouds. When these droplets\\n          become too heavy, they fall back to Earth as precipitation in the form of rain, snow, or hail.\\n          Some of this water flows into rivers and streams, eventually returning to the oceans, while some\\n          seeps into the ground to become groundwater. This process repeats continuously, making the water\\n          cycle essential for all life on Earth.\\n        </p>\\n      </div>\\n\\n      <div style=\"background: white; padding: 20px; border-radius: 8px; border: 2px solid #ddd;\">\\n        <h4 style=\"color: #1c4f82; margin-top: 0;\">Comprehension Questions</h4>\\n\\n        <div style=\"margin-bottom: 20px;\">\\n          <p style=\"font-weight: bold; margin-bottom: 10px;\">1. What causes water to evaporate from oceans and lakes?</p>\\n          <label style=\"display: block; margin: 5px 0; cursor: pointer;\">\\n            <input type=\"radio\" name=\"q1\" value=\"wind\" style=\"margin-right: 8px;\"> Wind\\n          </label>\\n          <label style=\"display: block; margin: 5px 0; cursor: pointer;\">\\n            <input type=\"radio\" name=\"q1\" value=\"heat\" style=\"margin-right: 8px;\"> Heat from the sun\\n          </label>\\n          <label style=\"display: block; margin: 5px 0; cursor: pointer;\">\\n            <input type=\"radio\" name=\"q1\" value=\"gravity\" style=\"margin-right: 8px;\"> Gravity\\n          </label>\\n        </div>\\n\\n        <div style=\"margin-bottom: 20px;\">\\n          <p style=\"font-weight: bold; margin-bottom: 10px;\">2. What happens when water vapor cools in the atmosphere?</p>\\n          <label style=\"display: block; margin: 5px 0; cursor: pointer;\">\\n            <input type=\"radio\" name=\"q2\" value=\"disappears\" style=\"margin-right: 8px;\"> It disappears\\n          </label>\\n          <label style=\"display: block; margin: 5px 0; cursor: pointer;\">\\n            <input type=\"radio\" name=\"q2\" value=\"condenses\" style=\"margin-right: 8px;\"> It condenses into droplets\\n          </label>\\n          <label style=\"display: block; margin: 5px 0; cursor: pointer;\">\\n            <input type=\"radio\" name=\"q2\" value=\"heats\" style=\"margin-right: 8px;\"> It heats up more\\n          </label>\\n        </div>\\n\\n        <div style=\"margin-bottom: 20px;\">\\n          <p style=\"font-weight: bold; margin-bottom: 10px;\">3. List three forms of precipitation mentioned in the passage:</p>\\n          <input type=\"text\" id=\"precipitation-answer\" placeholder=\"Enter three forms separated by commas\"\\n                 style=\"width: 100%; padding: 10px; border: 2px solid #ddd; border-radius: 5px;\">\\n        </div>\\n\\n        <button onclick=\"checkPassageAnswers()\"\\n                style=\"background: #1c4f82; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;\">\\n          Check Answers\\n        </button>\\n\\n        <div id=\"passage-feedback\" style=\"margin-top: 15px; font-weight: bold;\"></div>\\n      </div>\\n    </div>\\n  ';\n    window.checkPassageAnswers = ()=>{\n        const q1 = document.querySelector('input[name=\"q1\"]:checked');\n        const q2 = document.querySelector('input[name=\"q2\"]:checked');\n        const q3 = document.getElementById(\"precipitation-answer\");\n        const feedback = document.getElementById(\"passage-feedback\");\n        let score = 0;\n        let results = [];\n        if ((q1 === null || q1 === void 0 ? void 0 : q1.value) === \"heat\") {\n            score++;\n            results.push(\"✓ Question 1: Correct!\");\n        } else {\n            results.push(\"✗ Question 1: The sun's heat causes evaporation.\");\n        }\n        if ((q2 === null || q2 === void 0 ? void 0 : q2.value) === \"condenses\") {\n            score++;\n            results.push(\"✓ Question 2: Correct!\");\n        } else {\n            results.push(\"✗ Question 2: Water vapor condenses into droplets.\");\n        }\n        const precipitationAnswer = (q3 === null || q3 === void 0 ? void 0 : q3.value.toLowerCase()) || \"\";\n        const hasRain = precipitationAnswer.includes(\"rain\");\n        const hasSnow = precipitationAnswer.includes(\"snow\");\n        const hasHail = precipitationAnswer.includes(\"hail\");\n        if (hasRain && hasSnow && hasHail) {\n            score++;\n            results.push(\"✓ Question 3: Correct! Rain, snow, and hail.\");\n        } else {\n            results.push(\"✗ Question 3: The three forms are rain, snow, and hail.\");\n        }\n        if (feedback) {\n            const color = score === 3 ? \"#28a745\" : score >= 2 ? \"#ffc107\" : \"#dc3545\";\n            feedback.innerHTML = '\\n        <div style=\"color: '.concat(color, ';\">Score: ').concat(score, \"/3</div>\\n        \").concat(results.map((result)=>'<div style=\"margin: 5px 0;\">'.concat(result, \"</div>\")).join(\"\"), \"\\n      \");\n        }\n    };\n}\n// Programming Widget - CS Concepts\nfunction renderCSProgram(container) {\n    container.innerHTML = '\\n    <div class=\"cs-program-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Computer Science Concepts</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">Learn programming concepts through interactive exercises:</p>\\n\\n      <div style=\"display: flex; gap: 20px;\">\\n        <div style=\"flex: 1;\">\\n          <h4>Algorithm Challenge: Sorting</h4>\\n          <p style=\"color: #666; margin-bottom: 15px;\">Arrange these numbers in ascending order by dragging:</p>\\n\\n          <div id=\"sorting-container\" style=\"display: flex; gap: 10px; margin-bottom: 20px; min-height: 60px; padding: 10px; border: 2px dashed #ddd; border-radius: 5px;\">\\n            <div class=\"sort-item\" draggable=\"true\" data-value=\"7\" style=\"background: #ff6b6b; color: white; padding: 15px; border-radius: 5px; cursor: move; font-weight: bold; user-select: none;\">7</div>\\n            <div class=\"sort-item\" draggable=\"true\" data-value=\"3\" style=\"background: #4ecdc4; color: white; padding: 15px; border-radius: 5px; cursor: move; font-weight: bold; user-select: none;\">3</div>\\n            <div class=\"sort-item\" draggable=\"true\" data-value=\"9\" style=\"background: #45b7d1; color: white; padding: 15px; border-radius: 5px; cursor: move; font-weight: bold; user-select: none;\">9</div>\\n            <div class=\"sort-item\" draggable=\"true\" data-value=\"1\" style=\"background: #96ceb4; color: white; padding: 15px; border-radius: 5px; cursor: move; font-weight: bold; user-select: none;\">1</div>\\n            <div class=\"sort-item\" draggable=\"true\" data-value=\"5\" style=\"background: #feca57; color: white; padding: 15px; border-radius: 5px; cursor: move; font-weight: bold; user-select: none;\">5</div>\\n          </div>\\n\\n          <button onclick=\"checkSorting()\" style=\"background: #1c4f82; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;\">Check Order</button>\\n          <button onclick=\"shuffleNumbers()\" style=\"background: #6c757d; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;\">Shuffle</button>\\n\\n          <div id=\"sorting-feedback\" style=\"margin-top: 15px; font-weight: bold;\"></div>\\n        </div>\\n\\n        <div style=\"width: 300px;\">\\n          <h4>Binary Converter</h4>\\n          <p style=\"color: #666; margin-bottom: 15px;\">Convert between decimal and binary:</p>\\n\\n          <div style=\"margin-bottom: 15px;\">\\n            <label style=\"display: block; margin-bottom: 5px; font-weight: bold;\">Decimal:</label>\\n            <input type=\"number\" id=\"decimal-input\" placeholder=\"Enter decimal number\" min=\"0\" max=\"255\"\\n                   style=\"width: 100%; padding: 8px; border: 2px solid #ddd; border-radius: 5px;\">\\n          </div>\\n\\n          <div style=\"margin-bottom: 15px;\">\\n            <label style=\"display: block; margin-bottom: 5px; font-weight: bold;\">Binary:</label>\\n            <input type=\"text\" id=\"binary-input\" placeholder=\"Enter binary number\" pattern=\"[01]*\"\\n                   style=\"width: 100%; padding: 8px; border: 2px solid #ddd; border-radius: 5px;\">\\n          </div>\\n\\n          <div style=\"display: flex; gap: 10px; margin-bottom: 15px;\">\\n            <button onclick=\"convertToBinary()\" style=\"background: #28a745; color: white; padding: 8px 12px; border: none; border-radius: 5px; cursor: pointer; font-size: 12px;\">Dec→Bin</button>\\n            <button onclick=\"convertToDecimal()\" style=\"background: #dc3545; color: white; padding: 8px 12px; border: none; border-radius: 5px; cursor: pointer; font-size: 12px;\">Bin→Dec</button>\\n          </div>\\n\\n          <div id=\"binary-explanation\" style=\"background: #f8f9fa; padding: 10px; border-radius: 5px; border-left: 4px solid #1c4f82; font-size: 14px;\">\\n            <p style=\"margin: 0; color: #333;\">Enter a number and click convert to see the binary representation!</p>\\n          </div>\\n        </div>\\n      </div>\\n    </div>\\n  ';\n    // Setup drag and drop for sorting\n    const setupSorting = ()=>{\n        const container = document.getElementById(\"sorting-container\");\n        if (!container) return;\n        let draggedElement = null;\n        container.addEventListener(\"dragstart\", (e)=>{\n            draggedElement = e.target;\n            if (draggedElement) {\n                draggedElement.style.opacity = \"0.5\";\n            }\n        });\n        container.addEventListener(\"dragend\", (e)=>{\n            if (draggedElement) {\n                draggedElement.style.opacity = \"1\";\n                draggedElement = null;\n            }\n        });\n        container.addEventListener(\"dragover\", (e)=>{\n            e.preventDefault();\n        });\n        container.addEventListener(\"drop\", (e)=>{\n            e.preventDefault();\n            const target = e.target;\n            if (target && target.classList.contains(\"sort-item\") && draggedElement) {\n                const rect = target.getBoundingClientRect();\n                const midpoint = rect.left + rect.width / 2;\n                if (e.clientX < midpoint) {\n                    container.insertBefore(draggedElement, target);\n                } else {\n                    container.insertBefore(draggedElement, target.nextSibling);\n                }\n            }\n        });\n    };\n    setupSorting();\n    window.checkSorting = ()=>{\n        const items = Array.from(document.querySelectorAll(\".sort-item\"));\n        const values = items.map((item)=>parseInt(item.dataset.value || \"0\"));\n        const isCorrect = values.every((val, i)=>i === 0 || val >= values[i - 1]);\n        const feedback = document.getElementById(\"sorting-feedback\");\n        if (feedback) {\n            if (isCorrect) {\n                feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Perfect! The numbers are in ascending order: ' + values.join(\" < \") + \"</span>\";\n            } else {\n                feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Not quite right. Try arranging from smallest to largest.</span>';\n            }\n        }\n    };\n    window.shuffleNumbers = ()=>{\n        const container = document.getElementById(\"sorting-container\");\n        if (!container) return;\n        const items = Array.from(container.children);\n        items.sort(()=>Math.random() - 0.5);\n        items.forEach((item)=>container.appendChild(item));\n        const feedback = document.getElementById(\"sorting-feedback\");\n        if (feedback) feedback.innerHTML = \"\";\n    };\n    window.convertToBinary = ()=>{\n        const decimalInput = document.getElementById(\"decimal-input\");\n        const binaryInput = document.getElementById(\"binary-input\");\n        const explanation = document.getElementById(\"binary-explanation\");\n        const decimal = parseInt(decimalInput.value);\n        if (isNaN(decimal) || decimal < 0) return;\n        const binary = decimal.toString(2);\n        binaryInput.value = binary;\n        if (explanation) {\n            explanation.innerHTML = '\\n        <p style=\"margin: 0; color: #333;\">\\n          <strong>'.concat(decimal, \"</strong> in binary is <strong>\").concat(binary, \"</strong><br>\\n          <small>Each position represents a power of 2: \").concat(binary.split(\"\").map((bit, i)=>\"\".concat(bit, \"\\xd72^\").concat(binary.length - 1 - i)).join(\" + \"), \"</small>\\n        </p>\\n      \");\n        }\n    };\n    window.convertToDecimal = ()=>{\n        const decimalInput = document.getElementById(\"decimal-input\");\n        const binaryInput = document.getElementById(\"binary-input\");\n        const explanation = document.getElementById(\"binary-explanation\");\n        const binary = binaryInput.value;\n        if (!/^[01]+$/.test(binary)) return;\n        const decimal = parseInt(binary, 2);\n        decimalInput.value = decimal.toString();\n        if (explanation) {\n            explanation.innerHTML = '\\n        <p style=\"margin: 0; color: #333;\">\\n          <strong>'.concat(binary, \"</strong> in decimal is <strong>\").concat(decimal, \"</strong><br>\\n          <small>Calculation: \").concat(binary.split(\"\").map((bit, i)=>\"\".concat(bit, \"\\xd7\").concat(Math.pow(2, binary.length - 1 - i))).join(\" + \"), \" = \").concat(decimal, \"</small>\\n        </p>\\n      \");\n        }\n    };\n}\n// Quick implementations for remaining widgets\nfunction renderPhetSimulation(container) {\n    container.innerHTML = '\\n    <div style=\"text-align: center; padding: 40px;\">\\n      <div style=\"font-size: 48px; margin-bottom: 20px;\">⚗️</div>\\n      <h3 style=\"color: #1c4f82; margin-bottom: 15px;\">Physics Simulation</h3>\\n      <p style=\"color: #666; margin-bottom: 20px;\">Interactive physics experiments and simulations</p>\\n      <div style=\"background: #e3f2fd; padding: 20px; border-radius: 8px; border-left: 4px solid #1976d2;\">\\n        <p style=\"margin: 0; color: #333; font-size: 14px;\">\\n          Real PhET simulation integration - Interactive physics experiments\\n        </p>\\n      </div>\\n    </div>\\n  ';\n}\nfunction renderInteractiveGraph(container) {\n    container.innerHTML = '\\n    <div style=\"text-align: center; padding: 40px;\">\\n      <div style=\"font-size: 48px; margin-bottom: 20px;\">\\uD83D\\uDCCA</div>\\n      <h3 style=\"color: #1c4f82; margin-bottom: 15px;\">Interactive Data Graphs</h3>\\n      <p style=\"color: #666; margin-bottom: 20px;\">Analyze and manipulate scientific data</p>\\n      <div style=\"background: #f3e5f5; padding: 20px; border-radius: 8px; border-left: 4px solid #7b1fa2;\">\\n        <p style=\"margin: 0; color: #333; font-size: 14px;\">\\n          Real interactive graphing tools for data analysis\\n        </p>\\n      </div>\\n    </div>\\n  ';\n}\nfunction renderSorter(container) {\n    container.innerHTML = '\\n    <div style=\"text-align: center; padding: 40px;\">\\n      <div style=\"font-size: 48px; margin-bottom: 20px;\">\\uD83D\\uDD04</div>\\n      <h3 style=\"color: #1c4f82; margin-bottom: 15px;\">Logic Sorter</h3>\\n      <p style=\"color: #666; margin-bottom: 20px;\">Sort items by logical rules and patterns</p>\\n      <div style=\"background: #fff3e0; padding: 20px; border-radius: 8px; border-left: 4px solid #f57c00;\">\\n        <p style=\"margin: 0; color: #333; font-size: 14px;\">\\n          Advanced sorting algorithms and logical reasoning\\n        </p>\\n      </div>\\n    </div>\\n  ';\n}\nfunction renderPythonProgram(container) {\n    container.innerHTML = '\\n    <div style=\"text-align: center; padding: 40px;\">\\n      <div style=\"font-size: 48px; margin-bottom: 20px;\">\\uD83D\\uDC0D</div>\\n      <h3 style=\"color: #1c4f82; margin-bottom: 15px;\">Python Programming</h3>\\n      <p style=\"color: #666; margin-bottom: 20px;\">Interactive Python coding exercises</p>\\n      <div style=\"background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #388e3c;\">\\n        <p style=\"margin: 0; color: #333; font-size: 14px;\">\\n          Real Python interpreter with interactive coding challenges\\n        </p>\\n      </div>\\n    </div>\\n  ';\n}\n// Placeholder for unsupported widgets\nfunction renderPlaceholder(container, widgetType) {\n    container.innerHTML = '\\n    <div style=\"text-align: center; padding: 40px;\">\\n      <div style=\"font-size: 48px; margin-bottom: 20px;\">\\uD83C\\uDFAE</div>\\n      <h3 style=\"color: #1c4f82; margin-bottom: 15px;\">Perseus Widget: '.concat(widgetType, '</h3>\\n      <p style=\"color: #666; margin-bottom: 20px;\">This educational widget is being loaded...</p>\\n      <div style=\"background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #1c4f82;\">\\n        <p style=\"margin: 0; color: #333; font-size: 14px;\">\\n          Real Khan Academy Perseus widget integration in progress.\\n          This will render the actual interactive educational content.\\n        </p>\\n      </div>\\n    </div>\\n  ');\n}\nvar _c;\n$RefreshReg$(_c, \"PerseusRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/PerseusRenderer.tsx\n"));

/***/ })

});
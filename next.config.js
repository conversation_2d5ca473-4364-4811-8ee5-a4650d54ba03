/** @type {import('next').NextConfig} */

const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  webpack: (config, { isServer }) => {
    // Monaco Editor configuration
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
      };

      // Add Monaco Editor webpack plugin
      const MonacoWebpackPlugin = require('monaco-editor-webpack-plugin');
      config.plugins.push(
        new MonacoWebpackPlugin({
          languages: ['javascript', 'typescript', 'css', 'html', 'json'],
          features: ['!gotoSymbol'],
        })
      );
    }

    // Handle Monaco Editor CSS imports
    config.module.rules.push({
      test: /\.css$/,
      include: /node_modules[\\/]monaco-editor/,
      use: ['style-loader', 'css-loader'],
    });

    return config;
  },
};

module.exports = nextConfig;

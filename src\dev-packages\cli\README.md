<div align='center'>

<br />

<img src='https://raw.githubusercontent.com/eclipse-theia/theia/master/logo/theia.svg?sanitize=true' alt='theia-ext-logo' width='100px' />

<h2>ECLIPSE THEIA - CLI</h2>

<hr />

</div>

## Outline

- [Outline](#outline)
- [Description](#description)
- [Getting Started](#getting-started)
- [Configure](#configure)
  - [Application Properties](#application-properties)
  - [Default Preferences](#default-preferences)
  - [Default Theme](#default-theme)
  - [Build Target](#build-target)
  - [Electron Frontend Application Config](#electron-frontend-application-config)
  - [Using Latest Builds](#using-latest-builds)
- [Building](#building)
  - [Build](#build)
  - [Watch](#watch)
  - [Clean](#clean)
- [Rebuilding Native Modules](#rebuilding-native-modules)
- [Running](#running)
- [Debugging](#debugging)
- [Testing](#testing)
  - [Enabling Tests](#enabling-tests)
  - [Writing Tests](#writing-tests)
  - [Running Tests](#running-tests)
  - [Configuring Tests](#configuring-tests)
  - [Inspecting Tests](#inspecting-tests)
  - [Reporting Test Coverage](#reporting-test-coverage)
- [Downloading Plugins](#downloading-plugins)
- [Autogenerated Application](#autogenerated-application)
- [Additional Information](#additional-information)
- [License](#license)
- [Trademark](#trademark)

## Description

The `@theia/cli` package provides helpful scripts and commands for extension and application development.
The contributed `theia`, is a command line tool to manage Theia-based applications.

## Getting Started

Install `@theia/cli` as a dev dependency in your application.

With yarn:

```bash
yarn add @theia/cli@next --dev
```

With npm:

```bash
npm install @theia/cli@next --save-dev
```

## Configure

A Theia-based application can be configured via the `theia` property as described in the application's `package.json`.

### Application Properties

It is possible `Application Properties` for a given application.\
For example, an application can define it's `applicationName` using the following syntax:

```json
"theia": {
    "frontend": {
      "config": {
        "applicationName": "Custom Application Name",
      }
    }
  },
```

### Default Preferences

If required, an application can define for a given preference, the default value.
For example, an application can update the preference value for `files.enableTrash` based on it's requirements:

```json

"theia": {
    "frontend": {
      "config": {
        "preferences": {
          "files.enableTrash": false
        }
      }
    }
  },
```

### Default Theme

Default color and icon themes can be configured in `theia.frontend.config` section:

```json
"theia": {
    "frontend": {
      "config": {
        "defaultTheme": "light",
        "defaultIconTheme": "vs-seti"
      }
    }
  },
```

### Build Target

The following targets are supported: `browser` and `electron`. By default `browser` target is used.
The target can be configured in the `package.json` via `theia/target` property, e.g:

```json
{
    "theia": {
        "target": "electron"
    },
    "dependencies": {
        "@theia/electron": "latest"
    }
}
```

For `electron` target applications, is it mandatory to include **Electron** runtime dependencies. The `@theia/electron` package is the easiest way to install the necessary dependencies.

### Electron Frontend Application Config

The `electron` frontend application configuration provides configuration options for the `electron` target.\
The currently supported configurations are:

- `disallowReloadKeybinding`: if set to `true`, reloading the current browser window won't be possible with the <kbd>Ctrl/Cmd</kbd> + <kbd>r</kbd> keybinding. It is `false` by default. Has no effect if not in an electron environment.
- `windowOptions`: override or add properties to the electron `windowOptions`.

```json
{
    "theia": {
        "target": "electron",
        "frontend": {
            "config": {
                "electron": {
                    "disallowReloadKeybinding": true,
                    "windowOptions": {
                        "titleBarStyle": "hidden",
                        "webPreferences": {
                            "webSecurity": false,
                            "nodeIntegration": true,
                            "webviewTag": true
                        }
                    }
                }
            }
        }
    }
}
```

### Using Latest Builds

If you set `next` in your theia config, then Theia will prefer `next` over `latest` as the latest tag.

```json
{
    "theia": {
        "next": "true"
    }
}
```

## Building

### Build

The following command can be used to build the application:

**Development**

    theia build --mode development

**Production**

    theia build

### Watch

The following command can be used to rebuild the application on each change:

    theia build --watch --mode development

### Clean

The following command can be used to clean up the build result:

In order to clean up the build result:

    theia clean

Arguments are passed directly to [webpack](https://webpack.js.org/). Use `--help` to learn which options are supported.

## Rebuilding Native Modules

In order to run Electron targets, one should rebuild native node modules for an electron version:

    theia rebuild

To rollback native modules, change the target to `browser` and run the command again.

## Running

To run the backend server:

    theia start

For the browser target a server is started on <http://localhost:3000> by default.
For the electron target a server is started on `localhost` host with the dynamically allocated port by default.

Arguments are passed directly to a server, use `--help` to learn which options are supported.

## Debugging

To debug the backend server:

    theia start --inspect

Theia CLI accepts `--inspect` node flag: <https://nodejs.org/en/docs/inspector/#command-line-options>.

## Testing

### Enabling Tests

First enable `expose-loader` in `webpack.config.js`
to expose modules from bundled code to tests
by un-commenting:

```js
/**
 * Expose bundled modules on window.theia.moduleName namespace, e.g.
 * window['theia']['@theia/core/lib/common/uri'].
 * Such syntax can be used by external code, for instance, for testing.
config.module.rules.push({
    test: /\.js$/,
    loader: require.resolve('@theia/application-manager/lib/expose-loader')
}); */
```

After that run `theia build` again to expose modules in generated bundle files.

### Writing Tests

See [API Integration Testing](../../doc/api-testing.md) docs.

### Running Tests

To start the backend server and run API tests against it:

    theia test

After running test this command terminates. It accepts the same arguments as `start` command,
but as well additional arguments to specify test files, enable inspection or generate test coverage.

### Configuring Tests

To specify test files:

    theia test . --test-spec=./test/*.spec.js --plugins=./plugins

This command starts the application with a current directory as a workspace,
load VS Code extensions from `./plugins`
and run test files matching `./test/*.spec.js` glob.

Use `theia test --help` to learn more options. Test specific options start with `--test-`.

### Inspecting Tests

To inspect tests:

    theia test . --test-spec=./test/*.spec.js --test-inspect --inspect

This command starts the application server in the debug mode
as well as open the Chrome devtools to debug frontend code and test files.
One can reload/rerun code and tests by simply reloading the page.

> Important! Since tests are relying on focus, while running tests keep the page focused.

### Reporting Test Coverage

To report test coverage:

    theia test . --test-spec=./test/*.spec.js --test-coverage

This command executes tests and generate test coverage files consumable by [Istanbul](https://github.com/istanbuljs/istanbuljs).

## Downloading Plugins

The `@theia/cli` package provides a utility for applications to define and download a list of plugins it requires as part of their application using the command:

    theia download:plugins

This utility works by declaring in the `package.json` a location to store downloaded plugins, as well as defining each plugin the application wishes to download.

The property `theiaPluginsDir` describes the location of which to download plugins (relative to the `package.json`), for example:

```json
"theiaPluginsDir": "plugins",
```

The property `theiaPlugins` describes the list of plugins to download, for example:

```json
"theiaPlugins": {
    "vscode.theme-defaults": "https://open-vsx.org/api/vscode/theme-defaults/1.62.3/file/vscode.theme-defaults-1.62.3.vsix",
    "vscode-builtin-extension-pack": "https://open-vsx.org/api/eclipse-theia/builtin-extension-pack/1.50.0/file/eclipse-theia.builtin-extension-pack-1.50.0.vsix",
    "vscode-editorconfig": "https://open-vsx.org/api/EditorConfig/EditorConfig/0.14.4/file/EditorConfig.EditorConfig-0.14.4.vsix",
    "vscode-eslint": "https://open-vsx.org/api/dbaeumer/vscode-eslint/2.1.1/file/dbaeumer.vscode-eslint-2.1.1.vsix",
    "rust-analyzer": "https://open-vsx.org/api/rust-lang/rust-analyzer/${targetPlatform}/0.4.1473/file/rust-lang.rust-analyzer-0.4.1473@${targetPlatform}.vsix"
}
```

As seen in the `rust-analyzer` entry we can use placeholders in the URLs. Supported placeholders are:

- The `${targetPlatform}` Placeholder, which resolves to a string like `win32-x64` describing the local system and architecture. This is useful for adding non-universal plugins.

Please note that in order to use `extensionPacks` properly you should use `namespace.name` as the `id` you give extensions so that when resolving the pack we do not re-download an existing plugin under a different name.

The property `theiaPluginsExcludeIds` can be used to declare the list of plugin `ids` to exclude when using extension-packs.
The `ids` referenced by the property will not be downloaded when resolving extension-packs, and can be used to omit extensions which are problematic or unwanted. The format of the property is as follows:

```json
"theiaPluginsExcludeIds": [
  "vscode.cpp"
]
```

## Autogenerated Application

This package can auto-generate application code for both the backend and frontend, as well as webpack configuration files.

When targeting Electron, the `electron-main.js` script will spawn the backend process in a Node.js sub-process, where Electron's API won't be available. To prevent the generated application from forking the backend, you can pass a `--no-cluster` flag. This flag is mostly useful/used for debugging.

```sh
# when developing a Theia application with @theia/cli:
yarn theia start --no-cluster

# when starting a bundled application made using @theia/cli:
bundled-application.exe --no-cluster
```

## Additional Information

- [Theia - GitHub](https://github.com/eclipse-theia/theia)
- [Theia - Website](https://theia-ide.org/)

## License

- [Eclipse Public License 2.0](http://www.eclipse.org/legal/epl-2.0/)
- [一 (Secondary) GNU General Public License, version 2 with the GNU Classpath Exception](https://projects.eclipse.org/license/secondary-gpl-2.0-cp)

## Trademark

"Theia" is a trademark of the Eclipse Foundation
<https://www.eclipse.org/theia>

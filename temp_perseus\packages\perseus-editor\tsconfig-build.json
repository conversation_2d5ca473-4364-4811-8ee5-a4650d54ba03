{
    "extends": "../tsconfig-shared.json",
    "compilerOptions": {
        "outDir": "./dist",
        "rootDir": "src",
        "paths": {
            "jsdiff": ["../../vendor/jsdiff/jsdiff.js"],
            // NOTE(kevinb): We have to repeat this here because TS doesn't do
            // intelligent merge of tsconfig.json files when using `extends`.
            "@khanacademy/*": [
                "../*/src"
            ]
        }
    },
    "references": [
        {"path": "../kas/tsconfig-build.json"},
        {"path": "../kmath/tsconfig-build.json"},
        {"path": "../perseus-core/tsconfig-build.json"},
        {"path": "../perseus-utils/tsconfig-build.json"},
        {"path": "../perseus/tsconfig-build.json"},
    ]
}

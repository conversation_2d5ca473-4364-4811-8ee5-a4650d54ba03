import mysql from 'mysql2/promise';

interface DatabaseConfig {
  host: string;
  port: number;
  user: string;
  password: string;
  database: string;
  ssl?: boolean | { rejectUnauthorized: boolean };
}

const config: DatabaseConfig = {
  host: process.env.HOSTINGER_DB_HOST || process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.HOSTINGER_DB_PORT || process.env.DB_PORT || '3306'),
  user: process.env.HOSTINGER_DB_USER || process.env.DB_USER || '',
  password: process.env.HOSTINGER_DB_PASSWORD || process.env.DB_PASSWORD || '',
  database: process.env.HOSTINGER_DB_NAME || process.env.DB_NAME || '',
  ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
};

let connection: mysql.Connection | null = null;

export async function getConnection(): Promise<mysql.Connection> {
  if (!connection) {
    try {
      connection = await mysql.createConnection(config);
      console.log('✅ Database connected successfully');
    } catch (error) {
      console.error('❌ Database connection failed:', error);
      throw error;
    }
  }
  return connection;
}

export async function executeQuery<T = any>(
  query: string,
  params: any[] = []
): Promise<T[]> {
  try {
    const conn = await getConnection();
    const [rows] = await conn.execute(query, params);
    return rows as T[];
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
}

export async function executeQuerySingle<T = any>(
  query: string,
  params: any[] = []
): Promise<T | null> {
  const results = await executeQuery<T>(query, params);
  return results.length > 0 ? results[0] : null;
}

// Initialize database tables if they don't exist
export async function initializeDatabase() {
  try {
    const conn = await getConnection();
    
    // Create analytics table
    await conn.execute(`
      CREATE TABLE IF NOT EXISTS analytics (
        id INT AUTO_INCREMENT PRIMARY KEY,
        metric_name VARCHAR(100) NOT NULL,
        value DECIMAL(10,2) NOT NULL,
        unit VARCHAR(20),
        icon VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Create system_diagnostics table
    await conn.execute(`
      CREATE TABLE IF NOT EXISTS system_diagnostics (
        id INT AUTO_INCREMENT PRIMARY KEY,
        cpu_load DECIMAL(5,2) NOT NULL,
        ram_usage DECIMAL(5,2) NOT NULL,
        disk_usage DECIMAL(5,2) DEFAULT 0,
        network_status VARCHAR(20) DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create school_data table
    await conn.execute(`
      CREATE TABLE IF NOT EXISTS school_data (
        id INT AUTO_INCREMENT PRIMARY KEY,
        gpa DECIMAL(3,2) NOT NULL,
        credits INT NOT NULL,
        attendance DECIMAL(5,2) NOT NULL,
        assignments_completed INT DEFAULT 0,
        assignments_total INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Create market_data table
    await conn.execute(`
      CREATE TABLE IF NOT EXISTS market_data (
        id INT AUTO_INCREMENT PRIMARY KEY,
        featured_product_name VARCHAR(200) NOT NULL,
        featured_product_price DECIMAL(10,2) NOT NULL,
        featured_product_image VARCHAR(500),
        top_mover_symbol VARCHAR(10) NOT NULL,
        top_mover_change DECIMAL(10,2) NOT NULL,
        top_mover_is_up BOOLEAN NOT NULL,
        volume BIGINT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Create books table
    await conn.execute(`
      CREATE TABLE IF NOT EXISTS books (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(200) NOT NULL,
        author VARCHAR(100) NOT NULL,
        price DECIMAL(8,2) NOT NULL,
        cover_image VARCHAR(500),
        genre VARCHAR(50),
        rating DECIMAL(2,1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create notifications table
    await conn.execute(`
      CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(200) NOT NULL,
        message TEXT NOT NULL,
        type VARCHAR(50) DEFAULT 'info',
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    console.log('✅ Database tables initialized successfully');
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    throw error;
  }
}

// Seed initial data
export async function seedDatabase() {
  try {
    // Check if data already exists
    const analyticsCount = await executeQuerySingle<{count: number}>('SELECT COUNT(*) as count FROM analytics');
    
    if (analyticsCount && analyticsCount.count === 0) {
      // Seed analytics data
      await executeQuery(`
        INSERT INTO analytics (metric_name, value, unit, icon) VALUES
        ('Revenue', 125000.50, 'USD', 'CurrencyDollarIcon'),
        ('Users', 15420, 'count', 'UsersIcon'),
        ('Growth', 23.5, '%', 'TrendingUpIcon'),
        ('Conversion', 4.2, '%', 'ChartBarIcon')
      `);

      // Seed system diagnostics
      await executeQuery(`
        INSERT INTO system_diagnostics (cpu_load, ram_usage, disk_usage) VALUES
        (45.2, 67.8, 23.1)
      `);

      // Seed school data
      await executeQuery(`
        INSERT INTO school_data (gpa, credits, attendance, assignments_completed, assignments_total) VALUES
        (3.85, 120, 94.5, 28, 32)
      `);

      // Seed market data
      await executeQuery(`
        INSERT INTO market_data (featured_product_name, featured_product_price, top_mover_symbol, top_mover_change, top_mover_is_up, volume) VALUES
        ('Quantum Computing Module', 2499.99, 'QTECH', 15.75, TRUE, 1250000)
      `);

      // Seed books
      await executeQuery(`
        INSERT INTO books (title, author, price, genre, rating) VALUES
        ('Neural Networks Fundamentals', 'Dr. Sarah Chen', 89.99, 'Technology', 4.8),
        ('Quantum Physics Made Simple', 'Prof. Michael Torres', 75.50, 'Science', 4.6),
        ('AI Ethics in Practice', 'Dr. Aisha Patel', 65.00, 'Technology', 4.9),
        ('Future of Computing', 'James Rodriguez', 55.99, 'Technology', 4.4),
        ('Data Science Handbook', 'Lisa Wang', 79.99, 'Technology', 4.7),
        ('Cybersecurity Essentials', 'Mark Thompson', 69.99, 'Technology', 4.5)
      `);

      // Seed notifications
      await executeQuery(`
        INSERT INTO notifications (title, message, type) VALUES
        ('System Update Available', 'A new system update is ready for installation. Please schedule maintenance window.', 'info'),
        ('Security Alert', 'Unusual login activity detected from new location. Please verify your account.', 'warning'),
        ('Backup Completed', 'Daily backup process completed successfully. All data is secure.', 'success'),
        ('Performance Alert', 'CPU usage has been above 80% for the last 30 minutes. Consider scaling resources.', 'warning'),
        ('New Feature Released', 'Advanced analytics dashboard is now available in your control panel.', 'info')
      `);

      console.log('✅ Database seeded with initial data');
    }
  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    throw error;
  }
}


import React from 'react';
import { Card } from './Card';
import { UserCircleIcon, BuildingOfficeIcon, PuzzlePieceIcon, CogIcon } from '@heroicons/react/24/outline'; // Using heroicons for variety

const SnowflakeIcon: React.FC<{className?: string}> = ({className}) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 2L12 22M2 12H22M4.2 19.8L19.8 4.2M4.2 4.2L19.8 19.8M7 4.9L7 19.1M17 4.9V19.1" />
    </svg>
);

const ChromeIcon: React.FC<{className?: string}> = ({className}) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 21a9 9 0 1 0 0-18 9 9 0 0 0 0 18Z" />
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 3a9 9 0 1 0 0 18" />
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 12a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z" />
    </svg>
);


export const WellnessRequests: React.FC = () => {
    return (
        <Card title="Wellness Requests">
            <div className="grid grid-cols-2 gap-4 h-full">
                {/* Left Column */}
                <div className="space-y-3 text-xs sm:text-sm flex flex-col justify-center">
                    <div className="flex items-center gap-3 text-gray-300">
                        <UserCircleIcon className="w-5 h-5 text-cyan-400 flex-shrink-0" />
                        <span>Nai teflag</span>
                    </div>
                    <div className="flex items-center gap-3 text-gray-300">
                        <UserCircleIcon className="w-5 h-5 text-cyan-400 flex-shrink-0" />
                        <span>Ra fohtal snaising Peny Chtgeer</span>
                    </div>
                    <div className="flex items-center gap-3 text-gray-300">
                        <UserCircleIcon className="w-5 h-5 text-cyan-400 flex-shrink-0" />
                        <span>Fayrhebozs</span>
                    </div>
                </div>

                {/* Right Column */}
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 text-center">
                    <div className="flex flex-col items-center justify-center bg-gray-700/30 rounded-lg p-1 sm:p-2">
                        <BuildingOfficeIcon className="w-5 h-5 sm:w-6 sm:h-6 text-cyan-400"/>
                        <p className="text-base sm:text-xl font-bold text-white mt-1">1597</p>
                    </div>
                    <div className="flex flex-col items-center justify-center bg-gray-700/30 rounded-lg p-1 sm:p-2">
                        <PuzzlePieceIcon className="w-5 h-5 sm:w-6 sm:h-6 text-cyan-400"/>
                        <p className="text-base sm:text-xl font-bold text-white mt-1">97</p>
                    </div>
                    <div className="flex flex-col items-center justify-center bg-gray-700/30 rounded-lg p-1 sm:p-2">
                        <SnowflakeIcon className="w-5 h-5 sm:w-6 sm:h-6 text-cyan-400"/>
                    </div>
                    <div className="flex flex-col items-center justify-center bg-gray-700/30 rounded-lg p-1 sm:p-2">
                        <ChromeIcon className="w-5 h-5 sm:w-6 sm:h-6 text-cyan-400"/>
                        <p className="text-base sm:text-xl font-bold text-white">84%</p>
                        <p className="text-[10px] sm:text-xs text-gray-400">RAM Releer.</p>
                    </div>
                    <div className="col-span-2 flex flex-col items-center justify-center bg-gray-700/30 rounded-lg p-1 sm:p-2">
                        <CogIcon className="w-5 h-5 sm:w-6 sm:h-6 text-cyan-400"/>
                        <p className="text-base sm:text-xl font-bold text-white">37%</p>
                        <p className="text-[10px] sm:text-xs text-gray-400">Amicstation ease Dvevty</p>
                    </div>
                </div>
            </div>
        </Card>
    );
};

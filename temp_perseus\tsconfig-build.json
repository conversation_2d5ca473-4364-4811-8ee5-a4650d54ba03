// This file is used by the `build:types` command in package.json
/* Visit https://aka.ms/tsconfig to read more about this file */
{
    // Intentionally empty, all files are covered by tsconfig.json files
    // in each package directory.
    "files": [],
    "references": [
        {"path": "./packages/kas/tsconfig-build.json"},
        {"path": "./packages/keypad-context/tsconfig-build.json"},
        {"path": "./packages/kmath/tsconfig-build.json"},
        {"path": "./packages/math-input/tsconfig-build.json"},
        {"path": "./packages/perseus-core/tsconfig-build.json"},
        {"path": "./packages/perseus-editor/tsconfig-build.json"},
        {"path": "./packages/perseus-linter/tsconfig-build.json"},
        {"path": "./packages/perseus-score/tsconfig-build.json"},
        {"path": "./packages/perseus-utils/tsconfig-build.json"},
        {"path": "./packages/perseus/tsconfig-build.json"},
        {"path": "./packages/pure-markdown/tsconfig-build.json"},
        {"path": "./packages/simple-markdown/tsconfig-build.json"},
    ]
}

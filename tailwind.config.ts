import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/views/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        poppins: ['Poppins', 'sans-serif'],
      },
      colors: {
        'panel-bg': 'rgba(25, 40, 60, 0.6)',
        'container-bg': 'rgba(10, 20, 35, 0.7)',
        'brand-cyan': '#00FFFF',
        'brand-pink': '#FF007F',
      }
    },
  },
  plugins: [],
}
export default config
{"private": true, "name": "@theia/example-browser", "version": "1.63.0", "license": "EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0", "theia": {"frontend": {"config": {"applicationName": "<PERSON><PERSON>er Example", "preferences": {"files.enableTrash": false, "security.workspace.trust.enabled": false}, "reloadOnReconnect": true}}, "backend": {"config": {"frontendConnectionTimeout": 3000}}}, "theiaPluginsDir": "../../plugins", "dependencies": {"@theia/ai-anthropic": "1.63.0", "@theia/ai-chat": "1.63.0", "@theia/ai-chat-ui": "1.63.0", "@theia/ai-code-completion": "1.63.0", "@theia/ai-core": "1.63.0", "@theia/ai-editor": "1.63.0", "@theia/ai-google": "1.63.0", "@theia/ai-history": "1.63.0", "@theia/ai-huggingface": "1.63.0", "@theia/ai-ide": "1.63.0", "@theia/ai-llamafile": "1.63.0", "@theia/ai-mcp": "1.63.0", "@theia/ai-ollama": "1.63.0", "@theia/ai-openai": "1.63.0", "@theia/ai-scanoss": "1.63.0", "@theia/ai-terminal": "1.63.0", "@theia/ai-vercel-ai": "1.63.0", "@theia/api-provider-sample": "1.63.0", "@theia/api-samples": "1.63.0", "@theia/bulk-edit": "1.63.0", "@theia/callhierarchy": "1.63.0", "@theia/collaboration": "1.63.0", "@theia/console": "1.63.0", "@theia/core": "1.63.0", "@theia/debug": "1.63.0", "@theia/dev-container": "1.63.0", "@theia/editor": "1.63.0", "@theia/editor-preview": "1.63.0", "@theia/file-search": "1.63.0", "@theia/filesystem": "1.63.0", "@theia/getting-started": "1.63.0", "@theia/keymaps": "1.63.0", "@theia/markers": "1.63.0", "@theia/memory-inspector": "1.63.0", "@theia/messages": "1.63.0", "@theia/metrics": "1.63.0", "@theia/mini-browser": "1.63.0", "@theia/monaco": "1.63.0", "@theia/navigator": "1.63.0", "@theia/notebook": "1.63.0", "@theia/outline-view": "1.63.0", "@theia/output": "1.63.0", "@theia/plugin-dev": "1.63.0", "@theia/plugin-ext": "1.63.0", "@theia/plugin-ext-headless": "1.63.0", "@theia/plugin-ext-vscode": "1.63.0", "@theia/plugin-metrics": "1.63.0", "@theia/preferences": "1.63.0", "@theia/preview": "1.63.0", "@theia/process": "1.63.0", "@theia/property-view": "1.63.0", "@theia/remote": "1.63.0", "@theia/scanoss": "1.63.0", "@theia/scm": "1.63.0", "@theia/scm-extra": "1.63.0", "@theia/search-in-workspace": "1.63.0", "@theia/secondary-window": "1.63.0", "@theia/task": "1.63.0", "@theia/terminal": "1.63.0", "@theia/test": "1.63.0", "@theia/timeline": "1.63.0", "@theia/toolbar": "1.63.0", "@theia/typehierarchy": "1.63.0", "@theia/userstorage": "1.63.0", "@theia/variable-resolver": "1.63.0", "@theia/vsx-registry": "1.63.0", "@theia/workspace": "1.63.0"}, "scripts": {"clean": "theia clean", "build": "theiaext build && npm run -s bundle", "build:production": "theiaext build && npm run -s bundle:production", "bundle": "npm run rebuild && theia build --mode development", "bundle:production": "npm run rebuild && theia build --mode production", "compile": "tsc -b", "coverage": "npm run -s test --test-coverage && npm run -s coverage:report", "coverage:clean": "rimraf .nyc_output && rimraf coverage", "coverage:report": "nyc report --reporter=html", "rebuild": "theia rebuild:browser --cacheRoot ../..", "start": "theia start --plugins=local-dir:../../plugins --ovsx-router-config=../ovsx-router-config.json", "start:debug": "npm run -s start --log-level=debug", "start:watch": "concurrently --kill-others -n tsc,bundle,run -c red,yellow,green \"tsc -b -w --preserveWatchOutput\" \"npm run -s watch:bundle\" \"npm run -s start\"", "test": "theia test . --plugins=local-dir:../../plugins --test-spec=../api-tests/**/*.spec.js", "test:debug": "npm run -s test --test-inspect", "watch": "concurrently --kill-others -n tsc,bundle -c red,yellow \"tsc -b -w --preserveWatchOutput\" \"npm run -s watch:bundle\"", "watch:bundle": "theia build --watch --mode development", "watch:compile": "tsc -b -w"}, "devDependencies": {"@theia/cli": "1.63.0", "@theia/native-webpack-plugin": "1.63.0"}}
/**
 * Overrides of mafs theme-- we will want to move this into JS land
 * to take advantage of WB tokens
 */
.MafsView {
    --mafs-bg: transparent;
    --mafs-fg: rgb(33, 36, 44); /* WB color.offBlack */

    /* Grid lines */
    --mafs-line-color: rgba(33, 36, 44, 0.16); /* WB color.offBlack16*/

    /* Axis lines */
    --mafs-axis-stroke-width: 2px;

    --mafs-blue: #1865f2; /* WB color.blue */
    --mafs-red: #d92916; /* WB color.red */
    --mafs-green: #00a60e; /* WB color.green */
    --mafs-violet: #9059ff; /* WB color.purple */
    --mafs-yellow: #ffb100; /* WB color.gold */
    /* Note: offBlack50 is not the best for contrast. However, this color needs
    to be distinguishable from our regular interactive blue so it is clear that
    the element is not interactive, even though the visual elements (e.g.
    outlines, shadows) may indicate otherwise. When using a darker gray, it
    looks the same as our WB blue in grayscale. We had to make a call here, and
    we decided to make it lighter and more distinguishable even if it sacrifices
    color contrast. (Nisha 2025) */
    --static-gray: #909296 /* WB color.offBlack50 */;

    /* overridden on a per-point basis */
    --movable-point-color: var(--mafs-blue);
    --movable-point-center-radius: 6px;
    --movable-point-ring-radius: calc(2px + var(--movable-point-center-radius));
    --movable-point-halo-radius: calc(3px + var(--movable-point-ring-radius));
    --movable-point-hover-expansion: 2px;
    --movable-point-focus-ring-offset: 2px;

    --movable-line-stroke-weight: 2px;
    --movable-line-stroke-weight-active: 4px;
    overflow: visible !important;
}

.MafsView > svg {
    /* Chrome/Safari bugfix for LEMS-1906 */
    display: block;
    overflow: visible; /* LEMS-2035: This ensures that Mafs doesn't clip the axis labels */
}

.MafsView .movable-line:hover,
.movable-dragging {
    --movable-line-stroke-weight: var(--movable-line-stroke-weight-active);
}

.MafsView .movable-line:focus,
.movable-polygon:focus {
    outline: none;
}

.MafsView
    .movable-line
    :is(.movable-line-focus-outline, .movable-line-focus-outline-gap) {
    stroke: transparent;
}

.MafsView .movable-line:focus-visible .movable-line-focus-outline {
    stroke: var(--mafs-blue);
    stroke-width: 10px;
}

.MafsView .movable-line:focus-visible .movable-line-focus-outline-gap {
    stroke: white;
    stroke-width: 6px;
}

.MafsView .movable-point {
    cursor: grab;
    touch-action: none;
    outline: none;
}

.MafsView .movable-point.movable-point--dragging {
    cursor: grabbing;
}

.MafsView .movable-point.movable-point--dragging .movable-point-halo {
    opacity: 0;
}

.MafsView .movable-point-hitbox {
    fill: transparent;
}

.MafsView
    :is(
        .movable-point-center,
        .movable-point-ring,
        .movable-point-halo,
        .movable-point-focus-outline
    ) {
    transition:
        r 0.15s ease-out,
        opacity 0.15s ease-out;
}

.MafsView .movable-point-center {
    r: var(--movable-point-center-radius);
}

.MafsView .movable-point-halo {
    r: var(--movable-point-halo-radius);
    fill: var(--movable-point-color);
    opacity: 0.25;
    filter: drop-shadow(0 5px 5px #0008);
}

.MafsView .movable-point-ring {
    r: var(--movable-point-ring-radius);
    fill: #fff;
}

.MafsView .movable-point:hover .movable-point-center {
    r: calc(
        var(--movable-point-hover-expansion) +
            var(--movable-point-center-radius)
    );
}

.MafsView .movable-point:hover .movable-point-ring {
    r: calc(
        var(--movable-point-hover-expansion) + var(--movable-point-ring-radius)
    );
}

.MafsView .movable-point:hover .movable-point-halo {
    r: calc(
        var(--movable-point-hover-expansion) + var(--movable-point-halo-radius)
    );
}

.MafsView .movable-point .movable-point-focus-outline {
    visibility: hidden;
    r: calc(
        var(--movable-point-halo-radius) +
            var(--movable-point-focus-ring-offset)
    );
    stroke-width: 2px;
    fill: none;
    stroke: var(--mafs-blue);
}

.MafsView .movable-point:hover .movable-point-focus-outline {
    r: calc(
        var(--movable-point-hover-expansion) + var(--movable-point-halo-radius) +
            var(--movable-point-focus-ring-offset)
    );
}

/**
 * This is needed because empty elements with focus will still have the focus outline
 * when navigating via keyboard, causing weird blue dots to appear in Firefox.
 */
.MafsView .movable-point__focusable-handle:focus-visible {
    outline: none;
}

.MafsView
    .movable-point:is(:focus, .movable-point--focus)
    .movable-point-focus-outline {
    visibility: visible;
}

.MafsView .movable-circle {
    cursor: grab;
}

.MafsView .movable-circle .circle {
    stroke-width: 2px;
    fill: transparent;
}

.MafsView .movable-circle:hover .circle {
    fill: var(--mafs-blue);
    fill-opacity: 0.16;
}

.MafsView .movable-circle.movable-circle--dragging {
    cursor: grabbing;
    outline: none;
}

.MafsView .movable-circle .focus-ring {
    visibility: hidden; /* overridden when the circle is focused */
    stroke: var(--mafs-blue);
    stroke-width: 2px;
    fill: transparent;
}

.MafsView .movable-circle:focus {
    outline: none;
}

.MafsView .movable-circle:focus-visible .focus-ring {
    visibility: visible;
}

.MafsView .movable-circle .movable-circle-handle {
    stroke: #fff;
    stroke-width: 2px;
}

.MafsView .movable-circle .movable-circle-handle-dot {
    fill: #fff;
    r: 1.25px;
}

@font-face {
    font-family: Mafs-MJXTEX;
    src: url("https://cdn.kastatic.org/fonts/mathjax/MathJax_Main-Regular.woff")
        format("woff");
}

.MafsView pattern g {
    stroke: rgba(33, 36, 44, 0.32);
}

.MafsView .angle-arc-interactive {
    stroke: var(--mafs-blue);
    stroke-width: 0.1px;
}

.MafsView .angle-arc-static {
    stroke: var(--static-gray);
    stroke-width: 0.1px;
}

.MafsView .axis-tick-label {
    /* There is a bug in the MathJax font for Safari, so we use KaTeX */
    font-family: "KaTeX_Main", "Mafs-MJXTEX", sans-serif;
    stroke: #fff;
    stroke-width: 5px;
    paint-order: stroke fill;
}

.MafsView .axis-tick {
    stroke: black;
    stroke-width: 1px;
}

.mafs-sr-only {
    height: 0;
    width: 0;
    overflow: hidden;
}

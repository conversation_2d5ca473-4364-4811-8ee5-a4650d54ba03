{"private": true, "name": "@theia/re-exports", "version": "1.63.0", "description": "Theia re-export helper functions and scripts.", "main": "lib/index.js", "engines": {"node": ">= 12"}, "files": ["bin", "lib", "src"], "bin": {"theia-re-exports": "bin/theia-re-exports.js"}, "scripts": {"afterInstall": "npm run build", "build": "theiaext build", "clean": "theiaext clean", "compile": "theiaext compile", "lint": "theiaext lint", "test": "theiaext test", "watch": "theiaext watch"}, "dependencies": {"mustache": "^4.2.0", "semver": "^7.5.4", "tslib": "^2.6.2", "yargs": "^15.3.1"}, "devDependencies": {"@types/chai": "^4.3.0", "@types/mocha": "^10.0.0", "@types/mustache": "^4.1.2", "typescript": "^5.4.5"}}
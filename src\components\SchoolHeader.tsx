import React from 'react';
import { 
  AcademicCapIcon,
  BuildingOfficeIcon,
  UserGroupIcon,
  CurrencyDollarIcon,
  MegaphoneIcon,
  HeartIcon,
  UserIcon,
  CogIcon
} from '@heroicons/react/24/solid';

export type SchoolDepartment = 
  | 'school' 
  | 'administration' 
  | 'teacher' 
  | 'finance' 
  | 'marketing' 
  | 'parent' 
  | 'student' 
  | 'setting';

interface SchoolHeaderProps {
  activeDepartment: SchoolDepartment;
  setActiveDepartment: (department: SchoolDepartment) => void;
}

export const SchoolHeader: React.FC<SchoolHeaderProps> = ({ 
  activeDepartment, 
  setActiveDepartment 
}) => {
  const departments = [
    { 
      id: 'school' as SchoolDepartment, 
      name: 'School', 
      icon: AcademicCapIcon, 
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/20',
      borderColor: 'border-blue-400/30'
    },
    { 
      id: 'administration' as SchoolDepartment, 
      name: 'Administration', 
      icon: BuildingOfficeIcon, 
      color: 'text-purple-400',
      bgColor: 'bg-purple-500/20',
      borderColor: 'border-purple-400/30'
    },
    { 
      id: 'teacher' as SchoolDepartment, 
      name: 'Teacher', 
      icon: UserGroupIcon, 
      color: 'text-green-400',
      bgColor: 'bg-green-500/20',
      borderColor: 'border-green-400/30'
    },
    { 
      id: 'finance' as SchoolDepartment, 
      name: 'Finance', 
      icon: CurrencyDollarIcon, 
      color: 'text-yellow-400',
      bgColor: 'bg-yellow-500/20',
      borderColor: 'border-yellow-400/30'
    },
    { 
      id: 'marketing' as SchoolDepartment, 
      name: 'Marketing', 
      icon: MegaphoneIcon, 
      color: 'text-pink-400',
      bgColor: 'bg-pink-500/20',
      borderColor: 'border-pink-400/30'
    },
    { 
      id: 'parent' as SchoolDepartment, 
      name: 'Parent', 
      icon: HeartIcon, 
      color: 'text-red-400',
      bgColor: 'bg-red-500/20',
      borderColor: 'border-red-400/30'
    },
    { 
      id: 'student' as SchoolDepartment, 
      name: 'Student', 
      icon: UserIcon, 
      color: 'text-cyan-400',
      bgColor: 'bg-cyan-500/20',
      borderColor: 'border-cyan-400/30'
    },
    { 
      id: 'setting' as SchoolDepartment, 
      name: 'Settings', 
      icon: CogIcon, 
      color: 'text-gray-400',
      bgColor: 'bg-gray-500/20',
      borderColor: 'border-gray-400/30'
    }
  ];

  return (
    <div className="bg-panel-bg border border-gray-700/50 rounded-xl p-3 flex items-center justify-between text-sm w-full">
      {/* Left Section - School System Title */}
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
          <AcademicCapIcon className="w-5 h-5 text-white" />
        </div>
        <div>
          <div className="text-xs uppercase text-gray-400 tracking-wider">EDUCATION</div>
          <div className="text-lg font-bold text-white">School Management</div>
        </div>
      </div>

      {/* Center Section - Department Navigation */}
      <div className="flex items-center gap-2 flex-1 justify-center">
        {departments.map((dept) => {
          const IconComponent = dept.icon;
          const isActive = activeDepartment === dept.id;
          
          return (
            <button
              key={dept.id}
              onClick={() => setActiveDepartment(dept.id)}
              className={`
                relative group flex items-center gap-2 px-4 py-2 rounded-lg
                transition-all duration-300 ease-out
                ${isActive 
                  ? `${dept.bgColor} ${dept.borderColor} border shadow-lg scale-105` 
                  : 'bg-gray-800/40 border border-gray-600/30 hover:bg-gray-700/60 hover:scale-102'
                }
                backdrop-blur-sm
              `}
            >
              {/* Glow effect for active */}
              {isActive && (
                <div className={`absolute inset-0 rounded-lg ${dept.bgColor} opacity-30 blur-sm`} />
              )}
              
              {/* Icon */}
              <IconComponent className={`
                w-4 h-4 transition-all duration-200 relative z-10
                ${isActive ? dept.color : 'text-gray-400 group-hover:text-white'}
              `} />
              
              {/* Label */}
              <span className={`
                text-xs font-medium transition-all duration-200 relative z-10
                ${isActive ? 'text-white' : 'text-gray-400 group-hover:text-white'}
              `}>
                {dept.name}
              </span>
              
              {/* Active indicator */}
              {isActive && (
                <div className={`absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-0.5 ${dept.color.replace('text-', 'bg-')} rounded-full`} />
              )}
            </button>
          );
        })}
      </div>

      {/* Right Section - Status */}
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span className="text-xs text-gray-400">ONLINE</span>
        </div>
        <div className="text-xs text-gray-400">
          {new Date().toLocaleDateString()}
        </div>
      </div>
    </div>
  );
};

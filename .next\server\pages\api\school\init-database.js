"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/school/init-database";
exports.ids = ["pages/api/school/init-database"];
exports.modules = {

/***/ "mysql2/promise":
/*!*********************************!*\
  !*** external "mysql2/promise" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("mysql2/promise");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool%2Finit-database&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool%5Cinit-database.ts&middlewareConfigBase64=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool%2Finit-database&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool%5Cinit-database.ts&middlewareConfigBase64=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_school_init_database_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\school\\init-database.ts */ \"(api)/./pages/api/school/init-database.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_school_init_database_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_school_init_database_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/school/init-database\",\n        pathname: \"/api/school/init-database\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_school_init_database_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjNfcmVhY3QtZG9tQDE4LjIuMF9yZWFjdEAxOC4yLjBfX3JlYWN0QDE4LjIuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRnNjaG9vbCUyRmluaXQtZGF0YWJhc2UmcHJlZmVycmVkUmVnaW9uPSZhYnNvbHV0ZVBhZ2VQYXRoPS4lMkZwYWdlcyU1Q2FwaSU1Q3NjaG9vbCU1Q2luaXQtZGF0YWJhc2UudHMmbWlkZGxld2FyZUNvbmZpZ0Jhc2U2ND1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQXNHO0FBQ3ZDO0FBQ0w7QUFDMUQ7QUFDbUU7QUFDbkU7QUFDQSxpRUFBZSx3RUFBSyxDQUFDLCtEQUFRLFlBQVksRUFBQztBQUMxQztBQUNPLGVBQWUsd0VBQUssQ0FBQywrREFBUTtBQUNwQztBQUNPLHdCQUF3QixnSEFBbUI7QUFDbEQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsWUFBWTtBQUNaLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9leWVzLXNoaWVsZC1mdXR1cmlzdGljLXVpLz9lMWU4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBhZ2VzQVBJUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9wYWdlcy1hcGkvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgaG9pc3QgfSBmcm9tIFwibmV4dC9kaXN0L2J1aWxkL3RlbXBsYXRlcy9oZWxwZXJzXCI7XG4vLyBJbXBvcnQgdGhlIHVzZXJsYW5kIGNvZGUuXG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiLi9wYWdlc1xcXFxhcGlcXFxcc2Nob29sXFxcXGluaXQtZGF0YWJhc2UudHNcIjtcbi8vIFJlLWV4cG9ydCB0aGUgaGFuZGxlciAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgXCJkZWZhdWx0XCIpO1xuLy8gUmUtZXhwb3J0IGNvbmZpZy5cbmV4cG9ydCBjb25zdCBjb25maWcgPSBob2lzdCh1c2VybGFuZCwgXCJjb25maWdcIik7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc0FQSVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFU19BUEksXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9zY2hvb2wvaW5pdC1kYXRhYmFzZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL3NjaG9vbC9pbml0LWRhdGFiYXNlXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogXCJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwiXCJcbiAgICB9LFxuICAgIHVzZXJsYW5kXG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGFnZXMtYXBpLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool%2Finit-database&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool%5Cinit-database.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./pages/api/school/init-database.ts":
/*!*******************************************!*\
  !*** ./pages/api/school/init-database.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mysql2/promise */ \"mysql2/promise\");\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mysql2_promise__WEBPACK_IMPORTED_MODULE_0__);\n\n// Database connection configuration\nconst dbConfig = {\n    host: process.env.DB_HOST || \"localhost\",\n    user: process.env.DB_USER || \"root\",\n    password: process.env.DB_PASSWORD || \"\",\n    database: process.env.DB_NAME || \"school_management\",\n    port: parseInt(process.env.DB_PORT || \"3306\")\n};\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        const connection = await mysql2_promise__WEBPACK_IMPORTED_MODULE_0___default().createConnection(dbConfig);\n        try {\n            // Create students table\n            await connection.execute(`\n        CREATE TABLE IF NOT EXISTS students (\n          id INT AUTO_INCREMENT PRIMARY KEY,\n          name VARCHAR(255) NOT NULL,\n          grade VARCHAR(50) NOT NULL,\n          gpa DECIMAL(3,2) DEFAULT 0.00,\n          ai_mentor VARCHAR(100),\n          learning_style VARCHAR(100),\n          emotional_state VARCHAR(50),\n          neural_pathways INT DEFAULT 0,\n          status ENUM('active', 'inactive', 'graduated') DEFAULT 'active',\n          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n        )\n      `);\n            // Create teachers table\n            await connection.execute(`\n        CREATE TABLE IF NOT EXISTS teachers (\n          id INT AUTO_INCREMENT PRIMARY KEY,\n          name VARCHAR(255) NOT NULL,\n          role VARCHAR(255) NOT NULL,\n          department_id INT,\n          ai_partner VARCHAR(100),\n          efficiency_score DECIMAL(4,1) DEFAULT 0.0,\n          status ENUM('active', 'inactive', 'on_leave') DEFAULT 'active',\n          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n        )\n      `);\n            // Create departments table\n            await connection.execute(`\n        CREATE TABLE IF NOT EXISTS departments (\n          id INT AUTO_INCREMENT PRIMARY KEY,\n          name VARCHAR(255) NOT NULL,\n          color VARCHAR(100) NOT NULL,\n          innovation VARCHAR(255),\n          status ENUM('active', 'inactive') DEFAULT 'active',\n          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n        )\n      `);\n            // Create classes table\n            await connection.execute(`\n        CREATE TABLE IF NOT EXISTS classes (\n          id INT AUTO_INCREMENT PRIMARY KEY,\n          name VARCHAR(255) NOT NULL,\n          department_id INT,\n          teacher_id INT,\n          ai_tutor VARCHAR(100),\n          max_students INT DEFAULT 30,\n          status ENUM('active', 'inactive', 'completed') DEFAULT 'active',\n          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n          FOREIGN KEY (department_id) REFERENCES departments(id),\n          FOREIGN KEY (teacher_id) REFERENCES teachers(id)\n        )\n      `);\n            // Create innovations table\n            await connection.execute(`\n        CREATE TABLE IF NOT EXISTS innovations (\n          id INT AUTO_INCREMENT PRIMARY KEY,\n          title VARCHAR(255) NOT NULL,\n          icon VARCHAR(10) NOT NULL,\n          status ENUM('Active', 'Live', 'Beta', 'Testing', 'Inactive') DEFAULT 'Testing',\n          impact VARCHAR(255) NOT NULL,\n          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n        )\n      `);\n            // Create events table\n            await connection.execute(`\n        CREATE TABLE IF NOT EXISTS events (\n          id INT AUTO_INCREMENT PRIMARY KEY,\n          title VARCHAR(255) NOT NULL,\n          date DATE NOT NULL,\n          type VARCHAR(100) NOT NULL,\n          participants INT DEFAULT 0,\n          status ENUM('active', 'cancelled', 'completed') DEFAULT 'active',\n          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n        )\n      `);\n            // Create ai_tutor_sessions table\n            await connection.execute(`\n        CREATE TABLE IF NOT EXISTS ai_tutor_sessions (\n          id INT AUTO_INCREMENT PRIMARY KEY,\n          student_id INT,\n          ai_tutor VARCHAR(100) NOT NULL,\n          subject VARCHAR(100) NOT NULL,\n          duration_minutes INT DEFAULT 0,\n          effectiveness_score DECIMAL(3,1) DEFAULT 0.0,\n          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n          FOREIGN KEY (student_id) REFERENCES students(id)\n        )\n      `);\n            // Create junction tables\n            await connection.execute(`\n        CREATE TABLE IF NOT EXISTS student_departments (\n          id INT AUTO_INCREMENT PRIMARY KEY,\n          student_id INT,\n          department_id INT,\n          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n          FOREIGN KEY (student_id) REFERENCES students(id),\n          FOREIGN KEY (department_id) REFERENCES departments(id),\n          UNIQUE KEY unique_student_department (student_id, department_id)\n        )\n      `);\n            await connection.execute(`\n        CREATE TABLE IF NOT EXISTS teacher_departments (\n          id INT AUTO_INCREMENT PRIMARY KEY,\n          teacher_id INT,\n          department_id INT,\n          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n          FOREIGN KEY (teacher_id) REFERENCES teachers(id),\n          FOREIGN KEY (department_id) REFERENCES departments(id),\n          UNIQUE KEY unique_teacher_department (teacher_id, department_id)\n        )\n      `);\n            res.status(200).json({\n                message: \"Database tables created successfully\",\n                tables: [\n                    \"students\",\n                    \"teachers\",\n                    \"departments\",\n                    \"classes\",\n                    \"innovations\",\n                    \"events\",\n                    \"ai_tutor_sessions\",\n                    \"student_departments\",\n                    \"teacher_departments\"\n                ]\n            });\n        } finally{\n            await connection.end();\n        }\n    } catch (error) {\n        console.error(\"Database initialization error:\", error);\n        res.status(500).json({\n            message: \"Failed to initialize database\",\n            error:  true ? error.message : 0\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/school/init-database.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0"], () => (__webpack_exec__("(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool%2Finit-database&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool%5Cinit-database.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();
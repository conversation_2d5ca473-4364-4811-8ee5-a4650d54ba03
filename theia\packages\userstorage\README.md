<div align='center'>

<br />

<img src='https://raw.githubusercontent.com/eclipse-theia/theia/master/logo/theia.svg?sanitize=true' alt='theia-ext-logo' width='100px' />

<h2>ECLIPSE THEIA - USERSTORAGE EXTENSION</h2>

<hr />

</div>

## Description

The `@theia/userstorage` extension provides an API for accessing user storage files, i.e preferences, custom keymaps and other user-specific files without having to know how it's implemented. User storage files can then be accessed with `userstorage` uri scheme like so `userstorage://settings.json` and the user storage service implementation will then fetch the appropriate file.

## Additional Information

- [API documentation for `@theia/userstorage`](https://eclipse-theia.github.io/theia/docs/next/modules/userstorage.html)
- [Theia - GitHub](https://github.com/eclipse-theia/theia)
- [Theia - Website](https://theia-ide.org/)

## License

- [Eclipse Public License 2.0](http://www.eclipse.org/legal/epl-2.0/)
- [一 (Secondary) GNU General Public License, version 2 with the GNU Classpath Exception](https://projects.eclipse.org/license/secondary-gpl-2.0-cp)

## Trademark

"Theia" is a trademark of the Eclipse Foundation
<https://www.eclipse.org/theia>

# Theia - User Storage Extension

## License

- [Eclipse Public License 2.0](http://www.eclipse.org/legal/epl-2.0/)
- [一 (Secondary) GNU General Public License, version 2 with the GNU Classpath Exception](https://projects.eclipse.org/license/secondary-gpl-2.0-cp)

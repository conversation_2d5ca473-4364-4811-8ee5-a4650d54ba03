{"name": "@theia/application-package", "version": "1.63.0", "description": "Theia application package API.", "publishConfig": {"access": "public"}, "license": "EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0", "repository": {"type": "git", "url": "https://github.com/eclipse-theia/theia.git"}, "bugs": {"url": "https://github.com/eclipse-theia/theia/issues"}, "homepage": "https://github.com/eclipse-theia/theia", "files": ["lib", "src"], "main": "lib/index.js", "typings": "lib/index.d.ts", "scripts": {"build": "theiaext build", "clean": "theiaext clean", "compile": "theiaext compile", "lint": "theiaext lint", "test": "theiaext test", "watch": "theiaext watch"}, "dependencies": {"@theia/request": "1.63.0", "@types/fs-extra": "^4.0.2", "@types/semver": "^7.5.0", "@types/write-json-file": "^2.2.1", "deepmerge": "^4.2.2", "fs-extra": "^4.0.2", "is-electron": "^2.1.0", "nano": "^10.1.3", "resolve-package-path": "^4.0.3", "semver": "^7.5.4", "tslib": "^2.6.2", "write-json-file": "^2.2.0"}, "devDependencies": {"@theia/ext-scripts": "1.63.0"}, "nyc": {"extends": "../../configs/nyc.json"}, "gitHead": "21358137e41342742707f660b8e222f940a27652"}
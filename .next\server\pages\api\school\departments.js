"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/school/departments";
exports.ids = ["pages/api/school/departments"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool%2Fdepartments&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool%5Cdepartments.ts&middlewareConfigBase64=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool%2Fdepartments&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool%5Cdepartments.ts&middlewareConfigBase64=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_school_departments_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\school\\departments.ts */ \"(api)/./pages/api/school/departments.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_school_departments_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_school_departments_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/school/departments\",\n        pathname: \"/api/school/departments\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_school_departments_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool%2Fdepartments&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool%5Cdepartments.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./pages/api/school/departments.ts":
/*!*****************************************!*\
  !*** ./pages/api/school/departments.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n// In-memory data store for School Departments\nconst departmentsData = {\n    departments: [\n        {\n            id: 1,\n            name: \"AI & Robotics\",\n            description: \"Artificial Intelligence, Machine Learning, and Advanced Robotics\",\n            head: \"Dr. Sarah Chen\",\n            color: \"bg-blue-500/20 text-blue-400\",\n            icon: \"\\uD83E\\uDD16\",\n            students: 412,\n            teachers: 18,\n            courses: 24,\n            labs: 8,\n            innovation: \"Neural Networks Lab\",\n            budget: 2400000,\n            achievements: [\n                \"First high school to develop AGI prototype\",\n                \"Winner of Global AI Ethics Competition 2024\",\n                \"Published 47 research papers in top AI journals\"\n            ],\n            facilities: [\n                \"Quantum Neural Network Lab\",\n                \"Humanoid Robot Workshop\",\n                \"AI Ethics Research Center\",\n                \"Machine Learning Supercomputer Cluster\"\n            ],\n            courses_offered: [\n                {\n                    name: \"Introduction to AI\",\n                    level: \"Beginner\",\n                    students: 89,\n                    ai_tutor: \"Turing AI\"\n                },\n                {\n                    name: \"Neural Network Architecture\",\n                    level: \"Advanced\",\n                    students: 67,\n                    ai_tutor: \"LeCun AI\"\n                },\n                {\n                    name: \"Robotics Engineering\",\n                    level: \"Intermediate\",\n                    students: 78,\n                    ai_tutor: \"Asimov AI\"\n                },\n                {\n                    name: \"AI Ethics & Philosophy\",\n                    level: \"Advanced\",\n                    students: 45,\n                    ai_tutor: \"Russell AI\"\n                }\n            ],\n            recent_projects: [\n                {\n                    name: \"Conscious AI Development\",\n                    status: \"Active\",\n                    completion: 67\n                },\n                {\n                    name: \"Emotional Intelligence Robots\",\n                    status: \"Testing\",\n                    completion: 89\n                },\n                {\n                    name: \"AI Teacher Assistant Program\",\n                    status: \"Deployed\",\n                    completion: 100\n                }\n            ]\n        },\n        {\n            id: 2,\n            name: \"Quantum Computing\",\n            description: \"Quantum Physics, Quantum Algorithms, and Quantum Information Science\",\n            head: \"Prof. Lisa Wang\",\n            color: \"bg-green-500/20 text-green-400\",\n            icon: \"⚛️\",\n            students: 298,\n            teachers: 16,\n            courses: 18,\n            labs: 4,\n            innovation: \"Quantum Simulators\",\n            budget: 3200000,\n            achievements: [\n                \"Built first educational quantum computer\",\n                \"Solved NP-complete problems in polynomial time\",\n                \"Quantum teleportation successful over 1000km\"\n            ],\n            facilities: [\n                \"2048-qubit Quantum Computer\",\n                \"Quantum Entanglement Laboratory\",\n                \"Cryogenic Cooling Systems\",\n                \"Quantum Algorithm Development Center\"\n            ],\n            courses_offered: [\n                {\n                    name: \"Quantum Mechanics Fundamentals\",\n                    level: \"Beginner\",\n                    students: 76,\n                    ai_tutor: \"Schr\\xf6dinger AI\"\n                },\n                {\n                    name: \"Quantum Algorithm Design\",\n                    level: \"Advanced\",\n                    students: 54,\n                    ai_tutor: \"Shor AI\"\n                },\n                {\n                    name: \"Quantum Cryptography\",\n                    level: \"Expert\",\n                    students: 32,\n                    ai_tutor: \"Bennett AI\"\n                },\n                {\n                    name: \"Quantum Error Correction\",\n                    level: \"Expert\",\n                    students: 28,\n                    ai_tutor: \"Preskill AI\"\n                }\n            ],\n            recent_projects: [\n                {\n                    name: \"Quantum Internet Protocol\",\n                    status: \"Research\",\n                    completion: 34\n                },\n                {\n                    name: \"Room Temperature Quantum Computing\",\n                    status: \"Active\",\n                    completion: 78\n                },\n                {\n                    name: \"Quantum Machine Learning\",\n                    status: \"Testing\",\n                    completion: 92\n                }\n            ]\n        },\n        {\n            id: 3,\n            name: \"Bioengineering\",\n            description: \"Genetic Engineering, Synthetic Biology, and Biotechnology\",\n            head: \"Dr. Elena Rodriguez\",\n            color: \"bg-purple-500/20 text-purple-400\",\n            icon: \"\\uD83E\\uDDEC\",\n            students: 267,\n            teachers: 12,\n            courses: 16,\n            labs: 6,\n            innovation: \"Gene Editing Lab\",\n            budget: 1800000,\n            achievements: [\n                \"Developed CRISPR 3.0 gene editing system\",\n                \"Created synthetic organisms for carbon capture\",\n                \"Reversed aging in laboratory organisms\"\n            ],\n            facilities: [\n                \"CRISPR Gene Editing Suite\",\n                \"Synthetic Biology Laboratory\",\n                \"Bioreactor Manufacturing Center\",\n                \"DNA Sequencing Facility\"\n            ],\n            courses_offered: [\n                {\n                    name: \"Genetic Engineering Basics\",\n                    level: \"Beginner\",\n                    students: 67,\n                    ai_tutor: \"Watson AI\"\n                },\n                {\n                    name: \"Synthetic Biology Design\",\n                    level: \"Advanced\",\n                    students: 45,\n                    ai_tutor: \"Venter AI\"\n                },\n                {\n                    name: \"Bioethics & Regulation\",\n                    level: \"Intermediate\",\n                    students: 56,\n                    ai_tutor: \"Bioethics AI\"\n                },\n                {\n                    name: \"Longevity Research\",\n                    level: \"Expert\",\n                    students: 23,\n                    ai_tutor: \"Sinclair AI\"\n                }\n            ],\n            recent_projects: [\n                {\n                    name: \"Immortality Gene Therapy\",\n                    status: \"Research\",\n                    completion: 23\n                },\n                {\n                    name: \"Synthetic Blood Production\",\n                    status: \"Active\",\n                    completion: 67\n                },\n                {\n                    name: \"Organ 3D Printing\",\n                    status: \"Testing\",\n                    completion: 84\n                }\n            ]\n        },\n        {\n            id: 4,\n            name: \"Space Sciences\",\n            description: \"Space Exploration, Colonization, and Aerospace Technology\",\n            head: \"Dr. James Wilson\",\n            color: \"bg-pink-500/20 text-pink-400\",\n            icon: \"\\uD83D\\uDE80\",\n            students: 189,\n            teachers: 11,\n            courses: 14,\n            labs: 3,\n            innovation: \"Mars Simulation\",\n            budget: 2800000,\n            achievements: [\n                \"Designed Mars colony architecture\",\n                \"Developed fusion propulsion system\",\n                \"Established lunar mining protocols\"\n            ],\n            facilities: [\n                \"Mars Colony Simulation Chamber\",\n                \"Zero Gravity Training Center\",\n                \"Rocket Propulsion Laboratory\",\n                \"Space Suit Testing Facility\"\n            ],\n            courses_offered: [\n                {\n                    name: \"Space Physics\",\n                    level: \"Intermediate\",\n                    students: 45,\n                    ai_tutor: \"Hawking AI\"\n                },\n                {\n                    name: \"Mars Colonization Planning\",\n                    level: \"Advanced\",\n                    students: 34,\n                    ai_tutor: \"Musk AI\"\n                },\n                {\n                    name: \"Rocket Engineering\",\n                    level: \"Expert\",\n                    students: 28,\n                    ai_tutor: \"Von Braun AI\"\n                },\n                {\n                    name: \"Astrobiology\",\n                    level: \"Advanced\",\n                    students: 31,\n                    ai_tutor: \"Sagan AI\"\n                }\n            ],\n            recent_projects: [\n                {\n                    name: \"Interstellar Travel Engine\",\n                    status: \"Research\",\n                    completion: 12\n                },\n                {\n                    name: \"Mars Terraforming Plan\",\n                    status: \"Active\",\n                    completion: 56\n                },\n                {\n                    name: \"Space Elevator Design\",\n                    status: \"Planning\",\n                    completion: 34\n                }\n            ]\n        },\n        {\n            id: 5,\n            name: \"Metaverse Studies\",\n            description: \"Virtual Reality, Augmented Reality, and Digital Worlds\",\n            head: \"Prof. Alex Kim\",\n            color: \"bg-cyan-500/20 text-cyan-400\",\n            icon: \"\\uD83C\\uDF10\",\n            students: 345,\n            teachers: 14,\n            courses: 20,\n            labs: 7,\n            innovation: \"Virtual Reality Campus\",\n            budget: 1600000,\n            achievements: [\n                \"Created first fully immersive virtual school\",\n                \"Developed haptic feedback learning system\",\n                \"Built global metaverse education network\"\n            ],\n            facilities: [\n                \"360\\xb0 VR Learning Pods\",\n                \"Haptic Feedback Laboratory\",\n                \"Metaverse Development Studio\",\n                \"Neural Interface Testing Center\"\n            ],\n            courses_offered: [\n                {\n                    name: \"VR World Design\",\n                    level: \"Beginner\",\n                    students: 89,\n                    ai_tutor: \"Carmack AI\"\n                },\n                {\n                    name: \"Metaverse Economics\",\n                    level: \"Intermediate\",\n                    students: 67,\n                    ai_tutor: \"Zuckerberg AI\"\n                },\n                {\n                    name: \"Neural Interface Programming\",\n                    level: \"Expert\",\n                    students: 34,\n                    ai_tutor: \"Neuralink AI\"\n                },\n                {\n                    name: \"Digital Identity & Ethics\",\n                    level: \"Advanced\",\n                    students: 45,\n                    ai_tutor: \"Stephenson AI\"\n                }\n            ],\n            recent_projects: [\n                {\n                    name: \"Consciousness Upload System\",\n                    status: \"Research\",\n                    completion: 8\n                },\n                {\n                    name: \"Global Virtual Campus\",\n                    status: \"Active\",\n                    completion: 78\n                },\n                {\n                    name: \"Metaverse Learning Analytics\",\n                    status: \"Deployed\",\n                    completion: 100\n                }\n            ]\n        },\n        {\n            id: 6,\n            name: \"Environmental Technology\",\n            description: \"Climate Science, Renewable Energy, and Sustainability\",\n            head: \"Dr. Ahmed Hassan\",\n            color: \"bg-emerald-500/20 text-emerald-400\",\n            icon: \"\\uD83C\\uDF31\",\n            students: 223,\n            teachers: 9,\n            courses: 12,\n            labs: 5,\n            innovation: \"Carbon Neutral Systems\",\n            budget: 1400000,\n            achievements: [\n                \"Achieved campus carbon neutrality\",\n                \"Developed atmospheric carbon capture system\",\n                \"Created renewable energy microgrid\"\n            ],\n            facilities: [\n                \"Solar Panel Manufacturing Lab\",\n                \"Wind Turbine Testing Center\",\n                \"Carbon Capture Research Facility\",\n                \"Sustainable Materials Laboratory\"\n            ],\n            courses_offered: [\n                {\n                    name: \"Climate Science\",\n                    level: \"Beginner\",\n                    students: 56,\n                    ai_tutor: \"Hansen AI\"\n                },\n                {\n                    name: \"Renewable Energy Systems\",\n                    level: \"Intermediate\",\n                    students: 67,\n                    ai_tutor: \"Tesla AI\"\n                },\n                {\n                    name: \"Carbon Capture Technology\",\n                    level: \"Advanced\",\n                    students: 34,\n                    ai_tutor: \"Gates AI\"\n                },\n                {\n                    name: \"Sustainable Engineering\",\n                    level: \"Advanced\",\n                    students: 45,\n                    ai_tutor: \"Lovins AI\"\n                }\n            ],\n            recent_projects: [\n                {\n                    name: \"Atmospheric CO2 Reversal\",\n                    status: \"Active\",\n                    completion: 67\n                },\n                {\n                    name: \"Ocean Plastic Cleanup System\",\n                    status: \"Testing\",\n                    completion: 89\n                },\n                {\n                    name: \"Fusion Power Plant Design\",\n                    status: \"Research\",\n                    completion: 45\n                }\n            ]\n        }\n    ],\n    summary: {\n        totalDepartments: 6,\n        totalStudents: 1734,\n        totalTeachers: 80,\n        totalCourses: 104,\n        totalLabs: 33,\n        totalBudget: 13200000,\n        averageStudentsPerDepartment: 289,\n        averageTeachersPerDepartment: 13\n    }\n};\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        // Simulate some processing time\n        await new Promise((resolve)=>setTimeout(resolve, 120));\n        res.status(200).json(departmentsData);\n    } catch (error) {\n        console.error(\"API error:\", error);\n        res.status(500).json({\n            message: \"Internal server error\",\n            error:  true ? error.message : 0\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/school/departments.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0"], () => (__webpack_exec__("(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool%2Fdepartments&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool%5Cdepartments.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();
import React from 'react';
import { Card } from '../../../components/Card';
import { MoreHorizIcon } from '../../../components/icons';

const SquareIcon = ({ color }: { color: string }) => (
    <div className={`w-3 h-3 sm:w-4 sm:h-4 rounded-sm`} style={{ backgroundColor: color }}></div>
);

type SafetyItem = {
    label: string;
    color: string;
};

interface SafetyTentsProps {
    items: SafetyItem[];
    chartData: number[];
}

export const SafetyTents: React.FC<SafetyTentsProps> = ({ items, chartData = [] }) => {

    if (!items || items.length === 0) {
        return <Card title="Safety Status" headerIcon={<MoreHorizIcon className="w-5 h-5"/>}><p className="text-gray-400 text-sm">No status items available.</p></Card>;
    }

    return (
        <Card title="Safety Status" headerIcon={<MoreHorizIcon className="w-5 h-5"/>}>
            <div className="flex gap-4">
                <div className="flex-grow">
                    <div className="bg-gray-700/50 p-3 rounded-lg flex items-center gap-3">
                        <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-cyan-400/20 flex items-center justify-center animate-pulse">
                            <div className="w-5 h-5 sm:w-6 sm:h-6 rounded-full bg-cyan-400/50 flex items-center justify-center">
                                <div className="w-2 h-2 sm:w-3 sm:h-3 rounded-full bg-cyan-400"></div>
                            </div>
                        </div>
                        <div>
                            <p className="text-white font-semibold text-sm sm:text-base">Wellness Shield</p>
                            <p className="text-xs text-gray-400">Threat monitoring active</p>
                        </div>
                    </div>
                    <ul className="space-y-1 mt-3 text-xs sm:text-sm">
                        {items.map((item, index) => (
                            <li key={index}>
                                <button type="button" className="w-full flex items-center gap-3 p-1 rounded-md hover:bg-gray-700/30 focus:outline-none focus-visible:ring-1 focus-visible:ring-brand-cyan text-left transition-colors">
                                    <SquareIcon color={item.color} />
                                    <span className="text-gray-300">{item.label}</span>
                                </button>
                            </li>
                        ))}
                    </ul>
                </div>
                <div className="w-1/4 flex flex-col justify-end gap-1">
                    { chartData.map((h, i) => (
                        <div key={i} className="bg-cyan-500/50 rounded-sm" style={{height: `${h/4}%`}}></div>
                    ))}
                </div>
            </div>
        </Card>
    );
};

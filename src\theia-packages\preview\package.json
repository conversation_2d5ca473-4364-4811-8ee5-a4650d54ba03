{"name": "@theia/preview", "version": "1.63.0", "description": "Theia - Preview Extension", "dependencies": {"@theia/core": "1.63.0", "@theia/editor": "1.63.0", "@theia/mini-browser": "1.63.0", "@theia/monaco": "1.63.0", "@types/highlight.js": "^10.1.0", "highlight.js": "10.4.1", "markdown-it-anchor": "~5.0.0", "tslib": "^2.6.2"}, "publishConfig": {"access": "public"}, "theiaExtensions": [{"frontend": "lib/browser/preview-frontend-module"}], "keywords": ["theia-extension"], "license": "EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0", "repository": {"type": "git", "url": "https://github.com/eclipse-theia/theia.git"}, "bugs": {"url": "https://github.com/eclipse-theia/theia/issues"}, "homepage": "https://github.com/eclipse-theia/theia", "files": ["lib", "src"], "scripts": {"build": "theiaext build", "clean": "theiaext clean", "compile": "theiaext compile", "lint": "theiaext lint", "test": "theiaext test", "watch": "theiaext watch"}, "devDependencies": {"@theia/ext-scripts": "1.63.0", "@types/markdown-it-anchor": "^4.0.1"}, "nyc": {"extends": "../../configs/nyc.json"}, "gitHead": "21358137e41342742707f660b8e222f940a27652"}
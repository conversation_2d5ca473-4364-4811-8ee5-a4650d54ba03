import React, { useEffect, useRef, useState } from 'react';

interface ResponsiveWrapperProps {
  children: React.ReactNode;
  className?: string;
  targetWidth?: number;
  targetHeight?: number;
  minScale?: number;
  maxScale?: number;
  enableAutoScale?: boolean;
}

export const ResponsiveWrapper: React.FC<ResponsiveWrapperProps> = ({
  children,
  className = '',
  targetWidth = 1920,
  targetHeight = 1080,
  minScale = 0.5,
  maxScale = 1.0,
  enableAutoScale = true
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [scale, setScale] = useState(1);

  useEffect(() => {
    if (!enableAutoScale) return;

    const calculateScale = () => {
      if (!containerRef.current) return;

      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // Calculate scale factors for both dimensions
      const scaleX = viewportWidth / targetWidth;
      const scaleY = viewportHeight / targetHeight;

      // Use the smaller scale to ensure content fits in both dimensions
      const calculatedScale = Math.min(scaleX, scaleY);

      // Clamp the scale within the specified bounds
      const finalScale = Math.max(minScale, Math.min(maxScale, calculatedScale));

      setScale(finalScale);

      // Apply the scale to the container to fill viewport completely
      if (containerRef.current) {
        containerRef.current.style.transform = `scale(${finalScale})`;
        containerRef.current.style.transformOrigin = 'top left';
        containerRef.current.style.width = `${viewportWidth / finalScale}px`;
        containerRef.current.style.height = `${viewportHeight / finalScale}px`;
        containerRef.current.style.left = '0';
        containerRef.current.style.top = '0';
        containerRef.current.style.position = 'absolute';
      }
    };

    // Calculate initial scale
    calculateScale();

    // Recalculate on window resize
    const handleResize = () => {
      calculateScale();
    };

    window.addEventListener('resize', handleResize);
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [targetWidth, targetHeight, minScale, maxScale, enableAutoScale]);

  const containerStyle: React.CSSProperties = {
    position: enableAutoScale ? 'absolute' : 'relative',
    width: '100vw',
    height: '100vh',
    transformOrigin: 'top left',
    transition: 'transform 0.3s ease-out',
    overflow: 'hidden',
  };

  return (
    <div
      ref={containerRef}
      className={`responsive-container ${className}`}
      style={containerStyle}
    >
      {children}
    </div>
  );
};

// Hook for responsive values
export const useResponsiveValue = (baseValue: number, unit: string = 'px') => {
  const [responsiveValue, setResponsiveValue] = useState(`${baseValue}${unit}`);

  useEffect(() => {
    const updateValue = () => {
      const root = document.documentElement;
      const scaleFactor = parseFloat(getComputedStyle(root).getPropertyValue('--scale-factor')) || 1;
      const scaledValue = baseValue * scaleFactor;
      setResponsiveValue(`${scaledValue}${unit}`);
    };

    updateValue();
    window.addEventListener('resize', updateValue);
    
    return () => {
      window.removeEventListener('resize', updateValue);
    };
  }, [baseValue, unit]);

  return responsiveValue;
};

// Hook for responsive CSS variables
export const useResponsiveCSS = () => {
  const [cssVars, setCssVars] = useState({
    fontSize: 'var(--base-font-size)',
    spacing: 'var(--base-spacing)',
    borderRadius: 'var(--base-border-radius)',
    iconSize: 'var(--base-icon-size)',
    buttonHeight: 'var(--base-button-height)',
    cardPadding: 'var(--base-card-padding)',
    gap: 'var(--base-gap)',
  });

  return cssVars;
};

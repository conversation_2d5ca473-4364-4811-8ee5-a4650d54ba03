/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import './abap/abap.contribution';
import './apex/apex.contribution';
import './azcli/azcli.contribution';
import './bat/bat.contribution';
import './bicep/bicep.contribution';
import './cameligo/cameligo.contribution';
import './clojure/clojure.contribution';
import './coffee/coffee.contribution';
import './cpp/cpp.contribution';
import './csharp/csharp.contribution';
import './csp/csp.contribution';
import './css/css.contribution';
import './cypher/cypher.contribution';
import './dart/dart.contribution';
import './dockerfile/dockerfile.contribution';
import './ecl/ecl.contribution';
import './elixir/elixir.contribution';
import './flow9/flow9.contribution';
import './fsharp/fsharp.contribution';
import './freemarker2/freemarker2.contribution';
import './go/go.contribution';
import './graphql/graphql.contribution';
import './handlebars/handlebars.contribution';
import './hcl/hcl.contribution';
import './html/html.contribution';
import './ini/ini.contribution';
import './java/java.contribution';
import './javascript/javascript.contribution';
import './julia/julia.contribution';
import './kotlin/kotlin.contribution';
import './less/less.contribution';
import './lexon/lexon.contribution';
import './lua/lua.contribution';
import './liquid/liquid.contribution';
import './m3/m3.contribution';
import './markdown/markdown.contribution';
import './mdx/mdx.contribution';
import './mips/mips.contribution';
import './msdax/msdax.contribution';
import './mysql/mysql.contribution';
import './objective-c/objective-c.contribution';
import './pascal/pascal.contribution';
import './pascaligo/pascaligo.contribution';
import './perl/perl.contribution';
import './pgsql/pgsql.contribution';
import './php/php.contribution';
import './pla/pla.contribution';
import './postiats/postiats.contribution';
import './powerquery/powerquery.contribution';
import './powershell/powershell.contribution';
import './protobuf/protobuf.contribution';
import './pug/pug.contribution';
import './python/python.contribution';
import './qsharp/qsharp.contribution';
import './r/r.contribution';
import './razor/razor.contribution';
import './redis/redis.contribution';
import './redshift/redshift.contribution';
import './restructuredtext/restructuredtext.contribution';
import './ruby/ruby.contribution';
import './rust/rust.contribution';
import './sb/sb.contribution';
import './scala/scala.contribution';
import './scheme/scheme.contribution';
import './scss/scss.contribution';
import './shell/shell.contribution';
import './solidity/solidity.contribution';
import './sophia/sophia.contribution';
import './sparql/sparql.contribution';
import './sql/sql.contribution';
import './st/st.contribution';
import './swift/swift.contribution';
import './systemverilog/systemverilog.contribution';
import './tcl/tcl.contribution';
import './twig/twig.contribution';
import './typescript/typescript.contribution';
import './typespec/typespec.contribution';
import './vb/vb.contribution';
import './wgsl/wgsl.contribution';
import './xml/xml.contribution';
import './yaml/yaml.contribution';

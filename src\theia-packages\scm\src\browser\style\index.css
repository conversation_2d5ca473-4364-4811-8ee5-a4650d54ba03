/********************************************************************************
 * Copyright (C) 2019 Red Hat, Inc. and others.
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License v. 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * This Source Code may also be made available under the following Secondary
 * Licenses when the conditions for such availability set forth in the Eclipse
 * Public License v. 2.0 are satisfied: GNU General Public License, version 2
 * with the GNU Classpath Exception which is available at
 * https://www.gnu.org/software/classpath/license.html.
 *
 * SPDX-License-Identifier: EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0
 ********************************************************************************/

.theia-scm-commit {
  overflow: hidden;
  font-size: var(--theia-ui-font-size1);
  max-height: calc(100% - var(--theia-border-width));
  position: relative;
  padding: var(--theia-ui-padding)
           max(var(--theia-ui-padding), var(--theia-scrollbar-width))
           var(--theia-ui-padding)
           calc(var(--theia-ui-padding) * 3);
}

.theia-scm {
  box-sizing: border-box;
  height: 100%;
}

.groups-outer-container:focus {
  outline: 0;
  box-shadow: none;
  border: none;
}

.theia-scm .noWrapInfo {
  width: 100%;
  align-items: center;
}

.theia-scm:focus,
.theia-scm :focus {
  outline: 0;
  box-shadow: none;
  border: none;
}

.theia-scm .space-between {
  justify-content: space-between;
}

.theia-scm .changesHeader {
  font-weight: bold;
}

.theia-scm .theia-scm-amend {
  margin: 5px 0;
}

.theia-scm #messageInputContainer {
  position: relative;
}

.theia-scm #repositoryListContainer {
  display: flex;
  margin-bottom: 5px;
  flex: 1;
}

.theia-scm .groups-outer-container {
  overflow-y: auto;
  width: 100%;
  flex-grow: 1;
}

.theia-scm .warn {
  background-color: var(--theia-inputValidation-warningBackground) !important;
}

.theia-scm-main-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.theia-scm-input-message-container {
  display: flex;
  flex-direction: column;
  max-height: 400px;
}

.theia-scm-input-message-container textarea {
  line-height: var(--theia-content-line-height);
  resize: none;
  box-sizing: border-box;
  border: none;
}

.theia-scm-input-message-container textarea:placeholder-shown {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.theia-scm-input-message-container textarea:focus {
  outline: var(--theia-border-width) solid var(--theia-focusBorder);
}

.theia-scm-input-message {
  width: 100%;
}

.theia-scm-input-message-idle:not(:focus) {
  border-color: var(--theia-input-border);
}

.theia-scm-input-message-info {
  border-color: var(--theia-inputValidation-infoBorder) !important;
}

.theia-scm-input-message-success {
  border-color: var(--theia-successBackground) !important;
}

.theia-scm-input-message-warning {
  border-color: var(--theia-inputValidation-warningBorder) !important;
}

.theia-scm-input-message-error {
  border-color: var(--theia-inputValidation-errorBorder) !important;
}

.theia-scm-message,
.theia-scm-input-validation-message {
  padding: 4px 4px 4px 4px;
}

.theia-scm-validation-message-info {
  background-color: var(--theia-inputValidation-infoBackground) !important;
  color: var(--theia-inputValidation-infoForeground);
  border: var(--theia-border-width) solid
    var(--theia-inputValidation-infoBorder);
  border-top: none; /* remove top border since the input declares it already */
}

.theia-scm-validation-message-success {
  background-color: var(--theia-successBackground) !important;
  color: var(--theia-inputValidation-warningBackground);
}

.theia-scm-message-warning,
.theia-scm-validation-message-warning {
  background-color: var(--theia-inputValidation-warningBackground) !important;
  color: var(--theia-inputValidation-warningForeground);
  border: var(--theia-border-width) solid
    var(--theia-inputValidation-warningBorder);
  border-top: none; /* remove top border since the input declares it already */
}

.theia-scm-validation-message-error {
  background-color: var(--theia-inputValidation-errorBackground) !important;
  color: var(--theia-inputValidation-errorForeground);
  border: var(--theia-border-width) solid
    var(--theia-inputValidation-errorBorder);
  border-top: none; /* remove top border since the input declares it already */
}

.no-select:focus {
  outline: none;
}

.theia-scm .scmItem {
  font-size: var(--theia-ui-font-size1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: var(--theia-content-line-height);
  line-height: var(--theia-content-line-height);
  padding: 0px calc(var(--theia-ui-padding) / 2);
}

.theia-scm .scmItem:hover {
  cursor: pointer;
}

.theia-scm:focus-within .scmItem:focus {
  background: var(--theia-list-focusBackground);
  color: var(--theia-list-focusForeground);
}

.theia-scm:not(:focus-within) .scmItem:not(:focus) {
  background: var(--theia-list-inactiveFocusBackground);
}

.theia-scm:focus-within .scmItem.theia-mod-selected {
  background: var(--theia-list-activeSelectionBackground);
  color: var(--theia-list-activeSelectionForeground);
}

.theia-scm:not(:focus-within) .scmItem.theia-mod-selected {
  background: var(--theia-list-inactiveSelectionBackground);
  color: var(--theia-list-inactiveSelectionForeground);
}

.theia-scm .scmItem .path {
  font-size: var(--theia-ui-font-size0);
  margin-left: var(--theia-ui-padding);
  opacity: 0.7;
}

.theia-scm .scmItem .status {
  text-align: center;
  font-size: var(--theia-ui-font-size0);
  margin-right: var(--theia-ui-padding);
}

.scm-theia-header {
  display: flex;
  align-items: center;
  padding-right: var(--theia-ui-padding);
}

.scm-theia-header:hover {
  cursor: pointer;
}

.theia-scm-inline-actions-container {
  display: flex;
  justify-content: flex-end;
  margin-left: 3px;
  min-height: 16px;
}

.theia-scm-inline-actions {
  display: flex;
  align-items: center;
  gap: 2px;
  margin-right: var(--theia-ui-padding);
}

.theia-scm-inline-actions a {
  color: var(--theia-icon-foreground);
}

.theia-scm-inline-action {
  display: flex;
  align-items: center;
  min-width: var(--theia-icon-size);
  height: var(--theia-icon-size);
  line-height: var(--theia-icon-size);
  font-size: var(--theia-ui-font-size1);
  cursor: pointer;
}

.theia-scm-inline-action .open-file {
  height: var(--theia-icon-size);
  width: 12px;
  background: var(--theia-icon-open-file) no-repeat center center;
}

.theia-scm-panel {
  overflow: visible;
}

<div align='center'>

<br />

<img src='https://raw.githubusercontent.com/eclipse-theia/theia/master/logo/theia.svg?sanitize=true' alt='theia-ext-logo' width='100px' />

<h2>ECLIPSE THEIA - TOOLBAR EXTENSION</h2>

<hr />

</div>

## Description

The `@theia/toolbar` extension contributes a global toolbar to the application shell. The toolbar supplements the `Command Palette` and allows users to easily add, remove, and rearrange toolbar items to their liking. The toolbar is hidden by default and ships with a default layout which can be overridden by downstream applications. Extenders can also contribute custom widgets to the toolbar through a contribution point.

## Additional Information

An example toolbar custom widget and layout override can be found in [here](https://github.com/eclipse-theia/theia/tree/master/examples/api-samples/src/browser/toolbar).

- [API documentation for `@theia/toolbar`](https://eclipse-theia.github.io/theia/docs/next/modules/toolbar.html)
- [Theia - GitHub](https://github.com/eclipse-theia/theia)
- [Theia - Website](https://theia-ide.org/)

## License

- [Eclipse Public License 2.0](http://www.eclipse.org/legal/epl-2.0/)
- [一 (Secondary) GNU General Public License, version 2 with the GNU Classpath Exception](https://projects.eclipse.org/license/secondary-gpl-2.0-cp)

## Trademark

"Theia" is a trademark of the Eclipse Foundation
<https://www.eclipse.org/theia>

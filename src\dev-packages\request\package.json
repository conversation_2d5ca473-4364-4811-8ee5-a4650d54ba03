{"name": "@theia/request", "version": "1.63.0", "description": "Theia Proxy-Aware Request Service", "publishConfig": {"access": "public"}, "license": "EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0", "repository": {"type": "git", "url": "https://github.com/eclipse-theia/theia.git"}, "bugs": {"url": "https://github.com/eclipse-theia/theia/issues"}, "homepage": "https://github.com/eclipse-theia/theia", "files": ["lib", "src"], "main": "lib/index.js", "typings": "lib/index.d.ts", "scripts": {"build": "theiaext build", "clean": "theiaext clean", "compile": "theiaext compile", "lint": "theiaext lint", "test": "theiaext test", "watch": "theiaext watch"}, "dependencies": {"http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.0", "tslib": "^2.6.2"}, "gitHead": "21358137e41342742707f660b8e222f940a27652"}
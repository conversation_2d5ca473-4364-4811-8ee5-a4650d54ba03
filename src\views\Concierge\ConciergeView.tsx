
import React, { useState, useEffect } from 'react';
import { Card } from '../../components/Card';
import { ServiceRequest } from './ServiceRequest';
import { BellAlertIcon } from '@heroicons/react/24/outline';

type Notification = {
    id: string;
    message: string;
    time: string;
};

export const ConciergeView: React.FC = () => {
    const [notifications, setNotifications] = useState<Notification[]>([]);
    const [loading, setLoading] = useState(true);

    const fetchNotifications = async () => {
        try {
            setLoading(true);
            const response = await fetch('/api/notifications');
            const data = await response.json();
            // Transform the data to match the expected format
            const transformedData = data.map((notification: any) => ({
                id: notification.id,
                message: notification.message,
                time: new Date(notification.createdAt).toLocaleString()
            }));
            setNotifications(transformedData);
        } catch (error) {
            console.error("Failed to fetch notifications", error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchNotifications();
    }, []);

    const handleRequestSubmitted = () => {
        // Refetch notifications after a new request is submitted
        fetchNotifications();
    };

    return (
        <div className="flex-grow grid grid-cols-1 md:grid-cols-2 gap-4">
            <ServiceRequest onRequestSubmitted={handleRequestSubmitted} />
            <Card title="Notifications">
                 {loading ? <div className="text-gray-400">Loading...</div> : (
                    !notifications || !Array.isArray(notifications) || notifications.length === 0 ? (
                        <div className="text-gray-400">No notifications available.</div>
                    ) : (
                        <ul className="space-y-3">
                            {notifications.map(n => (
                                <li key={n.id} className="flex items-start gap-3">
                                    <div className="mt-1 text-cyan-400">
                                    <BellAlertIcon className="w-5 h-5" />
                                    </div>
                                    <div>
                                        <p className="text-white text-sm">{n.message}</p>
                                        <p className="text-xs text-gray-500">{n.time}</p>
                                    </div>
                                </li>
                            ))}
                        </ul>
                    )
                 )}
            </Card>
        </div>
    );
};

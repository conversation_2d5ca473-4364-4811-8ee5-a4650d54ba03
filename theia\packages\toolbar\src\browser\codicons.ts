// *****************************************************************************
// Copyright (C) 2022 <PERSON> and others.
//
// This program and the accompanying materials are made available under the
// terms of the Eclipse Public License v. 2.0 which is available at
// http://www.eclipse.org/legal/epl-2.0.
//
// This Source Code may also be made available under the following Secondary
// Licenses when the conditions for such availability set forth in the Eclipse
// Public License v. 2.0 are satisfied: GNU General Public License, version 2
// with the GNU Classpath Exception which is available at
// https://www.gnu.org/software/classpath/license.html.
//
// SPDX-License-Identifier: EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0
// *****************************************************************************

/* eslint-disable @typescript-eslint/quotes, max-len */
export const codicons = ["codicon-add", "codicon-plus", "codicon-gist-new", "codicon-repo-create", "codicon-lightbulb", "codicon-light-bulb", "codicon-repo", "codicon-repo-delete", "codicon-gist-fork", "codicon-repo-forked", "codicon-git-pull-request", "codicon-git-pull-request-abandoned", "codicon-record-keys", "codicon-keyboard", "codicon-tag", "codicon-tag-add", "codicon-tag-remove", "codicon-person", "codicon-person-follow", "codicon-person-outline", "codicon-person-filled", "codicon-git-branch", "codicon-git-branch-create", "codicon-git-branch-delete", "codicon-source-control", "codicon-mirror", "codicon-mirror-public", "codicon-star", "codicon-star-add", "codicon-star-delete", "codicon-star-empty", "codicon-comment", "codicon-comment-add", "codicon-alert", "codicon-warning", "codicon-search", "codicon-search-save", "codicon-log-out", "codicon-sign-out", "codicon-log-in", "codicon-sign-in", "codicon-eye", "codicon-eye-unwatch", "codicon-eye-watch", "codicon-circle-filled", "codicon-primitive-dot", "codicon-close-dirty", "codicon-debug-breakpoint", "codicon-debug-breakpoint-disabled", "codicon-debug-hint", "codicon-primitive-square", "codicon-edit", "codicon-pencil", "codicon-info", "codicon-issue-opened", "codicon-gist-private", "codicon-git-fork-private", "codicon-lock", "codicon-mirror-private", "codicon-close", "codicon-remove-close", "codicon-x", "codicon-repo-sync", "codicon-sync", "codicon-clone", "codicon-desktop-download", "codicon-beaker", "codicon-microscope", "codicon-vm", "codicon-device-desktop", "codicon-file", "codicon-file-text", "codicon-more", "codicon-ellipsis", "codicon-kebab-horizontal", "codicon-mail-reply", "codicon-reply", "codicon-organization", "codicon-organization-filled", "codicon-organization-outline", "codicon-new-file", "codicon-file-add", "codicon-new-folder", "codicon-file-directory-create", "codicon-trash", "codicon-trashcan", "codicon-history", "codicon-clock", "codicon-folder", "codicon-file-directory", "codicon-symbol-folder", "codicon-logo-github", "codicon-mark-github", "codicon-github", "codicon-terminal", "codicon-console", "codicon-repl", "codicon-zap", "codicon-symbol-event", "codicon-error", "codicon-stop", "codicon-variable", "codicon-symbol-variable", "codicon-array", "codicon-symbol-array", "codicon-symbol-module", "codicon-symbol-package", "codicon-symbol-namespace", "codicon-symbol-object", "codicon-symbol-method", "codicon-symbol-function", "codicon-symbol-constructor", "codicon-symbol-boolean", "codicon-symbol-null", "codicon-symbol-numeric", "codicon-symbol-number", "codicon-symbol-structure", "codicon-symbol-struct", "codicon-symbol-parameter", "codicon-symbol-type-parameter", "codicon-symbol-key", "codicon-symbol-text", "codicon-symbol-reference", "codicon-go-to-file", "codicon-symbol-enum", "codicon-symbol-value", "codicon-symbol-ruler", "codicon-symbol-unit", "codicon-activate-breakpoints", "codicon-archive", "codicon-arrow-both", "codicon-arrow-down", "codicon-arrow-left", "codicon-arrow-right", "codicon-arrow-small-down", "codicon-arrow-small-left", "codicon-arrow-small-right", "codicon-arrow-small-up", "codicon-arrow-up", "codicon-bell", "codicon-bold", "codicon-book", "codicon-bookmark", "codicon-debug-breakpoint-conditional-unverified", "codicon-debug-breakpoint-conditional", "codicon-debug-breakpoint-conditional-disabled", "codicon-debug-breakpoint-data-unverified", "codicon-debug-breakpoint-data", "codicon-debug-breakpoint-data-disabled", "codicon-debug-breakpoint-log-unverified", "codicon-debug-breakpoint-log", "codicon-debug-breakpoint-log-disabled", "codicon-briefcase", "codicon-broadcast", "codicon-browser", "codicon-bug", "codicon-calendar", "codicon-case-sensitive", "codicon-check", "codicon-checklist", "codicon-chevron-down", "codicon-chevron-left", "codicon-chevron-right", "codicon-chevron-up", "codicon-chrome-close", "codicon-chrome-maximize", "codicon-chrome-minimize", "codicon-chrome-restore", "codicon-circle-outline", "codicon-debug-breakpoint-unverified", "codicon-circle-slash", "codicon-circuit-board", "codicon-clear-all", "codicon-clippy", "codicon-close-all", "codicon-cloud-download", "codicon-cloud-upload", "codicon-code", "codicon-collapse-all", "codicon-color-mode", "codicon-comment-discussion", "codicon-credit-card", "codicon-dash", "codicon-dashboard", "codicon-database", "codicon-debug-continue", "codicon-debug-disconnect", "codicon-debug-pause", "codicon-debug-restart", "codicon-debug-start", "codicon-debug-step-into", "codicon-debug-step-out", "codicon-debug-step-over", "codicon-debug-stop", "codicon-debug", "codicon-device-camera-video", "codicon-device-camera", "codicon-device-mobile", "codicon-diff-added", "codicon-diff-ignored", "codicon-diff-modified", "codicon-diff-removed", "codicon-diff-renamed", "codicon-diff", "codicon-discard", "codicon-editor-layout", "codicon-empty-window", "codicon-exclude", "codicon-extensions", "codicon-eye-closed", "codicon-file-binary", "codicon-file-code", "codicon-file-media", "codicon-file-pdf", "codicon-file-submodule", "codicon-file-symlink-directory", "codicon-file-symlink-file", "codicon-file-zip", "codicon-files", "codicon-filter", "codicon-flame", "codicon-fold-down", "codicon-fold-up", "codicon-fold", "codicon-folder-active", "codicon-folder-opened", "codicon-gear", "codicon-gift", "codicon-gist-secret", "codicon-gist", "codicon-git-commit", "codicon-git-compare", "codicon-compare-changes", "codicon-git-merge", "codicon-github-action", "codicon-github-alt", "codicon-globe", "codicon-grabber", "codicon-graph", "codicon-gripper", "codicon-heart", "codicon-home", "codicon-horizontal-rule", "codicon-hubot", "codicon-inbox", "codicon-issue-reopened", "codicon-issues", "codicon-italic", "codicon-jersey", "codicon-json", "codicon-kebab-vertical", "codicon-key", "codicon-law", "codicon-lightbulb-autofix", "codicon-link-external", "codicon-link", "codicon-list-ordered", "codicon-list-unordered", "codicon-live-share", "codicon-loading", "codicon-location", "codicon-mail-read", "codicon-mail", "codicon-markdown", "codicon-megaphone", "codicon-mention", "codicon-milestone", "codicon-mortar-board", "codicon-move", "codicon-multiple-windows", "codicon-mute", "codicon-no-newline", "codicon-note", "codicon-octoface", "codicon-open-preview", "codicon-package", "codicon-paintcan", "codicon-pin", "codicon-play", "codicon-run", "codicon-plug", "codicon-preserve-case", "codicon-preview", "codicon-project", "codicon-pulse", "codicon-question", "codicon-quote", "codicon-radio-tower", "codicon-reactions", "codicon-references", "codicon-refresh", "codicon-regex", "codicon-remote-explorer", "codicon-remote", "codicon-remove", "codicon-replace-all", "codicon-replace", "codicon-repo-clone", "codicon-repo-force-push", "codicon-repo-pull", "codicon-repo-push", "codicon-report", "codicon-request-changes", "codicon-rocket", "codicon-root-folder-opened", "codicon-root-folder", "codicon-rss", "codicon-ruby", "codicon-save-all", "codicon-save-as", "codicon-save", "codicon-screen-full", "codicon-screen-normal", "codicon-search-stop", "codicon-server", "codicon-settings-gear", "codicon-settings", "codicon-shield", "codicon-smiley", "codicon-sort-precedence", "codicon-split-horizontal", "codicon-split-vertical", "codicon-squirrel", "codicon-star-full", "codicon-star-half", "codicon-symbol-class", "codicon-symbol-color", "codicon-symbol-constant", "codicon-symbol-enum-member", "codicon-symbol-field", "codicon-symbol-file", "codicon-symbol-interface", "codicon-symbol-keyword", "codicon-symbol-misc", "codicon-symbol-operator", "codicon-symbol-property", "codicon-wrench", "codicon-wrench-subaction", "codicon-symbol-snippet", "codicon-tasklist", "codicon-telescope", "codicon-text-size", "codicon-three-bars", "codicon-thumbsdown", "codicon-thumbsup", "codicon-tools", "codicon-triangle-down", "codicon-triangle-left", "codicon-triangle-right", "codicon-triangle-up", "codicon-twitter", "codicon-unfold", "codicon-unlock", "codicon-unmute", "codicon-unverified", "codicon-verified", "codicon-versions", "codicon-vm-active", "codicon-vm-outline", "codicon-vm-running", "codicon-watch", "codicon-whitespace", "codicon-whole-word", "codicon-window", "codicon-word-wrap", "codicon-zoom-in", "codicon-zoom-out", "codicon-list-filter", "codicon-list-flat", "codicon-list-selection", "codicon-selection", "codicon-list-tree", "codicon-debug-breakpoint-function-unverified", "codicon-debug-breakpoint-function", "codicon-debug-breakpoint-function-disabled", "codicon-debug-stackframe-active", "codicon-debug-stackframe-dot", "codicon-debug-stackframe", "codicon-debug-stackframe-focused", "codicon-debug-breakpoint-unsupported", "codicon-symbol-string", "codicon-debug-reverse-continue", "codicon-debug-step-back", "codicon-debug-restart-frame", "codicon-debug-alt", "codicon-call-incoming", "codicon-call-outgoing", "codicon-menu", "codicon-expand-all", "codicon-feedback", "codicon-group-by-ref-type", "codicon-ungroup-by-ref-type", "codicon-account", "codicon-bell-dot", "codicon-debug-console", "codicon-library", "codicon-output", "codicon-run-all", "codicon-sync-ignored", "codicon-pinned", "codicon-github-inverted", "codicon-server-process", "codicon-server-environment", "codicon-pass", "codicon-issue-closed", "codicon-stop-circle", "codicon-play-circle", "codicon-record", "codicon-debug-alt-small", "codicon-vm-connect", "codicon-cloud", "codicon-merge", "codicon-export", "codicon-graph-left", "codicon-magnet", "codicon-notebook", "codicon-redo", "codicon-check-all", "codicon-pinned-dirty", "codicon-pass-filled", "codicon-circle-large-filled", "codicon-circle-large-outline", "codicon-combine", "codicon-gather", "codicon-table", "codicon-variable-group", "codicon-type-hierarchy", "codicon-type-hierarchy-sub", "codicon-type-hierarchy-super", "codicon-git-pull-request-create", "codicon-run-above", "codicon-run-below", "codicon-notebook-template", "codicon-debug-rerun", "codicon-workspace-trusted", "codicon-workspace-untrusted", "codicon-workspace-unknown", "codicon-terminal-cmd", "codicon-terminal-debian", "codicon-terminal-linux", "codicon-terminal-powershell", "codicon-terminal-tmux", "codicon-terminal-ubuntu", "codicon-terminal-bash", "codicon-arrow-swap", "codicon-copy", "codicon-person-add", "codicon-filter-filled", "codicon-wand", "codicon-debug-line-by-line", "codicon-inspect", "codicon-layers", "codicon-layers-dot", "codicon-layers-active", "codicon-compass", "codicon-compass-dot", "codicon-compass-active", "codicon-azure", "codicon-issue-draft", "codicon-git-pull-request-closed", "codicon-git-pull-request-draft", "codicon-debug-all", "codicon-debug-coverage"];

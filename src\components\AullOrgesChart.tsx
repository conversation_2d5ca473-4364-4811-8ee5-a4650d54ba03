import React from 'react';
import { Card } from './Card';
import { <PERSON>C<PERSON>, <PERSON>, <PERSON>Axis, <PERSON>A<PERSON>s, Toolt<PERSON>, ResponsiveContainer, Cell } from 'recharts';

const data = [
  { name: 'CPU', value: 400 },
  { name: 'GPU', value: 300 },
  { name: 'RAM', value: 600 },
  { name: 'IO', value: 280 },
  { name: 'NET', value: 450 },
];
const colors = ['#00FFFF', '#00EFFF', '#00DFFF', '#00CFFF', '#00BFFF'];

export const AullOrgesChart: React.FC = () => {
  return (
    <Card title="Resource Allocation">
      <div className="w-full h-full flex-grow">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data} margin={{ top: 5, right: 0, left: -25, bottom: 0 }}>
            <XAxis dataKey="name" tick={{ fill: '#A0A0B0', fontSize: 10 }} axisLine={false} tickLine={false} />
            <YAxis tick={{ fill: '#A0A0B0', fontSize: 10 }} axisLine={false} tickLine={false} />
            <Tooltip
              cursor={{ fill: 'rgba(255, 255, 255, 0.1)' }}
              contentStyle={{
                background: 'rgba(25, 40, 60, 0.8)',
                borderColor: '#00FFFF',
                color: '#E0E0E0',
                borderRadius: '0.5rem'
              }}
            />
            <Bar dataKey="value" radius={[4, 4, 0, 0]}>
                 {data.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </div>
    </Card>
  );
};
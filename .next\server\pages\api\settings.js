"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/settings";
exports.ids = ["pages/api/settings"];
exports.modules = {

/***/ "@google/genai":
/*!********************************!*\
  !*** external "@google/genai" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("@google/genai");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsettings&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Csettings.ts&middlewareConfigBase64=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsettings&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Csettings.ts&middlewareConfigBase64=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_settings_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\settings.ts */ \"(api)/./pages/api/settings.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_settings_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_settings_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/settings\",\n        pathname: \"/api/settings\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_settings_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsettings&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Csettings.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./backend/lib/gemini.ts":
/*!*******************************!*\
  !*** ./backend/lib/gemini.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Type: () => (/* reexport safe */ _google_genai__WEBPACK_IMPORTED_MODULE_0__.Type),\n/* harmony export */   generateStructuredData: () => (/* binding */ generateStructuredData),\n/* harmony export */   generateText: () => (/* binding */ generateText)\n/* harmony export */ });\n/* harmony import */ var _google_genai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/genai */ \"@google/genai\");\n/* harmony import */ var _google_genai__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_google_genai__WEBPACK_IMPORTED_MODULE_0__);\n\nconst hasValidApiKey =  true && \"AIzaSyAlO1Kg7slyW4uNvNUE0FN1xSrIQQ1_-O8\".startsWith(\"AIza\");\nlet ai = null;\nif (hasValidApiKey) {\n    ai = new _google_genai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenAI({\n        apiKey: \"AIzaSyAlO1Kg7slyW4uNvNUE0FN1xSrIQQ1_-O8\"\n    });\n} else {\n    console.warn(\"⚠️  No valid Gemini API key found. Using fallback mock data. Set GEMINI_API_KEY in .env.local to use real AI features.\");\n}\nasync function generateText(prompt) {\n    if (!ai) {\n        // Fallback mock response for mood analysis\n        return \"System analysis indicates elevated stress levels detected. Recommend implementing relaxation protocols and scheduling wellness check-in within 2 hours.\";\n    }\n    try {\n        const response = await ai.models.generateContent({\n            model: \"gemini-2.5-flash\",\n            contents: prompt\n        });\n        return response.text;\n    } catch (e) {\n        console.error(\"Error generating text from Gemini:\", e);\n        // Return fallback data instead of throwing error\n        return \"System analysis temporarily unavailable. Default wellness protocols active. Please check back later for detailed analysis.\";\n    }\n}\nasync function generateStructuredData(prompt, schema) {\n    if (!ai) {\n        // Return fallback mock data for dashboard widgets\n        const fallbackData = {\n            contacts: [\n                {\n                    name: \"Dr. Aris Thorne\",\n                    detail: \"Chief Scientist\",\n                    avatar: \"aris\"\n                },\n                {\n                    name: \"Commander Zara Vex\",\n                    detail: \"Security Director\",\n                    avatar: \"zara\"\n                },\n                {\n                    name: \"Engineer Kai Nexus\",\n                    detail: \"Systems Architect\",\n                    avatar: \"kai\"\n                }\n            ],\n            safetyItems: [\n                {\n                    label: \"Firewall Active\",\n                    color: \"#00FFFF\"\n                },\n                {\n                    label: \"Quantum Encryption\",\n                    color: \"#00FFFF\"\n                },\n                {\n                    label: \"Bio-Scanner Online\",\n                    color: \"#3b82f6\"\n                },\n                {\n                    label: \"Sub-routine Anomaly\",\n                    color: \"#ef4444\"\n                },\n                {\n                    label: \"Neural Link Stable\",\n                    color: \"#3b82f6\"\n                },\n                {\n                    label: \"Core Temperature\",\n                    color: \"#3b82f6\"\n                }\n            ],\n            safetyChartData: [\n                85,\n                92,\n                78,\n                95,\n                88,\n                91\n            ],\n            systemRequests: [\n                {\n                    text: \"Security Patch Update\"\n                },\n                {\n                    text: \"Cryo-chamber diagnostics\"\n                },\n                {\n                    text: \"Neural interface calibration\"\n                }\n            ],\n            systemStats: [\n                {\n                    icon: \"BuildingOfficeIcon\",\n                    value: \"1597\",\n                    label: \"Processes\"\n                },\n                {\n                    icon: \"PuzzlePieceIcon\",\n                    value: \"84%\",\n                    label: \"RAM Usage\"\n                },\n                {\n                    icon: \"SnowflakeIcon\",\n                    value: \"24\\xb0C\",\n                    label: \"Core Temp\"\n                },\n                {\n                    icon: \"MemoryChipIcon\",\n                    value: \"2.4TB\",\n                    label: \"Storage\"\n                },\n                {\n                    icon: \"CogIcon\",\n                    value: \"99.7%\",\n                    label: \"Uptime\"\n                }\n            ]\n        };\n        return fallbackData;\n    }\n    try {\n        const response = await ai.models.generateContent({\n            model: \"gemini-2.5-flash\",\n            contents: prompt,\n            config: {\n                responseMimeType: \"application/json\",\n                responseSchema: schema\n            }\n        });\n        if (!response.text) {\n            throw new Error(\"Received an empty response from the AI.\");\n        }\n        const cleanedText = response.text.trim().replace(/^```json\\s*|```\\s*$/g, \"\");\n        if (!cleanedText) {\n            throw new Error(\"Received an empty JSON response from the AI after cleaning.\");\n        }\n        return JSON.parse(cleanedText);\n    } catch (e) {\n        console.error(\"Failed to generate or parse Gemini JSON response:\", e);\n        // Return fallback data instead of throwing error\n        const fallbackData = {\n            contacts: [\n                {\n                    name: \"Dr. Aris Thorne\",\n                    detail: \"Chief Scientist\",\n                    avatar: \"aris\"\n                },\n                {\n                    name: \"Commander Zara Vex\",\n                    detail: \"Security Director\",\n                    avatar: \"zara\"\n                },\n                {\n                    name: \"Engineer Kai Nexus\",\n                    detail: \"Systems Architect\",\n                    avatar: \"kai\"\n                }\n            ],\n            safetyItems: [\n                {\n                    label: \"Firewall Active\",\n                    color: \"#00FFFF\"\n                },\n                {\n                    label: \"Quantum Encryption\",\n                    color: \"#00FFFF\"\n                },\n                {\n                    label: \"Bio-Scanner Online\",\n                    color: \"#3b82f6\"\n                },\n                {\n                    label: \"Sub-routine Anomaly\",\n                    color: \"#ef4444\"\n                },\n                {\n                    label: \"Neural Link Stable\",\n                    color: \"#3b82f6\"\n                },\n                {\n                    label: \"Core Temperature\",\n                    color: \"#3b82f6\"\n                }\n            ],\n            safetyChartData: [\n                85,\n                92,\n                78,\n                95,\n                88,\n                91\n            ],\n            systemRequests: [\n                {\n                    text: \"Security Patch Update\"\n                },\n                {\n                    text: \"Cryo-chamber diagnostics\"\n                },\n                {\n                    text: \"Neural interface calibration\"\n                }\n            ],\n            systemStats: [\n                {\n                    icon: \"BuildingOfficeIcon\",\n                    value: \"1597\",\n                    label: \"Processes\"\n                },\n                {\n                    icon: \"PuzzlePieceIcon\",\n                    value: \"84%\",\n                    label: \"RAM Usage\"\n                },\n                {\n                    icon: \"SnowflakeIcon\",\n                    value: \"24\\xb0C\",\n                    label: \"Core Temp\"\n                },\n                {\n                    icon: \"MemoryChipIcon\",\n                    value: \"2.4TB\",\n                    label: \"Storage\"\n                },\n                {\n                    icon: \"CogIcon\",\n                    value: \"99.7%\",\n                    label: \"Uptime\"\n                }\n            ]\n        };\n        return fallbackData;\n    }\n}\n // Re-export Type for convenience\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9iYWNrZW5kL2xpYi9nZW1pbmkudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFDaUY7QUFFakYsTUFBTUUsaUJBQWlCQyxLQUVVLElBQy9CQSx5Q0FBMEIsQ0FBQ0csVUFBVSxDQUFDO0FBRXhDLElBQUlDLEtBQXlCO0FBRTdCLElBQUlMLGdCQUFnQjtJQUNsQkssS0FBSyxJQUFJUCxzREFBV0EsQ0FBQztRQUFFUSxRQUFRTCx5Q0FBMEI7SUFBRTtBQUM3RCxPQUFPO0lBQ0xNLFFBQVFDLElBQUksQ0FBQztBQUNmO0FBRU8sZUFBZUMsYUFBYUMsTUFBYztJQUMvQyxJQUFJLENBQUNMLElBQUk7UUFDUCwyQ0FBMkM7UUFDM0MsT0FBTztJQUNUO0lBRUEsSUFBSTtRQUNGLE1BQU1NLFdBQW9DLE1BQU1OLEdBQUdPLE1BQU0sQ0FBQ0MsZUFBZSxDQUFDO1lBQ3hFQyxPQUFPO1lBQ1BDLFVBQVVMO1FBQ1o7UUFDQSxPQUFPQyxTQUFTSyxJQUFJO0lBQ3RCLEVBQUUsT0FBTUMsR0FBRztRQUNUVixRQUFRVyxLQUFLLENBQUMsc0NBQXNDRDtRQUNwRCxpREFBaUQ7UUFDakQsT0FBTztJQUNUO0FBQ0Y7QUFFTyxlQUFlRSx1QkFBMEJULE1BQWMsRUFBRVUsTUFBVztJQUN6RSxJQUFJLENBQUNmLElBQUk7UUFDUCxrREFBa0Q7UUFDbEQsTUFBTWdCLGVBQWU7WUFDbkJDLFVBQVU7Z0JBQ1I7b0JBQUVDLE1BQU07b0JBQW1CQyxRQUFRO29CQUFtQkMsUUFBUTtnQkFBTztnQkFDckU7b0JBQUVGLE1BQU07b0JBQXNCQyxRQUFRO29CQUFxQkMsUUFBUTtnQkFBTztnQkFDMUU7b0JBQUVGLE1BQU07b0JBQXNCQyxRQUFRO29CQUFxQkMsUUFBUTtnQkFBTTthQUMxRTtZQUNEQyxhQUFhO2dCQUNYO29CQUFFQyxPQUFPO29CQUFtQkMsT0FBTztnQkFBVTtnQkFDN0M7b0JBQUVELE9BQU87b0JBQXNCQyxPQUFPO2dCQUFVO2dCQUNoRDtvQkFBRUQsT0FBTztvQkFBc0JDLE9BQU87Z0JBQVU7Z0JBQ2hEO29CQUFFRCxPQUFPO29CQUF1QkMsT0FBTztnQkFBVTtnQkFDakQ7b0JBQUVELE9BQU87b0JBQXNCQyxPQUFPO2dCQUFVO2dCQUNoRDtvQkFBRUQsT0FBTztvQkFBb0JDLE9BQU87Z0JBQVU7YUFDL0M7WUFDREMsaUJBQWlCO2dCQUFDO2dCQUFJO2dCQUFJO2dCQUFJO2dCQUFJO2dCQUFJO2FBQUc7WUFDekNDLGdCQUFnQjtnQkFDZDtvQkFBRWQsTUFBTTtnQkFBd0I7Z0JBQ2hDO29CQUFFQSxNQUFNO2dCQUEyQjtnQkFDbkM7b0JBQUVBLE1BQU07Z0JBQStCO2FBQ3hDO1lBQ0RlLGFBQWE7Z0JBQ1g7b0JBQUVDLE1BQU07b0JBQXNCQyxPQUFPO29CQUFRTixPQUFPO2dCQUFZO2dCQUNoRTtvQkFBRUssTUFBTTtvQkFBbUJDLE9BQU87b0JBQU9OLE9BQU87Z0JBQVk7Z0JBQzVEO29CQUFFSyxNQUFNO29CQUFpQkMsT0FBTztvQkFBUU4sT0FBTztnQkFBWTtnQkFDM0Q7b0JBQUVLLE1BQU07b0JBQWtCQyxPQUFPO29CQUFTTixPQUFPO2dCQUFVO2dCQUMzRDtvQkFBRUssTUFBTTtvQkFBV0MsT0FBTztvQkFBU04sT0FBTztnQkFBUzthQUNwRDtRQUNIO1FBQ0EsT0FBT047SUFDVDtJQUVBLElBQUk7UUFDRixNQUFNVixXQUFvQyxNQUFNTixHQUFHTyxNQUFNLENBQUNDLGVBQWUsQ0FBQztZQUN4RUMsT0FBTztZQUNQQyxVQUFVTDtZQUNWd0IsUUFBUTtnQkFDTkMsa0JBQWtCO2dCQUNsQkMsZ0JBQWdCaEI7WUFDbEI7UUFDRjtRQUVBLElBQUksQ0FBQ1QsU0FBU0ssSUFBSSxFQUFFO1lBQ2hCLE1BQU0sSUFBSXFCLE1BQU07UUFDcEI7UUFFQSxNQUFNQyxjQUFjM0IsU0FBU0ssSUFBSSxDQUFDdUIsSUFBSSxHQUFHQyxPQUFPLENBQUMsd0JBQXdCO1FBRXpFLElBQUksQ0FBQ0YsYUFBYTtZQUNkLE1BQU0sSUFBSUQsTUFBTTtRQUNwQjtRQUVBLE9BQU9JLEtBQUtDLEtBQUssQ0FBQ0o7SUFDcEIsRUFBRSxPQUFPckIsR0FBRztRQUNWVixRQUFRVyxLQUFLLENBQUMscURBQXFERDtRQUVuRSxpREFBaUQ7UUFDakQsTUFBTUksZUFBZTtZQUNuQkMsVUFBVTtnQkFDUjtvQkFBRUMsTUFBTTtvQkFBbUJDLFFBQVE7b0JBQW1CQyxRQUFRO2dCQUFPO2dCQUNyRTtvQkFBRUYsTUFBTTtvQkFBc0JDLFFBQVE7b0JBQXFCQyxRQUFRO2dCQUFPO2dCQUMxRTtvQkFBRUYsTUFBTTtvQkFBc0JDLFFBQVE7b0JBQXFCQyxRQUFRO2dCQUFNO2FBQzFFO1lBQ0RDLGFBQWE7Z0JBQ1g7b0JBQUVDLE9BQU87b0JBQW1CQyxPQUFPO2dCQUFVO2dCQUM3QztvQkFBRUQsT0FBTztvQkFBc0JDLE9BQU87Z0JBQVU7Z0JBQ2hEO29CQUFFRCxPQUFPO29CQUFzQkMsT0FBTztnQkFBVTtnQkFDaEQ7b0JBQUVELE9BQU87b0JBQXVCQyxPQUFPO2dCQUFVO2dCQUNqRDtvQkFBRUQsT0FBTztvQkFBc0JDLE9BQU87Z0JBQVU7Z0JBQ2hEO29CQUFFRCxPQUFPO29CQUFvQkMsT0FBTztnQkFBVTthQUMvQztZQUNEQyxpQkFBaUI7Z0JBQUM7Z0JBQUk7Z0JBQUk7Z0JBQUk7Z0JBQUk7Z0JBQUk7YUFBRztZQUN6Q0MsZ0JBQWdCO2dCQUNkO29CQUFFZCxNQUFNO2dCQUF3QjtnQkFDaEM7b0JBQUVBLE1BQU07Z0JBQTJCO2dCQUNuQztvQkFBRUEsTUFBTTtnQkFBK0I7YUFDeEM7WUFDRGUsYUFBYTtnQkFDWDtvQkFBRUMsTUFBTTtvQkFBc0JDLE9BQU87b0JBQVFOLE9BQU87Z0JBQVk7Z0JBQ2hFO29CQUFFSyxNQUFNO29CQUFtQkMsT0FBTztvQkFBT04sT0FBTztnQkFBWTtnQkFDNUQ7b0JBQUVLLE1BQU07b0JBQWlCQyxPQUFPO29CQUFRTixPQUFPO2dCQUFZO2dCQUMzRDtvQkFBRUssTUFBTTtvQkFBa0JDLE9BQU87b0JBQVNOLE9BQU87Z0JBQVU7Z0JBQzNEO29CQUFFSyxNQUFNO29CQUFXQyxPQUFPO29CQUFTTixPQUFPO2dCQUFTO2FBQ3BEO1FBQ0g7UUFDQSxPQUFPTjtJQUNUO0FBQ0Y7QUFFZ0IsQ0FBQyxpQ0FBaUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9leWVzLXNoaWVsZC1mdXR1cmlzdGljLXVpLy4vYmFja2VuZC9saWIvZ2VtaW5pLnRzP2MzMGYiXSwic291cmNlc0NvbnRlbnQiOlsiXG5pbXBvcnQgeyBHb29nbGVHZW5BSSwgR2VuZXJhdGVDb250ZW50UmVzcG9uc2UsIFR5cGUsIFBhcnQgfSBmcm9tICdAZ29vZ2xlL2dlbmFpJztcblxuY29uc3QgaGFzVmFsaWRBcGlLZXkgPSBwcm9jZXNzLmVudi5HRU1JTklfQVBJX0tFWSAmJlxuICBwcm9jZXNzLmVudi5HRU1JTklfQVBJX0tFWSAhPT0gJ3lvdXJfYWN0dWFsX2dlbWluaV9hcGlfa2V5X2hlcmUnICYmXG4gIHByb2Nlc3MuZW52LkdFTUlOSV9BUElfS0VZICE9PSAnUExBQ0VIT0xERVJfQVBJX0tFWScgJiZcbiAgcHJvY2Vzcy5lbnYuR0VNSU5JX0FQSV9LRVkuc3RhcnRzV2l0aCgnQUl6YScpO1xuXG5sZXQgYWk6IEdvb2dsZUdlbkFJIHwgbnVsbCA9IG51bGw7XG5cbmlmIChoYXNWYWxpZEFwaUtleSkge1xuICBhaSA9IG5ldyBHb29nbGVHZW5BSSh7IGFwaUtleTogcHJvY2Vzcy5lbnYuR0VNSU5JX0FQSV9LRVkhIH0pO1xufSBlbHNlIHtcbiAgY29uc29sZS53YXJuKCfimqDvuI8gIE5vIHZhbGlkIEdlbWluaSBBUEkga2V5IGZvdW5kLiBVc2luZyBmYWxsYmFjayBtb2NrIGRhdGEuIFNldCBHRU1JTklfQVBJX0tFWSBpbiAuZW52LmxvY2FsIHRvIHVzZSByZWFsIEFJIGZlYXR1cmVzLicpO1xufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2VuZXJhdGVUZXh0KHByb21wdDogc3RyaW5nKTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgaWYgKCFhaSkge1xuICAgIC8vIEZhbGxiYWNrIG1vY2sgcmVzcG9uc2UgZm9yIG1vb2QgYW5hbHlzaXNcbiAgICByZXR1cm4gXCJTeXN0ZW0gYW5hbHlzaXMgaW5kaWNhdGVzIGVsZXZhdGVkIHN0cmVzcyBsZXZlbHMgZGV0ZWN0ZWQuIFJlY29tbWVuZCBpbXBsZW1lbnRpbmcgcmVsYXhhdGlvbiBwcm90b2NvbHMgYW5kIHNjaGVkdWxpbmcgd2VsbG5lc3MgY2hlY2staW4gd2l0aGluIDIgaG91cnMuXCI7XG4gIH1cblxuICB0cnkge1xuICAgIGNvbnN0IHJlc3BvbnNlOiBHZW5lcmF0ZUNvbnRlbnRSZXNwb25zZSA9IGF3YWl0IGFpLm1vZGVscy5nZW5lcmF0ZUNvbnRlbnQoe1xuICAgICAgbW9kZWw6ICdnZW1pbmktMi41LWZsYXNoJyxcbiAgICAgIGNvbnRlbnRzOiBwcm9tcHQsXG4gICAgfSk7XG4gICAgcmV0dXJuIHJlc3BvbnNlLnRleHQ7XG4gIH0gY2F0Y2goZSkge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBnZW5lcmF0aW5nIHRleHQgZnJvbSBHZW1pbmk6XCIsIGUpO1xuICAgIC8vIFJldHVybiBmYWxsYmFjayBkYXRhIGluc3RlYWQgb2YgdGhyb3dpbmcgZXJyb3JcbiAgICByZXR1cm4gXCJTeXN0ZW0gYW5hbHlzaXMgdGVtcG9yYXJpbHkgdW5hdmFpbGFibGUuIERlZmF1bHQgd2VsbG5lc3MgcHJvdG9jb2xzIGFjdGl2ZS4gUGxlYXNlIGNoZWNrIGJhY2sgbGF0ZXIgZm9yIGRldGFpbGVkIGFuYWx5c2lzLlwiO1xuICB9XG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZW5lcmF0ZVN0cnVjdHVyZWREYXRhPFQ+KHByb21wdDogc3RyaW5nLCBzY2hlbWE6IGFueSk6IFByb21pc2U8VD4ge1xuICBpZiAoIWFpKSB7XG4gICAgLy8gUmV0dXJuIGZhbGxiYWNrIG1vY2sgZGF0YSBmb3IgZGFzaGJvYXJkIHdpZGdldHNcbiAgICBjb25zdCBmYWxsYmFja0RhdGEgPSB7XG4gICAgICBjb250YWN0czogW1xuICAgICAgICB7IG5hbWU6IFwiRHIuIEFyaXMgVGhvcm5lXCIsIGRldGFpbDogXCJDaGllZiBTY2llbnRpc3RcIiwgYXZhdGFyOiBcImFyaXNcIiB9LFxuICAgICAgICB7IG5hbWU6IFwiQ29tbWFuZGVyIFphcmEgVmV4XCIsIGRldGFpbDogXCJTZWN1cml0eSBEaXJlY3RvclwiLCBhdmF0YXI6IFwiemFyYVwiIH0sXG4gICAgICAgIHsgbmFtZTogXCJFbmdpbmVlciBLYWkgTmV4dXNcIiwgZGV0YWlsOiBcIlN5c3RlbXMgQXJjaGl0ZWN0XCIsIGF2YXRhcjogXCJrYWlcIiB9XG4gICAgICBdLFxuICAgICAgc2FmZXR5SXRlbXM6IFtcbiAgICAgICAgeyBsYWJlbDogXCJGaXJld2FsbCBBY3RpdmVcIiwgY29sb3I6IFwiIzAwRkZGRlwiIH0sXG4gICAgICAgIHsgbGFiZWw6IFwiUXVhbnR1bSBFbmNyeXB0aW9uXCIsIGNvbG9yOiBcIiMwMEZGRkZcIiB9LFxuICAgICAgICB7IGxhYmVsOiBcIkJpby1TY2FubmVyIE9ubGluZVwiLCBjb2xvcjogXCIjM2I4MmY2XCIgfSxcbiAgICAgICAgeyBsYWJlbDogXCJTdWItcm91dGluZSBBbm9tYWx5XCIsIGNvbG9yOiBcIiNlZjQ0NDRcIiB9LFxuICAgICAgICB7IGxhYmVsOiBcIk5ldXJhbCBMaW5rIFN0YWJsZVwiLCBjb2xvcjogXCIjM2I4MmY2XCIgfSxcbiAgICAgICAgeyBsYWJlbDogXCJDb3JlIFRlbXBlcmF0dXJlXCIsIGNvbG9yOiBcIiMzYjgyZjZcIiB9XG4gICAgICBdLFxuICAgICAgc2FmZXR5Q2hhcnREYXRhOiBbODUsIDkyLCA3OCwgOTUsIDg4LCA5MV0sXG4gICAgICBzeXN0ZW1SZXF1ZXN0czogW1xuICAgICAgICB7IHRleHQ6IFwiU2VjdXJpdHkgUGF0Y2ggVXBkYXRlXCIgfSxcbiAgICAgICAgeyB0ZXh0OiBcIkNyeW8tY2hhbWJlciBkaWFnbm9zdGljc1wiIH0sXG4gICAgICAgIHsgdGV4dDogXCJOZXVyYWwgaW50ZXJmYWNlIGNhbGlicmF0aW9uXCIgfVxuICAgICAgXSxcbiAgICAgIHN5c3RlbVN0YXRzOiBbXG4gICAgICAgIHsgaWNvbjogXCJCdWlsZGluZ09mZmljZUljb25cIiwgdmFsdWU6IFwiMTU5N1wiLCBsYWJlbDogXCJQcm9jZXNzZXNcIiB9LFxuICAgICAgICB7IGljb246IFwiUHV6emxlUGllY2VJY29uXCIsIHZhbHVlOiBcIjg0JVwiLCBsYWJlbDogXCJSQU0gVXNhZ2VcIiB9LFxuICAgICAgICB7IGljb246IFwiU25vd2ZsYWtlSWNvblwiLCB2YWx1ZTogXCIyNMKwQ1wiLCBsYWJlbDogXCJDb3JlIFRlbXBcIiB9LFxuICAgICAgICB7IGljb246IFwiTWVtb3J5Q2hpcEljb25cIiwgdmFsdWU6IFwiMi40VEJcIiwgbGFiZWw6IFwiU3RvcmFnZVwiIH0sXG4gICAgICAgIHsgaWNvbjogXCJDb2dJY29uXCIsIHZhbHVlOiBcIjk5LjclXCIsIGxhYmVsOiBcIlVwdGltZVwiIH1cbiAgICAgIF1cbiAgICB9O1xuICAgIHJldHVybiBmYWxsYmFja0RhdGEgYXMgVDtcbiAgfVxuXG4gIHRyeSB7XG4gICAgY29uc3QgcmVzcG9uc2U6IEdlbmVyYXRlQ29udGVudFJlc3BvbnNlID0gYXdhaXQgYWkubW9kZWxzLmdlbmVyYXRlQ29udGVudCh7XG4gICAgICBtb2RlbDogXCJnZW1pbmktMi41LWZsYXNoXCIsXG4gICAgICBjb250ZW50czogcHJvbXB0LFxuICAgICAgY29uZmlnOiB7XG4gICAgICAgIHJlc3BvbnNlTWltZVR5cGU6IFwiYXBwbGljYXRpb24vanNvblwiLFxuICAgICAgICByZXNwb25zZVNjaGVtYTogc2NoZW1hLFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIGlmICghcmVzcG9uc2UudGV4dCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJSZWNlaXZlZCBhbiBlbXB0eSByZXNwb25zZSBmcm9tIHRoZSBBSS5cIik7XG4gICAgfVxuXG4gICAgY29uc3QgY2xlYW5lZFRleHQgPSByZXNwb25zZS50ZXh0LnRyaW0oKS5yZXBsYWNlKC9eYGBganNvblxccyp8YGBgXFxzKiQvZywgJycpO1xuXG4gICAgaWYgKCFjbGVhbmVkVGV4dCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJSZWNlaXZlZCBhbiBlbXB0eSBKU09OIHJlc3BvbnNlIGZyb20gdGhlIEFJIGFmdGVyIGNsZWFuaW5nLlwiKTtcbiAgICB9XG5cbiAgICByZXR1cm4gSlNPTi5wYXJzZShjbGVhbmVkVGV4dCkgYXMgVDtcbiAgfSBjYXRjaCAoZSkge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJGYWlsZWQgdG8gZ2VuZXJhdGUgb3IgcGFyc2UgR2VtaW5pIEpTT04gcmVzcG9uc2U6XCIsIGUpO1xuXG4gICAgLy8gUmV0dXJuIGZhbGxiYWNrIGRhdGEgaW5zdGVhZCBvZiB0aHJvd2luZyBlcnJvclxuICAgIGNvbnN0IGZhbGxiYWNrRGF0YSA9IHtcbiAgICAgIGNvbnRhY3RzOiBbXG4gICAgICAgIHsgbmFtZTogXCJEci4gQXJpcyBUaG9ybmVcIiwgZGV0YWlsOiBcIkNoaWVmIFNjaWVudGlzdFwiLCBhdmF0YXI6IFwiYXJpc1wiIH0sXG4gICAgICAgIHsgbmFtZTogXCJDb21tYW5kZXIgWmFyYSBWZXhcIiwgZGV0YWlsOiBcIlNlY3VyaXR5IERpcmVjdG9yXCIsIGF2YXRhcjogXCJ6YXJhXCIgfSxcbiAgICAgICAgeyBuYW1lOiBcIkVuZ2luZWVyIEthaSBOZXh1c1wiLCBkZXRhaWw6IFwiU3lzdGVtcyBBcmNoaXRlY3RcIiwgYXZhdGFyOiBcImthaVwiIH1cbiAgICAgIF0sXG4gICAgICBzYWZldHlJdGVtczogW1xuICAgICAgICB7IGxhYmVsOiBcIkZpcmV3YWxsIEFjdGl2ZVwiLCBjb2xvcjogXCIjMDBGRkZGXCIgfSxcbiAgICAgICAgeyBsYWJlbDogXCJRdWFudHVtIEVuY3J5cHRpb25cIiwgY29sb3I6IFwiIzAwRkZGRlwiIH0sXG4gICAgICAgIHsgbGFiZWw6IFwiQmlvLVNjYW5uZXIgT25saW5lXCIsIGNvbG9yOiBcIiMzYjgyZjZcIiB9LFxuICAgICAgICB7IGxhYmVsOiBcIlN1Yi1yb3V0aW5lIEFub21hbHlcIiwgY29sb3I6IFwiI2VmNDQ0NFwiIH0sXG4gICAgICAgIHsgbGFiZWw6IFwiTmV1cmFsIExpbmsgU3RhYmxlXCIsIGNvbG9yOiBcIiMzYjgyZjZcIiB9LFxuICAgICAgICB7IGxhYmVsOiBcIkNvcmUgVGVtcGVyYXR1cmVcIiwgY29sb3I6IFwiIzNiODJmNlwiIH1cbiAgICAgIF0sXG4gICAgICBzYWZldHlDaGFydERhdGE6IFs4NSwgOTIsIDc4LCA5NSwgODgsIDkxXSxcbiAgICAgIHN5c3RlbVJlcXVlc3RzOiBbXG4gICAgICAgIHsgdGV4dDogXCJTZWN1cml0eSBQYXRjaCBVcGRhdGVcIiB9LFxuICAgICAgICB7IHRleHQ6IFwiQ3J5by1jaGFtYmVyIGRpYWdub3N0aWNzXCIgfSxcbiAgICAgICAgeyB0ZXh0OiBcIk5ldXJhbCBpbnRlcmZhY2UgY2FsaWJyYXRpb25cIiB9XG4gICAgICBdLFxuICAgICAgc3lzdGVtU3RhdHM6IFtcbiAgICAgICAgeyBpY29uOiBcIkJ1aWxkaW5nT2ZmaWNlSWNvblwiLCB2YWx1ZTogXCIxNTk3XCIsIGxhYmVsOiBcIlByb2Nlc3Nlc1wiIH0sXG4gICAgICAgIHsgaWNvbjogXCJQdXp6bGVQaWVjZUljb25cIiwgdmFsdWU6IFwiODQlXCIsIGxhYmVsOiBcIlJBTSBVc2FnZVwiIH0sXG4gICAgICAgIHsgaWNvbjogXCJTbm93Zmxha2VJY29uXCIsIHZhbHVlOiBcIjI0wrBDXCIsIGxhYmVsOiBcIkNvcmUgVGVtcFwiIH0sXG4gICAgICAgIHsgaWNvbjogXCJNZW1vcnlDaGlwSWNvblwiLCB2YWx1ZTogXCIyLjRUQlwiLCBsYWJlbDogXCJTdG9yYWdlXCIgfSxcbiAgICAgICAgeyBpY29uOiBcIkNvZ0ljb25cIiwgdmFsdWU6IFwiOTkuNyVcIiwgbGFiZWw6IFwiVXB0aW1lXCIgfVxuICAgICAgXVxuICAgIH07XG4gICAgcmV0dXJuIGZhbGxiYWNrRGF0YSBhcyBUO1xuICB9XG59XG5cbmV4cG9ydCB7IFR5cGUgfTsgLy8gUmUtZXhwb3J0IFR5cGUgZm9yIGNvbnZlbmllbmNlXG4iXSwibmFtZXMiOlsiR29vZ2xlR2VuQUkiLCJUeXBlIiwiaGFzVmFsaWRBcGlLZXkiLCJwcm9jZXNzIiwiZW52IiwiR0VNSU5JX0FQSV9LRVkiLCJzdGFydHNXaXRoIiwiYWkiLCJhcGlLZXkiLCJjb25zb2xlIiwid2FybiIsImdlbmVyYXRlVGV4dCIsInByb21wdCIsInJlc3BvbnNlIiwibW9kZWxzIiwiZ2VuZXJhdGVDb250ZW50IiwibW9kZWwiLCJjb250ZW50cyIsInRleHQiLCJlIiwiZXJyb3IiLCJnZW5lcmF0ZVN0cnVjdHVyZWREYXRhIiwic2NoZW1hIiwiZmFsbGJhY2tEYXRhIiwiY29udGFjdHMiLCJuYW1lIiwiZGV0YWlsIiwiYXZhdGFyIiwic2FmZXR5SXRlbXMiLCJsYWJlbCIsImNvbG9yIiwic2FmZXR5Q2hhcnREYXRhIiwic3lzdGVtUmVxdWVzdHMiLCJzeXN0ZW1TdGF0cyIsImljb24iLCJ2YWx1ZSIsImNvbmZpZyIsInJlc3BvbnNlTWltZVR5cGUiLCJyZXNwb25zZVNjaGVtYSIsIkVycm9yIiwiY2xlYW5lZFRleHQiLCJ0cmltIiwicmVwbGFjZSIsIkpTT04iLCJwYXJzZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./backend/lib/gemini.ts\n");

/***/ }),

/***/ "(api)/./pages/api/settings.ts":
/*!*******************************!*\
  !*** ./pages/api/settings.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../backend/lib/gemini */ \"(api)/./backend/lib/gemini.ts\");\n\nconst settingsSchema = {\n    type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.OBJECT,\n    properties: {\n        profile: {\n            type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.OBJECT,\n            properties: {\n                username: {\n                    type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                    description: \"A cool, futuristic username.\"\n                },\n                email: {\n                    type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                    description: \"A futuristic corporate email address.\"\n                },\n                avatar: {\n                    type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.STRING,\n                    description: \"A unique, single-word seed for a placeholder avatar URL, e.g., 'cyber' or 'neo'.\"\n                }\n            },\n            required: [\n                \"username\",\n                \"email\",\n                \"avatar\"\n            ]\n        },\n        notifications: {\n            type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.OBJECT,\n            properties: {\n                emailAlerts: {\n                    type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.BOOLEAN\n                },\n                pushNotifications: {\n                    type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.BOOLEAN\n                },\n                systemUpdates: {\n                    type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.BOOLEAN\n                }\n            },\n            required: [\n                \"emailAlerts\",\n                \"pushNotifications\",\n                \"systemUpdates\"\n            ]\n        },\n        security: {\n            type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.OBJECT,\n            properties: {\n                twoFactorAuth: {\n                    type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.BOOLEAN\n                },\n                biometricLock: {\n                    type: _backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.Type.BOOLEAN\n                }\n            },\n            required: [\n                \"twoFactorAuth\",\n                \"biometricLock\"\n            ]\n        }\n    },\n    required: [\n        \"profile\",\n        \"notifications\",\n        \"security\"\n    ]\n};\nasync function handler(req, res) {\n    if (req.method === \"GET\") {\n        try {\n            const prompt = \"Generate plausible data for a user settings page in a high-tech, futuristic dashboard. Provide a user profile, notification toggles, and security settings.\";\n            const data = await (0,_backend_lib_gemini__WEBPACK_IMPORTED_MODULE_0__.generateStructuredData)(prompt, settingsSchema);\n            // Construct the full avatar URL from the seed word\n            data.profile.avatar = `https://i.pravatar.cc/150?u=${data.profile.avatar}`;\n            return res.status(200).json(data);\n        } catch (error) {\n            console.error(\"Failed to fetch settings from AI:\", error);\n            return res.status(500).json({\n                error: \"Failed to fetch settings\"\n            });\n        }\n    }\n    if (req.method === \"POST\") {\n        const newSettings = req.body;\n        // In a real application with a database, you would validate and save the newSettings here.\n        // For this stateless implementation, we simply acknowledge the request was received.\n        console.log(\"Received stateless settings update:\", newSettings);\n        return res.status(200).json({\n            message: \"Settings updated successfully\"\n        });\n    }\n    res.setHeader(\"Allow\", [\n        \"GET\",\n        \"POST\"\n    ]);\n    res.status(405).end(`Method ${req.method} Not Allowed`);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/settings.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0"], () => (__webpack_exec__("(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsettings&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Csettings.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();
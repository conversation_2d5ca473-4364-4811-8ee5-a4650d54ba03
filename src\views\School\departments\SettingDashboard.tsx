import React from 'react';
import { Card } from '../../../components/Card';

interface SettingDashboardProps {
  activeSubSection: string;
}

export const SettingDashboard: React.FC<SettingDashboardProps> = ({ activeSubSection }) => {
  return (
    <div className="p-4 h-full">
      <Card title="Settings">
        <div className="p-6 text-center">
          <h3 className="text-xl font-bold text-white mb-4">System Settings</h3>
          <p className="text-gray-400">Configure system preferences and user settings.</p>
        </div>
      </Card>
    </div>
  );
};

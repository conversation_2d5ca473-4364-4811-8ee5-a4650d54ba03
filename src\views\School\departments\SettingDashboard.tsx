import React, { useState, useEffect } from 'react';
import { Card } from '../../../components/Card';
import {
  CogIcon,
  ShieldCheckIcon,
  BoltIcon,
  GlobeAltIcon,
  ChartBarIcon,
  UserIcon,
  ComputerDesktopIcon,
  WifiIcon
} from '@heroicons/react/24/solid';

interface SettingDashboardProps {
  activeSubSection: string;
}

export const SettingDashboard: React.FC<SettingDashboardProps> = ({ activeSubSection }) => {
  const [settingsData, setSettingsData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchSettingsData = async () => {
      try {
        setLoading(true);
        await new Promise(resolve => setTimeout(resolve, 500));

        const mockData = {
          systemStatus: {
            quantumProcessors: 'Online',
            neuralNetworks: 'Optimized',
            hologramProjectors: 'Active',
            blockchainNodes: 'Synchronized',
            aiModels: 'Learning',
            metaverseGateway: 'Connected',
            carbonFootprint: 'Neutral',
            securityLevel: 'Maximum'
          },
          aiSettings: [
            { name: 'Learning Algorithm Optimization', status: true, performance: 97.2, description: 'Adaptive learning path generation' },
            { name: 'Emotional Intelligence Monitoring', status: true, performance: 94.8, description: 'Student wellbeing analysis' },
            { name: 'Quantum Computing Integration', status: true, performance: 99.1, description: 'Advanced problem solving' },
            { name: 'Holographic Teacher Deployment', status: false, performance: 0, description: 'Virtual instructor system' }
          ],
          securitySettings: [
            { name: 'Quantum Encryption', level: 'Maximum', status: 'Active', strength: '2048-qubit' },
            { name: 'Biometric Authentication', level: 'High', status: 'Active', strength: 'Neural pattern' },
            { name: 'Blockchain Verification', level: 'Maximum', status: 'Active', strength: 'Immutable ledger' },
            { name: 'AI Threat Detection', level: 'Adaptive', status: 'Learning', strength: 'Predictive analysis' }
          ],
          performanceMetrics: [
            { metric: 'System Efficiency', value: 97.8, target: 95, color: 'text-green-400' },
            { metric: 'AI Response Time', value: 0.003, target: 0.01, unit: 'seconds', color: 'text-blue-400' },
            { metric: 'Quantum Coherence', value: 99.2, target: 98, color: 'text-purple-400' },
            { metric: 'Neural Network Accuracy', value: 96.7, target: 95, color: 'text-cyan-400' }
          ],
          environmentalSettings: [
            { setting: 'Carbon Neutral Operations', status: 'Active', impact: '100% renewable energy' },
            { setting: 'Quantum Cooling System', status: 'Optimized', impact: '60% energy reduction' },
            { setting: 'AI Power Management', status: 'Learning', impact: 'Adaptive consumption' },
            { setting: 'Hologram Efficiency Mode', status: 'Active', impact: '40% power savings' }
          ]
        };

        setSettingsData(mockData);
      } catch (error) {
        console.error('Failed to fetch settings data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSettingsData();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-cyan-400">Loading Quantum Settings...</div>
      </div>
    );
  }

  const renderContent = () => {
    switch (activeSubSection) {
      case 'dashboard':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* System Status */}
            <Card title="⚡ Quantum System Status" className="lg:col-span-3">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4">
                {Object.entries(settingsData.systemStatus).map(([key, value], index) => (
                  <div key={index} className="text-center">
                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-2 ${
                      value === 'Online' || value === 'Active' || value === 'Connected' || value === 'Optimized' || value === 'Synchronized' || value === 'Learning' || value === 'Neutral' || value === 'Maximum'
                        ? 'bg-gradient-to-br from-green-500 to-emerald-600'
                        : 'bg-gradient-to-br from-red-500 to-pink-600'
                    }`}>
                      <BoltIcon className="w-6 h-6 text-white" />
                    </div>
                    <p className={`text-sm font-bold ${
                      value === 'Online' || value === 'Active' || value === 'Connected' || value === 'Optimized' || value === 'Synchronized' || value === 'Learning' || value === 'Neutral' || value === 'Maximum'
                        ? 'text-green-400'
                        : 'text-red-400'
                    }`}>
                      {value as string}
                    </p>
                    <p className="text-xs text-gray-400 capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</p>
                  </div>
                ))}
              </div>
            </Card>

            {/* AI Settings */}
            <Card title="🤖 AI System Controls" className="lg:col-span-2">
              <div className="space-y-3 p-4">
                {settingsData.aiSettings.map((setting: any, index: number) => (
                  <div key={index} className="p-3 rounded-lg bg-gradient-to-r from-gray-800/40 to-gray-700/40 border border-gray-600/30">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1">
                        <h4 className="font-semibold text-white">{setting.name}</h4>
                        <p className="text-xs text-gray-400">{setting.description}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className={`text-sm font-bold ${setting.status ? 'text-green-400' : 'text-gray-400'}`}>
                          {setting.performance > 0 ? `${setting.performance}%` : 'Offline'}
                        </span>
                        <div className={`w-4 h-4 rounded-full ${setting.status ? 'bg-green-400' : 'bg-gray-600'}`}></div>
                      </div>
                    </div>
                    {setting.status && (
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-cyan-400 to-blue-500 h-2 rounded-full"
                          style={{ width: `${setting.performance}%` }}
                        ></div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </Card>

            {/* Security Settings */}
            <Card title="🛡️ Quantum Security">
              <div className="space-y-3 p-4">
                {settingsData.securitySettings.map((security: any, index: number) => (
                  <div key={index} className="p-3 rounded-lg bg-gradient-to-r from-purple-900/20 to-blue-900/20 border border-purple-500/30">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="text-sm font-medium text-white">{security.name}</h4>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        security.status === 'Active' ? 'bg-green-500/20 text-green-400' :
                        security.status === 'Learning' ? 'bg-blue-500/20 text-blue-400' :
                        'bg-yellow-500/20 text-yellow-400'
                      }`}>
                        {security.status}
                      </span>
                    </div>
                    <div className="flex justify-between items-center text-xs">
                      <span className="text-purple-400">{security.level} Level</span>
                      <span className="text-cyan-400">{security.strength}</span>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </div>
        );

      case 'analytics':
        return (
          <Card title="📊 Performance Analytics">
            <div className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {settingsData.performanceMetrics.map((metric: any, index: number) => (
                  <div key={index} className="p-4 rounded-lg bg-gradient-to-r from-gray-800/40 to-gray-700/40 border border-gray-600/30">
                    <h4 className="text-lg font-semibold text-white mb-2">{metric.metric}</h4>
                    <div className="flex items-center justify-between mb-2">
                      <span className={`text-2xl font-bold ${metric.color}`}>
                        {metric.value}{metric.unit || '%'}
                      </span>
                      <span className="text-sm text-gray-400">
                        Target: {metric.target}{metric.unit || '%'}
                      </span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${metric.color.replace('text-', 'bg-')}`}
                        style={{ width: `${Math.min((metric.value / metric.target) * 100, 100)}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        );

      case 'settings':
        return (
          <Card title="🌱 Environmental Controls">
            <div className="p-4">
              <div className="space-y-4">
                {settingsData.environmentalSettings.map((env: any, index: number) => (
                  <div key={index} className="p-4 rounded-lg bg-gradient-to-r from-green-900/20 to-emerald-900/20 border border-green-500/30">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-lg font-semibold text-white">{env.setting}</h4>
                      <span className={`px-3 py-1 rounded-full text-sm ${
                        env.status === 'Active' ? 'bg-green-500/20 text-green-400' :
                        env.status === 'Optimized' ? 'bg-blue-500/20 text-blue-400' :
                        'bg-yellow-500/20 text-yellow-400'
                      }`}>
                        {env.status}
                      </span>
                    </div>
                    <p className="text-sm text-cyan-400">💡 {env.impact}</p>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        );

      default:
        return (
          <Card title="Quantum System Settings">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Future System Controls</h3>
              <p className="text-gray-400">Quantum-powered settings with AI optimization and environmental consciousness.</p>
            </div>
          </Card>
        );
    }
  };

  return (
    <div className="p-4 h-full">
      {renderContent()}
    </div>
  );
};

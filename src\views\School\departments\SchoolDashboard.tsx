import React, { useState, useEffect } from 'react';
import { Card } from '../../../components/Card';
import { 
  AcademicCapIcon,
  UserGroupIcon,
  BuildingLibraryIcon,
  ChartBarIcon,
  CalendarIcon,
  BellIcon
} from '@heroicons/react/24/solid';

interface SchoolDashboardProps {
  activeSubSection: string;
}

export const SchoolDashboard: React.FC<SchoolDashboardProps> = ({ activeSubSection }) => {
  const [schoolData, setSchoolData] = useState<any>(null);
  const [overviewData, setOverviewData] = useState<any>(null);
  const [departmentsData, setDepartmentsData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch dashboard data
        const dashboardResponse = await fetch('/api/school/dashboard');
        if (dashboardResponse.ok) {
          const dashboardData = await dashboardResponse.json();
          setSchoolData(dashboardData);
        }

        // Fetch overview data
        const overviewResponse = await fetch('/api/school/overview');
        if (overviewResponse.ok) {
          const overviewDataResult = await overviewResponse.json();
          setOverviewData(overviewDataResult);
        }

        // Fetch departments data
        const departmentsResponse = await fetch('/api/school/departments');
        if (departmentsResponse.ok) {
          const departmentsDataResult = await departmentsResponse.json();
          setDepartmentsData(departmentsDataResult);
        }

      } catch (error) {
        console.error('Failed to fetch data:', error);
        // Fallback to mock data if API fails
        const mockData = {
          overview: {
            totalStudents: 1247,
            totalTeachers: 89,
            totalClasses: 156,
            averageGPA: 3.67,
            aiTutorSessions: 2847,
            virtualRealityClasses: 23,
            blockchainCertificates: 156,
            carbonFootprint: 2.3
          },
          departments: [
            { name: 'AI & Robotics', students: 312, teachers: 18, color: 'bg-blue-500/20 text-blue-400', innovation: 'Neural Networks Lab' },
            { name: 'Quantum Computing', students: 298, teachers: 15, color: 'bg-green-500/20 text-green-400', innovation: 'Quantum Simulator' }
          ],
          innovations: [
            { title: 'AI-Powered Personalized Learning', status: 'Active', impact: '94% improvement', icon: '🤖' },
            { title: 'Holographic Classrooms', status: 'Beta', impact: '87% engagement', icon: '🔮' }
          ],
          recentEvents: [
            { title: 'Global Virtual Science Fair', date: 'March 15', type: 'Innovation', participants: 47 },
            { title: 'AI Ethics Symposium', date: 'March 20', type: 'Academic', participants: 234 }
          ]
        };

        setSchoolData(mockData);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-cyan-400">Loading School Dashboard...</div>
      </div>
    );
  }

  const renderContent = () => {
    switch (activeSubSection) {
      case 'overview':
        return (
          <div className="space-y-4">
            {overviewData ? (
              <>
                {/* School Information */}
                <Card title="🏫 School Information" className="mb-4">
                  <div className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-xl font-bold text-white mb-2">{overviewData.schoolInfo.name}</h3>
                        <div className="space-y-2 text-sm">
                          <p className="text-gray-300"><span className="text-cyan-400">Established:</span> {overviewData.schoolInfo.established}</p>
                          <p className="text-gray-300"><span className="text-cyan-400">Motto:</span> {overviewData.schoolInfo.motto}</p>
                          <p className="text-gray-300"><span className="text-cyan-400">Accreditation:</span> {overviewData.schoolInfo.accreditation}</p>
                          <p className="text-gray-300"><span className="text-cyan-400">Ranking:</span> {overviewData.schoolInfo.ranking}</p>
                        </div>
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-white mb-2">Campus Details</h4>
                        <div className="space-y-2 text-sm">
                          <p className="text-gray-300"><span className="text-purple-400">Type:</span> {overviewData.schoolInfo.campusType}</p>
                          <p className="text-gray-300"><span className="text-purple-400">Access:</span> {overviewData.schoolInfo.timeZone}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>

                {/* Key Statistics */}
                <Card title="📊 Key Statistics" className="mb-4">
                  <div className="p-4">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-400">{overviewData.statistics.totalStudents.toLocaleString()}</div>
                        <div className="text-sm text-gray-400">Students</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-400">{overviewData.statistics.totalTeachers}</div>
                        <div className="text-sm text-gray-400">Teachers</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-400">{overviewData.statistics.virtualClassrooms}</div>
                        <div className="text-sm text-gray-400">Virtual Classrooms</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-cyan-400">{overviewData.statistics.aiTutors}</div>
                        <div className="text-sm text-gray-400">AI Tutors</div>
                      </div>
                    </div>
                  </div>
                </Card>

                {/* Achievements */}
                <Card title="🏆 Recent Achievements" className="mb-4">
                  <div className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {overviewData.achievements.slice(0, 6).map((achievement: any, index: number) => (
                        <div key={index} className="p-3 rounded-lg bg-gray-800/40 border border-gray-600/30">
                          <div className="flex items-start gap-3">
                            <span className="text-2xl">{achievement.icon}</span>
                            <div className="flex-1">
                              <h4 className="text-sm font-medium text-white">{achievement.title}</h4>
                              <p className="text-xs text-gray-400 mt-1">{achievement.description}</p>
                              <div className="flex items-center gap-2 mt-2">
                                <span className="px-2 py-1 rounded-full text-xs bg-blue-500/20 text-blue-400">
                                  {achievement.category}
                                </span>
                                <span className="text-xs text-gray-500">{achievement.year}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </Card>
              </>
            ) : (
              <div className="text-center text-gray-400">Loading overview data...</div>
            )}
          </div>
        );

      case 'departments':
        return (
          <div className="space-y-4">
            {departmentsData ? (
              <>
                {/* Department Summary */}
                <Card title="📊 Departments Overview" className="mb-4">
                  <div className="p-4">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-400">{departmentsData.summary.totalDepartments}</div>
                        <div className="text-sm text-gray-400">Departments</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-400">{departmentsData.summary.totalStudents.toLocaleString()}</div>
                        <div className="text-sm text-gray-400">Students</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-400">{departmentsData.summary.totalTeachers}</div>
                        <div className="text-sm text-gray-400">Teachers</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-cyan-400">${(departmentsData.summary.totalBudget / 1000000).toFixed(1)}M</div>
                        <div className="text-sm text-gray-400">Total Budget</div>
                      </div>
                    </div>
                  </div>
                </Card>

                {/* Departments Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {departmentsData.departments.map((dept: any) => (
                    <Card key={dept.id} title={`${dept.icon} ${dept.name}`} className="mb-4">
                      <div className="p-4">
                        <div className="mb-4">
                          <p className="text-sm text-gray-300 mb-2">{dept.description}</p>
                          <p className="text-xs text-cyan-400">Head: {dept.head}</p>
                        </div>

                        <div className="grid grid-cols-2 gap-4 mb-4">
                          <div className="text-center">
                            <div className="text-lg font-bold text-white">{dept.students}</div>
                            <div className="text-xs text-gray-400">Students</div>
                          </div>
                          <div className="text-center">
                            <div className="text-lg font-bold text-white">{dept.teachers}</div>
                            <div className="text-xs text-gray-400">Teachers</div>
                          </div>
                        </div>

                        <div className="mb-3">
                          <h4 className="text-sm font-semibold text-white mb-2">Innovation Focus</h4>
                          <span className="px-2 py-1 rounded-full text-xs bg-purple-500/20 text-purple-400">
                            {dept.innovation}
                          </span>
                        </div>

                        <div className="mb-3">
                          <h4 className="text-sm font-semibold text-white mb-2">Recent Projects</h4>
                          <div className="space-y-1">
                            {dept.recent_projects.slice(0, 2).map((project: any, index: number) => (
                              <div key={index} className="flex items-center justify-between text-xs">
                                <span className="text-gray-300">{project.name}</span>
                                <span className={`px-2 py-1 rounded-full ${
                                  project.status === 'Deployed' ? 'bg-green-500/20 text-green-400' :
                                  project.status === 'Active' ? 'bg-blue-500/20 text-blue-400' :
                                  project.status === 'Testing' ? 'bg-yellow-500/20 text-yellow-400' :
                                  'bg-gray-500/20 text-gray-400'
                                }`}>
                                  {project.status}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>

                        <div>
                          <h4 className="text-sm font-semibold text-white mb-2">Key Courses</h4>
                          <div className="space-y-1">
                            {dept.courses_offered.slice(0, 2).map((course: any, index: number) => (
                              <div key={index} className="flex items-center justify-between text-xs">
                                <span className="text-gray-300">{course.name}</span>
                                <span className="text-cyan-400">{course.students} students</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </>
            ) : (
              <div className="text-center text-gray-400">Loading departments data...</div>
            )}
          </div>
        );

      case 'dashboard':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Overview Stats */}
            <Card title="🚀 Future School Overview" className="lg:col-span-3">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4">
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <UserGroupIcon className="w-6 h-6 text-blue-400" />
                  </div>
                  <p className="text-2xl font-bold text-white">{schoolData.overview.totalStudents}</p>
                  <p className="text-sm text-gray-400">Global Students</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <AcademicCapIcon className="w-6 h-6 text-green-400" />
                  </div>
                  <p className="text-2xl font-bold text-white">{schoolData.overview.totalTeachers}</p>
                  <p className="text-sm text-gray-400">AI-Enhanced Teachers</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <BuildingLibraryIcon className="w-6 h-6 text-purple-400" />
                  </div>
                  <p className="text-2xl font-bold text-white">{schoolData.overview.virtualRealityClasses}</p>
                  <p className="text-sm text-gray-400">VR Classrooms</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <ChartBarIcon className="w-6 h-6 text-yellow-400" />
                  </div>
                  <p className="text-2xl font-bold text-white">{schoolData.overview.aiTutorSessions}</p>
                  <p className="text-sm text-gray-400">AI Tutor Sessions</p>
                </div>
              </div>
            </Card>

            {/* Innovation Hub */}
            <Card title="🔬 Innovation Hub" className="lg:col-span-2">
              <div className="space-y-3 p-4">
                {schoolData.innovations.map((innovation: any, index: number) => (
                  <div key={index} className="p-3 rounded-lg bg-gray-800/40 border border-gray-600/30">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{innovation.icon}</span>
                        <h4 className="text-sm font-medium text-white">{innovation.title}</h4>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        innovation.status === 'Active' ? 'bg-green-500/20 text-green-400' :
                        innovation.status === 'Live' ? 'bg-blue-500/20 text-blue-400' :
                        innovation.status === 'Beta' ? 'bg-yellow-500/20 text-yellow-400' :
                        'bg-purple-500/20 text-purple-400'
                      }`}>
                        {innovation.status}
                      </span>
                    </div>
                    <p className="text-xs text-gray-400">Impact: {innovation.impact}</p>
                  </div>
                ))}
              </div>
            </Card>

            {/* Future Departments */}
            <Card title="🌟 Next-Gen Departments">
              <div className="space-y-3 p-4">
                {schoolData.departments.map((dept: any, index: number) => (
                  <div key={index} className={`p-3 rounded-lg ${dept.color.split(' ')[0]} border border-gray-600/30`}>
                    <div className="flex justify-between items-center">
                      <div>
                        <h4 className={`font-semibold ${dept.color.split(' ').slice(1).join(' ')}`}>{dept.name}</h4>
                        <p className="text-sm text-gray-400">{dept.students} students • {dept.teachers} teachers</p>
                        <p className="text-xs text-cyan-400 mt-1">🚀 {dept.innovation}</p>
                      </div>
                      <div className={`w-8 h-8 rounded-lg ${dept.color.split(' ')[0]} flex items-center justify-center`}>
                        <BuildingLibraryIcon className={`w-4 h-4 ${dept.color.split(' ').slice(1).join(' ')}`} />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Global Events */}
            <Card title="🌍 Global Events">
              <div className="space-y-3 p-4">
                {schoolData.recentEvents.map((event: any, index: number) => (
                  <div key={index} className="flex items-start gap-3 p-3 rounded-lg hover:bg-gray-800/40 border border-gray-700/30">
                    <div className="w-10 h-10 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-lg flex items-center justify-center">
                      <CalendarIcon className="w-5 h-5 text-white" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-white">{event.title}</h4>
                      <p className="text-xs text-gray-400">{event.date} • {event.type}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-xs text-cyan-400">👥 {event.participants} participants</span>
                        <span className="w-1 h-1 bg-gray-500 rounded-full"></span>
                        <span className="text-xs text-green-400">🌐 Global</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </div>
        );

      case 'overview':
        return (
          <Card title="School Overview">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Comprehensive School Overview</h3>
              <p className="text-gray-400">Detailed school statistics and performance metrics will be displayed here.</p>
            </div>
          </Card>
        );

      case 'departments':
        return (
          <Card title="Department Management">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Department Management</h3>
              <p className="text-gray-400">Manage all school departments, their staff, and resources.</p>
            </div>
          </Card>
        );

      case 'facilities':
        return (
          <Card title="School Facilities">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Facilities Management</h3>
              <p className="text-gray-400">Monitor and manage school facilities, maintenance, and resources.</p>
            </div>
          </Card>
        );

      case 'events':
        return (
          <Card title="School Events">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Event Management</h3>
              <p className="text-gray-400">Plan, organize, and track school events and activities.</p>
            </div>
          </Card>
        );

      case 'announcements':
        return (
          <Card title="Announcements">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">School Announcements</h3>
              <p className="text-gray-400">Create and manage school-wide announcements and communications.</p>
            </div>
          </Card>
        );

      case 'performance':
        return (
          <Card title="Performance Analytics">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">School Performance</h3>
              <p className="text-gray-400">Analyze school performance metrics and academic achievements.</p>
            </div>
          </Card>
        );

      case 'settings':
        return (
          <Card title="School Settings">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">School Configuration</h3>
              <p className="text-gray-400">Configure school settings, policies, and system preferences.</p>
            </div>
          </Card>
        );

      default:
        return (
          <Card title="School Dashboard">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Welcome to School Management</h3>
              <p className="text-gray-400">Select a section from the navigation to get started.</p>
            </div>
          </Card>
        );
    }
  };

  return (
    <div className="h-full p-2">
      {renderContent()}
    </div>
  );
};

import React, { useState, useEffect } from 'react';
import { Card } from '../../../components/Card';
import { 
  AcademicCapIcon,
  UserGroupIcon,
  BuildingLibraryIcon,
  ChartBarIcon,
  CalendarIcon,
  BellIcon
} from '@heroicons/react/24/solid';

interface SchoolDashboardProps {
  activeSubSection: string;
}

export const SchoolDashboard: React.FC<SchoolDashboardProps> = ({ activeSubSection }) => {
  const [schoolData, setSchoolData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchSchoolData = async () => {
      try {
        setLoading(true);
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));
        
        const mockData = {
          overview: {
            totalStudents: 1247,
            totalTeachers: 89,
            totalClasses: 156,
            averageGPA: 3.67
          },
          departments: [
            { name: 'Science', students: 312, teachers: 18, color: 'bg-blue-500/20 text-blue-400' },
            { name: 'Mathematics', students: 298, teachers: 15, color: 'bg-green-500/20 text-green-400' },
            { name: 'Literature', students: 267, teachers: 12, color: 'bg-purple-500/20 text-purple-400' },
            { name: 'Arts', students: 189, teachers: 11, color: 'bg-pink-500/20 text-pink-400' }
          ],
          recentEvents: [
            { title: 'Science Fair 2024', date: 'March 15', type: 'Academic' },
            { title: 'Parent-Teacher Conference', date: 'March 20', type: 'Meeting' },
            { title: 'Spring Break', date: 'March 25-29', type: 'Holiday' }
          ]
        };
        
        setSchoolData(mockData);
      } catch (error) {
        console.error('Failed to fetch school data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSchoolData();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-cyan-400">Loading School Dashboard...</div>
      </div>
    );
  }

  const renderContent = () => {
    switch (activeSubSection) {
      case 'dashboard':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Overview Stats */}
            <Card title="School Overview" className="lg:col-span-3">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4">
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <UserGroupIcon className="w-6 h-6 text-blue-400" />
                  </div>
                  <p className="text-2xl font-bold text-white">{schoolData.overview.totalStudents}</p>
                  <p className="text-sm text-gray-400">Total Students</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <AcademicCapIcon className="w-6 h-6 text-green-400" />
                  </div>
                  <p className="text-2xl font-bold text-white">{schoolData.overview.totalTeachers}</p>
                  <p className="text-sm text-gray-400">Total Teachers</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <BuildingLibraryIcon className="w-6 h-6 text-purple-400" />
                  </div>
                  <p className="text-2xl font-bold text-white">{schoolData.overview.totalClasses}</p>
                  <p className="text-sm text-gray-400">Total Classes</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <ChartBarIcon className="w-6 h-6 text-yellow-400" />
                  </div>
                  <p className="text-2xl font-bold text-white">{schoolData.overview.averageGPA}</p>
                  <p className="text-sm text-gray-400">Average GPA</p>
                </div>
              </div>
            </Card>

            {/* Departments */}
            <Card title="Departments" className="md:col-span-2">
              <div className="space-y-3 p-4">
                {schoolData.departments.map((dept: any, index: number) => (
                  <div key={index} className={`p-3 rounded-lg ${dept.color.split(' ')[0]} border border-gray-600/30`}>
                    <div className="flex justify-between items-center">
                      <div>
                        <h4 className={`font-semibold ${dept.color.split(' ').slice(1).join(' ')}`}>{dept.name}</h4>
                        <p className="text-sm text-gray-400">{dept.students} students • {dept.teachers} teachers</p>
                      </div>
                      <div className={`w-8 h-8 rounded-lg ${dept.color.split(' ')[0]} flex items-center justify-center`}>
                        <BuildingLibraryIcon className={`w-4 h-4 ${dept.color.split(' ').slice(1).join(' ')}`} />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Recent Events */}
            <Card title="Recent Events">
              <div className="space-y-3 p-4">
                {schoolData.recentEvents.map((event: any, index: number) => (
                  <div key={index} className="flex items-start gap-3 p-2 rounded-lg hover:bg-gray-800/40">
                    <div className="w-8 h-8 bg-cyan-500/20 rounded-lg flex items-center justify-center">
                      <CalendarIcon className="w-4 h-4 text-cyan-400" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-white">{event.title}</h4>
                      <p className="text-xs text-gray-400">{event.date} • {event.type}</p>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </div>
        );

      case 'overview':
        return (
          <Card title="School Overview">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Comprehensive School Overview</h3>
              <p className="text-gray-400">Detailed school statistics and performance metrics will be displayed here.</p>
            </div>
          </Card>
        );

      case 'departments':
        return (
          <Card title="Department Management">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Department Management</h3>
              <p className="text-gray-400">Manage all school departments, their staff, and resources.</p>
            </div>
          </Card>
        );

      case 'facilities':
        return (
          <Card title="School Facilities">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Facilities Management</h3>
              <p className="text-gray-400">Monitor and manage school facilities, maintenance, and resources.</p>
            </div>
          </Card>
        );

      case 'events':
        return (
          <Card title="School Events">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Event Management</h3>
              <p className="text-gray-400">Plan, organize, and track school events and activities.</p>
            </div>
          </Card>
        );

      case 'announcements':
        return (
          <Card title="Announcements">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">School Announcements</h3>
              <p className="text-gray-400">Create and manage school-wide announcements and communications.</p>
            </div>
          </Card>
        );

      case 'performance':
        return (
          <Card title="Performance Analytics">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">School Performance</h3>
              <p className="text-gray-400">Analyze school performance metrics and academic achievements.</p>
            </div>
          </Card>
        );

      case 'settings':
        return (
          <Card title="School Settings">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">School Configuration</h3>
              <p className="text-gray-400">Configure school settings, policies, and system preferences.</p>
            </div>
          </Card>
        );

      default:
        return (
          <Card title="School Dashboard">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Welcome to School Management</h3>
              <p className="text-gray-400">Select a section from the navigation to get started.</p>
            </div>
          </Card>
        );
    }
  };

  return (
    <div className="p-4 h-full">
      {renderContent()}
    </div>
  );
};

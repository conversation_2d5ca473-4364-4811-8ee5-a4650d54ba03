import React, { useEffect, useRef } from 'react';
import { Container, ContainerModule } from '@theia/core/shared/inversify';
import { FrontendApplication } from '@theia/core/lib/browser/frontend-application';
import { FrontendApplicationModule } from '@theia/core/lib/browser/frontend-application-module';
import { messagingFrontendModule } from '@theia/core/lib/browser/messaging/messaging-frontend-module';
import { loggerFrontendModule } from '@theia/core/lib/browser/logger-frontend-module';
import { ThemeService } from '@theia/core/lib/browser/theming';
import { FrontendApplicationStateService } from '@theia/core/lib/browser/frontend-application-state';

// Core Theia modules
import { MonacoFrontendModule } from '@theia/monaco/lib/browser/monaco-frontend-module';
import { EditorFrontendModule } from '@theia/editor/lib/browser/editor-frontend-module';
import { NavigatorFrontendModule } from '@theia/navigator/lib/browser/navigator-frontend-module';
import { FileSystemFrontendModule } from '@theia/filesystem/lib/browser/filesystem-frontend-module';
import { WorkspaceFrontendModule } from '@theia/workspace/lib/browser/workspace-frontend-module';
import { TerminalFrontendModule } from '@theia/terminal/lib/browser/terminal-frontend-module';
import { OutlineViewFrontendModule } from '@theia/outline-view/lib/browser/outline-view-frontend-module';
import { PreferencesFrontendModule } from '@theia/preferences/lib/browser/preferences-frontend-module';
import { MenusFrontendModule } from '@theia/core/lib/browser/menu/menu-frontend-module';
import { KeymapsFrontendModule } from '@theia/keymaps/lib/browser/keymaps-frontend-module';

// Search and Git modules
import { FileSearchFrontendModule } from '@theia/file-search/lib/browser/file-search-frontend-module';
import { SearchInWorkspaceFrontendModule } from '@theia/search-in-workspace/lib/browser/search-in-workspace-frontend-module';
import { ScmFrontendModule } from '@theia/scm/lib/browser/scm-frontend-module';

// Debug and Task modules
import { DebugFrontendModule } from '@theia/debug/lib/browser/debug-frontend-module';
import { TaskFrontendModule } from '@theia/task/lib/browser/task-frontend-module';

// Plugin system
import { PluginExtFrontendModule } from '@theia/plugin-ext/lib/browser/plugin-ext-frontend-module';
import { PluginExtVSCodeFrontendModule } from '@theia/plugin-ext-vscode/lib/browser/plugin-ext-vscode-frontend-module';

// AI modules
import { AiCoreFrontendModule } from '@theia/ai-core/lib/browser/ai-core-frontend-module';
import { AiChatFrontendModule } from '@theia/ai-chat/lib/browser/ai-chat-frontend-module';
import { AiChatUiFrontendModule } from '@theia/ai-chat-ui/lib/browser/ai-chat-ui-frontend-module';
import { AiCodeCompletionFrontendModule } from '@theia/ai-code-completion/lib/browser/ai-code-completion-frontend-module';
import { AiEditorFrontendModule } from '@theia/ai-editor/lib/browser/ai-editor-frontend-module';
import { AiTerminalFrontendModule } from '@theia/ai-terminal/lib/browser/ai-terminal-frontend-module';

// Additional UI modules
import { ToolbarFrontendModule } from '@theia/toolbar/lib/browser/toolbar-frontend-module';
import { PropertyViewFrontendModule } from '@theia/property-view/lib/browser/property-view-frontend-module';
import { MessagesFrontendModule } from '@theia/messages/lib/browser/messages-frontend-module';
import { OutputFrontendModule } from '@theia/output/lib/browser/output-frontend-module';
import { MarkersFrontendModule } from '@theia/markers/lib/browser/markers-frontend-module';

interface TheiaIDEProps {
  className?: string;
}

export const TheiaIDE: React.FC<TheiaIDEProps> = ({ className = '' }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const applicationRef = useRef<FrontendApplication | null>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    const initializeTheia = async () => {
      try {
        // Create the main container
        const container = new Container();

        // Load all core modules
        container.load(
          FrontendApplicationModule,
          messagingFrontendModule,
          loggerFrontendModule,
          MenusFrontendModule,
          KeymapsFrontendModule,
          
          // Editor and Monaco
          MonacoFrontendModule,
          EditorFrontendModule,
          
          // File system and workspace
          FileSystemFrontendModule,
          WorkspaceFrontendModule,
          NavigatorFrontendModule,
          
          // Terminal
          TerminalFrontendModule,
          
          // UI components
          OutlineViewFrontendModule,
          PreferencesFrontendModule,
          ToolbarFrontendModule,
          PropertyViewFrontendModule,
          MessagesFrontendModule,
          OutputFrontendModule,
          MarkersFrontendModule,
          
          // Search functionality
          FileSearchFrontendModule,
          SearchInWorkspaceFrontendModule,
          
          // Version control
          ScmFrontendModule,
          
          // Debug and tasks
          DebugFrontendModule,
          TaskFrontendModule,
          
          // Plugin system
          PluginExtFrontendModule,
          PluginExtVSCodeFrontendModule,
          
          // AI features
          AiCoreFrontendModule,
          AiChatFrontendModule,
          AiChatUiFrontendModule,
          AiCodeCompletionFrontendModule,
          AiEditorFrontendModule,
          AiTerminalFrontendModule
        );

        // Custom configuration module
        const customModule = new ContainerModule(bind => {
          // Configure theme
          bind(ThemeService).toSelf().inSingletonScope();
          
          // Configure application state
          bind(FrontendApplicationStateService).toSelf().inSingletonScope();
        });

        container.load(customModule);

        // Get the application
        const application = container.get<FrontendApplication>(FrontendApplication);
        applicationRef.current = application;

        // Configure the application
        application.configure({
          applicationName: 'Theia IDE',
          defaultTheme: 'dark',
          defaultIconTheme: 'vs-seti'
        });

        // Start the application
        await application.start();

        // Attach to DOM
        if (containerRef.current) {
          containerRef.current.appendChild(application.shell.node);
          
          // Apply styling
          application.shell.node.style.width = '100%';
          application.shell.node.style.height = '100%';
          application.shell.node.style.overflow = 'hidden';
        }

        console.log('Theia IDE initialized successfully');

      } catch (error) {
        console.error('Failed to initialize Theia IDE:', error);
      }
    };

    initializeTheia();

    // Cleanup
    return () => {
      if (applicationRef.current) {
        applicationRef.current.stop();
      }
    };
  }, []);

  return (
    <div 
      ref={containerRef}
      className={`theia-ide-container ${className}`}
      style={{
        width: '100%',
        height: '100vh',
        overflow: 'hidden',
        background: '#1e1e1e',
        color: '#cccccc',
        fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace'
      }}
    />
  );
};

export default TheiaIDE;

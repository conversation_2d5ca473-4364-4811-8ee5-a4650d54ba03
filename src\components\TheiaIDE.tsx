import React, { useEffect, useRef, useState } from 'react';
import Editor from '@monaco-editor/react';

interface TheiaIDEProps {
  className?: string;
}

export const TheiaIDE: React.FC<TheiaIDEProps> = ({ className = '' }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const applicationRef = useRef<FrontendApplication | null>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    const initializeTheia = async () => {
      try {
        // Create the main container
        const container = new Container();

        // Load all core modules
        container.load(
          FrontendApplicationModule,
          messagingFrontendModule,
          loggerFrontendModule,
          MenusFrontendModule,
          KeymapsFrontendModule,
          
          // Editor and Monaco
          MonacoFrontendModule,
          EditorFrontendModule,
          
          // File system and workspace
          FileSystemFrontendModule,
          WorkspaceFrontendModule,
          NavigatorFrontendModule,
          
          // Terminal
          TerminalFrontendModule,
          
          // UI components
          OutlineViewFrontendModule,
          PreferencesFrontendModule,
          ToolbarFrontendModule,
          PropertyViewFrontendModule,
          MessagesFrontendModule,
          OutputFrontendModule,
          MarkersFrontendModule,
          
          // Search functionality
          FileSearchFrontendModule,
          SearchInWorkspaceFrontendModule,
          
          // Version control
          ScmFrontendModule,
          
          // Debug and tasks
          DebugFrontendModule,
          TaskFrontendModule,
          
          // Plugin system
          PluginExtFrontendModule,
          PluginExtVSCodeFrontendModule,
          
          // AI features
          AiCoreFrontendModule,
          AiChatFrontendModule,
          AiChatUiFrontendModule,
          AiCodeCompletionFrontendModule,
          AiEditorFrontendModule,
          AiTerminalFrontendModule
        );

        // Custom configuration module
        const customModule = new ContainerModule(bind => {
          // Configure theme
          bind(ThemeService).toSelf().inSingletonScope();
          
          // Configure application state
          bind(FrontendApplicationStateService).toSelf().inSingletonScope();
        });

        container.load(customModule);

        // Get the application
        const application = container.get<FrontendApplication>(FrontendApplication);
        applicationRef.current = application;

        // Configure the application
        application.configure({
          applicationName: 'Theia IDE',
          defaultTheme: 'dark',
          defaultIconTheme: 'vs-seti'
        });

        // Start the application
        await application.start();

        // Attach to DOM
        if (containerRef.current) {
          containerRef.current.appendChild(application.shell.node);
          
          // Apply styling
          application.shell.node.style.width = '100%';
          application.shell.node.style.height = '100%';
          application.shell.node.style.overflow = 'hidden';
        }

        console.log('Theia IDE initialized successfully');

      } catch (error) {
        console.error('Failed to initialize Theia IDE:', error);
      }
    };

    initializeTheia();

    // Cleanup
    return () => {
      if (applicationRef.current) {
        applicationRef.current.stop();
      }
    };
  }, []);

  return (
    <div 
      ref={containerRef}
      className={`theia-ide-container ${className}`}
      style={{
        width: '100%',
        height: '100vh',
        overflow: 'hidden',
        background: '#1e1e1e',
        color: '#cccccc',
        fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace'
      }}
    />
  );
};

export default TheiaIDE;


import React from 'react';
import { Card } from '../../components/Card';

export interface Book {
    title: string;
    author: string;
    image: string;
}

interface BookCardProps {
    book: Book;
}

export const BookCard: React.FC<BookCardProps> = ({ book }) => {
  return (
    <Card className="p-0 overflow-hidden group">
      <img src={book.image} alt={book.title} className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"/>
      <div className="p-3">
        <h4 className="text-white font-semibold truncate">{book.title}</h4>
        <p className="text-xs text-gray-400">{book.author}</p>
      </div>
    </Card>
  );
};


import { GoogleGenAI, GenerateContentResponse, Type, Part } from '@google/genai';

const hasValidApiKey = process.env.GEMINI_API_KEY &&
  process.env.GEMINI_API_KEY !== 'your_actual_gemini_api_key_here' &&
  process.env.GEMINI_API_KEY !== 'PLACEHOLDER_API_KEY';

let ai: GoogleGenAI | null = null;

if (hasValidApiKey) {
  ai = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY! });
} else {
  console.warn('⚠️  No valid Gemini API key found. Using fallback mock data. Set GEMINI_API_KEY in .env.local to use real AI features.');
}

export async function generateText(prompt: string): Promise<string> {
  if (!ai) {
    // Fallback mock response for mood analysis
    return "System analysis indicates elevated stress levels detected. Recommend implementing relaxation protocols and scheduling wellness check-in within 2 hours.";
  }

  try {
    const response: GenerateContentResponse = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: prompt,
    });
    return response.text;
  } catch(e) {
    console.error("Error generating text from Gemini:", e);
    // Return fallback data instead of throwing error
    return "System analysis temporarily unavailable. Default wellness protocols active. Please check back later for detailed analysis.";
  }
}

export async function generateStructuredData<T>(prompt: string, schema: any): Promise<T> {
  if (!ai) {
    // Return fallback mock data for dashboard widgets
    const fallbackData = {
      contacts: [
        { name: "Dr. Aris Thorne", detail: "Chief Scientist", avatar: "aris" },
        { name: "Commander Zara Vex", detail: "Security Director", avatar: "zara" },
        { name: "Engineer Kai Nexus", detail: "Systems Architect", avatar: "kai" }
      ],
      safetyItems: [
        { label: "Firewall Active", color: "#00FFFF" },
        { label: "Quantum Encryption", color: "#00FFFF" },
        { label: "Bio-Scanner Online", color: "#3b82f6" },
        { label: "Sub-routine Anomaly", color: "#ef4444" },
        { label: "Neural Link Stable", color: "#3b82f6" },
        { label: "Core Temperature", color: "#3b82f6" }
      ],
      safetyChartData: [85, 92, 78, 95, 88, 91],
      systemRequests: [
        { text: "Security Patch Update" },
        { text: "Cryo-chamber diagnostics" },
        { text: "Neural interface calibration" }
      ],
      systemStats: [
        { icon: "BuildingOfficeIcon", value: "1597", label: "Processes" },
        { icon: "PuzzlePieceIcon", value: "84%", label: "RAM Usage" },
        { icon: "SnowflakeIcon", value: "24°C", label: "Core Temp" },
        { icon: "MemoryChipIcon", value: "2.4TB", label: "Storage" },
        { icon: "CogIcon", value: "99.7%", label: "Uptime" }
      ]
    };
    return fallbackData as T;
  }

  try {
    const response: GenerateContentResponse = await ai.models.generateContent({
      model: "gemini-2.5-flash",
      contents: prompt,
      config: {
        responseMimeType: "application/json",
        responseSchema: schema,
      },
    });

    if (!response.text) {
        throw new Error("Received an empty response from the AI.");
    }

    const cleanedText = response.text.trim().replace(/^```json\s*|```\s*$/g, '');

    if (!cleanedText) {
        throw new Error("Received an empty JSON response from the AI after cleaning.");
    }

    return JSON.parse(cleanedText) as T;
  } catch (e) {
    console.error("Failed to generate or parse Gemini JSON response:", e);

    // Return fallback data instead of throwing error
    const fallbackData = {
      contacts: [
        { name: "Dr. Aris Thorne", detail: "Chief Scientist", avatar: "aris" },
        { name: "Commander Zara Vex", detail: "Security Director", avatar: "zara" },
        { name: "Engineer Kai Nexus", detail: "Systems Architect", avatar: "kai" }
      ],
      safetyItems: [
        { label: "Firewall Active", color: "#00FFFF" },
        { label: "Quantum Encryption", color: "#00FFFF" },
        { label: "Bio-Scanner Online", color: "#3b82f6" },
        { label: "Sub-routine Anomaly", color: "#ef4444" },
        { label: "Neural Link Stable", color: "#3b82f6" },
        { label: "Core Temperature", color: "#3b82f6" }
      ],
      safetyChartData: [85, 92, 78, 95, 88, 91],
      systemRequests: [
        { text: "Security Patch Update" },
        { text: "Cryo-chamber diagnostics" },
        { text: "Neural interface calibration" }
      ],
      systemStats: [
        { icon: "BuildingOfficeIcon", value: "1597", label: "Processes" },
        { icon: "PuzzlePieceIcon", value: "84%", label: "RAM Usage" },
        { icon: "SnowflakeIcon", value: "24°C", label: "Core Temp" },
        { icon: "MemoryChipIcon", value: "2.4TB", label: "Storage" },
        { icon: "CogIcon", value: "99.7%", label: "Uptime" }
      ]
    };
    return fallbackData as T;
  }
}

export { Type }; // Re-export Type for convenience

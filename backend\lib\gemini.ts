
import { GoogleGenAI, GenerateContentResponse, Type, Part } from '@google/genai';

if (!process.env.API_KEY) {
  throw new Error("API key not found. Please set the API_KEY environment variable.");
}

const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });

export async function generateText(prompt: string): Promise<string> {
  try {
    const response: GenerateContentResponse = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: prompt,
    });
    return response.text;
  } catch(e) {
    console.error("Error generating text from <PERSON>:", e);
    throw new Error("Failed to generate text from AI.");
  }
}

export async function generateStructuredData<T>(prompt: string, schema: any): Promise<T> {
  try {
    const response: GenerateContentResponse = await ai.models.generateContent({
      model: "gemini-2.5-flash",
      contents: prompt,
      config: {
        responseMimeType: "application/json",
        responseSchema: schema,
      },
    });

    if (!response.text) {
        throw new Error("Received an empty response from the AI.");
    }
    
    const cleanedText = response.text.trim().replace(/^```json\s*|```\s*$/g, '');

    if (!cleanedText) {
        throw new Error("Received an empty JSON response from the AI after cleaning.");
    }

    return JSON.parse(cleanedText) as T;
  } catch (e) {
    console.error("Failed to generate or parse Gemini JSON response:", e);
     if (e instanceof Error && e.message.startsWith("Received an empty")) {
        throw e;
    }
    throw new Error("Failed to process structured data from AI.");
  }
}

export { Type }; // Re-export Type for convenience

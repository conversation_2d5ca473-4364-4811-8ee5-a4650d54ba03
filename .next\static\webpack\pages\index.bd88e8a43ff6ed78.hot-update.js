"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _src_components_Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../src/components/Sidebar */ \"./src/components/Sidebar.tsx\");\n/* harmony import */ var _src_components_Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../src/components/Header */ \"./src/components/Header.tsx\");\n/* harmony import */ var _src_components_Footer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../src/components/Footer */ \"./src/components/Footer.tsx\");\n/* harmony import */ var _src_components_RightSidebar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../src/components/RightSidebar */ \"./src/components/RightSidebar.tsx\");\n/* harmony import */ var _src_views_School_SchoolView__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../src/views/School/SchoolView */ \"./src/views/School/SchoolView.tsx\");\n/* harmony import */ var _src_components_BackButton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../src/components/BackButton */ \"./src/components/BackButton.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst LoadingComponent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-grow flex items-center justify-center text-cyan-400\",\n        children: \"Loading View...\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n        lineNumber: 12,\n        columnNumber: 32\n    }, undefined);\n_c = LoadingComponent;\n// Dynamically import views for code splitting and performance\nconst viewComponents = {\n    Dashboard: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Dashboard_DashboardView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Dashboard/DashboardView */ \"./src/views/Dashboard/DashboardView.tsx\")).then((mod)=>mod.DashboardView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Dashboard/DashboardView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    School: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/School/SchoolView */ \"./src/views/School/SchoolView.tsx\")).then((mod)=>mod.SchoolView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/School/SchoolView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Tool: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Tool_ToolView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Tool/ToolView */ \"./src/views/Tool/ToolView.tsx\")).then((mod)=>mod.ToolView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Tool/ToolView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Market: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Market_MarketView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Market/MarketView */ \"./src/views/Market/MarketView.tsx\")).then((mod)=>mod.MarketView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Market/MarketView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Bookstore: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Bookstore_BookstoreView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Bookstore/BookstoreView */ \"./src/views/Bookstore/BookstoreView.tsx\")).then((mod)=>mod.BookstoreView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Bookstore/BookstoreView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Concierge: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Concierge_ConciergeView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Concierge/ConciergeView */ \"./src/views/Concierge/ConciergeView.tsx\")).then((mod)=>mod.ConciergeView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Concierge/ConciergeView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Analytic: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Analytic_AnalyticView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Analytic/AnalyticView */ \"./src/views/Analytic/AnalyticView.tsx\")).then((mod)=>mod.AnalyticView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Analytic/AnalyticView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Setting: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Setting_SettingView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Setting/SettingView */ \"./src/views/Setting/SettingView.tsx\")).then((mod)=>mod.SettingView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Setting/SettingView\"\n            ]\n        },\n        loading: LoadingComponent\n    })\n};\nconst HomePage = ()=>{\n    _s();\n    const [activeView, setActiveView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Dashboard\");\n    const [activeDepartment, setActiveDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"school\");\n    const [activeSubSection, setActiveSubSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"dashboard\");\n    const [activeApp, setActiveApp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const ActiveComponent = viewComponents[activeView] || viewComponents.Dashboard;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Eyes Shield UI\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"A futuristic, high-tech dashboard UI with a glassmorphism theme, displaying various data visualizations and system statuses on a semi-transparent, glowing panel.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1.0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"main-background font-poppins text-[#E0E0E0] dashboard-auto-scale main-layout\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/60 backdrop-blur-xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 bg-container-bg content-area\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Sidebar__WEBPACK_IMPORTED_MODULE_4__.Sidebar, {\n                                activeView: activeView,\n                                setActiveView: (view)=>setActiveView(view),\n                                activeApp: activeApp,\n                                setActiveApp: setActiveApp\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"main-content\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"header-section\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Header__WEBPACK_IMPORTED_MODULE_5__.Header, {\n                                            showSchoolButtons: activeView === \"School\" && !activeApp,\n                                            activeDepartment: activeDepartment,\n                                            setActiveDepartment: setActiveDepartment,\n                                            activeSubSection: activeView === \"School\" ? activeSubSection : undefined\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"main-section\",\n                                        children: activeApp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-full relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_BackButton__WEBPACK_IMPORTED_MODULE_9__.BackButton, {\n                                                    onBack: ()=>setActiveApp(\"\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                activeApp === \"gamification\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-full flex items-center justify-center text-accent-blue text-2xl\",\n                                                    children: \"\\uD83C\\uDFAE Gamification App Coming Soon\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 52\n                                                }, undefined),\n                                                activeApp === \"coder\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-full flex items-center justify-center text-accent-blue text-2xl\",\n                                                    children: \"\\uD83D\\uDCBB Coder IDE Coming Soon\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                activeApp === \"media\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-full flex items-center justify-center text-accent-blue text-2xl\",\n                                                    children: \"\\uD83C\\uDFA5 Media Hub Coming Soon\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                activeApp === \"studio\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-full flex items-center justify-center text-accent-blue text-2xl\",\n                                                    children: \"\\uD83E\\uDDD1‍\\uD83C\\uDFA8 Studio Coming Soon\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 46\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 17\n                                        }, undefined) : activeView === \"School\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_views_School_SchoolView__WEBPACK_IMPORTED_MODULE_8__.SchoolView, {\n                                            activeDepartment: activeDepartment,\n                                            setActiveDepartment: setActiveDepartment,\n                                            activeSubSection: activeSubSection,\n                                            setActiveSubSection: setActiveSubSection\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveComponent, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"footer-section\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Footer__WEBPACK_IMPORTED_MODULE_6__.Footer, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_RightSidebar__WEBPACK_IMPORTED_MODULE_7__.RightSidebar, {\n                                activeView: activeView,\n                                setActiveView: (view)=>setActiveView(view)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(HomePage, \"vJKLUvkt3e+p8xjLwRpFw6LnEJM=\");\n_c1 = HomePage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HomePage);\nvar _c, _c1;\n$RefreshReg$(_c, \"LoadingComponent\");\n$RefreshReg$(_c1, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n"));

/***/ }),

/***/ "./src/components/BackButton.tsx":
/*!***************************************!*\
  !*** ./src/components/BackButton.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BackButton: function() { return /* binding */ BackButton; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst BackButton = (param)=>{\n    let { onBack, label = \"Back to Dashboard\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onBack,\n        className: \"fixed top-4 right-4 z-50 bg-glass-bg border border-glass-border backdrop-filter backdrop-blur-20 rounded-lg px-4 py-2 hover:bg-glass-bg-light transition-all duration-300 hover:scale-105 flex items-center gap-2\",\n        style: {\n            boxShadow: \"var(--glass-shadow)\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5 text-accent-blue\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M10 19l-7-7m0 0l7-7m-7 7h18\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\BackButton.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\BackButton.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-text-primary font-medium\",\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\BackButton.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\BackButton.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n_c = BackButton;\nvar _c;\n$RefreshReg$(_c, \"BackButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/BackButton.tsx\n"));

/***/ })

});
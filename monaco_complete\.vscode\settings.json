// Place your settings in this file to overwrite default and user settings.
{
	"editor.tabSize": 4,
	"editor.insertSpaces": false,
	"files.insertFinalNewline": true,
	"files.trimTrailingWhitespace": true,
	"search.exclude": {
		"**/node_modules": true,
		"**/dist": true,
		"**/out": true
	},
	"typescript.tsdk": "./node_modules/typescript/lib",
	"git.branchProtection": ["main", "release/*"],
	"git.branchProtectionPrompt": "alwaysCommitToNewBranch",
	"git.branchRandomName.enable": true
}

{"name": "helloworld", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "helloworld", "devDependencies": {"monaco-editor": "^0.32.0", "parcel": "^2.2.1"}}, "node_modules/@babel/code-frame": {"version": "7.16.7", "resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.16.7.tgz", "integrity": "sha512-iAXqUn8IIeBTNd72xsFlgaXHkMBMt6y4HJp1tIaK465CWLT/fG1aqB7ykr95gHHmlBdGbFeWWfyB4NJJ0nmeIg==", "dev": true, "dependencies": {"@babel/highlight": "^7.16.7"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.16.7", "resolved": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.16.7.tgz", "integrity": "sha512-hsEnFemeiW4D08A5gUAZxLBTXpZ39P+a+DGDsHw1yxqyQ/jzFEnxf5uTEGp+3bzAbNOxU1paTgYS4ECU/IgfDw==", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/highlight": {"version": "7.16.10", "resolved": "https://registry.npmjs.org/@babel/highlight/-/highlight-7.16.10.tgz", "integrity": "sha512-5FnTQLSLswEj6IkgVw5KusNUUFY9ZGqe/TRFnP/BKYHYgfh7tc+C7mwiy95/yNP7Dh9x580Vv8r7u7ZfTBFxdw==", "dev": true, "dependencies": {"@babel/helper-validator-identifier": "^7.16.7", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/highlight/node_modules/ansi-styles": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==", "dev": true, "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/highlight/node_modules/chalk": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "integrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==", "dev": true, "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/highlight/node_modules/color-convert": {"version": "1.9.3", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz", "integrity": "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==", "dev": true, "dependencies": {"color-name": "1.1.3"}}, "node_modules/@babel/highlight/node_modules/color-name": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=", "dev": true}, "node_modules/@babel/highlight/node_modules/has-flag": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/@babel/highlight/node_modules/supports-color": {"version": "5.5.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz", "integrity": "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==", "dev": true, "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.2.tgz", "integrity": "sha512-mh65xKQAzI6iBcFzwv28KVWSmCkdRBWoOh+bYQGW3+6OZvbbN3TqMGo5hqYxQniRcH9F2VZIoJCm4pa3BPDK/A==", "dev": true, "dependencies": {"@jridgewell/set-array": "^1.0.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.9"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz", "integrity": "sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w==", "dev": true, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.1.2.tgz", "integrity": "sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==", "dev": true, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/source-map": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.2.tgz", "integrity": "sha512-m7O9o2uR8k2ObDysZYzdfhb08VuEml5oWGiosa1VdaPZ/A6QyPkAJuwN0Q1lhULOf6B7MtQmHENS743hWtCrgw==", "dev": true, "dependencies": {"@jridgewell/gen-mapping": "^0.3.0", "@jridgewell/trace-mapping": "^0.3.9"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.4.14", "resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz", "integrity": "sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw==", "dev": true}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.14", "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.14.tgz", "integrity": "sha512-bJWEfQ9lPTvm3SneWwRFVLzrh6nhjwqw7TUFFBEMzwvg7t7PCDenf2lDwqo4NQXzdpgBXyFgDWnQA+2vkruksQ==", "dev": true, "dependencies": {"@jridgewell/resolve-uri": "^3.0.3", "@jridgewell/sourcemap-codec": "^1.4.10"}}, "node_modules/@parcel/bundler-default": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/bundler-default/-/bundler-default-2.4.0.tgz", "integrity": "sha512-RaXlxo0M51739Ko3bsOJpDBZlJ+cqkDoBTozNeSc65jS2TMBIBWLMapm8095qmty39OrgYNhzjgPiIlKDS/LWA==", "dev": true, "dependencies": {"@parcel/diagnostic": "2.4.0", "@parcel/hash": "2.4.0", "@parcel/plugin": "2.4.0", "@parcel/utils": "2.4.0", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/cache": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/cache/-/cache-2.4.0.tgz", "integrity": "sha512-oOudoAafrCAHQY0zkU7gVHG1pAGBUz9rht7Tx4WupTmAH0O0F5UnZs6XbjoBJaPHg+CYUXK7v9wQcrNA72E3GA==", "dev": true, "dependencies": {"@parcel/fs": "2.4.0", "@parcel/logger": "2.4.0", "@parcel/utils": "2.4.0", "lmdb": "2.2.4"}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "peerDependencies": {"@parcel/core": "^2.4.0"}}, "node_modules/@parcel/codeframe": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/codeframe/-/codeframe-2.4.0.tgz", "integrity": "sha512-PJ3W9Z0sjoS2CANyo50c+LEr9IRZrtu0WsVPSYZ5ZYRuSXrSa/6PcAlnkyDk2+hi7Od8ncT2bmDexl0Oar3Jyg==", "dev": true, "dependencies": {"chalk": "^4.1.0"}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/compressor-raw": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/compressor-raw/-/compressor-raw-2.4.0.tgz", "integrity": "sha512-ZErX14fTc0gKIgtnuqW7Clfln4dpXWfUaJQQIf5C3x/LkpUeEhdXeKntkvSxOddDk2JpIKDwqzAxEMZUnDo4Nw==", "dev": true, "dependencies": {"@parcel/plugin": "2.4.0"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/config-default": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/config-default/-/config-default-2.4.0.tgz", "integrity": "sha512-pFOPBXPO6HGqNWTLkcK5i8haMOrRgUouUhcWPGWDpN9IPUYFK2E/O1E/uyMjIA1mSL3FnazI+jJwZ45NhKPpIA==", "dev": true, "dependencies": {"@parcel/bundler-default": "2.4.0", "@parcel/compressor-raw": "2.4.0", "@parcel/namer-default": "2.4.0", "@parcel/optimizer-css": "2.4.0", "@parcel/optimizer-htmlnano": "2.4.0", "@parcel/optimizer-image": "2.4.0", "@parcel/optimizer-svgo": "2.4.0", "@parcel/optimizer-terser": "2.4.0", "@parcel/packager-css": "2.4.0", "@parcel/packager-html": "2.4.0", "@parcel/packager-js": "2.4.0", "@parcel/packager-raw": "2.4.0", "@parcel/packager-svg": "2.4.0", "@parcel/reporter-dev-server": "2.4.0", "@parcel/resolver-default": "2.4.0", "@parcel/runtime-browser-hmr": "2.4.0", "@parcel/runtime-js": "2.4.0", "@parcel/runtime-react-refresh": "2.4.0", "@parcel/runtime-service-worker": "2.4.0", "@parcel/transformer-babel": "2.4.0", "@parcel/transformer-css": "2.4.0", "@parcel/transformer-html": "2.4.0", "@parcel/transformer-image": "2.4.0", "@parcel/transformer-js": "2.4.0", "@parcel/transformer-json": "2.4.0", "@parcel/transformer-postcss": "2.4.0", "@parcel/transformer-posthtml": "2.4.0", "@parcel/transformer-raw": "2.4.0", "@parcel/transformer-react-refresh-wrap": "2.4.0", "@parcel/transformer-svg": "2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "peerDependencies": {"@parcel/core": "^2.4.0"}}, "node_modules/@parcel/core": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/core/-/core-2.4.0.tgz", "integrity": "sha512-<PERSON>WZ2UWtIuwDc3fgsKyyTLpNNPoG8Yk2L117ICWF/+cqY8z/wJHm2KwLbeplDeq524shav0GJ9O4CemP3JPx0Nw==", "dev": true, "dependencies": {"@parcel/cache": "2.4.0", "@parcel/diagnostic": "2.4.0", "@parcel/events": "2.4.0", "@parcel/fs": "2.4.0", "@parcel/graph": "2.4.0", "@parcel/hash": "2.4.0", "@parcel/logger": "2.4.0", "@parcel/package-manager": "2.4.0", "@parcel/plugin": "2.4.0", "@parcel/source-map": "^2.0.0", "@parcel/types": "2.4.0", "@parcel/utils": "2.4.0", "@parcel/workers": "2.4.0", "abortcontroller-polyfill": "^1.1.9", "base-x": "^3.0.8", "browserslist": "^4.6.6", "clone": "^2.1.1", "dotenv": "^7.0.0", "dotenv-expand": "^5.1.0", "json-source-map": "^0.6.1", "json5": "^2.2.0", "msgpackr": "^1.5.4", "nullthrows": "^1.1.1", "semver": "^5.7.1"}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/css": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/@parcel/css/-/css-1.7.3.tgz", "integrity": "sha512-rgdRX4Uk31EvzH/mUScL0wdXtkci3U5N1W2pgam+9S10vQy4uONhWBepZ1tUCjONHLacGXr1jp3LbG/HI7LiTw==", "dev": true, "dependencies": {"detect-libc": "^1.0.3"}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "optionalDependencies": {"@parcel/css-darwin-arm64": "1.7.3", "@parcel/css-darwin-x64": "1.7.3", "@parcel/css-linux-arm-gnueabihf": "1.7.3", "@parcel/css-linux-arm64-gnu": "1.7.3", "@parcel/css-linux-arm64-musl": "1.7.3", "@parcel/css-linux-x64-gnu": "1.7.3", "@parcel/css-linux-x64-musl": "1.7.3", "@parcel/css-win32-x64-msvc": "1.7.3"}}, "node_modules/@parcel/css-darwin-arm64": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/@parcel/css-darwin-arm64/-/css-darwin-arm64-1.7.3.tgz", "integrity": "sha512-m3HDY+Rh8HJxmLELKAvCpF59vLS7FWtgBODHxl8G9Jl2CnGtXpXvdpyeMxNsTE+2QuPC+a5QT7IeZAKb2Gjmxg==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["darwin"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/css-darwin-x64": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/@parcel/css-darwin-x64/-/css-darwin-x64-1.7.3.tgz", "integrity": "sha512-LuhweXKxVwrz/hjAOm9XNRMSL+p23px20nhSCASkyUP7Higaxza948W3TSQdoL3YyR+wQxQH8Yj+R/T8Tz3E3g==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["darwin"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/css-linux-arm-gnueabihf": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/@parcel/css-linux-arm-gnueabihf/-/css-linux-arm-gnueabihf-1.7.3.tgz", "integrity": "sha512-/pd9Em18zMvt7eDZAMpNBEwF7c4VPVhAtBOZ59ClFrsXCTDNYP7mSy0cwNgtLelCRZCGAQmZNBDNQPH7vO3rew==", "cpu": ["arm"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/css-linux-arm64-gnu": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/@parcel/css-linux-arm64-gnu/-/css-linux-arm64-gnu-1.7.3.tgz", "integrity": "sha512-5aKiEhQK40riO4iVKzRqISzgYK+7Z7i3e6JTSz+/BHuQyHEUaBe/RuJ8Z0BDQtFz0HmWQlrQCd+7hd0Xgd8vYQ==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/css-linux-arm64-musl": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/@parcel/css-linux-arm64-musl/-/css-linux-arm64-musl-1.7.3.tgz", "integrity": "sha512-Wf7/aIueDED2JqBMfZvzbBAFSaPmd3TR28bD2pmP7CI/jZnm9vHVKMdOLgt9NKSSSjdGrp+VM410CsrUM7xcOw==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/css-linux-x64-gnu": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/@parcel/css-linux-x64-gnu/-/css-linux-x64-gnu-1.7.3.tgz", "integrity": "sha512-0ZADbuFklUrHC1p2uPY4BPcN07jUTMqJzr/SSdnGN2XiXgiVZGcDCMHUj0DvC9Vwy11DDM6Rnw4QBbKHG+QGjQ==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/css-linux-x64-musl": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/@parcel/css-linux-x64-musl/-/css-linux-x64-musl-1.7.3.tgz", "integrity": "sha512-mFWWM8lX2OIID81YQuDDt9zTqof0B7UcEcs0huE7Zbs60uLEEQupdf8iH0yh5EOhxPt3sRcQnGXf2QTrXdjIMA==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/css-win32-x64-msvc": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/@parcel/css-win32-x64-msvc/-/css-win32-x64-msvc-1.7.3.tgz", "integrity": "sha512-KUFEMQcoP7DG3QbsN21OxhjHkfQ1BARn7D9puX75bV5N1F1kv557aaLkQZiMsgiYOL4tmJvsdQXutG7x++3j4Q==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["win32"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/diagnostic": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/diagnostic/-/diagnostic-2.4.0.tgz", "integrity": "sha512-TjWO/b2zMFhub5ouwGjazMm7iAUvdmXBfWmjrg4TBhUbhoQwBnyWfvMDtAYo7PcvXfxVPgPZv86Nv6Ym5H6cHQ==", "dev": true, "dependencies": {"json-source-map": "^0.6.1", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/events": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/events/-/events-2.4.0.tgz", "integrity": "sha512-DEaEtFbhOhNAEmiXJ3MyF8Scq+sNDKiTyLax4lAC5/dpE5GvwfNnoD17C2+0gDuuDpdQkdHfXfvr50aYFt7jcw==", "dev": true, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/fs": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/fs/-/fs-2.4.0.tgz", "integrity": "sha512-CnUlWGUJ52SJVQi8QnaAPPQZOADmHMV9D9aX9GLcDm5XLT3Em7vmesG4bNLdMLwzYuzAtenhcWmuRCACuYztHw==", "dev": true, "dependencies": {"@parcel/fs-search": "2.4.0", "@parcel/types": "2.4.0", "@parcel/utils": "2.4.0", "@parcel/watcher": "^2.0.0", "@parcel/workers": "2.4.0"}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "peerDependencies": {"@parcel/core": "^2.4.0"}}, "node_modules/@parcel/fs-search": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/fs-search/-/fs-search-2.4.0.tgz", "integrity": "sha512-W/Vu6wbZk4wuB6AVdMkyymwh/S8Peed/PgJgSsApYD6lSTD315I6OuEdxZh3lWY+dqQdog/NJ7dvi/hdpH/Iqw==", "dev": true, "dependencies": {"detect-libc": "^1.0.3"}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/graph": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/graph/-/graph-2.4.0.tgz", "integrity": "sha512-5TZIAfDITkJCzgH4j4OQhnIvjV9IFwWqNBJanRl5QQTmKvdcODS3WbnK1SOJ+ZltcLVXMB+HNXmL0bX0tVolcw==", "dev": true, "dependencies": {"@parcel/utils": "2.4.0", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/hash": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/hash/-/hash-2.4.0.tgz", "integrity": "sha512-nB+wYNUhe6+G8M7vQhdeFXtpYJYwJgBHOPZ7Hd9O2jdlamWjDbw0t/u1dJbYvGJ8ZDtLDwiItawQVpuVdskQ9g==", "dev": true, "dependencies": {"detect-libc": "^1.0.3", "xxhash-wasm": "^0.4.2"}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/logger": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/logger/-/logger-2.4.0.tgz", "integrity": "sha512-DqfU0Zcs/0a7VBk+MsjJ80C66w4kM9EbkO3G12NIyEjNeG50ayW2CE9rUuJ91JaM9j0NFM1P82eyLpQPFFaVPw==", "dev": true, "dependencies": {"@parcel/diagnostic": "2.4.0", "@parcel/events": "2.4.0"}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/markdown-ansi": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/markdown-ansi/-/markdown-ansi-2.4.0.tgz", "integrity": "sha512-gPUP1xikxHiu2kFyPy35pfuVkFgAmcywO8YDQj7iYcB+k7l4QPpIYFYGXn2QADV4faf66ncMeTD4uYV8c0GqjQ==", "dev": true, "dependencies": {"chalk": "^4.1.0"}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/namer-default": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/namer-default/-/namer-default-2.4.0.tgz", "integrity": "sha512-DfL+Gx0Tyoa0vsgRpNybXjuKbWNw8MTVpy7Dk7r0btfVsn1jy3SSwlxH4USf76gb00/pK6XBsMp9zn7Z8ePREQ==", "dev": true, "dependencies": {"@parcel/diagnostic": "2.4.0", "@parcel/plugin": "2.4.0", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/node-resolver-core": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/node-resolver-core/-/node-resolver-core-2.4.0.tgz", "integrity": "sha512-qiN97XcfW2fYNoYuVEhNKuVPEJKj5ONQl0fqr/NEMmYvWz3bVKjgiXNJwW558elZvCI08gEbdxgyThpuFFQeKQ==", "dev": true, "dependencies": {"@parcel/diagnostic": "2.4.0", "@parcel/utils": "2.4.0", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/optimizer-css": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/optimizer-css/-/optimizer-css-2.4.0.tgz", "integrity": "sha512-LQmjjOGsHEHKTJqfHR2eJyhWhLXvHP0uOAU+qopBttYYlB2J/vMK9RYAye5cyAb8bQmV8wAdi2mq9rnt7FMSPw==", "dev": true, "dependencies": {"@parcel/css": "^1.7.2", "@parcel/diagnostic": "2.4.0", "@parcel/plugin": "2.4.0", "@parcel/source-map": "^2.0.0", "@parcel/utils": "2.4.0", "browserslist": "^4.6.6", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/optimizer-htmlnano": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/optimizer-htmlnano/-/optimizer-htmlnano-2.4.0.tgz", "integrity": "sha512-02EbeElLgNOAYhGU7fFBahpoKrX5G/yzahpaoKB/ypScM4roSsAMBkGcluboR5L10YRsvfvJEpxvfGyDA3tPmw==", "dev": true, "dependencies": {"@parcel/plugin": "2.4.0", "htmlnano": "^2.0.0", "nullthrows": "^1.1.1", "posthtml": "^0.16.5", "svgo": "^2.4.0"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/optimizer-image": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/optimizer-image/-/optimizer-image-2.4.0.tgz", "integrity": "sha512-Q4onaBMPkDyYxPzrb8ytBUftaQZFepj9dSUgq+ETuHDzkgia0tomDPfCqrw6ld0qvYyANzXTP5+LC4g0i5yh+A==", "dev": true, "dependencies": {"@parcel/diagnostic": "2.4.0", "@parcel/plugin": "2.4.0", "@parcel/utils": "2.4.0", "@parcel/workers": "2.4.0", "detect-libc": "^1.0.3"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/optimizer-svgo": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/optimizer-svgo/-/optimizer-svgo-2.4.0.tgz", "integrity": "sha512-mwvGuCqVuNCAuMlp2maFE/Uz9ud1T1AuX0f6cCRczjFYiwZuIr/0iDdfFzSziOkVo1MRAGAZNa0dRR/UzCZtVg==", "dev": true, "dependencies": {"@parcel/diagnostic": "2.4.0", "@parcel/plugin": "2.4.0", "@parcel/utils": "2.4.0", "svgo": "^2.4.0"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/optimizer-terser": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/optimizer-terser/-/optimizer-terser-2.4.0.tgz", "integrity": "sha512-PdCgRgXNSY6R1HTV9VG2MHp1CgUbP5pslCyxvlbUmQAS6bvEpMOpn3qSd+U28o7mGE/qXIhvpDyi808sb+MEcg==", "dev": true, "dependencies": {"@parcel/diagnostic": "2.4.0", "@parcel/plugin": "2.4.0", "@parcel/source-map": "^2.0.0", "@parcel/utils": "2.4.0", "nullthrows": "^1.1.1", "terser": "^5.2.0"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/package-manager": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/package-manager/-/package-manager-2.4.0.tgz", "integrity": "sha512-21AEfAQnZbHRVViTn7QsPGe/CiGaFaDUH5f0m8qVC7fDjjhC8LM8blkqU72goaO9FbaLMadtEf2txhzly7h/bg==", "dev": true, "dependencies": {"@parcel/diagnostic": "2.4.0", "@parcel/fs": "2.4.0", "@parcel/logger": "2.4.0", "@parcel/types": "2.4.0", "@parcel/utils": "2.4.0", "@parcel/workers": "2.4.0", "semver": "^5.7.1"}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "peerDependencies": {"@parcel/core": "^2.4.0"}}, "node_modules/@parcel/packager-css": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/packager-css/-/packager-css-2.4.0.tgz", "integrity": "sha512-LmPDWzkXi60Oy3WrPF0jPKQxeTwW5hmNBgrcXJMHSu+VcXdaQZNzNxVzhnZkJUbDd2z9vAUrUGzdLh8TquC8iQ==", "dev": true, "dependencies": {"@parcel/plugin": "2.4.0", "@parcel/source-map": "^2.0.0", "@parcel/utils": "2.4.0", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/packager-html": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/packager-html/-/packager-html-2.4.0.tgz", "integrity": "sha512-OPMIQ1uHYQFpRPrsmm5BqONbAyzjlhVsPRAzHlcBrglG4BTUeOR2ow4MUKblHmVVqc3QHnfZG4nHHtFkeuNQ3A==", "dev": true, "dependencies": {"@parcel/plugin": "2.4.0", "@parcel/types": "2.4.0", "@parcel/utils": "2.4.0", "nullthrows": "^1.1.1", "posthtml": "^0.16.5"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/packager-js": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/packager-js/-/packager-js-2.4.0.tgz", "integrity": "sha512-cfslIH43CJFgBS9PmdFaSnbInMCoejsFCnxtJa2GeUpjCXSfelPRp0OPx7m8n+fap4czftPhoxBALeDUElOZGQ==", "dev": true, "dependencies": {"@parcel/diagnostic": "2.4.0", "@parcel/hash": "2.4.0", "@parcel/plugin": "2.4.0", "@parcel/source-map": "^2.0.0", "@parcel/utils": "2.4.0", "globals": "^13.2.0", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/packager-raw": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/packager-raw/-/packager-raw-2.4.0.tgz", "integrity": "sha512-SFfw7chMFITj3J26ZVDJxbO6xwtPFcFBm1js8cwWMgzwuwS6CEc43k5+Abj+2/EqHU9kNJU9eWV5vT6lQwf3HA==", "dev": true, "dependencies": {"@parcel/plugin": "2.4.0"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/packager-svg": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/packager-svg/-/packager-svg-2.4.0.tgz", "integrity": "sha512-DwkgrdLEQop+tu9Ocr1ZaadmpsbSgVruJPr80xq1LaB0Jiwrl9HjHStMNH1laNFueK1yydxhnj9C2JQfW28qag==", "dev": true, "dependencies": {"@parcel/plugin": "2.4.0", "@parcel/types": "2.4.0", "@parcel/utils": "2.4.0", "posthtml": "^0.16.4"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/plugin": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/plugin/-/plugin-2.4.0.tgz", "integrity": "sha512-ehFUAL2+h27Lv+cYbbXA74UGy8C+eglUjcpvASOOjVRFuD6poMAMliKkKAXBhQaFx/Rvhz27A2PIPv9lL2i4UQ==", "dev": true, "dependencies": {"@parcel/types": "2.4.0"}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/reporter-cli": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/reporter-cli/-/reporter-cli-2.4.0.tgz", "integrity": "sha512-Q9bIFMaGvQgypCDxdMEKOwrJzIHAXScKkuFsqTHnUL6mmH3Mo2CoEGAq/wpMXuPhXRn1dPJcHgTNDwZ2fSzz0A==", "dev": true, "dependencies": {"@parcel/plugin": "2.4.0", "@parcel/types": "2.4.0", "@parcel/utils": "2.4.0", "chalk": "^4.1.0", "term-size": "^2.2.1"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/reporter-dev-server": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/reporter-dev-server/-/reporter-dev-server-2.4.0.tgz", "integrity": "sha512-24h++wevs7XYuX4dKa4PUfLSstvn3g7udajFv6CeQoME+dR25RL/wH/2LUbhV5ilgXXab76rWIndSqp78xHxPA==", "dev": true, "dependencies": {"@parcel/plugin": "2.4.0", "@parcel/utils": "2.4.0"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/resolver-default": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/resolver-default/-/resolver-default-2.4.0.tgz", "integrity": "sha512-K7pIIFmGm1hjg/7Mzkg99i8tfCClKfBUTuc2R5j8cdr2n0mCAi4/f2mFf5svLrb5XZrnDgoQ05tHKklLEfUDUw==", "dev": true, "dependencies": {"@parcel/node-resolver-core": "2.4.0", "@parcel/plugin": "2.4.0"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/runtime-browser-hmr": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/runtime-browser-hmr/-/runtime-browser-hmr-2.4.0.tgz", "integrity": "sha512-swPFtvxGoCA9LEjU/pHPNjxG1l0fte8447zXwRN/AaYrtjNu9Ww117OSKCyvCnE143E79jZOFStodTQGFuH+9A==", "dev": true, "dependencies": {"@parcel/plugin": "2.4.0", "@parcel/utils": "2.4.0"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/runtime-js": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/runtime-js/-/runtime-js-2.4.0.tgz", "integrity": "sha512-67OOvmkDdtmgzZVP/EyAzoXhJ/Ug3LUVUt7idg9arun5rdJptqEb3Um3wmH0zjcNa9jMbJt7Kl5x1wA8dJgPYg==", "dev": true, "dependencies": {"@parcel/plugin": "2.4.0", "@parcel/utils": "2.4.0", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/runtime-react-refresh": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/runtime-react-refresh/-/runtime-react-refresh-2.4.0.tgz", "integrity": "sha512-flnr+bf06lMZPbXZZLLaFNrPHvYpfuXTVovEghyUW46qLVpaHj33dpsU/LqZplIuHgBp2ibgrKhr/hY9ell68w==", "dev": true, "dependencies": {"@parcel/plugin": "2.4.0", "@parcel/utils": "2.4.0", "react-refresh": "^0.9.0"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/runtime-service-worker": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/runtime-service-worker/-/runtime-service-worker-2.4.0.tgz", "integrity": "sha512-RgM5QUqW22WzstW03CtV+Oih8VGVuwsf94Cc4hLouU2EAD0NUJgATWbFocZVTZIBTKELAWh2gjpSQDdnL4Ur+A==", "dev": true, "dependencies": {"@parcel/plugin": "2.4.0", "@parcel/utils": "2.4.0", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/source-map": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/@parcel/source-map/-/source-map-2.0.2.tgz", "integrity": "sha512-NnUrPYLpYB6qyx2v6bcRPn/gVigmGG6M6xL8wIg/i0dP1GLkuY1nf+Hqdf63FzPTqqT7K3k6eE5yHPQVMO5jcA==", "dev": true, "dependencies": {"detect-libc": "^1.0.3"}, "engines": {"node": "^12.18.3 || >=14"}}, "node_modules/@parcel/transformer-babel": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/transformer-babel/-/transformer-babel-2.4.0.tgz", "integrity": "sha512-iWDa7KzJTMP3HNmrYxiYq/S6redk2qminx/9MwmKIN9jzm8mgts2Lj9lOg/t66YaDGky6JAvw4DhB2qW4ni6yQ==", "dev": true, "dependencies": {"@parcel/diagnostic": "2.4.0", "@parcel/plugin": "2.4.0", "@parcel/source-map": "^2.0.0", "@parcel/utils": "2.4.0", "browserslist": "^4.6.6", "json5": "^2.2.0", "nullthrows": "^1.1.1", "semver": "^5.7.0"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/transformer-css": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/transformer-css/-/transformer-css-2.4.0.tgz", "integrity": "sha512-D2u48LuiQsQvbknABE0wVKFp9r6yCgWrHKEP1J6EJ31c49nXGXDHrpHJJwqq9BvAs/124eBI5mSsehTJyFEMwg==", "dev": true, "dependencies": {"@parcel/css": "^1.7.2", "@parcel/diagnostic": "2.4.0", "@parcel/plugin": "2.4.0", "@parcel/source-map": "^2.0.0", "@parcel/utils": "2.4.0", "browserslist": "^4.6.6", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/transformer-html": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/transformer-html/-/transformer-html-2.4.0.tgz", "integrity": "sha512-2/8X/o5QaCNVPr4wkxLCUub7v/YVvVN2L5yCEcTatNeFhNg/2iz7P2ekfqOaoDCHWZEOBT1VTwPbdBt+TMM71Q==", "dev": true, "dependencies": {"@parcel/diagnostic": "2.4.0", "@parcel/hash": "2.4.0", "@parcel/plugin": "2.4.0", "nullthrows": "^1.1.1", "posthtml": "^0.16.5", "posthtml-parser": "^0.10.1", "posthtml-render": "^3.0.0", "semver": "^5.7.1"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/transformer-image": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/transformer-image/-/transformer-image-2.4.0.tgz", "integrity": "sha512-JZkQvGGoGiD0AVKLIbAYYUWxepMmUaWZ4XXx71MmS/kA7cUDwTZ0CXq63YnSY1m+DX+ClTuTN8mBlwe2dkcGbA==", "dev": true, "dependencies": {"@parcel/plugin": "2.4.0", "@parcel/workers": "2.4.0", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}}, "node_modules/@parcel/transformer-js": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/transformer-js/-/transformer-js-2.4.0.tgz", "integrity": "sha512-eeLHFwv3jT3GmIxpLC7B8EXExGK0MFaK91HXljOMh6l8a+GlQYw27MSFQVtoXr0Olx9Uq2uvjXP1+zSsq3LQUQ==", "dev": true, "dependencies": {"@parcel/diagnostic": "2.4.0", "@parcel/plugin": "2.4.0", "@parcel/source-map": "^2.0.0", "@parcel/utils": "2.4.0", "@parcel/workers": "2.4.0", "@swc/helpers": "^0.3.6", "browserslist": "^4.6.6", "detect-libc": "^1.0.3", "nullthrows": "^1.1.1", "regenerator-runtime": "^0.13.7", "semver": "^5.7.1"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/transformer-json": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/transformer-json/-/transformer-json-2.4.0.tgz", "integrity": "sha512-3nR+d39mbURoXIypDfVCaxpwL65qMV+h8SLD78up2uhaRGklHQfN7GuemR7L+mcVAgNrmwVvZHhyNjdgYwWqqg==", "dev": true, "dependencies": {"@parcel/plugin": "2.4.0", "json5": "^2.2.0"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/transformer-postcss": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/transformer-postcss/-/transformer-postcss-2.4.0.tgz", "integrity": "sha512-ijIa2x+dbKnJhr7zO5WlXkvuj832fDoGksMBk2DX3u2WMrbh2rqVWPpGFsDhESx7EAy38nUoV/5KUdrNqUmCEA==", "dev": true, "dependencies": {"@parcel/diagnostic": "2.4.0", "@parcel/hash": "2.4.0", "@parcel/plugin": "2.4.0", "@parcel/utils": "2.4.0", "clone": "^2.1.1", "nullthrows": "^1.1.1", "postcss-value-parser": "^4.2.0", "semver": "^5.7.1"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/transformer-posthtml": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/transformer-posthtml/-/transformer-posthtml-2.4.0.tgz", "integrity": "sha512-xoL3AzgtVeRRAo6bh0AHAYm9bt1jZ+HiH86/7oARj/uJs6Wd8kXK/DZf6fH+F87hj4e7bnjmDDc0GPVK0lPz1w==", "dev": true, "dependencies": {"@parcel/plugin": "2.4.0", "@parcel/utils": "2.4.0", "nullthrows": "^1.1.1", "posthtml": "^0.16.5", "posthtml-parser": "^0.10.1", "posthtml-render": "^3.0.0", "semver": "^5.7.1"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/transformer-raw": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/transformer-raw/-/transformer-raw-2.4.0.tgz", "integrity": "sha512-fciFbNrzj0kLlDgr6OsI0PUv414rVygDWAsgbCCq4BexDkuemMs9f9FjMctx9B2VZlctE8dTT4RGkuQumTIpUg==", "dev": true, "dependencies": {"@parcel/plugin": "2.4.0"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/transformer-react-refresh-wrap": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/transformer-react-refresh-wrap/-/transformer-react-refresh-wrap-2.4.0.tgz", "integrity": "sha512-9+f6sGOWkf0jyUQ1CuFWk+04Mq3KTOCU9kRiwCHX1YdUCv5uki6r9XUSpqiYodrV+L6w9CCwLvGMLCDHxtCxMg==", "dev": true, "dependencies": {"@parcel/plugin": "2.4.0", "@parcel/utils": "2.4.0", "react-refresh": "^0.9.0"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/transformer-svg": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/transformer-svg/-/transformer-svg-2.4.0.tgz", "integrity": "sha512-D+yzVtSxtQML3d26fd/g4E/xYW68+OMbMUVLXORtoYMU42fnXQkJP6jGOdqy8Td+WORNY7EwVtQnESLwhBmolw==", "dev": true, "dependencies": {"@parcel/diagnostic": "2.4.0", "@parcel/hash": "2.4.0", "@parcel/plugin": "2.4.0", "nullthrows": "^1.1.1", "posthtml": "^0.16.5", "posthtml-parser": "^0.10.1", "posthtml-render": "^3.0.0", "semver": "^5.7.1"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/types": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/types/-/types-2.4.0.tgz", "integrity": "sha512-nysGIbBEnp+7R+tKTysdcTFOZDTCodsiXFeAhYQa5bhiOnG1l9gzhxQnE2OsdsgvMm40IOsgKprqvM/DbdLfnQ==", "dev": true, "dependencies": {"@parcel/cache": "2.4.0", "@parcel/diagnostic": "2.4.0", "@parcel/fs": "2.4.0", "@parcel/package-manager": "2.4.0", "@parcel/source-map": "^2.0.0", "@parcel/workers": "2.4.0", "utility-types": "^3.10.0"}}, "node_modules/@parcel/utils": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/utils/-/utils-2.4.0.tgz", "integrity": "sha512-sdNo+mZqDZT8LJYB6WWRKa4wFVZcK6Zb5Jh6Du76QvXXwHbPIQNZgJBb6gd/Rbk4GLOp2tW7MnBfq6zP9E9E2g==", "dev": true, "dependencies": {"@parcel/codeframe": "2.4.0", "@parcel/diagnostic": "2.4.0", "@parcel/hash": "2.4.0", "@parcel/logger": "2.4.0", "@parcel/markdown-ansi": "2.4.0", "@parcel/source-map": "^2.0.0", "chalk": "^4.1.0"}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.0.5.tgz", "integrity": "sha512-x0hUbjv891omnkcHD7ZOhiyyUqUUR6MNjq89JhEI3BxppeKWAm6NPQsqqRrAkCJBogdT/o/My21sXtTI9rJIsw==", "dev": true, "hasInstallScript": true, "dependencies": {"node-addon-api": "^3.2.1", "node-gyp-build": "^4.3.0"}, "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/workers": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/workers/-/workers-2.4.0.tgz", "integrity": "sha512-eSFyvEoXXPgFzQfKIlpkUjpHfIbezUCRFTPKyJAKCxvU5DSXOpb1kz5vDESWQ4qTZXKnrKvxS1PPWN6bam9z0g==", "dev": true, "dependencies": {"@parcel/diagnostic": "2.4.0", "@parcel/logger": "2.4.0", "@parcel/types": "2.4.0", "@parcel/utils": "2.4.0", "chrome-trace-event": "^1.0.2", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "peerDependencies": {"@parcel/core": "^2.4.0"}}, "node_modules/@swc/helpers": {"version": "0.3.8", "resolved": "https://registry.npmjs.org/@swc/helpers/-/helpers-0.3.8.tgz", "integrity": "sha512-aWItSZvJj4+GI6FWkjZR13xPNPctq2RRakzo+O6vN7bC2yjwdg5EFpgaSAUn95b7BGSgcflvzVDPoKmJv24IOg==", "dev": true}, "node_modules/@trysound/sax": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/@trysound/sax/-/sax-0.2.0.tgz", "integrity": "sha512-L7z9BgrNEcYyUYtF+HaEfiS5ebkh9jXqbszz7pC0hRBPaatV0XjSD3+eHrpqFemQfgwiFF0QPIarnIihIDn7OA==", "dev": true, "engines": {"node": ">=10.13.0"}}, "node_modules/@types/parse-json": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/@types/parse-json/-/parse-json-4.0.0.tgz", "integrity": "sha512-//oorEZjL6sbPcKUaCdIGlIUeH26mgzimjBB77G6XRgnDl/L5wOnpyBGRe/Mmf5CVW3PwEBE1NjiMZ/ssFh4wA==", "dev": true}, "node_modules/abortcontroller-polyfill": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/abortcontroller-polyfill/-/abortcontroller-polyfill-1.7.3.tgz", "integrity": "sha512-zetDJxd89y3X99Kvo4qFx8GKlt6GsvN3UcRZHwU6iFA/0KiOmhkTVhe8oRoTBiTVPZu09x3vCra47+w8Yz1+2Q==", "dev": true}, "node_modules/acorn": {"version": "8.7.0", "resolved": "https://registry.npmjs.org/acorn/-/acorn-8.7.0.tgz", "integrity": "sha512-V/LGr1APy+PXIwKebEWrkZPwoeoF+w1jiOBUmuxuiUIaOHtob8Qc9BTrYo7VuI5fR8tqsy+buA2WFooR5olqvQ==", "dev": true, "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "dev": true, "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/base-x": {"version": "3.0.9", "resolved": "https://registry.npmjs.org/base-x/-/base-x-3.0.9.tgz", "integrity": "sha512-H7JU6iBHTal1gp56aKoaa//YUxEaAOUiydvrV/pILqIHXTtqxSkATOnDA2u+jZ/61sD+L/412+7kzXRtWukhpQ==", "dev": true, "dependencies": {"safe-buffer": "^5.0.1"}}, "node_modules/boolbase": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz", "integrity": "sha1-aN/1++YMUes3cl6p4+0xDcwed24=", "dev": true}, "node_modules/browserslist": {"version": "4.20.2", "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.20.2.tgz", "integrity": "sha512-CQOBCqp/9pDvDbx3xfMi+86pr4KXIf2FDkTTdeuYw8OxS9t898LA1Khq57gtufFILXpfgsSx5woNgsBgvGjpsA==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}], "dependencies": {"caniuse-lite": "^1.0.30001317", "electron-to-chromium": "^1.4.84", "escalade": "^3.1.1", "node-releases": "^2.0.2", "picocolors": "^1.0.0"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/buffer-from": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz", "integrity": "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==", "dev": true}, "node_modules/callsites": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz", "integrity": "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==", "dev": true, "engines": {"node": ">=6"}}, "node_modules/caniuse-lite": {"version": "1.0.30001322", "resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001322.tgz", "integrity": "sha512-neRmrmIrCGuMnxGSoh+x7zYtQFFgnSY2jaomjU56sCkTA6JINqQrxutF459JpWcWRajvoyn95sOXq4Pqrnyjew==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}]}, "node_modules/chalk": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==", "dev": true, "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/chrome-trace-event": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/chrome-trace-event/-/chrome-trace-event-1.0.3.tgz", "integrity": "sha512-p3KULyQg4S7NIHixdwbGX+nFHkoBiA4YQmyWtjb8XngSKV124nJmRysgAeujbUVb15vh+RvFUfCPqU7rXk+hZg==", "dev": true, "engines": {"node": ">=6.0"}}, "node_modules/clone": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/clone/-/clone-2.1.2.tgz", "integrity": "sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18=", "dev": true, "engines": {"node": ">=0.8"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "dev": true, "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "dev": true}, "node_modules/commander": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/commander/-/commander-7.2.0.tgz", "integrity": "sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==", "dev": true, "engines": {"node": ">= 10"}}, "node_modules/cosmiconfig": {"version": "7.0.1", "resolved": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-7.0.1.tgz", "integrity": "sha512-a1YWNUV2HwGimB7dU2s1wUMurNKjpx60HxBB6xUM8Re+2s1g1IIfJvFR0/iCF+XHdE0GMTKTuLR32UQff4TEyQ==", "dev": true, "dependencies": {"@types/parse-json": "^4.0.0", "import-fresh": "^3.2.1", "parse-json": "^5.0.0", "path-type": "^4.0.0", "yaml": "^1.10.0"}, "engines": {"node": ">=10"}}, "node_modules/css-select": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/css-select/-/css-select-4.3.0.tgz", "integrity": "sha512-wPpOYtnsVontu2mODhA19JrqWxNsfdatRKd64kmpRbQgh1KtItko5sTnEpPdpSaJszTOhEMlF/RPz28qj4HqhQ==", "dev": true, "dependencies": {"boolbase": "^1.0.0", "css-what": "^6.0.1", "domhandler": "^4.3.1", "domutils": "^2.8.0", "nth-check": "^2.0.1"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/css-tree": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/css-tree/-/css-tree-1.1.3.tgz", "integrity": "sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q==", "dev": true, "dependencies": {"mdn-data": "2.0.14", "source-map": "^0.6.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/css-what": {"version": "6.0.1", "resolved": "https://registry.npmjs.org/css-what/-/css-what-6.0.1.tgz", "integrity": "sha512-z93ZGFLNc6yaoXAmVhqoSIb+BduplteCt1fepvwhBUQK6MNE4g6fgjpuZKJKp0esUe+vXWlIkwZZjNWoOKw0ZA==", "dev": true, "engines": {"node": ">= 6"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/csso": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/csso/-/csso-4.2.0.tgz", "integrity": "sha512-wvlcdIbf6pwKEk7vHj8/Bkc0B4ylXZruLvOgs9doS5eOsOpuodOV2zJChSpkp+pRpYQLQMeF04nr3Z68Sta9jA==", "dev": true, "dependencies": {"css-tree": "^1.1.2"}, "engines": {"node": ">=8.0.0"}}, "node_modules/detect-libc": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/detect-libc/-/detect-libc-1.0.3.tgz", "integrity": "sha1-+hN8S9aY7fVc1c0CrFWfkaTEups=", "dev": true, "bin": {"detect-libc": "bin/detect-libc.js"}, "engines": {"node": ">=0.10"}}, "node_modules/dom-serializer": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-1.3.2.tgz", "integrity": "sha512-5c54Bk5Dw4qAxNOI1pFEizPSjVsx5+bpJKmL2kPn8JhBUq2q09tTCa3mjijun2NfK78NMouDYNMBkOrPZiS+ig==", "dev": true, "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.2.0", "entities": "^2.0.0"}, "funding": {"url": "https://github.com/cheeriojs/dom-serializer?sponsor=1"}}, "node_modules/dom-serializer/node_modules/entities": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/entities/-/entities-2.2.0.tgz", "integrity": "sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A==", "dev": true, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/domelementtype": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/domelementtype/-/domelementtype-2.2.0.tgz", "integrity": "sha512-DtBMo82pv1dFtUmHyr48beiuq792Sxohr+8Hm9zoxklYPfa6n0Z3Byjj2IV7bmr2IyqClnqEQhfgHJJ5QF0R5A==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/fb55"}]}, "node_modules/domhandler": {"version": "4.3.1", "resolved": "https://registry.npmjs.org/domhandler/-/domhandler-4.3.1.tgz", "integrity": "sha512-GrwoxYN+uWlzO8uhUXRl0P+kHE4GtVPfYzVLcUxPL7KNdHKj66vvlhiweIHqYYXWlw+T8iLMp42Lm67ghw4WMQ==", "dev": true, "dependencies": {"domelementtype": "^2.2.0"}, "engines": {"node": ">= 4"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}}, "node_modules/domutils": {"version": "2.8.0", "resolved": "https://registry.npmjs.org/domutils/-/domutils-2.8.0.tgz", "integrity": "sha512-w96Cjofp72M5IIhpjgobBimYEfoPjx1Vx0BSX9P30WBdZW2WIKU0T1Bd0kz2eNZ9ikjKgHbEyKx8BB6H1L3h3A==", "dev": true, "dependencies": {"dom-serializer": "^1.0.1", "domelementtype": "^2.2.0", "domhandler": "^4.2.0"}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "node_modules/dotenv": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/dotenv/-/dotenv-7.0.0.tgz", "integrity": "sha512-M3NhsLbV1i6HuGzBUH8vXrtxOk+tWmzWKDMbAVSUp3Zsjm7ywFeuwrUXhmhQyRK1q5B5GGy7hcXPbj3bnfZg2g==", "dev": true, "engines": {"node": ">=6"}}, "node_modules/dotenv-expand": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/dotenv-expand/-/dotenv-expand-5.1.0.tgz", "integrity": "sha512-YXQl1DSa4/PQyRfgrv6aoNjhasp/p4qs9FjJ4q4cQk+8m4r6k4ZSiEyytKG8f8W9gi8WsQtIObNmKd+tMzNTmA==", "dev": true}, "node_modules/electron-to-chromium": {"version": "1.4.98", "resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.4.98.tgz", "integrity": "sha512-1IdsuSAnIGVxoYT1LkcUFb9MfjRxdHhCU9qiaDzhl1XvYgK9c8E2O9aJOPgGMQ68CSI8NxmLwrYhjvGauT8yuw==", "dev": true}, "node_modules/entities": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/entities/-/entities-3.0.1.tgz", "integrity": "sha512-WiyBqoomrwMdFG1e0kqvASYfnlb0lp8M5o5Fw2OFq1hNZxxcNk8Ik0Xm7LxzBhuidnZB/UtBqVCgUz3kBOP51Q==", "dev": true, "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/error-ex": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz", "integrity": "sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==", "dev": true, "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/escalade": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/escalade/-/escalade-3.1.1.tgz", "integrity": "sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==", "dev": true, "engines": {"node": ">=6"}}, "node_modules/escape-string-regexp": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=", "dev": true, "engines": {"node": ">=0.8.0"}}, "node_modules/get-port": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/get-port/-/get-port-4.2.0.tgz", "integrity": "sha512-/b3jarXkH8KJoOMQc3uVGHASwGLPq3gSFJ7tgJm2diza+bydJPTGOibin2steecKeOylE8oY2JERlVWkAJO6yw==", "dev": true, "engines": {"node": ">=6"}}, "node_modules/globals": {"version": "13.13.0", "resolved": "https://registry.npmjs.org/globals/-/globals-13.13.0.tgz", "integrity": "sha512-EQ7Q18AJlPwp3vUDL4mKA0KXrXyNIQyWon6T6XQiBQF0XHvRsiCSrWmmeATpUzdJN2HhWZU6Pdl0a9zdep5p6A==", "dev": true, "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/htmlnano": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/htmlnano/-/htmlnano-2.0.0.tgz", "integrity": "sha512-thKQfhcp2xgtsWNE27A2bliEeqVL5xjAgGn0wajyttvFFsvFWWah1ntV9aEX61gz0T6MBQ5xK/1lXuEumhJTcg==", "dev": true, "dependencies": {"cosmiconfig": "^7.0.1", "posthtml": "^0.16.5", "timsort": "^0.3.0"}, "peerDependencies": {"cssnano": "^5.0.11", "postcss": "^8.3.11", "purgecss": "^4.0.3", "relateurl": "^0.2.7", "srcset": "^5.0.0", "svgo": "^2.8.0", "terser": "^5.10.0", "uncss": "^0.17.3"}, "peerDependenciesMeta": {"cssnano": {"optional": true}, "postcss": {"optional": true}, "purgecss": {"optional": true}, "relateurl": {"optional": true}, "srcset": {"optional": true}, "svgo": {"optional": true}, "terser": {"optional": true}, "uncss": {"optional": true}}}, "node_modules/htmlparser2": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/htmlparser2/-/htmlparser2-7.2.0.tgz", "integrity": "sha512-H7MImA4MS6cw7nbyURtLPO1Tms7C5H602LRETv95z1MxO/7CP7rDVROehUYeYBUYEON94NXXDEPmZuq+hX4sog==", "dev": true, "funding": ["https://github.com/fb55/htmlparser2?sponsor=1", {"type": "github", "url": "https://github.com/sponsors/fb55"}], "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.2.2", "domutils": "^2.8.0", "entities": "^3.0.1"}}, "node_modules/import-fresh": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.0.tgz", "integrity": "sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==", "dev": true, "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-arrayish": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz", "integrity": "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=", "dev": true}, "node_modules/is-json": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/is-json/-/is-json-2.0.1.tgz", "integrity": "sha1-a+Fm0USCihMdaGiRuYPfYsOUkf8=", "dev": true}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==", "dev": true}, "node_modules/json-parse-even-better-errors": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "integrity": "sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==", "dev": true}, "node_modules/json-source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/json-source-map/-/json-source-map-0.6.1.tgz", "integrity": "sha512-1QoztHPsMQqhDq0hlXY5ZqcEdUzxQEIxgFkKl4WUp2pgShObl+9ovi4kRh2TfvAfxAoHOJ9vIMEqk3k4iex7tg==", "dev": true}, "node_modules/json5": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/json5/-/json5-2.2.1.tgz", "integrity": "sha512-1hqLFMSrGHRHxav9q9gNjJ5EXznIxGVO09xQRrwplcS8qs28pZ8s8hupZAmqDwZUmVZ2Qb2jnyPOWcDH8m8dlA==", "dev": true, "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/lines-and-columns": {"version": "1.2.4", "resolved": "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz", "integrity": "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==", "dev": true}, "node_modules/lmdb": {"version": "2.2.4", "resolved": "https://registry.npmjs.org/lmdb/-/lmdb-2.2.4.tgz", "integrity": "sha512-gto+BB2uEob8qRiTlOq+R3uX0YNHsX9mjxj9Sbdue/LIKqu6IlZjrsjKeGyOMquc/474GEqFyX2pdytpydp0rQ==", "dev": true, "hasInstallScript": true, "dependencies": {"msgpackr": "^1.5.4", "nan": "^2.14.2", "node-gyp-build": "^4.2.3", "ordered-binary": "^1.2.4", "weak-lru-cache": "^1.2.2"}}, "node_modules/mdn-data": {"version": "2.0.14", "resolved": "https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.14.tgz", "integrity": "sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow==", "dev": true}, "node_modules/monaco-editor": {"version": "0.32.1", "resolved": "https://registry.npmjs.org/monaco-editor/-/monaco-editor-0.32.1.tgz", "integrity": "sha512-LUt2wsUvQmEi2tfTOK+tjAPvt7eQ+K5C4rZPr6SeuyzjAuAHrIvlUloTcOiGjZW3fn3a/jFQCONrEJbNOaCqbA==", "dev": true}, "node_modules/msgpackr": {"version": "1.5.5", "resolved": "https://registry.npmjs.org/msgpackr/-/msgpackr-1.5.5.tgz", "integrity": "sha512-JG0V47xRIQ9pyUnx6Hb4+3TrQoia2nA3UIdmyTldhxaxtKFkekkKpUW/N6fwHwod9o4BGuJGtouxOk+yCP5PEA==", "dev": true, "optionalDependencies": {"msgpackr-extract": "^1.0.14"}}, "node_modules/msgpackr-extract": {"version": "1.0.16", "resolved": "https://registry.npmjs.org/msgpackr-extract/-/msgpackr-extract-1.0.16.tgz", "integrity": "sha512-fxdRfQUxPrL/TizyfYfMn09dK58e+d65bRD/fcaVH4052vj30QOzzqxcQIS7B0NsqlypEQ/6Du3QmP2DhWFfCA==", "dev": true, "hasInstallScript": true, "optional": true, "dependencies": {"nan": "^2.14.2", "node-gyp-build": "^4.2.3"}}, "node_modules/nan": {"version": "2.15.0", "resolved": "https://registry.npmjs.org/nan/-/nan-2.15.0.tgz", "integrity": "sha512-8ZtvEnA2c5aYCZYd1cvgdnU6cqwixRoYg70xPLWUws5ORTa/lnw+u4amixRS/Ac5U5mQVgp9pnlSUnbNWFaWZQ==", "dev": true}, "node_modules/node-addon-api": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-3.2.1.tgz", "integrity": "sha512-mmcei9JghVNDYydghQmeDX8KoAm0FAiYyIcUt/N4nhyAipB17pllZQDOJD2fotxABnt4Mdz+dKTO7eftLg4d0A==", "dev": true}, "node_modules/node-gyp-build": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/node-gyp-build/-/node-gyp-build-4.3.0.tgz", "integrity": "sha512-iWjXZvmboq0ja1pUGULQBexmxq8CV4xBhX7VDOTbL7ZR4FOowwY/VOtRxBN/yKxmdGoIp4j5ysNT4u3S2pDQ3Q==", "dev": true, "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}}, "node_modules/node-releases": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.2.tgz", "integrity": "sha512-XxYDdcQ6eKqp/YjI+tb2C5WM2LgjnZrfYg4vgQt49EK268b6gYCHsBLrK2qvJo4FmCtqmKezb0WZFK4fkrZNsg==", "dev": true}, "node_modules/nth-check": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/nth-check/-/nth-check-2.0.1.tgz", "integrity": "sha512-it1vE95zF6dTT9lBsYbxvqh0Soy4SPowchj0UBGj/V6cTPnXXtQOPUbhZ6CmGzAD/rW22LQK6E96pcdJXk4A4w==", "dev": true, "dependencies": {"boolbase": "^1.0.0"}, "funding": {"url": "https://github.com/fb55/nth-check?sponsor=1"}}, "node_modules/nullthrows": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/nullthrows/-/nullthrows-1.1.1.tgz", "integrity": "sha512-2vPPEi+Z7WqML2jZYddDIfy5Dqb0r2fze2zTxNNknZaFpVHU3mFB3R+DWeJWGVx0ecvttSGlJTI+WG+8Z4cDWw==", "dev": true}, "node_modules/ordered-binary": {"version": "1.2.4", "resolved": "https://registry.npmjs.org/ordered-binary/-/ordered-binary-1.2.4.tgz", "integrity": "sha512-A/csN0d3n+igxBPfUrjbV5GC69LWj2pjZzAAeeHXLukQ4+fytfP4T1Lg0ju7MSPSwq7KtHkGaiwO8URZN5IpLg==", "dev": true}, "node_modules/parcel": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/parcel/-/parcel-2.4.0.tgz", "integrity": "sha512-dPWpu4RnxG9HqiLvaF8COEWEnT/KrigrC6PyPaQ0zEgpBfp7/jzXZFBVaZk2N+lpvrbNEYMjN9bv5UQGJJszIw==", "dev": true, "dependencies": {"@parcel/config-default": "2.4.0", "@parcel/core": "2.4.0", "@parcel/diagnostic": "2.4.0", "@parcel/events": "2.4.0", "@parcel/fs": "2.4.0", "@parcel/logger": "2.4.0", "@parcel/package-manager": "2.4.0", "@parcel/reporter-cli": "2.4.0", "@parcel/reporter-dev-server": "2.4.0", "@parcel/utils": "2.4.0", "chalk": "^4.1.0", "commander": "^7.0.0", "get-port": "^4.2.0", "v8-compile-cache": "^2.0.0"}, "bin": {"parcel": "lib/bin.js"}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/parent-module": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz", "integrity": "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==", "dev": true, "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/parse-json": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz", "integrity": "sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==", "dev": true, "dependencies": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/path-type": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz", "integrity": "sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/picocolors": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/picocolors/-/picocolors-1.0.0.tgz", "integrity": "sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==", "dev": true}, "node_modules/postcss-value-parser": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "integrity": "sha512-1N<PERSON>s6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==", "dev": true}, "node_modules/posthtml": {"version": "0.16.6", "resolved": "https://registry.npmjs.org/posthtml/-/posthtml-0.16.6.tgz", "integrity": "sha512-JcEmHlyLK/o0uGAlj65vgg+7LIms0xKXe60lcDOTU7oVX/3LuEuLwrQpW3VJ7de5TaFKiW4kWkaIpJL42FEgxQ==", "dev": true, "dependencies": {"posthtml-parser": "^0.11.0", "posthtml-render": "^3.0.0"}, "engines": {"node": ">=12.0.0"}}, "node_modules/posthtml-parser": {"version": "0.10.2", "resolved": "https://registry.npmjs.org/posthtml-parser/-/posthtml-parser-0.10.2.tgz", "integrity": "sha512-PId6zZ/2lyJi9LiKfe+i2xv57oEjJgWbsHGGANwos5AvdQp98i6AtamAl8gzSVFGfQ43Glb5D614cvZf012VKg==", "dev": true, "dependencies": {"htmlparser2": "^7.1.1"}, "engines": {"node": ">=12"}}, "node_modules/posthtml-render": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/posthtml-render/-/posthtml-render-3.0.0.tgz", "integrity": "sha512-z+16RoxK3fUPgwaIgH9NGnK1HKY9XIDpydky5eQGgAFVXTCSezalv9U2jQuNV+Z9qV1fDWNzldcw4eK0SSbqKA==", "dev": true, "dependencies": {"is-json": "^2.0.1"}, "engines": {"node": ">=12"}}, "node_modules/posthtml/node_modules/posthtml-parser": {"version": "0.11.0", "resolved": "https://registry.npmjs.org/posthtml-parser/-/posthtml-parser-0.11.0.tgz", "integrity": "sha512-QecJtfLekJbWVo/dMAA+OSwY79wpRmbqS5TeXvXSX+f0c6pW4/SE6inzZ2qkU7oAMCPqIDkZDvd/bQsSFUnKyw==", "dev": true, "dependencies": {"htmlparser2": "^7.1.1"}, "engines": {"node": ">=12"}}, "node_modules/react-refresh": {"version": "0.9.0", "resolved": "https://registry.npmjs.org/react-refresh/-/react-refresh-0.9.0.tgz", "integrity": "sha512-Gvzk7OZpiqKSkxsQvO/mbTN1poglhmAV7gR/DdIrRrSMXraRQQlfikRJOr3Nb9GTMPC5kof948Zy6jJZIFtDvQ==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/regenerator-runtime": {"version": "0.13.9", "resolved": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.9.tgz", "integrity": "sha512-p3VT+cOEgxFsRRA9X4lkI1E+k2/CtnKtU4gcxyaCUreilL/vqI6CdZ3wxVUx3UOUg+gnUOQQcRI7BmSI656MYA==", "dev": true}, "node_modules/resolve-from": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz", "integrity": "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==", "dev": true, "engines": {"node": ">=4"}}, "node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/semver": {"version": "5.7.1", "resolved": "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz", "integrity": "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ==", "dev": true, "bin": {"semver": "bin/semver"}}, "node_modules/source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-support": {"version": "0.5.21", "resolved": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz", "integrity": "sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==", "dev": true, "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/stable": {"version": "0.1.8", "resolved": "https://registry.npmjs.org/stable/-/stable-0.1.8.tgz", "integrity": "sha512-ji9qxRnOVfcuLDySj9qzhGSEFVobyt1kIOSkj1qZzYLzq7Tos/oUUWvotUPQLlrsidqsK6tBH89Bc9kL5zHA6w==", "dev": true}, "node_modules/supports-color": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "dev": true, "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/svgo": {"version": "2.8.0", "resolved": "https://registry.npmjs.org/svgo/-/svgo-2.8.0.tgz", "integrity": "sha512-+N/Q9kV1+F+UeWYoSiULYo4xYSDQlTgb+ayMobAXPwMnLvop7oxKMo9OzIrX5x3eS4L4f2UHhc9axXwY8DpChg==", "dev": true, "dependencies": {"@trysound/sax": "0.2.0", "commander": "^7.2.0", "css-select": "^4.1.3", "css-tree": "^1.1.3", "csso": "^4.2.0", "picocolors": "^1.0.0", "stable": "^0.1.8"}, "bin": {"svgo": "bin/svgo"}, "engines": {"node": ">=10.13.0"}}, "node_modules/term-size": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/term-size/-/term-size-2.2.1.tgz", "integrity": "sha512-wK0Ri4fOGjv/XPy8SBHZChl8CM7uMc5VML7SqiQ0zG7+J5Vr+RMQDoHa2CNT6KHUnTGIXH34UDMkPzAUyapBZg==", "dev": true, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/terser": {"version": "5.14.2", "resolved": "https://registry.npmjs.org/terser/-/terser-5.14.2.tgz", "integrity": "sha512-oL0rGeM/WFQCUd0y2QrWxYnq7tfSuKBiqTjRPWrRgB46WD/kiwHwF8T23z78H6Q6kGCuuHcPB+KULHRdxvVGQA==", "dev": true, "dependencies": {"@jridgewell/source-map": "^0.3.2", "acorn": "^8.5.0", "commander": "^2.20.0", "source-map-support": "~0.5.20"}, "bin": {"terser": "bin/terser"}, "engines": {"node": ">=10"}}, "node_modules/terser/node_modules/commander": {"version": "2.20.3", "resolved": "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz", "integrity": "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==", "dev": true}, "node_modules/timsort": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/timsort/-/timsort-0.3.0.tgz", "integrity": "sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q=", "dev": true}, "node_modules/type-fest": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz", "integrity": "sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==", "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/utility-types": {"version": "3.10.0", "resolved": "https://registry.npmjs.org/utility-types/-/utility-types-3.10.0.tgz", "integrity": "sha512-O11mqxmi7wMKCo6HKFt5AhO4BwY3VV68YU07tgxfz8zJTIxr4BpsezN49Ffwy9j3ZpwwJp4fkRwjRzq3uWE6Rg==", "dev": true, "engines": {"node": ">= 4"}}, "node_modules/v8-compile-cache": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/v8-compile-cache/-/v8-compile-cache-2.3.0.tgz", "integrity": "sha512-l8lCEmLcLYZh4nbunNZvQCJc5pv7+RCwa8q/LdUx8u7lsWvPDKmpodJAJNwkAhJC//dFY48KuIEmjtd4RViDrA==", "dev": true}, "node_modules/weak-lru-cache": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/weak-lru-cache/-/weak-lru-cache-1.2.2.tgz", "integrity": "sha512-DEAoo25RfSYMuTGc9vPJzZcZullwIqRDSI9LOy+fkCJPi6hykCnfKaXTuPBDuXAUcqHXyOgFtHNp/kB2FjYHbw==", "dev": true}, "node_modules/xxhash-wasm": {"version": "0.4.2", "resolved": "https://registry.npmjs.org/xxhash-wasm/-/xxhash-wasm-0.4.2.tgz", "integrity": "sha512-/eyHVRJQCirEkSZ1agRSCwriMhwlyUcFkXD5TPVSLP+IPzjsqMVzZwdoczLp1SoQU0R3dxz1RpIK+4YNQbCVOA==", "dev": true}, "node_modules/yaml": {"version": "1.10.2", "resolved": "https://registry.npmjs.org/yaml/-/yaml-1.10.2.tgz", "integrity": "sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==", "dev": true, "engines": {"node": ">= 6"}}}, "dependencies": {"@babel/code-frame": {"version": "7.16.7", "resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.16.7.tgz", "integrity": "sha512-iAXqUn8IIeBTNd72xsFlgaXHkMBMt6y4HJp1tIaK465CWLT/fG1aqB7ykr95gHHmlBdGbFeWWfyB4NJJ0nmeIg==", "dev": true, "requires": {"@babel/highlight": "^7.16.7"}}, "@babel/helper-validator-identifier": {"version": "7.16.7", "resolved": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.16.7.tgz", "integrity": "sha512-hsEnFemeiW4D08A5gUAZxLBTXpZ39P+a+DGDsHw1yxqyQ/jzFEnxf5uTEGp+3bzAbNOxU1paTgYS4ECU/IgfDw==", "dev": true}, "@babel/highlight": {"version": "7.16.10", "resolved": "https://registry.npmjs.org/@babel/highlight/-/highlight-7.16.10.tgz", "integrity": "sha512-5FnTQLSLswEj6IkgVw5KusNUUFY9ZGqe/TRFnP/BKYHYgfh7tc+C7mwiy95/yNP7Dh9x580Vv8r7u7ZfTBFxdw==", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.16.7", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}, "dependencies": {"ansi-styles": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==", "dev": true, "requires": {"color-convert": "^1.9.0"}}, "chalk": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "integrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "color-convert": {"version": "1.9.3", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz", "integrity": "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==", "dev": true, "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=", "dev": true}, "has-flag": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0=", "dev": true}, "supports-color": {"version": "5.5.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz", "integrity": "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==", "dev": true, "requires": {"has-flag": "^3.0.0"}}}}, "@jridgewell/gen-mapping": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.2.tgz", "integrity": "sha512-mh65xKQAzI6iBcFzwv28KVWSmCkdRBWoOh+bYQGW3+6OZvbbN3TqMGo5hqYxQniRcH9F2VZIoJCm4pa3BPDK/A==", "dev": true, "requires": {"@jridgewell/set-array": "^1.0.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.9"}}, "@jridgewell/resolve-uri": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz", "integrity": "sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w==", "dev": true}, "@jridgewell/set-array": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.1.2.tgz", "integrity": "sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==", "dev": true}, "@jridgewell/source-map": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.2.tgz", "integrity": "sha512-m7O9o2uR8k2ObDysZYzdfhb08VuEml5oWGiosa1VdaPZ/A6QyPkAJuwN0Q1lhULOf6B7MtQmHENS743hWtCrgw==", "dev": true, "requires": {"@jridgewell/gen-mapping": "^0.3.0", "@jridgewell/trace-mapping": "^0.3.9"}}, "@jridgewell/sourcemap-codec": {"version": "1.4.14", "resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz", "integrity": "sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw==", "dev": true}, "@jridgewell/trace-mapping": {"version": "0.3.14", "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.14.tgz", "integrity": "sha512-bJWEfQ9lPTvm3SneWwRFVLzrh6nhjwqw7TUFFBEMzwvg7t7PCDenf2lDwqo4NQXzdpgBXyFgDWnQA+2vkruksQ==", "dev": true, "requires": {"@jridgewell/resolve-uri": "^3.0.3", "@jridgewell/sourcemap-codec": "^1.4.10"}}, "@parcel/bundler-default": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/bundler-default/-/bundler-default-2.4.0.tgz", "integrity": "sha512-RaXlxo0M51739Ko3bsOJpDBZlJ+cqkDoBTozNeSc65jS2TMBIBWLMapm8095qmty39OrgYNhzjgPiIlKDS/LWA==", "dev": true, "requires": {"@parcel/diagnostic": "2.4.0", "@parcel/hash": "2.4.0", "@parcel/plugin": "2.4.0", "@parcel/utils": "2.4.0", "nullthrows": "^1.1.1"}}, "@parcel/cache": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/cache/-/cache-2.4.0.tgz", "integrity": "sha512-oOudoAafrCAHQY0zkU7gVHG1pAGBUz9rht7Tx4WupTmAH0O0F5UnZs6XbjoBJaPHg+CYUXK7v9wQcrNA72E3GA==", "dev": true, "requires": {"@parcel/fs": "2.4.0", "@parcel/logger": "2.4.0", "@parcel/utils": "2.4.0", "lmdb": "2.2.4"}}, "@parcel/codeframe": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/codeframe/-/codeframe-2.4.0.tgz", "integrity": "sha512-PJ3W9Z0sjoS2CANyo50c+LEr9IRZrtu0WsVPSYZ5ZYRuSXrSa/6PcAlnkyDk2+hi7Od8ncT2bmDexl0Oar3Jyg==", "dev": true, "requires": {"chalk": "^4.1.0"}}, "@parcel/compressor-raw": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/compressor-raw/-/compressor-raw-2.4.0.tgz", "integrity": "sha512-ZErX14fTc0gKIgtnuqW7Clfln4dpXWfUaJQQIf5C3x/LkpUeEhdXeKntkvSxOddDk2JpIKDwqzAxEMZUnDo4Nw==", "dev": true, "requires": {"@parcel/plugin": "2.4.0"}}, "@parcel/config-default": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/config-default/-/config-default-2.4.0.tgz", "integrity": "sha512-pFOPBXPO6HGqNWTLkcK5i8haMOrRgUouUhcWPGWDpN9IPUYFK2E/O1E/uyMjIA1mSL3FnazI+jJwZ45NhKPpIA==", "dev": true, "requires": {"@parcel/bundler-default": "2.4.0", "@parcel/compressor-raw": "2.4.0", "@parcel/namer-default": "2.4.0", "@parcel/optimizer-css": "2.4.0", "@parcel/optimizer-htmlnano": "2.4.0", "@parcel/optimizer-image": "2.4.0", "@parcel/optimizer-svgo": "2.4.0", "@parcel/optimizer-terser": "2.4.0", "@parcel/packager-css": "2.4.0", "@parcel/packager-html": "2.4.0", "@parcel/packager-js": "2.4.0", "@parcel/packager-raw": "2.4.0", "@parcel/packager-svg": "2.4.0", "@parcel/reporter-dev-server": "2.4.0", "@parcel/resolver-default": "2.4.0", "@parcel/runtime-browser-hmr": "2.4.0", "@parcel/runtime-js": "2.4.0", "@parcel/runtime-react-refresh": "2.4.0", "@parcel/runtime-service-worker": "2.4.0", "@parcel/transformer-babel": "2.4.0", "@parcel/transformer-css": "2.4.0", "@parcel/transformer-html": "2.4.0", "@parcel/transformer-image": "2.4.0", "@parcel/transformer-js": "2.4.0", "@parcel/transformer-json": "2.4.0", "@parcel/transformer-postcss": "2.4.0", "@parcel/transformer-posthtml": "2.4.0", "@parcel/transformer-raw": "2.4.0", "@parcel/transformer-react-refresh-wrap": "2.4.0", "@parcel/transformer-svg": "2.4.0"}}, "@parcel/core": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/core/-/core-2.4.0.tgz", "integrity": "sha512-<PERSON>WZ2UWtIuwDc3fgsKyyTLpNNPoG8Yk2L117ICWF/+cqY8z/wJHm2KwLbeplDeq524shav0GJ9O4CemP3JPx0Nw==", "dev": true, "requires": {"@parcel/cache": "2.4.0", "@parcel/diagnostic": "2.4.0", "@parcel/events": "2.4.0", "@parcel/fs": "2.4.0", "@parcel/graph": "2.4.0", "@parcel/hash": "2.4.0", "@parcel/logger": "2.4.0", "@parcel/package-manager": "2.4.0", "@parcel/plugin": "2.4.0", "@parcel/source-map": "^2.0.0", "@parcel/types": "2.4.0", "@parcel/utils": "2.4.0", "@parcel/workers": "2.4.0", "abortcontroller-polyfill": "^1.1.9", "base-x": "^3.0.8", "browserslist": "^4.6.6", "clone": "^2.1.1", "dotenv": "^7.0.0", "dotenv-expand": "^5.1.0", "json-source-map": "^0.6.1", "json5": "^2.2.0", "msgpackr": "^1.5.4", "nullthrows": "^1.1.1", "semver": "^5.7.1"}}, "@parcel/css": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/@parcel/css/-/css-1.7.3.tgz", "integrity": "sha512-rgdRX4Uk31EvzH/mUScL0wdXtkci3U5N1W2pgam+9S10vQy4uONhWBepZ1tUCjONHLacGXr1jp3LbG/HI7LiTw==", "dev": true, "requires": {"@parcel/css-darwin-arm64": "1.7.3", "@parcel/css-darwin-x64": "1.7.3", "@parcel/css-linux-arm-gnueabihf": "1.7.3", "@parcel/css-linux-arm64-gnu": "1.7.3", "@parcel/css-linux-arm64-musl": "1.7.3", "@parcel/css-linux-x64-gnu": "1.7.3", "@parcel/css-linux-x64-musl": "1.7.3", "@parcel/css-win32-x64-msvc": "1.7.3", "detect-libc": "^1.0.3"}}, "@parcel/css-darwin-arm64": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/@parcel/css-darwin-arm64/-/css-darwin-arm64-1.7.3.tgz", "integrity": "sha512-m3HDY+Rh8HJxmLELKAvCpF59vLS7FWtgBODHxl8G9Jl2CnGtXpXvdpyeMxNsTE+2QuPC+a5QT7IeZAKb2Gjmxg==", "dev": true, "optional": true}, "@parcel/css-darwin-x64": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/@parcel/css-darwin-x64/-/css-darwin-x64-1.7.3.tgz", "integrity": "sha512-LuhweXKxVwrz/hjAOm9XNRMSL+p23px20nhSCASkyUP7Higaxza948W3TSQdoL3YyR+wQxQH8Yj+R/T8Tz3E3g==", "dev": true, "optional": true}, "@parcel/css-linux-arm-gnueabihf": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/@parcel/css-linux-arm-gnueabihf/-/css-linux-arm-gnueabihf-1.7.3.tgz", "integrity": "sha512-/pd9Em18zMvt7eDZAMpNBEwF7c4VPVhAtBOZ59ClFrsXCTDNYP7mSy0cwNgtLelCRZCGAQmZNBDNQPH7vO3rew==", "dev": true, "optional": true}, "@parcel/css-linux-arm64-gnu": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/@parcel/css-linux-arm64-gnu/-/css-linux-arm64-gnu-1.7.3.tgz", "integrity": "sha512-5aKiEhQK40riO4iVKzRqISzgYK+7Z7i3e6JTSz+/BHuQyHEUaBe/RuJ8Z0BDQtFz0HmWQlrQCd+7hd0Xgd8vYQ==", "dev": true, "optional": true}, "@parcel/css-linux-arm64-musl": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/@parcel/css-linux-arm64-musl/-/css-linux-arm64-musl-1.7.3.tgz", "integrity": "sha512-Wf7/aIueDED2JqBMfZvzbBAFSaPmd3TR28bD2pmP7CI/jZnm9vHVKMdOLgt9NKSSSjdGrp+VM410CsrUM7xcOw==", "dev": true, "optional": true}, "@parcel/css-linux-x64-gnu": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/@parcel/css-linux-x64-gnu/-/css-linux-x64-gnu-1.7.3.tgz", "integrity": "sha512-0ZADbuFklUrHC1p2uPY4BPcN07jUTMqJzr/SSdnGN2XiXgiVZGcDCMHUj0DvC9Vwy11DDM6Rnw4QBbKHG+QGjQ==", "dev": true, "optional": true}, "@parcel/css-linux-x64-musl": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/@parcel/css-linux-x64-musl/-/css-linux-x64-musl-1.7.3.tgz", "integrity": "sha512-mFWWM8lX2OIID81YQuDDt9zTqof0B7UcEcs0huE7Zbs60uLEEQupdf8iH0yh5EOhxPt3sRcQnGXf2QTrXdjIMA==", "dev": true, "optional": true}, "@parcel/css-win32-x64-msvc": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/@parcel/css-win32-x64-msvc/-/css-win32-x64-msvc-1.7.3.tgz", "integrity": "sha512-KUFEMQcoP7DG3QbsN21OxhjHkfQ1BARn7D9puX75bV5N1F1kv557aaLkQZiMsgiYOL4tmJvsdQXutG7x++3j4Q==", "dev": true, "optional": true}, "@parcel/diagnostic": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/diagnostic/-/diagnostic-2.4.0.tgz", "integrity": "sha512-TjWO/b2zMFhub5ouwGjazMm7iAUvdmXBfWmjrg4TBhUbhoQwBnyWfvMDtAYo7PcvXfxVPgPZv86Nv6Ym5H6cHQ==", "dev": true, "requires": {"json-source-map": "^0.6.1", "nullthrows": "^1.1.1"}}, "@parcel/events": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/events/-/events-2.4.0.tgz", "integrity": "sha512-DEaEtFbhOhNAEmiXJ3MyF8Scq+sNDKiTyLax4lAC5/dpE5GvwfNnoD17C2+0gDuuDpdQkdHfXfvr50aYFt7jcw==", "dev": true}, "@parcel/fs": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/fs/-/fs-2.4.0.tgz", "integrity": "sha512-CnUlWGUJ52SJVQi8QnaAPPQZOADmHMV9D9aX9GLcDm5XLT3Em7vmesG4bNLdMLwzYuzAtenhcWmuRCACuYztHw==", "dev": true, "requires": {"@parcel/fs-search": "2.4.0", "@parcel/types": "2.4.0", "@parcel/utils": "2.4.0", "@parcel/watcher": "^2.0.0", "@parcel/workers": "2.4.0"}}, "@parcel/fs-search": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/fs-search/-/fs-search-2.4.0.tgz", "integrity": "sha512-W/Vu6wbZk4wuB6AVdMkyymwh/S8Peed/PgJgSsApYD6lSTD315I6OuEdxZh3lWY+dqQdog/NJ7dvi/hdpH/Iqw==", "dev": true, "requires": {"detect-libc": "^1.0.3"}}, "@parcel/graph": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/graph/-/graph-2.4.0.tgz", "integrity": "sha512-5TZIAfDITkJCzgH4j4OQhnIvjV9IFwWqNBJanRl5QQTmKvdcODS3WbnK1SOJ+ZltcLVXMB+HNXmL0bX0tVolcw==", "dev": true, "requires": {"@parcel/utils": "2.4.0", "nullthrows": "^1.1.1"}}, "@parcel/hash": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/hash/-/hash-2.4.0.tgz", "integrity": "sha512-nB+wYNUhe6+G8M7vQhdeFXtpYJYwJgBHOPZ7Hd9O2jdlamWjDbw0t/u1dJbYvGJ8ZDtLDwiItawQVpuVdskQ9g==", "dev": true, "requires": {"detect-libc": "^1.0.3", "xxhash-wasm": "^0.4.2"}}, "@parcel/logger": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/logger/-/logger-2.4.0.tgz", "integrity": "sha512-DqfU0Zcs/0a7VBk+MsjJ80C66w4kM9EbkO3G12NIyEjNeG50ayW2CE9rUuJ91JaM9j0NFM1P82eyLpQPFFaVPw==", "dev": true, "requires": {"@parcel/diagnostic": "2.4.0", "@parcel/events": "2.4.0"}}, "@parcel/markdown-ansi": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/markdown-ansi/-/markdown-ansi-2.4.0.tgz", "integrity": "sha512-gPUP1xikxHiu2kFyPy35pfuVkFgAmcywO8YDQj7iYcB+k7l4QPpIYFYGXn2QADV4faf66ncMeTD4uYV8c0GqjQ==", "dev": true, "requires": {"chalk": "^4.1.0"}}, "@parcel/namer-default": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/namer-default/-/namer-default-2.4.0.tgz", "integrity": "sha512-DfL+Gx0Tyoa0vsgRpNybXjuKbWNw8MTVpy7Dk7r0btfVsn1jy3SSwlxH4USf76gb00/pK6XBsMp9zn7Z8ePREQ==", "dev": true, "requires": {"@parcel/diagnostic": "2.4.0", "@parcel/plugin": "2.4.0", "nullthrows": "^1.1.1"}}, "@parcel/node-resolver-core": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/node-resolver-core/-/node-resolver-core-2.4.0.tgz", "integrity": "sha512-qiN97XcfW2fYNoYuVEhNKuVPEJKj5ONQl0fqr/NEMmYvWz3bVKjgiXNJwW558elZvCI08gEbdxgyThpuFFQeKQ==", "dev": true, "requires": {"@parcel/diagnostic": "2.4.0", "@parcel/utils": "2.4.0", "nullthrows": "^1.1.1"}}, "@parcel/optimizer-css": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/optimizer-css/-/optimizer-css-2.4.0.tgz", "integrity": "sha512-LQmjjOGsHEHKTJqfHR2eJyhWhLXvHP0uOAU+qopBttYYlB2J/vMK9RYAye5cyAb8bQmV8wAdi2mq9rnt7FMSPw==", "dev": true, "requires": {"@parcel/css": "^1.7.2", "@parcel/diagnostic": "2.4.0", "@parcel/plugin": "2.4.0", "@parcel/source-map": "^2.0.0", "@parcel/utils": "2.4.0", "browserslist": "^4.6.6", "nullthrows": "^1.1.1"}}, "@parcel/optimizer-htmlnano": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/optimizer-htmlnano/-/optimizer-htmlnano-2.4.0.tgz", "integrity": "sha512-02EbeElLgNOAYhGU7fFBahpoKrX5G/yzahpaoKB/ypScM4roSsAMBkGcluboR5L10YRsvfvJEpxvfGyDA3tPmw==", "dev": true, "requires": {"@parcel/plugin": "2.4.0", "htmlnano": "^2.0.0", "nullthrows": "^1.1.1", "posthtml": "^0.16.5", "svgo": "^2.4.0"}}, "@parcel/optimizer-image": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/optimizer-image/-/optimizer-image-2.4.0.tgz", "integrity": "sha512-Q4onaBMPkDyYxPzrb8ytBUftaQZFepj9dSUgq+ETuHDzkgia0tomDPfCqrw6ld0qvYyANzXTP5+LC4g0i5yh+A==", "dev": true, "requires": {"@parcel/diagnostic": "2.4.0", "@parcel/plugin": "2.4.0", "@parcel/utils": "2.4.0", "@parcel/workers": "2.4.0", "detect-libc": "^1.0.3"}}, "@parcel/optimizer-svgo": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/optimizer-svgo/-/optimizer-svgo-2.4.0.tgz", "integrity": "sha512-mwvGuCqVuNCAuMlp2maFE/Uz9ud1T1AuX0f6cCRczjFYiwZuIr/0iDdfFzSziOkVo1MRAGAZNa0dRR/UzCZtVg==", "dev": true, "requires": {"@parcel/diagnostic": "2.4.0", "@parcel/plugin": "2.4.0", "@parcel/utils": "2.4.0", "svgo": "^2.4.0"}}, "@parcel/optimizer-terser": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/optimizer-terser/-/optimizer-terser-2.4.0.tgz", "integrity": "sha512-PdCgRgXNSY6R1HTV9VG2MHp1CgUbP5pslCyxvlbUmQAS6bvEpMOpn3qSd+U28o7mGE/qXIhvpDyi808sb+MEcg==", "dev": true, "requires": {"@parcel/diagnostic": "2.4.0", "@parcel/plugin": "2.4.0", "@parcel/source-map": "^2.0.0", "@parcel/utils": "2.4.0", "nullthrows": "^1.1.1", "terser": "^5.2.0"}}, "@parcel/package-manager": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/package-manager/-/package-manager-2.4.0.tgz", "integrity": "sha512-21AEfAQnZbHRVViTn7QsPGe/CiGaFaDUH5f0m8qVC7fDjjhC8LM8blkqU72goaO9FbaLMadtEf2txhzly7h/bg==", "dev": true, "requires": {"@parcel/diagnostic": "2.4.0", "@parcel/fs": "2.4.0", "@parcel/logger": "2.4.0", "@parcel/types": "2.4.0", "@parcel/utils": "2.4.0", "@parcel/workers": "2.4.0", "semver": "^5.7.1"}}, "@parcel/packager-css": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/packager-css/-/packager-css-2.4.0.tgz", "integrity": "sha512-LmPDWzkXi60Oy3WrPF0jPKQxeTwW5hmNBgrcXJMHSu+VcXdaQZNzNxVzhnZkJUbDd2z9vAUrUGzdLh8TquC8iQ==", "dev": true, "requires": {"@parcel/plugin": "2.4.0", "@parcel/source-map": "^2.0.0", "@parcel/utils": "2.4.0", "nullthrows": "^1.1.1"}}, "@parcel/packager-html": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/packager-html/-/packager-html-2.4.0.tgz", "integrity": "sha512-OPMIQ1uHYQFpRPrsmm5BqONbAyzjlhVsPRAzHlcBrglG4BTUeOR2ow4MUKblHmVVqc3QHnfZG4nHHtFkeuNQ3A==", "dev": true, "requires": {"@parcel/plugin": "2.4.0", "@parcel/types": "2.4.0", "@parcel/utils": "2.4.0", "nullthrows": "^1.1.1", "posthtml": "^0.16.5"}}, "@parcel/packager-js": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/packager-js/-/packager-js-2.4.0.tgz", "integrity": "sha512-cfslIH43CJFgBS9PmdFaSnbInMCoejsFCnxtJa2GeUpjCXSfelPRp0OPx7m8n+fap4czftPhoxBALeDUElOZGQ==", "dev": true, "requires": {"@parcel/diagnostic": "2.4.0", "@parcel/hash": "2.4.0", "@parcel/plugin": "2.4.0", "@parcel/source-map": "^2.0.0", "@parcel/utils": "2.4.0", "globals": "^13.2.0", "nullthrows": "^1.1.1"}}, "@parcel/packager-raw": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/packager-raw/-/packager-raw-2.4.0.tgz", "integrity": "sha512-SFfw7chMFITj3J26ZVDJxbO6xwtPFcFBm1js8cwWMgzwuwS6CEc43k5+Abj+2/EqHU9kNJU9eWV5vT6lQwf3HA==", "dev": true, "requires": {"@parcel/plugin": "2.4.0"}}, "@parcel/packager-svg": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/packager-svg/-/packager-svg-2.4.0.tgz", "integrity": "sha512-DwkgrdLEQop+tu9Ocr1ZaadmpsbSgVruJPr80xq1LaB0Jiwrl9HjHStMNH1laNFueK1yydxhnj9C2JQfW28qag==", "dev": true, "requires": {"@parcel/plugin": "2.4.0", "@parcel/types": "2.4.0", "@parcel/utils": "2.4.0", "posthtml": "^0.16.4"}}, "@parcel/plugin": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/plugin/-/plugin-2.4.0.tgz", "integrity": "sha512-ehFUAL2+h27Lv+cYbbXA74UGy8C+eglUjcpvASOOjVRFuD6poMAMliKkKAXBhQaFx/Rvhz27A2PIPv9lL2i4UQ==", "dev": true, "requires": {"@parcel/types": "2.4.0"}}, "@parcel/reporter-cli": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/reporter-cli/-/reporter-cli-2.4.0.tgz", "integrity": "sha512-Q9bIFMaGvQgypCDxdMEKOwrJzIHAXScKkuFsqTHnUL6mmH3Mo2CoEGAq/wpMXuPhXRn1dPJcHgTNDwZ2fSzz0A==", "dev": true, "requires": {"@parcel/plugin": "2.4.0", "@parcel/types": "2.4.0", "@parcel/utils": "2.4.0", "chalk": "^4.1.0", "term-size": "^2.2.1"}}, "@parcel/reporter-dev-server": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/reporter-dev-server/-/reporter-dev-server-2.4.0.tgz", "integrity": "sha512-24h++wevs7XYuX4dKa4PUfLSstvn3g7udajFv6CeQoME+dR25RL/wH/2LUbhV5ilgXXab76rWIndSqp78xHxPA==", "dev": true, "requires": {"@parcel/plugin": "2.4.0", "@parcel/utils": "2.4.0"}}, "@parcel/resolver-default": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/resolver-default/-/resolver-default-2.4.0.tgz", "integrity": "sha512-K7pIIFmGm1hjg/7Mzkg99i8tfCClKfBUTuc2R5j8cdr2n0mCAi4/f2mFf5svLrb5XZrnDgoQ05tHKklLEfUDUw==", "dev": true, "requires": {"@parcel/node-resolver-core": "2.4.0", "@parcel/plugin": "2.4.0"}}, "@parcel/runtime-browser-hmr": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/runtime-browser-hmr/-/runtime-browser-hmr-2.4.0.tgz", "integrity": "sha512-swPFtvxGoCA9LEjU/pHPNjxG1l0fte8447zXwRN/AaYrtjNu9Ww117OSKCyvCnE143E79jZOFStodTQGFuH+9A==", "dev": true, "requires": {"@parcel/plugin": "2.4.0", "@parcel/utils": "2.4.0"}}, "@parcel/runtime-js": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/runtime-js/-/runtime-js-2.4.0.tgz", "integrity": "sha512-67OOvmkDdtmgzZVP/EyAzoXhJ/Ug3LUVUt7idg9arun5rdJptqEb3Um3wmH0zjcNa9jMbJt7Kl5x1wA8dJgPYg==", "dev": true, "requires": {"@parcel/plugin": "2.4.0", "@parcel/utils": "2.4.0", "nullthrows": "^1.1.1"}}, "@parcel/runtime-react-refresh": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/runtime-react-refresh/-/runtime-react-refresh-2.4.0.tgz", "integrity": "sha512-flnr+bf06lMZPbXZZLLaFNrPHvYpfuXTVovEghyUW46qLVpaHj33dpsU/LqZplIuHgBp2ibgrKhr/hY9ell68w==", "dev": true, "requires": {"@parcel/plugin": "2.4.0", "@parcel/utils": "2.4.0", "react-refresh": "^0.9.0"}}, "@parcel/runtime-service-worker": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/runtime-service-worker/-/runtime-service-worker-2.4.0.tgz", "integrity": "sha512-RgM5QUqW22WzstW03CtV+Oih8VGVuwsf94Cc4hLouU2EAD0NUJgATWbFocZVTZIBTKELAWh2gjpSQDdnL4Ur+A==", "dev": true, "requires": {"@parcel/plugin": "2.4.0", "@parcel/utils": "2.4.0", "nullthrows": "^1.1.1"}}, "@parcel/source-map": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/@parcel/source-map/-/source-map-2.0.2.tgz", "integrity": "sha512-NnUrPYLpYB6qyx2v6bcRPn/gVigmGG6M6xL8wIg/i0dP1GLkuY1nf+Hqdf63FzPTqqT7K3k6eE5yHPQVMO5jcA==", "dev": true, "requires": {"detect-libc": "^1.0.3"}}, "@parcel/transformer-babel": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/transformer-babel/-/transformer-babel-2.4.0.tgz", "integrity": "sha512-iWDa7KzJTMP3HNmrYxiYq/S6redk2qminx/9MwmKIN9jzm8mgts2Lj9lOg/t66YaDGky6JAvw4DhB2qW4ni6yQ==", "dev": true, "requires": {"@parcel/diagnostic": "2.4.0", "@parcel/plugin": "2.4.0", "@parcel/source-map": "^2.0.0", "@parcel/utils": "2.4.0", "browserslist": "^4.6.6", "json5": "^2.2.0", "nullthrows": "^1.1.1", "semver": "^5.7.0"}}, "@parcel/transformer-css": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/transformer-css/-/transformer-css-2.4.0.tgz", "integrity": "sha512-D2u48LuiQsQvbknABE0wVKFp9r6yCgWrHKEP1J6EJ31c49nXGXDHrpHJJwqq9BvAs/124eBI5mSsehTJyFEMwg==", "dev": true, "requires": {"@parcel/css": "^1.7.2", "@parcel/diagnostic": "2.4.0", "@parcel/plugin": "2.4.0", "@parcel/source-map": "^2.0.0", "@parcel/utils": "2.4.0", "browserslist": "^4.6.6", "nullthrows": "^1.1.1"}}, "@parcel/transformer-html": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/transformer-html/-/transformer-html-2.4.0.tgz", "integrity": "sha512-2/8X/o5QaCNVPr4wkxLCUub7v/YVvVN2L5yCEcTatNeFhNg/2iz7P2ekfqOaoDCHWZEOBT1VTwPbdBt+TMM71Q==", "dev": true, "requires": {"@parcel/diagnostic": "2.4.0", "@parcel/hash": "2.4.0", "@parcel/plugin": "2.4.0", "nullthrows": "^1.1.1", "posthtml": "^0.16.5", "posthtml-parser": "^0.10.1", "posthtml-render": "^3.0.0", "semver": "^5.7.1"}}, "@parcel/transformer-image": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/transformer-image/-/transformer-image-2.4.0.tgz", "integrity": "sha512-JZkQvGGoGiD0AVKLIbAYYUWxepMmUaWZ4XXx71MmS/kA7cUDwTZ0CXq63YnSY1m+DX+ClTuTN8mBlwe2dkcGbA==", "dev": true, "requires": {"@parcel/plugin": "2.4.0", "@parcel/workers": "2.4.0", "nullthrows": "^1.1.1"}}, "@parcel/transformer-js": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/transformer-js/-/transformer-js-2.4.0.tgz", "integrity": "sha512-eeLHFwv3jT3GmIxpLC7B8EXExGK0MFaK91HXljOMh6l8a+GlQYw27MSFQVtoXr0Olx9Uq2uvjXP1+zSsq3LQUQ==", "dev": true, "requires": {"@parcel/diagnostic": "2.4.0", "@parcel/plugin": "2.4.0", "@parcel/source-map": "^2.0.0", "@parcel/utils": "2.4.0", "@parcel/workers": "2.4.0", "@swc/helpers": "^0.3.6", "browserslist": "^4.6.6", "detect-libc": "^1.0.3", "nullthrows": "^1.1.1", "regenerator-runtime": "^0.13.7", "semver": "^5.7.1"}}, "@parcel/transformer-json": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/transformer-json/-/transformer-json-2.4.0.tgz", "integrity": "sha512-3nR+d39mbURoXIypDfVCaxpwL65qMV+h8SLD78up2uhaRGklHQfN7GuemR7L+mcVAgNrmwVvZHhyNjdgYwWqqg==", "dev": true, "requires": {"@parcel/plugin": "2.4.0", "json5": "^2.2.0"}}, "@parcel/transformer-postcss": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/transformer-postcss/-/transformer-postcss-2.4.0.tgz", "integrity": "sha512-ijIa2x+dbKnJhr7zO5WlXkvuj832fDoGksMBk2DX3u2WMrbh2rqVWPpGFsDhESx7EAy38nUoV/5KUdrNqUmCEA==", "dev": true, "requires": {"@parcel/diagnostic": "2.4.0", "@parcel/hash": "2.4.0", "@parcel/plugin": "2.4.0", "@parcel/utils": "2.4.0", "clone": "^2.1.1", "nullthrows": "^1.1.1", "postcss-value-parser": "^4.2.0", "semver": "^5.7.1"}}, "@parcel/transformer-posthtml": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/transformer-posthtml/-/transformer-posthtml-2.4.0.tgz", "integrity": "sha512-xoL3AzgtVeRRAo6bh0AHAYm9bt1jZ+HiH86/7oARj/uJs6Wd8kXK/DZf6fH+F87hj4e7bnjmDDc0GPVK0lPz1w==", "dev": true, "requires": {"@parcel/plugin": "2.4.0", "@parcel/utils": "2.4.0", "nullthrows": "^1.1.1", "posthtml": "^0.16.5", "posthtml-parser": "^0.10.1", "posthtml-render": "^3.0.0", "semver": "^5.7.1"}}, "@parcel/transformer-raw": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/transformer-raw/-/transformer-raw-2.4.0.tgz", "integrity": "sha512-fciFbNrzj0kLlDgr6OsI0PUv414rVygDWAsgbCCq4BexDkuemMs9f9FjMctx9B2VZlctE8dTT4RGkuQumTIpUg==", "dev": true, "requires": {"@parcel/plugin": "2.4.0"}}, "@parcel/transformer-react-refresh-wrap": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/transformer-react-refresh-wrap/-/transformer-react-refresh-wrap-2.4.0.tgz", "integrity": "sha512-9+f6sGOWkf0jyUQ1CuFWk+04Mq3KTOCU9kRiwCHX1YdUCv5uki6r9XUSpqiYodrV+L6w9CCwLvGMLCDHxtCxMg==", "dev": true, "requires": {"@parcel/plugin": "2.4.0", "@parcel/utils": "2.4.0", "react-refresh": "^0.9.0"}}, "@parcel/transformer-svg": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/transformer-svg/-/transformer-svg-2.4.0.tgz", "integrity": "sha512-D+yzVtSxtQML3d26fd/g4E/xYW68+OMbMUVLXORtoYMU42fnXQkJP6jGOdqy8Td+WORNY7EwVtQnESLwhBmolw==", "dev": true, "requires": {"@parcel/diagnostic": "2.4.0", "@parcel/hash": "2.4.0", "@parcel/plugin": "2.4.0", "nullthrows": "^1.1.1", "posthtml": "^0.16.5", "posthtml-parser": "^0.10.1", "posthtml-render": "^3.0.0", "semver": "^5.7.1"}}, "@parcel/types": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/types/-/types-2.4.0.tgz", "integrity": "sha512-nysGIbBEnp+7R+tKTysdcTFOZDTCodsiXFeAhYQa5bhiOnG1l9gzhxQnE2OsdsgvMm40IOsgKprqvM/DbdLfnQ==", "dev": true, "requires": {"@parcel/cache": "2.4.0", "@parcel/diagnostic": "2.4.0", "@parcel/fs": "2.4.0", "@parcel/package-manager": "2.4.0", "@parcel/source-map": "^2.0.0", "@parcel/workers": "2.4.0", "utility-types": "^3.10.0"}}, "@parcel/utils": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/utils/-/utils-2.4.0.tgz", "integrity": "sha512-sdNo+mZqDZT8LJYB6WWRKa4wFVZcK6Zb5Jh6Du76QvXXwHbPIQNZgJBb6gd/Rbk4GLOp2tW7MnBfq6zP9E9E2g==", "dev": true, "requires": {"@parcel/codeframe": "2.4.0", "@parcel/diagnostic": "2.4.0", "@parcel/hash": "2.4.0", "@parcel/logger": "2.4.0", "@parcel/markdown-ansi": "2.4.0", "@parcel/source-map": "^2.0.0", "chalk": "^4.1.0"}}, "@parcel/watcher": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.0.5.tgz", "integrity": "sha512-x0hUbjv891omnkcHD7ZOhiyyUqUUR6MNjq89JhEI3BxppeKWAm6NPQsqqRrAkCJBogdT/o/My21sXtTI9rJIsw==", "dev": true, "requires": {"node-addon-api": "^3.2.1", "node-gyp-build": "^4.3.0"}}, "@parcel/workers": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@parcel/workers/-/workers-2.4.0.tgz", "integrity": "sha512-eSFyvEoXXPgFzQfKIlpkUjpHfIbezUCRFTPKyJAKCxvU5DSXOpb1kz5vDESWQ4qTZXKnrKvxS1PPWN6bam9z0g==", "dev": true, "requires": {"@parcel/diagnostic": "2.4.0", "@parcel/logger": "2.4.0", "@parcel/types": "2.4.0", "@parcel/utils": "2.4.0", "chrome-trace-event": "^1.0.2", "nullthrows": "^1.1.1"}}, "@swc/helpers": {"version": "0.3.8", "resolved": "https://registry.npmjs.org/@swc/helpers/-/helpers-0.3.8.tgz", "integrity": "sha512-aWItSZvJj4+GI6FWkjZR13xPNPctq2RRakzo+O6vN7bC2yjwdg5EFpgaSAUn95b7BGSgcflvzVDPoKmJv24IOg==", "dev": true}, "@trysound/sax": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/@trysound/sax/-/sax-0.2.0.tgz", "integrity": "sha512-L7z9BgrNEcYyUYtF+HaEfiS5ebkh9jXqbszz7pC0hRBPaatV0XjSD3+eHrpqFemQfgwiFF0QPIarnIihIDn7OA==", "dev": true}, "@types/parse-json": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/@types/parse-json/-/parse-json-4.0.0.tgz", "integrity": "sha512-//oorEZjL6sbPcKUaCdIGlIUeH26mgzimjBB77G6XRgnDl/L5wOnpyBGRe/Mmf5CVW3PwEBE1NjiMZ/ssFh4wA==", "dev": true}, "abortcontroller-polyfill": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/abortcontroller-polyfill/-/abortcontroller-polyfill-1.7.3.tgz", "integrity": "sha512-zetDJxd89y3X99Kvo4qFx8GKlt6GsvN3UcRZHwU6iFA/0KiOmhkTVhe8oRoTBiTVPZu09x3vCra47+w8Yz1+2Q==", "dev": true}, "acorn": {"version": "8.7.0", "resolved": "https://registry.npmjs.org/acorn/-/acorn-8.7.0.tgz", "integrity": "sha512-V/LGr1APy+PXIwKebEWrkZPwoeoF+w1jiOBUmuxuiUIaOHtob8Qc9BTrYo7VuI5fR8tqsy+buA2WFooR5olqvQ==", "dev": true}, "ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "base-x": {"version": "3.0.9", "resolved": "https://registry.npmjs.org/base-x/-/base-x-3.0.9.tgz", "integrity": "sha512-H7JU6iBHTal1gp56aKoaa//YUxEaAOUiydvrV/pILqIHXTtqxSkATOnDA2u+jZ/61sD+L/412+7kzXRtWukhpQ==", "dev": true, "requires": {"safe-buffer": "^5.0.1"}}, "boolbase": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz", "integrity": "sha1-aN/1++YMUes3cl6p4+0xDcwed24=", "dev": true}, "browserslist": {"version": "4.20.2", "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.20.2.tgz", "integrity": "sha512-CQOBCqp/9pDvDbx3xfMi+86pr4KXIf2FDkTTdeuYw8OxS9t898LA1Khq57gtufFILXpfgsSx5woNgsBgvGjpsA==", "dev": true, "requires": {"caniuse-lite": "^1.0.30001317", "electron-to-chromium": "^1.4.84", "escalade": "^3.1.1", "node-releases": "^2.0.2", "picocolors": "^1.0.0"}}, "buffer-from": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz", "integrity": "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==", "dev": true}, "callsites": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz", "integrity": "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==", "dev": true}, "caniuse-lite": {"version": "1.0.30001322", "resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001322.tgz", "integrity": "sha512-neRmrmIrCGuMnxGSoh+x7zYtQFFgnSY2jaomjU56sCkTA6JINqQrxutF459JpWcWRajvoyn95sOXq4Pqrnyjew==", "dev": true}, "chalk": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "chrome-trace-event": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/chrome-trace-event/-/chrome-trace-event-1.0.3.tgz", "integrity": "sha512-p3KULyQg4S7NIHixdwbGX+nFHkoBiA4YQmyWtjb8XngSKV124nJmRysgAeujbUVb15vh+RvFUfCPqU7rXk+hZg==", "dev": true}, "clone": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/clone/-/clone-2.1.2.tgz", "integrity": "sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18=", "dev": true}, "color-convert": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "dev": true}, "commander": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/commander/-/commander-7.2.0.tgz", "integrity": "sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==", "dev": true}, "cosmiconfig": {"version": "7.0.1", "resolved": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-7.0.1.tgz", "integrity": "sha512-a1YWNUV2HwGimB7dU2s1wUMurNKjpx60HxBB6xUM8Re+2s1g1IIfJvFR0/iCF+XHdE0GMTKTuLR32UQff4TEyQ==", "dev": true, "requires": {"@types/parse-json": "^4.0.0", "import-fresh": "^3.2.1", "parse-json": "^5.0.0", "path-type": "^4.0.0", "yaml": "^1.10.0"}}, "css-select": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/css-select/-/css-select-4.3.0.tgz", "integrity": "sha512-wPpOYtnsVontu2mODhA19JrqWxNsfdatRKd64kmpRbQgh1KtItko5sTnEpPdpSaJszTOhEMlF/RPz28qj4HqhQ==", "dev": true, "requires": {"boolbase": "^1.0.0", "css-what": "^6.0.1", "domhandler": "^4.3.1", "domutils": "^2.8.0", "nth-check": "^2.0.1"}}, "css-tree": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/css-tree/-/css-tree-1.1.3.tgz", "integrity": "sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q==", "dev": true, "requires": {"mdn-data": "2.0.14", "source-map": "^0.6.1"}}, "css-what": {"version": "6.0.1", "resolved": "https://registry.npmjs.org/css-what/-/css-what-6.0.1.tgz", "integrity": "sha512-z93ZGFLNc6yaoXAmVhqoSIb+BduplteCt1fepvwhBUQK6MNE4g6fgjpuZKJKp0esUe+vXWlIkwZZjNWoOKw0ZA==", "dev": true}, "csso": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/csso/-/csso-4.2.0.tgz", "integrity": "sha512-wvlcdIbf6pwKEk7vHj8/Bkc0B4ylXZruLvOgs9doS5eOsOpuodOV2zJChSpkp+pRpYQLQMeF04nr3Z68Sta9jA==", "dev": true, "requires": {"css-tree": "^1.1.2"}}, "detect-libc": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/detect-libc/-/detect-libc-1.0.3.tgz", "integrity": "sha1-+hN8S9aY7fVc1c0CrFWfkaTEups=", "dev": true}, "dom-serializer": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-1.3.2.tgz", "integrity": "sha512-5c54Bk5Dw4qAxNOI1pFEizPSjVsx5+bpJKmL2kPn8JhBUq2q09tTCa3mjijun2NfK78NMouDYNMBkOrPZiS+ig==", "dev": true, "requires": {"domelementtype": "^2.0.1", "domhandler": "^4.2.0", "entities": "^2.0.0"}, "dependencies": {"entities": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/entities/-/entities-2.2.0.tgz", "integrity": "sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A==", "dev": true}}}, "domelementtype": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/domelementtype/-/domelementtype-2.2.0.tgz", "integrity": "sha512-DtBMo82pv1dFtUmHyr48beiuq792Sxohr+8Hm9zoxklYPfa6n0Z3Byjj2IV7bmr2IyqClnqEQhfgHJJ5QF0R5A==", "dev": true}, "domhandler": {"version": "4.3.1", "resolved": "https://registry.npmjs.org/domhandler/-/domhandler-4.3.1.tgz", "integrity": "sha512-GrwoxYN+uWlzO8uhUXRl0P+kHE4GtVPfYzVLcUxPL7KNdHKj66vvlhiweIHqYYXWlw+T8iLMp42Lm67ghw4WMQ==", "dev": true, "requires": {"domelementtype": "^2.2.0"}}, "domutils": {"version": "2.8.0", "resolved": "https://registry.npmjs.org/domutils/-/domutils-2.8.0.tgz", "integrity": "sha512-w96Cjofp72M5IIhpjgobBimYEfoPjx1Vx0BSX9P30WBdZW2WIKU0T1Bd0kz2eNZ9ikjKgHbEyKx8BB6H1L3h3A==", "dev": true, "requires": {"dom-serializer": "^1.0.1", "domelementtype": "^2.2.0", "domhandler": "^4.2.0"}}, "dotenv": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/dotenv/-/dotenv-7.0.0.tgz", "integrity": "sha512-M3NhsLbV1i6HuGzBUH8vXrtxOk+tWmzWKDMbAVSUp3Zsjm7ywFeuwrUXhmhQyRK1q5B5GGy7hcXPbj3bnfZg2g==", "dev": true}, "dotenv-expand": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/dotenv-expand/-/dotenv-expand-5.1.0.tgz", "integrity": "sha512-YXQl1DSa4/PQyRfgrv6aoNjhasp/p4qs9FjJ4q4cQk+8m4r6k4ZSiEyytKG8f8W9gi8WsQtIObNmKd+tMzNTmA==", "dev": true}, "electron-to-chromium": {"version": "1.4.98", "resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.4.98.tgz", "integrity": "sha512-1IdsuSAnIGVxoYT1LkcUFb9MfjRxdHhCU9qiaDzhl1XvYgK9c8E2O9aJOPgGMQ68CSI8NxmLwrYhjvGauT8yuw==", "dev": true}, "entities": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/entities/-/entities-3.0.1.tgz", "integrity": "sha512-WiyBqoomrwMdFG1e0kqvASYfnlb0lp8M5o5Fw2OFq1hNZxxcNk8Ik0Xm7LxzBhuidnZB/UtBqVCgUz3kBOP51Q==", "dev": true}, "error-ex": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz", "integrity": "sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==", "dev": true, "requires": {"is-arrayish": "^0.2.1"}}, "escalade": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/escalade/-/escalade-3.1.1.tgz", "integrity": "sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==", "dev": true}, "escape-string-regexp": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=", "dev": true}, "get-port": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/get-port/-/get-port-4.2.0.tgz", "integrity": "sha512-/b3jarXkH8KJoOMQc3uVGHASwGLPq3gSFJ7tgJm2diza+bydJPTGOibin2steecKeOylE8oY2JERlVWkAJO6yw==", "dev": true}, "globals": {"version": "13.13.0", "resolved": "https://registry.npmjs.org/globals/-/globals-13.13.0.tgz", "integrity": "sha512-EQ7Q18AJlPwp3vUDL4mKA0KXrXyNIQyWon6T6XQiBQF0XHvRsiCSrWmmeATpUzdJN2HhWZU6Pdl0a9zdep5p6A==", "dev": true, "requires": {"type-fest": "^0.20.2"}}, "has-flag": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==", "dev": true}, "htmlnano": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/htmlnano/-/htmlnano-2.0.0.tgz", "integrity": "sha512-thKQfhcp2xgtsWNE27A2bliEeqVL5xjAgGn0wajyttvFFsvFWWah1ntV9aEX61gz0T6MBQ5xK/1lXuEumhJTcg==", "dev": true, "requires": {"cosmiconfig": "^7.0.1", "posthtml": "^0.16.5", "timsort": "^0.3.0"}}, "htmlparser2": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/htmlparser2/-/htmlparser2-7.2.0.tgz", "integrity": "sha512-H7MImA4MS6cw7nbyURtLPO1Tms7C5H602LRETv95z1MxO/7CP7rDVROehUYeYBUYEON94NXXDEPmZuq+hX4sog==", "dev": true, "requires": {"domelementtype": "^2.0.1", "domhandler": "^4.2.2", "domutils": "^2.8.0", "entities": "^3.0.1"}}, "import-fresh": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.0.tgz", "integrity": "sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==", "dev": true, "requires": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}}, "is-arrayish": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz", "integrity": "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=", "dev": true}, "is-json": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/is-json/-/is-json-2.0.1.tgz", "integrity": "sha1-a+Fm0USCihMdaGiRuYPfYsOUkf8=", "dev": true}, "js-tokens": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==", "dev": true}, "json-parse-even-better-errors": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "integrity": "sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==", "dev": true}, "json-source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/json-source-map/-/json-source-map-0.6.1.tgz", "integrity": "sha512-1QoztHPsMQqhDq0hlXY5ZqcEdUzxQEIxgFkKl4WUp2pgShObl+9ovi4kRh2TfvAfxAoHOJ9vIMEqk3k4iex7tg==", "dev": true}, "json5": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/json5/-/json5-2.2.1.tgz", "integrity": "sha512-1hqLFMSrGHRHxav9q9gNjJ5EXznIxGVO09xQRrwplcS8qs28pZ8s8hupZAmqDwZUmVZ2Qb2jnyPOWcDH8m8dlA==", "dev": true}, "lines-and-columns": {"version": "1.2.4", "resolved": "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz", "integrity": "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==", "dev": true}, "lmdb": {"version": "2.2.4", "resolved": "https://registry.npmjs.org/lmdb/-/lmdb-2.2.4.tgz", "integrity": "sha512-gto+BB2uEob8qRiTlOq+R3uX0YNHsX9mjxj9Sbdue/LIKqu6IlZjrsjKeGyOMquc/474GEqFyX2pdytpydp0rQ==", "dev": true, "requires": {"msgpackr": "^1.5.4", "nan": "^2.14.2", "node-gyp-build": "^4.2.3", "ordered-binary": "^1.2.4", "weak-lru-cache": "^1.2.2"}}, "mdn-data": {"version": "2.0.14", "resolved": "https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.14.tgz", "integrity": "sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow==", "dev": true}, "monaco-editor": {"version": "0.32.1", "resolved": "https://registry.npmjs.org/monaco-editor/-/monaco-editor-0.32.1.tgz", "integrity": "sha512-LUt2wsUvQmEi2tfTOK+tjAPvt7eQ+K5C4rZPr6SeuyzjAuAHrIvlUloTcOiGjZW3fn3a/jFQCONrEJbNOaCqbA==", "dev": true}, "msgpackr": {"version": "1.5.5", "resolved": "https://registry.npmjs.org/msgpackr/-/msgpackr-1.5.5.tgz", "integrity": "sha512-JG0V47xRIQ9pyUnx6Hb4+3TrQoia2nA3UIdmyTldhxaxtKFkekkKpUW/N6fwHwod9o4BGuJGtouxOk+yCP5PEA==", "dev": true, "requires": {"msgpackr-extract": "^1.0.14"}}, "msgpackr-extract": {"version": "1.0.16", "resolved": "https://registry.npmjs.org/msgpackr-extract/-/msgpackr-extract-1.0.16.tgz", "integrity": "sha512-fxdRfQUxPrL/TizyfYfMn09dK58e+d65bRD/fcaVH4052vj30QOzzqxcQIS7B0NsqlypEQ/6Du3QmP2DhWFfCA==", "dev": true, "optional": true, "requires": {"nan": "^2.14.2", "node-gyp-build": "^4.2.3"}}, "nan": {"version": "2.15.0", "resolved": "https://registry.npmjs.org/nan/-/nan-2.15.0.tgz", "integrity": "sha512-8ZtvEnA2c5aYCZYd1cvgdnU6cqwixRoYg70xPLWUws5ORTa/lnw+u4amixRS/Ac5U5mQVgp9pnlSUnbNWFaWZQ==", "dev": true}, "node-addon-api": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-3.2.1.tgz", "integrity": "sha512-mmcei9JghVNDYydghQmeDX8KoAm0FAiYyIcUt/N4nhyAipB17pllZQDOJD2fotxABnt4Mdz+dKTO7eftLg4d0A==", "dev": true}, "node-gyp-build": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/node-gyp-build/-/node-gyp-build-4.3.0.tgz", "integrity": "sha512-iWjXZvmboq0ja1pUGULQBexmxq8CV4xBhX7VDOTbL7ZR4FOowwY/VOtRxBN/yKxmdGoIp4j5ysNT4u3S2pDQ3Q==", "dev": true}, "node-releases": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.2.tgz", "integrity": "sha512-XxYDdcQ6eKqp/YjI+tb2C5WM2LgjnZrfYg4vgQt49EK268b6gYCHsBLrK2qvJo4FmCtqmKezb0WZFK4fkrZNsg==", "dev": true}, "nth-check": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/nth-check/-/nth-check-2.0.1.tgz", "integrity": "sha512-it1vE95zF6dTT9lBsYbxvqh0Soy4SPowchj0UBGj/V6cTPnXXtQOPUbhZ6CmGzAD/rW22LQK6E96pcdJXk4A4w==", "dev": true, "requires": {"boolbase": "^1.0.0"}}, "nullthrows": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/nullthrows/-/nullthrows-1.1.1.tgz", "integrity": "sha512-2vPPEi+Z7WqML2jZYddDIfy5Dqb0r2fze2zTxNNknZaFpVHU3mFB3R+DWeJWGVx0ecvttSGlJTI+WG+8Z4cDWw==", "dev": true}, "ordered-binary": {"version": "1.2.4", "resolved": "https://registry.npmjs.org/ordered-binary/-/ordered-binary-1.2.4.tgz", "integrity": "sha512-A/csN0d3n+igxBPfUrjbV5GC69LWj2pjZzAAeeHXLukQ4+fytfP4T1Lg0ju7MSPSwq7KtHkGaiwO8URZN5IpLg==", "dev": true}, "parcel": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/parcel/-/parcel-2.4.0.tgz", "integrity": "sha512-dPWpu4RnxG9HqiLvaF8COEWEnT/KrigrC6PyPaQ0zEgpBfp7/jzXZFBVaZk2N+lpvrbNEYMjN9bv5UQGJJszIw==", "dev": true, "requires": {"@parcel/config-default": "2.4.0", "@parcel/core": "2.4.0", "@parcel/diagnostic": "2.4.0", "@parcel/events": "2.4.0", "@parcel/fs": "2.4.0", "@parcel/logger": "2.4.0", "@parcel/package-manager": "2.4.0", "@parcel/reporter-cli": "2.4.0", "@parcel/reporter-dev-server": "2.4.0", "@parcel/utils": "2.4.0", "chalk": "^4.1.0", "commander": "^7.0.0", "get-port": "^4.2.0", "v8-compile-cache": "^2.0.0"}}, "parent-module": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz", "integrity": "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==", "dev": true, "requires": {"callsites": "^3.0.0"}}, "parse-json": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz", "integrity": "sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==", "dev": true, "requires": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}}, "path-type": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz", "integrity": "sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==", "dev": true}, "picocolors": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/picocolors/-/picocolors-1.0.0.tgz", "integrity": "sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==", "dev": true}, "postcss-value-parser": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "integrity": "sha512-1N<PERSON>s6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==", "dev": true}, "posthtml": {"version": "0.16.6", "resolved": "https://registry.npmjs.org/posthtml/-/posthtml-0.16.6.tgz", "integrity": "sha512-JcEmHlyLK/o0uGAlj65vgg+7LIms0xKXe60lcDOTU7oVX/3LuEuLwrQpW3VJ7de5TaFKiW4kWkaIpJL42FEgxQ==", "dev": true, "requires": {"posthtml-parser": "^0.11.0", "posthtml-render": "^3.0.0"}, "dependencies": {"posthtml-parser": {"version": "0.11.0", "resolved": "https://registry.npmjs.org/posthtml-parser/-/posthtml-parser-0.11.0.tgz", "integrity": "sha512-QecJtfLekJbWVo/dMAA+OSwY79wpRmbqS5TeXvXSX+f0c6pW4/SE6inzZ2qkU7oAMCPqIDkZDvd/bQsSFUnKyw==", "dev": true, "requires": {"htmlparser2": "^7.1.1"}}}}, "posthtml-parser": {"version": "0.10.2", "resolved": "https://registry.npmjs.org/posthtml-parser/-/posthtml-parser-0.10.2.tgz", "integrity": "sha512-PId6zZ/2lyJi9LiKfe+i2xv57oEjJgWbsHGGANwos5AvdQp98i6AtamAl8gzSVFGfQ43Glb5D614cvZf012VKg==", "dev": true, "requires": {"htmlparser2": "^7.1.1"}}, "posthtml-render": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/posthtml-render/-/posthtml-render-3.0.0.tgz", "integrity": "sha512-z+16RoxK3fUPgwaIgH9NGnK1HKY9XIDpydky5eQGgAFVXTCSezalv9U2jQuNV+Z9qV1fDWNzldcw4eK0SSbqKA==", "dev": true, "requires": {"is-json": "^2.0.1"}}, "react-refresh": {"version": "0.9.0", "resolved": "https://registry.npmjs.org/react-refresh/-/react-refresh-0.9.0.tgz", "integrity": "sha512-Gvzk7OZpiqKSkxsQvO/mbTN1poglhmAV7gR/DdIrRrSMXraRQQlfikRJOr3Nb9GTMPC5kof948Zy6jJZIFtDvQ==", "dev": true}, "regenerator-runtime": {"version": "0.13.9", "resolved": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.9.tgz", "integrity": "sha512-p3VT+cOEgxFsRRA9X4lkI1E+k2/CtnKtU4gcxyaCUreilL/vqI6CdZ3wxVUx3UOUg+gnUOQQcRI7BmSI656MYA==", "dev": true}, "resolve-from": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz", "integrity": "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==", "dev": true}, "safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "dev": true}, "semver": {"version": "5.7.1", "resolved": "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz", "integrity": "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ==", "dev": true}, "source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "dev": true}, "source-map-support": {"version": "0.5.21", "resolved": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz", "integrity": "sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==", "dev": true, "requires": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "stable": {"version": "0.1.8", "resolved": "https://registry.npmjs.org/stable/-/stable-0.1.8.tgz", "integrity": "sha512-ji9qxRnOVfcuLDySj9qzhGSEFVobyt1kIOSkj1qZzYLzq7Tos/oUUWvotUPQLlrsidqsK6tBH89Bc9kL5zHA6w==", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "dev": true, "requires": {"has-flag": "^4.0.0"}}, "svgo": {"version": "2.8.0", "resolved": "https://registry.npmjs.org/svgo/-/svgo-2.8.0.tgz", "integrity": "sha512-+N/Q9kV1+F+UeWYoSiULYo4xYSDQlTgb+ayMobAXPwMnLvop7oxKMo9OzIrX5x3eS4L4f2UHhc9axXwY8DpChg==", "dev": true, "requires": {"@trysound/sax": "0.2.0", "commander": "^7.2.0", "css-select": "^4.1.3", "css-tree": "^1.1.3", "csso": "^4.2.0", "picocolors": "^1.0.0", "stable": "^0.1.8"}}, "term-size": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/term-size/-/term-size-2.2.1.tgz", "integrity": "sha512-wK0Ri4fOGjv/XPy8SBHZChl8CM7uMc5VML7SqiQ0zG7+J5Vr+RMQDoHa2CNT6KHUnTGIXH34UDMkPzAUyapBZg==", "dev": true}, "terser": {"version": "5.14.2", "resolved": "https://registry.npmjs.org/terser/-/terser-5.14.2.tgz", "integrity": "sha512-oL0rGeM/WFQCUd0y2QrWxYnq7tfSuKBiqTjRPWrRgB46WD/kiwHwF8T23z78H6Q6kGCuuHcPB+KULHRdxvVGQA==", "dev": true, "requires": {"@jridgewell/source-map": "^0.3.2", "acorn": "^8.5.0", "commander": "^2.20.0", "source-map-support": "~0.5.20"}, "dependencies": {"commander": {"version": "2.20.3", "resolved": "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz", "integrity": "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==", "dev": true}}}, "timsort": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/timsort/-/timsort-0.3.0.tgz", "integrity": "sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q=", "dev": true}, "type-fest": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz", "integrity": "sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==", "dev": true}, "utility-types": {"version": "3.10.0", "resolved": "https://registry.npmjs.org/utility-types/-/utility-types-3.10.0.tgz", "integrity": "sha512-O11mqxmi7wMKCo6HKFt5AhO4BwY3VV68YU07tgxfz8zJTIxr4BpsezN49Ffwy9j3ZpwwJp4fkRwjRzq3uWE6Rg==", "dev": true}, "v8-compile-cache": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/v8-compile-cache/-/v8-compile-cache-2.3.0.tgz", "integrity": "sha512-l8lCEmLcLYZh4nbunNZvQCJc5pv7+RCwa8q/LdUx8u7lsWvPDKmpodJAJNwkAhJC//dFY48KuIEmjtd4RViDrA==", "dev": true}, "weak-lru-cache": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/weak-lru-cache/-/weak-lru-cache-1.2.2.tgz", "integrity": "sha512-DEAoo25RfSYMuTGc9vPJzZcZullwIqRDSI9LOy+fkCJPi6hykCnfKaXTuPBDuXAUcqHXyOgFtHNp/kB2FjYHbw==", "dev": true}, "xxhash-wasm": {"version": "0.4.2", "resolved": "https://registry.npmjs.org/xxhash-wasm/-/xxhash-wasm-0.4.2.tgz", "integrity": "sha512-/eyHVRJQCirEkSZ1agRSCwriMhwlyUcFkXD5TPVSLP+IPzjsqMVzZwdoczLp1SoQU0R3dxz1RpIK+4YNQbCVOA==", "dev": true}, "yaml": {"version": "1.10.2", "resolved": "https://registry.npmjs.org/yaml/-/yaml-1.10.2.tgz", "integrity": "sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==", "dev": true}}}
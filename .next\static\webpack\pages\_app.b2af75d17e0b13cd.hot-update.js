"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[1]!./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[2]!./styles/globals.css":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[1]!./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[2]!./styles/globals.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_pnpm_next_14_2_3_react_dom_18_2_0_react_18_2_0_react_18_2_0_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_pnpm_next_14_2_3_react_dom_18_2_0_react_18_2_0_react_18_2_0_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_pnpm_next_14_2_3_react_dom_18_2_0_react_18_2_0_react_18_2_0_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_pnpm_next_14_2_3_react_dom_18_2_0_react_18_2_0_react_18_2_0_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');\\n\\n*, ::before, ::after{\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n::backdrop{\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*/\\n\\n/*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\n\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\"; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\n\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\n.container{\\n  width: 100%;\\n}\\n@media (min-width: 640px){\\n\\n  .container{\\n    max-width: 640px;\\n  }\\n}\\n@media (min-width: 768px){\\n\\n  .container{\\n    max-width: 768px;\\n  }\\n}\\n@media (min-width: 1024px){\\n\\n  .container{\\n    max-width: 1024px;\\n  }\\n}\\n@media (min-width: 1280px){\\n\\n  .container{\\n    max-width: 1280px;\\n  }\\n}\\n@media (min-width: 1536px){\\n\\n  .container{\\n    max-width: 1536px;\\n  }\\n}\\n.fixed{\\n  position: fixed;\\n}\\n.absolute{\\n  position: absolute;\\n}\\n.relative{\\n  position: relative;\\n}\\n.inset-0{\\n  inset: 0px;\\n}\\n.-bottom-1{\\n  bottom: -0.25rem;\\n}\\n.-left-1\\\\/2{\\n  left: -50%;\\n}\\n.-right-1{\\n  right: -0.25rem;\\n}\\n.-top-1{\\n  top: -0.25rem;\\n}\\n.-top-1\\\\/2{\\n  top: -50%;\\n}\\n.left-0{\\n  left: 0px;\\n}\\n.left-1\\\\/2{\\n  left: 50%;\\n}\\n.top-1\\\\/2{\\n  top: 50%;\\n}\\n.z-10{\\n  z-index: 10;\\n}\\n.z-50{\\n  z-index: 50;\\n}\\n.col-span-2{\\n  grid-column: span 2 / span 2;\\n}\\n.mx-auto{\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n.\\\\!mt-6{\\n  margin-top: 1.5rem !important;\\n}\\n.mb-1{\\n  margin-bottom: 0.25rem;\\n}\\n.mb-2{\\n  margin-bottom: 0.5rem;\\n}\\n.mb-3{\\n  margin-bottom: 0.75rem;\\n}\\n.mb-4{\\n  margin-bottom: 1rem;\\n}\\n.mb-6{\\n  margin-bottom: 1.5rem;\\n}\\n.mb-8{\\n  margin-bottom: 2rem;\\n}\\n.mr-2{\\n  margin-right: 0.5rem;\\n}\\n.mt-1{\\n  margin-top: 0.25rem;\\n}\\n.mt-2{\\n  margin-top: 0.5rem;\\n}\\n.mt-3{\\n  margin-top: 0.75rem;\\n}\\n.mt-4{\\n  margin-top: 1rem;\\n}\\n.inline-block{\\n  display: inline-block;\\n}\\n.flex{\\n  display: flex;\\n}\\n.table{\\n  display: table;\\n}\\n.grid{\\n  display: grid;\\n}\\n.hidden{\\n  display: none;\\n}\\n.h-0\\\\.5{\\n  height: 0.125rem;\\n}\\n.h-1{\\n  height: 0.25rem;\\n}\\n.h-10{\\n  height: 2.5rem;\\n}\\n.h-12{\\n  height: 3rem;\\n}\\n.h-14{\\n  height: 3.5rem;\\n}\\n.h-16{\\n  height: 4rem;\\n}\\n.h-2{\\n  height: 0.5rem;\\n}\\n.h-24{\\n  height: 6rem;\\n}\\n.h-3{\\n  height: 0.75rem;\\n}\\n.h-32{\\n  height: 8rem;\\n}\\n.h-4{\\n  height: 1rem;\\n}\\n.h-40{\\n  height: 10rem;\\n}\\n.h-48{\\n  height: 12rem;\\n}\\n.h-5{\\n  height: 1.25rem;\\n}\\n.h-6{\\n  height: 1.5rem;\\n}\\n.h-64{\\n  height: 16rem;\\n}\\n.h-8{\\n  height: 2rem;\\n}\\n.h-\\\\[200\\\\%\\\\]{\\n  height: 200%;\\n}\\n.h-full{\\n  height: 100%;\\n}\\n.max-h-64{\\n  max-height: 16rem;\\n}\\n.min-h-0{\\n  min-height: 0px;\\n}\\n.w-1{\\n  width: 0.25rem;\\n}\\n.w-1\\\\/4{\\n  width: 25%;\\n}\\n.w-10{\\n  width: 2.5rem;\\n}\\n.w-12{\\n  width: 3rem;\\n}\\n.w-14{\\n  width: 3.5rem;\\n}\\n.w-16{\\n  width: 4rem;\\n}\\n.w-2{\\n  width: 0.5rem;\\n}\\n.w-24{\\n  width: 6rem;\\n}\\n.w-3{\\n  width: 0.75rem;\\n}\\n.w-32{\\n  width: 8rem;\\n}\\n.w-4{\\n  width: 1rem;\\n}\\n.w-5{\\n  width: 1.25rem;\\n}\\n.w-6{\\n  width: 1.5rem;\\n}\\n.w-8{\\n  width: 2rem;\\n}\\n.w-\\\\[200\\\\%\\\\]{\\n  width: 200%;\\n}\\n.w-full{\\n  width: 100%;\\n}\\n.max-w-md{\\n  max-width: 28rem;\\n}\\n.max-w-sm{\\n  max-width: 24rem;\\n}\\n.flex-1{\\n  flex: 1 1 0%;\\n}\\n.flex-shrink-0{\\n  flex-shrink: 0;\\n}\\n.flex-grow{\\n  flex-grow: 1;\\n}\\n.-translate-x-1\\\\/2{\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.-translate-y-1\\\\/2{\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.translate-x-0{\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.translate-x-5{\\n  --tw-translate-x: 1.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.scale-105{\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.scale-90{\\n  --tw-scale-x: .9;\\n  --tw-scale-y: .9;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.scale-95{\\n  --tw-scale-x: .95;\\n  --tw-scale-y: .95;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.transform{\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n@keyframes pulse{\\n\\n  50%{\\n    opacity: .5;\\n  }\\n}\\n.animate-pulse{\\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\n@keyframes spin{\\n\\n  to{\\n    transform: rotate(360deg);\\n  }\\n}\\n.animate-spin{\\n  animation: spin 1s linear infinite;\\n}\\n.cursor-pointer{\\n  cursor: pointer;\\n}\\n.resize{\\n  resize: both;\\n}\\n.grid-cols-1{\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\n.grid-cols-2{\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\n.grid-cols-3{\\n  grid-template-columns: repeat(3, minmax(0, 1fr));\\n}\\n.grid-cols-4{\\n  grid-template-columns: repeat(4, minmax(0, 1fr));\\n}\\n.flex-col{\\n  flex-direction: column;\\n}\\n.items-start{\\n  align-items: flex-start;\\n}\\n.items-end{\\n  align-items: flex-end;\\n}\\n.items-center{\\n  align-items: center;\\n}\\n.justify-end{\\n  justify-content: flex-end;\\n}\\n.justify-center{\\n  justify-content: center;\\n}\\n.justify-between{\\n  justify-content: space-between;\\n}\\n.justify-around{\\n  justify-content: space-around;\\n}\\n.gap-1{\\n  gap: 0.25rem;\\n}\\n.gap-2{\\n  gap: 0.5rem;\\n}\\n.gap-3{\\n  gap: 0.75rem;\\n}\\n.gap-4{\\n  gap: 1rem;\\n}\\n.gap-6{\\n  gap: 1.5rem;\\n}\\n.space-y-1 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\n.space-y-2 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-3 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\n.space-y-4 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\n.overflow-auto{\\n  overflow: auto;\\n}\\n.overflow-hidden{\\n  overflow: hidden;\\n}\\n.overflow-y-auto{\\n  overflow-y: auto;\\n}\\n.truncate{\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.rounded-full{\\n  border-radius: 9999px;\\n}\\n.rounded-lg{\\n  border-radius: 0.5rem;\\n}\\n.rounded-md{\\n  border-radius: 0.375rem;\\n}\\n.rounded-sm{\\n  border-radius: 0.125rem;\\n}\\n.rounded-xl{\\n  border-radius: 0.75rem;\\n}\\n.rounded-r-full{\\n  border-top-right-radius: 9999px;\\n  border-bottom-right-radius: 9999px;\\n}\\n.border{\\n  border-width: 1px;\\n}\\n.border-2{\\n  border-width: 2px;\\n}\\n.border-b-2{\\n  border-bottom-width: 2px;\\n}\\n.border-t{\\n  border-top-width: 1px;\\n}\\n.border-blue-400\\\\/30{\\n  border-color: rgb(96 165 250 / 0.3);\\n}\\n.border-blue-500\\\\/30{\\n  border-color: rgb(59 130 246 / 0.3);\\n}\\n.border-brand-cyan{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(0 255 255 / var(--tw-border-opacity, 1));\\n}\\n.border-cyan-400{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(34 211 238 / var(--tw-border-opacity, 1));\\n}\\n.border-cyan-400\\\\/30{\\n  border-color: rgb(34 211 238 / 0.3);\\n}\\n.border-cyan-500\\\\/30{\\n  border-color: rgb(6 182 212 / 0.3);\\n}\\n.border-cyan-500\\\\/50{\\n  border-color: rgb(6 182 212 / 0.5);\\n}\\n.border-gray-400\\\\/30{\\n  border-color: rgb(156 163 175 / 0.3);\\n}\\n.border-gray-500\\\\/40{\\n  border-color: rgb(107 114 128 / 0.4);\\n}\\n.border-gray-600{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-600\\\\/30{\\n  border-color: rgb(75 85 99 / 0.3);\\n}\\n.border-gray-700{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-700\\\\/30{\\n  border-color: rgb(55 65 81 / 0.3);\\n}\\n.border-gray-700\\\\/50{\\n  border-color: rgb(55 65 81 / 0.5);\\n}\\n.border-green-400\\\\/30{\\n  border-color: rgb(74 222 128 / 0.3);\\n}\\n.border-green-500\\\\/30{\\n  border-color: rgb(34 197 94 / 0.3);\\n}\\n.border-pink-400\\\\/30{\\n  border-color: rgb(244 114 182 / 0.3);\\n}\\n.border-pink-500\\\\/90{\\n  border-color: rgb(236 72 153 / 0.9);\\n}\\n.border-purple-400\\\\/30{\\n  border-color: rgb(192 132 252 / 0.3);\\n}\\n.border-purple-400\\\\/40{\\n  border-color: rgb(192 132 252 / 0.4);\\n}\\n.border-purple-500\\\\/30{\\n  border-color: rgb(168 85 247 / 0.3);\\n}\\n.border-red-400\\\\/30{\\n  border-color: rgb(248 113 113 / 0.3);\\n}\\n.border-red-500\\\\/30{\\n  border-color: rgb(239 68 68 / 0.3);\\n}\\n.border-yellow-400\\\\/30{\\n  border-color: rgb(250 204 21 / 0.3);\\n}\\n.border-yellow-400\\\\/50{\\n  border-color: rgb(250 204 21 / 0.5);\\n}\\n.border-yellow-500\\\\/30{\\n  border-color: rgb(234 179 8 / 0.3);\\n}\\n.bg-black\\\\/60{\\n  background-color: rgb(0 0 0 / 0.6);\\n}\\n.bg-black\\\\/70{\\n  background-color: rgb(0 0 0 / 0.7);\\n}\\n.bg-blue-400{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-500\\\\/20{\\n  background-color: rgb(59 130 246 / 0.2);\\n}\\n.bg-brand-cyan{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-brand-cyan\\\\/20{\\n  background-color: rgb(0 255 255 / 0.2);\\n}\\n.bg-brand-pink\\\\/20{\\n  background-color: rgb(255 0 127 / 0.2);\\n}\\n.bg-container-bg{\\n  background-color: rgba(10, 20, 35, 0.7);\\n}\\n.bg-cyan-400{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 211 238 / var(--tw-bg-opacity, 1));\\n}\\n.bg-cyan-400\\\\/10{\\n  background-color: rgb(34 211 238 / 0.1);\\n}\\n.bg-cyan-400\\\\/20{\\n  background-color: rgb(34 211 238 / 0.2);\\n}\\n.bg-cyan-400\\\\/50{\\n  background-color: rgb(34 211 238 / 0.5);\\n}\\n.bg-cyan-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(6 182 212 / var(--tw-bg-opacity, 1));\\n}\\n.bg-cyan-500\\\\/20{\\n  background-color: rgb(6 182 212 / 0.2);\\n}\\n.bg-cyan-500\\\\/30{\\n  background-color: rgb(6 182 212 / 0.3);\\n}\\n.bg-cyan-500\\\\/50{\\n  background-color: rgb(6 182 212 / 0.5);\\n}\\n.bg-emerald-500\\\\/20{\\n  background-color: rgb(16 185 129 / 0.2);\\n}\\n.bg-gray-200\\\\/20{\\n  background-color: rgb(229 231 235 / 0.2);\\n}\\n.bg-gray-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-500\\\\/20{\\n  background-color: rgb(107 114 128 / 0.2);\\n}\\n.bg-gray-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-700{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-700\\\\/30{\\n  background-color: rgb(55 65 81 / 0.3);\\n}\\n.bg-gray-700\\\\/50{\\n  background-color: rgb(55 65 81 / 0.5);\\n}\\n.bg-gray-700\\\\/60{\\n  background-color: rgb(55 65 81 / 0.6);\\n}\\n.bg-gray-700\\\\/80{\\n  background-color: rgb(55 65 81 / 0.8);\\n}\\n.bg-gray-800\\\\/40{\\n  background-color: rgb(31 41 55 / 0.4);\\n}\\n.bg-green-400{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-500\\\\/20{\\n  background-color: rgb(34 197 94 / 0.2);\\n}\\n.bg-panel-bg{\\n  background-color: rgba(25, 40, 60, 0.6);\\n}\\n.bg-pink-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(236 72 153 / var(--tw-bg-opacity, 1));\\n}\\n.bg-pink-500\\\\/20{\\n  background-color: rgb(236 72 153 / 0.2);\\n}\\n.bg-pink-500\\\\/80{\\n  background-color: rgb(236 72 153 / 0.8);\\n}\\n.bg-purple-500\\\\/20{\\n  background-color: rgb(168 85 247 / 0.2);\\n}\\n.bg-red-500\\\\/20{\\n  background-color: rgb(239 68 68 / 0.2);\\n}\\n.bg-white{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-white\\\\/20{\\n  background-color: rgb(255 255 255 / 0.2);\\n}\\n.bg-yellow-400{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-500\\\\/20{\\n  background-color: rgb(234 179 8 / 0.2);\\n}\\n.bg-\\\\[radial-gradient\\\\(circle_at_center\\\\2c _rgba\\\\(255\\\\2c 0\\\\2c 127\\\\2c 0\\\\.5\\\\)\\\\2c _transparent_40\\\\%\\\\)\\\\]{\\n  background-image: radial-gradient(circle at center, rgba(255,0,127,0.5), transparent 40%);\\n}\\n.bg-gradient-to-br{\\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\\n}\\n.bg-gradient-to-r{\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n}\\n.from-\\\\[\\\\#FF007F\\\\]{\\n  --tw-gradient-from: #FF007F var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(255 0 127 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-400\\\\/20{\\n  --tw-gradient-from: rgb(96 165 250 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-500{\\n  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-900\\\\/20{\\n  --tw-gradient-from: rgb(30 58 138 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-cyan-400{\\n  --tw-gradient-from: #22d3ee var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 211 238 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-cyan-400\\\\/20{\\n  --tw-gradient-from: rgb(34 211 238 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 211 238 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-cyan-500{\\n  --tw-gradient-from: #06b6d4 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(6 182 212 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-cyan-900\\\\/20{\\n  --tw-gradient-from: rgb(22 78 99 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(22 78 99 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-gray-800\\\\/40{\\n  --tw-gradient-from: rgb(31 41 55 / 0.4) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-gray-800\\\\/60{\\n  --tw-gradient-from: rgb(31 41 55 / 0.6) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-gray-900\\\\/40{\\n  --tw-gradient-from: rgb(17 24 39 / 0.4) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-green-400\\\\/20{\\n  --tw-gradient-from: rgb(74 222 128 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(74 222 128 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-green-500{\\n  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-green-900\\\\/20{\\n  --tw-gradient-from: rgb(20 83 45 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(20 83 45 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-purple-400\\\\/20{\\n  --tw-gradient-from: rgb(192 132 252 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(192 132 252 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-purple-500{\\n  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-purple-900\\\\/20{\\n  --tw-gradient-from: rgb(88 28 135 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-purple-900\\\\/30{\\n  --tw-gradient-from: rgb(88 28 135 / 0.3) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-red-500{\\n  --tw-gradient-from: #ef4444 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-red-900\\\\/20{\\n  --tw-gradient-from: rgb(127 29 29 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(127 29 29 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-white{\\n  --tw-gradient-from: #fff var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-yellow-400\\\\/20{\\n  --tw-gradient-from: rgb(250 204 21 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(250 204 21 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-yellow-500{\\n  --tw-gradient-from: #eab308 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(234 179 8 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-yellow-900\\\\/20{\\n  --tw-gradient-from: rgb(113 63 18 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(113 63 18 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.to-\\\\[\\\\#d6006a\\\\]{\\n  --tw-gradient-to: #d6006a var(--tw-gradient-to-position);\\n}\\n.to-blue-500{\\n  --tw-gradient-to: #3b82f6 var(--tw-gradient-to-position);\\n}\\n.to-blue-500\\\\/20{\\n  --tw-gradient-to: rgb(59 130 246 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-blue-600{\\n  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);\\n}\\n.to-blue-600\\\\/20{\\n  --tw-gradient-to: rgb(37 99 235 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-blue-800\\\\/20{\\n  --tw-gradient-to: rgb(30 64 175 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-blue-900\\\\/20{\\n  --tw-gradient-to: rgb(30 58 138 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-blue-900\\\\/30{\\n  --tw-gradient-to: rgb(30 58 138 / 0.3) var(--tw-gradient-to-position);\\n}\\n.to-cyan-600{\\n  --tw-gradient-to: #0891b2 var(--tw-gradient-to-position);\\n}\\n.to-cyan-800\\\\/20{\\n  --tw-gradient-to: rgb(21 94 117 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-cyan-900\\\\/20{\\n  --tw-gradient-to: rgb(22 78 99 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-emerald-600{\\n  --tw-gradient-to: #059669 var(--tw-gradient-to-position);\\n}\\n.to-emerald-900\\\\/20{\\n  --tw-gradient-to: rgb(6 78 59 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-fuchsia-500{\\n  --tw-gradient-to: #d946ef var(--tw-gradient-to-position);\\n}\\n.to-gray-700\\\\/40{\\n  --tw-gradient-to: rgb(55 65 81 / 0.4) var(--tw-gradient-to-position);\\n}\\n.to-gray-700\\\\/60{\\n  --tw-gradient-to: rgb(55 65 81 / 0.6) var(--tw-gradient-to-position);\\n}\\n.to-gray-800\\\\/40{\\n  --tw-gradient-to: rgb(31 41 55 / 0.4) var(--tw-gradient-to-position);\\n}\\n.to-gray-900\\\\/60{\\n  --tw-gradient-to: rgb(17 24 39 / 0.6) var(--tw-gradient-to-position);\\n}\\n.to-green-600\\\\/20{\\n  --tw-gradient-to: rgb(22 163 74 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-green-800\\\\/20{\\n  --tw-gradient-to: rgb(22 101 52 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-orange-600{\\n  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);\\n}\\n.to-orange-900\\\\/20{\\n  --tw-gradient-to: rgb(124 45 18 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-pink-600{\\n  --tw-gradient-to: #db2777 var(--tw-gradient-to-position);\\n}\\n.to-pink-900\\\\/20{\\n  --tw-gradient-to: rgb(131 24 67 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-purple-600{\\n  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);\\n}\\n.to-purple-600\\\\/20{\\n  --tw-gradient-to: rgb(147 51 234 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-purple-800\\\\/20{\\n  --tw-gradient-to: rgb(107 33 168 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-purple-900\\\\/20{\\n  --tw-gradient-to: rgb(88 28 135 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-transparent{\\n  --tw-gradient-to: transparent var(--tw-gradient-to-position);\\n}\\n.to-yellow-600\\\\/20{\\n  --tw-gradient-to: rgb(202 138 4 / 0.2) var(--tw-gradient-to-position);\\n}\\n.object-cover{\\n  -o-object-fit: cover;\\n     object-fit: cover;\\n}\\n.p-0{\\n  padding: 0px;\\n}\\n.p-1{\\n  padding: 0.25rem;\\n}\\n.p-2{\\n  padding: 0.5rem;\\n}\\n.p-3{\\n  padding: 0.75rem;\\n}\\n.p-4{\\n  padding: 1rem;\\n}\\n.p-6{\\n  padding: 1.5rem;\\n}\\n.px-2{\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\n.px-3{\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\n.px-4{\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n.py-1{\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\n.py-1\\\\.5{\\n  padding-top: 0.375rem;\\n  padding-bottom: 0.375rem;\\n}\\n.py-2{\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\n.pb-1{\\n  padding-bottom: 0.25rem;\\n}\\n.pt-2{\\n  padding-top: 0.5rem;\\n}\\n.pt-4{\\n  padding-top: 1rem;\\n}\\n.text-left{\\n  text-align: left;\\n}\\n.text-center{\\n  text-align: center;\\n}\\n.text-right{\\n  text-align: right;\\n}\\n.font-poppins{\\n  font-family: Poppins, sans-serif;\\n}\\n.text-2xl{\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\n.text-3xl{\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\n.text-\\\\[10px\\\\]{\\n  font-size: 10px;\\n}\\n.text-base{\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\n.text-lg{\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\n.text-sm{\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n.text-xl{\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n.text-xs{\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\n.font-bold{\\n  font-weight: 700;\\n}\\n.font-medium{\\n  font-weight: 500;\\n}\\n.font-normal{\\n  font-weight: 400;\\n}\\n.font-semibold{\\n  font-weight: 600;\\n}\\n.uppercase{\\n  text-transform: uppercase;\\n}\\n.capitalize{\\n  text-transform: capitalize;\\n}\\n.leading-relaxed{\\n  line-height: 1.625;\\n}\\n.tracking-wider{\\n  letter-spacing: 0.05em;\\n}\\n.text-\\\\[\\\\#A0A0B0\\\\]{\\n  --tw-text-opacity: 1;\\n  color: rgb(160 160 176 / var(--tw-text-opacity, 1));\\n}\\n.text-\\\\[\\\\#E0E0E0\\\\]{\\n  --tw-text-opacity: 1;\\n  color: rgb(224 224 224 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\\n}\\n.text-brand-cyan{\\n  --tw-text-opacity: 1;\\n  color: rgb(0 255 255 / var(--tw-text-opacity, 1));\\n}\\n.text-cyan-300{\\n  --tw-text-opacity: 1;\\n  color: rgb(103 232 249 / var(--tw-text-opacity, 1));\\n}\\n.text-cyan-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(34 211 238 / var(--tw-text-opacity, 1));\\n}\\n.text-emerald-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(52 211 153 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-300{\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-500{\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-900{\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\n.text-green-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\\n}\\n.text-indigo-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(129 140 248 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(251 146 60 / var(--tw-text-opacity, 1));\\n}\\n.text-pink-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(244 114 182 / var(--tw-text-opacity, 1));\\n}\\n.text-purple-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(192 132 252 / var(--tw-text-opacity, 1));\\n}\\n.text-red-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\\n}\\n.text-teal-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(45 212 191 / var(--tw-text-opacity, 1));\\n}\\n.text-white{\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\\n}\\n.opacity-0{\\n  opacity: 0;\\n}\\n.opacity-30{\\n  opacity: 0.3;\\n}\\n.opacity-50{\\n  opacity: 0.5;\\n}\\n.opacity-90{\\n  opacity: 0.9;\\n}\\n.shadow-inner{\\n  --tw-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: inset 0 2px 4px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-lg{\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-md{\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.blur-sm{\\n  --tw-blur: blur(4px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.drop-shadow{\\n  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.filter{\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.backdrop-blur-sm{\\n  --tw-backdrop-blur: blur(4px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.backdrop-blur-xl{\\n  --tw-backdrop-blur: blur(24px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.backdrop-filter{\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.transition{\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-all{\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-colors{\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-opacity{\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-transform{\\n  transition-property: transform;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.duration-200{\\n  transition-duration: 200ms;\\n}\\n.duration-300{\\n  transition-duration: 300ms;\\n}\\n.duration-500{\\n  transition-duration: 500ms;\\n}\\n.ease-in-out{\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.ease-out{\\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n}\\n\\n/* CSS Custom Properties for Responsive Scaling - Optimized for Content Fit */\\n:root {\\n  --scale-factor: 1;\\n  --content-scale: 3;\\n  --base-font-size: calc(14px * var(--content-scale));\\n  --base-spacing: calc(6px * var(--content-scale));\\n  --base-border-radius: calc(8px * var(--content-scale));\\n  --base-icon-size: calc(20px * var(--content-scale));\\n  --base-button-height: calc(36px * var(--content-scale));\\n  --base-card-padding: calc(1px * var(--content-scale));\\n  --base-gap: calc(6px * var(--content-scale));\\n  --header-height: calc(60px * var(--content-scale));\\n  --footer-height: calc(50px * var(--content-scale));\\n  --sidebar-width: calc(80px * var(--content-scale));\\n\\n  /* HUD/HDR Color Palette */\\n  --hud-bg-primary: #311861;\\n  --hud-bg-secondary: #22184f;\\n  --hud-bg-panel: rgba(15, 20, 25, 0.85);\\n  --hud-bg-card: rgba(20, 30, 45, 0.6);\\n  --hud-border: rgba(0, 207, 255, 0.2);\\n  --hud-border-glow: rgba(0, 207, 255, 0.4);\\n\\n  /* HUD Accent Colors */\\n  --hud-neon-blue: #00cfff;\\n  --hud-aqua-green: #00ffbd;\\n  --hud-energy-orange: #ffa500;\\n  --hud-electric-purple: #bc13fe;\\n  --hud-deep-red: #ff4b4b;\\n  --hud-bright-white: #ffffff;\\n  --hud-silver: #b0b8c4;\\n\\n  /* Glassmorphism Effects */\\n  --glass-bg: rgba(255, 255, 255, 0.05);\\n  --glass-border: rgba(255, 255, 255, 0.1);\\n  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\\n}\\n\\n/* Responsive scaling variables - now handled by --content-scale */\\n@media (max-width: 1920px) and (min-width: 1600px) {\\n  :root {\\n    --scale-factor: 0.9;\\n    --content-scale: 0.9;\\n  }\\n}\\n\\n@media (max-width: 1599px) and (min-width: 1400px) {\\n  :root {\\n    --scale-factor: 0.8;\\n    --content-scale: 0.8;\\n  }\\n}\\n\\n@media (max-width: 1399px) and (min-width: 1200px) {\\n  :root {\\n    --scale-factor: 0.7;\\n    --content-scale: 0.7;\\n  }\\n}\\n\\n@media (max-width: 1199px) and (min-width: 1000px) {\\n  :root {\\n    --scale-factor: 0.6;\\n    --content-scale: 0.6;\\n  }\\n}\\n\\n@media (max-width: 999px) {\\n  :root {\\n    --scale-factor: 0.5;\\n    --content-scale: 0.5;\\n  }\\n}\\n\\n/* Ensure base styles are applied */\\n* {\\n  box-sizing: border-box;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nhtml, body {\\n  height: 100vh;\\n  width: 100vw;\\n  overflow: hidden;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nbody {\\n  font-family: 'Poppins', sans-serif;\\n  font-size: var(--base-font-size);\\n  line-height: 1.5;\\n}\\n\\n#__next {\\n  height: 100vh;\\n  width: 100vw;\\n  overflow: hidden;\\n}\\n\\n.main-background {\\n  background: linear-gradient(135deg, var(--hud-bg-primary) 0%, var(--hud-bg-secondary) 50%, #05080e 100%);\\n  background-attachment: fixed;\\n  position: relative;\\n}\\n\\n.main-background::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(ellipse at 20% 30%, rgba(209, 160, 255, 0.1) 0%, transparent 50%),\\n              radial-gradient(ellipse at 80% 70%, rgba(188, 19, 254, 0.08) 0%, transparent 50%),\\n              radial-gradient(ellipse at 50% 50%, rgba(41, 52, 95, 0.05) 0%, transparent 70%);\\n  pointer-events: none;\\n}\\n\\n/* Global Responsive Classes */\\n.responsive-text {\\n  font-size: var(--base-font-size);\\n}\\n\\n.responsive-text-sm {\\n  font-size: calc(var(--base-font-size) * 0.875);\\n}\\n\\n.responsive-text-xs {\\n  font-size: calc(var(--base-font-size) * 0.75);\\n}\\n\\n.responsive-text-lg {\\n  font-size: calc(var(--base-font-size) * 1.125);\\n}\\n\\n.responsive-text-xl {\\n  font-size: calc(var(--base-font-size) * 1.25);\\n}\\n\\n.responsive-text-2xl {\\n  font-size: calc(var(--base-font-size) * 1.5);\\n}\\n\\n.responsive-spacing {\\n  padding: var(--base-spacing);\\n}\\n\\n.responsive-spacing-sm {\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.responsive-spacing-lg {\\n  padding: calc(var(--base-spacing) * 1.5);\\n}\\n\\n.responsive-gap {\\n  gap: var(--base-gap);\\n}\\n\\n.responsive-gap-sm {\\n  gap: calc(var(--base-gap) * 0.5);\\n}\\n\\n.responsive-gap-lg {\\n  gap: calc(var(--base-gap) * 1.5);\\n}\\n\\n.responsive-border-radius {\\n  border-radius: var(--base-border-radius);\\n}\\n\\n.responsive-icon {\\n  width: var(--base-icon-size);\\n  height: var(--base-icon-size);\\n}\\n\\n.responsive-button {\\n  height: var(--base-button-height);\\n  padding: 0 var(--base-spacing);\\n  font-size: var(--base-font-size);\\n  border-radius: var(--base-border-radius);\\n}\\n\\n.responsive-card {\\n  padding: var(--base-card-padding);\\n  border-radius: calc(var(--base-border-radius) * 1.5);\\n}\\n\\n/* Enhanced HUD Card System */\\n.hud-card {\\n  background: var(--hud-bg-card);\\n  border: 1px solid var(--hud-border);\\n  border-radius: calc(var(--base-border-radius) * 1.5);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  box-shadow: var(--glass-shadow);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.hud-card::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(90deg, transparent, var(--hud-neon-blue), transparent);\\n  opacity: 0.6;\\n}\\n\\n.hud-card:hover {\\n  border-color: var(--hud-border-glow);\\n  box-shadow: 0 8px 40px rgba(0, 207, 255, 0.15), var(--glass-shadow);\\n  transform: translateY(-2px);\\n}\\n\\n/* Compact HUD card padding */\\n.hud-card .ant-card-head {\\n  padding: calc(var(--base-spacing) * 0.75) calc(var(--base-spacing) * 1.5) !important;\\n  min-height: auto !important;\\n}\\n\\n.hud-card .ant-card-body {\\n  padding: calc(var(--base-spacing) * 1) !important;\\n}\\n\\n/* Compact metric cards */\\n.metric-card {\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  border-radius: var(--base-border-radius);\\n  padding: calc(var(--base-spacing) * 0.5);\\n  text-align: center;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n  min-height: auto;\\n  height: -moz-fit-content;\\n  height: fit-content;\\n}\\n\\n.metric-card::after {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(0, 207, 255, 0.05) 0%, rgba(0, 255, 189, 0.05) 100%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.metric-card:hover::after {\\n  opacity: 1;\\n}\\n\\n.metric-card:hover {\\n  border-color: var(--hud-neon-blue);\\n  box-shadow: 0 4px 20px rgba(0, 207, 255, 0.2);\\n  transform: scale(1.02);\\n}\\n\\n/* Compact metric card text */\\n.metric-card p {\\n  margin-bottom: calc(var(--base-spacing) * 0.25) !important;\\n  line-height: 1.2;\\n}\\n\\n.metric-card .text-xl {\\n  font-size: calc(var(--base-font-size) * 1.2) !important;\\n  line-height: 1.1;\\n}\\n\\n/* Compact metric card icons */\\n.metric-card .w-10.h-10 {\\n  width: calc(var(--base-icon-size) * 1.5) !important;\\n  height: calc(var(--base-icon-size) * 1.5) !important;\\n  margin-bottom: calc(var(--base-spacing) * 0.5) !important;\\n}\\n\\n/* Status badges with glow effects */\\n.status-badge {\\n  padding: calc(var(--base-spacing) * 0.5) calc(var(--base-spacing) * 1);\\n  border-radius: calc(var(--base-border-radius) * 2);\\n  font-size: calc(10px * var(--content-scale));\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  border: 1px solid;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.status-badge.active {\\n  background: rgba(0, 255, 189, 0.2);\\n  color: var(--hud-aqua-green);\\n  border-color: var(--hud-aqua-green);\\n  box-shadow: 0 0 10px rgba(0, 255, 189, 0.3);\\n}\\n\\n.status-badge.beta {\\n  background: rgba(255, 165, 0, 0.2);\\n  color: var(--hud-energy-orange);\\n  border-color: var(--hud-energy-orange);\\n  box-shadow: 0 0 10px rgba(255, 165, 0, 0.3);\\n}\\n\\n.status-badge.live {\\n  background: rgba(0, 207, 255, 0.2);\\n  color: var(--hud-neon-blue);\\n  border-color: var(--hud-neon-blue);\\n  box-shadow: 0 0 10px rgba(0, 207, 255, 0.3);\\n}\\n\\n.status-badge.testing {\\n  background: rgba(188, 19, 254, 0.2);\\n  color: var(--hud-electric-purple);\\n  border-color: var(--hud-electric-purple);\\n  box-shadow: 0 0 10px rgba(188, 19, 254, 0.3);\\n}\\n\\n/* Enhanced Header Styling */\\n.header-hud {\\n  background: var(--hud-bg-panel);\\n  border: 1px solid var(--hud-border);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n  position: relative;\\n}\\n\\n.header-hud::before {\\n  content: '';\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(90deg, transparent, var(--hud-neon-blue), transparent);\\n  opacity: 0.4;\\n}\\n\\n/* Enhanced Department Buttons */\\n.dept-button {\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.dept-button:hover {\\n  background: rgba(0, 207, 255, 0.1);\\n  border-color: var(--hud-border-glow);\\n  box-shadow: 0 4px 20px rgba(0, 207, 255, 0.2);\\n  transform: scale(1.05);\\n}\\n\\n.dept-button.active {\\n  background: linear-gradient(135deg, rgba(0, 207, 255, 0.2) 0%, rgba(0, 255, 189, 0.1) 100%);\\n  border-color: var(--hud-neon-blue);\\n  box-shadow: 0 0 20px rgba(0, 207, 255, 0.3);\\n}\\n\\n.dept-button.active::after {\\n  content: '';\\n  position: absolute;\\n  inset: 0;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\\n  pointer-events: none;\\n}\\n\\n/* Dashboard Auto-Scale - Scales ALL content including text, icons, spacing */\\n.dashboard-auto-scale {\\n  transform-origin: top left;\\n  width: 100vw !important;\\n  height: 100vh !important;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  overflow: hidden;\\n  transition: transform 0.3s ease-out;\\n}\\n\\n/* Scale everything - content, text, icons, spacing */\\n@media (max-width: 1920px) and (min-width: 1600px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.9);\\n    width: calc(100vw / 0.9) !important;\\n    height: calc(100vh / 0.9) !important;\\n  }\\n  :root {\\n    --content-scale: 0.9;\\n  }\\n}\\n\\n@media (max-width: 1599px) and (min-width: 1400px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.8);\\n    width: calc(100vw / 0.8) !important;\\n    height: calc(100vh / 0.8) !important;\\n  }\\n  :root {\\n    --content-scale: 0.8;\\n  }\\n}\\n\\n@media (max-width: 1399px) and (min-width: 1200px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.7);\\n    width: calc(100vw / 0.7) !important;\\n    height: calc(100vh / 0.7) !important;\\n  }\\n  :root {\\n    --content-scale: 0.7;\\n  }\\n}\\n\\n@media (max-width: 1199px) and (min-width: 1000px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.6);\\n    width: calc(100vw / 0.6) !important;\\n    height: calc(100vh / 0.6) !important;\\n  }\\n  :root {\\n    --content-scale: 0.6;\\n  }\\n}\\n\\n@media (max-width: 999px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.5);\\n    width: calc(100vw / 0.5) !important;\\n    height: calc(100vh / 0.5) !important;\\n  }\\n  :root {\\n    --content-scale: 0.5;\\n  }\\n}\\n\\n/* Default content scale */\\n:root {\\n  --content-scale: 1;\\n}\\n\\n/* Universal content scaling - applies to ALL elements */\\n* {\\n  font-size: inherit;\\n}\\n\\n/* Scale all text elements in main content */\\n.main-section h1, .main-section h2, .main-section h3, .main-section h4, .main-section h5, .main-section h6,\\n.main-section p, .main-section span, .main-section div, .main-section button,\\n.main-section input, .main-section textarea, .main-section select, .main-section label, .main-section a {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale card content specifically */\\n.card-content * {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale chart text and numbers */\\n.recharts-text, .recharts-label, .chart-text, .metric-value, .metric-label {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale all icons and images */\\nsvg, img, .icon {\\n  width: calc(var(--base-icon-size));\\n  height: calc(var(--base-icon-size));\\n}\\n\\n/* Scale all spacing */\\n.p-1 { padding: calc(4px * var(--content-scale)) !important; }\\n.p-2 { padding: calc(8px * var(--content-scale)) !important; }\\n.p-3 { padding: calc(12px * var(--content-scale)) !important; }\\n.p-4 { padding: calc(16px * var(--content-scale)) !important; }\\n.p-5 { padding: calc(20px * var(--content-scale)) !important; }\\n.p-6 { padding: calc(24px * var(--content-scale)) !important; }\\n\\n.m-1 { margin: calc(4px * var(--content-scale)) !important; }\\n.m-2 { margin: calc(8px * var(--content-scale)) !important; }\\n.m-3 { margin: calc(12px * var(--content-scale)) !important; }\\n.m-4 { margin: calc(16px * var(--content-scale)) !important; }\\n.m-5 { margin: calc(20px * var(--content-scale)) !important; }\\n.m-6 { margin: calc(24px * var(--content-scale)) !important; }\\n\\n/* Scale gaps */\\n.gap-1 { gap: calc(4px * var(--content-scale)) !important; }\\n.gap-2 { gap: calc(8px * var(--content-scale)) !important; }\\n.gap-3 { gap: calc(12px * var(--content-scale)) !important; }\\n.gap-4 { gap: calc(16px * var(--content-scale)) !important; }\\n.gap-5 { gap: calc(20px * var(--content-scale)) !important; }\\n.gap-6 { gap: calc(24px * var(--content-scale)) !important; }\\n\\n/* Scale border radius */\\n.rounded { border-radius: calc(4px * var(--content-scale)) !important; }\\n.rounded-md { border-radius: calc(6px * var(--content-scale)) !important; }\\n.rounded-lg { border-radius: calc(8px * var(--content-scale)) !important; }\\n.rounded-xl { border-radius: calc(12px * var(--content-scale)) !important; }\\n.rounded-2xl { border-radius: calc(16px * var(--content-scale)) !important; }\\n\\n@keyframes fade-in-up {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n.animate-fade-in-up {\\n  animation: fade-in-up 0.3s ease-out forwards;\\n}\\n\\n@keyframes fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n.animate-fadeIn {\\n  animation: fadeIn 0.5s ease-in-out;\\n}\\n\\n/* Override Tailwind text sizes with content scaling */\\n.text-xs { font-size: calc(12px * var(--content-scale)) !important; }\\n.text-sm { font-size: calc(14px * var(--content-scale)) !important; }\\n.text-base { font-size: calc(16px * var(--content-scale)) !important; }\\n.text-lg { font-size: calc(18px * var(--content-scale)) !important; }\\n.text-xl { font-size: calc(20px * var(--content-scale)) !important; }\\n.text-2xl { font-size: calc(24px * var(--content-scale)) !important; }\\n.text-3xl { font-size: calc(30px * var(--content-scale)) !important; }\\n.text-4xl { font-size: calc(36px * var(--content-scale)) !important; }\\n\\n/* Override Tailwind width/height for icons */\\n.w-3 { width: calc(12px * var(--content-scale)) !important; }\\n.w-4 { width: calc(16px * var(--content-scale)) !important; }\\n.w-5 { width: calc(20px * var(--content-scale)) !important; }\\n.w-6 { width: calc(24px * var(--content-scale)) !important; }\\n.w-8 { width: calc(32px * var(--content-scale)) !important; }\\n.w-10 { width: calc(40px * var(--content-scale)) !important; }\\n.w-12 { width: calc(48px * var(--content-scale)) !important; }\\n\\n.h-3 { height: calc(12px * var(--content-scale)) !important; }\\n.h-4 { height: calc(16px * var(--content-scale)) !important; }\\n.h-5 { height: calc(20px * var(--content-scale)) !important; }\\n.h-6 { height: calc(24px * var(--content-scale)) !important; }\\n.h-8 { height: calc(32px * var(--content-scale)) !important; }\\n.h-10 { height: calc(40px * var(--content-scale)) !important; }\\n.h-12 { height: calc(48px * var(--content-scale)) !important; }\\n\\n/* Optimized Layout Classes for Perfect Content Fit */\\n.main-layout {\\n  height: 100vh;\\n  width: 100vw;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.content-area {\\n  flex: 1;\\n  display: flex;\\n  min-height: 0;\\n  overflow: hidden;\\n}\\n\\n.main-content {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 0;\\n  overflow: hidden;\\n}\\n\\n.header-section {\\n  height: var(--header-height);\\n  flex-shrink: 0;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.main-section {\\n  flex: 1;\\n  min-height: 0;\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.footer-section {\\n  height: calc(var(--footer-height) * 1.5);\\n  flex-shrink: 0;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.sidebar-left {\\n  width: var(--sidebar-width);\\n  flex-shrink: 0;\\n}\\n\\n.sidebar-right {\\n  width: var(--sidebar-width);\\n  flex-shrink: 0;\\n}\\n\\n/* Enhanced Dashboard Grid for No-Scroll Layout */\\n.dashboard-grid {\\n  display: grid;\\n  gap: calc(var(--base-gap) * 0.75);\\n  height: 100%;\\n  overflow: hidden;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.dashboard-grid-teacher {\\n  grid-template-rows: auto 1fr auto;\\n  grid-template-columns: 1fr;\\n}\\n\\n.dashboard-grid-school {\\n  grid-template-rows: auto 1fr;\\n  grid-template-columns: 1fr 1fr;\\n  gap: calc(var(--base-gap) * 1);\\n}\\n\\n/* Compact content areas */\\n.content-compact {\\n  max-height: calc(100vh - var(--header-height) - var(--footer-height) - calc(var(--base-spacing) * 4));\\n  overflow-y: auto;\\n}\\n\\n.content-no-scroll {\\n  height: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n/* Enhanced Animations */\\n@keyframes pulse-glow {\\n  0%, 100% {\\n    box-shadow: 0 0 5px rgba(0, 207, 255, 0.3);\\n  }\\n  50% {\\n    box-shadow: 0 0 20px rgba(0, 207, 255, 0.6), 0 0 30px rgba(0, 207, 255, 0.4);\\n  }\\n}\\n\\n@keyframes slide-in-up {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes fade-in-scale {\\n  from {\\n    opacity: 0;\\n    transform: scale(0.95);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n}\\n\\n.animate-pulse-glow {\\n  animation: pulse-glow 2s ease-in-out infinite;\\n}\\n\\n.animate-slide-in-up {\\n  animation: slide-in-up 0.5s ease-out;\\n}\\n\\n.animate-fade-in-scale {\\n  animation: fade-in-scale 0.3s ease-out;\\n}\\n\\n/* Hover Effects */\\n.hover-lift {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.hover-lift:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 207, 255, 0.2);\\n}\\n\\n/* Progress Bar Animations */\\n.progress-bar {\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.progress-bar::after {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\\n  animation: shimmer 2s infinite;\\n}\\n\\n@keyframes shimmer {\\n  0% {\\n    left: -100%;\\n  }\\n  100% {\\n    left: 100%;\\n  }\\n}\\n\\n/* Optimized Card Grid */\\n.card-grid {\\n  display: grid;\\n  gap: var(--base-gap);\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n.card-grid-2 {\\n  grid-template-columns: 1fr 1fr;\\n}\\n\\n.card-grid-3 {\\n  grid-template-columns: 1fr 1fr 1fr;\\n}\\n\\n.card-grid-4 {\\n  grid-template-columns: 1fr 1fr 1fr 1fr;\\n}\\n\\n.card-grid-auto {\\n  grid-template-columns: repeat(auto-fit, minmax(calc(250px * var(--content-scale)), 1fr));\\n}\\n\\n/* Optimized Card Sizing */\\n.card-compact {\\n  padding: calc(var(--base-card-padding) * 0.75);\\n  min-height: calc(120px * var(--content-scale));\\n}\\n\\n.card-standard {\\n  padding: var(--base-card-padding);\\n  min-height: calc(160px * var(--content-scale));\\n}\\n\\n.card-large {\\n  padding: calc(var(--base-card-padding) * 1.25);\\n  min-height: calc(200px * var(--content-scale));\\n}\\n\\n/* Neumorphic HUD Subnav Styles */\\n.subnav-hud {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  padding: calc(var(--base-spacing) * 1.5) 0;\\n  margin: 0 auto;\\n  max-width: calc(72px * 8 + var(--base-gap) * 7); /* 8 buttons with gaps */\\n}\\n\\n.subnav-button {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  width: calc(72px * var(--content-scale));\\n  height: calc(72px * var(--content-scale));\\n  margin: 0 calc(var(--base-gap) * 0.5);\\n  border-radius: calc(16px * var(--content-scale));\\n  background: linear-gradient(145deg, #2a2a3a, #1a1a2a);\\n  box-shadow:\\n    calc(8px * var(--content-scale)) calc(8px * var(--content-scale)) calc(16px * var(--content-scale)) rgba(0, 0, 0, 0.4),\\n    calc(-4px * var(--content-scale)) calc(-4px * var(--content-scale)) calc(8px * var(--content-scale)) rgba(255, 255, 255, 0.05);\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.subnav-button:hover {\\n  transform: translateY(calc(-2px * var(--content-scale)));\\n  box-shadow:\\n    calc(12px * var(--content-scale)) calc(12px * var(--content-scale)) calc(24px * var(--content-scale)) rgba(0, 0, 0, 0.5),\\n    calc(-6px * var(--content-scale)) calc(-6px * var(--content-scale)) calc(12px * var(--content-scale)) rgba(255, 255, 255, 0.08);\\n}\\n\\n.subnav-button.active {\\n  background: linear-gradient(145deg, #3a4a6a, #2a3a5a);\\n  box-shadow:\\n    inset calc(4px * var(--content-scale)) calc(4px * var(--content-scale)) calc(8px * var(--content-scale)) rgba(0, 0, 0, 0.3),\\n    inset calc(-2px * var(--content-scale)) calc(-2px * var(--content-scale)) calc(4px * var(--content-scale)) rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(100, 200, 255, 0.3);\\n}\\n\\n.subnav-icon {\\n  width: calc(24px * var(--content-scale));\\n  height: calc(24px * var(--content-scale));\\n  margin-bottom: calc(4px * var(--content-scale));\\n  color: #a0a0b0;\\n  transition: color 0.3s ease;\\n}\\n\\n.subnav-button:hover .subnav-icon,\\n.subnav-button.active .subnav-icon {\\n  color: #64b5f6;\\n}\\n\\n.subnav-label {\\n  font-size: calc(10px * var(--content-scale));\\n  font-weight: 500;\\n  color: #a0a0b0;\\n  text-align: center;\\n  line-height: 1.2;\\n  transition: color 0.3s ease;\\n}\\n\\n.subnav-button:hover .subnav-label,\\n.subnav-button.active .subnav-label {\\n  color: #64b5f6;\\n}\\n\\n/* HUD Glow Effect */\\n.subnav-button::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(circle at center, rgba(100, 181, 246, 0.1) 0%, transparent 70%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n  border-radius: inherit;\\n}\\n\\n.subnav-button:hover::before,\\n.subnav-button.active::before {\\n  opacity: 1;\\n}\\n\\n/* Enhanced HUD Vertical Layout for Left Sidebar */\\n.subnav-hud-vertical {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: stretch;\\n  justify-content: flex-start;\\n  gap: calc(var(--base-gap) * 0.75);\\n  padding: calc(var(--base-spacing) * 1);\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n/* Enhanced subnav button with glassmorphism */\\n.subnav-hud-vertical .subnav-button {\\n  width: 100%;\\n  height: calc(42px * var(--content-scale));\\n  margin: 0;\\n  flex-shrink: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  padding: calc(var(--base-spacing) * 1) calc(var(--base-spacing) * 1.5);\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  border-radius: var(--base-border-radius);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover {\\n  background: rgba(0, 207, 255, 0.1);\\n  border-color: var(--hud-border-glow);\\n  transform: translateX(4px);\\n  box-shadow: 0 4px 20px rgba(0, 207, 255, 0.2);\\n}\\n\\n.subnav-hud-vertical .subnav-button.active {\\n  background: linear-gradient(135deg, rgba(0, 207, 255, 0.2) 0%, rgba(0, 255, 189, 0.1) 100%);\\n  border-color: var(--hud-neon-blue);\\n  box-shadow: 0 0 20px rgba(0, 207, 255, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n}\\n\\n/* Enhanced subnav icon */\\n.subnav-hud-vertical .subnav-icon {\\n  width: calc(18px * var(--content-scale));\\n  height: calc(18px * var(--content-scale));\\n  margin-right: calc(var(--base-spacing) * 1.5);\\n  color: var(--hud-silver);\\n  transition: all 0.3s ease;\\n  flex-shrink: 0;\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover .subnav-icon {\\n  color: var(--hud-neon-blue);\\n  filter: drop-shadow(0 0 8px rgba(0, 207, 255, 0.6));\\n}\\n\\n.subnav-hud-vertical .subnav-button.active .subnav-icon {\\n  color: var(--hud-bright-white);\\n  filter: drop-shadow(0 0 12px rgba(0, 207, 255, 0.8));\\n}\\n\\n/* Enhanced subnav label - full text display */\\n.subnav-hud-vertical .subnav-label {\\n  color: var(--hud-silver);\\n  font-size: calc(12px * var(--content-scale));\\n  font-weight: 500;\\n  line-height: 1.2;\\n  white-space: nowrap;\\n  transition: all 0.3s ease;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover .subnav-label {\\n  color: var(--hud-bright-white);\\n  text-shadow: 0 0 8px rgba(0, 207, 255, 0.6);\\n}\\n\\n.subnav-hud-vertical .subnav-button.active .subnav-label {\\n  color: var(--hud-bright-white);\\n  font-weight: 600;\\n  text-shadow: 0 0 12px rgba(0, 207, 255, 0.8);\\n}\\n\\n.hover\\\\:scale-105:hover{\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.hover\\\\:border-cyan-400\\\\/50:hover{\\n  border-color: rgb(34 211 238 / 0.5);\\n}\\n\\n.hover\\\\:border-cyan-400\\\\/60:hover{\\n  border-color: rgb(34 211 238 / 0.6);\\n}\\n\\n.hover\\\\:border-gray-500\\\\/50:hover{\\n  border-color: rgb(107 114 128 / 0.5);\\n}\\n\\n.hover\\\\:bg-cyan-500\\\\/50:hover{\\n  background-color: rgb(6 182 212 / 0.5);\\n}\\n\\n.hover\\\\:bg-gray-700\\\\/30:hover{\\n  background-color: rgb(55 65 81 / 0.3);\\n}\\n\\n.hover\\\\:bg-gray-700\\\\/50:hover{\\n  background-color: rgb(55 65 81 / 0.5);\\n}\\n\\n.hover\\\\:bg-gray-700\\\\/60:hover{\\n  background-color: rgb(55 65 81 / 0.6);\\n}\\n\\n.hover\\\\:bg-gray-800\\\\/40:hover{\\n  background-color: rgb(31 41 55 / 0.4);\\n}\\n\\n.hover\\\\:bg-pink-500\\\\/90:hover{\\n  background-color: rgb(236 72 153 / 0.9);\\n}\\n\\n.hover\\\\:bg-white\\\\/30:hover{\\n  background-color: rgb(255 255 255 / 0.3);\\n}\\n\\n.hover\\\\:text-white:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:shadow-lg:hover{\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.hover\\\\:shadow-cyan-400\\\\/20:hover{\\n  --tw-shadow-color: rgb(34 211 238 / 0.2);\\n  --tw-shadow: var(--tw-shadow-colored);\\n}\\n\\n.focus\\\\:outline-none:focus{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n\\n.focus-visible\\\\:ring-1:focus-visible{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n\\n.focus-visible\\\\:ring-2:focus-visible{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n\\n.focus-visible\\\\:ring-brand-cyan:focus-visible{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(0 255 255 / var(--tw-ring-opacity, 1));\\n}\\n\\n.focus-visible\\\\:ring-white:focus-visible{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity, 1));\\n}\\n\\n.focus-visible\\\\:ring-offset-2:focus-visible{\\n  --tw-ring-offset-width: 2px;\\n}\\n\\n.focus-visible\\\\:ring-offset-gray-800:focus-visible{\\n  --tw-ring-offset-color: #1f2937;\\n}\\n\\n.disabled\\\\:cursor-not-allowed:disabled{\\n  cursor: not-allowed;\\n}\\n\\n.disabled\\\\:cursor-wait:disabled{\\n  cursor: wait;\\n}\\n\\n.disabled\\\\:opacity-50:disabled{\\n  opacity: 0.5;\\n}\\n\\n.group:hover .group-hover\\\\:scale-105{\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.group:hover .group-hover\\\\:scale-110{\\n  --tw-scale-x: 1.1;\\n  --tw-scale-y: 1.1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.group:hover .group-hover\\\\:text-cyan-300{\\n  --tw-text-opacity: 1;\\n  color: rgb(103 232 249 / var(--tw-text-opacity, 1));\\n}\\n\\n.group:hover .group-hover\\\\:text-white{\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n\\n.group:hover .group-hover\\\\:opacity-20{\\n  opacity: 0.2;\\n}\\n\\n@media (prefers-reduced-motion: no-preference){\\n\\n  @keyframes pulse{\\n\\n    50%{\\n      opacity: .5;\\n    }\\n  }\\n\\n  .motion-safe\\\\:animate-pulse{\\n    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n  }\\n}\\n\\n@media (min-width: 640px){\\n\\n  .sm\\\\:mr-3{\\n    margin-right: 0.75rem;\\n  }\\n\\n  .sm\\\\:mt-4{\\n    margin-top: 1rem;\\n  }\\n\\n  .sm\\\\:h-10{\\n    height: 2.5rem;\\n  }\\n\\n  .sm\\\\:h-12{\\n    height: 3rem;\\n  }\\n\\n  .sm\\\\:h-16{\\n    height: 4rem;\\n  }\\n\\n  .sm\\\\:h-3{\\n    height: 0.75rem;\\n  }\\n\\n  .sm\\\\:h-36{\\n    height: 9rem;\\n  }\\n\\n  .sm\\\\:h-4{\\n    height: 1rem;\\n  }\\n\\n  .sm\\\\:h-40{\\n    height: 10rem;\\n  }\\n\\n  .sm\\\\:h-48{\\n    height: 12rem;\\n  }\\n\\n  .sm\\\\:h-6{\\n    height: 1.5rem;\\n  }\\n\\n  .sm\\\\:h-8{\\n    height: 2rem;\\n  }\\n\\n  .sm\\\\:w-10{\\n    width: 2.5rem;\\n  }\\n\\n  .sm\\\\:w-12{\\n    width: 3rem;\\n  }\\n\\n  .sm\\\\:w-16{\\n    width: 4rem;\\n  }\\n\\n  .sm\\\\:w-3{\\n    width: 0.75rem;\\n  }\\n\\n  .sm\\\\:w-36{\\n    width: 9rem;\\n  }\\n\\n  .sm\\\\:w-4{\\n    width: 1rem;\\n  }\\n\\n  .sm\\\\:w-40{\\n    width: 10rem;\\n  }\\n\\n  .sm\\\\:w-6{\\n    width: 1.5rem;\\n  }\\n\\n  .sm\\\\:w-8{\\n    width: 2rem;\\n  }\\n\\n  .sm\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .sm\\\\:gap-4{\\n    gap: 1rem;\\n  }\\n\\n  .sm\\\\:gap-6{\\n    gap: 1.5rem;\\n  }\\n\\n  .sm\\\\:p-2{\\n    padding: 0.5rem;\\n  }\\n\\n  .sm\\\\:p-3{\\n    padding: 0.75rem;\\n  }\\n\\n  .sm\\\\:p-4{\\n    padding: 1rem;\\n  }\\n\\n  .sm\\\\:p-6{\\n    padding: 1.5rem;\\n  }\\n\\n  .sm\\\\:px-4{\\n    padding-left: 1rem;\\n    padding-right: 1rem;\\n  }\\n\\n  .sm\\\\:py-2{\\n    padding-top: 0.5rem;\\n    padding-bottom: 0.5rem;\\n  }\\n\\n  .sm\\\\:text-base{\\n    font-size: 1rem;\\n    line-height: 1.5rem;\\n  }\\n\\n  .sm\\\\:text-lg{\\n    font-size: 1.125rem;\\n    line-height: 1.75rem;\\n  }\\n\\n  .sm\\\\:text-sm{\\n    font-size: 0.875rem;\\n    line-height: 1.25rem;\\n  }\\n\\n  .sm\\\\:text-xl{\\n    font-size: 1.25rem;\\n    line-height: 1.75rem;\\n  }\\n\\n  .sm\\\\:text-xs{\\n    font-size: 0.75rem;\\n    line-height: 1rem;\\n  }\\n}\\n\\n@media (min-width: 768px){\\n\\n  .md\\\\:col-span-2{\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .md\\\\:grid-cols-2{\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-4{\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n}\\n\\n@media (min-width: 1024px){\\n\\n  .lg\\\\:col-span-12{\\n    grid-column: span 12 / span 12;\\n  }\\n\\n  .lg\\\\:col-span-2{\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .lg\\\\:col-span-3{\\n    grid-column: span 3 / span 3;\\n  }\\n\\n  .lg\\\\:col-span-4{\\n    grid-column: span 4 / span 4;\\n  }\\n\\n  .lg\\\\:col-span-5{\\n    grid-column: span 5 / span 5;\\n  }\\n\\n  .lg\\\\:col-span-7{\\n    grid-column: span 7 / span 7;\\n  }\\n\\n  .lg\\\\:h-40{\\n    height: 10rem;\\n  }\\n\\n  .lg\\\\:h-48{\\n    height: 12rem;\\n  }\\n\\n  .lg\\\\:w-40{\\n    width: 10rem;\\n  }\\n\\n  .lg\\\\:w-48{\\n    width: 12rem;\\n  }\\n\\n  .lg\\\\:grid-cols-12{\\n    grid-template-columns: repeat(12, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-4{\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:text-xl{\\n    font-size: 1.25rem;\\n    line-height: 1.75rem;\\n  }\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA,qGAAqG;;AAErG;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;;CAAc;;AAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,+HAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,+GAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;;AAAd;EAAA,aAAc;AAAA;AACd;EAAA;AAAoB;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AACpB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,yBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,2EAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yDAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;KAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gDAAmB;EAAnB,6DAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,6EAAmB;EAAnB,iGAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kGAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,gKAAmB;EAAnB,wJAAmB;EAAnB,iLAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;;AAEnB,6EAA6E;AAC7E;EACE,iBAAiB;EACjB,kBAAkB;EAClB,mDAAmD;EACnD,gDAAgD;EAChD,sDAAsD;EACtD,mDAAmD;EACnD,uDAAuD;EACvD,qDAAqD;EACrD,4CAA4C;EAC5C,kDAAkD;EAClD,kDAAkD;EAClD,kDAAkD;;EAElD,0BAA0B;EAC1B,yBAAyB;EACzB,2BAA2B;EAC3B,sCAAsC;EACtC,oCAAoC;EACpC,oCAAoC;EACpC,yCAAyC;;EAEzC,sBAAsB;EACtB,wBAAwB;EACxB,yBAAyB;EACzB,4BAA4B;EAC5B,8BAA8B;EAC9B,uBAAuB;EACvB,2BAA2B;EAC3B,qBAAqB;;EAErB,0BAA0B;EAC1B,qCAAqC;EACrC,wCAAwC;EACxC,6CAA6C;AAC/C;;AAEA,kEAAkE;AAClE;EACE;IACE,mBAAmB;IACnB,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,mBAAmB;IACnB,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,mBAAmB;IACnB,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,mBAAmB;IACnB,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,mBAAmB;IACnB,oBAAoB;EACtB;AACF;;AAEA,mCAAmC;AACnC;EACE,sBAAsB;EACtB,SAAS;EACT,UAAU;AACZ;;AAEA;EACE,aAAa;EACb,YAAY;EACZ,gBAAgB;EAChB,SAAS;EACT,UAAU;AACZ;;AAEA;EACE,kCAAkC;EAClC,gCAAgC;EAChC,gBAAgB;AAClB;;AAEA;EACE,aAAa;EACb,YAAY;EACZ,gBAAgB;AAClB;;AAEA;EACE,wGAAwG;EACxG,4BAA4B;EAC5B,kBAAkB;AACpB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT;;6FAE2F;EAC3F,oBAAoB;AACtB;;AAEA,8BAA8B;AAC9B;EACE,gCAAgC;AAClC;;AAEA;EACE,8CAA8C;AAChD;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,8CAA8C;AAChD;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4BAA4B;AAC9B;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;EACE,oBAAoB;AACtB;;AAEA;EACE,gCAAgC;AAClC;;AAEA;EACE,gCAAgC;AAClC;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;EACE,4BAA4B;EAC5B,6BAA6B;AAC/B;;AAEA;EACE,iCAAiC;EACjC,8BAA8B;EAC9B,gCAAgC;EAChC,wCAAwC;AAC1C;;AAEA;EACE,iCAAiC;EACjC,oDAAoD;AACtD;;AAEA,6BAA6B;AAC7B;EACE,8BAA8B;EAC9B,mCAAmC;EACnC,oDAAoD;EACpD,mCAA2B;UAA3B,2BAA2B;EAC3B,+BAA+B;EAC/B,iDAAiD;EACjD,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,WAAW;EACX,kFAAkF;EAClF,YAAY;AACd;;AAEA;EACE,oCAAoC;EACpC,mEAAmE;EACnE,2BAA2B;AAC7B;;AAEA,6BAA6B;AAC7B;EACE,oFAAoF;EACpF,2BAA2B;AAC7B;;AAEA;EACE,iDAAiD;AACnD;;AAEA,yBAAyB;AACzB;EACE,2BAA2B;EAC3B,qCAAqC;EACrC,wCAAwC;EACxC,wCAAwC;EACxC,kBAAkB;EAClB,yBAAyB;EACzB,kBAAkB;EAClB,gBAAgB;EAChB,gBAAgB;EAChB,wBAAmB;EAAnB,mBAAmB;AACrB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT,6FAA6F;EAC7F,UAAU;EACV,6BAA6B;AAC/B;;AAEA;EACE,UAAU;AACZ;;AAEA;EACE,kCAAkC;EAClC,6CAA6C;EAC7C,sBAAsB;AACxB;;AAEA,6BAA6B;AAC7B;EACE,0DAA0D;EAC1D,gBAAgB;AAClB;;AAEA;EACE,uDAAuD;EACvD,gBAAgB;AAClB;;AAEA,8BAA8B;AAC9B;EACE,mDAAmD;EACnD,oDAAoD;EACpD,yDAAyD;AAC3D;;AAEA,oCAAoC;AACpC;EACE,sEAAsE;EACtE,kDAAkD;EAClD,4CAA4C;EAC5C,gBAAgB;EAChB,yBAAyB;EACzB,qBAAqB;EACrB,iBAAiB;EACjB,mCAA2B;UAA3B,2BAA2B;EAC3B,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,kCAAkC;EAClC,4BAA4B;EAC5B,mCAAmC;EACnC,2CAA2C;AAC7C;;AAEA;EACE,kCAAkC;EAClC,+BAA+B;EAC/B,sCAAsC;EACtC,2CAA2C;AAC7C;;AAEA;EACE,kCAAkC;EAClC,2BAA2B;EAC3B,kCAAkC;EAClC,2CAA2C;AAC7C;;AAEA;EACE,mCAAmC;EACnC,iCAAiC;EACjC,wCAAwC;EACxC,4CAA4C;AAC9C;;AAEA,4BAA4B;AAC5B;EACE,+BAA+B;EAC/B,mCAAmC;EACnC,mCAA2B;UAA3B,2BAA2B;EAC3B,yCAAyC;EACzC,kBAAkB;AACpB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,SAAS;EACT,OAAO;EACP,QAAQ;EACR,WAAW;EACX,kFAAkF;EAClF,YAAY;AACd;;AAEA,gCAAgC;AAChC;EACE,2BAA2B;EAC3B,qCAAqC;EACrC,mCAA2B;UAA3B,2BAA2B;EAC3B,iDAAiD;EACjD,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,kCAAkC;EAClC,oCAAoC;EACpC,6CAA6C;EAC7C,sBAAsB;AACxB;;AAEA;EACE,2FAA2F;EAC3F,kCAAkC;EAClC,2CAA2C;AAC7C;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,QAAQ;EACR,iFAAiF;EACjF,oBAAoB;AACtB;;AAEA,6EAA6E;AAC7E;EACE,0BAA0B;EAC1B,uBAAuB;EACvB,wBAAwB;EACxB,eAAe;EACf,MAAM;EACN,OAAO;EACP,gBAAgB;EAChB,mCAAmC;AACrC;;AAEA,qDAAqD;AACrD;EACE;IACE,qBAAqB;IACrB,mCAAmC;IACnC,oCAAoC;EACtC;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,mCAAmC;IACnC,oCAAoC;EACtC;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,mCAAmC;IACnC,oCAAoC;EACtC;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,mCAAmC;IACnC,oCAAoC;EACtC;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,mCAAmC;IACnC,oCAAoC;EACtC;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA,0BAA0B;AAC1B;EACE,kBAAkB;AACpB;;AAEA,wDAAwD;AACxD;EACE,kBAAkB;AACpB;;AAEA,4CAA4C;AAC5C;;;EAGE,wEAAwE;AAC1E;;AAEA,oCAAoC;AACpC;EACE,wEAAwE;AAC1E;;AAEA,iCAAiC;AACjC;EACE,wEAAwE;AAC1E;;AAEA,+BAA+B;AAC/B;EACE,kCAAkC;EAClC,mCAAmC;AACrC;;AAEA,sBAAsB;AACtB,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,qDAAqD,EAAE;AAC9D,OAAO,qDAAqD,EAAE;AAC9D,OAAO,qDAAqD,EAAE;AAC9D,OAAO,qDAAqD,EAAE;;AAE9D,OAAO,mDAAmD,EAAE;AAC5D,OAAO,mDAAmD,EAAE;AAC5D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;;AAE7D,eAAe;AACf,SAAS,gDAAgD,EAAE;AAC3D,SAAS,gDAAgD,EAAE;AAC3D,SAAS,iDAAiD,EAAE;AAC5D,SAAS,iDAAiD,EAAE;AAC5D,SAAS,iDAAiD,EAAE;AAC5D,SAAS,iDAAiD,EAAE;;AAE5D,wBAAwB;AACxB,WAAW,0DAA0D,EAAE;AACvE,cAAc,0DAA0D,EAAE;AAC1E,cAAc,0DAA0D,EAAE;AAC1E,cAAc,2DAA2D,EAAE;AAC3E,eAAe,2DAA2D,EAAE;;AAE5E;EACE;IACE,UAAU;IACV,2BAA2B;EAC7B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE;IACE,UAAU;EACZ;EACA;IACE,UAAU;EACZ;AACF;;AAEA;EACE,kCAAkC;AACpC;;AAEA,sDAAsD;AACtD,WAAW,uDAAuD,EAAE;AACpE,WAAW,uDAAuD,EAAE;AACpE,aAAa,uDAAuD,EAAE;AACtE,WAAW,uDAAuD,EAAE;AACpE,WAAW,uDAAuD,EAAE;AACpE,YAAY,uDAAuD,EAAE;AACrE,YAAY,uDAAuD,EAAE;AACrE,YAAY,uDAAuD,EAAE;;AAErE,6CAA6C;AAC7C,OAAO,mDAAmD,EAAE;AAC5D,OAAO,mDAAmD,EAAE;AAC5D,OAAO,mDAAmD,EAAE;AAC5D,OAAO,mDAAmD,EAAE;AAC5D,OAAO,mDAAmD,EAAE;AAC5D,QAAQ,mDAAmD,EAAE;AAC7D,QAAQ,mDAAmD,EAAE;;AAE7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,QAAQ,oDAAoD,EAAE;AAC9D,QAAQ,oDAAoD,EAAE;;AAE9D,qDAAqD;AACrD;EACE,aAAa;EACb,YAAY;EACZ,aAAa;EACb,sBAAsB;EACtB,gBAAgB;AAClB;;AAEA;EACE,OAAO;EACP,aAAa;EACb,aAAa;EACb,gBAAgB;AAClB;;AAEA;EACE,OAAO;EACP,aAAa;EACb,sBAAsB;EACtB,aAAa;EACb,gBAAgB;AAClB;;AAEA;EACE,4BAA4B;EAC5B,cAAc;EACd,wCAAwC;AAC1C;;AAEA;EACE,OAAO;EACP,aAAa;EACb,gBAAgB;EAChB,kBAAkB;EAClB,wCAAwC;AAC1C;;AAEA;EACE,wCAAwC;EACxC,cAAc;EACd,wCAAwC;AAC1C;;AAEA;EACE,2BAA2B;EAC3B,cAAc;AAChB;;AAEA;EACE,2BAA2B;EAC3B,cAAc;AAChB;;AAEA,iDAAiD;AACjD;EACE,aAAa;EACb,iCAAiC;EACjC,YAAY;EACZ,gBAAgB;EAChB,wCAAwC;AAC1C;;AAEA;EACE,iCAAiC;EACjC,0BAA0B;AAC5B;;AAEA;EACE,4BAA4B;EAC5B,8BAA8B;EAC9B,8BAA8B;AAChC;;AAEA,0BAA0B;AAC1B;EACE,qGAAqG;EACrG,gBAAgB;AAClB;;AAEA;EACE,YAAY;EACZ,gBAAgB;EAChB,aAAa;EACb,sBAAsB;AACxB;;AAEA,wBAAwB;AACxB;EACE;IACE,0CAA0C;EAC5C;EACA;IACE,4EAA4E;EAC9E;AACF;;AAEA;EACE;IACE,UAAU;IACV,2BAA2B;EAC7B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE;IACE,UAAU;IACV,sBAAsB;EACxB;EACA;IACE,UAAU;IACV,mBAAmB;EACrB;AACF;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,oCAAoC;AACtC;;AAEA;EACE,sCAAsC;AACxC;;AAEA,kBAAkB;AAClB;EACE,iDAAiD;AACnD;;AAEA;EACE,2BAA2B;EAC3B,0EAA0E;AAC5E;;AAEA,4BAA4B;AAC5B;EACE,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,WAAW;EACX,WAAW;EACX,YAAY;EACZ,sFAAsF;EACtF,8BAA8B;AAChC;;AAEA;EACE;IACE,WAAW;EACb;EACA;IACE,UAAU;EACZ;AACF;;AAEA,wBAAwB;AACxB;EACE,aAAa;EACb,oBAAoB;EACpB,YAAY;EACZ,gBAAgB;AAClB;;AAEA;EACE,8BAA8B;AAChC;;AAEA;EACE,kCAAkC;AACpC;;AAEA;EACE,sCAAsC;AACxC;;AAEA;EACE,wFAAwF;AAC1F;;AAEA,0BAA0B;AAC1B;EACE,8CAA8C;EAC9C,8CAA8C;AAChD;;AAEA;EACE,iCAAiC;EACjC,8CAA8C;AAChD;;AAEA;EACE,8CAA8C;EAC9C,8CAA8C;AAChD;;AAEA,iCAAiC;AACjC;EACE,aAAa;EACb,uBAAuB;EACvB,mBAAmB;EACnB,0CAA0C;EAC1C,cAAc;EACd,+CAA+C,EAAE,wBAAwB;AAC3E;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,mBAAmB;EACnB,uBAAuB;EACvB,wCAAwC;EACxC,yCAAyC;EACzC,qCAAqC;EACrC,gDAAgD;EAChD,qDAAqD;EACrD;;kIAEgI;EAChI,0CAA0C;EAC1C,yBAAyB;EACzB,eAAe;EACf,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,wDAAwD;EACxD;;mIAEiI;AACnI;;AAEA;EACE,qDAAqD;EACrD;;uIAEqI;EACrI,0CAA0C;AAC5C;;AAEA;EACE,wCAAwC;EACxC,yCAAyC;EACzC,+CAA+C;EAC/C,cAAc;EACd,2BAA2B;AAC7B;;AAEA;;EAEE,cAAc;AAChB;;AAEA;EACE,4CAA4C;EAC5C,gBAAgB;EAChB,cAAc;EACd,kBAAkB;EAClB,gBAAgB;EAChB,2BAA2B;AAC7B;;AAEA;;EAEE,cAAc;AAChB;;AAEA,oBAAoB;AACpB;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT,2FAA2F;EAC3F,UAAU;EACV,6BAA6B;EAC7B,sBAAsB;AACxB;;AAEA;;EAEE,UAAU;AACZ;;AAEA,kDAAkD;AAClD;EACE,aAAa;EACb,sBAAsB;EACtB,oBAAoB;EACpB,2BAA2B;EAC3B,iCAAiC;EACjC,sCAAsC;EACtC,YAAY;EACZ,gBAAgB;AAClB;;AAEA,8CAA8C;AAC9C;EACE,WAAW;EACX,yCAAyC;EACzC,SAAS;EACT,cAAc;EACd,aAAa;EACb,mBAAmB;EACnB,2BAA2B;EAC3B,sEAAsE;EACtE,2BAA2B;EAC3B,qCAAqC;EACrC,wCAAwC;EACxC,mCAA2B;UAA3B,2BAA2B;EAC3B,iDAAiD;EACjD,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,kCAAkC;EAClC,oCAAoC;EACpC,0BAA0B;EAC1B,6CAA6C;AAC/C;;AAEA;EACE,2FAA2F;EAC3F,kCAAkC;EAClC,mFAAmF;AACrF;;AAEA,yBAAyB;AACzB;EACE,wCAAwC;EACxC,yCAAyC;EACzC,6CAA6C;EAC7C,wBAAwB;EACxB,yBAAyB;EACzB,cAAc;AAChB;;AAEA;EACE,2BAA2B;EAC3B,mDAAmD;AACrD;;AAEA;EACE,8BAA8B;EAC9B,oDAAoD;AACtD;;AAEA,8CAA8C;AAC9C;EACE,wBAAwB;EACxB,4CAA4C;EAC5C,gBAAgB;EAChB,gBAAgB;EAChB,mBAAmB;EACnB,yBAAyB;EACzB,yCAAyC;AAC3C;;AAEA;EACE,8BAA8B;EAC9B,2CAA2C;AAC7C;;AAEA;EACE,8BAA8B;EAC9B,gBAAgB;EAChB,4CAA4C;AAC9C;;AAt7BA;EAAA,kBAu7BA;EAv7BA,kBAu7BA;EAv7BA;AAu7BA;;AAv7BA;EAAA;AAu7BA;;AAv7BA;EAAA;AAu7BA;;AAv7BA;EAAA;AAu7BA;;AAv7BA;EAAA;AAu7BA;;AAv7BA;EAAA;AAu7BA;;AAv7BA;EAAA;AAu7BA;;AAv7BA;EAAA;AAu7BA;;AAv7BA;EAAA;AAu7BA;;AAv7BA;EAAA;AAu7BA;;AAv7BA;EAAA;AAu7BA;;AAv7BA;EAAA,oBAu7BA;EAv7BA;AAu7BA;;AAv7BA;EAAA,+EAu7BA;EAv7BA,mGAu7BA;EAv7BA;AAu7BA;;AAv7BA;EAAA,wCAu7BA;EAv7BA;AAu7BA;;AAv7BA;EAAA,8BAu7BA;EAv7BA;AAu7BA;;AAv7BA;EAAA,2GAu7BA;EAv7BA,yGAu7BA;EAv7BA;AAu7BA;;AAv7BA;EAAA,2GAu7BA;EAv7BA,yGAu7BA;EAv7BA;AAu7BA;;AAv7BA;EAAA,oBAu7BA;EAv7BA;AAu7BA;;AAv7BA;EAAA,oBAu7BA;EAv7BA;AAu7BA;;AAv7BA;EAAA;AAu7BA;;AAv7BA;EAAA;AAu7BA;;AAv7BA;EAAA;AAu7BA;;AAv7BA;EAAA;AAu7BA;;AAv7BA;EAAA;AAu7BA;;AAv7BA;EAAA,kBAu7BA;EAv7BA,kBAu7BA;EAv7BA;AAu7BA;;AAv7BA;EAAA,iBAu7BA;EAv7BA,iBAu7BA;EAv7BA;AAu7BA;;AAv7BA;EAAA,oBAu7BA;EAv7BA;AAu7BA;;AAv7BA;EAAA,oBAu7BA;EAv7BA;AAu7BA;;AAv7BA;EAAA;AAu7BA;;AAv7BA;;EAAA;;IAAA;MAAA;IAu7BA;EAAA;;EAv7BA;IAAA;EAu7BA;AAAA;;AAv7BA;;EAAA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA,kBAu7BA;IAv7BA;EAu7BA;;EAv7BA;IAAA,mBAu7BA;IAv7BA;EAu7BA;;EAv7BA;IAAA,eAu7BA;IAv7BA;EAu7BA;;EAv7BA;IAAA,mBAu7BA;IAv7BA;EAu7BA;;EAv7BA;IAAA,mBAu7BA;IAv7BA;EAu7BA;;EAv7BA;IAAA,kBAu7BA;IAv7BA;EAu7BA;;EAv7BA;IAAA,kBAu7BA;IAv7BA;EAu7BA;AAAA;;AAv7BA;;EAAA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;AAAA;;AAv7BA;;EAAA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA;EAu7BA;;EAv7BA;IAAA,kBAu7BA;IAv7BA;EAu7BA;AAAA\",\"sourcesContent\":[\"@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');\\n\\n@tailwind base;\\n@tailwind components;\\n@tailwind utilities;\\n\\n/* CSS Custom Properties for Responsive Scaling - Optimized for Content Fit */\\n:root {\\n  --scale-factor: 1;\\n  --content-scale: 3;\\n  --base-font-size: calc(14px * var(--content-scale));\\n  --base-spacing: calc(6px * var(--content-scale));\\n  --base-border-radius: calc(8px * var(--content-scale));\\n  --base-icon-size: calc(20px * var(--content-scale));\\n  --base-button-height: calc(36px * var(--content-scale));\\n  --base-card-padding: calc(1px * var(--content-scale));\\n  --base-gap: calc(6px * var(--content-scale));\\n  --header-height: calc(60px * var(--content-scale));\\n  --footer-height: calc(50px * var(--content-scale));\\n  --sidebar-width: calc(80px * var(--content-scale));\\n\\n  /* HUD/HDR Color Palette */\\n  --hud-bg-primary: #311861;\\n  --hud-bg-secondary: #22184f;\\n  --hud-bg-panel: rgba(15, 20, 25, 0.85);\\n  --hud-bg-card: rgba(20, 30, 45, 0.6);\\n  --hud-border: rgba(0, 207, 255, 0.2);\\n  --hud-border-glow: rgba(0, 207, 255, 0.4);\\n\\n  /* HUD Accent Colors */\\n  --hud-neon-blue: #00cfff;\\n  --hud-aqua-green: #00ffbd;\\n  --hud-energy-orange: #ffa500;\\n  --hud-electric-purple: #bc13fe;\\n  --hud-deep-red: #ff4b4b;\\n  --hud-bright-white: #ffffff;\\n  --hud-silver: #b0b8c4;\\n\\n  /* Glassmorphism Effects */\\n  --glass-bg: rgba(255, 255, 255, 0.05);\\n  --glass-border: rgba(255, 255, 255, 0.1);\\n  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\\n}\\n\\n/* Responsive scaling variables - now handled by --content-scale */\\n@media (max-width: 1920px) and (min-width: 1600px) {\\n  :root {\\n    --scale-factor: 0.9;\\n    --content-scale: 0.9;\\n  }\\n}\\n\\n@media (max-width: 1599px) and (min-width: 1400px) {\\n  :root {\\n    --scale-factor: 0.8;\\n    --content-scale: 0.8;\\n  }\\n}\\n\\n@media (max-width: 1399px) and (min-width: 1200px) {\\n  :root {\\n    --scale-factor: 0.7;\\n    --content-scale: 0.7;\\n  }\\n}\\n\\n@media (max-width: 1199px) and (min-width: 1000px) {\\n  :root {\\n    --scale-factor: 0.6;\\n    --content-scale: 0.6;\\n  }\\n}\\n\\n@media (max-width: 999px) {\\n  :root {\\n    --scale-factor: 0.5;\\n    --content-scale: 0.5;\\n  }\\n}\\n\\n/* Ensure base styles are applied */\\n* {\\n  box-sizing: border-box;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nhtml, body {\\n  height: 100vh;\\n  width: 100vw;\\n  overflow: hidden;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nbody {\\n  font-family: 'Poppins', sans-serif;\\n  font-size: var(--base-font-size);\\n  line-height: 1.5;\\n}\\n\\n#__next {\\n  height: 100vh;\\n  width: 100vw;\\n  overflow: hidden;\\n}\\n\\n.main-background {\\n  background: linear-gradient(135deg, var(--hud-bg-primary) 0%, var(--hud-bg-secondary) 50%, #05080e 100%);\\n  background-attachment: fixed;\\n  position: relative;\\n}\\n\\n.main-background::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(ellipse at 20% 30%, rgba(209, 160, 255, 0.1) 0%, transparent 50%),\\n              radial-gradient(ellipse at 80% 70%, rgba(188, 19, 254, 0.08) 0%, transparent 50%),\\n              radial-gradient(ellipse at 50% 50%, rgba(41, 52, 95, 0.05) 0%, transparent 70%);\\n  pointer-events: none;\\n}\\n\\n/* Global Responsive Classes */\\n.responsive-text {\\n  font-size: var(--base-font-size);\\n}\\n\\n.responsive-text-sm {\\n  font-size: calc(var(--base-font-size) * 0.875);\\n}\\n\\n.responsive-text-xs {\\n  font-size: calc(var(--base-font-size) * 0.75);\\n}\\n\\n.responsive-text-lg {\\n  font-size: calc(var(--base-font-size) * 1.125);\\n}\\n\\n.responsive-text-xl {\\n  font-size: calc(var(--base-font-size) * 1.25);\\n}\\n\\n.responsive-text-2xl {\\n  font-size: calc(var(--base-font-size) * 1.5);\\n}\\n\\n.responsive-spacing {\\n  padding: var(--base-spacing);\\n}\\n\\n.responsive-spacing-sm {\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.responsive-spacing-lg {\\n  padding: calc(var(--base-spacing) * 1.5);\\n}\\n\\n.responsive-gap {\\n  gap: var(--base-gap);\\n}\\n\\n.responsive-gap-sm {\\n  gap: calc(var(--base-gap) * 0.5);\\n}\\n\\n.responsive-gap-lg {\\n  gap: calc(var(--base-gap) * 1.5);\\n}\\n\\n.responsive-border-radius {\\n  border-radius: var(--base-border-radius);\\n}\\n\\n.responsive-icon {\\n  width: var(--base-icon-size);\\n  height: var(--base-icon-size);\\n}\\n\\n.responsive-button {\\n  height: var(--base-button-height);\\n  padding: 0 var(--base-spacing);\\n  font-size: var(--base-font-size);\\n  border-radius: var(--base-border-radius);\\n}\\n\\n.responsive-card {\\n  padding: var(--base-card-padding);\\n  border-radius: calc(var(--base-border-radius) * 1.5);\\n}\\n\\n/* Enhanced HUD Card System */\\n.hud-card {\\n  background: var(--hud-bg-card);\\n  border: 1px solid var(--hud-border);\\n  border-radius: calc(var(--base-border-radius) * 1.5);\\n  backdrop-filter: blur(20px);\\n  box-shadow: var(--glass-shadow);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.hud-card::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(90deg, transparent, var(--hud-neon-blue), transparent);\\n  opacity: 0.6;\\n}\\n\\n.hud-card:hover {\\n  border-color: var(--hud-border-glow);\\n  box-shadow: 0 8px 40px rgba(0, 207, 255, 0.15), var(--glass-shadow);\\n  transform: translateY(-2px);\\n}\\n\\n/* Compact HUD card padding */\\n.hud-card .ant-card-head {\\n  padding: calc(var(--base-spacing) * 0.75) calc(var(--base-spacing) * 1.5) !important;\\n  min-height: auto !important;\\n}\\n\\n.hud-card .ant-card-body {\\n  padding: calc(var(--base-spacing) * 1) !important;\\n}\\n\\n/* Compact metric cards */\\n.metric-card {\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  border-radius: var(--base-border-radius);\\n  padding: calc(var(--base-spacing) * 0.5);\\n  text-align: center;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n  min-height: auto;\\n  height: fit-content;\\n}\\n\\n.metric-card::after {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(0, 207, 255, 0.05) 0%, rgba(0, 255, 189, 0.05) 100%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.metric-card:hover::after {\\n  opacity: 1;\\n}\\n\\n.metric-card:hover {\\n  border-color: var(--hud-neon-blue);\\n  box-shadow: 0 4px 20px rgba(0, 207, 255, 0.2);\\n  transform: scale(1.02);\\n}\\n\\n/* Compact metric card text */\\n.metric-card p {\\n  margin-bottom: calc(var(--base-spacing) * 0.25) !important;\\n  line-height: 1.2;\\n}\\n\\n.metric-card .text-xl {\\n  font-size: calc(var(--base-font-size) * 1.2) !important;\\n  line-height: 1.1;\\n}\\n\\n/* Compact metric card icons */\\n.metric-card .w-10.h-10 {\\n  width: calc(var(--base-icon-size) * 1.5) !important;\\n  height: calc(var(--base-icon-size) * 1.5) !important;\\n  margin-bottom: calc(var(--base-spacing) * 0.5) !important;\\n}\\n\\n/* Status badges with glow effects */\\n.status-badge {\\n  padding: calc(var(--base-spacing) * 0.5) calc(var(--base-spacing) * 1);\\n  border-radius: calc(var(--base-border-radius) * 2);\\n  font-size: calc(10px * var(--content-scale));\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  border: 1px solid;\\n  backdrop-filter: blur(10px);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.status-badge.active {\\n  background: rgba(0, 255, 189, 0.2);\\n  color: var(--hud-aqua-green);\\n  border-color: var(--hud-aqua-green);\\n  box-shadow: 0 0 10px rgba(0, 255, 189, 0.3);\\n}\\n\\n.status-badge.beta {\\n  background: rgba(255, 165, 0, 0.2);\\n  color: var(--hud-energy-orange);\\n  border-color: var(--hud-energy-orange);\\n  box-shadow: 0 0 10px rgba(255, 165, 0, 0.3);\\n}\\n\\n.status-badge.live {\\n  background: rgba(0, 207, 255, 0.2);\\n  color: var(--hud-neon-blue);\\n  border-color: var(--hud-neon-blue);\\n  box-shadow: 0 0 10px rgba(0, 207, 255, 0.3);\\n}\\n\\n.status-badge.testing {\\n  background: rgba(188, 19, 254, 0.2);\\n  color: var(--hud-electric-purple);\\n  border-color: var(--hud-electric-purple);\\n  box-shadow: 0 0 10px rgba(188, 19, 254, 0.3);\\n}\\n\\n/* Enhanced Header Styling */\\n.header-hud {\\n  background: var(--hud-bg-panel);\\n  border: 1px solid var(--hud-border);\\n  backdrop-filter: blur(20px);\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n  position: relative;\\n}\\n\\n.header-hud::before {\\n  content: '';\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(90deg, transparent, var(--hud-neon-blue), transparent);\\n  opacity: 0.4;\\n}\\n\\n/* Enhanced Department Buttons */\\n.dept-button {\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  backdrop-filter: blur(10px);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.dept-button:hover {\\n  background: rgba(0, 207, 255, 0.1);\\n  border-color: var(--hud-border-glow);\\n  box-shadow: 0 4px 20px rgba(0, 207, 255, 0.2);\\n  transform: scale(1.05);\\n}\\n\\n.dept-button.active {\\n  background: linear-gradient(135deg, rgba(0, 207, 255, 0.2) 0%, rgba(0, 255, 189, 0.1) 100%);\\n  border-color: var(--hud-neon-blue);\\n  box-shadow: 0 0 20px rgba(0, 207, 255, 0.3);\\n}\\n\\n.dept-button.active::after {\\n  content: '';\\n  position: absolute;\\n  inset: 0;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\\n  pointer-events: none;\\n}\\n\\n/* Dashboard Auto-Scale - Scales ALL content including text, icons, spacing */\\n.dashboard-auto-scale {\\n  transform-origin: top left;\\n  width: 100vw !important;\\n  height: 100vh !important;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  overflow: hidden;\\n  transition: transform 0.3s ease-out;\\n}\\n\\n/* Scale everything - content, text, icons, spacing */\\n@media (max-width: 1920px) and (min-width: 1600px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.9);\\n    width: calc(100vw / 0.9) !important;\\n    height: calc(100vh / 0.9) !important;\\n  }\\n  :root {\\n    --content-scale: 0.9;\\n  }\\n}\\n\\n@media (max-width: 1599px) and (min-width: 1400px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.8);\\n    width: calc(100vw / 0.8) !important;\\n    height: calc(100vh / 0.8) !important;\\n  }\\n  :root {\\n    --content-scale: 0.8;\\n  }\\n}\\n\\n@media (max-width: 1399px) and (min-width: 1200px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.7);\\n    width: calc(100vw / 0.7) !important;\\n    height: calc(100vh / 0.7) !important;\\n  }\\n  :root {\\n    --content-scale: 0.7;\\n  }\\n}\\n\\n@media (max-width: 1199px) and (min-width: 1000px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.6);\\n    width: calc(100vw / 0.6) !important;\\n    height: calc(100vh / 0.6) !important;\\n  }\\n  :root {\\n    --content-scale: 0.6;\\n  }\\n}\\n\\n@media (max-width: 999px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.5);\\n    width: calc(100vw / 0.5) !important;\\n    height: calc(100vh / 0.5) !important;\\n  }\\n  :root {\\n    --content-scale: 0.5;\\n  }\\n}\\n\\n/* Default content scale */\\n:root {\\n  --content-scale: 1;\\n}\\n\\n/* Universal content scaling - applies to ALL elements */\\n* {\\n  font-size: inherit;\\n}\\n\\n/* Scale all text elements in main content */\\n.main-section h1, .main-section h2, .main-section h3, .main-section h4, .main-section h5, .main-section h6,\\n.main-section p, .main-section span, .main-section div, .main-section button,\\n.main-section input, .main-section textarea, .main-section select, .main-section label, .main-section a {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale card content specifically */\\n.card-content * {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale chart text and numbers */\\n.recharts-text, .recharts-label, .chart-text, .metric-value, .metric-label {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale all icons and images */\\nsvg, img, .icon {\\n  width: calc(var(--base-icon-size));\\n  height: calc(var(--base-icon-size));\\n}\\n\\n/* Scale all spacing */\\n.p-1 { padding: calc(4px * var(--content-scale)) !important; }\\n.p-2 { padding: calc(8px * var(--content-scale)) !important; }\\n.p-3 { padding: calc(12px * var(--content-scale)) !important; }\\n.p-4 { padding: calc(16px * var(--content-scale)) !important; }\\n.p-5 { padding: calc(20px * var(--content-scale)) !important; }\\n.p-6 { padding: calc(24px * var(--content-scale)) !important; }\\n\\n.m-1 { margin: calc(4px * var(--content-scale)) !important; }\\n.m-2 { margin: calc(8px * var(--content-scale)) !important; }\\n.m-3 { margin: calc(12px * var(--content-scale)) !important; }\\n.m-4 { margin: calc(16px * var(--content-scale)) !important; }\\n.m-5 { margin: calc(20px * var(--content-scale)) !important; }\\n.m-6 { margin: calc(24px * var(--content-scale)) !important; }\\n\\n/* Scale gaps */\\n.gap-1 { gap: calc(4px * var(--content-scale)) !important; }\\n.gap-2 { gap: calc(8px * var(--content-scale)) !important; }\\n.gap-3 { gap: calc(12px * var(--content-scale)) !important; }\\n.gap-4 { gap: calc(16px * var(--content-scale)) !important; }\\n.gap-5 { gap: calc(20px * var(--content-scale)) !important; }\\n.gap-6 { gap: calc(24px * var(--content-scale)) !important; }\\n\\n/* Scale border radius */\\n.rounded { border-radius: calc(4px * var(--content-scale)) !important; }\\n.rounded-md { border-radius: calc(6px * var(--content-scale)) !important; }\\n.rounded-lg { border-radius: calc(8px * var(--content-scale)) !important; }\\n.rounded-xl { border-radius: calc(12px * var(--content-scale)) !important; }\\n.rounded-2xl { border-radius: calc(16px * var(--content-scale)) !important; }\\n\\n@keyframes fade-in-up {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n.animate-fade-in-up {\\n  animation: fade-in-up 0.3s ease-out forwards;\\n}\\n\\n@keyframes fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n.animate-fadeIn {\\n  animation: fadeIn 0.5s ease-in-out;\\n}\\n\\n/* Override Tailwind text sizes with content scaling */\\n.text-xs { font-size: calc(12px * var(--content-scale)) !important; }\\n.text-sm { font-size: calc(14px * var(--content-scale)) !important; }\\n.text-base { font-size: calc(16px * var(--content-scale)) !important; }\\n.text-lg { font-size: calc(18px * var(--content-scale)) !important; }\\n.text-xl { font-size: calc(20px * var(--content-scale)) !important; }\\n.text-2xl { font-size: calc(24px * var(--content-scale)) !important; }\\n.text-3xl { font-size: calc(30px * var(--content-scale)) !important; }\\n.text-4xl { font-size: calc(36px * var(--content-scale)) !important; }\\n\\n/* Override Tailwind width/height for icons */\\n.w-3 { width: calc(12px * var(--content-scale)) !important; }\\n.w-4 { width: calc(16px * var(--content-scale)) !important; }\\n.w-5 { width: calc(20px * var(--content-scale)) !important; }\\n.w-6 { width: calc(24px * var(--content-scale)) !important; }\\n.w-8 { width: calc(32px * var(--content-scale)) !important; }\\n.w-10 { width: calc(40px * var(--content-scale)) !important; }\\n.w-12 { width: calc(48px * var(--content-scale)) !important; }\\n\\n.h-3 { height: calc(12px * var(--content-scale)) !important; }\\n.h-4 { height: calc(16px * var(--content-scale)) !important; }\\n.h-5 { height: calc(20px * var(--content-scale)) !important; }\\n.h-6 { height: calc(24px * var(--content-scale)) !important; }\\n.h-8 { height: calc(32px * var(--content-scale)) !important; }\\n.h-10 { height: calc(40px * var(--content-scale)) !important; }\\n.h-12 { height: calc(48px * var(--content-scale)) !important; }\\n\\n/* Optimized Layout Classes for Perfect Content Fit */\\n.main-layout {\\n  height: 100vh;\\n  width: 100vw;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.content-area {\\n  flex: 1;\\n  display: flex;\\n  min-height: 0;\\n  overflow: hidden;\\n}\\n\\n.main-content {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 0;\\n  overflow: hidden;\\n}\\n\\n.header-section {\\n  height: var(--header-height);\\n  flex-shrink: 0;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.main-section {\\n  flex: 1;\\n  min-height: 0;\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.footer-section {\\n  height: calc(var(--footer-height) * 1.5);\\n  flex-shrink: 0;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.sidebar-left {\\n  width: var(--sidebar-width);\\n  flex-shrink: 0;\\n}\\n\\n.sidebar-right {\\n  width: var(--sidebar-width);\\n  flex-shrink: 0;\\n}\\n\\n/* Enhanced Dashboard Grid for No-Scroll Layout */\\n.dashboard-grid {\\n  display: grid;\\n  gap: calc(var(--base-gap) * 0.75);\\n  height: 100%;\\n  overflow: hidden;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.dashboard-grid-teacher {\\n  grid-template-rows: auto 1fr auto;\\n  grid-template-columns: 1fr;\\n}\\n\\n.dashboard-grid-school {\\n  grid-template-rows: auto 1fr;\\n  grid-template-columns: 1fr 1fr;\\n  gap: calc(var(--base-gap) * 1);\\n}\\n\\n/* Compact content areas */\\n.content-compact {\\n  max-height: calc(100vh - var(--header-height) - var(--footer-height) - calc(var(--base-spacing) * 4));\\n  overflow-y: auto;\\n}\\n\\n.content-no-scroll {\\n  height: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n/* Enhanced Animations */\\n@keyframes pulse-glow {\\n  0%, 100% {\\n    box-shadow: 0 0 5px rgba(0, 207, 255, 0.3);\\n  }\\n  50% {\\n    box-shadow: 0 0 20px rgba(0, 207, 255, 0.6), 0 0 30px rgba(0, 207, 255, 0.4);\\n  }\\n}\\n\\n@keyframes slide-in-up {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes fade-in-scale {\\n  from {\\n    opacity: 0;\\n    transform: scale(0.95);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n}\\n\\n.animate-pulse-glow {\\n  animation: pulse-glow 2s ease-in-out infinite;\\n}\\n\\n.animate-slide-in-up {\\n  animation: slide-in-up 0.5s ease-out;\\n}\\n\\n.animate-fade-in-scale {\\n  animation: fade-in-scale 0.3s ease-out;\\n}\\n\\n/* Hover Effects */\\n.hover-lift {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.hover-lift:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 207, 255, 0.2);\\n}\\n\\n/* Progress Bar Animations */\\n.progress-bar {\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.progress-bar::after {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\\n  animation: shimmer 2s infinite;\\n}\\n\\n@keyframes shimmer {\\n  0% {\\n    left: -100%;\\n  }\\n  100% {\\n    left: 100%;\\n  }\\n}\\n\\n/* Optimized Card Grid */\\n.card-grid {\\n  display: grid;\\n  gap: var(--base-gap);\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n.card-grid-2 {\\n  grid-template-columns: 1fr 1fr;\\n}\\n\\n.card-grid-3 {\\n  grid-template-columns: 1fr 1fr 1fr;\\n}\\n\\n.card-grid-4 {\\n  grid-template-columns: 1fr 1fr 1fr 1fr;\\n}\\n\\n.card-grid-auto {\\n  grid-template-columns: repeat(auto-fit, minmax(calc(250px * var(--content-scale)), 1fr));\\n}\\n\\n/* Optimized Card Sizing */\\n.card-compact {\\n  padding: calc(var(--base-card-padding) * 0.75);\\n  min-height: calc(120px * var(--content-scale));\\n}\\n\\n.card-standard {\\n  padding: var(--base-card-padding);\\n  min-height: calc(160px * var(--content-scale));\\n}\\n\\n.card-large {\\n  padding: calc(var(--base-card-padding) * 1.25);\\n  min-height: calc(200px * var(--content-scale));\\n}\\n\\n/* Neumorphic HUD Subnav Styles */\\n.subnav-hud {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  padding: calc(var(--base-spacing) * 1.5) 0;\\n  margin: 0 auto;\\n  max-width: calc(72px * 8 + var(--base-gap) * 7); /* 8 buttons with gaps */\\n}\\n\\n.subnav-button {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  width: calc(72px * var(--content-scale));\\n  height: calc(72px * var(--content-scale));\\n  margin: 0 calc(var(--base-gap) * 0.5);\\n  border-radius: calc(16px * var(--content-scale));\\n  background: linear-gradient(145deg, #2a2a3a, #1a1a2a);\\n  box-shadow:\\n    calc(8px * var(--content-scale)) calc(8px * var(--content-scale)) calc(16px * var(--content-scale)) rgba(0, 0, 0, 0.4),\\n    calc(-4px * var(--content-scale)) calc(-4px * var(--content-scale)) calc(8px * var(--content-scale)) rgba(255, 255, 255, 0.05);\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.subnav-button:hover {\\n  transform: translateY(calc(-2px * var(--content-scale)));\\n  box-shadow:\\n    calc(12px * var(--content-scale)) calc(12px * var(--content-scale)) calc(24px * var(--content-scale)) rgba(0, 0, 0, 0.5),\\n    calc(-6px * var(--content-scale)) calc(-6px * var(--content-scale)) calc(12px * var(--content-scale)) rgba(255, 255, 255, 0.08);\\n}\\n\\n.subnav-button.active {\\n  background: linear-gradient(145deg, #3a4a6a, #2a3a5a);\\n  box-shadow:\\n    inset calc(4px * var(--content-scale)) calc(4px * var(--content-scale)) calc(8px * var(--content-scale)) rgba(0, 0, 0, 0.3),\\n    inset calc(-2px * var(--content-scale)) calc(-2px * var(--content-scale)) calc(4px * var(--content-scale)) rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(100, 200, 255, 0.3);\\n}\\n\\n.subnav-icon {\\n  width: calc(24px * var(--content-scale));\\n  height: calc(24px * var(--content-scale));\\n  margin-bottom: calc(4px * var(--content-scale));\\n  color: #a0a0b0;\\n  transition: color 0.3s ease;\\n}\\n\\n.subnav-button:hover .subnav-icon,\\n.subnav-button.active .subnav-icon {\\n  color: #64b5f6;\\n}\\n\\n.subnav-label {\\n  font-size: calc(10px * var(--content-scale));\\n  font-weight: 500;\\n  color: #a0a0b0;\\n  text-align: center;\\n  line-height: 1.2;\\n  transition: color 0.3s ease;\\n}\\n\\n.subnav-button:hover .subnav-label,\\n.subnav-button.active .subnav-label {\\n  color: #64b5f6;\\n}\\n\\n/* HUD Glow Effect */\\n.subnav-button::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(circle at center, rgba(100, 181, 246, 0.1) 0%, transparent 70%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n  border-radius: inherit;\\n}\\n\\n.subnav-button:hover::before,\\n.subnav-button.active::before {\\n  opacity: 1;\\n}\\n\\n/* Enhanced HUD Vertical Layout for Left Sidebar */\\n.subnav-hud-vertical {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: stretch;\\n  justify-content: flex-start;\\n  gap: calc(var(--base-gap) * 0.75);\\n  padding: calc(var(--base-spacing) * 1);\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n/* Enhanced subnav button with glassmorphism */\\n.subnav-hud-vertical .subnav-button {\\n  width: 100%;\\n  height: calc(42px * var(--content-scale));\\n  margin: 0;\\n  flex-shrink: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  padding: calc(var(--base-spacing) * 1) calc(var(--base-spacing) * 1.5);\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  border-radius: var(--base-border-radius);\\n  backdrop-filter: blur(10px);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover {\\n  background: rgba(0, 207, 255, 0.1);\\n  border-color: var(--hud-border-glow);\\n  transform: translateX(4px);\\n  box-shadow: 0 4px 20px rgba(0, 207, 255, 0.2);\\n}\\n\\n.subnav-hud-vertical .subnav-button.active {\\n  background: linear-gradient(135deg, rgba(0, 207, 255, 0.2) 0%, rgba(0, 255, 189, 0.1) 100%);\\n  border-color: var(--hud-neon-blue);\\n  box-shadow: 0 0 20px rgba(0, 207, 255, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n}\\n\\n/* Enhanced subnav icon */\\n.subnav-hud-vertical .subnav-icon {\\n  width: calc(18px * var(--content-scale));\\n  height: calc(18px * var(--content-scale));\\n  margin-right: calc(var(--base-spacing) * 1.5);\\n  color: var(--hud-silver);\\n  transition: all 0.3s ease;\\n  flex-shrink: 0;\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover .subnav-icon {\\n  color: var(--hud-neon-blue);\\n  filter: drop-shadow(0 0 8px rgba(0, 207, 255, 0.6));\\n}\\n\\n.subnav-hud-vertical .subnav-button.active .subnav-icon {\\n  color: var(--hud-bright-white);\\n  filter: drop-shadow(0 0 12px rgba(0, 207, 255, 0.8));\\n}\\n\\n/* Enhanced subnav label - full text display */\\n.subnav-hud-vertical .subnav-label {\\n  color: var(--hud-silver);\\n  font-size: calc(12px * var(--content-scale));\\n  font-weight: 500;\\n  line-height: 1.2;\\n  white-space: nowrap;\\n  transition: all 0.3s ease;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover .subnav-label {\\n  color: var(--hud-bright-white);\\n  text-shadow: 0 0 8px rgba(0, 207, 255, 0.6);\\n}\\n\\n.subnav-hud-vertical .subnav-button.active .subnav-label {\\n  color: var(--hud-bright-white);\\n  font-weight: 600;\\n  text-shadow: 0 0 12px rgba(0, 207, 255, 0.8);\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[1]!./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[2]!./styles/globals.css\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _icons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons */ \"./src/components/icons.tsx\");\n\n\n\nconst NavItem = (param)=>{\n    let { icon, label, active, onClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        \"aria-label\": label,\n        \"aria-pressed\": active,\n        className: \"w-full flex items-center justify-center responsive-border-radius transition-colors duration-200 relative \".concat(active ? \"bg-brand-cyan/20 text-white\" : \"text-gray-400 hover:bg-gray-700/50 hover:text-white\"),\n        style: {\n            padding: \"calc(var(--base-spacing) * 0.5)\",\n            height: \"var(--base-button-height)\"\n        },\n        children: [\n            active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-0 top-1/2 -translate-y-1/2 bg-brand-cyan rounded-r-full\",\n                style: {\n                    height: \"calc(var(--base-button-height) * 0.6)\",\n                    width: \"3px\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, undefined),\n            icon\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n_c = NavItem;\nconst Sidebar = (param)=>{\n    let { activeView, setActiveView } = param;\n    const navItems = [\n        {\n            id: \"Dashboard\",\n            label: \"Home\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.HomeIcon, {\n                className: \"responsive-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 62,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: \"Analytic\",\n            label: \"Analytics\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.ChartBarIcon, {\n                className: \"responsive-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 67,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: \"Tool\",\n            label: \"Modules\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.GridIcon, {\n                className: \"responsive-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 72,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: \"Setting\",\n            label: \"Profile\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.UserCircleIcon, {\n                className: \"responsive-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 77,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: \"Setting\",\n            label: \"Settings\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.SettingsIcon, {\n                className: \"responsive-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 82,\n                columnNumber: 13\n            }, undefined)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"bg-panel-bg border border-gray-700/50 responsive-border-radius sidebar-left flex flex-col items-center justify-between\",\n        style: {\n            padding: \"calc(var(--base-spacing) * 0.75)\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center w-full\",\n                style: {\n                    gap: \"calc(var(--base-gap) * 0.75)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-cyan-400 to-fuchsia-500 rounded-full flex items-center justify-center text-white\",\n                        style: {\n                            width: \"calc(var(--base-icon-size) * 1.25)\",\n                            height: \"calc(var(--base-icon-size) * 1.25)\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.ShieldCheckIcon, {\n                            style: {\n                                width: \"calc(var(--base-icon-size) * 1)\",\n                                height: \"calc(var(--base-icon-size) * 1)\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 10\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full border-t border-gray-700\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined),\n                    navItems.slice(0, 3).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavItem, {\n                            icon: item.icon,\n                            label: item.label,\n                            active: activeView === item.id,\n                            onClick: ()=>setActiveView(item.id)\n                        }, item.label, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center gap-4 w-full\",\n                children: navItems.slice(3).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavItem, {\n                        icon: item.icon,\n                        label: item.label,\n                        active: activeView === item.id && item.label === \"Settings\",\n                        onClick: ()=>setActiveView(item.id)\n                    }, item.label, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = Sidebar;\nvar _c, _c1;\n$RefreshReg$(_c, \"NavItem\");\n$RefreshReg$(_c1, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Sidebar.tsx\n"));

/***/ })

});
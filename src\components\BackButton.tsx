import React from 'react';

interface BackButtonProps {
  onBack: () => void;
  label?: string;
}

export const BackButton: React.FC<BackButtonProps> = ({ onBack, label = 'Back to Dashboard' }) => {
  return (
    <button
      onClick={onBack}
      className="fixed top-4 right-4 z-50 bg-panel-bg border border-gray-700/50 backdrop-blur-xl rounded-lg px-4 py-2 hover:bg-gray-800/60 transition-all duration-300 hover:scale-105 flex items-center gap-2"
      style={{
        boxShadow: 'var(--glass-shadow)'
      }}
    >
      <svg 
        className="w-5 h-5 text-accent-blue" 
        xmlns="http://www.w3.org/2000/svg" 
        fill="none" 
        viewBox="0 0 24 24" 
        stroke="currentColor"
      >
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
      </svg>
      <span className="text-text-primary font-medium">{label}</span>
    </button>
  );
};

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_views_Bookstore_BookstoreView_tsx";
exports.ids = ["src_views_Bookstore_BookstoreView_tsx"];
exports.modules = {

/***/ "./src/views/Bookstore/BookCard.tsx":
/*!******************************************!*\
  !*** ./src/views/Bookstore/BookCard.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BookCard: () => (/* binding */ BookCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/Card */ \"./src/components/Card.tsx\");\n\n\n\nconst BookCard = ({ book })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"p-0 overflow-hidden group\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                src: book.image,\n                alt: book.title,\n                className: \"w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Bookstore\\\\BookCard.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-white font-semibold truncate\",\n                        children: book.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Bookstore\\\\BookCard.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-400\",\n                        children: book.author\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Bookstore\\\\BookCard.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Bookstore\\\\BookCard.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Bookstore\\\\BookCard.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvdmlld3MvQm9va3N0b3JlL0Jvb2tDYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQzBCO0FBQ21CO0FBWXRDLE1BQU1FLFdBQW9DLENBQUMsRUFBRUMsSUFBSSxFQUFFO0lBQ3hELHFCQUNFLDhEQUFDRixrREFBSUE7UUFBQ0csV0FBVTs7MEJBQ2QsOERBQUNDO2dCQUFJQyxLQUFLSCxLQUFLSSxLQUFLO2dCQUFFQyxLQUFLTCxLQUFLTSxLQUFLO2dCQUFFTCxXQUFVOzs7Ozs7MEJBQ2pELDhEQUFDTTtnQkFBSU4sV0FBVTs7a0NBQ2IsOERBQUNPO3dCQUFHUCxXQUFVO2tDQUFxQ0QsS0FBS00sS0FBSzs7Ozs7O2tDQUM3RCw4REFBQ0c7d0JBQUVSLFdBQVU7a0NBQXlCRCxLQUFLVSxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJekQsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL2V5ZXMtc2hpZWxkLWZ1dHVyaXN0aWMtdWkvLi9zcmMvdmlld3MvQm9va3N0b3JlL0Jvb2tDYXJkLnRzeD8wYjk0Il0sInNvdXJjZXNDb250ZW50IjpbIlxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IENhcmQgfSBmcm9tICcuLi8uLi9jb21wb25lbnRzL0NhcmQnO1xuXG5leHBvcnQgaW50ZXJmYWNlIEJvb2sge1xuICAgIHRpdGxlOiBzdHJpbmc7XG4gICAgYXV0aG9yOiBzdHJpbmc7XG4gICAgaW1hZ2U6IHN0cmluZztcbn1cblxuaW50ZXJmYWNlIEJvb2tDYXJkUHJvcHMge1xuICAgIGJvb2s6IEJvb2s7XG59XG5cbmV4cG9ydCBjb25zdCBCb29rQ2FyZDogUmVhY3QuRkM8Qm9va0NhcmRQcm9wcz4gPSAoeyBib29rIH0pID0+IHtcbiAgcmV0dXJuIChcbiAgICA8Q2FyZCBjbGFzc05hbWU9XCJwLTAgb3ZlcmZsb3ctaGlkZGVuIGdyb3VwXCI+XG4gICAgICA8aW1nIHNyYz17Ym9vay5pbWFnZX0gYWx0PXtib29rLnRpdGxlfSBjbGFzc05hbWU9XCJ3LWZ1bGwgaC00OCBvYmplY3QtY292ZXIgZ3JvdXAtaG92ZXI6c2NhbGUtMTA1IHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMFwiLz5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zXCI+XG4gICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGQgdHJ1bmNhdGVcIj57Ym9vay50aXRsZX08L2g0PlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj57Ym9vay5hdXRob3J9PC9wPlxuICAgICAgPC9kaXY+XG4gICAgPC9DYXJkPlxuICApO1xufTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNhcmQiLCJCb29rQ2FyZCIsImJvb2siLCJjbGFzc05hbWUiLCJpbWciLCJzcmMiLCJpbWFnZSIsImFsdCIsInRpdGxlIiwiZGl2IiwiaDQiLCJwIiwiYXV0aG9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/views/Bookstore/BookCard.tsx\n");

/***/ }),

/***/ "./src/views/Bookstore/BookstoreView.tsx":
/*!***********************************************!*\
  !*** ./src/views/Bookstore/BookstoreView.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BookstoreView: () => (/* binding */ BookstoreView)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _BookCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BookCard */ \"./src/views/Bookstore/BookCard.tsx\");\n\n\n\nconst BookstoreView = ()=>{\n    const [books, setBooks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchBooks = async ()=>{\n            try {\n                setLoading(true);\n                const response = await fetch(\"/api/books\");\n                const data = await response.json();\n                setBooks(data);\n            } catch (error) {\n                console.error(\"Failed to fetch books\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchBooks();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-full text-cyan-400\",\n            children: \"Loading Bookstore...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Bookstore\\\\BookstoreView.tsx\",\n            lineNumber: 26,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (!books || !Array.isArray(books) || books.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-full text-red-400\",\n            children: \"No books available.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Bookstore\\\\BookstoreView.tsx\",\n            lineNumber: 30,\n            columnNumber: 12\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-grow grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n        children: books.map((book)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BookCard__WEBPACK_IMPORTED_MODULE_2__.BookCard, {\n                book: book\n            }, book.title, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Bookstore\\\\BookstoreView.tsx\",\n                lineNumber: 36,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Bookstore\\\\BookstoreView.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/Bookstore/BookstoreView.tsx\n");

/***/ })

};
;
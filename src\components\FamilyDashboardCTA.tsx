
import React from 'react';
import { ChevronRightIcon, UserGroupIcon } from '@heroicons/react/24/solid';

export const FamilyDashboardCTA: React.FC = () => {
    return (
        <div className="relative p-4 sm:p-6 rounded-xl flex items-center justify-between h-full bg-gradient-to-br from-[#FF007F] to-[#d6006a] text-white overflow-hidden">
            {/* Glowing effect */}
            <div className="absolute -top-1/2 -left-1/2 w-[200%] h-[200%] bg-[radial-gradient(circle_at_center,_rgba(255,0,127,0.5),_transparent_40%)] animate-spin" style={{animationDuration: '10s'}}></div>
            
            <div className="relative z-10 flex items-center gap-3 sm:gap-6">
                <div className="bg-white/20 p-3 sm:p-4 rounded-full">
                    <UserGroupIcon className="w-8 h-8 sm:w-10 sm:h-10 text-white" />
                </div>
                <div>
                    <h3 className="text-base sm:text-xl font-bold">Family Dashboard</h3>
                    <p className="text-xs sm:text-sm max-w-sm mt-1 opacity-90">
                        Pan tie to rene yua topn! eahur ind onn-entord on alt egrer laas.
                    </p>
                </div>
            </div>
            <div className="relative z-10">
                <button onClick={() => alert('Opening Family Dashboard...')} className="bg-white/20 hover:bg-white/30 transition-colors p-2 sm:p-3 rounded-full">
                    <ChevronRightIcon className="w-6 h-6 sm:w-8 sm:h-8 text-white"/>
                </button>
            </div>
        </div>
    );
};

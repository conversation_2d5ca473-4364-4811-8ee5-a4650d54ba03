import { NextApiRequest, NextApiResponse } from 'next';

// Real Staff Management Data with Professional Structure
const staffData = {
  organizationChart: {
    principal: {
      id: 'principal-001',
      name: 'Dr. <PERSON>',
      position: 'Principal & CEO',
      department: 'Executive Leadership',
      photo: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      email: '<EMAIL>',
      phone: '+****************',
      experience: '15 years',
      education: 'PhD in Educational Leadership, Harvard',
      specialization: 'AI-Enhanced Education',
      startDate: '2020-01-15',
      salary: 250000,
      performance: 98,
      directReports: ['vp-academic', 'vp-operations', 'vp-technology']
    },
    leadership: [
      {
        id: 'vp-academic',
        name: 'Prof. <PERSON>',
        position: 'VP of Academic Affairs',
        department: 'Academic Leadership',
        photo: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        email: 'micha<PERSON>.<EMAIL>',
        phone: '+****************',
        experience: '12 years',
        education: 'PhD in Quantum Physics, MIT',
        specialization: 'Quantum Education',
        startDate: '2020-03-01',
        salary: 180000,
        performance: 96,
        reportsTo: 'principal-001',
        directReports: ['dept-ai', 'dept-quantum', 'dept-bio']
      },
      {
        id: 'vp-operations',
        name: 'Dr. Elena Rodriguez',
        position: 'VP of Operations',
        department: 'Operations',
        photo: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
        email: '<EMAIL>',
        phone: '+****************',
        experience: '10 years',
        education: 'MBA Operations, Stanford',
        specialization: 'Educational Operations',
        startDate: '2020-06-15',
        salary: 160000,
        performance: 94,
        reportsTo: 'principal-001',
        directReports: ['hr-director', 'finance-director', 'facilities-director']
      },
      {
        id: 'vp-technology',
        name: 'Dr. James Wilson',
        position: 'VP of Technology',
        department: 'Technology',
        photo: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        email: '<EMAIL>',
        phone: '+****************',
        experience: '14 years',
        education: 'PhD Computer Science, Carnegie Mellon',
        specialization: 'AI & Quantum Computing',
        startDate: '2020-02-01',
        salary: 200000,
        performance: 97,
        reportsTo: 'principal-001',
        directReports: ['it-director', 'ai-director', 'security-director']
      }
    ],
    departmentHeads: [
      {
        id: 'dept-ai',
        name: 'Dr. Lisa Wang',
        position: 'Head of AI & Robotics',
        department: 'AI & Robotics',
        photo: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face',
        email: '<EMAIL>',
        phone: '+****************',
        experience: '8 years',
        education: 'PhD AI, Stanford',
        specialization: 'Neural Networks',
        startDate: '2021-01-15',
        salary: 140000,
        performance: 95,
        reportsTo: 'vp-academic',
        teamSize: 18,
        students: 412
      },
      {
        id: 'dept-quantum',
        name: 'Prof. Ahmed Hassan',
        position: 'Head of Quantum Computing',
        department: 'Quantum Computing',
        photo: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
        email: '<EMAIL>',
        phone: '+****************',
        experience: '11 years',
        education: 'PhD Quantum Physics, Oxford',
        specialization: 'Quantum Algorithms',
        startDate: '2020-09-01',
        salary: 145000,
        performance: 98,
        reportsTo: 'vp-academic',
        teamSize: 16,
        students: 298
      },
      {
        id: 'dept-bio',
        name: 'Dr. Maria Santos',
        position: 'Head of Bioengineering',
        department: 'Bioengineering',
        photo: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=150&h=150&fit=crop&crop=face',
        email: '<EMAIL>',
        phone: '+****************',
        experience: '9 years',
        education: 'PhD Bioengineering, MIT',
        specialization: 'Gene Editing',
        startDate: '2021-03-15',
        salary: 135000,
        performance: 93,
        reportsTo: 'vp-academic',
        teamSize: 12,
        students: 267
      }
    ],
    supportStaff: [
      {
        id: 'hr-director',
        name: 'Jennifer Kim',
        position: 'HR Director',
        department: 'Human Resources',
        photo: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150&h=150&fit=crop&crop=face',
        email: '<EMAIL>',
        phone: '+****************',
        experience: '7 years',
        education: 'MBA HR, Wharton',
        specialization: 'Talent Management',
        startDate: '2021-05-01',
        salary: 95000,
        performance: 91,
        reportsTo: 'vp-operations',
        teamSize: 8
      },
      {
        id: 'finance-director',
        name: 'Robert Thompson',
        position: 'Finance Director',
        department: 'Finance',
        photo: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face',
        email: '<EMAIL>',
        phone: '+****************',
        experience: '12 years',
        education: 'CPA, MBA Finance',
        specialization: 'Educational Finance',
        startDate: '2020-08-15',
        salary: 110000,
        performance: 96,
        reportsTo: 'vp-operations',
        teamSize: 6
      }
    ]
  },
  statistics: {
    totalEmployees: 156,
    totalDepartments: 12,
    averageTenure: 3.2,
    averageSalary: 125000,
    performanceAverage: 94.5,
    retentionRate: 96.8,
    diversityIndex: 87.3,
    satisfactionScore: 4.6
  },
  recentHires: [
    {
      name: 'Dr. Alex Chen',
      position: 'Quantum AI Researcher',
      department: 'AI & Robotics',
      startDate: '2024-03-01',
      photo: 'https://images.unsplash.com/photo-1519345182560-3f2917c472ef?w=150&h=150&fit=crop&crop=face'
    },
    {
      name: 'Prof. Sarah Kim',
      position: 'Metaverse Learning Designer',
      department: 'Metaverse Studies',
      startDate: '2024-02-15',
      photo: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
    }
  ],
  upcomingReviews: [
    {
      employeeName: 'Dr. Lisa Wang',
      position: 'Head of AI & Robotics',
      reviewDate: '2024-03-25',
      reviewType: 'Annual Performance Review',
      currentRating: 95
    },
    {
      employeeName: 'Prof. Ahmed Hassan',
      position: 'Head of Quantum Computing',
      reviewDate: '2024-03-28',
      reviewType: 'Promotion Review',
      currentRating: 98
    }
  ]
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 200));
    
    res.status(200).json(staffData);
  } catch (error) {
    console.error('API error:', error);
    res.status(500).json({ 
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}

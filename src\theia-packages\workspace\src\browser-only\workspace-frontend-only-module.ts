// *****************************************************************************
// Copyright (C) 2023 EclipseSource and others.
//
// This program and the accompanying materials are made available under the
// terms of the Eclipse Public License v. 2.0 which is available at
// http://www.eclipse.org/legal/epl-2.0.
//
// This Source Code may also be made available under the following Secondary
// Licenses when the conditions for such availability set forth in the Eclipse
// Public License v. 2.0 are satisfied: GNU General Public License, version 2
// with the GNU Classpath Exception which is available at
// https://www.gnu.org/software/classpath/license.html.
//
// SPDX-License-Identifier: EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0
// *****************************************************************************

import { ContainerModule, interfaces } from '@theia/core/shared/inversify';
import { BrowserOnlyWorkspaceServer } from './browser-only-workspace-server';
import { WorkspaceServer } from '../common';

export default new ContainerModule((bind: interfaces.Bind, unbind: interfaces.Unbind, isBound: interfaces.IsBound, rebind: interfaces.Rebind) => {
    bind(BrowserOnlyWorkspaceServer).toSelf().inSingletonScope();
    if (isBound(WorkspaceServer)) {
        rebind(WorkspaceServer).toService(BrowserOnlyWorkspaceServer);
    } else {
        bind(WorkspaceServer).toService(BrowserOnlyWorkspaceServer);
    }
});

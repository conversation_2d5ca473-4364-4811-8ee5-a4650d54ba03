/********************************************************************************
 * Copyright (C) 2020 TypeFox and others.
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License v. 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * This Source Code may also be made available under the following Secondary
 * Licenses when the conditions for such availability set forth in the Eclipse
 * Public License v. 2.0 are satisfied: GNU General Public License, version 2
 * with the GNU Classpath Exception which is available at
 * https://www.gnu.org/software/classpath/license.html.
 *
 * SPDX-License-Identifier: EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0
 ********************************************************************************/

:root {
  --theia-vsx-extension-icon-size: calc(var(--theia-ui-icon-font-size) * 3);
  --theia-vsx-extension-editor-icon-size: calc(var(--theia-vsx-extension-icon-size) * 3);
}

.vsx-search-container {
  display: flex;
  align-items: center;
  width: 100%;
  background: var(--theia-input-background);
  border-style: solid;
  border-width: var(--theia-border-width);
  border-color: var(--theia-input-background);
  border-radius: 2px;
}

.vsx-search-container:focus-within {
  border-color: var(--theia-focusBorder);
}

.vsx-search-container .option-buttons {
  height: 23px;
  display: flex;
  align-items: center;
  align-self: flex-start;
  background-color: none;
  margin: 2px;
}

.vsx-search-container .option {
  margin: 0 1px;
  display: inline-block;
  box-sizing: border-box;
  user-select: none;
  background-repeat: no-repeat;
  background-position: center;
  border: var(--theia-border-width) solid transparent;
  opacity: 0.7;
  cursor: pointer;
}

.vsx-search-container .option.enabled {
  color: var(--theia-inputOption-activeForeground);
  border: var(--theia-border-width) var(--theia-inputOption-activeBorder) solid;
  background-color: var(--theia-inputOption-activeBackground);
  opacity: 1;
}

.vsx-search-container .option:hover {
  opacity: 1;
}

.theia-vsx-extensions {
  height: 100%;
}

.theia-vsx-extension,
.theia-vsx-extensions-view-container .part>.body {
  min-height: calc(var(--theia-content-line-height) * 3);
}

.theia-vsx-extensions-search-bar {
  display: flex;
  align-content: center;
  padding: var(--theia-ui-padding) 
           max(var(--theia-scrollbar-width), var(--theia-ui-padding)) 
           var(--theia-ui-padding) 
           calc(var(--theia-ui-padding) * 3);
}

.theia-vsx-extensions-search-bar .theia-input {
  overflow: hidden;
  flex: 1;
  background: transparent;
}

.theia-vsx-extensions-search-bar .theia-input:focus {
  border: none;
  outline: none;
}

.theia-vsx-extension {
  display: flex;
  flex-direction: row;
  line-height: calc(var(--theia-content-line-height) * 17 / 22);
  align-items: center;
}

.theia-vsx-extension-icon {
  height: var(--theia-vsx-extension-icon-size);
  width: var(--theia-vsx-extension-icon-size);
  align-self: center;
  padding-right: calc(var(--theia-ui-padding) * 2.5);
  flex-shrink: 0;
  object-fit: contain;
}

.theia-vsx-extension-icon.placeholder {
  background-size: var(--theia-vsx-extension-icon-size);
  background-repeat: no-repeat;
  background-image: url("defaultIcon.png");
}

.theia-vsx-extension-content {
  display: flex;
  flex-direction: column;
  width: calc(100% - var(--theia-vsx-extension-icon-size) - var(--theia-ui-padding) * 2.5);
}

.theia-vsx-extension-content .title {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  white-space: nowrap;
}

.theia-vsx-extension-content .title .name {
  font-weight: bold;
}

.theia-vsx-extension-content .disabled {
  font-weight: bold;
}

.theia-vsx-extension-content .title .version,
.theia-vsx-extension-content .title .stat {
  opacity: 0.85;
  font-size: 80%;
}

.theia-vsx-extension-content .title .stat .codicon {
  font-size: 110%;
}

.theia-vsx-extension-content .title .stat .download-count,
.theia-vsx-extension-content .title .stat .average-rating {
  display: inline-flex;
  align-items: center;
}

.theia-vsx-extension-content .title .stat .average-rating>i {
  color: #ff8e00;
}

.theia-vsx-extension-editor .download-count>i,
.theia-vsx-extension-content .title .stat .average-rating>i,
.theia-vsx-extension-content .title .stat .download-count>i {
  padding-right: calc(var(--theia-ui-padding) / 2);
}

.theia-vsx-extension-content .title .stat .average-rating,
.theia-vsx-extension-content .title .stat .download-count {
  padding-left: var(--theia-ui-padding);
}

.theia-vsx-extension-description {
  padding-right: calc(var(--theia-ui-padding) * 2);
}

.theia-vsx-extension-publisher {
  font-weight: 600;
  font-size: 90%;
}

.theia-vsx-extension-action-bar {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  white-space: nowrap;
}

.theia-vsx-extension-action-bar .codicon-verified-filled {
  color: var(--theia-extensionIcon-verifiedForeground);
  margin-right: 2px;
}

.theia-vsx-extension-publisher-container {
  display: flex;
}

.theia-vsx-extension-action-bar .action {
  font-size: 90%;
  min-width: auto !important;
  padding: 2px var(--theia-ui-padding) !important;
  margin-top: 2px;
  vertical-align: middle;
}

/* Editor Section */

.theia-vsx-extension-editor {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding-top: 0;
  position: relative;
}

.theia-vsx-extension-editor .header {
  display: flex;
  padding: calc(var(--theia-ui-padding) * 3)
           max(var(--theia-ui-padding), var(--theia-scrollbar-width))
           calc(var(--theia-ui-padding) * 3)
           calc(var(--theia-ui-padding) * 3);
  flex-shrink: 0;
  border-bottom: 1px solid hsla(0, 0%, 50%, 0.5);
  width: 100%;
  background: var(--theia-editor-background);
}

.theia-vsx-extension-editor .scroll-container {
  position: relative;
  padding-top: 0;
  max-width: 100%;
  width: 100%;
}

.theia-vsx-extension-editor .body {
  flex: 1;
  padding: calc(var(--theia-ui-padding) * 2);
  padding-top: 0;
  max-width: 1000px;
  margin: 0 auto;
  line-height: 22px;
}

.theia-vsx-extension-editor .body h1 {
  padding-bottom: var(--theia-ui-padding);
  border-bottom: 1px solid hsla(0, 0%, 50%, 0.5);
  margin-top: calc(var(--theia-ui-padding) * 5);
}

.theia-vsx-extension-editor .body a {
  text-decoration: none;
}

.theia-vsx-extension-editor .body a:hover {
  text-decoration: underline;
}

.theia-vsx-extension-editor .body table {
  border-collapse: collapse;
}

.theia-vsx-extension-editor .body table>thead>tr>th {
  text-align: left;
  border-bottom: 1px solid var(--theia-extensionEditor-tableHeadBorder);
}

.theia-vsx-extension-editor .body table>thead>tr>th,
.theia-vsx-extension-editor .body table>thead>tr>td,
.theia-vsx-extension-editor .body table>tbody>tr>th,
.theia-vsx-extension-editor .body table>tbody>tr>td {
  padding: 5px 10px;
}

.theia-vsx-extension-editor .body table>tbody>tr+tr>td {
  border-top: 1px solid var(--theia-extensionEditor-tableCellBorder);
}

.theia-vsx-extension-editor .scroll-container .body pre {
  white-space: normal;
}

.theia-vsx-extension-editor .body img {
  max-width: 100%;
}

.theia-vsx-extension-editor .header .icon-container {
  height: var(--theia-vsx-extension-editor-icon-size);
  width: var(--theia-vsx-extension-editor-icon-size);
  align-self: center;
  padding-right: calc(var(--theia-ui-padding) * 2.5);
  flex-shrink: 0;
  object-fit: contain;
}

.theia-vsx-extension-editor .header .icon-container.placeholder {
  background-size: var(--theia-vsx-extension-editor-icon-size);
  background-repeat: no-repeat;
  background-image: url("defaultIcon.png");
}

.theia-vsx-extension-editor .header .details {
  overflow: hidden;
  user-select: text;
  -webkit-user-select: text;
}

.theia-vsx-extension-editor .header .details .title,
.theia-vsx-extension-editor .header .details .subtitle {
  display: flex;
  align-items: center;
}

.theia-vsx-extension-editor .header .details .title .name {
  flex: 0;
  font-size: calc(var(--theia-ui-font-size1) * 2);
  font-weight: 600;
  white-space: nowrap;
  cursor: pointer;
}

.theia-vsx-extension-editor .header .details .title .identifier {
  margin-left: calc(var(--theia-ui-padding) * 5 / 3);
  opacity: 0.6;
  background: hsla(0, 0%, 68%, 0.31);
  user-select: text;
  -webkit-user-select: text;
  white-space: nowrap;
}

.theia-vsx-extension-editor .header .details .title .preview {
  background: #d63f26;
}

.vs .theia-vsx-extension-editor .header .details .title .preview {
  color: white;
}

.theia-vsx-extension-editor .header .details .title .identifier,
.theia-vsx-extension-editor .header .details .title .preview,
.theia-vsx-extension-editor .header .details .title .builtin {
  line-height: var(--theia-code-line-height);
}

.theia-vsx-extension-editor .header .details .title .identifier,
.theia-vsx-extension-editor .header .details .title .preview {
  padding: calc(var(--theia-ui-padding) * 2 / 3);
  padding-top: 0px;
  padding-bottom: 0px;
  border-radius: calc(var(--theia-ui-padding) * 2 / 3);
}

.theia-vsx-extension-editor .header .details .title .preview,
.theia-vsx-extension-editor .header .details .title .builtin {
  font-size: var(--theia-ui-font-size0);
  font-style: italic;
  margin-left: calc(var(--theia-ui-padding) * 5 / 3);
}

.theia-vsx-extension-editor .header .details .subtitle {
  padding-top: var(--theia-ui-padding);
  white-space: nowrap;
}

.theia-vsx-extension-editor .header .details .subtitle>span {
  display: flex;
  align-items: center;
  cursor: pointer;
  line-height: var(--theia-content-line-height);
  height: var(--theia-content-line-height);
}

.theia-vsx-extension-editor .header .details .subtitle>span:not(:first-child):not(:empty) {
  border-left: 1px solid hsla(0, 0%, 50%, 0.7);
  padding-left: calc(var(--theia-ui-padding) * 2);
  margin-left: calc(var(--theia-ui-padding) * 2);
  font-weight: 500;
}

.theia-vsx-extension-editor .header .details .subtitle .publisher {
  font-size: var(--theia-ui-font-size3);
}

.theia-vsx-extension-editor .header .details .subtitle .publisher .namespace-access,
.theia-vsx-extension-editor .header .details .subtitle .download-count::before {
  padding-right: var(--theia-ui-padding);
}

.theia-vsx-extension-editor .header .details .subtitle .average-rating>i {
  color: #ff8e00;
}

.theia-vsx-extension-editor .header .details .subtitle .average-rating>i:not(:first-child) {
  padding-left: calc(var(--theia-ui-padding) / 2);
}

.theia-vsx-extension-editor .header .details .description {
  margin-top: calc(var(--theia-ui-padding) * 5 / 3);
}

.theia-vsx-extension-editor .action {
  font-weight: 600;
  margin-top: calc(var(--theia-ui-padding) * 5 / 3);
  margin-left: 0px;
  padding: 1px var(--theia-ui-padding);
  vertical-align: middle;
}

/** Theming */

.theia-vsx-extension-editor .action.prominent,
.theia-vsx-extension-action-bar .action.prominent {
  color: var(--theia-extensionButton-prominentForeground);
  background-color: var(--theia-extensionButton-prominentBackground);
}

.theia-vsx-extension-editor .action.prominent:hover,
.theia-vsx-extension-action-bar .action.prominent:hover {
  background-color: var(--theia-extensionButton-prominentHoverBackground);
}

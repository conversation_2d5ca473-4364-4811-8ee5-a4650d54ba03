
import type { NextApiRequest, NextApiResponse } from 'next';
import { generateText } from '../../backend/lib/gemini';

type ResponseData = {
  analysis?: string;
  error?: string;
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ResponseData>
) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  try {
    const mockDashboardData = `
      - Past Prelesss Gauge: High Stress
      - Wellness Tracker: High activity, low recovery
      - Safety Checklist: Reports 'Anxiety' and a 'Missed Meeting'
    `;
    const prompt = `You are a futuristic AI assistant named '<PERSON><PERSON><PERSON><PERSON>' providing real-time status updates inside a high-tech UI. Based on the following user data, provide a concise, one-sentence analysis of their current wellness and mood state. Keep the tone slightly robotic but helpful. Data: ${mockDashboardData}`;
    
    const analysis = await generateText(prompt);
    res.status(200).json({ analysis });
  } catch (error) {
    console.error(error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred.';
    res.status(500).json({ error: `Analysis failed: ${errorMessage}` });
  }
}

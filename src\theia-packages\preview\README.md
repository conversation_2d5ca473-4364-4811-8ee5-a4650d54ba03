<div align='center'>

<br />

<img src='https://raw.githubusercontent.com/eclipse-theia/theia/master/logo/theia.svg?sanitize=true' alt='theia-ext-logo' width='100px' />

<h2>ECLIPSE THEIA - PREVIEW EXTENSION</h2>

<hr />

</div>

## Description

The `@theia/preview` extension adds the ability to display rendered previews of supported resources.\
The extension comes with built-in support for rendering `markdown` files.

## Contribute Custom Previews

To provide custom previews implement and bind the `PreviewHandler` interface, e.g.

```typescript
@injectable
class MyPreviewHandler implements PreviewHandler {
  ...
}
// in container
bind(MyPreviewHandler).toSelf().inSingletonScope();
bind(PreviewHandler).toService(MyPreviewHandler);
```

## Additional Information

- [API documentation for `@theia/preview`](https://eclipse-theia.github.io/theia/docs/next/modules/preview.html)
- [Theia - GitHub](https://github.com/eclipse-theia/theia)
- [Theia - Website](https://theia-ide.org/)

## License

- [Eclipse Public License 2.0](http://www.eclipse.org/legal/epl-2.0/)
- [一 (Secondary) GNU General Public License, version 2 with the GNU Classpath Exception](https://projects.eclipse.org/license/secondary-gpl-2.0-cp)

## Trademark

"Theia" is a trademark of the Eclipse Foundation
<https://www.eclipse.org/theia>

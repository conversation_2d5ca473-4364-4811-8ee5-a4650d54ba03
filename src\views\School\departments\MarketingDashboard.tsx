import React, { useState, useEffect } from 'react';
import { Card } from '../../../components/Card';
import {
  MegaphoneIcon,
  GlobeAltIcon,
  ChartBarIcon,
  UserGroupIcon,
  StarIcon,
  EyeIcon,
  HeartIcon,
  ShareIcon
} from '@heroicons/react/24/solid';

interface MarketingDashboardProps {
  activeSubSection: string;
}

export const MarketingDashboard: React.FC<MarketingDashboardProps> = ({ activeSubSection }) => {
  const [marketingData, setMarketingData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchMarketingData = async () => {
      try {
        setLoading(true);
        await new Promise(resolve => setTimeout(resolve, 500));

        const mockData = {
          overview: {
            globalReach: 2847000,
            metaverseVisitors: 156000,
            aiGeneratedContent: 847,
            virtualEvents: 23,
            hologramAds: 156,
            socialSentiment: 94.7,
            influencerPartners: 89,
            nftCampaigns: 12
          },
          campaigns: [
            {
              name: 'Metaverse Open House',
              platform: 'Virtual Reality',
              reach: 450000,
              engagement: 87.3,
              roi: '+340%',
              status: 'Active',
              color: 'bg-blue-500/20 text-blue-400'
            },
            {
              name: 'AI Teacher Showcase',
              platform: 'Hologram Network',
              reach: 320000,
              engagement: 92.1,
              roi: '+280%',
              status: 'Live',
              color: 'bg-green-500/20 text-green-400'
            },
            {
              name: 'Quantum Learning NFTs',
              platform: 'Blockchain Social',
              reach: 180000,
              engagement: 96.4,
              roi: '+520%',
              status: 'Trending',
              color: 'bg-purple-500/20 text-purple-400'
            }
          ],
          socialMetrics: [
            { platform: 'MetaBook VR', followers: '2.4M', engagement: '94.7%', growth: '+23.4%', color: 'text-blue-400' },
            { platform: 'HoloGram', followers: '1.8M', engagement: '87.2%', growth: '+18.7%', color: 'text-purple-400' },
            { platform: 'QuantumTok', followers: '3.2M', engagement: '96.1%', growth: '+45.2%', color: 'text-pink-400' },
            { platform: 'AI-LinkedIn', followers: '890K', followers_type: 'professionals', engagement: '89.3%', growth: '+12.8%', color: 'text-cyan-400' }
          ],
          aiInsights: [
            {
              insight: 'Quantum physics content performs 340% better than traditional subjects',
              confidence: 96,
              action: 'Increase quantum-themed content by 60%',
              impact: 'High'
            },
            {
              insight: 'Holographic demonstrations drive 87% more enrollment inquiries',
              confidence: 94,
              action: 'Schedule weekly hologram showcases',
              impact: 'High'
            },
            {
              insight: 'Parent engagement peaks during AI teacher interactions',
              confidence: 89,
              action: 'Create AI parent-teacher meetup events',
              impact: 'Medium'
            }
          ],
          upcomingEvents: [
            { event: 'Global VR Science Fair', date: '2024-03-25', attendees: '50K+', platform: 'Metaverse' },
            { event: 'AI Ethics Symposium', date: '2024-03-30', attendees: '25K+', platform: 'Hologram Network' },
            { event: 'Quantum Computing Workshop', date: '2024-04-05', attendees: '15K+', platform: 'Neural Interface' }
          ]
        };

        setMarketingData(mockData);
      } catch (error) {
        console.error('Failed to fetch marketing data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchMarketingData();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-cyan-400">Loading Metaverse Marketing...</div>
      </div>
    );
  }

  const renderContent = () => {
    switch (activeSubSection) {
      case 'dashboard':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Metaverse Marketing Overview */}
            <Card title="🌐 Metaverse Marketing Hub" className="lg:col-span-3">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4">
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <GlobeAltIcon className="w-6 h-6 text-white" />
                  </div>
                  <p className="text-2xl font-bold text-white">{(marketingData.overview.globalReach / 1000000).toFixed(1)}M</p>
                  <p className="text-sm text-gray-400">Global Reach</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <EyeIcon className="w-6 h-6 text-white" />
                  </div>
                  <p className="text-2xl font-bold text-white">{(marketingData.overview.metaverseVisitors / 1000).toFixed(0)}K</p>
                  <p className="text-sm text-gray-400">Metaverse Visitors</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <ChartBarIcon className="w-6 h-6 text-white" />
                  </div>
                  <p className="text-2xl font-bold text-white">{marketingData.overview.socialSentiment}%</p>
                  <p className="text-sm text-gray-400">AI Sentiment</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <StarIcon className="w-6 h-6 text-white" />
                  </div>
                  <p className="text-2xl font-bold text-white">{marketingData.overview.nftCampaigns}</p>
                  <p className="text-sm text-gray-400">NFT Campaigns</p>
                </div>
              </div>
            </Card>

            {/* Active Campaigns */}
            <Card title="🚀 Active Campaigns" className="lg:col-span-2">
              <div className="space-y-3 p-4">
                {marketingData.campaigns.map((campaign: any, index: number) => (
                  <div key={index} className={`p-3 rounded-lg ${campaign.color.split(' ')[0]} border border-gray-600/30`}>
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1">
                        <h4 className={`font-semibold ${campaign.color.split(' ').slice(1).join(' ')}`}>{campaign.name}</h4>
                        <p className="text-sm text-gray-400">{campaign.platform}</p>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        campaign.status === 'Active' ? 'bg-green-500/20 text-green-400' :
                        campaign.status === 'Live' ? 'bg-blue-500/20 text-blue-400' :
                        'bg-purple-500/20 text-purple-400'
                      }`}>
                        {campaign.status}
                      </span>
                    </div>
                    <div className="grid grid-cols-3 gap-2 text-xs">
                      <div className="text-center">
                        <p className="text-white font-semibold">{(campaign.reach / 1000).toFixed(0)}K</p>
                        <p className="text-gray-400">Reach</p>
                      </div>
                      <div className="text-center">
                        <p className="text-white font-semibold">{campaign.engagement}%</p>
                        <p className="text-gray-400">Engagement</p>
                      </div>
                      <div className="text-center">
                        <p className="text-green-400 font-semibold">{campaign.roi}</p>
                        <p className="text-gray-400">ROI</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Social Media Metrics */}
            <Card title="📱 Social Metaverse">
              <div className="space-y-3 p-4">
                {marketingData.socialMetrics.map((social: any, index: number) => (
                  <div key={index} className="p-3 rounded-lg bg-gray-800/40 border border-gray-600/30">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="text-sm font-medium text-white">{social.platform}</h4>
                      <span className={`text-sm ${social.color}`}>{social.growth}</span>
                    </div>
                    <div className="flex justify-between items-center text-xs">
                      <span className="text-gray-400">{social.followers} followers</span>
                      <span className="text-cyan-400">{social.engagement} engaged</span>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </div>
        );

      case 'campaigns':
        return (
          <Card title="🧠 AI Marketing Insights">
            <div className="p-4">
              <div className="space-y-4">
                {marketingData.aiInsights.map((insight: any, index: number) => (
                  <div key={index} className={`p-4 rounded-lg border ${
                    insight.impact === 'High' ? 'bg-gradient-to-r from-red-900/20 to-pink-900/20 border-red-500/30' :
                    'bg-gradient-to-r from-blue-900/20 to-purple-900/20 border-blue-500/30'
                  }`}>
                    <div className="flex items-center justify-between mb-2">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        insight.impact === 'High' ? 'bg-red-500/20 text-red-400' :
                        'bg-blue-500/20 text-blue-400'
                      }`}>
                        {insight.impact} Impact
                      </span>
                      <span className="text-xs text-gray-400">{insight.confidence}% confidence</span>
                    </div>
                    <p className="text-sm text-gray-300 mb-2">{insight.insight}</p>
                    <p className="text-sm text-cyan-400">💡 {insight.action}</p>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        );

      default:
        return (
          <Card title="Metaverse Marketing">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Future Marketing Department</h3>
              <p className="text-gray-400">AI-powered campaigns across metaverse, hologram networks, and quantum social media.</p>
            </div>
          </Card>
        );
    }
  };

  return (
    <div className="p-4 h-full">
      {renderContent()}
    </div>
  );
};

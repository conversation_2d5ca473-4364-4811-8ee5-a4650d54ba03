import React from 'react';
import { Card } from '../../../components/Card';

interface MarketingDashboardProps {
  activeSubSection: string;
}

export const MarketingDashboard: React.FC<MarketingDashboardProps> = ({ activeSubSection }) => {
  return (
    <div className="p-4 h-full">
      <Card title="Marketing Dashboard">
        <div className="p-6 text-center">
          <h3 className="text-xl font-bold text-white mb-4">Marketing Department</h3>
          <p className="text-gray-400">School marketing campaigns and outreach programs.</p>
        </div>
      </Card>
    </div>
  );
};

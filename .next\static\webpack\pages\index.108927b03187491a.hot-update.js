"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _src_components_Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../src/components/Sidebar */ \"./src/components/Sidebar.tsx\");\n/* harmony import */ var _src_components_Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../src/components/Header */ \"./src/components/Header.tsx\");\n/* harmony import */ var _src_components_Footer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../src/components/Footer */ \"./src/components/Footer.tsx\");\n/* harmony import */ var _src_components_RightSidebar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../src/components/RightSidebar */ \"./src/components/RightSidebar.tsx\");\n/* harmony import */ var _src_views_School_SchoolView__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../src/views/School/SchoolView */ \"./src/views/School/SchoolView.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst LoadingComponent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-grow flex items-center justify-center text-cyan-400\",\n        children: \"Loading View...\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n        lineNumber: 11,\n        columnNumber: 32\n    }, undefined);\n_c = LoadingComponent;\n// Dynamically import views for code splitting and performance\nconst viewComponents = {\n    Dashboard: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Dashboard_DashboardView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Dashboard/DashboardView */ \"./src/views/Dashboard/DashboardView.tsx\")).then((mod)=>mod.DashboardView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Dashboard/DashboardView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    School: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/School/SchoolView */ \"./src/views/School/SchoolView.tsx\")).then((mod)=>mod.SchoolView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/School/SchoolView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Tool: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Tool_ToolView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Tool/ToolView */ \"./src/views/Tool/ToolView.tsx\")).then((mod)=>mod.ToolView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Tool/ToolView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Market: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Market_MarketView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Market/MarketView */ \"./src/views/Market/MarketView.tsx\")).then((mod)=>mod.MarketView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Market/MarketView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Bookstore: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Bookstore_BookstoreView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Bookstore/BookstoreView */ \"./src/views/Bookstore/BookstoreView.tsx\")).then((mod)=>mod.BookstoreView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Bookstore/BookstoreView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Concierge: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Concierge_ConciergeView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Concierge/ConciergeView */ \"./src/views/Concierge/ConciergeView.tsx\")).then((mod)=>mod.ConciergeView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Concierge/ConciergeView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Analytic: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Analytic_AnalyticView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Analytic/AnalyticView */ \"./src/views/Analytic/AnalyticView.tsx\")).then((mod)=>mod.AnalyticView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Analytic/AnalyticView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Setting: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Setting_SettingView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Setting/SettingView */ \"./src/views/Setting/SettingView.tsx\")).then((mod)=>mod.SettingView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Setting/SettingView\"\n            ]\n        },\n        loading: LoadingComponent\n    })\n};\nconst HomePage = ()=>{\n    _s();\n    const [activeView, setActiveView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Dashboard\");\n    const [activeDepartment, setActiveDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"school\");\n    const ActiveComponent = viewComponents[activeView] || viewComponents.Dashboard;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Eyes Shield UI\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"A futuristic, high-tech dashboard UI with a glassmorphism theme, displaying various data visualizations and system statuses on a semi-transparent, glowing panel.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1.0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"main-background font-poppins text-[#E0E0E0] w-screen h-screen overflow-hidden dashboard-auto-scale\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/60 backdrop-blur-xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10 bg-container-bg flex-1 flex border-0 min-h-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Sidebar__WEBPACK_IMPORTED_MODULE_4__.Sidebar, {\n                                    activeView: activeView,\n                                    setActiveView: (view)=>setActiveView(view)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 flex flex-col min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"responsive-spacing-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Header__WEBPACK_IMPORTED_MODULE_5__.Header, {\n                                                showSchoolButtons: activeView === \"School\",\n                                                activeDepartment: activeDepartment,\n                                                setActiveDepartment: setActiveDepartment\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-h-0 overflow-auto responsive-spacing-sm\",\n                                            children: activeView === \"School\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_views_School_SchoolView__WEBPACK_IMPORTED_MODULE_8__.SchoolView, {\n                                                activeDepartment: activeDepartment,\n                                                setActiveDepartment: setActiveDepartment\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                                lineNumber: 56,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveComponent, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"responsive-spacing-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Footer__WEBPACK_IMPORTED_MODULE_6__.Footer, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_RightSidebar__WEBPACK_IMPORTED_MODULE_7__.RightSidebar, {\n                                    activeView: activeView,\n                                    setActiveView: (view)=>setActiveView(view)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(HomePage, \"hiL3npIHT5BoUgaxxT9SkQafJgA=\");\n_c1 = HomePage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HomePage);\nvar _c, _c1;\n$RefreshReg$(_c, \"LoadingComponent\");\n$RefreshReg$(_c1, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n"));

/***/ })

});
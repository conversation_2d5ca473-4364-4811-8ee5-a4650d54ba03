{"name": "@theia/vsx-registry", "version": "1.63.0", "description": "Theia - VSX Registry", "dependencies": {"@theia/core": "1.63.0", "@theia/filesystem": "1.63.0", "@theia/navigator": "1.63.0", "@theia/ovsx-client": "1.63.0", "@theia/plugin-ext": "1.63.0", "@theia/plugin-ext-vscode": "1.63.0", "@theia/preferences": "1.63.0", "@theia/workspace": "1.63.0", "limiter": "^2.1.0", "luxon": "^2.4.0", "p-debounce": "^2.1.0", "semver": "^7.5.4", "tslib": "^2.6.2"}, "publishConfig": {"access": "public"}, "keywords": ["theia-extension"], "theiaExtensions": [{"frontend": "lib/common/vsx-registry-common-module", "backend": "lib/common/vsx-registry-common-module"}, {"frontend": "lib/browser/vsx-registry-frontend-module", "backend": "lib/node/vsx-registry-backend-module"}], "license": "EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0", "repository": {"type": "git", "url": "https://github.com/eclipse-theia/theia.git"}, "bugs": {"url": "https://github.com/eclipse-theia/theia/issues"}, "homepage": "https://github.com/eclipse-theia/theia", "files": ["lib", "src"], "scripts": {"build": "theiaext build", "clean": "theiaext clean", "compile": "theiaext compile", "lint": "theiaext lint", "test": "theiaext test", "watch": "theiaext watch"}, "devDependencies": {"@theia/ext-scripts": "1.63.0", "@types/luxon": "^2.3.2"}, "nyc": {"extends": "../../configs/nyc.json"}, "gitHead": "21358137e41342742707f660b8e222f940a27652"}
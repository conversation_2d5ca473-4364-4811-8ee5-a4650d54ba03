"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/PerseusRenderer.tsx":
/*!********************************************!*\
  !*** ./src/components/PerseusRenderer.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PerseusRenderer: function() { return /* binding */ PerseusRenderer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst PerseusRenderer = (param)=>{\n    let { widgetType, widgetData } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!containerRef.current) return;\n        // Clear previous content\n        containerRef.current.innerHTML = \"\";\n        // Render based on widget type\n        switch(widgetType){\n            case \"numeric-input\":\n                renderNumericInput(containerRef.current);\n                break;\n            case \"categorizer\":\n                renderCategorizer(containerRef.current);\n                break;\n            case \"matcher\":\n                renderMatcher(containerRef.current);\n                break;\n            case \"orderer\":\n                renderOrderer(containerRef.current);\n                break;\n            case \"radio\":\n                renderRadio(containerRef.current);\n                break;\n            case \"expression\":\n                renderExpression(containerRef.current);\n                break;\n            case \"grapher\":\n                renderGrapher(containerRef.current);\n                break;\n            case \"matrix\":\n                renderMatrix(containerRef.current);\n                break;\n            case \"molecule\":\n                renderMolecule(containerRef.current);\n                break;\n            case \"phet-simulation\":\n                renderPhetSimulation(containerRef.current);\n                break;\n            case \"interactive-graph\":\n                renderInteractiveGraph(containerRef.current);\n                break;\n            case \"passage\":\n                renderPassage(containerRef.current);\n                break;\n            case \"sorter\":\n                renderSorter(containerRef.current);\n                break;\n            case \"cs-program\":\n                renderCSProgram(containerRef.current);\n                break;\n            case \"python-program\":\n                renderPythonProgram(containerRef.current);\n                break;\n            default:\n                renderPlaceholder(containerRef.current, widgetType);\n        }\n    }, [\n        widgetType,\n        widgetData\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"perseus-widget bg-white rounded-lg p-6 min-h-[400px]\",\n        style: {\n            fontFamily: \"Arial, sans-serif\"\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\PerseusRenderer.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PerseusRenderer, \"8puyVO4ts1RhCfXUmci3vLI3Njw=\");\n_c = PerseusRenderer;\n// Numeric Input Widget\nfunction renderNumericInput(container) {\n    container.innerHTML = '\\n    <div class=\"numeric-input-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Math Problem</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">What is 15 + 27?</p>\\n      <input \\n        type=\"number\" \\n        placeholder=\"Enter your answer\"\\n        style=\"\\n          padding: 10px; \\n          border: 2px solid #ddd; \\n          border-radius: 5px; \\n          font-size: 16px;\\n          width: 200px;\\n          margin-right: 10px;\\n        \"\\n        id=\"numeric-answer\"\\n      />\\n      <button \\n        onclick=\"checkNumericAnswer()\"\\n        style=\"\\n          background: #1c4f82; \\n          color: white; \\n          padding: 10px 20px; \\n          border: none; \\n          border-radius: 5px;\\n          cursor: pointer;\\n          font-size: 16px;\\n        \"\\n      >Check Answer</button>\\n      <div id=\"numeric-feedback\" style=\"margin-top: 15px; font-weight: bold;\"></div>\\n    </div>\\n  ';\n    // Add event handler\n    window.checkNumericAnswer = ()=>{\n        const input = document.getElementById(\"numeric-answer\");\n        const feedback = document.getElementById(\"numeric-feedback\");\n        if (input && feedback) {\n            const answer = parseInt(input.value);\n            if (answer === 42) {\n                feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Correct! Great job!</span>';\n            } else {\n                feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Try again. Think about 15 + 27.</span>';\n            }\n        }\n    };\n}\n// Categorizer Widget\nfunction renderCategorizer(container) {\n    container.innerHTML = '\\n    <div class=\"categorizer-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Categorize Animals</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">Drag each animal to the correct category:</p>\\n      \\n      <div style=\"display: flex; gap: 20px; margin-bottom: 20px;\">\\n        <div class=\"category-box\" style=\"\\n          border: 2px dashed #1c4f82; \\n          padding: 15px; \\n          min-height: 100px; \\n          width: 150px;\\n          border-radius: 8px;\\n          background: #f8f9fa;\\n        \">\\n          <h4 style=\"margin: 0 0 10px 0; color: #1c4f82;\">Mammals</h4>\\n          <div id=\"mammals-drop\" class=\"drop-zone\"></div>\\n        </div>\\n        \\n        <div class=\"category-box\" style=\"\\n          border: 2px dashed #28a745; \\n          padding: 15px; \\n          min-height: 100px; \\n          width: 150px;\\n          border-radius: 8px;\\n          background: #f8f9fa;\\n        \">\\n          <h4 style=\"margin: 0 0 10px 0; color: #28a745;\">Birds</h4>\\n          <div id=\"birds-drop\" class=\"drop-zone\"></div>\\n        </div>\\n      </div>\\n      \\n      <div style=\"display: flex; gap: 10px; flex-wrap: wrap;\">\\n        <div class=\"draggable-item\" draggable=\"true\" data-category=\"mammals\" style=\"\\n          background: #e9ecef; \\n          padding: 8px 12px; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">\\uD83D\\uDC15 Dog</div>\\n        <div class=\"draggable-item\" draggable=\"true\" data-category=\"birds\" style=\"\\n          background: #e9ecef; \\n          padding: 8px 12px; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">\\uD83E\\uDD85 Eagle</div>\\n        <div class=\"draggable-item\" draggable=\"true\" data-category=\"mammals\" style=\"\\n          background: #e9ecef; \\n          padding: 8px 12px; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">\\uD83D\\uDC31 Cat</div>\\n        <div class=\"draggable-item\" draggable=\"true\" data-category=\"birds\" style=\"\\n          background: #e9ecef; \\n          padding: 8px 12px; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">\\uD83D\\uDC26 Sparrow</div>\\n      </div>\\n      \\n      <button \\n        onclick=\"checkCategorizer()\"\\n        style=\"\\n          background: #1c4f82; \\n          color: white; \\n          padding: 10px 20px; \\n          border: none; \\n          border-radius: 5px;\\n          cursor: pointer;\\n          font-size: 16px;\\n          margin-top: 20px;\\n        \"\\n      >Check Categories</button>\\n      <div id=\"categorizer-feedback\" style=\"margin-top: 15px; font-weight: bold;\"></div>\\n    </div>\\n  ';\n    // Add drag and drop functionality\n    setupDragAndDrop();\n}\nfunction setupDragAndDrop() {\n    const draggables = document.querySelectorAll(\".draggable-item\");\n    const dropZones = document.querySelectorAll(\".drop-zone\");\n    draggables.forEach((draggable)=>{\n        draggable.addEventListener(\"dragstart\", (e)=>{\n            var _e_dataTransfer, _e_dataTransfer1;\n            (_e_dataTransfer = e.dataTransfer) === null || _e_dataTransfer === void 0 ? void 0 : _e_dataTransfer.setData(\"text/plain\", draggable.outerHTML);\n            (_e_dataTransfer1 = e.dataTransfer) === null || _e_dataTransfer1 === void 0 ? void 0 : _e_dataTransfer1.setData(\"category\", draggable.dataset.category || \"\");\n        });\n    });\n    dropZones.forEach((zone)=>{\n        zone.addEventListener(\"dragover\", (e)=>{\n            e.preventDefault();\n        });\n        zone.addEventListener(\"drop\", (e)=>{\n            var _e_dataTransfer, _e_dataTransfer1;\n            e.preventDefault();\n            const html = (_e_dataTransfer = e.dataTransfer) === null || _e_dataTransfer === void 0 ? void 0 : _e_dataTransfer.getData(\"text/plain\");\n            const category = (_e_dataTransfer1 = e.dataTransfer) === null || _e_dataTransfer1 === void 0 ? void 0 : _e_dataTransfer1.getData(\"category\");\n            if (html && zone.id.includes(category || \"\")) {\n                zone.innerHTML += html;\n            }\n        });\n    });\n    window.checkCategorizer = ()=>{\n        const mammalsZone = document.getElementById(\"mammals-drop\");\n        const birdsZone = document.getElementById(\"birds-drop\");\n        const feedback = document.getElementById(\"categorizer-feedback\");\n        if (mammalsZone && birdsZone && feedback) {\n            const mammalsCount = mammalsZone.children.length;\n            const birdsCount = birdsZone.children.length;\n            if (mammalsCount === 2 && birdsCount === 2) {\n                feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Perfect categorization!</span>';\n            } else {\n                feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Some animals are in wrong categories. Try again!</span>';\n            }\n        }\n    };\n}\n// Matcher Widget\nfunction renderMatcher(container) {\n    container.innerHTML = '\\n    <div class=\"matcher-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Match Words with Definitions</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">Click on a word, then click on its matching definition:</p>\\n      \\n      <div style=\"display: flex; gap: 40px;\">\\n        <div>\\n          <h4 style=\"color: #1c4f82;\">Words</h4>\\n          <div class=\"match-item\" data-match=\"photosynthesis\" onclick=\"selectMatch(this)\" style=\"\\n            background: #e9ecef; \\n            padding: 10px; \\n            margin: 5px 0; \\n            border-radius: 5px; \\n            cursor: pointer;\\n            border: 2px solid transparent;\\n          \">Photosynthesis</div>\\n          <div class=\"match-item\" data-match=\"gravity\" onclick=\"selectMatch(this)\" style=\"\\n            background: #e9ecef; \\n            padding: 10px; \\n            margin: 5px 0; \\n            border-radius: 5px; \\n            cursor: pointer;\\n            border: 2px solid transparent;\\n          \">Gravity</div>\\n          <div class=\"match-item\" data-match=\"democracy\" onclick=\"selectMatch(this)\" style=\"\\n            background: #e9ecef; \\n            padding: 10px; \\n            margin: 5px 0; \\n            border-radius: 5px; \\n            cursor: pointer;\\n            border: 2px solid transparent;\\n          \">Democracy</div>\\n        </div>\\n        \\n        <div>\\n          <h4 style=\"color: #28a745;\">Definitions</h4>\\n          <div class=\"match-item\" data-match=\"gravity\" onclick=\"selectMatch(this)\" style=\"\\n            background: #e9ecef; \\n            padding: 10px; \\n            margin: 5px 0; \\n            border-radius: 5px; \\n            cursor: pointer;\\n            border: 2px solid transparent;\\n          \">Force that pulls objects toward Earth</div>\\n          <div class=\"match-item\" data-match=\"photosynthesis\" onclick=\"selectMatch(this)\" style=\"\\n            background: #e9ecef; \\n            padding: 10px; \\n            margin: 5px 0; \\n            border-radius: 5px; \\n            cursor: pointer;\\n            border: 2px solid transparent;\\n          \">Process plants use to make food from sunlight</div>\\n          <div class=\"match-item\" data-match=\"democracy\" onclick=\"selectMatch(this)\" style=\"\\n            background: #e9ecef; \\n            padding: 10px; \\n            margin: 5px 0; \\n            border-radius: 5px; \\n            cursor: pointer;\\n            border: 2px solid transparent;\\n          \">Government by the people</div>\\n        </div>\\n      </div>\\n      \\n      <div id=\"matcher-feedback\" style=\"margin-top: 20px; font-weight: bold;\"></div>\\n    </div>\\n  ';\n    let selectedItems = [];\n    window.selectMatch = (element)=>{\n        if (selectedItems.length === 0) {\n            element.style.border = \"2px solid #1c4f82\";\n            selectedItems.push(element);\n        } else if (selectedItems.length === 1) {\n            const first = selectedItems[0];\n            const feedback = document.getElementById(\"matcher-feedback\");\n            if (first.dataset.match === element.dataset.match) {\n                first.style.background = \"#d4edda\";\n                element.style.background = \"#d4edda\";\n                first.style.border = \"2px solid #28a745\";\n                element.style.border = \"2px solid #28a745\";\n                if (feedback) feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Correct match!</span>';\n            } else {\n                first.style.border = \"2px solid #dc3545\";\n                element.style.border = \"2px solid #dc3545\";\n                if (feedback) feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Try again!</span>';\n                setTimeout(()=>{\n                    first.style.border = \"2px solid transparent\";\n                    element.style.border = \"2px solid transparent\";\n                }, 1000);\n            }\n            selectedItems = [];\n        }\n    };\n}\n// Orderer Widget\nfunction renderOrderer(container) {\n    container.innerHTML = '\\n    <div class=\"orderer-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Put in Chronological Order</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">Drag to arrange these historical events in order:</p>\\n      \\n      <div id=\"sortable-list\" style=\"min-height: 200px;\">\\n        <div class=\"sortable-item\" draggable=\"true\" data-order=\"3\" style=\"\\n          background: #e9ecef; \\n          padding: 15px; \\n          margin: 10px 0; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">World War II ends (1945)</div>\\n        <div class=\"sortable-item\" draggable=\"true\" data-order=\"1\" style=\"\\n          background: #e9ecef; \\n          padding: 15px; \\n          margin: 10px 0; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">American Civil War begins (1861)</div>\\n        <div class=\"sortable-item\" draggable=\"true\" data-order=\"2\" style=\"\\n          background: #e9ecef; \\n          padding: 15px; \\n          margin: 10px 0; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">World War I begins (1914)</div>\\n        <div class=\"sortable-item\" draggable=\"true\" data-order=\"4\" style=\"\\n          background: #e9ecef; \\n          padding: 15px; \\n          margin: 10px 0; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">Moon landing (1969)</div>\\n      </div>\\n      \\n      <button \\n        onclick=\"checkOrder()\"\\n        style=\"\\n          background: #1c4f82; \\n          color: white; \\n          padding: 10px 20px; \\n          border: none; \\n          border-radius: 5px;\\n          cursor: pointer;\\n          font-size: 16px;\\n          margin-top: 20px;\\n        \"\\n      >Check Order</button>\\n      <div id=\"orderer-feedback\" style=\"margin-top: 15px; font-weight: bold;\"></div>\\n    </div>\\n  ';\n    setupSortable();\n}\nfunction setupSortable() {\n    const sortableList = document.getElementById(\"sortable-list\");\n    if (!sortableList) return;\n    let draggedElement = null;\n    sortableList.addEventListener(\"dragstart\", (e)=>{\n        draggedElement = e.target;\n        if (draggedElement) {\n            draggedElement.style.opacity = \"0.5\";\n        }\n    });\n    sortableList.addEventListener(\"dragend\", (e)=>{\n        if (draggedElement) {\n            draggedElement.style.opacity = \"1\";\n            draggedElement = null;\n        }\n    });\n    sortableList.addEventListener(\"dragover\", (e)=>{\n        e.preventDefault();\n    });\n    sortableList.addEventListener(\"drop\", (e)=>{\n        e.preventDefault();\n        const target = e.target;\n        if (target && target.classList.contains(\"sortable-item\") && draggedElement) {\n            const rect = target.getBoundingClientRect();\n            const midpoint = rect.top + rect.height / 2;\n            if (e.clientY < midpoint) {\n                sortableList.insertBefore(draggedElement, target);\n            } else {\n                sortableList.insertBefore(draggedElement, target.nextSibling);\n            }\n        }\n    });\n    window.checkOrder = ()=>{\n        const items = Array.from(document.querySelectorAll(\".sortable-item\"));\n        const feedback = document.getElementById(\"orderer-feedback\");\n        let isCorrect = true;\n        items.forEach((item, index)=>{\n            const expectedOrder = parseInt(item.dataset.order || \"0\");\n            if (expectedOrder !== index + 1) {\n                isCorrect = false;\n            }\n        });\n        if (feedback) {\n            if (isCorrect) {\n                feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Perfect chronological order!</span>';\n            } else {\n                feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Not quite right. Check the dates!</span>';\n            }\n        }\n    };\n}\n// Radio (Multiple Choice) Widget\nfunction renderRadio(container) {\n    container.innerHTML = '\\n    <div class=\"radio-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Multiple Choice Question</h3>\\n      <p style=\"color: #333; margin-bottom: 15px; font-size: 16px;\">What is the capital of France?</p>\\n      \\n      <div style=\"margin-bottom: 20px;\">\\n        <label style=\"display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 5px; background: #f8f9fa;\">\\n          <input type=\"radio\" name=\"capital\" value=\"london\" style=\"margin-right: 10px;\"> London\\n        </label>\\n        <label style=\"display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 5px; background: #f8f9fa;\">\\n          <input type=\"radio\" name=\"capital\" value=\"paris\" style=\"margin-right: 10px;\"> Paris\\n        </label>\\n        <label style=\"display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 5px; background: #f8f9fa;\">\\n          <input type=\"radio\" name=\"capital\" value=\"berlin\" style=\"margin-right: 10px;\"> Berlin\\n        </label>\\n        <label style=\"display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 5px; background: #f8f9fa;\">\\n          <input type=\"radio\" name=\"capital\" value=\"madrid\" style=\"margin-right: 10px;\"> Madrid\\n        </label>\\n      </div>\\n      \\n      <button \\n        onclick=\"checkRadio()\"\\n        style=\"\\n          background: #1c4f82; \\n          color: white; \\n          padding: 10px 20px; \\n          border: none; \\n          border-radius: 5px;\\n          cursor: pointer;\\n          font-size: 16px;\\n        \"\\n      >Submit Answer</button>\\n      <div id=\"radio-feedback\" style=\"margin-top: 15px; font-weight: bold;\"></div>\\n    </div>\\n  ';\n    window.checkRadio = ()=>{\n        const selected = document.querySelector('input[name=\"capital\"]:checked');\n        const feedback = document.getElementById(\"radio-feedback\");\n        if (!selected) {\n            if (feedback) feedback.innerHTML = '<span style=\"color: #ffc107;\">Please select an answer.</span>';\n            return;\n        }\n        if (feedback) {\n            if (selected.value === \"paris\") {\n                feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Correct! Paris is the capital of France.</span>';\n            } else {\n                feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Incorrect. The capital of France is Paris.</span>';\n            }\n        }\n    };\n}\n// Expression Widget\nfunction renderExpression(container) {\n    container.innerHTML = '\\n    <div class=\"expression-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Solve the Expression</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">Simplify: 2x + 3x - 5</p>\\n      \\n      <input \\n        type=\"text\" \\n        placeholder=\"Enter simplified expression\"\\n        style=\"\\n          padding: 10px; \\n          border: 2px solid #ddd; \\n          border-radius: 5px; \\n          font-size: 16px;\\n          width: 250px;\\n          margin-right: 10px;\\n        \"\\n        id=\"expression-answer\"\\n      />\\n      <button \\n        onclick=\"checkExpression()\"\\n        style=\"\\n          background: #1c4f82; \\n          color: white; \\n          padding: 10px 20px; \\n          border: none; \\n          border-radius: 5px;\\n          cursor: pointer;\\n          font-size: 16px;\\n        \"\\n      >Check Answer</button>\\n      \\n      <div style=\"margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;\">\\n        <p style=\"margin: 0; color: #666; font-size: 14px;\">\\n          <strong>Hint:</strong> Combine like terms (terms with the same variable)\\n        </p>\\n      </div>\\n      \\n      <div id=\"expression-feedback\" style=\"margin-top: 15px; font-weight: bold;\"></div>\\n    </div>\\n  ';\n    window.checkExpression = ()=>{\n        const input = document.getElementById(\"expression-answer\");\n        const feedback = document.getElementById(\"expression-feedback\");\n        if (input && feedback) {\n            const answer = input.value.toLowerCase().replace(/\\s/g, \"\");\n            const correctAnswers = [\n                \"5x-5\",\n                \"5x+-5\",\n                \"-5+5x\"\n            ];\n            if (correctAnswers.includes(answer)) {\n                feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Excellent! 2x + 3x - 5 = 5x - 5</span>';\n            } else {\n                feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Not quite. Remember to combine like terms: 2x + 3x = 5x</span>';\n            }\n        }\n    };\n}\n// Placeholder for unsupported widgets\nfunction renderPlaceholder(container, widgetType) {\n    container.innerHTML = '\\n    <div style=\"text-align: center; padding: 40px;\">\\n      <div style=\"font-size: 48px; margin-bottom: 20px;\">\\uD83C\\uDFAE</div>\\n      <h3 style=\"color: #1c4f82; margin-bottom: 15px;\">Perseus Widget: '.concat(widgetType, '</h3>\\n      <p style=\"color: #666; margin-bottom: 20px;\">This educational widget is being loaded...</p>\\n      <div style=\"background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #1c4f82;\">\\n        <p style=\"margin: 0; color: #333; font-size: 14px;\">\\n          Real Khan Academy Perseus widget integration in progress.\\n          This will render the actual interactive educational content.\\n        </p>\\n      </div>\\n    </div>\\n  ');\n}\nvar _c;\n$RefreshReg$(_c, \"PerseusRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9QZXJzZXVzUmVuZGVyZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBaUQ7QUFPMUMsTUFBTUcsa0JBQWtEO1FBQUMsRUFBRUMsVUFBVSxFQUFFQyxVQUFVLEVBQUU7O0lBQ3hGLE1BQU1DLGVBQWVKLDZDQUFNQSxDQUFpQjtJQUU1Q0QsZ0RBQVNBLENBQUM7UUFDUixJQUFJLENBQUNLLGFBQWFDLE9BQU8sRUFBRTtRQUUzQix5QkFBeUI7UUFDekJELGFBQWFDLE9BQU8sQ0FBQ0MsU0FBUyxHQUFHO1FBRWpDLDhCQUE4QjtRQUM5QixPQUFRSjtZQUNOLEtBQUs7Z0JBQ0hLLG1CQUFtQkgsYUFBYUMsT0FBTztnQkFDdkM7WUFDRixLQUFLO2dCQUNIRyxrQkFBa0JKLGFBQWFDLE9BQU87Z0JBQ3RDO1lBQ0YsS0FBSztnQkFDSEksY0FBY0wsYUFBYUMsT0FBTztnQkFDbEM7WUFDRixLQUFLO2dCQUNISyxjQUFjTixhQUFhQyxPQUFPO2dCQUNsQztZQUNGLEtBQUs7Z0JBQ0hNLFlBQVlQLGFBQWFDLE9BQU87Z0JBQ2hDO1lBQ0YsS0FBSztnQkFDSE8saUJBQWlCUixhQUFhQyxPQUFPO2dCQUNyQztZQUNGLEtBQUs7Z0JBQ0hRLGNBQWNULGFBQWFDLE9BQU87Z0JBQ2xDO1lBQ0YsS0FBSztnQkFDSFMsYUFBYVYsYUFBYUMsT0FBTztnQkFDakM7WUFDRixLQUFLO2dCQUNIVSxlQUFlWCxhQUFhQyxPQUFPO2dCQUNuQztZQUNGLEtBQUs7Z0JBQ0hXLHFCQUFxQlosYUFBYUMsT0FBTztnQkFDekM7WUFDRixLQUFLO2dCQUNIWSx1QkFBdUJiLGFBQWFDLE9BQU87Z0JBQzNDO1lBQ0YsS0FBSztnQkFDSGEsY0FBY2QsYUFBYUMsT0FBTztnQkFDbEM7WUFDRixLQUFLO2dCQUNIYyxhQUFhZixhQUFhQyxPQUFPO2dCQUNqQztZQUNGLEtBQUs7Z0JBQ0hlLGdCQUFnQmhCLGFBQWFDLE9BQU87Z0JBQ3BDO1lBQ0YsS0FBSztnQkFDSGdCLG9CQUFvQmpCLGFBQWFDLE9BQU87Z0JBQ3hDO1lBQ0Y7Z0JBQ0VpQixrQkFBa0JsQixhQUFhQyxPQUFPLEVBQUVIO1FBQzVDO0lBQ0YsR0FBRztRQUFDQTtRQUFZQztLQUFXO0lBRTNCLHFCQUNFLDhEQUFDb0I7UUFDQ0MsS0FBS3BCO1FBQ0xxQixXQUFVO1FBQ1ZDLE9BQU87WUFBRUMsWUFBWTtRQUFvQjs7Ozs7O0FBRy9DLEVBQUU7R0FwRVcxQjtLQUFBQTtBQXNFYix1QkFBdUI7QUFDdkIsU0FBU00sbUJBQW1CcUIsU0FBc0I7SUFDaERBLFVBQVV0QixTQUFTLEdBQUk7SUFpQ3ZCLG9CQUFvQjtJQUNuQnVCLE9BQWVDLGtCQUFrQixHQUFHO1FBQ25DLE1BQU1DLFFBQVFDLFNBQVNDLGNBQWMsQ0FBQztRQUN0QyxNQUFNQyxXQUFXRixTQUFTQyxjQUFjLENBQUM7UUFDekMsSUFBSUYsU0FBU0csVUFBVTtZQUNyQixNQUFNQyxTQUFTQyxTQUFTTCxNQUFNTSxLQUFLO1lBQ25DLElBQUlGLFdBQVcsSUFBSTtnQkFDakJELFNBQVM1QixTQUFTLEdBQUc7WUFDdkIsT0FBTztnQkFDTDRCLFNBQVM1QixTQUFTLEdBQUc7WUFDdkI7UUFDRjtJQUNGO0FBQ0Y7QUFFQSxxQkFBcUI7QUFDckIsU0FBU0Usa0JBQWtCb0IsU0FBc0I7SUFDL0NBLFVBQVV0QixTQUFTLEdBQUk7SUErRXZCLGtDQUFrQztJQUNsQ2dDO0FBQ0Y7QUFFQSxTQUFTQTtJQUNQLE1BQU1DLGFBQWFQLFNBQVNRLGdCQUFnQixDQUFDO0lBQzdDLE1BQU1DLFlBQVlULFNBQVNRLGdCQUFnQixDQUFDO0lBRTVDRCxXQUFXRyxPQUFPLENBQUNDLENBQUFBO1FBQ2pCQSxVQUFVQyxnQkFBZ0IsQ0FBQyxhQUFhLENBQUNDO2dCQUN2QyxpQkFDQTthQURBLG9CQUFpQkMsWUFBWSxjQUE3QixzREFBK0JDLE9BQU8sQ0FBQyxjQUFjLFVBQTJCQyxTQUFTO2FBQ3pGLHFCQUFpQkYsWUFBWSxjQUE3Qix3REFBK0JDLE9BQU8sQ0FBQyxZQUFZLFVBQTJCRSxPQUFPLENBQUNDLFFBQVEsSUFBSTtRQUNwRztJQUNGO0lBRUFULFVBQVVDLE9BQU8sQ0FBQ1MsQ0FBQUE7UUFDaEJBLEtBQUtQLGdCQUFnQixDQUFDLFlBQVksQ0FBQ0M7WUFDakNBLEVBQUVPLGNBQWM7UUFDbEI7UUFFQUQsS0FBS1AsZ0JBQWdCLENBQUMsUUFBUSxDQUFDQztnQkFFaEIsaUJBQ0k7WUFGakJBLEVBQUVPLGNBQWM7WUFDaEIsTUFBTUMsUUFBTyxvQkFBaUJQLFlBQVksY0FBN0Isc0RBQStCUSxPQUFPLENBQUM7WUFDcEQsTUFBTUosWUFBVyxxQkFBaUJKLFlBQVksY0FBN0Isd0RBQStCUSxPQUFPLENBQUM7WUFFeEQsSUFBSUQsUUFBUUYsS0FBS0ksRUFBRSxDQUFDQyxRQUFRLENBQUNOLFlBQVksS0FBSztnQkFDNUNDLEtBQUs3QyxTQUFTLElBQUkrQztZQUNwQjtRQUNGO0lBQ0Y7SUFFQ3hCLE9BQWU0QixnQkFBZ0IsR0FBRztRQUNqQyxNQUFNQyxjQUFjMUIsU0FBU0MsY0FBYyxDQUFDO1FBQzVDLE1BQU0wQixZQUFZM0IsU0FBU0MsY0FBYyxDQUFDO1FBQzFDLE1BQU1DLFdBQVdGLFNBQVNDLGNBQWMsQ0FBQztRQUV6QyxJQUFJeUIsZUFBZUMsYUFBYXpCLFVBQVU7WUFDeEMsTUFBTTBCLGVBQWVGLFlBQVlHLFFBQVEsQ0FBQ0MsTUFBTTtZQUNoRCxNQUFNQyxhQUFhSixVQUFVRSxRQUFRLENBQUNDLE1BQU07WUFFNUMsSUFBSUYsaUJBQWlCLEtBQUtHLGVBQWUsR0FBRztnQkFDMUM3QixTQUFTNUIsU0FBUyxHQUFHO1lBQ3ZCLE9BQU87Z0JBQ0w0QixTQUFTNUIsU0FBUyxHQUFHO1lBQ3ZCO1FBQ0Y7SUFDRjtBQUNGO0FBRUEsaUJBQWlCO0FBQ2pCLFNBQVNHLGNBQWNtQixTQUFzQjtJQUMzQ0EsVUFBVXRCLFNBQVMsR0FBSTtJQW1FdkIsSUFBSTBELGdCQUErQixFQUFFO0lBRXBDbkMsT0FBZW9DLFdBQVcsR0FBRyxDQUFDQztRQUM3QixJQUFJRixjQUFjRixNQUFNLEtBQUssR0FBRztZQUM5QkksUUFBUXhDLEtBQUssQ0FBQ3lDLE1BQU0sR0FBRztZQUN2QkgsY0FBY0ksSUFBSSxDQUFDRjtRQUNyQixPQUFPLElBQUlGLGNBQWNGLE1BQU0sS0FBSyxHQUFHO1lBQ3JDLE1BQU1PLFFBQVFMLGFBQWEsQ0FBQyxFQUFFO1lBQzlCLE1BQU05QixXQUFXRixTQUFTQyxjQUFjLENBQUM7WUFFekMsSUFBSW9DLE1BQU1wQixPQUFPLENBQUNxQixLQUFLLEtBQUtKLFFBQVFqQixPQUFPLENBQUNxQixLQUFLLEVBQUU7Z0JBQ2pERCxNQUFNM0MsS0FBSyxDQUFDNkMsVUFBVSxHQUFHO2dCQUN6QkwsUUFBUXhDLEtBQUssQ0FBQzZDLFVBQVUsR0FBRztnQkFDM0JGLE1BQU0zQyxLQUFLLENBQUN5QyxNQUFNLEdBQUc7Z0JBQ3JCRCxRQUFReEMsS0FBSyxDQUFDeUMsTUFBTSxHQUFHO2dCQUN2QixJQUFJakMsVUFBVUEsU0FBUzVCLFNBQVMsR0FBRztZQUNyQyxPQUFPO2dCQUNMK0QsTUFBTTNDLEtBQUssQ0FBQ3lDLE1BQU0sR0FBRztnQkFDckJELFFBQVF4QyxLQUFLLENBQUN5QyxNQUFNLEdBQUc7Z0JBQ3ZCLElBQUlqQyxVQUFVQSxTQUFTNUIsU0FBUyxHQUFHO2dCQUNuQ2tFLFdBQVc7b0JBQ1RILE1BQU0zQyxLQUFLLENBQUN5QyxNQUFNLEdBQUc7b0JBQ3JCRCxRQUFReEMsS0FBSyxDQUFDeUMsTUFBTSxHQUFHO2dCQUN6QixHQUFHO1lBQ0w7WUFDQUgsZ0JBQWdCLEVBQUU7UUFDcEI7SUFDRjtBQUNGO0FBRUEsaUJBQWlCO0FBQ2pCLFNBQVN0RCxjQUFja0IsU0FBc0I7SUFDM0NBLFVBQVV0QixTQUFTLEdBQUk7SUF5RHZCbUU7QUFDRjtBQUVBLFNBQVNBO0lBQ1AsTUFBTUMsZUFBZTFDLFNBQVNDLGNBQWMsQ0FBQztJQUM3QyxJQUFJLENBQUN5QyxjQUFjO0lBRW5CLElBQUlDLGlCQUFxQztJQUV6Q0QsYUFBYTlCLGdCQUFnQixDQUFDLGFBQWEsQ0FBQ0M7UUFDMUM4QixpQkFBaUI5QixFQUFFK0IsTUFBTTtRQUN6QixJQUFJRCxnQkFBZ0I7WUFDbEJBLGVBQWVqRCxLQUFLLENBQUNtRCxPQUFPLEdBQUc7UUFDakM7SUFDRjtJQUVBSCxhQUFhOUIsZ0JBQWdCLENBQUMsV0FBVyxDQUFDQztRQUN4QyxJQUFJOEIsZ0JBQWdCO1lBQ2xCQSxlQUFlakQsS0FBSyxDQUFDbUQsT0FBTyxHQUFHO1lBQy9CRixpQkFBaUI7UUFDbkI7SUFDRjtJQUVBRCxhQUFhOUIsZ0JBQWdCLENBQUMsWUFBWSxDQUFDQztRQUN6Q0EsRUFBRU8sY0FBYztJQUNsQjtJQUVBc0IsYUFBYTlCLGdCQUFnQixDQUFDLFFBQVEsQ0FBQ0M7UUFDckNBLEVBQUVPLGNBQWM7UUFDaEIsTUFBTXdCLFNBQVMvQixFQUFFK0IsTUFBTTtRQUN2QixJQUFJQSxVQUFVQSxPQUFPRSxTQUFTLENBQUNDLFFBQVEsQ0FBQyxvQkFBb0JKLGdCQUFnQjtZQUMxRSxNQUFNSyxPQUFPSixPQUFPSyxxQkFBcUI7WUFDekMsTUFBTUMsV0FBV0YsS0FBS0csR0FBRyxHQUFHSCxLQUFLSSxNQUFNLEdBQUc7WUFFMUMsSUFBSXZDLEVBQUV3QyxPQUFPLEdBQUdILFVBQVU7Z0JBQ3hCUixhQUFhWSxZQUFZLENBQUNYLGdCQUFnQkM7WUFDNUMsT0FBTztnQkFDTEYsYUFBYVksWUFBWSxDQUFDWCxnQkFBZ0JDLE9BQU9XLFdBQVc7WUFDOUQ7UUFDRjtJQUNGO0lBRUMxRCxPQUFlMkQsVUFBVSxHQUFHO1FBQzNCLE1BQU1DLFFBQVFDLE1BQU1DLElBQUksQ0FBQzNELFNBQVNRLGdCQUFnQixDQUFDO1FBQ25ELE1BQU1OLFdBQVdGLFNBQVNDLGNBQWMsQ0FBQztRQUV6QyxJQUFJMkQsWUFBWTtRQUNoQkgsTUFBTS9DLE9BQU8sQ0FBQyxDQUFDbUQsTUFBTUM7WUFDbkIsTUFBTUMsZ0JBQWdCM0QsU0FBUyxLQUFzQmEsT0FBTyxDQUFDK0MsS0FBSyxJQUFJO1lBQ3RFLElBQUlELGtCQUFrQkQsUUFBUSxHQUFHO2dCQUMvQkYsWUFBWTtZQUNkO1FBQ0Y7UUFFQSxJQUFJMUQsVUFBVTtZQUNaLElBQUkwRCxXQUFXO2dCQUNiMUQsU0FBUzVCLFNBQVMsR0FBRztZQUN2QixPQUFPO2dCQUNMNEIsU0FBUzVCLFNBQVMsR0FBRztZQUN2QjtRQUNGO0lBQ0Y7QUFDRjtBQUVBLGlDQUFpQztBQUNqQyxTQUFTSyxZQUFZaUIsU0FBc0I7SUFDekNBLFVBQVV0QixTQUFTLEdBQUk7SUFvQ3RCdUIsT0FBZW9FLFVBQVUsR0FBRztRQUMzQixNQUFNQyxXQUFXbEUsU0FBU21FLGFBQWEsQ0FBQztRQUN4QyxNQUFNakUsV0FBV0YsU0FBU0MsY0FBYyxDQUFDO1FBRXpDLElBQUksQ0FBQ2lFLFVBQVU7WUFDYixJQUFJaEUsVUFBVUEsU0FBUzVCLFNBQVMsR0FBRztZQUNuQztRQUNGO1FBRUEsSUFBSTRCLFVBQVU7WUFDWixJQUFJZ0UsU0FBUzdELEtBQUssS0FBSyxTQUFTO2dCQUM5QkgsU0FBUzVCLFNBQVMsR0FBRztZQUN2QixPQUFPO2dCQUNMNEIsU0FBUzVCLFNBQVMsR0FBRztZQUN2QjtRQUNGO0lBQ0Y7QUFDRjtBQUVBLG9CQUFvQjtBQUNwQixTQUFTTSxpQkFBaUJnQixTQUFzQjtJQUM5Q0EsVUFBVXRCLFNBQVMsR0FBSTtJQXlDdEJ1QixPQUFldUUsZUFBZSxHQUFHO1FBQ2hDLE1BQU1yRSxRQUFRQyxTQUFTQyxjQUFjLENBQUM7UUFDdEMsTUFBTUMsV0FBV0YsU0FBU0MsY0FBYyxDQUFDO1FBRXpDLElBQUlGLFNBQVNHLFVBQVU7WUFDckIsTUFBTUMsU0FBU0osTUFBTU0sS0FBSyxDQUFDZ0UsV0FBVyxHQUFHQyxPQUFPLENBQUMsT0FBTztZQUN4RCxNQUFNQyxpQkFBaUI7Z0JBQUM7Z0JBQVE7Z0JBQVM7YUFBUTtZQUVqRCxJQUFJQSxlQUFlL0MsUUFBUSxDQUFDckIsU0FBUztnQkFDbkNELFNBQVM1QixTQUFTLEdBQUc7WUFDdkIsT0FBTztnQkFDTDRCLFNBQVM1QixTQUFTLEdBQUc7WUFDdkI7UUFDRjtJQUNGO0FBQ0Y7QUFFQSxzQ0FBc0M7QUFDdEMsU0FBU2dCLGtCQUFrQk0sU0FBc0IsRUFBRTFCLFVBQWtCO0lBQ25FMEIsVUFBVXRCLFNBQVMsR0FBRywrTUFHNEQsT0FBWEosWUFBVztBQVVwRiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9QZXJzZXVzUmVuZGVyZXIudHN4P2YyOGUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuXG5pbnRlcmZhY2UgUGVyc2V1c1JlbmRlcmVyUHJvcHMge1xuICB3aWRnZXRUeXBlOiBzdHJpbmc7XG4gIHdpZGdldERhdGE/OiBhbnk7XG59XG5cbmV4cG9ydCBjb25zdCBQZXJzZXVzUmVuZGVyZXI6IFJlYWN0LkZDPFBlcnNldXNSZW5kZXJlclByb3BzPiA9ICh7IHdpZGdldFR5cGUsIHdpZGdldERhdGEgfSkgPT4ge1xuICBjb25zdCBjb250YWluZXJSZWYgPSB1c2VSZWY8SFRNTERpdkVsZW1lbnQ+KG51bGwpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFjb250YWluZXJSZWYuY3VycmVudCkgcmV0dXJuO1xuXG4gICAgLy8gQ2xlYXIgcHJldmlvdXMgY29udGVudFxuICAgIGNvbnRhaW5lclJlZi5jdXJyZW50LmlubmVySFRNTCA9ICcnO1xuXG4gICAgLy8gUmVuZGVyIGJhc2VkIG9uIHdpZGdldCB0eXBlXG4gICAgc3dpdGNoICh3aWRnZXRUeXBlKSB7XG4gICAgICBjYXNlICdudW1lcmljLWlucHV0JzpcbiAgICAgICAgcmVuZGVyTnVtZXJpY0lucHV0KGNvbnRhaW5lclJlZi5jdXJyZW50KTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlICdjYXRlZ29yaXplcic6XG4gICAgICAgIHJlbmRlckNhdGVnb3JpemVyKGNvbnRhaW5lclJlZi5jdXJyZW50KTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlICdtYXRjaGVyJzpcbiAgICAgICAgcmVuZGVyTWF0Y2hlcihjb250YWluZXJSZWYuY3VycmVudCk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAnb3JkZXJlcic6XG4gICAgICAgIHJlbmRlck9yZGVyZXIoY29udGFpbmVyUmVmLmN1cnJlbnQpO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgJ3JhZGlvJzpcbiAgICAgICAgcmVuZGVyUmFkaW8oY29udGFpbmVyUmVmLmN1cnJlbnQpO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgJ2V4cHJlc3Npb24nOlxuICAgICAgICByZW5kZXJFeHByZXNzaW9uKGNvbnRhaW5lclJlZi5jdXJyZW50KTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlICdncmFwaGVyJzpcbiAgICAgICAgcmVuZGVyR3JhcGhlcihjb250YWluZXJSZWYuY3VycmVudCk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAnbWF0cml4JzpcbiAgICAgICAgcmVuZGVyTWF0cml4KGNvbnRhaW5lclJlZi5jdXJyZW50KTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlICdtb2xlY3VsZSc6XG4gICAgICAgIHJlbmRlck1vbGVjdWxlKGNvbnRhaW5lclJlZi5jdXJyZW50KTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlICdwaGV0LXNpbXVsYXRpb24nOlxuICAgICAgICByZW5kZXJQaGV0U2ltdWxhdGlvbihjb250YWluZXJSZWYuY3VycmVudCk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAnaW50ZXJhY3RpdmUtZ3JhcGgnOlxuICAgICAgICByZW5kZXJJbnRlcmFjdGl2ZUdyYXBoKGNvbnRhaW5lclJlZi5jdXJyZW50KTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlICdwYXNzYWdlJzpcbiAgICAgICAgcmVuZGVyUGFzc2FnZShjb250YWluZXJSZWYuY3VycmVudCk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAnc29ydGVyJzpcbiAgICAgICAgcmVuZGVyU29ydGVyKGNvbnRhaW5lclJlZi5jdXJyZW50KTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlICdjcy1wcm9ncmFtJzpcbiAgICAgICAgcmVuZGVyQ1NQcm9ncmFtKGNvbnRhaW5lclJlZi5jdXJyZW50KTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlICdweXRob24tcHJvZ3JhbSc6XG4gICAgICAgIHJlbmRlclB5dGhvblByb2dyYW0oY29udGFpbmVyUmVmLmN1cnJlbnQpO1xuICAgICAgICBicmVhaztcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJlbmRlclBsYWNlaG9sZGVyKGNvbnRhaW5lclJlZi5jdXJyZW50LCB3aWRnZXRUeXBlKTtcbiAgICB9XG4gIH0sIFt3aWRnZXRUeXBlLCB3aWRnZXREYXRhXSk7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IFxuICAgICAgcmVmPXtjb250YWluZXJSZWZ9IFxuICAgICAgY2xhc3NOYW1lPVwicGVyc2V1cy13aWRnZXQgYmctd2hpdGUgcm91bmRlZC1sZyBwLTYgbWluLWgtWzQwMHB4XVwiXG4gICAgICBzdHlsZT17eyBmb250RmFtaWx5OiAnQXJpYWwsIHNhbnMtc2VyaWYnIH19XG4gICAgLz5cbiAgKTtcbn07XG5cbi8vIE51bWVyaWMgSW5wdXQgV2lkZ2V0XG5mdW5jdGlvbiByZW5kZXJOdW1lcmljSW5wdXQoY29udGFpbmVyOiBIVE1MRWxlbWVudCkge1xuICBjb250YWluZXIuaW5uZXJIVE1MID0gYFxuICAgIDxkaXYgY2xhc3M9XCJudW1lcmljLWlucHV0LXdpZGdldFwiPlxuICAgICAgPGgzIHN0eWxlPVwiY29sb3I6ICMxYzRmODI7IG1hcmdpbi1ib3R0b206IDIwcHg7IGZvbnQtc2l6ZTogMThweDtcIj5NYXRoIFByb2JsZW08L2gzPlxuICAgICAgPHAgc3R5bGU9XCJjb2xvcjogIzMzMzsgbWFyZ2luLWJvdHRvbTogMTVweDtcIj5XaGF0IGlzIDE1ICsgMjc/PC9wPlxuICAgICAgPGlucHV0IFxuICAgICAgICB0eXBlPVwibnVtYmVyXCIgXG4gICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgeW91ciBhbnN3ZXJcIlxuICAgICAgICBzdHlsZT1cIlxuICAgICAgICAgIHBhZGRpbmc6IDEwcHg7IFxuICAgICAgICAgIGJvcmRlcjogMnB4IHNvbGlkICNkZGQ7IFxuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDVweDsgXG4gICAgICAgICAgZm9udC1zaXplOiAxNnB4O1xuICAgICAgICAgIHdpZHRoOiAyMDBweDtcbiAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDEwcHg7XG4gICAgICAgIFwiXG4gICAgICAgIGlkPVwibnVtZXJpYy1hbnN3ZXJcIlxuICAgICAgLz5cbiAgICAgIDxidXR0b24gXG4gICAgICAgIG9uY2xpY2s9XCJjaGVja051bWVyaWNBbnN3ZXIoKVwiXG4gICAgICAgIHN0eWxlPVwiXG4gICAgICAgICAgYmFja2dyb3VuZDogIzFjNGY4MjsgXG4gICAgICAgICAgY29sb3I6IHdoaXRlOyBcbiAgICAgICAgICBwYWRkaW5nOiAxMHB4IDIwcHg7IFxuICAgICAgICAgIGJvcmRlcjogbm9uZTsgXG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogNXB4O1xuICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICAgICAgICBmb250LXNpemU6IDE2cHg7XG4gICAgICAgIFwiXG4gICAgICA+Q2hlY2sgQW5zd2VyPC9idXR0b24+XG4gICAgICA8ZGl2IGlkPVwibnVtZXJpYy1mZWVkYmFja1wiIHN0eWxlPVwibWFyZ2luLXRvcDogMTVweDsgZm9udC13ZWlnaHQ6IGJvbGQ7XCI+PC9kaXY+XG4gICAgPC9kaXY+XG4gIGA7XG5cbiAgLy8gQWRkIGV2ZW50IGhhbmRsZXJcbiAgKHdpbmRvdyBhcyBhbnkpLmNoZWNrTnVtZXJpY0Fuc3dlciA9ICgpID0+IHtcbiAgICBjb25zdCBpbnB1dCA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdudW1lcmljLWFuc3dlcicpIGFzIEhUTUxJbnB1dEVsZW1lbnQ7XG4gICAgY29uc3QgZmVlZGJhY2sgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnbnVtZXJpYy1mZWVkYmFjaycpO1xuICAgIGlmIChpbnB1dCAmJiBmZWVkYmFjaykge1xuICAgICAgY29uc3QgYW5zd2VyID0gcGFyc2VJbnQoaW5wdXQudmFsdWUpO1xuICAgICAgaWYgKGFuc3dlciA9PT0gNDIpIHtcbiAgICAgICAgZmVlZGJhY2suaW5uZXJIVE1MID0gJzxzcGFuIHN0eWxlPVwiY29sb3I6ICMyOGE3NDU7XCI+4pyTIENvcnJlY3QhIEdyZWF0IGpvYiE8L3NwYW4+JztcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGZlZWRiYWNrLmlubmVySFRNTCA9ICc8c3BhbiBzdHlsZT1cImNvbG9yOiAjZGMzNTQ1O1wiPuKclyBUcnkgYWdhaW4uIFRoaW5rIGFib3V0IDE1ICsgMjcuPC9zcGFuPic7XG4gICAgICB9XG4gICAgfVxuICB9O1xufVxuXG4vLyBDYXRlZ29yaXplciBXaWRnZXRcbmZ1bmN0aW9uIHJlbmRlckNhdGVnb3JpemVyKGNvbnRhaW5lcjogSFRNTEVsZW1lbnQpIHtcbiAgY29udGFpbmVyLmlubmVySFRNTCA9IGBcbiAgICA8ZGl2IGNsYXNzPVwiY2F0ZWdvcml6ZXItd2lkZ2V0XCI+XG4gICAgICA8aDMgc3R5bGU9XCJjb2xvcjogIzFjNGY4MjsgbWFyZ2luLWJvdHRvbTogMjBweDsgZm9udC1zaXplOiAxOHB4O1wiPkNhdGVnb3JpemUgQW5pbWFsczwvaDM+XG4gICAgICA8cCBzdHlsZT1cImNvbG9yOiAjMzMzOyBtYXJnaW4tYm90dG9tOiAxNXB4O1wiPkRyYWcgZWFjaCBhbmltYWwgdG8gdGhlIGNvcnJlY3QgY2F0ZWdvcnk6PC9wPlxuICAgICAgXG4gICAgICA8ZGl2IHN0eWxlPVwiZGlzcGxheTogZmxleDsgZ2FwOiAyMHB4OyBtYXJnaW4tYm90dG9tOiAyMHB4O1wiPlxuICAgICAgICA8ZGl2IGNsYXNzPVwiY2F0ZWdvcnktYm94XCIgc3R5bGU9XCJcbiAgICAgICAgICBib3JkZXI6IDJweCBkYXNoZWQgIzFjNGY4MjsgXG4gICAgICAgICAgcGFkZGluZzogMTVweDsgXG4gICAgICAgICAgbWluLWhlaWdodDogMTAwcHg7IFxuICAgICAgICAgIHdpZHRoOiAxNTBweDtcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgICAgICAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcbiAgICAgICAgXCI+XG4gICAgICAgICAgPGg0IHN0eWxlPVwibWFyZ2luOiAwIDAgMTBweCAwOyBjb2xvcjogIzFjNGY4MjtcIj5NYW1tYWxzPC9oND5cbiAgICAgICAgICA8ZGl2IGlkPVwibWFtbWFscy1kcm9wXCIgY2xhc3M9XCJkcm9wLXpvbmVcIj48L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIFxuICAgICAgICA8ZGl2IGNsYXNzPVwiY2F0ZWdvcnktYm94XCIgc3R5bGU9XCJcbiAgICAgICAgICBib3JkZXI6IDJweCBkYXNoZWQgIzI4YTc0NTsgXG4gICAgICAgICAgcGFkZGluZzogMTVweDsgXG4gICAgICAgICAgbWluLWhlaWdodDogMTAwcHg7IFxuICAgICAgICAgIHdpZHRoOiAxNTBweDtcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgICAgICAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcbiAgICAgICAgXCI+XG4gICAgICAgICAgPGg0IHN0eWxlPVwibWFyZ2luOiAwIDAgMTBweCAwOyBjb2xvcjogIzI4YTc0NTtcIj5CaXJkczwvaDQ+XG4gICAgICAgICAgPGRpdiBpZD1cImJpcmRzLWRyb3BcIiBjbGFzcz1cImRyb3Atem9uZVwiPjwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICAgXG4gICAgICA8ZGl2IHN0eWxlPVwiZGlzcGxheTogZmxleDsgZ2FwOiAxMHB4OyBmbGV4LXdyYXA6IHdyYXA7XCI+XG4gICAgICAgIDxkaXYgY2xhc3M9XCJkcmFnZ2FibGUtaXRlbVwiIGRyYWdnYWJsZT1cInRydWVcIiBkYXRhLWNhdGVnb3J5PVwibWFtbWFsc1wiIHN0eWxlPVwiXG4gICAgICAgICAgYmFja2dyb3VuZDogI2U5ZWNlZjsgXG4gICAgICAgICAgcGFkZGluZzogOHB4IDEycHg7IFxuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDVweDsgXG4gICAgICAgICAgY3Vyc29yOiBtb3ZlO1xuICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNjZWQ0ZGE7XG4gICAgICAgIFwiPvCfkJUgRG9nPC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3M9XCJkcmFnZ2FibGUtaXRlbVwiIGRyYWdnYWJsZT1cInRydWVcIiBkYXRhLWNhdGVnb3J5PVwiYmlyZHNcIiBzdHlsZT1cIlxuICAgICAgICAgIGJhY2tncm91bmQ6ICNlOWVjZWY7IFxuICAgICAgICAgIHBhZGRpbmc6IDhweCAxMnB4OyBcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiA1cHg7IFxuICAgICAgICAgIGN1cnNvcjogbW92ZTtcbiAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjY2VkNGRhO1xuICAgICAgICBcIj7wn6aFIEVhZ2xlPC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3M9XCJkcmFnZ2FibGUtaXRlbVwiIGRyYWdnYWJsZT1cInRydWVcIiBkYXRhLWNhdGVnb3J5PVwibWFtbWFsc1wiIHN0eWxlPVwiXG4gICAgICAgICAgYmFja2dyb3VuZDogI2U5ZWNlZjsgXG4gICAgICAgICAgcGFkZGluZzogOHB4IDEycHg7IFxuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDVweDsgXG4gICAgICAgICAgY3Vyc29yOiBtb3ZlO1xuICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNjZWQ0ZGE7XG4gICAgICAgIFwiPvCfkLEgQ2F0PC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3M9XCJkcmFnZ2FibGUtaXRlbVwiIGRyYWdnYWJsZT1cInRydWVcIiBkYXRhLWNhdGVnb3J5PVwiYmlyZHNcIiBzdHlsZT1cIlxuICAgICAgICAgIGJhY2tncm91bmQ6ICNlOWVjZWY7IFxuICAgICAgICAgIHBhZGRpbmc6IDhweCAxMnB4OyBcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiA1cHg7IFxuICAgICAgICAgIGN1cnNvcjogbW92ZTtcbiAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjY2VkNGRhO1xuICAgICAgICBcIj7wn5CmIFNwYXJyb3c8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICAgXG4gICAgICA8YnV0dG9uIFxuICAgICAgICBvbmNsaWNrPVwiY2hlY2tDYXRlZ29yaXplcigpXCJcbiAgICAgICAgc3R5bGU9XCJcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAjMWM0ZjgyOyBcbiAgICAgICAgICBjb2xvcjogd2hpdGU7IFxuICAgICAgICAgIHBhZGRpbmc6IDEwcHggMjBweDsgXG4gICAgICAgICAgYm9yZGVyOiBub25lOyBcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiA1cHg7XG4gICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICAgICAgICBtYXJnaW4tdG9wOiAyMHB4O1xuICAgICAgICBcIlxuICAgICAgPkNoZWNrIENhdGVnb3JpZXM8L2J1dHRvbj5cbiAgICAgIDxkaXYgaWQ9XCJjYXRlZ29yaXplci1mZWVkYmFja1wiIHN0eWxlPVwibWFyZ2luLXRvcDogMTVweDsgZm9udC13ZWlnaHQ6IGJvbGQ7XCI+PC9kaXY+XG4gICAgPC9kaXY+XG4gIGA7XG5cbiAgLy8gQWRkIGRyYWcgYW5kIGRyb3AgZnVuY3Rpb25hbGl0eVxuICBzZXR1cERyYWdBbmREcm9wKCk7XG59XG5cbmZ1bmN0aW9uIHNldHVwRHJhZ0FuZERyb3AoKSB7XG4gIGNvbnN0IGRyYWdnYWJsZXMgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCcuZHJhZ2dhYmxlLWl0ZW0nKTtcbiAgY29uc3QgZHJvcFpvbmVzID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvckFsbCgnLmRyb3Atem9uZScpO1xuXG4gIGRyYWdnYWJsZXMuZm9yRWFjaChkcmFnZ2FibGUgPT4ge1xuICAgIGRyYWdnYWJsZS5hZGRFdmVudExpc3RlbmVyKCdkcmFnc3RhcnQnLCAoZSkgPT4ge1xuICAgICAgKGUgYXMgRHJhZ0V2ZW50KS5kYXRhVHJhbnNmZXI/LnNldERhdGEoJ3RleHQvcGxhaW4nLCAoZHJhZ2dhYmxlIGFzIEhUTUxFbGVtZW50KS5vdXRlckhUTUwpO1xuICAgICAgKGUgYXMgRHJhZ0V2ZW50KS5kYXRhVHJhbnNmZXI/LnNldERhdGEoJ2NhdGVnb3J5JywgKGRyYWdnYWJsZSBhcyBIVE1MRWxlbWVudCkuZGF0YXNldC5jYXRlZ29yeSB8fCAnJyk7XG4gICAgfSk7XG4gIH0pO1xuXG4gIGRyb3Bab25lcy5mb3JFYWNoKHpvbmUgPT4ge1xuICAgIHpvbmUuYWRkRXZlbnRMaXN0ZW5lcignZHJhZ292ZXInLCAoZSkgPT4ge1xuICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIH0pO1xuXG4gICAgem9uZS5hZGRFdmVudExpc3RlbmVyKCdkcm9wJywgKGUpID0+IHtcbiAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgIGNvbnN0IGh0bWwgPSAoZSBhcyBEcmFnRXZlbnQpLmRhdGFUcmFuc2Zlcj8uZ2V0RGF0YSgndGV4dC9wbGFpbicpO1xuICAgICAgY29uc3QgY2F0ZWdvcnkgPSAoZSBhcyBEcmFnRXZlbnQpLmRhdGFUcmFuc2Zlcj8uZ2V0RGF0YSgnY2F0ZWdvcnknKTtcbiAgICAgIFxuICAgICAgaWYgKGh0bWwgJiYgem9uZS5pZC5pbmNsdWRlcyhjYXRlZ29yeSB8fCAnJykpIHtcbiAgICAgICAgem9uZS5pbm5lckhUTUwgKz0gaHRtbDtcbiAgICAgIH1cbiAgICB9KTtcbiAgfSk7XG5cbiAgKHdpbmRvdyBhcyBhbnkpLmNoZWNrQ2F0ZWdvcml6ZXIgPSAoKSA9PiB7XG4gICAgY29uc3QgbWFtbWFsc1pvbmUgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnbWFtbWFscy1kcm9wJyk7XG4gICAgY29uc3QgYmlyZHNab25lID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ2JpcmRzLWRyb3AnKTtcbiAgICBjb25zdCBmZWVkYmFjayA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdjYXRlZ29yaXplci1mZWVkYmFjaycpO1xuICAgIFxuICAgIGlmIChtYW1tYWxzWm9uZSAmJiBiaXJkc1pvbmUgJiYgZmVlZGJhY2spIHtcbiAgICAgIGNvbnN0IG1hbW1hbHNDb3VudCA9IG1hbW1hbHNab25lLmNoaWxkcmVuLmxlbmd0aDtcbiAgICAgIGNvbnN0IGJpcmRzQ291bnQgPSBiaXJkc1pvbmUuY2hpbGRyZW4ubGVuZ3RoO1xuICAgICAgXG4gICAgICBpZiAobWFtbWFsc0NvdW50ID09PSAyICYmIGJpcmRzQ291bnQgPT09IDIpIHtcbiAgICAgICAgZmVlZGJhY2suaW5uZXJIVE1MID0gJzxzcGFuIHN0eWxlPVwiY29sb3I6ICMyOGE3NDU7XCI+4pyTIFBlcmZlY3QgY2F0ZWdvcml6YXRpb24hPC9zcGFuPic7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBmZWVkYmFjay5pbm5lckhUTUwgPSAnPHNwYW4gc3R5bGU9XCJjb2xvcjogI2RjMzU0NTtcIj7inJcgU29tZSBhbmltYWxzIGFyZSBpbiB3cm9uZyBjYXRlZ29yaWVzLiBUcnkgYWdhaW4hPC9zcGFuPic7XG4gICAgICB9XG4gICAgfVxuICB9O1xufVxuXG4vLyBNYXRjaGVyIFdpZGdldFxuZnVuY3Rpb24gcmVuZGVyTWF0Y2hlcihjb250YWluZXI6IEhUTUxFbGVtZW50KSB7XG4gIGNvbnRhaW5lci5pbm5lckhUTUwgPSBgXG4gICAgPGRpdiBjbGFzcz1cIm1hdGNoZXItd2lkZ2V0XCI+XG4gICAgICA8aDMgc3R5bGU9XCJjb2xvcjogIzFjNGY4MjsgbWFyZ2luLWJvdHRvbTogMjBweDsgZm9udC1zaXplOiAxOHB4O1wiPk1hdGNoIFdvcmRzIHdpdGggRGVmaW5pdGlvbnM8L2gzPlxuICAgICAgPHAgc3R5bGU9XCJjb2xvcjogIzMzMzsgbWFyZ2luLWJvdHRvbTogMTVweDtcIj5DbGljayBvbiBhIHdvcmQsIHRoZW4gY2xpY2sgb24gaXRzIG1hdGNoaW5nIGRlZmluaXRpb246PC9wPlxuICAgICAgXG4gICAgICA8ZGl2IHN0eWxlPVwiZGlzcGxheTogZmxleDsgZ2FwOiA0MHB4O1wiPlxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxoNCBzdHlsZT1cImNvbG9yOiAjMWM0ZjgyO1wiPldvcmRzPC9oND5cbiAgICAgICAgICA8ZGl2IGNsYXNzPVwibWF0Y2gtaXRlbVwiIGRhdGEtbWF0Y2g9XCJwaG90b3N5bnRoZXNpc1wiIG9uY2xpY2s9XCJzZWxlY3RNYXRjaCh0aGlzKVwiIHN0eWxlPVwiXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZTllY2VmOyBcbiAgICAgICAgICAgIHBhZGRpbmc6IDEwcHg7IFxuICAgICAgICAgICAgbWFyZ2luOiA1cHggMDsgXG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiA1cHg7IFxuICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgICAgICAgICAgYm9yZGVyOiAycHggc29saWQgdHJhbnNwYXJlbnQ7XG4gICAgICAgICAgXCI+UGhvdG9zeW50aGVzaXM8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzPVwibWF0Y2gtaXRlbVwiIGRhdGEtbWF0Y2g9XCJncmF2aXR5XCIgb25jbGljaz1cInNlbGVjdE1hdGNoKHRoaXMpXCIgc3R5bGU9XCJcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICNlOWVjZWY7IFxuICAgICAgICAgICAgcGFkZGluZzogMTBweDsgXG4gICAgICAgICAgICBtYXJnaW46IDVweCAwOyBcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDVweDsgXG4gICAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgICAgICAgICBib3JkZXI6IDJweCBzb2xpZCB0cmFuc3BhcmVudDtcbiAgICAgICAgICBcIj5HcmF2aXR5PC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzcz1cIm1hdGNoLWl0ZW1cIiBkYXRhLW1hdGNoPVwiZGVtb2NyYWN5XCIgb25jbGljaz1cInNlbGVjdE1hdGNoKHRoaXMpXCIgc3R5bGU9XCJcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICNlOWVjZWY7IFxuICAgICAgICAgICAgcGFkZGluZzogMTBweDsgXG4gICAgICAgICAgICBtYXJnaW46IDVweCAwOyBcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDVweDsgXG4gICAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgICAgICAgICBib3JkZXI6IDJweCBzb2xpZCB0cmFuc3BhcmVudDtcbiAgICAgICAgICBcIj5EZW1vY3JhY3k8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIFxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxoNCBzdHlsZT1cImNvbG9yOiAjMjhhNzQ1O1wiPkRlZmluaXRpb25zPC9oND5cbiAgICAgICAgICA8ZGl2IGNsYXNzPVwibWF0Y2gtaXRlbVwiIGRhdGEtbWF0Y2g9XCJncmF2aXR5XCIgb25jbGljaz1cInNlbGVjdE1hdGNoKHRoaXMpXCIgc3R5bGU9XCJcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICNlOWVjZWY7IFxuICAgICAgICAgICAgcGFkZGluZzogMTBweDsgXG4gICAgICAgICAgICBtYXJnaW46IDVweCAwOyBcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDVweDsgXG4gICAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgICAgICAgICBib3JkZXI6IDJweCBzb2xpZCB0cmFuc3BhcmVudDtcbiAgICAgICAgICBcIj5Gb3JjZSB0aGF0IHB1bGxzIG9iamVjdHMgdG93YXJkIEVhcnRoPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzcz1cIm1hdGNoLWl0ZW1cIiBkYXRhLW1hdGNoPVwicGhvdG9zeW50aGVzaXNcIiBvbmNsaWNrPVwic2VsZWN0TWF0Y2godGhpcylcIiBzdHlsZT1cIlxuICAgICAgICAgICAgYmFja2dyb3VuZDogI2U5ZWNlZjsgXG4gICAgICAgICAgICBwYWRkaW5nOiAxMHB4OyBcbiAgICAgICAgICAgIG1hcmdpbjogNXB4IDA7IFxuICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNXB4OyBcbiAgICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICAgICAgICAgIGJvcmRlcjogMnB4IHNvbGlkIHRyYW5zcGFyZW50O1xuICAgICAgICAgIFwiPlByb2Nlc3MgcGxhbnRzIHVzZSB0byBtYWtlIGZvb2QgZnJvbSBzdW5saWdodDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3M9XCJtYXRjaC1pdGVtXCIgZGF0YS1tYXRjaD1cImRlbW9jcmFjeVwiIG9uY2xpY2s9XCJzZWxlY3RNYXRjaCh0aGlzKVwiIHN0eWxlPVwiXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZTllY2VmOyBcbiAgICAgICAgICAgIHBhZGRpbmc6IDEwcHg7IFxuICAgICAgICAgICAgbWFyZ2luOiA1cHggMDsgXG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiA1cHg7IFxuICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgICAgICAgICAgYm9yZGVyOiAycHggc29saWQgdHJhbnNwYXJlbnQ7XG4gICAgICAgICAgXCI+R292ZXJubWVudCBieSB0aGUgcGVvcGxlPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgICBcbiAgICAgIDxkaXYgaWQ9XCJtYXRjaGVyLWZlZWRiYWNrXCIgc3R5bGU9XCJtYXJnaW4tdG9wOiAyMHB4OyBmb250LXdlaWdodDogYm9sZDtcIj48L2Rpdj5cbiAgICA8L2Rpdj5cbiAgYDtcblxuICBsZXQgc2VsZWN0ZWRJdGVtczogSFRNTEVsZW1lbnRbXSA9IFtdO1xuICBcbiAgKHdpbmRvdyBhcyBhbnkpLnNlbGVjdE1hdGNoID0gKGVsZW1lbnQ6IEhUTUxFbGVtZW50KSA9PiB7XG4gICAgaWYgKHNlbGVjdGVkSXRlbXMubGVuZ3RoID09PSAwKSB7XG4gICAgICBlbGVtZW50LnN0eWxlLmJvcmRlciA9ICcycHggc29saWQgIzFjNGY4Mic7XG4gICAgICBzZWxlY3RlZEl0ZW1zLnB1c2goZWxlbWVudCk7XG4gICAgfSBlbHNlIGlmIChzZWxlY3RlZEl0ZW1zLmxlbmd0aCA9PT0gMSkge1xuICAgICAgY29uc3QgZmlyc3QgPSBzZWxlY3RlZEl0ZW1zWzBdO1xuICAgICAgY29uc3QgZmVlZGJhY2sgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnbWF0Y2hlci1mZWVkYmFjaycpO1xuICAgICAgXG4gICAgICBpZiAoZmlyc3QuZGF0YXNldC5tYXRjaCA9PT0gZWxlbWVudC5kYXRhc2V0Lm1hdGNoKSB7XG4gICAgICAgIGZpcnN0LnN0eWxlLmJhY2tncm91bmQgPSAnI2Q0ZWRkYSc7XG4gICAgICAgIGVsZW1lbnQuc3R5bGUuYmFja2dyb3VuZCA9ICcjZDRlZGRhJztcbiAgICAgICAgZmlyc3Quc3R5bGUuYm9yZGVyID0gJzJweCBzb2xpZCAjMjhhNzQ1JztcbiAgICAgICAgZWxlbWVudC5zdHlsZS5ib3JkZXIgPSAnMnB4IHNvbGlkICMyOGE3NDUnO1xuICAgICAgICBpZiAoZmVlZGJhY2spIGZlZWRiYWNrLmlubmVySFRNTCA9ICc8c3BhbiBzdHlsZT1cImNvbG9yOiAjMjhhNzQ1O1wiPuKckyBDb3JyZWN0IG1hdGNoITwvc3Bhbj4nO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgZmlyc3Quc3R5bGUuYm9yZGVyID0gJzJweCBzb2xpZCAjZGMzNTQ1JztcbiAgICAgICAgZWxlbWVudC5zdHlsZS5ib3JkZXIgPSAnMnB4IHNvbGlkICNkYzM1NDUnO1xuICAgICAgICBpZiAoZmVlZGJhY2spIGZlZWRiYWNrLmlubmVySFRNTCA9ICc8c3BhbiBzdHlsZT1cImNvbG9yOiAjZGMzNTQ1O1wiPuKclyBUcnkgYWdhaW4hPC9zcGFuPic7XG4gICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgIGZpcnN0LnN0eWxlLmJvcmRlciA9ICcycHggc29saWQgdHJhbnNwYXJlbnQnO1xuICAgICAgICAgIGVsZW1lbnQuc3R5bGUuYm9yZGVyID0gJzJweCBzb2xpZCB0cmFuc3BhcmVudCc7XG4gICAgICAgIH0sIDEwMDApO1xuICAgICAgfVxuICAgICAgc2VsZWN0ZWRJdGVtcyA9IFtdO1xuICAgIH1cbiAgfTtcbn1cblxuLy8gT3JkZXJlciBXaWRnZXRcbmZ1bmN0aW9uIHJlbmRlck9yZGVyZXIoY29udGFpbmVyOiBIVE1MRWxlbWVudCkge1xuICBjb250YWluZXIuaW5uZXJIVE1MID0gYFxuICAgIDxkaXYgY2xhc3M9XCJvcmRlcmVyLXdpZGdldFwiPlxuICAgICAgPGgzIHN0eWxlPVwiY29sb3I6ICMxYzRmODI7IG1hcmdpbi1ib3R0b206IDIwcHg7IGZvbnQtc2l6ZTogMThweDtcIj5QdXQgaW4gQ2hyb25vbG9naWNhbCBPcmRlcjwvaDM+XG4gICAgICA8cCBzdHlsZT1cImNvbG9yOiAjMzMzOyBtYXJnaW4tYm90dG9tOiAxNXB4O1wiPkRyYWcgdG8gYXJyYW5nZSB0aGVzZSBoaXN0b3JpY2FsIGV2ZW50cyBpbiBvcmRlcjo8L3A+XG4gICAgICBcbiAgICAgIDxkaXYgaWQ9XCJzb3J0YWJsZS1saXN0XCIgc3R5bGU9XCJtaW4taGVpZ2h0OiAyMDBweDtcIj5cbiAgICAgICAgPGRpdiBjbGFzcz1cInNvcnRhYmxlLWl0ZW1cIiBkcmFnZ2FibGU9XCJ0cnVlXCIgZGF0YS1vcmRlcj1cIjNcIiBzdHlsZT1cIlxuICAgICAgICAgIGJhY2tncm91bmQ6ICNlOWVjZWY7IFxuICAgICAgICAgIHBhZGRpbmc6IDE1cHg7IFxuICAgICAgICAgIG1hcmdpbjogMTBweCAwOyBcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiA1cHg7IFxuICAgICAgICAgIGN1cnNvcjogbW92ZTtcbiAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjY2VkNGRhO1xuICAgICAgICBcIj5Xb3JsZCBXYXIgSUkgZW5kcyAoMTk0NSk8L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzcz1cInNvcnRhYmxlLWl0ZW1cIiBkcmFnZ2FibGU9XCJ0cnVlXCIgZGF0YS1vcmRlcj1cIjFcIiBzdHlsZT1cIlxuICAgICAgICAgIGJhY2tncm91bmQ6ICNlOWVjZWY7IFxuICAgICAgICAgIHBhZGRpbmc6IDE1cHg7IFxuICAgICAgICAgIG1hcmdpbjogMTBweCAwOyBcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiA1cHg7IFxuICAgICAgICAgIGN1cnNvcjogbW92ZTtcbiAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjY2VkNGRhO1xuICAgICAgICBcIj5BbWVyaWNhbiBDaXZpbCBXYXIgYmVnaW5zICgxODYxKTwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzPVwic29ydGFibGUtaXRlbVwiIGRyYWdnYWJsZT1cInRydWVcIiBkYXRhLW9yZGVyPVwiMlwiIHN0eWxlPVwiXG4gICAgICAgICAgYmFja2dyb3VuZDogI2U5ZWNlZjsgXG4gICAgICAgICAgcGFkZGluZzogMTVweDsgXG4gICAgICAgICAgbWFyZ2luOiAxMHB4IDA7IFxuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDVweDsgXG4gICAgICAgICAgY3Vyc29yOiBtb3ZlO1xuICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNjZWQ0ZGE7XG4gICAgICAgIFwiPldvcmxkIFdhciBJIGJlZ2lucyAoMTkxNCk8L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzcz1cInNvcnRhYmxlLWl0ZW1cIiBkcmFnZ2FibGU9XCJ0cnVlXCIgZGF0YS1vcmRlcj1cIjRcIiBzdHlsZT1cIlxuICAgICAgICAgIGJhY2tncm91bmQ6ICNlOWVjZWY7IFxuICAgICAgICAgIHBhZGRpbmc6IDE1cHg7IFxuICAgICAgICAgIG1hcmdpbjogMTBweCAwOyBcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiA1cHg7IFxuICAgICAgICAgIGN1cnNvcjogbW92ZTtcbiAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjY2VkNGRhO1xuICAgICAgICBcIj5Nb29uIGxhbmRpbmcgKDE5NjkpPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICAgIFxuICAgICAgPGJ1dHRvbiBcbiAgICAgICAgb25jbGljaz1cImNoZWNrT3JkZXIoKVwiXG4gICAgICAgIHN0eWxlPVwiXG4gICAgICAgICAgYmFja2dyb3VuZDogIzFjNGY4MjsgXG4gICAgICAgICAgY29sb3I6IHdoaXRlOyBcbiAgICAgICAgICBwYWRkaW5nOiAxMHB4IDIwcHg7IFxuICAgICAgICAgIGJvcmRlcjogbm9uZTsgXG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogNXB4O1xuICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICAgICAgICBmb250LXNpemU6IDE2cHg7XG4gICAgICAgICAgbWFyZ2luLXRvcDogMjBweDtcbiAgICAgICAgXCJcbiAgICAgID5DaGVjayBPcmRlcjwvYnV0dG9uPlxuICAgICAgPGRpdiBpZD1cIm9yZGVyZXItZmVlZGJhY2tcIiBzdHlsZT1cIm1hcmdpbi10b3A6IDE1cHg7IGZvbnQtd2VpZ2h0OiBib2xkO1wiPjwvZGl2PlxuICAgIDwvZGl2PlxuICBgO1xuXG4gIHNldHVwU29ydGFibGUoKTtcbn1cblxuZnVuY3Rpb24gc2V0dXBTb3J0YWJsZSgpIHtcbiAgY29uc3Qgc29ydGFibGVMaXN0ID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ3NvcnRhYmxlLWxpc3QnKTtcbiAgaWYgKCFzb3J0YWJsZUxpc3QpIHJldHVybjtcblxuICBsZXQgZHJhZ2dlZEVsZW1lbnQ6IEhUTUxFbGVtZW50IHwgbnVsbCA9IG51bGw7XG5cbiAgc29ydGFibGVMaXN0LmFkZEV2ZW50TGlzdGVuZXIoJ2RyYWdzdGFydCcsIChlKSA9PiB7XG4gICAgZHJhZ2dlZEVsZW1lbnQgPSBlLnRhcmdldCBhcyBIVE1MRWxlbWVudDtcbiAgICBpZiAoZHJhZ2dlZEVsZW1lbnQpIHtcbiAgICAgIGRyYWdnZWRFbGVtZW50LnN0eWxlLm9wYWNpdHkgPSAnMC41JztcbiAgICB9XG4gIH0pO1xuXG4gIHNvcnRhYmxlTGlzdC5hZGRFdmVudExpc3RlbmVyKCdkcmFnZW5kJywgKGUpID0+IHtcbiAgICBpZiAoZHJhZ2dlZEVsZW1lbnQpIHtcbiAgICAgIGRyYWdnZWRFbGVtZW50LnN0eWxlLm9wYWNpdHkgPSAnMSc7XG4gICAgICBkcmFnZ2VkRWxlbWVudCA9IG51bGw7XG4gICAgfVxuICB9KTtcblxuICBzb3J0YWJsZUxpc3QuYWRkRXZlbnRMaXN0ZW5lcignZHJhZ292ZXInLCAoZSkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgfSk7XG5cbiAgc29ydGFibGVMaXN0LmFkZEV2ZW50TGlzdGVuZXIoJ2Ryb3AnLCAoZSkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBjb25zdCB0YXJnZXQgPSBlLnRhcmdldCBhcyBIVE1MRWxlbWVudDtcbiAgICBpZiAodGFyZ2V0ICYmIHRhcmdldC5jbGFzc0xpc3QuY29udGFpbnMoJ3NvcnRhYmxlLWl0ZW0nKSAmJiBkcmFnZ2VkRWxlbWVudCkge1xuICAgICAgY29uc3QgcmVjdCA9IHRhcmdldC5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKTtcbiAgICAgIGNvbnN0IG1pZHBvaW50ID0gcmVjdC50b3AgKyByZWN0LmhlaWdodCAvIDI7XG4gICAgICBcbiAgICAgIGlmIChlLmNsaWVudFkgPCBtaWRwb2ludCkge1xuICAgICAgICBzb3J0YWJsZUxpc3QuaW5zZXJ0QmVmb3JlKGRyYWdnZWRFbGVtZW50LCB0YXJnZXQpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc29ydGFibGVMaXN0Lmluc2VydEJlZm9yZShkcmFnZ2VkRWxlbWVudCwgdGFyZ2V0Lm5leHRTaWJsaW5nKTtcbiAgICAgIH1cbiAgICB9XG4gIH0pO1xuXG4gICh3aW5kb3cgYXMgYW55KS5jaGVja09yZGVyID0gKCkgPT4ge1xuICAgIGNvbnN0IGl0ZW1zID0gQXJyYXkuZnJvbShkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCcuc29ydGFibGUtaXRlbScpKTtcbiAgICBjb25zdCBmZWVkYmFjayA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdvcmRlcmVyLWZlZWRiYWNrJyk7XG4gICAgXG4gICAgbGV0IGlzQ29ycmVjdCA9IHRydWU7XG4gICAgaXRlbXMuZm9yRWFjaCgoaXRlbSwgaW5kZXgpID0+IHtcbiAgICAgIGNvbnN0IGV4cGVjdGVkT3JkZXIgPSBwYXJzZUludCgoaXRlbSBhcyBIVE1MRWxlbWVudCkuZGF0YXNldC5vcmRlciB8fCAnMCcpO1xuICAgICAgaWYgKGV4cGVjdGVkT3JkZXIgIT09IGluZGV4ICsgMSkge1xuICAgICAgICBpc0NvcnJlY3QgPSBmYWxzZTtcbiAgICAgIH1cbiAgICB9KTtcbiAgICBcbiAgICBpZiAoZmVlZGJhY2spIHtcbiAgICAgIGlmIChpc0NvcnJlY3QpIHtcbiAgICAgICAgZmVlZGJhY2suaW5uZXJIVE1MID0gJzxzcGFuIHN0eWxlPVwiY29sb3I6ICMyOGE3NDU7XCI+4pyTIFBlcmZlY3QgY2hyb25vbG9naWNhbCBvcmRlciE8L3NwYW4+JztcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGZlZWRiYWNrLmlubmVySFRNTCA9ICc8c3BhbiBzdHlsZT1cImNvbG9yOiAjZGMzNTQ1O1wiPuKclyBOb3QgcXVpdGUgcmlnaHQuIENoZWNrIHRoZSBkYXRlcyE8L3NwYW4+JztcbiAgICAgIH1cbiAgICB9XG4gIH07XG59XG5cbi8vIFJhZGlvIChNdWx0aXBsZSBDaG9pY2UpIFdpZGdldFxuZnVuY3Rpb24gcmVuZGVyUmFkaW8oY29udGFpbmVyOiBIVE1MRWxlbWVudCkge1xuICBjb250YWluZXIuaW5uZXJIVE1MID0gYFxuICAgIDxkaXYgY2xhc3M9XCJyYWRpby13aWRnZXRcIj5cbiAgICAgIDxoMyBzdHlsZT1cImNvbG9yOiAjMWM0ZjgyOyBtYXJnaW4tYm90dG9tOiAyMHB4OyBmb250LXNpemU6IDE4cHg7XCI+TXVsdGlwbGUgQ2hvaWNlIFF1ZXN0aW9uPC9oMz5cbiAgICAgIDxwIHN0eWxlPVwiY29sb3I6ICMzMzM7IG1hcmdpbi1ib3R0b206IDE1cHg7IGZvbnQtc2l6ZTogMTZweDtcIj5XaGF0IGlzIHRoZSBjYXBpdGFsIG9mIEZyYW5jZT88L3A+XG4gICAgICBcbiAgICAgIDxkaXYgc3R5bGU9XCJtYXJnaW4tYm90dG9tOiAyMHB4O1wiPlxuICAgICAgICA8bGFiZWwgc3R5bGU9XCJkaXNwbGF5OiBibG9jazsgbWFyZ2luOiAxMHB4IDA7IGN1cnNvcjogcG9pbnRlcjsgcGFkZGluZzogMTBweDsgYm9yZGVyLXJhZGl1czogNXB4OyBiYWNrZ3JvdW5kOiAjZjhmOWZhO1wiPlxuICAgICAgICAgIDxpbnB1dCB0eXBlPVwicmFkaW9cIiBuYW1lPVwiY2FwaXRhbFwiIHZhbHVlPVwibG9uZG9uXCIgc3R5bGU9XCJtYXJnaW4tcmlnaHQ6IDEwcHg7XCI+IExvbmRvblxuICAgICAgICA8L2xhYmVsPlxuICAgICAgICA8bGFiZWwgc3R5bGU9XCJkaXNwbGF5OiBibG9jazsgbWFyZ2luOiAxMHB4IDA7IGN1cnNvcjogcG9pbnRlcjsgcGFkZGluZzogMTBweDsgYm9yZGVyLXJhZGl1czogNXB4OyBiYWNrZ3JvdW5kOiAjZjhmOWZhO1wiPlxuICAgICAgICAgIDxpbnB1dCB0eXBlPVwicmFkaW9cIiBuYW1lPVwiY2FwaXRhbFwiIHZhbHVlPVwicGFyaXNcIiBzdHlsZT1cIm1hcmdpbi1yaWdodDogMTBweDtcIj4gUGFyaXNcbiAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgPGxhYmVsIHN0eWxlPVwiZGlzcGxheTogYmxvY2s7IG1hcmdpbjogMTBweCAwOyBjdXJzb3I6IHBvaW50ZXI7IHBhZGRpbmc6IDEwcHg7IGJvcmRlci1yYWRpdXM6IDVweDsgYmFja2dyb3VuZDogI2Y4ZjlmYTtcIj5cbiAgICAgICAgICA8aW5wdXQgdHlwZT1cInJhZGlvXCIgbmFtZT1cImNhcGl0YWxcIiB2YWx1ZT1cImJlcmxpblwiIHN0eWxlPVwibWFyZ2luLXJpZ2h0OiAxMHB4O1wiPiBCZXJsaW5cbiAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgPGxhYmVsIHN0eWxlPVwiZGlzcGxheTogYmxvY2s7IG1hcmdpbjogMTBweCAwOyBjdXJzb3I6IHBvaW50ZXI7IHBhZGRpbmc6IDEwcHg7IGJvcmRlci1yYWRpdXM6IDVweDsgYmFja2dyb3VuZDogI2Y4ZjlmYTtcIj5cbiAgICAgICAgICA8aW5wdXQgdHlwZT1cInJhZGlvXCIgbmFtZT1cImNhcGl0YWxcIiB2YWx1ZT1cIm1hZHJpZFwiIHN0eWxlPVwibWFyZ2luLXJpZ2h0OiAxMHB4O1wiPiBNYWRyaWRcbiAgICAgICAgPC9sYWJlbD5cbiAgICAgIDwvZGl2PlxuICAgICAgXG4gICAgICA8YnV0dG9uIFxuICAgICAgICBvbmNsaWNrPVwiY2hlY2tSYWRpbygpXCJcbiAgICAgICAgc3R5bGU9XCJcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAjMWM0ZjgyOyBcbiAgICAgICAgICBjb2xvcjogd2hpdGU7IFxuICAgICAgICAgIHBhZGRpbmc6IDEwcHggMjBweDsgXG4gICAgICAgICAgYm9yZGVyOiBub25lOyBcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiA1cHg7XG4gICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICAgICAgXCJcbiAgICAgID5TdWJtaXQgQW5zd2VyPC9idXR0b24+XG4gICAgICA8ZGl2IGlkPVwicmFkaW8tZmVlZGJhY2tcIiBzdHlsZT1cIm1hcmdpbi10b3A6IDE1cHg7IGZvbnQtd2VpZ2h0OiBib2xkO1wiPjwvZGl2PlxuICAgIDwvZGl2PlxuICBgO1xuXG4gICh3aW5kb3cgYXMgYW55KS5jaGVja1JhZGlvID0gKCkgPT4ge1xuICAgIGNvbnN0IHNlbGVjdGVkID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcignaW5wdXRbbmFtZT1cImNhcGl0YWxcIl06Y2hlY2tlZCcpIGFzIEhUTUxJbnB1dEVsZW1lbnQ7XG4gICAgY29uc3QgZmVlZGJhY2sgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgncmFkaW8tZmVlZGJhY2snKTtcbiAgICBcbiAgICBpZiAoIXNlbGVjdGVkKSB7XG4gICAgICBpZiAoZmVlZGJhY2spIGZlZWRiYWNrLmlubmVySFRNTCA9ICc8c3BhbiBzdHlsZT1cImNvbG9yOiAjZmZjMTA3O1wiPlBsZWFzZSBzZWxlY3QgYW4gYW5zd2VyLjwvc3Bhbj4nO1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBcbiAgICBpZiAoZmVlZGJhY2spIHtcbiAgICAgIGlmIChzZWxlY3RlZC52YWx1ZSA9PT0gJ3BhcmlzJykge1xuICAgICAgICBmZWVkYmFjay5pbm5lckhUTUwgPSAnPHNwYW4gc3R5bGU9XCJjb2xvcjogIzI4YTc0NTtcIj7inJMgQ29ycmVjdCEgUGFyaXMgaXMgdGhlIGNhcGl0YWwgb2YgRnJhbmNlLjwvc3Bhbj4nO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgZmVlZGJhY2suaW5uZXJIVE1MID0gJzxzcGFuIHN0eWxlPVwiY29sb3I6ICNkYzM1NDU7XCI+4pyXIEluY29ycmVjdC4gVGhlIGNhcGl0YWwgb2YgRnJhbmNlIGlzIFBhcmlzLjwvc3Bhbj4nO1xuICAgICAgfVxuICAgIH1cbiAgfTtcbn1cblxuLy8gRXhwcmVzc2lvbiBXaWRnZXRcbmZ1bmN0aW9uIHJlbmRlckV4cHJlc3Npb24oY29udGFpbmVyOiBIVE1MRWxlbWVudCkge1xuICBjb250YWluZXIuaW5uZXJIVE1MID0gYFxuICAgIDxkaXYgY2xhc3M9XCJleHByZXNzaW9uLXdpZGdldFwiPlxuICAgICAgPGgzIHN0eWxlPVwiY29sb3I6ICMxYzRmODI7IG1hcmdpbi1ib3R0b206IDIwcHg7IGZvbnQtc2l6ZTogMThweDtcIj5Tb2x2ZSB0aGUgRXhwcmVzc2lvbjwvaDM+XG4gICAgICA8cCBzdHlsZT1cImNvbG9yOiAjMzMzOyBtYXJnaW4tYm90dG9tOiAxNXB4O1wiPlNpbXBsaWZ5OiAyeCArIDN4IC0gNTwvcD5cbiAgICAgIFxuICAgICAgPGlucHV0IFxuICAgICAgICB0eXBlPVwidGV4dFwiIFxuICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHNpbXBsaWZpZWQgZXhwcmVzc2lvblwiXG4gICAgICAgIHN0eWxlPVwiXG4gICAgICAgICAgcGFkZGluZzogMTBweDsgXG4gICAgICAgICAgYm9yZGVyOiAycHggc29saWQgI2RkZDsgXG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogNXB4OyBcbiAgICAgICAgICBmb250LXNpemU6IDE2cHg7XG4gICAgICAgICAgd2lkdGg6IDI1MHB4O1xuICAgICAgICAgIG1hcmdpbi1yaWdodDogMTBweDtcbiAgICAgICAgXCJcbiAgICAgICAgaWQ9XCJleHByZXNzaW9uLWFuc3dlclwiXG4gICAgICAvPlxuICAgICAgPGJ1dHRvbiBcbiAgICAgICAgb25jbGljaz1cImNoZWNrRXhwcmVzc2lvbigpXCJcbiAgICAgICAgc3R5bGU9XCJcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAjMWM0ZjgyOyBcbiAgICAgICAgICBjb2xvcjogd2hpdGU7IFxuICAgICAgICAgIHBhZGRpbmc6IDEwcHggMjBweDsgXG4gICAgICAgICAgYm9yZGVyOiBub25lOyBcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiA1cHg7XG4gICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICAgICAgXCJcbiAgICAgID5DaGVjayBBbnN3ZXI8L2J1dHRvbj5cbiAgICAgIFxuICAgICAgPGRpdiBzdHlsZT1cIm1hcmdpbi10b3A6IDIwcHg7IHBhZGRpbmc6IDE1cHg7IGJhY2tncm91bmQ6ICNmOGY5ZmE7IGJvcmRlci1yYWRpdXM6IDVweDtcIj5cbiAgICAgICAgPHAgc3R5bGU9XCJtYXJnaW46IDA7IGNvbG9yOiAjNjY2OyBmb250LXNpemU6IDE0cHg7XCI+XG4gICAgICAgICAgPHN0cm9uZz5IaW50Ojwvc3Ryb25nPiBDb21iaW5lIGxpa2UgdGVybXMgKHRlcm1zIHdpdGggdGhlIHNhbWUgdmFyaWFibGUpXG4gICAgICAgIDwvcD5cbiAgICAgIDwvZGl2PlxuICAgICAgXG4gICAgICA8ZGl2IGlkPVwiZXhwcmVzc2lvbi1mZWVkYmFja1wiIHN0eWxlPVwibWFyZ2luLXRvcDogMTVweDsgZm9udC13ZWlnaHQ6IGJvbGQ7XCI+PC9kaXY+XG4gICAgPC9kaXY+XG4gIGA7XG5cbiAgKHdpbmRvdyBhcyBhbnkpLmNoZWNrRXhwcmVzc2lvbiA9ICgpID0+IHtcbiAgICBjb25zdCBpbnB1dCA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdleHByZXNzaW9uLWFuc3dlcicpIGFzIEhUTUxJbnB1dEVsZW1lbnQ7XG4gICAgY29uc3QgZmVlZGJhY2sgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnZXhwcmVzc2lvbi1mZWVkYmFjaycpO1xuICAgIFxuICAgIGlmIChpbnB1dCAmJiBmZWVkYmFjaykge1xuICAgICAgY29uc3QgYW5zd2VyID0gaW5wdXQudmFsdWUudG9Mb3dlckNhc2UoKS5yZXBsYWNlKC9cXHMvZywgJycpO1xuICAgICAgY29uc3QgY29ycmVjdEFuc3dlcnMgPSBbJzV4LTUnLCAnNXgrLTUnLCAnLTUrNXgnXTtcbiAgICAgIFxuICAgICAgaWYgKGNvcnJlY3RBbnN3ZXJzLmluY2x1ZGVzKGFuc3dlcikpIHtcbiAgICAgICAgZmVlZGJhY2suaW5uZXJIVE1MID0gJzxzcGFuIHN0eWxlPVwiY29sb3I6ICMyOGE3NDU7XCI+4pyTIEV4Y2VsbGVudCEgMnggKyAzeCAtIDUgPSA1eCAtIDU8L3NwYW4+JztcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGZlZWRiYWNrLmlubmVySFRNTCA9ICc8c3BhbiBzdHlsZT1cImNvbG9yOiAjZGMzNTQ1O1wiPuKclyBOb3QgcXVpdGUuIFJlbWVtYmVyIHRvIGNvbWJpbmUgbGlrZSB0ZXJtczogMnggKyAzeCA9IDV4PC9zcGFuPic7XG4gICAgICB9XG4gICAgfVxuICB9O1xufVxuXG4vLyBQbGFjZWhvbGRlciBmb3IgdW5zdXBwb3J0ZWQgd2lkZ2V0c1xuZnVuY3Rpb24gcmVuZGVyUGxhY2Vob2xkZXIoY29udGFpbmVyOiBIVE1MRWxlbWVudCwgd2lkZ2V0VHlwZTogc3RyaW5nKSB7XG4gIGNvbnRhaW5lci5pbm5lckhUTUwgPSBgXG4gICAgPGRpdiBzdHlsZT1cInRleHQtYWxpZ246IGNlbnRlcjsgcGFkZGluZzogNDBweDtcIj5cbiAgICAgIDxkaXYgc3R5bGU9XCJmb250LXNpemU6IDQ4cHg7IG1hcmdpbi1ib3R0b206IDIwcHg7XCI+8J+OrjwvZGl2PlxuICAgICAgPGgzIHN0eWxlPVwiY29sb3I6ICMxYzRmODI7IG1hcmdpbi1ib3R0b206IDE1cHg7XCI+UGVyc2V1cyBXaWRnZXQ6ICR7d2lkZ2V0VHlwZX08L2gzPlxuICAgICAgPHAgc3R5bGU9XCJjb2xvcjogIzY2NjsgbWFyZ2luLWJvdHRvbTogMjBweDtcIj5UaGlzIGVkdWNhdGlvbmFsIHdpZGdldCBpcyBiZWluZyBsb2FkZWQuLi48L3A+XG4gICAgICA8ZGl2IHN0eWxlPVwiYmFja2dyb3VuZDogI2Y4ZjlmYTsgcGFkZGluZzogMTVweDsgYm9yZGVyLXJhZGl1czogOHB4OyBib3JkZXItbGVmdDogNHB4IHNvbGlkICMxYzRmODI7XCI+XG4gICAgICAgIDxwIHN0eWxlPVwibWFyZ2luOiAwOyBjb2xvcjogIzMzMzsgZm9udC1zaXplOiAxNHB4O1wiPlxuICAgICAgICAgIFJlYWwgS2hhbiBBY2FkZW15IFBlcnNldXMgd2lkZ2V0IGludGVncmF0aW9uIGluIHByb2dyZXNzLlxuICAgICAgICAgIFRoaXMgd2lsbCByZW5kZXIgdGhlIGFjdHVhbCBpbnRlcmFjdGl2ZSBlZHVjYXRpb25hbCBjb250ZW50LlxuICAgICAgICA8L3A+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgYDtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUVmZmVjdCIsInVzZVJlZiIsIlBlcnNldXNSZW5kZXJlciIsIndpZGdldFR5cGUiLCJ3aWRnZXREYXRhIiwiY29udGFpbmVyUmVmIiwiY3VycmVudCIsImlubmVySFRNTCIsInJlbmRlck51bWVyaWNJbnB1dCIsInJlbmRlckNhdGVnb3JpemVyIiwicmVuZGVyTWF0Y2hlciIsInJlbmRlck9yZGVyZXIiLCJyZW5kZXJSYWRpbyIsInJlbmRlckV4cHJlc3Npb24iLCJyZW5kZXJHcmFwaGVyIiwicmVuZGVyTWF0cml4IiwicmVuZGVyTW9sZWN1bGUiLCJyZW5kZXJQaGV0U2ltdWxhdGlvbiIsInJlbmRlckludGVyYWN0aXZlR3JhcGgiLCJyZW5kZXJQYXNzYWdlIiwicmVuZGVyU29ydGVyIiwicmVuZGVyQ1NQcm9ncmFtIiwicmVuZGVyUHl0aG9uUHJvZ3JhbSIsInJlbmRlclBsYWNlaG9sZGVyIiwiZGl2IiwicmVmIiwiY2xhc3NOYW1lIiwic3R5bGUiLCJmb250RmFtaWx5IiwiY29udGFpbmVyIiwid2luZG93IiwiY2hlY2tOdW1lcmljQW5zd2VyIiwiaW5wdXQiLCJkb2N1bWVudCIsImdldEVsZW1lbnRCeUlkIiwiZmVlZGJhY2siLCJhbnN3ZXIiLCJwYXJzZUludCIsInZhbHVlIiwic2V0dXBEcmFnQW5kRHJvcCIsImRyYWdnYWJsZXMiLCJxdWVyeVNlbGVjdG9yQWxsIiwiZHJvcFpvbmVzIiwiZm9yRWFjaCIsImRyYWdnYWJsZSIsImFkZEV2ZW50TGlzdGVuZXIiLCJlIiwiZGF0YVRyYW5zZmVyIiwic2V0RGF0YSIsIm91dGVySFRNTCIsImRhdGFzZXQiLCJjYXRlZ29yeSIsInpvbmUiLCJwcmV2ZW50RGVmYXVsdCIsImh0bWwiLCJnZXREYXRhIiwiaWQiLCJpbmNsdWRlcyIsImNoZWNrQ2F0ZWdvcml6ZXIiLCJtYW1tYWxzWm9uZSIsImJpcmRzWm9uZSIsIm1hbW1hbHNDb3VudCIsImNoaWxkcmVuIiwibGVuZ3RoIiwiYmlyZHNDb3VudCIsInNlbGVjdGVkSXRlbXMiLCJzZWxlY3RNYXRjaCIsImVsZW1lbnQiLCJib3JkZXIiLCJwdXNoIiwiZmlyc3QiLCJtYXRjaCIsImJhY2tncm91bmQiLCJzZXRUaW1lb3V0Iiwic2V0dXBTb3J0YWJsZSIsInNvcnRhYmxlTGlzdCIsImRyYWdnZWRFbGVtZW50IiwidGFyZ2V0Iiwib3BhY2l0eSIsImNsYXNzTGlzdCIsImNvbnRhaW5zIiwicmVjdCIsImdldEJvdW5kaW5nQ2xpZW50UmVjdCIsIm1pZHBvaW50IiwidG9wIiwiaGVpZ2h0IiwiY2xpZW50WSIsImluc2VydEJlZm9yZSIsIm5leHRTaWJsaW5nIiwiY2hlY2tPcmRlciIsIml0ZW1zIiwiQXJyYXkiLCJmcm9tIiwiaXNDb3JyZWN0IiwiaXRlbSIsImluZGV4IiwiZXhwZWN0ZWRPcmRlciIsIm9yZGVyIiwiY2hlY2tSYWRpbyIsInNlbGVjdGVkIiwicXVlcnlTZWxlY3RvciIsImNoZWNrRXhwcmVzc2lvbiIsInRvTG93ZXJDYXNlIiwicmVwbGFjZSIsImNvcnJlY3RBbnN3ZXJzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/PerseusRenderer.tsx\n"));

/***/ })

});
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, Metric, Text, Badge, ProgressBar } from '@tremor/react';
import { 
  Users, 
  Mail, 
  Phone, 
  Calendar, 
  TrendingUp, 
  Award,
  Building2,
  GraduationCap,
  DollarSign
} from 'lucide-react';
// Avatar component - we'll create a simple one for now
const Avatar: React.FC<{ className: string; children: React.ReactNode }> = ({ className, children }) => (
  <div className={className}>{children}</div>
);

const AvatarImage: React.FC<{ src: string; alt: string; className: string }> = ({ src, alt, className }) => (
  <img src={src} alt={alt} className={className} />
);

const AvatarFallback: React.FC<{ className: string; children: React.ReactNode }> = ({ className, children }) => (
  <div className={className}>{children}</div>
);

interface StaffMember {
  id: string;
  name: string;
  position: string;
  department: string;
  photo: string;
  email: string;
  phone: string;
  experience: string;
  education: string;
  specialization: string;
  startDate: string;
  salary: number;
  performance: number;
  reportsTo?: string;
  directReports?: string[];
  teamSize?: number;
  students?: number;
}

interface StaffData {
  organizationChart: {
    principal: StaffMember;
    leadership: StaffMember[];
    departmentHeads: StaffMember[];
    supportStaff: StaffMember[];
  };
  statistics: {
    totalEmployees: number;
    totalDepartments: number;
    averageTenure: number;
    averageSalary: number;
    performanceAverage: number;
    retentionRate: number;
    diversityIndex: number;
    satisfactionScore: number;
  };
  recentHires: Array<{
    name: string;
    position: string;
    department: string;
    startDate: string;
    photo: string;
  }>;
  upcomingReviews: Array<{
    employeeName: string;
    position: string;
    reviewDate: string;
    reviewType: string;
    currentRating: number;
  }>;
}

export const StaffManagement: React.FC = () => {
  const [staffData, setStaffData] = useState<StaffData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedEmployee, setSelectedEmployee] = useState<StaffMember | null>(null);

  useEffect(() => {
    const fetchStaffData = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/school/staff');
        if (response.ok) {
          const data = await response.json();
          setStaffData(data);
        }
      } catch (error) {
        console.error('Failed to fetch staff data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStaffData();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-cyan-400">Loading Staff Management...</div>
      </div>
    );
  }

  if (!staffData) {
    return (
      <div className="text-center text-red-400">Failed to load staff data</div>
    );
  }

  const EmployeeCard: React.FC<{ employee: StaffMember; level: number }> = ({ employee, level }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`relative cursor-pointer ${
        level === 0 ? 'mb-8' : level === 1 ? 'mb-6' : 'mb-4'
      }`}
      onClick={() => setSelectedEmployee(employee)}
    >
      <div
        className={`
          bg-gradient-to-br from-gray-800/60 to-gray-900/60
          backdrop-blur-xl border border-gray-600/30 rounded-xl
          hover:border-cyan-400/50 transition-all duration-300
          hover:shadow-lg hover:shadow-cyan-400/20
          ${level === 0 ? 'border-2 border-yellow-400/50' : ''}
          ${level === 1 ? 'border-blue-400/30' : ''}
        `}
        style={{ padding: 'clamp(8px, 0.8vw, 16px)' }}
      >
        <div
          className="flex items-center"
          style={{ gap: 'clamp(8px, 0.8vw, 16px)' }}
        >
          <div className="relative">
            <Avatar
              className="rounded-full overflow-hidden border-2 border-cyan-400/30"
              style={{
                width: 'clamp(40px, 4vw, 64px)',
                height: 'clamp(40px, 4vw, 64px)'
              }}
            >
              <AvatarImage src={employee.photo} alt={employee.name} className="w-full h-full object-cover" />
              <AvatarFallback
                className="bg-gray-700 text-white font-semibold"
                style={{ fontSize: 'clamp(12px, 1vw, 18px)' }}
              >
                {employee.name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            {level === 0 && (
              <div
                className="absolute -top-1 -right-1 bg-yellow-400 rounded-full flex items-center justify-center"
                style={{
                  width: 'clamp(20px, 1.5vw, 24px)',
                  height: 'clamp(20px, 1.5vw, 24px)'
                }}
              >
                <Award
                  className="text-gray-900"
                  style={{ width: 'clamp(10px, 0.8vw, 12px)', height: 'clamp(10px, 0.8vw, 12px)' }}
                />
              </div>
            )}
          </div>

          <div className="flex-1">
            <h3
              className="font-bold text-white"
              style={{ fontSize: 'clamp(14px, 1.1vw, 18px)' }}
            >
              {employee.name}
            </h3>
            <p
              className="text-cyan-400 font-medium"
              style={{ fontSize: 'clamp(11px, 0.9vw, 14px)' }}
            >
              {employee.position}
            </p>
            <p
              className="text-gray-400"
              style={{ fontSize: 'clamp(10px, 0.8vw, 12px)' }}
            >
              {employee.department}
            </p>

            <div
              className="flex items-center mt-1"
              style={{
                gap: 'clamp(8px, 0.8vw, 16px)',
                marginTop: 'clamp(4px, 0.4vw, 8px)'
              }}
            >
              <div className="flex items-center gap-1">
                <TrendingUp
                  className="text-green-400"
                  style={{ width: 'clamp(10px, 0.8vw, 12px)', height: 'clamp(10px, 0.8vw, 12px)' }}
                />
                <span
                  className="text-green-400"
                  style={{ fontSize: 'clamp(9px, 0.7vw, 11px)' }}
                >
                  {employee.performance}%
                </span>
              </div>
              <div className="flex items-center gap-1">
                <Calendar
                  className="text-blue-400"
                  style={{ width: 'clamp(10px, 0.8vw, 12px)', height: 'clamp(10px, 0.8vw, 12px)' }}
                />
                <span
                  className="text-gray-400"
                  style={{ fontSize: 'clamp(9px, 0.7vw, 11px)' }}
                >
                  {employee.experience}
                </span>
              </div>
              {employee.teamSize && (
                <div className="flex items-center gap-1">
                  <Users
                    className="text-purple-400"
                    style={{ width: 'clamp(10px, 0.8vw, 12px)', height: 'clamp(10px, 0.8vw, 12px)' }}
                  />
                  <span
                    className="text-gray-400"
                    style={{ fontSize: 'clamp(9px, 0.7vw, 11px)' }}
                  >
                    {employee.teamSize} team
                  </span>
                </div>
              )}
            </div>
          </div>

          <div className="text-right">
            <Badge
              color={employee.performance >= 95 ? 'green' : employee.performance >= 90 ? 'blue' : 'yellow'}
              size="sm"
            >
              {employee.performance >= 95 ? 'Excellent' : employee.performance >= 90 ? 'Good' : 'Average'}
            </Badge>
            <p
              className="text-gray-400 mt-1"
              style={{ fontSize: 'clamp(9px, 0.7vw, 11px)' }}
            >
              ${(employee.salary / 1000).toFixed(0)}K
            </p>
          </div>
        </div>
      </div>
    </motion.div>
  );

  return (
    <div
      className="space-y-4 p-2 h-full overflow-auto"
      style={{
        fontSize: 'clamp(10px, 0.7vw, 14px)',
        gap: 'clamp(8px, 0.5vw, 24px)'
      }}
    >
      {/* Staff Statistics Dashboard */}
      <div
        className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-4"
        style={{ gap: 'clamp(8px, 0.5vw, 16px)' }}
      >
        <Card className="bg-gradient-to-br from-blue-900/20 to-blue-800/20 border-blue-500/30">
          <div className="flex items-center gap-2" style={{ gap: 'clamp(6px, 0.4vw, 12px)' }}>
            <div
              className="bg-blue-500/20 rounded-lg flex items-center justify-center"
              style={{
                width: 'clamp(32px, 3vw, 48px)',
                height: 'clamp(32px, 3vw, 48px)'
              }}
            >
              <Users className="text-blue-400" style={{ width: 'clamp(16px, 1.5vw, 24px)', height: 'clamp(16px, 1.5vw, 24px)' }} />
            </div>
            <div>
              <Metric className="text-white" style={{ fontSize: 'clamp(16px, 1.2vw, 24px)' }}>{staffData.statistics.totalEmployees}</Metric>
              <Text className="text-gray-400" style={{ fontSize: 'clamp(10px, 0.8vw, 14px)' }}>Total Staff</Text>
            </div>
          </div>
        </Card>

        <Card className="bg-gradient-to-br from-green-900/20 to-green-800/20 border-green-500/30">
          <div className="flex items-center gap-2" style={{ gap: 'clamp(6px, 0.4vw, 12px)' }}>
            <div
              className="bg-green-500/20 rounded-lg flex items-center justify-center"
              style={{
                width: 'clamp(32px, 3vw, 48px)',
                height: 'clamp(32px, 3vw, 48px)'
              }}
            >
              <TrendingUp className="text-green-400" style={{ width: 'clamp(16px, 1.5vw, 24px)', height: 'clamp(16px, 1.5vw, 24px)' }} />
            </div>
            <div>
              <Metric className="text-white" style={{ fontSize: 'clamp(16px, 1.2vw, 24px)' }}>{staffData.statistics.performanceAverage}%</Metric>
              <Text className="text-gray-400" style={{ fontSize: 'clamp(10px, 0.8vw, 14px)' }}>Avg Performance</Text>
            </div>
          </div>
        </Card>

        <Card className="bg-gradient-to-br from-purple-900/20 to-purple-800/20 border-purple-500/30">
          <div className="flex items-center gap-2" style={{ gap: 'clamp(6px, 0.4vw, 12px)' }}>
            <div
              className="bg-purple-500/20 rounded-lg flex items-center justify-center"
              style={{
                width: 'clamp(32px, 3vw, 48px)',
                height: 'clamp(32px, 3vw, 48px)'
              }}
            >
              <DollarSign className="text-purple-400" style={{ width: 'clamp(16px, 1.5vw, 24px)', height: 'clamp(16px, 1.5vw, 24px)' }} />
            </div>
            <div>
              <Metric className="text-white" style={{ fontSize: 'clamp(16px, 1.2vw, 24px)' }}>${(staffData.statistics.averageSalary / 1000).toFixed(0)}K</Metric>
              <Text className="text-gray-400" style={{ fontSize: 'clamp(10px, 0.8vw, 14px)' }}>Avg Salary</Text>
            </div>
          </div>
        </Card>

        <Card className="bg-gradient-to-br from-cyan-900/20 to-cyan-800/20 border-cyan-500/30">
          <div className="flex items-center gap-2" style={{ gap: 'clamp(6px, 0.4vw, 12px)' }}>
            <div
              className="bg-cyan-500/20 rounded-lg flex items-center justify-center"
              style={{
                width: 'clamp(32px, 3vw, 48px)',
                height: 'clamp(32px, 3vw, 48px)'
              }}
            >
              <Award className="text-cyan-400" style={{ width: 'clamp(16px, 1.5vw, 24px)', height: 'clamp(16px, 1.5vw, 24px)' }} />
            </div>
            <div>
              <Metric className="text-white" style={{ fontSize: 'clamp(16px, 1.2vw, 24px)' }}>{staffData.statistics.retentionRate}%</Metric>
              <Text className="text-gray-400" style={{ fontSize: 'clamp(10px, 0.8vw, 14px)' }}>Retention</Text>
            </div>
          </div>
        </Card>
      </div>

      {/* Organization Chart */}
      <Card className="bg-gradient-to-br from-gray-900/40 to-gray-800/40 border-gray-600/30">
        <div
          className="p-3"
          style={{ padding: 'clamp(12px, 1vw, 24px)' }}
        >
          <h2
            className="font-bold text-white mb-4 flex items-center gap-2"
            style={{
              fontSize: 'clamp(16px, 1.4vw, 24px)',
              marginBottom: 'clamp(16px, 1.2vw, 24px)',
              gap: 'clamp(6px, 0.4vw, 8px)'
            }}
          >
            <Building2
              className="text-cyan-400"
              style={{ width: 'clamp(18px, 1.5vw, 24px)', height: 'clamp(18px, 1.5vw, 24px)' }}
            />
            Organization Chart
          </h2>

          {/* Principal */}
          <div
            className="text-center mb-6"
            style={{ marginBottom: 'clamp(24px, 2vw, 32px)' }}
          >
            <EmployeeCard employee={staffData.organizationChart.principal} level={0} />
          </div>

          {/* Leadership Team */}
          <div
            className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6"
            style={{
              gap: 'clamp(12px, 1vw, 24px)',
              marginBottom: 'clamp(24px, 2vw, 32px)'
            }}
          >
            {staffData.organizationChart.leadership.map((leader) => (
              <EmployeeCard key={leader.id} employee={leader} level={1} />
            ))}
          </div>

          {/* Department Heads */}
          <div
            className="grid grid-cols-1 md:grid-cols-3 gap-3"
            style={{ gap: 'clamp(8px, 0.8vw, 16px)' }}
          >
            {staffData.organizationChart.departmentHeads.map((head) => (
              <EmployeeCard key={head.id} employee={head} level={2} />
            ))}
          </div>
        </div>
      </Card>
    </div>
  );
};


import type { NextApiRequest, NextApiResponse } from 'next';
import { generateStructuredData, Type } from '../../backend/lib/gemini';

type Stat = {
  id: string;
  icon: 'UserGroupIcon' | 'CloudArrowUpIcon' | 'ShieldCheckIcon' | 'ChartBarIcon';
  label: string;
  value: string;
  change: string;
  changeType: 'increase' | 'decrease';
};
type ChartDataPoint = { name: string; usage: number };
type AnalyticsData = {
  stats: Stat[];
  chartData: ChartDataPoint[];
};

const analyticsSchema = {
    type: Type.OBJECT,
    properties: {
        stats: {
            type: Type.ARRAY,
            description: "A list of 4 system statistics.",
            items: {
                type: Type.OBJECT,
                properties: {
                    id: { type: Type.STRING, description: "A unique ID for the stat, e.g., 'users'." },
                    icon: { type: Type.STRING, enum: ['UserGroupIcon', 'CloudArrowUpIcon', 'ShieldCheckIcon', 'ChartBarIcon'], description: "The icon to display." },
                    label: { type: Type.STRING, description: "The label for the stat, e.g., 'Active Users'." },
                    value: { type: Type.STRING, description: "The primary value, e.g., '1,302' or '99.98%'." },
                    change: { type: Type.STRING, description: "The change description, e.g., '+14.2% this month'." },
                    changeType: { type: Type.STRING, enum: ['increase', 'decrease'] }
                },
                required: ["id", "icon", "label", "value", "change", "changeType"]
            }
        },
        chartData: {
            type: Type.ARRAY,
            description: "A list of 7 data points for a weekly usage chart.",
            items: {
                type: Type.OBJECT,
                properties: {
                    name: { type: Type.STRING, description: "The day of the week abbreviation, e.g., 'Mon'." },
                    usage: { type: Type.NUMBER, description: "A random usage value between 50 and 300." }
                },
                required: ["name", "usage"]
            }
        }
    },
    required: ["stats", "chartData"]
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<AnalyticsData | { error: string }>
) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  try {
    const prompt = "Generate plausible data for a futuristic system analytics dashboard. Provide 4 summary stats and data for a 7-day usage chart.";
    const data = await generateStructuredData<AnalyticsData>(prompt, analyticsSchema);
    res.status(200).json(data);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Failed to fetch analytics data from AI.' });
  }
}

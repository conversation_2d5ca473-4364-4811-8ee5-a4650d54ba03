
import type { NextApiRequest, NextApiResponse } from 'next';
import { executeQuery, initializeDatabase, seedDatabase } from '../../backend/lib/database';

type Stat = {
  id: string;
  icon: 'UserGroupIcon' | 'CloudArrowUpIcon' | 'ShieldCheckIcon' | 'ChartBarIcon';
  label: string;
  value: string;
  change: string;
  changeType: 'increase' | 'decrease';
};

type ChartDataPoint = { name: string; usage: number };

type AnalyticsData = {
  stats: Stat[];
  chartData: ChartDataPoint[];
};

type DatabaseStat = {
  id: number;
  metric_name: string;
  value: number;
  unit: string;
  icon: string;
};



export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<AnalyticsData | { error: string }>
) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  try {
    // Initialize database if needed
    await initializeDatabase();
    await seedDatabase();

    // Get stats from database
    const dbStats = await executeQuery<DatabaseStat>(`
      SELECT id, metric_name, value, unit, icon
      FROM analytics
      ORDER BY id ASC
    `);

    // Transform database stats to expected format
    const stats: Stat[] = dbStats.map((stat, index) => {
      const iconMap: Record<string, 'UserGroupIcon' | 'CloudArrowUpIcon' | 'ShieldCheckIcon' | 'ChartBarIcon'> = {
        'CurrencyDollarIcon': 'ChartBarIcon',
        'UsersIcon': 'UserGroupIcon',
        'TrendingUpIcon': 'CloudArrowUpIcon',
        'ChartBarIcon': 'ShieldCheckIcon'
      };

      const changeValues = ['+14.2%', '+8.7%', '+23.1%', '+5.4%'];
      const changeTypes: ('increase' | 'decrease')[] = ['increase', 'increase', 'increase', 'increase'];

      return {
        id: stat.id.toString(),
        icon: iconMap[stat.icon] || 'ChartBarIcon',
        label: stat.metric_name,
        value: stat.unit === 'USD' ? `$${stat.value.toLocaleString()}` :
               stat.unit === '%' ? `${stat.value}%` :
               stat.value.toLocaleString(),
        change: `${changeValues[index]} this month`,
        changeType: changeTypes[index]
      };
    });

    // Generate chart data (weekly usage)
    const chartData: ChartDataPoint[] = [
      { name: 'Mon', usage: Math.floor(Math.random() * 250) + 50 },
      { name: 'Tue', usage: Math.floor(Math.random() * 250) + 50 },
      { name: 'Wed', usage: Math.floor(Math.random() * 250) + 50 },
      { name: 'Thu', usage: Math.floor(Math.random() * 250) + 50 },
      { name: 'Fri', usage: Math.floor(Math.random() * 250) + 50 },
      { name: 'Sat', usage: Math.floor(Math.random() * 250) + 50 },
      { name: 'Sun', usage: Math.floor(Math.random() * 250) + 50 }
    ];

    res.status(200).json({ stats, chartData });
  } catch (error) {
    console.error('Analytics API error:', error);

    // Fallback data if database fails
    const fallbackStats: Stat[] = [
      { id: '1', icon: 'ChartBarIcon', label: 'Revenue', value: '$125,000', change: '+14.2% this month', changeType: 'increase' },
      { id: '2', icon: 'UserGroupIcon', label: 'Users', value: '15,420', change: '+8.7% this month', changeType: 'increase' },
      { id: '3', icon: 'CloudArrowUpIcon', label: 'Growth', value: '23.5%', change: '+23.1% this month', changeType: 'increase' },
      { id: '4', icon: 'ShieldCheckIcon', label: 'Conversion', value: '4.2%', change: '+5.4% this month', changeType: 'increase' }
    ];

    const fallbackChartData: ChartDataPoint[] = [
      { name: 'Mon', usage: 180 },
      { name: 'Tue', usage: 220 },
      { name: 'Wed', usage: 190 },
      { name: 'Thu', usage: 250 },
      { name: 'Fri', usage: 280 },
      { name: 'Sat', usage: 160 },
      { name: 'Sun', usage: 140 }
    ];

    res.status(200).json({ stats: fallbackStats, chartData: fallbackChartData });
  }
}

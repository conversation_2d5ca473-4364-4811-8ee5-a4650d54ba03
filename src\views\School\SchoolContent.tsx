import React from 'react';
import { SchoolDepartment } from '../../components/Header';

// Import department components
import { SchoolDashboard } from './departments/SchoolDashboard';
import { AdministrationDashboard } from './departments/AdministrationDashboard';
import { TeacherDashboard } from './departments/TeacherDashboard';
import { FinanceDashboard } from './departments/FinanceDashboard';
import { MarketingDashboard } from './departments/MarketingDashboard';
import { ParentDashboard } from './departments/ParentDashboard';
import { StudentDashboard } from './departments/StudentDashboard';
import { SettingDashboard } from './departments/SettingDashboard';

interface SchoolContentProps {
  activeDepartment: SchoolDepartment;
  activeSubSection: string;
}

export const SchoolContent: React.FC<SchoolContentProps> = ({
  activeDepartment,
  activeSubSection
}) => {
  const contentProps = {
    activeSubSection
  };

  const renderDepartmentContent = () => {
    switch (activeDepartment) {
      case 'school':
        return <SchoolDashboard {...contentProps} />;
      case 'administration':
        return <AdministrationDashboard {...contentProps} />;
      case 'teacher':
        return <TeacherDashboard {...contentProps} />;
      case 'finance':
        return <FinanceDashboard {...contentProps} />;
      case 'marketing':
        return <MarketingDashboard {...contentProps} />;
      case 'parent':
        return <ParentDashboard {...contentProps} />;
      case 'student':
        return <StudentDashboard {...contentProps} />;
      case 'setting':
        return <SettingDashboard {...contentProps} />;
      default:
        return <SchoolDashboard {...contentProps} />;
    }
  };

  return (
    <div className="h-full">
      {renderDepartmentContent()}
    </div>
  );
};

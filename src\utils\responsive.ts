// Responsive utility functions for the entire project

export const responsiveStyles = {
  // Font sizes
  textXs: { fontSize: 'calc(var(--base-font-size) * 0.75)' },
  textSm: { fontSize: 'calc(var(--base-font-size) * 0.875)' },
  text: { fontSize: 'var(--base-font-size)' },
  textLg: { fontSize: 'calc(var(--base-font-size) * 1.125)' },
  textXl: { fontSize: 'calc(var(--base-font-size) * 1.25)' },
  text2xl: { fontSize: 'calc(var(--base-font-size) * 1.5)' },
  text3xl: { fontSize: 'calc(var(--base-font-size) * 1.875)' },
  text4xl: { fontSize: 'calc(var(--base-font-size) * 2.25)' },

  // Spacing
  spacingSm: { padding: 'calc(var(--base-spacing) * 0.5)' },
  spacing: { padding: 'var(--base-spacing)' },
  spacingLg: { padding: 'calc(var(--base-spacing) * 1.5)' },
  spacingXl: { padding: 'calc(var(--base-spacing) * 2)' },

  // Margins
  marginSm: { margin: 'calc(var(--base-spacing) * 0.5)' },
  margin: { margin: 'var(--base-spacing)' },
  marginLg: { margin: 'calc(var(--base-spacing) * 1.5)' },
  marginXl: { margin: 'calc(var(--base-spacing) * 2)' },

  // Gaps
  gapSm: { gap: 'calc(var(--base-gap) * 0.5)' },
  gap: { gap: 'var(--base-gap)' },
  gapLg: { gap: 'calc(var(--base-gap) * 1.5)' },
  gapXl: { gap: 'calc(var(--base-gap) * 2)' },

  // Icons
  iconSm: { 
    width: 'calc(var(--base-icon-size) * 0.75)', 
    height: 'calc(var(--base-icon-size) * 0.75)' 
  },
  icon: { 
    width: 'var(--base-icon-size)', 
    height: 'var(--base-icon-size)' 
  },
  iconLg: { 
    width: 'calc(var(--base-icon-size) * 1.5)', 
    height: 'calc(var(--base-icon-size) * 1.5)' 
  },
  iconXl: { 
    width: 'calc(var(--base-icon-size) * 2)', 
    height: 'calc(var(--base-icon-size) * 2)' 
  },

  // Buttons
  button: {
    height: 'var(--base-button-height)',
    padding: '0 var(--base-spacing)',
    fontSize: 'var(--base-font-size)',
    borderRadius: 'var(--base-border-radius)',
  },
  buttonSm: {
    height: 'calc(var(--base-button-height) * 0.8)',
    padding: '0 calc(var(--base-spacing) * 0.75)',
    fontSize: 'calc(var(--base-font-size) * 0.875)',
    borderRadius: 'var(--base-border-radius)',
  },
  buttonLg: {
    height: 'calc(var(--base-button-height) * 1.25)',
    padding: '0 calc(var(--base-spacing) * 1.25)',
    fontSize: 'calc(var(--base-font-size) * 1.125)',
    borderRadius: 'var(--base-border-radius)',
  },

  // Cards
  card: {
    padding: 'var(--base-card-padding)',
    borderRadius: 'calc(var(--base-border-radius) * 1.5)',
  },
  cardSm: {
    padding: 'calc(var(--base-card-padding) * 0.75)',
    borderRadius: 'var(--base-border-radius)',
  },
  cardLg: {
    padding: 'calc(var(--base-card-padding) * 1.25)',
    borderRadius: 'calc(var(--base-border-radius) * 2)',
  },

  // Border radius
  borderRadius: { borderRadius: 'var(--base-border-radius)' },
  borderRadiusSm: { borderRadius: 'calc(var(--base-border-radius) * 0.5)' },
  borderRadiusLg: { borderRadius: 'calc(var(--base-border-radius) * 1.5)' },
  borderRadiusXl: { borderRadius: 'calc(var(--base-border-radius) * 2)' },
};

// Responsive class names
export const responsiveClasses = {
  // Text sizes
  textXs: 'responsive-text-xs',
  textSm: 'responsive-text-sm',
  text: 'responsive-text',
  textLg: 'responsive-text-lg',
  textXl: 'responsive-text-xl',
  text2xl: 'responsive-text-2xl',

  // Spacing
  spacingSm: 'responsive-spacing-sm',
  spacing: 'responsive-spacing',
  spacingLg: 'responsive-spacing-lg',

  // Gaps
  gapSm: 'responsive-gap-sm',
  gap: 'responsive-gap',
  gapLg: 'responsive-gap-lg',

  // Icons
  icon: 'responsive-icon',

  // Buttons
  button: 'responsive-button',

  // Cards
  card: 'responsive-card',

  // Border radius
  borderRadius: 'responsive-border-radius',
};

// Helper function to get responsive value
export const getResponsiveValue = (baseValue: number, multiplier: number = 1): string => {
  return `calc(${baseValue}px * var(--scale-factor) * ${multiplier})`;
};

// Helper function to get responsive CSS variable
export const getResponsiveCSSVar = (varName: string, multiplier: number = 1): string => {
  return `calc(var(--${varName}) * ${multiplier})`;
};

// Responsive breakpoints
export const breakpoints = {
  xs: '(max-width: 999px)',
  sm: '(max-width: 1199px) and (min-width: 1000px)',
  md: '(max-width: 1399px) and (min-width: 1200px)',
  lg: '(max-width: 1599px) and (min-width: 1400px)',
  xl: '(max-width: 1920px) and (min-width: 1600px)',
  xxl: '(min-width: 1921px)',
};

// Scale factors for different breakpoints
export const scaleFactors = {
  xs: 0.5,
  sm: 0.6,
  md: 0.7,
  lg: 0.8,
  xl: 0.9,
  xxl: 1.0,
};

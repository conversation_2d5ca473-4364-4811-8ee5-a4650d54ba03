
import React from 'react';
import { HomeIcon, ChartBarIcon, GridIcon, SettingsIcon, UserCircleIcon, ShieldCheckIcon } from './icons';

interface NavItemProps {
    icon: React.ReactNode;
    label: string;
    active: boolean;
    onClick: () => void;
}

const NavItem = ({ icon, label, active, onClick }: NavItemProps) => (
    <button
        onClick={onClick}
        aria-label={label}
        aria-pressed={active}
        className={`w-full flex items-center justify-center rounded-lg transition-colors duration-200 relative ${
            active
            ? 'bg-brand-cyan/20 text-white'
            : 'text-gray-400 hover:bg-gray-700/50 hover:text-white'
        }`}
        style={{ padding: 'clamp(6px, 0.6vw, 12px)' }}
    >
    {active && (
      <span
        className="absolute left-0 top-1/2 -translate-y-1/2 bg-brand-cyan rounded-r-full"
        style={{
          height: 'clamp(16px, 1.2vw, 20px)',
          width: 'clamp(3px, 0.2vw, 4px)'
        }}
      />
    )}
    {icon}
  </button>
);

interface SidebarProps {
    activeView: string;
    setActiveView: (view: string) => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ activeView, setActiveView }) => {
  const navItems = [
    {
      id: 'Dashboard',
      label: 'Home',
      icon: <HomeIcon style={{ width: 'clamp(20px, 1.5vw, 24px)', height: 'clamp(20px, 1.5vw, 24px)' }} />
    },
    {
      id: 'Analytic',
      label: 'Analytics',
      icon: <ChartBarIcon style={{ width: 'clamp(20px, 1.5vw, 24px)', height: 'clamp(20px, 1.5vw, 24px)' }} />
    },
    {
      id: 'Tool',
      label: 'Modules',
      icon: <GridIcon style={{ width: 'clamp(20px, 1.5vw, 24px)', height: 'clamp(20px, 1.5vw, 24px)' }} />
    },
    {
      id: 'Setting',
      label: 'Profile',
      icon: <UserCircleIcon style={{ width: 'clamp(20px, 1.5vw, 24px)', height: 'clamp(20px, 1.5vw, 24px)' }} />
    },
    {
      id: 'Setting',
      label: 'Settings',
      icon: <SettingsIcon style={{ width: 'clamp(20px, 1.5vw, 24px)', height: 'clamp(20px, 1.5vw, 24px)' }} />
    },
  ];

  return (
    <aside
      className="bg-panel-bg border border-gray-700/50 rounded-xl flex flex-col items-center justify-between"
      style={{ padding: 'clamp(4px, 0.4vw, 8px)' }}
    >
      <div
        className="flex flex-col items-center w-full"
        style={{ gap: 'clamp(8px, 0.8vw, 16px)' }}
      >
         <div
           className="bg-gradient-to-br from-cyan-400 to-fuchsia-500 rounded-full flex items-center justify-center text-white"
           style={{
             width: 'clamp(32px, 2.5vw, 40px)',
             height: 'clamp(32px, 2.5vw, 40px)'
           }}
         >
            <ShieldCheckIcon style={{ width: 'clamp(20px, 1.8vw, 28px)', height: 'clamp(20px, 1.8vw, 28px)' }} />
        </div>
        <div className="w-full border-t border-gray-700"></div>
        {navItems.slice(0, 3).map(item => (
            <NavItem 
                key={item.label} 
                icon={item.icon} 
                label={item.label}
                active={activeView === item.id}
                onClick={() => setActiveView(item.id)} 
            />
        ))}
      </div>
      <div className="flex flex-col items-center gap-4 w-full">
        {navItems.slice(3).map(item => (
            <NavItem 
                key={item.label} 
                icon={item.icon} 
                label={item.label}
                active={activeView === item.id && item.label === 'Settings'}
                onClick={() => setActiveView(item.id)} 
            />
        ))}
      </div>
    </aside>
  );
};

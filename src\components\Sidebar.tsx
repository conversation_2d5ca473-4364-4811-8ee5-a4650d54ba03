
import React, { useState } from 'react';
import {
  HomeIcon,
  ChartBarIcon,
  GridIcon,
  SettingsIcon,
  UserCircleIcon,
  ShieldCheckIcon,
  Bars3Icon,
  XMarkIcon,
  PinIcon
} from './icons';

interface NavItemProps {
    icon: React.ReactNode;
    label: string;
    active: boolean;
    onClick: () => void;
}

const NavItem = ({ icon, label, active, onClick }: NavItemProps) => (
    <button
        onClick={onClick}
        aria-label={label}
        aria-pressed={active}
        className={`w-full flex items-center justify-center responsive-border-radius transition-colors duration-200 relative ${
            active
            ? 'bg-brand-cyan/20 text-white'
            : 'text-gray-400 hover:bg-gray-700/50 hover:text-white'
        }`}
        style={{
          padding: 'calc(var(--base-spacing) * 0.5)',
          height: 'var(--base-button-height)'
        }}
    >
    {active && (
      <span
        className="absolute left-0 top-1/2 -translate-y-1/2 bg-brand-cyan rounded-r-full"
        style={{
          height: 'calc(var(--base-button-height) * 0.6)',
          width: '3px'
        }}
      />
    )}
    {icon}
  </button>
);

interface SidebarProps {
    activeView: string;
    setActiveView: (view: string) => void;
    activeApp?: string;
    setActiveApp?: (app: string) => void;
}

export const Sidebar: React.FC<SidebarProps> = ({
  activeView,
  setActiveView,
  activeApp,
  setActiveApp
}) => {
  const [isCollapsed, setIsCollapsed] = useState(true);
  const [isPinned, setIsPinned] = useState(false);

  // Main function buttons for the collapsible sidebar
  const functionButtons = [
    {
      id: 'gamification',
      label: 'Gamification',
      icon: '🎮',
      description: 'Learning Games'
    },
    {
      id: 'coder',
      label: 'Coder',
      icon: '💻',
      description: 'IDE View'
    },
    {
      id: 'media',
      label: 'Media',
      icon: '🎥',
      description: 'Media Hub'
    },
    {
      id: 'studio',
      label: 'Studio',
      icon: '🧑‍🎨',
      description: 'Creative Workspace'
    }
  ];

  // Original navigation items (when collapsed)
  const navItems = [
    {
      id: 'Dashboard',
      label: 'Home',
      icon: <HomeIcon className="responsive-icon" />
    },
    {
      id: 'Analytic',
      label: 'Analytics',
      icon: <ChartBarIcon className="responsive-icon" />
    },
    {
      id: 'Tool',
      label: 'Modules',
      icon: <GridIcon className="responsive-icon" />
    },
    {
      id: 'Setting',
      label: 'Profile',
      icon: <UserCircleIcon className="responsive-icon" />
    },
    {
      id: 'Setting',
      label: 'Settings',
      icon: <SettingsIcon className="responsive-icon" />
    },
  ];

  const handleFunctionButtonClick = (appId: string) => {
    if (setActiveApp) {
      setActiveApp(appId);
    }
    // Auto-collapse unless pinned
    if (!isPinned) {
      setIsCollapsed(true);
    }
  };

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  const togglePin = () => {
    setIsPinned(!isPinned);
  };

  return (
    <>
      {/* Toggle Button - Always visible in top-left */}
      <button
        onClick={toggleSidebar}
        className="fixed top-4 left-4 z-50 bg-glass-bg border border-glass-border backdrop-filter backdrop-blur-20 rounded-lg p-2 hover:bg-glass-bg-light transition-all duration-300 hover:scale-105"
        style={{
          boxShadow: 'var(--glass-shadow)',
          width: 'calc(var(--base-button-height) * 1.2)',
          height: 'calc(var(--base-button-height) * 1.2)'
        }}
      >
        {isCollapsed ? (
          <Bars3Icon className="w-6 h-6 text-accent-blue" />
        ) : (
          <XMarkIcon className="w-6 h-6 text-accent-blue" />
        )}
      </button>

      {/* Collapsible Sidebar */}
      <aside
        className={`fixed left-0 top-0 h-full bg-glass-bg border-r border-glass-border backdrop-filter backdrop-blur-20 z-40 transition-all duration-300 ${
          isCollapsed ? '-translate-x-full' : 'translate-x-0'
        }`}
        style={{
          width: 'calc(280px * var(--content-scale))',
          boxShadow: 'var(--glass-shadow)'
        }}
      >
        <div className="flex flex-col h-full p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="bg-gradient-to-br from-accent-blue to-accent-cyan rounded-full flex items-center justify-center text-white w-10 h-10">
                <ShieldCheckIcon className="w-6 h-6" />
              </div>
              <div>
                <h2 className="text-text-primary font-semibold text-lg">Functions</h2>
                <p className="text-text-muted text-sm">Choose your workspace</p>
              </div>
            </div>
            <button
              onClick={togglePin}
              className={`p-2 rounded-lg transition-all duration-200 ${
                isPinned
                  ? 'bg-accent-blue/20 text-accent-blue'
                  : 'text-text-muted hover:text-text-primary hover:bg-glass-bg-light'
              }`}
            >
              <PinIcon className="w-4 h-4" />
            </button>
          </div>

          {/* Function Buttons */}
          <div className="space-y-3 mb-6">
            {functionButtons.map((button) => (
              <button
                key={button.id}
                onClick={() => handleFunctionButtonClick(button.id)}
                className={`w-full flex items-center gap-4 p-4 rounded-xl transition-all duration-300 group ${
                  activeApp === button.id
                    ? 'bg-accent-blue/20 border border-accent-blue/30 shadow-lg shadow-accent-blue/10'
                    : 'bg-glass-bg-light border border-glass-border hover:bg-accent-blue/10 hover:border-accent-blue/20'
                }`}
              >
                <div className="text-2xl">{button.icon}</div>
                <div className="flex-1 text-left">
                  <div className={`font-semibold ${
                    activeApp === button.id ? 'text-accent-blue' : 'text-text-primary'
                  }`}>
                    {button.label}
                  </div>
                  <div className="text-text-muted text-sm">{button.description}</div>
                </div>
                <div className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  activeApp === button.id ? 'bg-accent-blue' : 'bg-transparent'
                }`} />
              </button>
            ))}
          </div>

          {/* Divider */}
          <div className="border-t border-glass-border mb-6"></div>

          {/* Original Navigation */}
          <div className="space-y-2">
            <h3 className="text-text-muted text-sm font-medium mb-3">Dashboard</h3>
            {navItems.slice(0, 3).map(item => (
              <button
                key={item.label}
                onClick={() => {
                  setActiveView(item.id);
                  if (setActiveApp) setActiveApp('');
                  if (!isPinned) setIsCollapsed(true);
                }}
                className={`w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200 ${
                  activeView === item.id && !activeApp
                    ? 'bg-accent-blue/20 text-accent-blue'
                    : 'text-text-secondary hover:text-text-primary hover:bg-glass-bg-light'
                }`}
              >
                {item.icon}
                <span className="font-medium">{item.label}</span>
              </button>
            ))}
          </div>

          {/* Bottom Navigation */}
          <div className="mt-auto space-y-2">
            {navItems.slice(3).map(item => (
              <button
                key={item.label}
                onClick={() => {
                  setActiveView(item.id);
                  if (setActiveApp) setActiveApp('');
                  if (!isPinned) setIsCollapsed(true);
                }}
                className={`w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200 ${
                  activeView === item.id && item.label === 'Settings' && !activeApp
                    ? 'bg-accent-blue/20 text-accent-blue'
                    : 'text-text-secondary hover:text-text-primary hover:bg-glass-bg-light'
                }`}
              >
                {item.icon}
                <span className="font-medium">{item.label}</span>
              </button>
            ))}
          </div>
        </div>
      </aside>

      {/* Overlay when sidebar is open */}
      {!isCollapsed && (
        <div
          className="fixed inset-0 bg-black/20 backdrop-blur-sm z-30"
          onClick={() => setIsCollapsed(true)}
        />
      )}

      {/* Original Collapsed Sidebar (when sidebar is collapsed) */}
      <aside
        className={`bg-panel-bg border border-gray-700/50 responsive-border-radius sidebar-left flex flex-col items-center justify-between transition-all duration-300 ${
          isCollapsed ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}
        style={{ padding: 'calc(var(--base-spacing) * 0.75)' }}
      >
        <div className="flex flex-col items-center w-full" style={{ gap: 'calc(var(--base-gap) * 0.75)' }}>
          <div
            className="bg-gradient-to-br from-cyan-400 to-fuchsia-500 rounded-full flex items-center justify-center text-white"
            style={{
              width: 'calc(var(--base-icon-size) * 1.25)',
              height: 'calc(var(--base-icon-size) * 1.25)'
            }}
          >
            <ShieldCheckIcon
              style={{
                width: 'calc(var(--base-icon-size) * 1)',
                height: 'calc(var(--base-icon-size) * 1)'
              }}
            />
          </div>
          <div className="w-full border-t border-gray-700"></div>
          {navItems.slice(0, 3).map(item => (
            <NavItem
              key={item.label}
              icon={item.icon}
              label={item.label}
              active={activeView === item.id && !activeApp}
              onClick={() => {
                setActiveView(item.id);
                if (setActiveApp) setActiveApp('');
              }}
            />
          ))}
        </div>
        <div className="flex flex-col items-center gap-4 w-full">
          {navItems.slice(3).map(item => (
            <NavItem
              key={item.label}
              icon={item.icon}
              label={item.label}
              active={activeView === item.id && item.label === 'Settings' && !activeApp}
              onClick={() => {
                setActiveView(item.id);
                if (setActiveApp) setActiveApp('');
              }}
            />
          ))}
        </div>
      </aside>
    </>
  );
};

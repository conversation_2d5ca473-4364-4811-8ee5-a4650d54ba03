
import React from 'react';
import { HomeIcon, ChartBarIcon, GridIcon, SettingsIcon, UserCircleIcon, ShieldCheckIcon } from './icons';

interface NavItemProps {
    icon: React.ReactNode;
    label: string;
    active: boolean;
    onClick: () => void;
}

const NavItem = ({ icon, label, active, onClick }: NavItemProps) => (
    <button
        onClick={onClick}
        aria-label={label}
        aria-pressed={active}
        className={`w-full flex items-center justify-center responsive-border-radius transition-colors duration-200 relative ${
            active
            ? 'bg-brand-cyan/20 text-white'
            : 'text-gray-400 hover:bg-gray-700/50 hover:text-white'
        }`}
        style={{
          padding: 'calc(var(--base-spacing) * 0.5)',
          height: 'var(--base-button-height)'
        }}
    >
    {active && (
      <span
        className="absolute left-0 top-1/2 -translate-y-1/2 bg-brand-cyan rounded-r-full"
        style={{
          height: 'calc(var(--base-button-height) * 0.6)',
          width: '3px'
        }}
      />
    )}
    {icon}
  </button>
);

interface SidebarProps {
    activeView: string;
    setActiveView: (view: string) => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ activeView, setActiveView }) => {
  const navItems = [
    {
      id: 'Dashboard',
      label: 'Home',
      icon: <HomeIcon className="responsive-icon" />
    },
    {
      id: 'Analytic',
      label: 'Analytics',
      icon: <ChartBarIcon className="responsive-icon" />
    },
    {
      id: 'Tool',
      label: 'Modules',
      icon: <GridIcon className="responsive-icon" />
    },
    {
      id: 'Setting',
      label: 'Profile',
      icon: <UserCircleIcon className="responsive-icon" />
    },
    {
      id: 'Setting',
      label: 'Settings',
      icon: <SettingsIcon className="responsive-icon" />
    },
  ];

  return (
    <aside className="bg-panel-bg border border-gray-700/50 responsive-border-radius sidebar-left flex flex-col items-center justify-between" style={{ padding: 'calc(var(--base-spacing) * 0.75)' }}>
      <div className="flex flex-col items-center w-full" style={{ gap: 'calc(var(--base-gap) * 0.75)' }}>
         <div
           className="bg-gradient-to-br from-cyan-400 to-fuchsia-500 rounded-full flex items-center justify-center text-white"
           style={{
             width: 'calc(var(--base-icon-size) * 1.25)',
             height: 'calc(var(--base-icon-size) * 1.25)'
           }}
         >
            <ShieldCheckIcon
              style={{
                width: 'calc(var(--base-icon-size) * 1)',
                height: 'calc(var(--base-icon-size) * 1)'
              }}
            />
        </div>
        <div className="w-full border-t border-gray-700"></div>
        {navItems.slice(0, 3).map(item => (
            <NavItem 
                key={item.label} 
                icon={item.icon} 
                label={item.label}
                active={activeView === item.id}
                onClick={() => setActiveView(item.id)} 
            />
        ))}
      </div>
      <div className="flex flex-col items-center gap-4 w-full">
        {navItems.slice(3).map(item => (
            <NavItem 
                key={item.label} 
                icon={item.icon} 
                label={item.label}
                active={activeView === item.id && item.label === 'Settings'}
                onClick={() => setActiveView(item.id)} 
            />
        ))}
      </div>
    </aside>
  );
};

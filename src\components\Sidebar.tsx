
import React from 'react';
import { HomeIcon, ChartBarIcon, GridIcon, SettingsIcon, UserCircleIcon, ShieldCheckIcon } from './icons';

interface NavItemProps {
    icon: React.ReactNode;
    label: string;
    active: boolean;
    onClick: () => void;
}

const NavItem = ({ icon, label, active, onClick }: NavItemProps) => (
    <button
        onClick={onClick}
        aria-label={label}
        aria-pressed={active}
        className={`w-full flex items-center justify-center p-3 rounded-lg transition-colors duration-200 relative ${
            active 
            ? 'bg-brand-cyan/20 text-white' 
            : 'text-gray-400 hover:bg-gray-700/50 hover:text-white'
        }`}
    >
    {active && <span className="absolute left-0 top-1/2 -translate-y-1/2 h-5 w-1 bg-brand-cyan rounded-r-full"></span>}
    {icon}
  </button>
);

interface SidebarProps {
    activeView: string;
    setActiveView: (view: string) => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ activeView, setActiveView }) => {
  const navItems = [
    { id: 'Dashboard', label: 'Home', icon: <HomeIcon className="w-6 h-6" /> },
    { id: 'Analytic', label: 'Analytics', icon: <ChartBarIcon className="w-6 h-6" /> },
    { id: 'Tool', label: 'Modules', icon: <GridIcon className="w-6 h-6" /> },
    { id: 'Setting', label: 'Profile', icon: <UserCircleIcon className="w-6 h-6" /> },
    { id: 'Setting', label: 'Settings', icon: <SettingsIcon className="w-6 h-6" /> },
  ];

  return (
    <aside className="bg-panel-bg border border-gray-700/50 rounded-xl p-2 flex flex-col items-center justify-between">
      <div className="flex flex-col items-center gap-4 w-full">
         <div className="w-10 h-10 bg-gradient-to-br from-cyan-400 to-fuchsia-500 rounded-full flex items-center justify-center text-white">
            <ShieldCheckIcon className="w-7 h-7" />
        </div>
        <div className="w-full border-t border-gray-700"></div>
        {navItems.slice(0, 3).map(item => (
            <NavItem 
                key={item.label} 
                icon={item.icon} 
                label={item.label}
                active={activeView === item.id}
                onClick={() => setActiveView(item.id)} 
            />
        ))}
      </div>
      <div className="flex flex-col items-center gap-4 w-full">
        {navItems.slice(3).map(item => (
            <NavItem 
                key={item.label} 
                icon={item.icon} 
                label={item.label}
                active={activeView === item.id && item.label === 'Settings'}
                onClick={() => setActiveView(item.id)} 
            />
        ))}
      </div>
    </aside>
  );
};

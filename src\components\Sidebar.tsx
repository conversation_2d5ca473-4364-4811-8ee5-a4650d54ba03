import React, { useState } from 'react';
import { HomeIcon, ChartBarIcon, GridIcon, SettingsIcon, UserCircleIcon, ShieldCheckIcon, Bars3Icon } from './icons';

interface NavItemProps {
    icon: React.ReactNode;
    label: string;
    active: boolean;
    onClick: () => void;
}

const NavItem = ({ icon, label, active, onClick }: NavItemProps) => (
    <button
        onClick={onClick}
        aria-label={label}
        aria-pressed={active}
        className={`w-full flex items-center justify-center responsive-border-radius transition-colors duration-200 relative ${
            active
            ? 'bg-brand-cyan/20 text-white'
            : 'text-gray-400 hover:bg-gray-700/50 hover:text-white'
        }`}
        style={{
          padding: 'calc(var(--base-spacing) * 0.5)',
          height: 'var(--base-button-height)'
        }}
    >
    {active && (
      <span
        className="absolute left-0 top-1/2 -translate-y-1/2 bg-brand-cyan rounded-r-full"
        style={{
          height: 'calc(var(--base-button-height) * 0.6)',
          width: '3px'
        }}
      />
    )}
    {icon}
  </button>
);

interface SidebarProps {
    activeView: string;
    setActiveView: (view: string) => void;
    activeApp?: string;
    setActiveApp?: (app: string) => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ 
  activeView, 
  setActiveView, 
  activeApp, 
  setActiveApp 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Main function buttons for the expanded sidebar
  const functionButtons = [
    {
      id: 'gamification',
      label: 'Gamification',
      icon: '🎮',
      description: 'Learning Games'
    },
    {
      id: 'coder',
      label: 'Coder',
      icon: '💻',
      description: 'IDE View'
    },
    {
      id: 'media',
      label: 'Media',
      icon: '🎥',
      description: 'Media Hub'
    },
    {
      id: 'studio',
      label: 'Studio',
      icon: '🧑‍🎨',
      description: 'Creative Workspace'
    }
  ];

  // Original navigation items
  const navItems = [
    {
      id: 'Dashboard',
      label: 'Home',
      icon: <HomeIcon className="responsive-icon" />
    },
    {
      id: 'Analytic',
      label: 'Analytics',
      icon: <ChartBarIcon className="responsive-icon" />
    },
    {
      id: 'Tool',
      label: 'Modules',
      icon: <GridIcon className="responsive-icon" />
    },
    {
      id: 'Setting',
      label: 'Profile',
      icon: <UserCircleIcon className="responsive-icon" />
    },
    {
      id: 'Setting',
      label: 'Settings',
      icon: <SettingsIcon className="responsive-icon" />
    },
  ];

  const handleFunctionButtonClick = (appId: string) => {
    if (setActiveApp) {
      setActiveApp(appId);
    }
    setIsExpanded(false); // Auto-collapse after selection
  };

  return (
    <>
      {/* Expanded Sidebar Overlay */}
      {isExpanded && (
        <>
          <div 
            className="fixed inset-0 bg-black/20 backdrop-blur-sm z-30"
            onClick={() => setIsExpanded(false)}
          />
          <div className="fixed left-20 top-0 h-full bg-panel-bg border border-gray-700/50 backdrop-blur-xl z-40 p-6" 
               style={{ width: 'calc(240px * var(--content-scale))' }}>
            <div className="mb-6">
              <h2 className="text-white font-semibold text-lg mb-2">Functions</h2>
              <p className="text-gray-400 text-sm">Choose your workspace</p>
            </div>
            
            <div className="space-y-3">
              {functionButtons.map((button) => (
                <button
                  key={button.id}
                  onClick={() => handleFunctionButtonClick(button.id)}
                  className={`w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200 ${
                    activeApp === button.id
                      ? 'bg-blue-500/20 text-blue-400 border border-blue-400/30'
                      : 'text-gray-300 hover:text-white hover:bg-gray-800/60'
                  }`}
                >
                  <span className="text-xl">{button.icon}</span>
                  <div className="text-left">
                    <div className="font-medium">{button.label}</div>
                    <div className="text-xs text-gray-400">{button.description}</div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </>
      )}

      {/* Main Sidebar */}
      <aside className="bg-panel-bg border border-gray-700/50 responsive-border-radius sidebar-left flex flex-col items-center justify-between" style={{ padding: 'calc(var(--base-spacing) * 0.75)' }}>
        <div className="flex flex-col items-center w-full" style={{ gap: 'calc(var(--base-gap) * 0.75)' }}>
          {/* Logo/Toggle Button */}
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="bg-gradient-to-br from-cyan-400 to-fuchsia-500 rounded-full flex items-center justify-center text-white hover:scale-105 transition-transform duration-200"
            style={{
              width: 'calc(var(--base-icon-size) * 1.25)',
              height: 'calc(var(--base-icon-size) * 1.25)'
            }}
          >
            {isExpanded ? (
              <Bars3Icon className="w-6 h-6" />
            ) : (
              <ShieldCheckIcon className="w-6 h-6" />
            )}
          </button>
          
          <div className="w-full border-t border-gray-700"></div>
          
          {navItems.slice(0, 3).map(item => (
            <NavItem 
              key={item.label} 
              icon={item.icon} 
              label={item.label}
              active={activeView === item.id && !activeApp}
              onClick={() => {
                setActiveView(item.id);
                if (setActiveApp) setActiveApp('');
              }} 
            />
          ))}
        </div>
        
        <div className="flex flex-col items-center gap-4 w-full">
          {navItems.slice(3).map(item => (
            <NavItem 
              key={item.label} 
              icon={item.icon} 
              label={item.label}
              active={activeView === item.id && item.label === 'Settings' && !activeApp}
              onClick={() => {
                setActiveView(item.id);
                if (setActiveApp) setActiveApp('');
              }} 
            />
          ))}
        </div>
      </aside>
    </>
  );
};

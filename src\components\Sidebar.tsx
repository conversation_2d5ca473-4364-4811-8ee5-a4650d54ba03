import React, { useState } from 'react';
import { HomeIcon, ShieldCheckIcon } from './icons';

interface SidebarProps {
    activeView: string;
    setActiveView: (view: string) => void;
    activeApp?: string;
    setActiveApp?: (app: string) => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ 
  activeView, 
  setActiveView, 
  activeApp, 
  setActiveApp 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Main function buttons
  const functionButtons = [
    {
      id: 'gamification',
      label: 'Gamification',
      icon: '🎮',
      description: 'Learning Games'
    },
    {
      id: 'coder',
      label: 'Coder',
      icon: '💻',
      description: 'IDE View'
    },
    {
      id: 'studio',
      label: 'Studio',
      icon: '🧑‍🎨',
      description: 'Creative Workspace'
    },
    {
      id: 'design',
      label: 'Design',
      icon: '🎨',
      description: 'Design Tools'
    }
  ];



  const handleFunctionButtonClick = (appId: string) => {
    if (setActiveApp) {
      setActiveApp(appId);
    }
    setIsExpanded(false); // Auto-collapse after selection
  };

  return (
    <>
      {/* Expanded Sidebar Overlay */}
      {isExpanded && (
        <>
          <div 
            className="fixed inset-0 bg-black/20 backdrop-blur-sm z-30"
            onClick={() => setIsExpanded(false)}
          />
          <div className="fixed left-20 top-0 h-full bg-panel-bg border border-gray-700/50 backdrop-blur-xl z-40 p-6" 
               style={{ width: 'calc(240px * var(--content-scale))' }}>
            <div className="mb-6">
              <h2 className="text-white font-semibold text-lg mb-2">Functions</h2>
              <p className="text-gray-400 text-sm">Choose your workspace</p>
            </div>
            
            <div className="space-y-3">
              {functionButtons.map((button) => (
                <button
                  key={button.id}
                  onClick={() => handleFunctionButtonClick(button.id)}
                  className={`w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200 ${
                    activeApp === button.id
                      ? 'bg-blue-500/20 text-blue-400 border border-blue-400/30'
                      : 'text-gray-300 hover:text-white hover:bg-gray-800/60'
                  }`}
                >
                  <span className="text-xl">{button.icon}</span>
                  <div className="text-left">
                    <div className="font-medium">{button.label}</div>
                    <div className="text-xs text-gray-400">{button.description}</div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </>
      )}

      {/* Main Sidebar */}
      <aside className="bg-panel-bg border border-gray-700/50 responsive-border-radius sidebar-left flex flex-col items-center justify-between" style={{ padding: 'calc(var(--base-spacing) * 0.75)' }}>
        <div className="flex flex-col items-center w-full" style={{ gap: 'calc(var(--base-gap) * 0.75)' }}>
          {/* Logo */}
          <div
            className="bg-gradient-to-br from-cyan-400 to-fuchsia-500 rounded-full flex items-center justify-center text-white"
            style={{
              width: 'calc(var(--base-icon-size) * 1.25)',
              height: 'calc(var(--base-icon-size) * 1.25)'
            }}
          >
            <ShieldCheckIcon className="w-6 h-6" />
          </div>

          <div className="w-full border-t border-gray-700"></div>

          {/* Function Buttons */}
          {functionButtons.map((button) => (
            <button
              key={button.id}
              onClick={() => {
                if (setActiveApp) {
                  setActiveApp(button.id);
                }
              }}
              aria-label={button.label}
              className={`w-full flex items-center justify-center responsive-border-radius transition-colors duration-200 relative ${
                activeApp === button.id
                  ? 'bg-brand-cyan/20 text-white'
                  : 'text-gray-400 hover:bg-gray-700/50 hover:text-white'
              }`}
              style={{
                padding: 'calc(var(--base-spacing) * 0.5)',
                height: 'var(--base-button-height)'
              }}
            >
              {activeApp === button.id && (
                <span
                  className="absolute left-0 top-1/2 -translate-y-1/2 bg-brand-cyan rounded-r-full"
                  style={{
                    height: 'calc(var(--base-button-height) * 0.6)',
                    width: '3px'
                  }}
                />
              )}
              <span className="text-xl">{button.icon}</span>
            </button>
          ))}
        </div>

        <div className="flex flex-col items-center gap-4 w-full">
          {/* Dashboard button at bottom */}
          <button
            onClick={() => {
              setActiveView('Dashboard');
              if (setActiveApp) setActiveApp('');
            }}
            aria-label="Dashboard"
            className={`w-full flex items-center justify-center responsive-border-radius transition-colors duration-200 relative ${
              activeView === 'Dashboard' && !activeApp
                ? 'bg-brand-cyan/20 text-white'
                : 'text-gray-400 hover:bg-gray-700/50 hover:text-white'
            }`}
            style={{
              padding: 'calc(var(--base-spacing) * 0.5)',
              height: 'var(--base-button-height)'
            }}
          >
            {activeView === 'Dashboard' && !activeApp && (
              <span
                className="absolute left-0 top-1/2 -translate-y-1/2 bg-brand-cyan rounded-r-full"
                style={{
                  height: 'calc(var(--base-button-height) * 0.6)',
                  width: '3px'
                }}
              />
            )}
            <HomeIcon className="responsive-icon" />
          </button>
        </div>
      </aside>
    </>
  );
};

{"name": "@khanacademy/perseus", "description": "Core Perseus API (includes renderers and widgets)", "author": "Khan Academy", "license": "MIT", "version": "65.8.0", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/Khan/perseus.git", "directory": "packages/perseus"}, "bugs": {"url": "https://github.com/Khan/perseus/issues"}, "module": "dist/es/index.js", "main": "dist/index.js", "source": "src/index.ts", "exports": {".": {"import": "./dist/es/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts", "source": "./src/index.ts"}, "./strings": {"import": "./dist/es/strings.js", "require": "./dist/strings.js", "types": "./dist/strings.d.ts", "source": "./src/strings.ts"}, "./styles.css": "./dist/index.css"}, "files": ["dist"], "scripts": {"prepublishOnly": "../../utils/package-pre-publish-check.sh"}, "dependencies": {"@khanacademy/kas": "workspace:*", "@khanacademy/keypad-context": "workspace:*", "@khanacademy/kmath": "workspace:*", "@khanacademy/math-input": "workspace:*", "@khanacademy/perseus-core": "workspace:*", "@khanacademy/perseus-linter": "workspace:*", "@khanacademy/perseus-score": "workspace:*", "@khanacademy/perseus-utils": "workspace:*", "@khanacademy/pure-markdown": "workspace:*", "@khanacademy/simple-markdown": "workspace:*", "@use-gesture/react": "^10.2.27", "mafs": "0.19.0", "tiny-invariant": "catalog:prodDeps", "uuid": "^10.0.0"}, "devDependencies": {"@khanacademy/wonder-blocks-banner": "catalog:devDeps", "@khanacademy/wonder-blocks-button": "catalog:devDeps", "@khanacademy/wonder-blocks-clickable": "catalog:devDeps", "@khanacademy/wonder-blocks-core": "catalog:devDeps", "@khanacademy/wonder-blocks-data": "catalog:devDeps", "@khanacademy/wonder-blocks-dropdown": "catalog:devDeps", "@khanacademy/wonder-blocks-form": "catalog:devDeps", "@khanacademy/wonder-blocks-icon-button": "catalog:devDeps", "@khanacademy/wonder-blocks-icon": "catalog:devDeps", "@khanacademy/wonder-blocks-labeled-field": "catalog:devDeps", "@khanacademy/wonder-blocks-layout": "catalog:devDeps", "@khanacademy/wonder-blocks-link": "catalog:devDeps", "@khanacademy/wonder-blocks-pill": "catalog:devDeps", "@khanacademy/wonder-blocks-popover": "catalog:devDeps", "@khanacademy/wonder-blocks-progress-spinner": "catalog:devDeps", "@khanacademy/wonder-blocks-switch": "catalog:devDeps", "@khanacademy/wonder-blocks-tokens": "catalog:devDeps", "@khanacademy/wonder-blocks-tooltip": "catalog:devDeps", "@khanacademy/wonder-blocks-typography": "catalog:devDeps", "@khanacademy/wonder-stuff-core": "catalog:devDeps", "@phosphor-icons/core": "catalog:devDeps", "@popperjs/core": "catalog:devDeps", "aphrodite": "catalog:devDeps", "classnames": "catalog:devDeps", "csstype": "^3.1.3", "intersection-observer": "^0.12.0", "jquery": "catalog:devDeps", "lodash.debounce": "^4.0.8", "perseus-build-settings": "workspace:*", "prop-types": "catalog:devDeps", "raphael": "workspace:*", "react": "catalog:devDeps", "react-dom": "catalog:devDeps", "react-popper": "^2.2.5", "underscore": "catalog:devDeps"}, "peerDependencies": {"@khanacademy/wonder-blocks-banner": "catalog:peerDeps", "@khanacademy/wonder-blocks-button": "catalog:peerDeps", "@khanacademy/wonder-blocks-clickable": "catalog:peerDeps", "@khanacademy/wonder-blocks-core": "catalog:peerDeps", "@khanacademy/wonder-blocks-data": "catalog:peerDeps", "@khanacademy/wonder-blocks-dropdown": "catalog:peerDeps", "@khanacademy/wonder-blocks-form": "catalog:peerDeps", "@khanacademy/wonder-blocks-icon": "catalog:peerDeps", "@khanacademy/wonder-blocks-icon-button": "catalog:peerDeps", "@khanacademy/wonder-blocks-labeled-field": "catalog:peerDeps", "@khanacademy/wonder-blocks-layout": "catalog:peerDeps", "@khanacademy/wonder-blocks-link": "catalog:peerDeps", "@khanacademy/wonder-blocks-pill": "catalog:peerDeps", "@khanacademy/wonder-blocks-popover": "catalog:peerDeps", "@khanacademy/wonder-blocks-progress-spinner": "catalog:peerDeps", "@khanacademy/wonder-blocks-switch": "catalog:peerDeps", "@khanacademy/wonder-blocks-tokens": "catalog:peerDeps", "@khanacademy/wonder-blocks-tooltip": "catalog:peerDeps", "@khanacademy/wonder-blocks-typography": "catalog:peerDeps", "@khanacademy/wonder-stuff-core": "catalog:peerDeps", "@phosphor-icons/core": "catalog:peerDeps", "@popperjs/core": "catalog:peerDeps", "aphrodite": "catalog:peerDeps", "classnames": "catalog:peerDeps", "intersection-observer": "^0.12.0", "jquery": "catalog:peerDeps", "lodash.debounce": "^4.0.8", "prop-types": "catalog:peerDeps", "react": "catalog:peerDeps", "react-dom": "catalog:peerDeps", "react-popper": "^2.2.5", "underscore": "catalog:peerDeps"}, "keywords": []}
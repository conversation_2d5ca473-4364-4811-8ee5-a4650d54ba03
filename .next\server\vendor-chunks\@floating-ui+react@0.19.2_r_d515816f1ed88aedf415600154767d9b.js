"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@floating-ui+react@0.19.2_r_d515816f1ed88aedf415600154767d9b";
exports.ids = ["vendor-chunks/@floating-ui+react@0.19.2_r_d515816f1ed88aedf415600154767d9b"];
exports.modules = {

/***/ "./node_modules/.pnpm/@floating-ui+react@0.19.2_r_d515816f1ed88aedf415600154767d9b/node_modules/@floating-ui/react/dist/floating-ui.react.esm.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@floating-ui+react@0.19.2_r_d515816f1ed88aedf415600154767d9b/node_modules/@floating-ui/react/dist/floating-ui.react.esm.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FloatingDelayGroup: () => (/* binding */ FloatingDelayGroup),\n/* harmony export */   FloatingFocusManager: () => (/* binding */ FloatingFocusManager),\n/* harmony export */   FloatingNode: () => (/* binding */ FloatingNode),\n/* harmony export */   FloatingOverlay: () => (/* binding */ FloatingOverlay),\n/* harmony export */   FloatingPortal: () => (/* binding */ FloatingPortal),\n/* harmony export */   FloatingTree: () => (/* binding */ FloatingTree),\n/* harmony export */   arrow: () => (/* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.arrow),\n/* harmony export */   autoPlacement: () => (/* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.autoPlacement),\n/* harmony export */   autoUpdate: () => (/* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.autoUpdate),\n/* harmony export */   computePosition: () => (/* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.computePosition),\n/* harmony export */   detectOverflow: () => (/* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.detectOverflow),\n/* harmony export */   flip: () => (/* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.flip),\n/* harmony export */   getOverflowAncestors: () => (/* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.getOverflowAncestors),\n/* harmony export */   hide: () => (/* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.hide),\n/* harmony export */   inline: () => (/* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.inline),\n/* harmony export */   inner: () => (/* binding */ inner),\n/* harmony export */   limitShift: () => (/* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.limitShift),\n/* harmony export */   offset: () => (/* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.offset),\n/* harmony export */   platform: () => (/* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.platform),\n/* harmony export */   safePolygon: () => (/* binding */ safePolygon),\n/* harmony export */   shift: () => (/* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.shift),\n/* harmony export */   size: () => (/* reexport safe */ _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.size),\n/* harmony export */   useClick: () => (/* binding */ useClick),\n/* harmony export */   useDelayGroup: () => (/* binding */ useDelayGroup),\n/* harmony export */   useDelayGroupContext: () => (/* binding */ useDelayGroupContext),\n/* harmony export */   useDismiss: () => (/* binding */ useDismiss),\n/* harmony export */   useFloating: () => (/* binding */ useFloating),\n/* harmony export */   useFloatingNodeId: () => (/* binding */ useFloatingNodeId),\n/* harmony export */   useFloatingParentNodeId: () => (/* binding */ useFloatingParentNodeId),\n/* harmony export */   useFloatingPortalNode: () => (/* binding */ useFloatingPortalNode),\n/* harmony export */   useFloatingTree: () => (/* binding */ useFloatingTree),\n/* harmony export */   useFocus: () => (/* binding */ useFocus),\n/* harmony export */   useHover: () => (/* binding */ useHover),\n/* harmony export */   useId: () => (/* binding */ useId),\n/* harmony export */   useInnerOffset: () => (/* binding */ useInnerOffset),\n/* harmony export */   useInteractions: () => (/* binding */ useInteractions),\n/* harmony export */   useListNavigation: () => (/* binding */ useListNavigation),\n/* harmony export */   useMergeRefs: () => (/* binding */ useMergeRefs),\n/* harmony export */   useRole: () => (/* binding */ useRole),\n/* harmony export */   useTransitionStatus: () => (/* binding */ useTransitionStatus),\n/* harmony export */   useTransitionStyles: () => (/* binding */ useTransitionStyles),\n/* harmony export */   useTypeahead: () => (/* binding */ useTypeahead)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! aria-hidden */ \"./node_modules/.pnpm/aria-hidden@1.2.6/node_modules/aria-hidden/dist/es5/index.js\");\n/* harmony import */ var tabbable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tabbable */ \"./node_modules/.pnpm/tabbable@6.2.0/node_modules/tabbable/dist/index.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"react-dom\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @floating-ui/react-dom */ \"./node_modules/.pnpm/@floating-ui+react-dom@1.3._487be64c6d1329e3a2c6dd328a941ee1/node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.esm.js\");\n\n\n\n\n\n\n\n\nvar index = typeof document !== 'undefined' ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\nlet serverHandoffComplete = false;\nlet count = 0;\nconst genId = () => \"floating-ui-\" + count++;\nfunction useFloatingId() {\n  const [id, setId] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => serverHandoffComplete ? genId() : undefined);\n  index(() => {\n    if (id == null) {\n      setId(genId());\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (!serverHandoffComplete) {\n      serverHandoffComplete = true;\n    }\n  }, []);\n  return id;\n}\n\n// `toString()` prevents bundlers from trying to `import { useId } from 'react'`\nconst useReactId = react__WEBPACK_IMPORTED_MODULE_0__[/*#__PURE__*/'useId'.toString()];\n\n/**\n * Uses React 18's built-in `useId()` when available, or falls back to a\n * slightly less performant (requiring a double render) implementation for\n * earlier React versions.\n * @see https://floating-ui.com/docs/useId\n */\nconst useId = useReactId || useFloatingId;\n\nfunction createPubSub() {\n  const map = new Map();\n  return {\n    emit(event, data) {\n      var _map$get;\n      (_map$get = map.get(event)) == null ? void 0 : _map$get.forEach(handler => handler(data));\n    },\n    on(event, listener) {\n      map.set(event, [...(map.get(event) || []), listener]);\n    },\n    off(event, listener) {\n      map.set(event, (map.get(event) || []).filter(l => l !== listener));\n    }\n  };\n}\n\nconst FloatingNodeContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst FloatingTreeContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useFloatingParentNodeId = () => {\n  var _React$useContext;\n  return ((_React$useContext = react__WEBPACK_IMPORTED_MODULE_0__.useContext(FloatingNodeContext)) == null ? void 0 : _React$useContext.id) || null;\n};\nconst useFloatingTree = () => react__WEBPACK_IMPORTED_MODULE_0__.useContext(FloatingTreeContext);\n\n/**\n * Registers a node into the floating tree, returning its id.\n */\nconst useFloatingNodeId = customParentId => {\n  const id = useId();\n  const tree = useFloatingTree();\n  const reactParentId = useFloatingParentNodeId();\n  const parentId = customParentId || reactParentId;\n  index(() => {\n    const node = {\n      id,\n      parentId\n    };\n    tree == null ? void 0 : tree.addNode(node);\n    return () => {\n      tree == null ? void 0 : tree.removeNode(node);\n    };\n  }, [tree, id, parentId]);\n  return id;\n};\n\n/**\n * Provides parent node context for nested floating elements.\n * @see https://floating-ui.com/docs/FloatingTree\n */\nconst FloatingNode = _ref => {\n  let {\n    children,\n    id\n  } = _ref;\n  const parentId = useFloatingParentNodeId();\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(FloatingNodeContext.Provider, {\n    value: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n      id,\n      parentId\n    }), [id, parentId])\n  }, children);\n};\n\n/**\n * Provides context for nested floating elements when they are not children of\n * each other on the DOM (i.e. portalled to a common node, rather than their\n * respective parent).\n * @see https://floating-ui.com/docs/FloatingTree\n */\nconst FloatingTree = _ref2 => {\n  let {\n    children\n  } = _ref2;\n  const nodesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef([]);\n  const addNode = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(node => {\n    nodesRef.current = [...nodesRef.current, node];\n  }, []);\n  const removeNode = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(node => {\n    nodesRef.current = nodesRef.current.filter(n => n !== node);\n  }, []);\n  const events = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => createPubSub())[0];\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(FloatingTreeContext.Provider, {\n    value: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n      nodesRef,\n      addNode,\n      removeNode,\n      events\n    }), [nodesRef, addNode, removeNode, events])\n  }, children);\n};\n\nfunction getDocument(node) {\n  return (node == null ? void 0 : node.ownerDocument) || document;\n}\n\n// Avoid Chrome DevTools blue warning.\nfunction getPlatform() {\n  const uaData = navigator.userAgentData;\n  if (uaData != null && uaData.platform) {\n    return uaData.platform;\n  }\n  return navigator.platform;\n}\nfunction getUserAgent() {\n  const uaData = navigator.userAgentData;\n  if (uaData && Array.isArray(uaData.brands)) {\n    return uaData.brands.map(_ref => {\n      let {\n        brand,\n        version\n      } = _ref;\n      return brand + \"/\" + version;\n    }).join(' ');\n  }\n  return navigator.userAgent;\n}\n\nfunction getWindow(value) {\n  return getDocument(value).defaultView || window;\n}\nfunction isElement(value) {\n  return value ? value instanceof getWindow(value).Element : false;\n}\nfunction isHTMLElement(value) {\n  return value ? value instanceof getWindow(value).HTMLElement : false;\n}\nfunction isShadowRoot(node) {\n  // Browsers without `ShadowRoot` support\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  const OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\n// License: https://github.com/adobe/react-spectrum/blob/b35d5c02fe900badccd0cf1a8f23bb593419f238/packages/@react-aria/utils/src/isVirtualEvent.ts\nfunction isVirtualClick(event) {\n  if (event.mozInputSource === 0 && event.isTrusted) {\n    return true;\n  }\n  const androidRe = /Android/i;\n  if ((androidRe.test(getPlatform()) || androidRe.test(getUserAgent())) && event.pointerType) {\n    return event.type === 'click' && event.buttons === 1;\n  }\n  return event.detail === 0 && !event.pointerType;\n}\nfunction isVirtualPointerEvent(event) {\n  return event.width === 0 && event.height === 0 || event.width === 1 && event.height === 1 && event.pressure === 0 && event.detail === 0 && event.pointerType !== 'mouse' ||\n  // iOS VoiceOver returns 0.333• for width/height.\n  event.width < 1 && event.height < 1 && event.pressure === 0 && event.detail === 0;\n}\nfunction isSafari() {\n  // Chrome DevTools does not complain about navigator.vendor\n  return /apple/i.test(navigator.vendor);\n}\nfunction isMac() {\n  return getPlatform().toLowerCase().startsWith('mac') && !navigator.maxTouchPoints;\n}\nfunction isMouseLikePointerType(pointerType, strict) {\n  // On some Linux machines with Chromium, mouse inputs return a `pointerType`\n  // of \"pen\": https://github.com/floating-ui/floating-ui/issues/2015\n  const values = ['mouse', 'pen'];\n  if (!strict) {\n    values.push('', undefined);\n  }\n  return values.includes(pointerType);\n}\n\nfunction useLatestRef(value) {\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(value);\n  index(() => {\n    ref.current = value;\n  });\n  return ref;\n}\n\nconst safePolygonIdentifier = 'data-floating-ui-safe-polygon';\nfunction getDelay(value, prop, pointerType) {\n  if (pointerType && !isMouseLikePointerType(pointerType)) {\n    return 0;\n  }\n  if (typeof value === 'number') {\n    return value;\n  }\n  return value == null ? void 0 : value[prop];\n}\n/**\n * Opens the floating element while hovering over the reference element, like\n * CSS `:hover`.\n * @see https://floating-ui.com/docs/useHover\n */\nconst useHover = function (context, _temp) {\n  let {\n    enabled = true,\n    delay = 0,\n    handleClose = null,\n    mouseOnly = false,\n    restMs = 0,\n    move = true\n  } = _temp === void 0 ? {} : _temp;\n  const {\n    open,\n    onOpenChange,\n    dataRef,\n    events,\n    elements: {\n      domReference,\n      floating\n    },\n    refs\n  } = context;\n  const tree = useFloatingTree();\n  const parentId = useFloatingParentNodeId();\n  const handleCloseRef = useLatestRef(handleClose);\n  const delayRef = useLatestRef(delay);\n  const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n  const timeoutRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n  const handlerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n  const restTimeoutRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n  const blockMouseMoveRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n  const performedPointerEventsMutationRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  const unbindMouseMoveRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(() => {});\n  const isHoverOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    var _dataRef$current$open;\n    const type = (_dataRef$current$open = dataRef.current.openEvent) == null ? void 0 : _dataRef$current$open.type;\n    return (type == null ? void 0 : type.includes('mouse')) && type !== 'mousedown';\n  }, [dataRef]);\n\n  // When dismissing before opening, clear the delay timeouts to cancel it\n  // from showing.\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (!enabled) {\n      return;\n    }\n    function onDismiss() {\n      clearTimeout(timeoutRef.current);\n      clearTimeout(restTimeoutRef.current);\n      blockMouseMoveRef.current = true;\n    }\n    events.on('dismiss', onDismiss);\n    return () => {\n      events.off('dismiss', onDismiss);\n    };\n  }, [enabled, events]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (!enabled || !handleCloseRef.current || !open) {\n      return;\n    }\n    function onLeave() {\n      if (isHoverOpen()) {\n        onOpenChange(false);\n      }\n    }\n    const html = getDocument(floating).documentElement;\n    html.addEventListener('mouseleave', onLeave);\n    return () => {\n      html.removeEventListener('mouseleave', onLeave);\n    };\n  }, [floating, open, onOpenChange, enabled, handleCloseRef, dataRef, isHoverOpen]);\n  const closeWithDelay = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (runElseBranch) {\n    if (runElseBranch === void 0) {\n      runElseBranch = true;\n    }\n    const closeDelay = getDelay(delayRef.current, 'close', pointerTypeRef.current);\n    if (closeDelay && !handlerRef.current) {\n      clearTimeout(timeoutRef.current);\n      timeoutRef.current = setTimeout(() => onOpenChange(false), closeDelay);\n    } else if (runElseBranch) {\n      clearTimeout(timeoutRef.current);\n      onOpenChange(false);\n    }\n  }, [delayRef, onOpenChange]);\n  const cleanupMouseMoveHandler = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    unbindMouseMoveRef.current();\n    handlerRef.current = undefined;\n  }, []);\n  const clearPointerEvents = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    if (performedPointerEventsMutationRef.current) {\n      const body = getDocument(refs.floating.current).body;\n      body.style.pointerEvents = '';\n      body.removeAttribute(safePolygonIdentifier);\n      performedPointerEventsMutationRef.current = false;\n    }\n  }, [refs]);\n\n  // Registering the mouse events on the reference directly to bypass React's\n  // delegation system. If the cursor was on a disabled element and then entered\n  // the reference (no gap), `mouseenter` doesn't fire in the delegation system.\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (!enabled) {\n      return;\n    }\n    function isClickLikeOpenEvent() {\n      return dataRef.current.openEvent ? ['click', 'mousedown'].includes(dataRef.current.openEvent.type) : false;\n    }\n    function onMouseEnter(event) {\n      clearTimeout(timeoutRef.current);\n      blockMouseMoveRef.current = false;\n      if (mouseOnly && !isMouseLikePointerType(pointerTypeRef.current) || restMs > 0 && getDelay(delayRef.current, 'open') === 0) {\n        return;\n      }\n      dataRef.current.openEvent = event;\n      const openDelay = getDelay(delayRef.current, 'open', pointerTypeRef.current);\n      if (openDelay) {\n        timeoutRef.current = setTimeout(() => {\n          onOpenChange(true);\n        }, openDelay);\n      } else {\n        onOpenChange(true);\n      }\n    }\n    function onMouseLeave(event) {\n      if (isClickLikeOpenEvent()) {\n        return;\n      }\n      unbindMouseMoveRef.current();\n      const doc = getDocument(floating);\n      clearTimeout(restTimeoutRef.current);\n      if (handleCloseRef.current) {\n        // Prevent clearing `onScrollMouseLeave` timeout.\n        if (!open) {\n          clearTimeout(timeoutRef.current);\n        }\n        handlerRef.current = handleCloseRef.current({\n          ...context,\n          tree,\n          x: event.clientX,\n          y: event.clientY,\n          onClose() {\n            clearPointerEvents();\n            cleanupMouseMoveHandler();\n            closeWithDelay();\n          }\n        });\n        const handler = handlerRef.current;\n        doc.addEventListener('mousemove', handler);\n        unbindMouseMoveRef.current = () => {\n          doc.removeEventListener('mousemove', handler);\n        };\n        return;\n      }\n      closeWithDelay();\n    }\n\n    // Ensure the floating element closes after scrolling even if the pointer\n    // did not move.\n    // https://github.com/floating-ui/floating-ui/discussions/1692\n    function onScrollMouseLeave(event) {\n      if (isClickLikeOpenEvent()) {\n        return;\n      }\n      handleCloseRef.current == null ? void 0 : handleCloseRef.current({\n        ...context,\n        tree,\n        x: event.clientX,\n        y: event.clientY,\n        onClose() {\n          clearPointerEvents();\n          cleanupMouseMoveHandler();\n          closeWithDelay();\n        }\n      })(event);\n    }\n    if (isElement(domReference)) {\n      const ref = domReference;\n      open && ref.addEventListener('mouseleave', onScrollMouseLeave);\n      floating == null ? void 0 : floating.addEventListener('mouseleave', onScrollMouseLeave);\n      move && ref.addEventListener('mousemove', onMouseEnter, {\n        once: true\n      });\n      ref.addEventListener('mouseenter', onMouseEnter);\n      ref.addEventListener('mouseleave', onMouseLeave);\n      return () => {\n        open && ref.removeEventListener('mouseleave', onScrollMouseLeave);\n        floating == null ? void 0 : floating.removeEventListener('mouseleave', onScrollMouseLeave);\n        move && ref.removeEventListener('mousemove', onMouseEnter);\n        ref.removeEventListener('mouseenter', onMouseEnter);\n        ref.removeEventListener('mouseleave', onMouseLeave);\n      };\n    }\n  }, [domReference, floating, enabled, context, mouseOnly, restMs, move, closeWithDelay, cleanupMouseMoveHandler, clearPointerEvents, onOpenChange, open, tree, delayRef, handleCloseRef, dataRef]);\n\n  // Block pointer-events of every element other than the reference and floating\n  // while the floating element is open and has a `handleClose` handler. Also\n  // handles nested floating elements.\n  // https://github.com/floating-ui/floating-ui/issues/1722\n  index(() => {\n    var _handleCloseRef$curre;\n    if (!enabled) {\n      return;\n    }\n    if (open && (_handleCloseRef$curre = handleCloseRef.current) != null && _handleCloseRef$curre.__options.blockPointerEvents && isHoverOpen()) {\n      const body = getDocument(floating).body;\n      body.setAttribute(safePolygonIdentifier, '');\n      body.style.pointerEvents = 'none';\n      performedPointerEventsMutationRef.current = true;\n      if (isElement(domReference) && floating) {\n        var _tree$nodesRef$curren, _tree$nodesRef$curren2;\n        const ref = domReference;\n        const parentFloating = tree == null ? void 0 : (_tree$nodesRef$curren = tree.nodesRef.current.find(node => node.id === parentId)) == null ? void 0 : (_tree$nodesRef$curren2 = _tree$nodesRef$curren.context) == null ? void 0 : _tree$nodesRef$curren2.elements.floating;\n        if (parentFloating) {\n          parentFloating.style.pointerEvents = '';\n        }\n        ref.style.pointerEvents = 'auto';\n        floating.style.pointerEvents = 'auto';\n        return () => {\n          ref.style.pointerEvents = '';\n          floating.style.pointerEvents = '';\n        };\n      }\n    }\n  }, [enabled, open, parentId, floating, domReference, tree, handleCloseRef, dataRef, isHoverOpen]);\n  index(() => {\n    if (!open) {\n      pointerTypeRef.current = undefined;\n      cleanupMouseMoveHandler();\n      clearPointerEvents();\n    }\n  }, [open, cleanupMouseMoveHandler, clearPointerEvents]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    return () => {\n      cleanupMouseMoveHandler();\n      clearTimeout(timeoutRef.current);\n      clearTimeout(restTimeoutRef.current);\n      clearPointerEvents();\n    };\n  }, [enabled, cleanupMouseMoveHandler, clearPointerEvents]);\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    if (!enabled) {\n      return {};\n    }\n    function setPointerRef(event) {\n      pointerTypeRef.current = event.pointerType;\n    }\n    return {\n      reference: {\n        onPointerDown: setPointerRef,\n        onPointerEnter: setPointerRef,\n        onMouseMove() {\n          if (open || restMs === 0) {\n            return;\n          }\n          clearTimeout(restTimeoutRef.current);\n          restTimeoutRef.current = setTimeout(() => {\n            if (!blockMouseMoveRef.current) {\n              onOpenChange(true);\n            }\n          }, restMs);\n        }\n      },\n      floating: {\n        onMouseEnter() {\n          clearTimeout(timeoutRef.current);\n        },\n        onMouseLeave() {\n          events.emit('dismiss', {\n            type: 'mouseLeave',\n            data: {\n              returnFocus: false\n            }\n          });\n          closeWithDelay(false);\n        }\n      }\n    };\n  }, [events, enabled, restMs, open, onOpenChange, closeWithDelay]);\n};\n\nconst FloatingDelayGroupContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n  delay: 0,\n  initialDelay: 0,\n  timeoutMs: 0,\n  currentId: null,\n  setCurrentId: () => {},\n  setState: () => {},\n  isInstantPhase: false\n});\nconst useDelayGroupContext = () => react__WEBPACK_IMPORTED_MODULE_0__.useContext(FloatingDelayGroupContext);\n\n/**\n * Provides context for a group of floating elements that should share a\n * `delay`.\n * @see https://floating-ui.com/docs/FloatingDelayGroup\n */\nconst FloatingDelayGroup = _ref => {\n  let {\n    children,\n    delay,\n    timeoutMs = 0\n  } = _ref;\n  const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer((prev, next) => ({\n    ...prev,\n    ...next\n  }), {\n    delay,\n    timeoutMs,\n    initialDelay: delay,\n    currentId: null,\n    isInstantPhase: false\n  });\n  const initialCurrentIdRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const setCurrentId = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(currentId => {\n    setState({\n      currentId\n    });\n  }, []);\n  index(() => {\n    if (state.currentId) {\n      if (initialCurrentIdRef.current === null) {\n        initialCurrentIdRef.current = state.currentId;\n      } else {\n        setState({\n          isInstantPhase: true\n        });\n      }\n    } else {\n      setState({\n        isInstantPhase: false\n      });\n      initialCurrentIdRef.current = null;\n    }\n  }, [state.currentId]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(FloatingDelayGroupContext.Provider, {\n    value: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n      ...state,\n      setState,\n      setCurrentId\n    }), [state, setState, setCurrentId])\n  }, children);\n};\nconst useDelayGroup = (_ref2, _ref3) => {\n  let {\n    open,\n    onOpenChange\n  } = _ref2;\n  let {\n    id\n  } = _ref3;\n  const {\n    currentId,\n    setCurrentId,\n    initialDelay,\n    setState,\n    timeoutMs\n  } = useDelayGroupContext();\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (currentId) {\n      setState({\n        delay: {\n          open: 1,\n          close: getDelay(initialDelay, 'close')\n        }\n      });\n      if (currentId !== id) {\n        onOpenChange(false);\n      }\n    }\n  }, [id, onOpenChange, setState, currentId, initialDelay]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    function unset() {\n      onOpenChange(false);\n      setState({\n        delay: initialDelay,\n        currentId: null\n      });\n    }\n    if (!open && currentId === id) {\n      if (timeoutMs) {\n        const timeout = window.setTimeout(unset, timeoutMs);\n        return () => {\n          clearTimeout(timeout);\n        };\n      } else {\n        unset();\n      }\n    }\n  }, [open, setState, currentId, id, onOpenChange, initialDelay, timeoutMs]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (open) {\n      setCurrentId(id);\n    }\n  }, [open, setCurrentId, id]);\n};\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\n/**\n * Find the real active element. Traverses into shadowRoots.\n */\nfunction activeElement$1(doc) {\n  let activeElement = doc.activeElement;\n  while (((_activeElement = activeElement) == null ? void 0 : (_activeElement$shadow = _activeElement.shadowRoot) == null ? void 0 : _activeElement$shadow.activeElement) != null) {\n    var _activeElement, _activeElement$shadow;\n    activeElement = activeElement.shadowRoot.activeElement;\n  }\n  return activeElement;\n}\n\nfunction contains(parent, child) {\n  if (!parent || !child) {\n    return false;\n  }\n  const rootNode = child.getRootNode && child.getRootNode();\n\n  // First, attempt with faster native method\n  if (parent.contains(child)) {\n    return true;\n  }\n  // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n    let next = child;\n    do {\n      if (next && parent === next) {\n        return true;\n      }\n      // @ts-ignore\n      next = next.parentNode || next.host;\n    } while (next);\n  }\n\n  // Give up, the result is false\n  return false;\n}\n\nlet rafId = 0;\nfunction enqueueFocus(el, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    preventScroll = false,\n    cancelPrevious = true,\n    sync = false\n  } = options;\n  cancelPrevious && cancelAnimationFrame(rafId);\n  const exec = () => el == null ? void 0 : el.focus({\n    preventScroll\n  });\n  if (sync) {\n    exec();\n  } else {\n    rafId = requestAnimationFrame(exec);\n  }\n}\n\nfunction getAncestors(nodes, id) {\n  var _nodes$find;\n  let allAncestors = [];\n  let currentParentId = (_nodes$find = nodes.find(node => node.id === id)) == null ? void 0 : _nodes$find.parentId;\n  while (currentParentId) {\n    const currentNode = nodes.find(node => node.id === currentParentId);\n    currentParentId = currentNode == null ? void 0 : currentNode.parentId;\n    if (currentNode) {\n      allAncestors = allAncestors.concat(currentNode);\n    }\n  }\n  return allAncestors;\n}\n\nfunction getChildren(nodes, id) {\n  let allChildren = nodes.filter(node => {\n    var _node$context;\n    return node.parentId === id && ((_node$context = node.context) == null ? void 0 : _node$context.open);\n  }) || [];\n  let currentChildren = allChildren;\n  while (currentChildren.length) {\n    currentChildren = nodes.filter(node => {\n      var _currentChildren;\n      return (_currentChildren = currentChildren) == null ? void 0 : _currentChildren.some(n => {\n        var _node$context2;\n        return node.parentId === n.id && ((_node$context2 = node.context) == null ? void 0 : _node$context2.open);\n      });\n    }) || [];\n    allChildren = allChildren.concat(currentChildren);\n  }\n  return allChildren;\n}\n\nfunction getTarget(event) {\n  if ('composedPath' in event) {\n    return event.composedPath()[0];\n  }\n\n  // TS thinks `event` is of type never as it assumes all browsers support\n  // `composedPath()`, but browsers without shadow DOM don't.\n  return event.target;\n}\n\nconst TYPEABLE_SELECTOR = \"input:not([type='hidden']):not([disabled]),\" + \"[contenteditable]:not([contenteditable='false']),textarea:not([disabled])\";\nfunction isTypeableElement(element) {\n  return isHTMLElement(element) && element.matches(TYPEABLE_SELECTOR);\n}\n\nfunction stopEvent(event) {\n  event.preventDefault();\n  event.stopPropagation();\n}\n\nconst getTabbableOptions = () => ({\n  getShadowRoot: true,\n  displayCheck:\n  // JSDOM does not support the `tabbable` library. To solve this we can\n  // check if `ResizeObserver` is a real function (not polyfilled), which\n  // determines if the current environment is JSDOM-like.\n  typeof ResizeObserver === 'function' && ResizeObserver.toString().includes('[native code]') ? 'full' : 'none'\n});\nfunction getTabbableIn(container, direction) {\n  const allTabbable = (0,tabbable__WEBPACK_IMPORTED_MODULE_3__.tabbable)(container, getTabbableOptions());\n  if (direction === 'prev') {\n    allTabbable.reverse();\n  }\n  const activeIndex = allTabbable.indexOf(activeElement$1(getDocument(container)));\n  const nextTabbableElements = allTabbable.slice(activeIndex + 1);\n  return nextTabbableElements[0];\n}\nfunction getNextTabbable() {\n  return getTabbableIn(document.body, 'next');\n}\nfunction getPreviousTabbable() {\n  return getTabbableIn(document.body, 'prev');\n}\nfunction isOutsideEvent(event, container) {\n  const containerElement = container || event.currentTarget;\n  const relatedTarget = event.relatedTarget;\n  return !relatedTarget || !contains(containerElement, relatedTarget);\n}\nfunction disableFocusInside(container) {\n  const tabbableElements = (0,tabbable__WEBPACK_IMPORTED_MODULE_3__.tabbable)(container, getTabbableOptions());\n  tabbableElements.forEach(element => {\n    element.dataset.tabindex = element.getAttribute('tabindex') || '';\n    element.setAttribute('tabindex', '-1');\n  });\n}\nfunction enableFocusInside(container) {\n  const elements = container.querySelectorAll('[data-tabindex]');\n  elements.forEach(element => {\n    const tabindex = element.dataset.tabindex;\n    delete element.dataset.tabindex;\n    if (tabindex) {\n      element.setAttribute('tabindex', tabindex);\n    } else {\n      element.removeAttribute('tabindex');\n    }\n  });\n}\n\n// `toString()` prevents bundlers from trying to `import { useInsertionEffect } from 'react'`\nconst useInsertionEffect = react__WEBPACK_IMPORTED_MODULE_0__[/*#__PURE__*/'useInsertionEffect'.toString()];\nconst useSafeInsertionEffect = useInsertionEffect || (fn => fn());\nfunction useEvent(callback) {\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(() => {\n    if (true) {\n      throw new Error('Cannot call an event handler while rendering.');\n    }\n  });\n  useSafeInsertionEffect(() => {\n    ref.current = callback;\n  });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return ref.current == null ? void 0 : ref.current(...args);\n  }, []);\n}\n\n// See Diego Haz's Sandbox for making this logic work well on Safari/iOS:\n// https://codesandbox.io/s/tabbable-portal-f4tng?file=/src/FocusTrap.tsx\n\nconst HIDDEN_STYLES = {\n  border: 0,\n  clip: 'rect(0 0 0 0)',\n  height: '1px',\n  margin: '-1px',\n  overflow: 'hidden',\n  padding: 0,\n  position: 'fixed',\n  whiteSpace: 'nowrap',\n  width: '1px',\n  top: 0,\n  left: 0\n};\nlet activeElement;\nlet timeoutId;\nfunction setActiveElementOnTab(event) {\n  if (event.key === 'Tab') {\n    activeElement = event.target;\n    clearTimeout(timeoutId);\n  }\n}\nfunction isTabFocus(event) {\n  const result = activeElement === event.relatedTarget;\n  activeElement = event.relatedTarget;\n  clearTimeout(timeoutId);\n  return result;\n}\nconst FocusGuard = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function FocusGuard(props, ref) {\n  const onFocus = useEvent(props.onFocus);\n  const [role, setRole] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n  index(() => {\n    if (isSafari()) {\n      // Unlike other screen readers such as NVDA and JAWS, the virtual cursor\n      // on VoiceOver does trigger the onFocus event, so we can use the focus\n      // trap element. On Safari, only buttons trigger the onFocus event.\n      // NB: \"group\" role in the Sandbox no longer appears to work, must be a\n      // button role.\n      setRole('button');\n    }\n    document.addEventListener('keydown', setActiveElementOnTab);\n    return () => {\n      document.removeEventListener('keydown', setActiveElementOnTab);\n    };\n  }, []);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", _extends({}, props, {\n    ref: ref,\n    tabIndex: 0\n    // Role is only for VoiceOver\n    ,\n    role: role,\n    \"aria-hidden\": role ? undefined : true,\n    \"data-floating-ui-focus-guard\": \"\",\n    style: HIDDEN_STYLES,\n    onFocus: event => {\n      if (isSafari() && isMac() && !isTabFocus(event)) {\n        // On macOS we need to wait a little bit before moving\n        // focus again.\n        event.persist();\n        timeoutId = window.setTimeout(() => {\n          onFocus(event);\n        }, 50);\n      } else {\n        onFocus(event);\n      }\n    }\n  }));\n});\n\nconst PortalContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useFloatingPortalNode = function (_temp) {\n  let {\n    id,\n    enabled = true\n  } = _temp === void 0 ? {} : _temp;\n  const [portalEl, setPortalEl] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n  const uniqueId = useId();\n  const portalContext = usePortalContext();\n  index(() => {\n    if (!enabled) {\n      return;\n    }\n    const rootNode = id ? document.getElementById(id) : null;\n    if (rootNode) {\n      rootNode.setAttribute('data-floating-ui-portal', '');\n      setPortalEl(rootNode);\n    } else {\n      const newPortalEl = document.createElement('div');\n      if (id !== '') {\n        newPortalEl.id = id || uniqueId;\n      }\n      newPortalEl.setAttribute('data-floating-ui-portal', '');\n      setPortalEl(newPortalEl);\n      const container = (portalContext == null ? void 0 : portalContext.portalNode) || document.body;\n      container.appendChild(newPortalEl);\n      return () => {\n        container.removeChild(newPortalEl);\n      };\n    }\n  }, [id, portalContext, uniqueId, enabled]);\n  return portalEl;\n};\n\n/**\n * Portals the floating element into a given container element — by default,\n * outside of the app root and into the body.\n * @see https://floating-ui.com/docs/FloatingPortal\n */\nconst FloatingPortal = _ref => {\n  let {\n    children,\n    id,\n    root = null,\n    preserveTabOrder = true\n  } = _ref;\n  const portalNode = useFloatingPortalNode({\n    id,\n    enabled: !root\n  });\n  const [focusManagerState, setFocusManagerState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n  const beforeOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const afterOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const beforeInsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const afterInsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const shouldRenderGuards =\n  // The FocusManager and therefore floating element are currently open/\n  // rendered.\n  !!focusManagerState &&\n  // Guards are only for non-modal focus management.\n  !focusManagerState.modal && !!(root || portalNode) && preserveTabOrder;\n\n  // https://codesandbox.io/s/tabbable-portal-f4tng?file=/src/TabbablePortal.tsx\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (!portalNode || !preserveTabOrder || focusManagerState != null && focusManagerState.modal) {\n      return;\n    }\n\n    // Make sure elements inside the portal element are tabbable only when the\n    // portal has already been focused, either by tabbing into a focus trap\n    // element outside or using the mouse.\n    function onFocus(event) {\n      if (portalNode && isOutsideEvent(event)) {\n        const focusing = event.type === 'focusin';\n        const manageFocus = focusing ? enableFocusInside : disableFocusInside;\n        manageFocus(portalNode);\n      }\n    }\n    // Listen to the event on the capture phase so they run before the focus\n    // trap elements onFocus prop is called.\n    portalNode.addEventListener('focusin', onFocus, true);\n    portalNode.addEventListener('focusout', onFocus, true);\n    return () => {\n      portalNode.removeEventListener('focusin', onFocus, true);\n      portalNode.removeEventListener('focusout', onFocus, true);\n    };\n  }, [portalNode, preserveTabOrder, focusManagerState == null ? void 0 : focusManagerState.modal]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(PortalContext.Provider, {\n    value: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n      preserveTabOrder,\n      beforeOutsideRef,\n      afterOutsideRef,\n      beforeInsideRef,\n      afterInsideRef,\n      portalNode,\n      setFocusManagerState\n    }), [preserveTabOrder, portalNode])\n  }, shouldRenderGuards && portalNode && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(FocusGuard, {\n    \"data-type\": \"outside\",\n    ref: beforeOutsideRef,\n    onFocus: event => {\n      if (isOutsideEvent(event, portalNode)) {\n        var _beforeInsideRef$curr;\n        (_beforeInsideRef$curr = beforeInsideRef.current) == null ? void 0 : _beforeInsideRef$curr.focus();\n      } else {\n        const prevTabbable = getPreviousTabbable() || (focusManagerState == null ? void 0 : focusManagerState.refs.domReference.current);\n        prevTabbable == null ? void 0 : prevTabbable.focus();\n      }\n    }\n  }), shouldRenderGuards && portalNode && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", {\n    \"aria-owns\": portalNode.id,\n    style: HIDDEN_STYLES\n  }), root ? /*#__PURE__*/(0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(children, root) : portalNode ? /*#__PURE__*/(0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(children, portalNode) : null, shouldRenderGuards && portalNode && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(FocusGuard, {\n    \"data-type\": \"outside\",\n    ref: afterOutsideRef,\n    onFocus: event => {\n      if (isOutsideEvent(event, portalNode)) {\n        var _afterInsideRef$curre;\n        (_afterInsideRef$curre = afterInsideRef.current) == null ? void 0 : _afterInsideRef$curre.focus();\n      } else {\n        const nextTabbable = getNextTabbable() || (focusManagerState == null ? void 0 : focusManagerState.refs.domReference.current);\n        nextTabbable == null ? void 0 : nextTabbable.focus();\n        (focusManagerState == null ? void 0 : focusManagerState.closeOnFocusOut) && (focusManagerState == null ? void 0 : focusManagerState.onOpenChange(false));\n      }\n    }\n  }));\n};\nconst usePortalContext = () => react__WEBPACK_IMPORTED_MODULE_0__.useContext(PortalContext);\n\nconst VisuallyHiddenDismiss = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function VisuallyHiddenDismiss(props, ref) {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", _extends({}, props, {\n    type: \"button\",\n    ref: ref,\n    tabIndex: -1,\n    style: HIDDEN_STYLES\n  }));\n});\n/**\n * Provides focus management for the floating element.\n * @see https://floating-ui.com/docs/FloatingFocusManager\n */\nfunction FloatingFocusManager(_ref) {\n  let {\n    context,\n    children,\n    order = ['content'],\n    guards = true,\n    initialFocus = 0,\n    returnFocus = true,\n    modal = true,\n    visuallyHiddenDismiss = false,\n    closeOnFocusOut = true\n  } = _ref;\n  const {\n    refs,\n    nodeId,\n    onOpenChange,\n    events,\n    dataRef,\n    elements: {\n      domReference,\n      floating\n    }\n  } = context;\n  const orderRef = useLatestRef(order);\n  const tree = useFloatingTree();\n  const portalContext = usePortalContext();\n  const [tabbableContentLength, setTabbableContentLength] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n\n  // Controlled by `useListNavigation`.\n  const ignoreInitialFocus = typeof initialFocus === 'number' && initialFocus < 0;\n  const startDismissButtonRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const endDismissButtonRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const preventReturnFocusRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  const previouslyFocusedElementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const isPointerDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  const isInsidePortal = portalContext != null;\n\n  // If the reference is a combobox and is typeable (e.g. input/textarea),\n  // there are different focus semantics. The guards should not be rendered, but\n  // aria-hidden should be applied to all nodes still. Further, the visually\n  // hidden dismiss button should only appear at the end of the list, not the\n  // start.\n  const isTypeableCombobox = domReference && domReference.getAttribute('role') === 'combobox' && isTypeableElement(domReference);\n  const getTabbableContent = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (container) {\n    if (container === void 0) {\n      container = floating;\n    }\n    return container ? (0,tabbable__WEBPACK_IMPORTED_MODULE_3__.tabbable)(container, getTabbableOptions()) : [];\n  }, [floating]);\n  const getTabbableElements = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(container => {\n    const content = getTabbableContent(container);\n    return orderRef.current.map(type => {\n      if (domReference && type === 'reference') {\n        return domReference;\n      }\n      if (floating && type === 'floating') {\n        return floating;\n      }\n      return content;\n    }).filter(Boolean).flat();\n  }, [domReference, floating, orderRef, getTabbableContent]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (!modal) {\n      return;\n    }\n    function onKeyDown(event) {\n      if (event.key === 'Tab') {\n        // The focus guards have nothing to focus, so we need to stop the event.\n        if (getTabbableContent().length === 0 && !isTypeableCombobox) {\n          stopEvent(event);\n        }\n        const els = getTabbableElements();\n        const target = getTarget(event);\n        if (orderRef.current[0] === 'reference' && target === domReference) {\n          stopEvent(event);\n          if (event.shiftKey) {\n            enqueueFocus(els[els.length - 1]);\n          } else {\n            enqueueFocus(els[1]);\n          }\n        }\n        if (orderRef.current[1] === 'floating' && target === floating && event.shiftKey) {\n          stopEvent(event);\n          enqueueFocus(els[0]);\n        }\n      }\n    }\n    const doc = getDocument(floating);\n    doc.addEventListener('keydown', onKeyDown);\n    return () => {\n      doc.removeEventListener('keydown', onKeyDown);\n    };\n  }, [domReference, floating, modal, orderRef, refs, isTypeableCombobox, getTabbableContent, getTabbableElements]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (!closeOnFocusOut) {\n      return;\n    }\n\n    // In Safari, buttons lose focus when pressing them.\n    function handlePointerDown() {\n      isPointerDownRef.current = true;\n      setTimeout(() => {\n        isPointerDownRef.current = false;\n      });\n    }\n    function handleFocusOutside(event) {\n      const relatedTarget = event.relatedTarget;\n      const movedToUnrelatedNode = !(contains(domReference, relatedTarget) || contains(floating, relatedTarget) || contains(relatedTarget, floating) || contains(portalContext == null ? void 0 : portalContext.portalNode, relatedTarget) || relatedTarget != null && relatedTarget.hasAttribute('data-floating-ui-focus-guard') || tree && (getChildren(tree.nodesRef.current, nodeId).find(node => {\n        var _node$context, _node$context2;\n        return contains((_node$context = node.context) == null ? void 0 : _node$context.elements.floating, relatedTarget) || contains((_node$context2 = node.context) == null ? void 0 : _node$context2.elements.domReference, relatedTarget);\n      }) || getAncestors(tree.nodesRef.current, nodeId).find(node => {\n        var _node$context3, _node$context4;\n        return ((_node$context3 = node.context) == null ? void 0 : _node$context3.elements.floating) === relatedTarget || ((_node$context4 = node.context) == null ? void 0 : _node$context4.elements.domReference) === relatedTarget;\n      })));\n\n      // Focus did not move inside the floating tree, and there are no tabbable\n      // portal guards to handle closing.\n      if (relatedTarget && movedToUnrelatedNode && !isPointerDownRef.current &&\n      // Fix React 18 Strict Mode returnFocus due to double rendering.\n      relatedTarget !== previouslyFocusedElementRef.current) {\n        preventReturnFocusRef.current = true;\n        // On iOS VoiceOver, dismissing the nested submenu will cause the\n        // first item of the list to receive focus. Delaying it appears to fix\n        // the issue.\n        setTimeout(() => onOpenChange(false));\n      }\n    }\n    if (floating && isHTMLElement(domReference)) {\n      domReference.addEventListener('focusout', handleFocusOutside);\n      domReference.addEventListener('pointerdown', handlePointerDown);\n      !modal && floating.addEventListener('focusout', handleFocusOutside);\n      return () => {\n        domReference.removeEventListener('focusout', handleFocusOutside);\n        domReference.removeEventListener('pointerdown', handlePointerDown);\n        !modal && floating.removeEventListener('focusout', handleFocusOutside);\n      };\n    }\n  }, [domReference, floating, modal, nodeId, tree, portalContext, onOpenChange, closeOnFocusOut]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    var _portalContext$portal;\n    // Don't hide portals nested within the parent portal.\n    const portalNodes = Array.from((portalContext == null ? void 0 : (_portalContext$portal = portalContext.portalNode) == null ? void 0 : _portalContext$portal.querySelectorAll('[data-floating-ui-portal]')) || []);\n    function getDismissButtons() {\n      return [startDismissButtonRef.current, endDismissButtonRef.current].filter(Boolean);\n    }\n    if (floating && modal) {\n      const insideNodes = [floating, ...portalNodes, ...getDismissButtons()];\n      const cleanup = (0,aria_hidden__WEBPACK_IMPORTED_MODULE_4__.hideOthers)(orderRef.current.includes('reference') || isTypeableCombobox ? insideNodes.concat(domReference || []) : insideNodes);\n      return () => {\n        cleanup();\n      };\n    }\n  }, [domReference, floating, modal, orderRef, portalContext, isTypeableCombobox]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (modal && !guards && floating) {\n      const tabIndexValues = [];\n      const options = getTabbableOptions();\n      const allTabbable = (0,tabbable__WEBPACK_IMPORTED_MODULE_3__.tabbable)(getDocument(floating).body, options);\n      const floatingTabbable = getTabbableElements();\n\n      // Exclude all tabbable elements that are part of the order\n      const elements = allTabbable.filter(el => !floatingTabbable.includes(el));\n      elements.forEach((el, i) => {\n        tabIndexValues[i] = el.getAttribute('tabindex');\n        el.setAttribute('tabindex', '-1');\n      });\n      return () => {\n        elements.forEach((el, i) => {\n          const value = tabIndexValues[i];\n          if (value == null) {\n            el.removeAttribute('tabindex');\n          } else {\n            el.setAttribute('tabindex', value);\n          }\n        });\n      };\n    }\n  }, [floating, modal, guards, getTabbableElements]);\n  index(() => {\n    if (!floating) return;\n    const doc = getDocument(floating);\n    let returnFocusValue = returnFocus;\n    let preventReturnFocusScroll = false;\n    const previouslyFocusedElement = activeElement$1(doc);\n    const contextData = dataRef.current;\n    previouslyFocusedElementRef.current = previouslyFocusedElement;\n    const focusableElements = getTabbableElements(floating);\n    const elToFocus = (typeof initialFocus === 'number' ? focusableElements[initialFocus] : initialFocus.current) || floating;\n\n    // If the `useListNavigation` hook is active, always ignore `initialFocus`\n    // because it has its own handling of the initial focus.\n    !ignoreInitialFocus && enqueueFocus(elToFocus, {\n      preventScroll: elToFocus === floating\n    });\n\n    // Dismissing via outside press should always ignore `returnFocus` to\n    // prevent unwanted scrolling.\n    function onDismiss(payload) {\n      if (payload.type === 'escapeKey' && refs.domReference.current) {\n        previouslyFocusedElementRef.current = refs.domReference.current;\n      }\n      if (['referencePress', 'escapeKey'].includes(payload.type)) {\n        return;\n      }\n      const returnFocus = payload.data.returnFocus;\n      if (typeof returnFocus === 'object') {\n        returnFocusValue = true;\n        preventReturnFocusScroll = returnFocus.preventScroll;\n      } else {\n        returnFocusValue = returnFocus;\n      }\n    }\n    events.on('dismiss', onDismiss);\n    return () => {\n      events.off('dismiss', onDismiss);\n      if (contains(floating, activeElement$1(doc)) && refs.domReference.current) {\n        previouslyFocusedElementRef.current = refs.domReference.current;\n      }\n      if (returnFocusValue && isHTMLElement(previouslyFocusedElementRef.current) && !preventReturnFocusRef.current) {\n        // `isPointerDownRef.current` to avoid the focus ring from appearing on\n        // the reference element when click-toggling it.\n        if (!refs.domReference.current || isPointerDownRef.current) {\n          enqueueFocus(previouslyFocusedElementRef.current, {\n            // When dismissing nested floating elements, by the time the rAF has\n            // executed, the menus will all have been unmounted. When they try\n            // to get focused, the calls get ignored — leaving the root\n            // reference focused as desired.\n            cancelPrevious: false,\n            preventScroll: preventReturnFocusScroll\n          });\n        } else {\n          var _previouslyFocusedEle;\n          // If the user has specified a `keydown` listener that calls\n          // setOpen(false) (e.g. selecting an item and closing the floating\n          // element), then sync return focus causes `useClick` to immediately\n          // re-open it, unless they call `event.preventDefault()` in the\n          // `keydown` listener. This helps keep backwards compatibility with\n          // older examples.\n          contextData.__syncReturnFocus = true;\n\n          // In Safari, `useListNavigation` moves focus sync, so making this\n          // sync ensures the initial item remains focused despite this being\n          // invoked in Strict Mode due to double-invoked useEffects. This also\n          // has the positive side effect of closing a modally focus-managed\n          // <Menu> on `Tab` keydown to move naturally to the next focusable\n          // element.\n          (_previouslyFocusedEle = previouslyFocusedElementRef.current) == null ? void 0 : _previouslyFocusedEle.focus({\n            preventScroll: preventReturnFocusScroll\n          });\n          setTimeout(() => {\n            // This isn't an actual property the user should access, make sure\n            // it doesn't persist.\n            delete contextData.__syncReturnFocus;\n          });\n        }\n      }\n    };\n  }, [floating, getTabbableElements, initialFocus, returnFocus, dataRef, refs, events, ignoreInitialFocus]);\n\n  // Synchronize the `context` & `modal` value to the FloatingPortal context.\n  // It will decide whether or not it needs to render its own guards.\n  index(() => {\n    if (!portalContext) return;\n    portalContext.setFocusManagerState({\n      ...context,\n      modal,\n      closeOnFocusOut\n      // Not concerned about the <RT> generic type.\n    });\n\n    return () => {\n      portalContext.setFocusManagerState(null);\n    };\n  }, [portalContext, modal, closeOnFocusOut, context]);\n  index(() => {\n    if (ignoreInitialFocus || !floating) return;\n    function setState() {\n      setTabbableContentLength(getTabbableContent().length);\n    }\n    setState();\n    if (typeof MutationObserver === 'function') {\n      const observer = new MutationObserver(setState);\n      observer.observe(floating, {\n        childList: true,\n        subtree: true\n      });\n      return () => {\n        observer.disconnect();\n      };\n    }\n  }, [floating, getTabbableContent, ignoreInitialFocus, refs]);\n  const shouldRenderGuards = guards && (isInsidePortal || modal) && !isTypeableCombobox;\n  function renderDismissButton(location) {\n    return visuallyHiddenDismiss && modal ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(VisuallyHiddenDismiss, {\n      ref: location === 'start' ? startDismissButtonRef : endDismissButtonRef,\n      onClick: () => onOpenChange(false)\n    }, typeof visuallyHiddenDismiss === 'string' ? visuallyHiddenDismiss : 'Dismiss') : null;\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, shouldRenderGuards && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(FocusGuard, {\n    \"data-type\": \"inside\",\n    ref: portalContext == null ? void 0 : portalContext.beforeInsideRef,\n    onFocus: event => {\n      if (modal) {\n        const els = getTabbableElements();\n        enqueueFocus(order[0] === 'reference' ? els[0] : els[els.length - 1]);\n      } else if (portalContext != null && portalContext.preserveTabOrder && portalContext.portalNode) {\n        preventReturnFocusRef.current = false;\n        if (isOutsideEvent(event, portalContext.portalNode)) {\n          const nextTabbable = getNextTabbable() || domReference;\n          nextTabbable == null ? void 0 : nextTabbable.focus();\n        } else {\n          var _portalContext$before;\n          (_portalContext$before = portalContext.beforeOutsideRef.current) == null ? void 0 : _portalContext$before.focus();\n        }\n      }\n    }\n  }), isTypeableCombobox ? null : renderDismissButton('start'), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, tabbableContentLength === 0 || order.includes('floating') ? {\n    tabIndex: 0\n  } : {}), renderDismissButton('end'), shouldRenderGuards && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(FocusGuard, {\n    \"data-type\": \"inside\",\n    ref: portalContext == null ? void 0 : portalContext.afterInsideRef,\n    onFocus: event => {\n      if (modal) {\n        enqueueFocus(getTabbableElements()[0]);\n      } else if (portalContext != null && portalContext.preserveTabOrder && portalContext.portalNode) {\n        preventReturnFocusRef.current = true;\n        if (isOutsideEvent(event, portalContext.portalNode)) {\n          const prevTabbable = getPreviousTabbable() || domReference;\n          prevTabbable == null ? void 0 : prevTabbable.focus();\n        } else {\n          var _portalContext$afterO;\n          (_portalContext$afterO = portalContext.afterOutsideRef.current) == null ? void 0 : _portalContext$afterO.focus();\n        }\n      }\n    }\n  }));\n}\n\nconst identifier = 'data-floating-ui-scroll-lock';\n\n/**\n * Provides base styling for a fixed overlay element to dim content or block\n * pointer events behind a floating element.\n * It's a regular `<div>`, so it can be styled via any CSS solution you prefer.\n * @see https://floating-ui.com/docs/FloatingOverlay\n */\nconst FloatingOverlay = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function FloatingOverlay(_ref, ref) {\n  let {\n    lockScroll = false,\n    ...rest\n  } = _ref;\n  index(() => {\n    var _window$visualViewpor, _window$visualViewpor2;\n    if (!lockScroll) {\n      return;\n    }\n    const alreadyLocked = document.body.hasAttribute(identifier);\n    if (alreadyLocked) {\n      return;\n    }\n    document.body.setAttribute(identifier, '');\n\n    // RTL <body> scrollbar\n    const scrollbarX = Math.round(document.documentElement.getBoundingClientRect().left) + document.documentElement.scrollLeft;\n    const paddingProp = scrollbarX ? 'paddingLeft' : 'paddingRight';\n    const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;\n\n    // Only iOS doesn't respect `overflow: hidden` on document.body, and this\n    // technique has fewer side effects.\n    if (!/iP(hone|ad|od)|iOS/.test(getPlatform())) {\n      Object.assign(document.body.style, {\n        overflow: 'hidden',\n        [paddingProp]: scrollbarWidth + \"px\"\n      });\n      return () => {\n        document.body.removeAttribute(identifier);\n        Object.assign(document.body.style, {\n          overflow: '',\n          [paddingProp]: ''\n        });\n      };\n    }\n\n    // iOS 12 does not support `visualViewport`.\n    const offsetLeft = ((_window$visualViewpor = window.visualViewport) == null ? void 0 : _window$visualViewpor.offsetLeft) || 0;\n    const offsetTop = ((_window$visualViewpor2 = window.visualViewport) == null ? void 0 : _window$visualViewpor2.offsetTop) || 0;\n    const scrollX = window.pageXOffset;\n    const scrollY = window.pageYOffset;\n    Object.assign(document.body.style, {\n      position: 'fixed',\n      overflow: 'hidden',\n      top: -(scrollY - Math.floor(offsetTop)) + \"px\",\n      left: -(scrollX - Math.floor(offsetLeft)) + \"px\",\n      right: '0',\n      [paddingProp]: scrollbarWidth + \"px\"\n    });\n    return () => {\n      Object.assign(document.body.style, {\n        position: '',\n        overflow: '',\n        top: '',\n        left: '',\n        right: '',\n        [paddingProp]: ''\n      });\n      document.body.removeAttribute(identifier);\n      window.scrollTo(scrollX, scrollY);\n    };\n  }, [lockScroll]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", _extends({\n    ref: ref\n  }, rest, {\n    style: {\n      position: 'fixed',\n      overflow: 'auto',\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0,\n      ...rest.style\n    }\n  }));\n});\n\nfunction isButtonTarget(event) {\n  return isHTMLElement(event.target) && event.target.tagName === 'BUTTON';\n}\nfunction isSpaceIgnored(element) {\n  return isTypeableElement(element);\n}\n/**\n * Opens or closes the floating element when clicking the reference element.\n * @see https://floating-ui.com/docs/useClick\n */\nconst useClick = function (_ref, _temp) {\n  let {\n    open,\n    onOpenChange,\n    dataRef,\n    elements: {\n      domReference\n    }\n  } = _ref;\n  let {\n    enabled = true,\n    event: eventOption = 'click',\n    toggle = true,\n    ignoreMouse = false,\n    keyboardHandlers = true\n  } = _temp === void 0 ? {} : _temp;\n  const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    if (!enabled) {\n      return {};\n    }\n    return {\n      reference: {\n        onPointerDown(event) {\n          pointerTypeRef.current = event.pointerType;\n        },\n        onMouseDown(event) {\n          // Ignore all buttons except for the \"main\" button.\n          // https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/button\n          if (event.button !== 0) {\n            return;\n          }\n          if (isMouseLikePointerType(pointerTypeRef.current, true) && ignoreMouse) {\n            return;\n          }\n          if (eventOption === 'click') {\n            return;\n          }\n          if (open) {\n            if (toggle && (dataRef.current.openEvent ? dataRef.current.openEvent.type === 'mousedown' : true)) {\n              onOpenChange(false);\n            }\n          } else {\n            // Prevent stealing focus from the floating element\n            event.preventDefault();\n            onOpenChange(true);\n          }\n          dataRef.current.openEvent = event.nativeEvent;\n        },\n        onClick(event) {\n          if (dataRef.current.__syncReturnFocus) {\n            return;\n          }\n          if (eventOption === 'mousedown' && pointerTypeRef.current) {\n            pointerTypeRef.current = undefined;\n            return;\n          }\n          if (isMouseLikePointerType(pointerTypeRef.current, true) && ignoreMouse) {\n            return;\n          }\n          if (open) {\n            if (toggle && (dataRef.current.openEvent ? dataRef.current.openEvent.type === 'click' : true)) {\n              onOpenChange(false);\n            }\n          } else {\n            onOpenChange(true);\n          }\n          dataRef.current.openEvent = event.nativeEvent;\n        },\n        onKeyDown(event) {\n          pointerTypeRef.current = undefined;\n          if (!keyboardHandlers) {\n            return;\n          }\n          if (isButtonTarget(event)) {\n            return;\n          }\n          if (event.key === ' ' && !isSpaceIgnored(domReference)) {\n            // Prevent scrolling\n            event.preventDefault();\n          }\n          if (event.key === 'Enter') {\n            if (open) {\n              if (toggle) {\n                onOpenChange(false);\n              }\n            } else {\n              onOpenChange(true);\n            }\n          }\n        },\n        onKeyUp(event) {\n          if (!keyboardHandlers) {\n            return;\n          }\n          if (isButtonTarget(event) || isSpaceIgnored(domReference)) {\n            return;\n          }\n          if (event.key === ' ') {\n            if (open) {\n              if (toggle) {\n                onOpenChange(false);\n              }\n            } else {\n              onOpenChange(true);\n            }\n          }\n        }\n      }\n    };\n  }, [enabled, dataRef, eventOption, ignoreMouse, keyboardHandlers, domReference, toggle, open, onOpenChange]);\n};\n\n/**\n * Check whether the event.target is within the provided node. Uses event.composedPath if available for custom element support.\n *\n * @param event The event whose target/composedPath to check\n * @param node The node to check against\n * @returns Whether the event.target/composedPath is within the node.\n */\nfunction isEventTargetWithin(event, node) {\n  if (node == null) {\n    return false;\n  }\n  if ('composedPath' in event) {\n    return event.composedPath().includes(node);\n  }\n\n  // TS thinks `event` is of type never as it assumes all browsers support composedPath, but browsers without shadow dom don't\n  const e = event;\n  return e.target != null && node.contains(e.target);\n}\n\nconst bubbleHandlerKeys = {\n  pointerdown: 'onPointerDown',\n  mousedown: 'onMouseDown',\n  click: 'onClick'\n};\nconst captureHandlerKeys = {\n  pointerdown: 'onPointerDownCapture',\n  mousedown: 'onMouseDownCapture',\n  click: 'onClickCapture'\n};\nconst normalizeBubblesProp = function (bubbles) {\n  var _bubbles$escapeKey, _bubbles$outsidePress;\n  if (bubbles === void 0) {\n    bubbles = true;\n  }\n  return {\n    escapeKeyBubbles: typeof bubbles === 'boolean' ? bubbles : (_bubbles$escapeKey = bubbles.escapeKey) != null ? _bubbles$escapeKey : true,\n    outsidePressBubbles: typeof bubbles === 'boolean' ? bubbles : (_bubbles$outsidePress = bubbles.outsidePress) != null ? _bubbles$outsidePress : true\n  };\n};\n/**\n * Closes the floating element when a dismissal is requested — by default, when\n * the user presses the `escape` key or outside of the floating element.\n * @see https://floating-ui.com/docs/useDismiss\n */\nconst useDismiss = function (_ref, _temp) {\n  let {\n    open,\n    onOpenChange,\n    events,\n    nodeId,\n    elements: {\n      reference,\n      domReference,\n      floating\n    },\n    dataRef\n  } = _ref;\n  let {\n    enabled = true,\n    escapeKey = true,\n    outsidePress: unstable_outsidePress = true,\n    outsidePressEvent = 'pointerdown',\n    referencePress = false,\n    referencePressEvent = 'pointerdown',\n    ancestorScroll = false,\n    bubbles = true\n  } = _temp === void 0 ? {} : _temp;\n  const tree = useFloatingTree();\n  const nested = useFloatingParentNodeId() != null;\n  const outsidePressFn = useEvent(typeof unstable_outsidePress === 'function' ? unstable_outsidePress : () => false);\n  const outsidePress = typeof unstable_outsidePress === 'function' ? outsidePressFn : unstable_outsidePress;\n  const insideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  const {\n    escapeKeyBubbles,\n    outsidePressBubbles\n  } = normalizeBubblesProp(bubbles);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (!open || !enabled) {\n      return;\n    }\n    dataRef.current.__escapeKeyBubbles = escapeKeyBubbles;\n    dataRef.current.__outsidePressBubbles = outsidePressBubbles;\n    function onKeyDown(event) {\n      if (event.key === 'Escape') {\n        const children = tree ? getChildren(tree.nodesRef.current, nodeId) : [];\n        if (children.length > 0) {\n          let shouldDismiss = true;\n          children.forEach(child => {\n            var _child$context;\n            if ((_child$context = child.context) != null && _child$context.open && !child.context.dataRef.current.__escapeKeyBubbles) {\n              shouldDismiss = false;\n              return;\n            }\n          });\n          if (!shouldDismiss) {\n            return;\n          }\n        }\n        events.emit('dismiss', {\n          type: 'escapeKey',\n          data: {\n            returnFocus: {\n              preventScroll: false\n            }\n          }\n        });\n        onOpenChange(false);\n      }\n    }\n    function onOutsidePress(event) {\n      // Given developers can stop the propagation of the synthetic event,\n      // we can only be confident with a positive value.\n      const insideReactTree = insideReactTreeRef.current;\n      insideReactTreeRef.current = false;\n      if (insideReactTree) {\n        return;\n      }\n      if (typeof outsidePress === 'function' && !outsidePress(event)) {\n        return;\n      }\n      const target = getTarget(event);\n\n      // Check if the click occurred on the scrollbar\n      if (isHTMLElement(target) && floating) {\n        const win = floating.ownerDocument.defaultView || window;\n        const canScrollX = target.scrollWidth > target.clientWidth;\n        const canScrollY = target.scrollHeight > target.clientHeight;\n        let xCond = canScrollY && event.offsetX > target.clientWidth;\n\n        // In some browsers it is possible to change the <body> (or window)\n        // scrollbar to the left side, but is very rare and is difficult to\n        // check for. Plus, for modal dialogs with backdrops, it is more\n        // important that the backdrop is checked but not so much the window.\n        if (canScrollY) {\n          const isRTL = win.getComputedStyle(target).direction === 'rtl';\n          if (isRTL) {\n            xCond = event.offsetX <= target.offsetWidth - target.clientWidth;\n          }\n        }\n        if (xCond || canScrollX && event.offsetY > target.clientHeight) {\n          return;\n        }\n      }\n      const targetIsInsideChildren = tree && getChildren(tree.nodesRef.current, nodeId).some(node => {\n        var _node$context;\n        return isEventTargetWithin(event, (_node$context = node.context) == null ? void 0 : _node$context.elements.floating);\n      });\n      if (isEventTargetWithin(event, floating) || isEventTargetWithin(event, domReference) || targetIsInsideChildren) {\n        return;\n      }\n      const children = tree ? getChildren(tree.nodesRef.current, nodeId) : [];\n      if (children.length > 0) {\n        let shouldDismiss = true;\n        children.forEach(child => {\n          var _child$context2;\n          if ((_child$context2 = child.context) != null && _child$context2.open && !child.context.dataRef.current.__outsidePressBubbles) {\n            shouldDismiss = false;\n            return;\n          }\n        });\n        if (!shouldDismiss) {\n          return;\n        }\n      }\n      events.emit('dismiss', {\n        type: 'outsidePress',\n        data: {\n          returnFocus: nested ? {\n            preventScroll: true\n          } : isVirtualClick(event) || isVirtualPointerEvent(event)\n        }\n      });\n      onOpenChange(false);\n    }\n    function onScroll() {\n      onOpenChange(false);\n    }\n    const doc = getDocument(floating);\n    escapeKey && doc.addEventListener('keydown', onKeyDown);\n    outsidePress && doc.addEventListener(outsidePressEvent, onOutsidePress);\n    let ancestors = [];\n    if (ancestorScroll) {\n      if (isElement(domReference)) {\n        ancestors = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.getOverflowAncestors)(domReference);\n      }\n      if (isElement(floating)) {\n        ancestors = ancestors.concat((0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.getOverflowAncestors)(floating));\n      }\n      if (!isElement(reference) && reference && reference.contextElement) {\n        ancestors = ancestors.concat((0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.getOverflowAncestors)(reference.contextElement));\n      }\n    }\n\n    // Ignore the visual viewport for scrolling dismissal (allow pinch-zoom)\n    ancestors = ancestors.filter(ancestor => {\n      var _doc$defaultView;\n      return ancestor !== ((_doc$defaultView = doc.defaultView) == null ? void 0 : _doc$defaultView.visualViewport);\n    });\n    ancestors.forEach(ancestor => {\n      ancestor.addEventListener('scroll', onScroll, {\n        passive: true\n      });\n    });\n    return () => {\n      escapeKey && doc.removeEventListener('keydown', onKeyDown);\n      outsidePress && doc.removeEventListener(outsidePressEvent, onOutsidePress);\n      ancestors.forEach(ancestor => {\n        ancestor.removeEventListener('scroll', onScroll);\n      });\n    };\n  }, [dataRef, floating, domReference, reference, escapeKey, outsidePress, outsidePressEvent, events, tree, nodeId, open, onOpenChange, ancestorScroll, enabled, escapeKeyBubbles, outsidePressBubbles, nested]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    insideReactTreeRef.current = false;\n  }, [outsidePress, outsidePressEvent]);\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    if (!enabled) {\n      return {};\n    }\n    return {\n      reference: {\n        [bubbleHandlerKeys[referencePressEvent]]: () => {\n          if (referencePress) {\n            events.emit('dismiss', {\n              type: 'referencePress',\n              data: {\n                returnFocus: false\n              }\n            });\n            onOpenChange(false);\n          }\n        }\n      },\n      floating: {\n        [captureHandlerKeys[outsidePressEvent]]: () => {\n          insideReactTreeRef.current = true;\n        }\n      }\n    };\n  }, [enabled, events, referencePress, outsidePressEvent, referencePressEvent, onOpenChange]);\n};\n\n/**\n * Opens the floating element while the reference element has focus, like CSS\n * `:focus`.\n * @see https://floating-ui.com/docs/useFocus\n */\nconst useFocus = function (_ref, _temp) {\n  let {\n    open,\n    onOpenChange,\n    dataRef,\n    events,\n    refs,\n    elements: {\n      floating,\n      domReference\n    }\n  } = _ref;\n  let {\n    enabled = true,\n    keyboardOnly = true\n  } = _temp === void 0 ? {} : _temp;\n  const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef('');\n  const blockFocusRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  const timeoutRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (!enabled) {\n      return;\n    }\n    const doc = getDocument(floating);\n    const win = doc.defaultView || window;\n\n    // If the reference was focused and the user left the tab/window, and the\n    // floating element was not open, the focus should be blocked when they\n    // return to the tab/window.\n    function onBlur() {\n      if (!open && isHTMLElement(domReference) && domReference === activeElement$1(getDocument(domReference))) {\n        blockFocusRef.current = true;\n      }\n    }\n    win.addEventListener('blur', onBlur);\n    return () => {\n      win.removeEventListener('blur', onBlur);\n    };\n  }, [floating, domReference, open, enabled]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (!enabled) {\n      return;\n    }\n    function onDismiss(payload) {\n      if (payload.type === 'referencePress' || payload.type === 'escapeKey') {\n        blockFocusRef.current = true;\n      }\n    }\n    events.on('dismiss', onDismiss);\n    return () => {\n      events.off('dismiss', onDismiss);\n    };\n  }, [events, enabled]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    return () => {\n      clearTimeout(timeoutRef.current);\n    };\n  }, []);\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    if (!enabled) {\n      return {};\n    }\n    return {\n      reference: {\n        onPointerDown(_ref2) {\n          let {\n            pointerType\n          } = _ref2;\n          pointerTypeRef.current = pointerType;\n          blockFocusRef.current = !!(pointerType && keyboardOnly);\n        },\n        onMouseLeave() {\n          blockFocusRef.current = false;\n        },\n        onFocus(event) {\n          var _dataRef$current$open;\n          if (blockFocusRef.current) {\n            return;\n          }\n\n          // Dismiss with click should ignore the subsequent `focus` trigger,\n          // but only if the click originated inside the reference element.\n          if (event.type === 'focus' && ((_dataRef$current$open = dataRef.current.openEvent) == null ? void 0 : _dataRef$current$open.type) === 'mousedown' && dataRef.current.openEvent && isEventTargetWithin(dataRef.current.openEvent, domReference)) {\n            return;\n          }\n          dataRef.current.openEvent = event.nativeEvent;\n          onOpenChange(true);\n        },\n        onBlur(event) {\n          blockFocusRef.current = false;\n          const relatedTarget = event.relatedTarget;\n\n          // Hit the non-modal focus management portal guard. Focus will be\n          // moved into the floating element immediately after.\n          const movedToFocusGuard = isElement(relatedTarget) && relatedTarget.hasAttribute('data-floating-ui-focus-guard') && relatedTarget.getAttribute('data-type') === 'outside';\n\n          // Wait for the window blur listener to fire.\n          timeoutRef.current = setTimeout(() => {\n            // When focusing the reference element (e.g. regular click), then\n            // clicking into the floating element, prevent it from hiding.\n            // Note: it must be focusable, e.g. `tabindex=\"-1\"`.\n            if (contains(refs.floating.current, relatedTarget) || contains(domReference, relatedTarget) || movedToFocusGuard) {\n              return;\n            }\n            onOpenChange(false);\n          });\n        }\n      }\n    };\n  }, [enabled, keyboardOnly, domReference, refs, dataRef, onOpenChange]);\n};\n\nlet isPreventScrollSupported = false;\nconst ARROW_UP = 'ArrowUp';\nconst ARROW_DOWN = 'ArrowDown';\nconst ARROW_LEFT = 'ArrowLeft';\nconst ARROW_RIGHT = 'ArrowRight';\nfunction isDifferentRow(index, cols, prevRow) {\n  return Math.floor(index / cols) !== prevRow;\n}\nfunction isIndexOutOfBounds(listRef, index) {\n  return index < 0 || index >= listRef.current.length;\n}\nfunction findNonDisabledIndex(listRef, _temp) {\n  let {\n    startingIndex = -1,\n    decrement = false,\n    disabledIndices,\n    amount = 1\n  } = _temp === void 0 ? {} : _temp;\n  const list = listRef.current;\n  let index = startingIndex;\n  do {\n    var _list$index, _list$index2;\n    index = index + (decrement ? -amount : amount);\n  } while (index >= 0 && index <= list.length - 1 && (disabledIndices ? disabledIndices.includes(index) : list[index] == null || ((_list$index = list[index]) == null ? void 0 : _list$index.hasAttribute('disabled')) || ((_list$index2 = list[index]) == null ? void 0 : _list$index2.getAttribute('aria-disabled')) === 'true'));\n  return index;\n}\nfunction doSwitch(orientation, vertical, horizontal) {\n  switch (orientation) {\n    case 'vertical':\n      return vertical;\n    case 'horizontal':\n      return horizontal;\n    default:\n      return vertical || horizontal;\n  }\n}\nfunction isMainOrientationKey(key, orientation) {\n  const vertical = key === ARROW_UP || key === ARROW_DOWN;\n  const horizontal = key === ARROW_LEFT || key === ARROW_RIGHT;\n  return doSwitch(orientation, vertical, horizontal);\n}\nfunction isMainOrientationToEndKey(key, orientation, rtl) {\n  const vertical = key === ARROW_DOWN;\n  const horizontal = rtl ? key === ARROW_LEFT : key === ARROW_RIGHT;\n  return doSwitch(orientation, vertical, horizontal) || key === 'Enter' || key == ' ' || key === '';\n}\nfunction isCrossOrientationOpenKey(key, orientation, rtl) {\n  const vertical = rtl ? key === ARROW_LEFT : key === ARROW_RIGHT;\n  const horizontal = key === ARROW_DOWN;\n  return doSwitch(orientation, vertical, horizontal);\n}\nfunction isCrossOrientationCloseKey(key, orientation, rtl) {\n  const vertical = rtl ? key === ARROW_RIGHT : key === ARROW_LEFT;\n  const horizontal = key === ARROW_UP;\n  return doSwitch(orientation, vertical, horizontal);\n}\nfunction getMinIndex(listRef, disabledIndices) {\n  return findNonDisabledIndex(listRef, {\n    disabledIndices\n  });\n}\nfunction getMaxIndex(listRef, disabledIndices) {\n  return findNonDisabledIndex(listRef, {\n    decrement: true,\n    startingIndex: listRef.current.length,\n    disabledIndices\n  });\n}\n/**\n * Adds arrow key-based navigation of a list of items, either using real DOM\n * focus or virtual focus.\n * @see https://floating-ui.com/docs/useListNavigation\n */\nconst useListNavigation = function (_ref, _temp2) {\n  let {\n    open,\n    onOpenChange,\n    refs,\n    elements: {\n      domReference\n    }\n  } = _ref;\n  let {\n    listRef,\n    activeIndex,\n    onNavigate: unstable_onNavigate = () => {},\n    enabled = true,\n    selectedIndex = null,\n    allowEscape = false,\n    loop = false,\n    nested = false,\n    rtl = false,\n    virtual = false,\n    focusItemOnOpen = 'auto',\n    focusItemOnHover = true,\n    openOnArrowKeyDown = true,\n    disabledIndices = undefined,\n    orientation = 'vertical',\n    cols = 1,\n    scrollItemIntoView = true\n  } = _temp2 === void 0 ? {\n    listRef: {\n      current: []\n    },\n    activeIndex: null,\n    onNavigate: () => {}\n  } : _temp2;\n  if (true) {\n    if (allowEscape) {\n      if (!loop) {\n        console.warn(['Floating UI: `useListNavigation` looping must be enabled to allow', 'escaping.'].join(' '));\n      }\n      if (!virtual) {\n        console.warn(['Floating UI: `useListNavigation` must be virtual to allow', 'escaping.'].join(' '));\n      }\n    }\n    if (orientation === 'vertical' && cols > 1) {\n      console.warn(['Floating UI: In grid list navigation mode (`cols` > 1), the', '`orientation` should be either \"horizontal\" or \"both\".'].join(' '));\n    }\n  }\n  const parentId = useFloatingParentNodeId();\n  const tree = useFloatingTree();\n  const onNavigate = useEvent(unstable_onNavigate);\n  const focusItemOnOpenRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(focusItemOnOpen);\n  const indexRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(selectedIndex != null ? selectedIndex : -1);\n  const keyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const isPointerModalityRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n  const previousOnNavigateRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(onNavigate);\n  const previousOpenRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(open);\n  const forceSyncFocus = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  const forceScrollIntoViewRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  const disabledIndicesRef = useLatestRef(disabledIndices);\n  const latestOpenRef = useLatestRef(open);\n  const scrollItemIntoViewRef = useLatestRef(scrollItemIntoView);\n  const [activeId, setActiveId] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n  const focusItem = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (listRef, indexRef, forceScrollIntoView) {\n    if (forceScrollIntoView === void 0) {\n      forceScrollIntoView = false;\n    }\n    const item = listRef.current[indexRef.current];\n    if (virtual) {\n      setActiveId(item == null ? void 0 : item.id);\n    } else {\n      enqueueFocus(item, {\n        preventScroll: true,\n        // Mac Safari does not move the virtual cursor unless the focus call\n        // is sync. However, for the very first focus call, we need to wait\n        // for the position to be ready in order to prevent unwanted\n        // scrolling. This means the virtual cursor will not move to the first\n        // item when first opening the floating element, but will on\n        // subsequent calls. `preventScroll` is supported in modern Safari,\n        // so we can use that instead.\n        // iOS Safari must be async or the first item will not be focused.\n        sync: isMac() && isSafari() ? isPreventScrollSupported || forceSyncFocus.current : false\n      });\n    }\n    requestAnimationFrame(() => {\n      const scrollIntoViewOptions = scrollItemIntoViewRef.current;\n      const shouldScrollIntoView = scrollIntoViewOptions && item && (forceScrollIntoView || !isPointerModalityRef.current);\n      if (shouldScrollIntoView) {\n        // JSDOM doesn't support `.scrollIntoView()` but it's widely supported\n        // by all browsers.\n        item.scrollIntoView == null ? void 0 : item.scrollIntoView(typeof scrollIntoViewOptions === 'boolean' ? {\n          block: 'nearest',\n          inline: 'nearest'\n        } : scrollIntoViewOptions);\n      }\n    });\n  }, [virtual, scrollItemIntoViewRef]);\n  index(() => {\n    document.createElement('div').focus({\n      get preventScroll() {\n        isPreventScrollSupported = true;\n        return false;\n      }\n    });\n  }, []);\n\n  // Sync `selectedIndex` to be the `activeIndex` upon opening the floating\n  // element. Also, reset `activeIndex` upon closing the floating element.\n  index(() => {\n    if (!enabled) {\n      return;\n    }\n    if (open) {\n      if (focusItemOnOpenRef.current && selectedIndex != null) {\n        // Regardless of the pointer modality, we want to ensure the selected\n        // item comes into view when the floating element is opened.\n        forceScrollIntoViewRef.current = true;\n        onNavigate(selectedIndex);\n      }\n    } else if (previousOpenRef.current) {\n      // Since the user can specify `onNavigate` conditionally\n      // (onNavigate: open ? setActiveIndex : setSelectedIndex),\n      // we store and call the previous function.\n      indexRef.current = -1;\n      previousOnNavigateRef.current(null);\n    }\n  }, [enabled, open, selectedIndex, onNavigate]);\n\n  // Sync `activeIndex` to be the focused item while the floating element is\n  // open.\n  index(() => {\n    if (!enabled) {\n      return;\n    }\n    if (open) {\n      if (activeIndex == null) {\n        forceSyncFocus.current = false;\n        if (selectedIndex != null) {\n          return;\n        }\n\n        // Reset while the floating element was open (e.g. the list changed).\n        if (previousOpenRef.current) {\n          indexRef.current = -1;\n          focusItem(listRef, indexRef);\n        }\n\n        // Initial sync.\n        if (!previousOpenRef.current && focusItemOnOpenRef.current && (keyRef.current != null || focusItemOnOpenRef.current === true && keyRef.current == null)) {\n          indexRef.current = keyRef.current == null || isMainOrientationToEndKey(keyRef.current, orientation, rtl) || nested ? getMinIndex(listRef, disabledIndicesRef.current) : getMaxIndex(listRef, disabledIndicesRef.current);\n          onNavigate(indexRef.current);\n        }\n      } else if (!isIndexOutOfBounds(listRef, activeIndex)) {\n        indexRef.current = activeIndex;\n        focusItem(listRef, indexRef, forceScrollIntoViewRef.current);\n        forceScrollIntoViewRef.current = false;\n      }\n    }\n  }, [enabled, open, activeIndex, selectedIndex, nested, listRef, orientation, rtl, onNavigate, focusItem, disabledIndicesRef]);\n\n  // Ensure the parent floating element has focus when a nested child closes\n  // to allow arrow key navigation to work after the pointer leaves the child.\n  index(() => {\n    if (!enabled) {\n      return;\n    }\n    if (previousOpenRef.current && !open) {\n      var _tree$nodesRef$curren, _tree$nodesRef$curren2;\n      const parentFloating = tree == null ? void 0 : (_tree$nodesRef$curren = tree.nodesRef.current.find(node => node.id === parentId)) == null ? void 0 : (_tree$nodesRef$curren2 = _tree$nodesRef$curren.context) == null ? void 0 : _tree$nodesRef$curren2.elements.floating;\n      if (parentFloating && !contains(parentFloating, activeElement$1(getDocument(parentFloating)))) {\n        parentFloating.focus({\n          preventScroll: true\n        });\n      }\n    }\n  }, [enabled, open, tree, parentId]);\n  index(() => {\n    keyRef.current = null;\n    previousOnNavigateRef.current = onNavigate;\n    previousOpenRef.current = open;\n  });\n  const hasActiveIndex = activeIndex != null;\n  const item = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    function syncCurrentTarget(currentTarget) {\n      if (!open) return;\n      const index = listRef.current.indexOf(currentTarget);\n      if (index !== -1) {\n        onNavigate(index);\n      }\n    }\n    const props = {\n      onFocus(_ref2) {\n        let {\n          currentTarget\n        } = _ref2;\n        syncCurrentTarget(currentTarget);\n      },\n      onClick: _ref3 => {\n        let {\n          currentTarget\n        } = _ref3;\n        return currentTarget.focus({\n          preventScroll: true\n        });\n      },\n      // Safari\n      ...(focusItemOnHover && {\n        onMouseMove(_ref4) {\n          let {\n            currentTarget\n          } = _ref4;\n          syncCurrentTarget(currentTarget);\n        },\n        onPointerLeave() {\n          if (!isPointerModalityRef.current) {\n            return;\n          }\n          indexRef.current = -1;\n          focusItem(listRef, indexRef);\n\n          // Virtual cursor with VoiceOver on iOS needs this to be flushed\n          // synchronously or there is a glitch that prevents nested\n          // submenus from being accessible.\n          (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync)(() => onNavigate(null));\n          if (!virtual) {\n            var _refs$floating$curren;\n            // This also needs to be sync to prevent fast mouse movements\n            // from leaving behind a stale active item when landing on a\n            // disabled button item.\n            (_refs$floating$curren = refs.floating.current) == null ? void 0 : _refs$floating$curren.focus({\n              preventScroll: true\n            });\n          }\n        }\n      })\n    };\n    return props;\n  }, [open, refs, focusItem, focusItemOnHover, listRef, onNavigate, virtual]);\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    if (!enabled) {\n      return {};\n    }\n    const disabledIndices = disabledIndicesRef.current;\n    function onKeyDown(event) {\n      isPointerModalityRef.current = false;\n      forceSyncFocus.current = true;\n\n      // If the floating element is animating out, ignore navigation. Otherwise,\n      // the `activeIndex` gets set to 0 despite not being open so the next time\n      // the user ArrowDowns, the first item won't be focused.\n      if (!latestOpenRef.current && event.currentTarget === refs.floating.current) {\n        return;\n      }\n      if (nested && isCrossOrientationCloseKey(event.key, orientation, rtl)) {\n        stopEvent(event);\n        onOpenChange(false);\n        if (isHTMLElement(domReference)) {\n          domReference.focus();\n        }\n        return;\n      }\n      const currentIndex = indexRef.current;\n      const minIndex = getMinIndex(listRef, disabledIndices);\n      const maxIndex = getMaxIndex(listRef, disabledIndices);\n      if (event.key === 'Home') {\n        indexRef.current = minIndex;\n        onNavigate(indexRef.current);\n      }\n      if (event.key === 'End') {\n        indexRef.current = maxIndex;\n        onNavigate(indexRef.current);\n      }\n\n      // Grid navigation.\n      if (cols > 1) {\n        const prevIndex = indexRef.current;\n        if (event.key === ARROW_UP) {\n          stopEvent(event);\n          if (prevIndex === -1) {\n            indexRef.current = maxIndex;\n          } else {\n            indexRef.current = findNonDisabledIndex(listRef, {\n              startingIndex: prevIndex,\n              amount: cols,\n              decrement: true,\n              disabledIndices\n            });\n            if (loop && (prevIndex - cols < minIndex || indexRef.current < 0)) {\n              const col = prevIndex % cols;\n              const maxCol = maxIndex % cols;\n              const offset = maxIndex - (maxCol - col);\n              if (maxCol === col) {\n                indexRef.current = maxIndex;\n              } else {\n                indexRef.current = maxCol > col ? offset : offset - cols;\n              }\n            }\n          }\n          if (isIndexOutOfBounds(listRef, indexRef.current)) {\n            indexRef.current = prevIndex;\n          }\n          onNavigate(indexRef.current);\n        }\n        if (event.key === ARROW_DOWN) {\n          stopEvent(event);\n          if (prevIndex === -1) {\n            indexRef.current = minIndex;\n          } else {\n            indexRef.current = findNonDisabledIndex(listRef, {\n              startingIndex: prevIndex,\n              amount: cols,\n              disabledIndices\n            });\n            if (loop && prevIndex + cols > maxIndex) {\n              indexRef.current = findNonDisabledIndex(listRef, {\n                startingIndex: prevIndex % cols - cols,\n                amount: cols,\n                disabledIndices\n              });\n            }\n          }\n          if (isIndexOutOfBounds(listRef, indexRef.current)) {\n            indexRef.current = prevIndex;\n          }\n          onNavigate(indexRef.current);\n        }\n\n        // Remains on the same row/column.\n        if (orientation === 'both') {\n          const prevRow = Math.floor(prevIndex / cols);\n          if (event.key === ARROW_RIGHT) {\n            stopEvent(event);\n            if (prevIndex % cols !== cols - 1) {\n              indexRef.current = findNonDisabledIndex(listRef, {\n                startingIndex: prevIndex,\n                disabledIndices\n              });\n              if (loop && isDifferentRow(indexRef.current, cols, prevRow)) {\n                indexRef.current = findNonDisabledIndex(listRef, {\n                  startingIndex: prevIndex - prevIndex % cols - 1,\n                  disabledIndices\n                });\n              }\n            } else if (loop) {\n              indexRef.current = findNonDisabledIndex(listRef, {\n                startingIndex: prevIndex - prevIndex % cols - 1,\n                disabledIndices\n              });\n            }\n            if (isDifferentRow(indexRef.current, cols, prevRow)) {\n              indexRef.current = prevIndex;\n            }\n          }\n          if (event.key === ARROW_LEFT) {\n            stopEvent(event);\n            if (prevIndex % cols !== 0) {\n              indexRef.current = findNonDisabledIndex(listRef, {\n                startingIndex: prevIndex,\n                disabledIndices,\n                decrement: true\n              });\n              if (loop && isDifferentRow(indexRef.current, cols, prevRow)) {\n                indexRef.current = findNonDisabledIndex(listRef, {\n                  startingIndex: prevIndex + (cols - prevIndex % cols),\n                  decrement: true,\n                  disabledIndices\n                });\n              }\n            } else if (loop) {\n              indexRef.current = findNonDisabledIndex(listRef, {\n                startingIndex: prevIndex + (cols - prevIndex % cols),\n                decrement: true,\n                disabledIndices\n              });\n            }\n            if (isDifferentRow(indexRef.current, cols, prevRow)) {\n              indexRef.current = prevIndex;\n            }\n          }\n          const lastRow = Math.floor(maxIndex / cols) === prevRow;\n          if (isIndexOutOfBounds(listRef, indexRef.current)) {\n            if (loop && lastRow) {\n              indexRef.current = event.key === ARROW_LEFT ? maxIndex : findNonDisabledIndex(listRef, {\n                startingIndex: prevIndex - prevIndex % cols - 1,\n                disabledIndices\n              });\n            } else {\n              indexRef.current = prevIndex;\n            }\n          }\n          onNavigate(indexRef.current);\n          return;\n        }\n      }\n      if (isMainOrientationKey(event.key, orientation)) {\n        stopEvent(event);\n\n        // Reset the index if no item is focused.\n        if (open && !virtual && activeElement$1(event.currentTarget.ownerDocument) === event.currentTarget) {\n          indexRef.current = isMainOrientationToEndKey(event.key, orientation, rtl) ? minIndex : maxIndex;\n          onNavigate(indexRef.current);\n          return;\n        }\n        if (isMainOrientationToEndKey(event.key, orientation, rtl)) {\n          if (loop) {\n            indexRef.current = currentIndex >= maxIndex ? allowEscape && currentIndex !== listRef.current.length ? -1 : minIndex : findNonDisabledIndex(listRef, {\n              startingIndex: currentIndex,\n              disabledIndices\n            });\n          } else {\n            indexRef.current = Math.min(maxIndex, findNonDisabledIndex(listRef, {\n              startingIndex: currentIndex,\n              disabledIndices\n            }));\n          }\n        } else {\n          if (loop) {\n            indexRef.current = currentIndex <= minIndex ? allowEscape && currentIndex !== -1 ? listRef.current.length : maxIndex : findNonDisabledIndex(listRef, {\n              startingIndex: currentIndex,\n              decrement: true,\n              disabledIndices\n            });\n          } else {\n            indexRef.current = Math.max(minIndex, findNonDisabledIndex(listRef, {\n              startingIndex: currentIndex,\n              decrement: true,\n              disabledIndices\n            }));\n          }\n        }\n        if (isIndexOutOfBounds(listRef, indexRef.current)) {\n          onNavigate(null);\n        } else {\n          onNavigate(indexRef.current);\n        }\n      }\n    }\n    function checkVirtualMouse(event) {\n      if (focusItemOnOpen === 'auto' && isVirtualClick(event.nativeEvent)) {\n        focusItemOnOpenRef.current = true;\n      }\n    }\n    function checkVirtualPointer(event) {\n      // `pointerdown` fires first, reset the state then perform the checks.\n      focusItemOnOpenRef.current = focusItemOnOpen;\n      if (focusItemOnOpen === 'auto' && isVirtualPointerEvent(event.nativeEvent)) {\n        focusItemOnOpenRef.current = true;\n      }\n    }\n    const ariaActiveDescendantProp = virtual && open && hasActiveIndex && {\n      'aria-activedescendant': activeId\n    };\n    return {\n      reference: {\n        ...ariaActiveDescendantProp,\n        onKeyDown(event) {\n          isPointerModalityRef.current = false;\n          const isArrowKey = event.key.indexOf('Arrow') === 0;\n          if (virtual && open) {\n            return onKeyDown(event);\n          }\n\n          // If a floating element should not open on arrow key down, avoid\n          // setting `activeIndex` while it's closed.\n          if (!open && !openOnArrowKeyDown && isArrowKey) {\n            return;\n          }\n          const isNavigationKey = isArrowKey || event.key === 'Enter' || event.key === ' ' || event.key === '';\n          if (isNavigationKey) {\n            keyRef.current = event.key;\n          }\n          if (nested) {\n            if (isCrossOrientationOpenKey(event.key, orientation, rtl)) {\n              stopEvent(event);\n              if (open) {\n                indexRef.current = getMinIndex(listRef, disabledIndices);\n                onNavigate(indexRef.current);\n              } else {\n                onOpenChange(true);\n              }\n            }\n            return;\n          }\n          if (isMainOrientationKey(event.key, orientation)) {\n            if (selectedIndex != null) {\n              indexRef.current = selectedIndex;\n            }\n            stopEvent(event);\n            if (!open && openOnArrowKeyDown) {\n              onOpenChange(true);\n            } else {\n              onKeyDown(event);\n            }\n            if (open) {\n              onNavigate(indexRef.current);\n            }\n          }\n        },\n        onFocus() {\n          if (open) {\n            onNavigate(null);\n          }\n        },\n        onPointerDown: checkVirtualPointer,\n        onMouseDown: checkVirtualMouse,\n        onClick: checkVirtualMouse\n      },\n      floating: {\n        'aria-orientation': orientation === 'both' ? undefined : orientation,\n        ...ariaActiveDescendantProp,\n        onKeyDown,\n        onPointerMove() {\n          isPointerModalityRef.current = true;\n        }\n      },\n      item\n    };\n  }, [domReference, refs, activeId, disabledIndicesRef, latestOpenRef, listRef, enabled, orientation, rtl, virtual, open, hasActiveIndex, nested, selectedIndex, openOnArrowKeyDown, allowEscape, cols, loop, focusItemOnOpen, onNavigate, onOpenChange, item]);\n};\n\n/**\n * Merges an array of refs into a single memoized callback ref or `null`.\n * @see https://floating-ui.com/docs/useMergeRefs\n */\nfunction useMergeRefs(refs) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    if (refs.every(ref => ref == null)) {\n      return null;\n    }\n    return value => {\n      refs.forEach(ref => {\n        if (typeof ref === 'function') {\n          ref(value);\n        } else if (ref != null) {\n          ref.current = value;\n        }\n      });\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n}\n\n/**\n * Adds base screen reader props to the reference and floating elements for a\n * given floating element `role`.\n * @see https://floating-ui.com/docs/useRole\n */\nconst useRole = function (_ref, _temp) {\n  let {\n    open\n  } = _ref;\n  let {\n    enabled = true,\n    role = 'dialog'\n  } = _temp === void 0 ? {} : _temp;\n  const rootId = useId();\n  const referenceId = useId();\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    const floatingProps = {\n      id: rootId,\n      role\n    };\n    if (!enabled) {\n      return {};\n    }\n    if (role === 'tooltip') {\n      return {\n        reference: {\n          'aria-describedby': open ? rootId : undefined\n        },\n        floating: floatingProps\n      };\n    }\n    return {\n      reference: {\n        'aria-expanded': open ? 'true' : 'false',\n        'aria-haspopup': role === 'alertdialog' ? 'dialog' : role,\n        'aria-controls': open ? rootId : undefined,\n        ...(role === 'listbox' && {\n          role: 'combobox'\n        }),\n        ...(role === 'menu' && {\n          id: referenceId\n        })\n      },\n      floating: {\n        ...floatingProps,\n        ...(role === 'menu' && {\n          'aria-labelledby': referenceId\n        })\n      }\n    };\n  }, [enabled, role, open, rootId, referenceId]);\n};\n\n// Converts a JS style key like `backgroundColor` to a CSS transition-property\n// like `background-color`.\nconst camelCaseToKebabCase = str => str.replace(/[A-Z]+(?![a-z])|[A-Z]/g, ($, ofs) => (ofs ? '-' : '') + $.toLowerCase());\nfunction useDelayUnmount(open, durationMs) {\n  const [isMounted, setIsMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(open);\n  if (open && !isMounted) {\n    setIsMounted(true);\n  }\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (!open) {\n      const timeout = setTimeout(() => setIsMounted(false), durationMs);\n      return () => clearTimeout(timeout);\n    }\n  }, [open, durationMs]);\n  return isMounted;\n}\n/**\n * Provides a status string to apply CSS transitions to a floating element,\n * correctly handling placement-aware transitions.\n * @see https://floating-ui.com/docs/useTransition#usetransitionstatus\n */\nfunction useTransitionStatus(_ref, _temp) {\n  let {\n    open,\n    elements: {\n      floating\n    }\n  } = _ref;\n  let {\n    duration = 250\n  } = _temp === void 0 ? {} : _temp;\n  const isNumberDuration = typeof duration === 'number';\n  const closeDuration = (isNumberDuration ? duration : duration.close) || 0;\n  const [initiated, setInitiated] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n  const [status, setStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState('unmounted');\n  const isMounted = useDelayUnmount(open, closeDuration);\n\n  // `initiated` check prevents this `setState` call from breaking\n  // <FloatingPortal />. This call is necessary to ensure subsequent opens\n  // after the initial one allows the correct side animation to play when the\n  // placement has changed.\n  index(() => {\n    if (initiated && !isMounted) {\n      setStatus('unmounted');\n    }\n  }, [initiated, isMounted]);\n  index(() => {\n    if (!floating) return;\n    if (open) {\n      setStatus('initial');\n      const frame = requestAnimationFrame(() => {\n        setStatus('open');\n      });\n      return () => {\n        cancelAnimationFrame(frame);\n      };\n    } else {\n      setInitiated(true);\n      setStatus('close');\n    }\n  }, [open, floating]);\n  return {\n    isMounted,\n    status\n  };\n}\n/**\n * Provides styles to apply CSS transitions to a floating element, correctly\n * handling placement-aware transitions. Wrapper around `useTransitionStatus`.\n * @see https://floating-ui.com/docs/useTransition#usetransitionstyles\n */\nfunction useTransitionStyles(context, _temp2) {\n  let {\n    initial: unstable_initial = {\n      opacity: 0\n    },\n    open: unstable_open,\n    close: unstable_close,\n    common: unstable_common,\n    duration = 250\n  } = _temp2 === void 0 ? {} : _temp2;\n  const placement = context.placement;\n  const side = placement.split('-')[0];\n  const [styles, setStyles] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n  const {\n    isMounted,\n    status\n  } = useTransitionStatus(context, {\n    duration\n  });\n  const initialRef = useLatestRef(unstable_initial);\n  const openRef = useLatestRef(unstable_open);\n  const closeRef = useLatestRef(unstable_close);\n  const commonRef = useLatestRef(unstable_common);\n  const isNumberDuration = typeof duration === 'number';\n  const openDuration = (isNumberDuration ? duration : duration.open) || 0;\n  const closeDuration = (isNumberDuration ? duration : duration.close) || 0;\n  index(() => {\n    const fnArgs = {\n      side,\n      placement\n    };\n    const initial = initialRef.current;\n    const close = closeRef.current;\n    const open = openRef.current;\n    const common = commonRef.current;\n    const initialStyles = typeof initial === 'function' ? initial(fnArgs) : initial;\n    const closeStyles = typeof close === 'function' ? close(fnArgs) : close;\n    const commonStyles = typeof common === 'function' ? common(fnArgs) : common;\n    const openStyles = (typeof open === 'function' ? open(fnArgs) : open) || Object.keys(initialStyles).reduce((acc, key) => {\n      acc[key] = '';\n      return acc;\n    }, {});\n    if (status === 'initial' || status === 'unmounted') {\n      setStyles(styles => ({\n        transitionProperty: styles.transitionProperty,\n        ...commonStyles,\n        ...initialStyles\n      }));\n    }\n    if (status === 'open') {\n      setStyles({\n        transitionProperty: Object.keys(openStyles).map(camelCaseToKebabCase).join(','),\n        transitionDuration: openDuration + \"ms\",\n        ...commonStyles,\n        ...openStyles\n      });\n    }\n    if (status === 'close') {\n      const styles = closeStyles || initialStyles;\n      setStyles({\n        transitionProperty: Object.keys(styles).map(camelCaseToKebabCase).join(','),\n        transitionDuration: closeDuration + \"ms\",\n        ...commonStyles,\n        ...styles\n      });\n    }\n  }, [side, placement, closeDuration, closeRef, initialRef, openRef, commonRef, openDuration, status]);\n  return {\n    isMounted,\n    styles\n  };\n}\n\n/**\n * Provides a matching callback that can be used to focus an item as the user\n * types, often used in tandem with `useListNavigation()`.\n * @see https://floating-ui.com/docs/useTypeahead\n */\nconst useTypeahead = function (_ref, _temp) {\n  var _ref2;\n  let {\n    open,\n    dataRef,\n    refs\n  } = _ref;\n  let {\n    listRef,\n    activeIndex,\n    onMatch: unstable_onMatch = () => {},\n    enabled = true,\n    findMatch = null,\n    resetMs = 1000,\n    ignoreKeys = [],\n    selectedIndex = null\n  } = _temp === void 0 ? {\n    listRef: {\n      current: []\n    },\n    activeIndex: null\n  } : _temp;\n  const timeoutIdRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n  const stringRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef('');\n  const prevIndexRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef((_ref2 = selectedIndex != null ? selectedIndex : activeIndex) != null ? _ref2 : -1);\n  const matchIndexRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const onMatch = useEvent(unstable_onMatch);\n  const findMatchRef = useLatestRef(findMatch);\n  const ignoreKeysRef = useLatestRef(ignoreKeys);\n  index(() => {\n    if (open) {\n      clearTimeout(timeoutIdRef.current);\n      matchIndexRef.current = null;\n      stringRef.current = '';\n    }\n  }, [open]);\n  index(() => {\n    // Sync arrow key navigation but not typeahead navigation.\n    if (open && stringRef.current === '') {\n      var _ref3;\n      prevIndexRef.current = (_ref3 = selectedIndex != null ? selectedIndex : activeIndex) != null ? _ref3 : -1;\n    }\n  }, [open, selectedIndex, activeIndex]);\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    if (!enabled) {\n      return {};\n    }\n    function onKeyDown(event) {\n      var _refs$floating$curren;\n      // Correctly scope nested non-portalled floating elements. Since the nested\n      // floating element is inside of the another, we find the closest role\n      // that indicates the floating element scope.\n      const target = getTarget(event.nativeEvent);\n      if (isElement(target) && (activeElement$1(getDocument(target)) !== event.currentTarget ? (_refs$floating$curren = refs.floating.current) != null && _refs$floating$curren.contains(target) ? target.closest('[role=\"dialog\"],[role=\"menu\"],[role=\"listbox\"],[role=\"tree\"],[role=\"grid\"]') !== event.currentTarget : false : !event.currentTarget.contains(target))) {\n        return;\n      }\n      if (stringRef.current.length > 0 && stringRef.current[0] !== ' ') {\n        dataRef.current.typing = true;\n        if (event.key === ' ') {\n          stopEvent(event);\n        }\n      }\n      const listContent = listRef.current;\n      if (listContent == null || ignoreKeysRef.current.includes(event.key) ||\n      // Character key.\n      event.key.length !== 1 ||\n      // Modifier key.\n      event.ctrlKey || event.metaKey || event.altKey) {\n        return;\n      }\n\n      // Bail out if the list contains a word like \"llama\" or \"aaron\". TODO:\n      // allow it in this case, too.\n      const allowRapidSuccessionOfFirstLetter = listContent.every(text => {\n        var _text$, _text$2;\n        return text ? ((_text$ = text[0]) == null ? void 0 : _text$.toLocaleLowerCase()) !== ((_text$2 = text[1]) == null ? void 0 : _text$2.toLocaleLowerCase()) : true;\n      });\n\n      // Allows the user to cycle through items that start with the same letter\n      // in rapid succession.\n      if (allowRapidSuccessionOfFirstLetter && stringRef.current === event.key) {\n        stringRef.current = '';\n        prevIndexRef.current = matchIndexRef.current;\n      }\n      stringRef.current += event.key;\n      clearTimeout(timeoutIdRef.current);\n      timeoutIdRef.current = setTimeout(() => {\n        stringRef.current = '';\n        prevIndexRef.current = matchIndexRef.current;\n        dataRef.current.typing = false;\n      }, resetMs);\n      const prevIndex = prevIndexRef.current;\n      const orderedList = [...listContent.slice((prevIndex || 0) + 1), ...listContent.slice(0, (prevIndex || 0) + 1)];\n      const str = findMatchRef.current ? findMatchRef.current(orderedList, stringRef.current) : orderedList.find(text => (text == null ? void 0 : text.toLocaleLowerCase().indexOf(stringRef.current.toLocaleLowerCase())) === 0);\n      const index = str ? listContent.indexOf(str) : -1;\n      if (index !== -1) {\n        onMatch(index);\n        matchIndexRef.current = index;\n      }\n    }\n    return {\n      reference: {\n        onKeyDown\n      },\n      floating: {\n        onKeyDown\n      }\n    };\n  }, [enabled, dataRef, listRef, resetMs, ignoreKeysRef, findMatchRef, onMatch, refs]);\n};\n\nfunction getArgsWithCustomFloatingHeight(state, height) {\n  return {\n    ...state,\n    rects: {\n      ...state.rects,\n      floating: {\n        ...state.rects.floating,\n        height\n      }\n    }\n  };\n}\n/**\n * Positions the floating element such that an inner element inside\n * of it is anchored to the reference element.\n * @see https://floating-ui.com/docs/inner\n */\nconst inner = props => ({\n  name: 'inner',\n  options: props,\n  async fn(state) {\n    const {\n      listRef,\n      overflowRef,\n      onFallbackChange,\n      offset: innerOffset = 0,\n      index = 0,\n      minItemsVisible = 4,\n      referenceOverflowThreshold = 0,\n      scrollRef,\n      ...detectOverflowOptions\n    } = props;\n    const {\n      rects,\n      elements: {\n        floating\n      }\n    } = state;\n    const item = listRef.current[index];\n    if (true) {\n      if (!state.placement.startsWith('bottom')) {\n        console.warn(['Floating UI: `placement` side must be \"bottom\" when using the', '`inner` middleware.'].join(' '));\n      }\n    }\n    if (!item) {\n      return {};\n    }\n    const nextArgs = {\n      ...state,\n      ...(await (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.offset)(-item.offsetTop - rects.reference.height / 2 - item.offsetHeight / 2 - innerOffset).fn(state))\n    };\n    const el = (scrollRef == null ? void 0 : scrollRef.current) || floating;\n    const overflow = await (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.detectOverflow)(getArgsWithCustomFloatingHeight(nextArgs, el.scrollHeight), detectOverflowOptions);\n    const refOverflow = await (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.detectOverflow)(nextArgs, {\n      ...detectOverflowOptions,\n      elementContext: 'reference'\n    });\n    const diffY = Math.max(0, overflow.top);\n    const nextY = nextArgs.y + diffY;\n    const maxHeight = Math.max(0, el.scrollHeight - diffY - Math.max(0, overflow.bottom));\n    el.style.maxHeight = maxHeight + \"px\";\n    el.scrollTop = diffY;\n\n    // There is not enough space, fallback to standard anchored positioning\n    if (onFallbackChange) {\n      if (el.offsetHeight < item.offsetHeight * Math.min(minItemsVisible, listRef.current.length - 1) - 1 || refOverflow.top >= -referenceOverflowThreshold || refOverflow.bottom >= -referenceOverflowThreshold) {\n        (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync)(() => onFallbackChange(true));\n      } else {\n        (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync)(() => onFallbackChange(false));\n      }\n    }\n    if (overflowRef) {\n      overflowRef.current = await (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.detectOverflow)(getArgsWithCustomFloatingHeight({\n        ...nextArgs,\n        y: nextY\n      }, el.offsetHeight), detectOverflowOptions);\n    }\n    return {\n      y: nextY\n    };\n  }\n});\n/**\n * Changes the `inner` middleware's `offset` upon a `wheel` event to\n * expand the floating element's height, revealing more list items.\n * @see https://floating-ui.com/docs/inner\n */\nconst useInnerOffset = (_ref, _ref2) => {\n  let {\n    open,\n    elements\n  } = _ref;\n  let {\n    enabled = true,\n    overflowRef,\n    scrollRef,\n    onChange: unstable_onChange\n  } = _ref2;\n  const onChange = useEvent(unstable_onChange);\n  const controlledScrollingRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  const prevScrollTopRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const initialOverflowRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (!enabled) {\n      return;\n    }\n    function onWheel(e) {\n      if (e.ctrlKey || !el || overflowRef.current == null) {\n        return;\n      }\n      const dY = e.deltaY;\n      const isAtTop = overflowRef.current.top >= -0.5;\n      const isAtBottom = overflowRef.current.bottom >= -0.5;\n      const remainingScroll = el.scrollHeight - el.clientHeight;\n      const sign = dY < 0 ? -1 : 1;\n      const method = dY < 0 ? 'max' : 'min';\n      if (el.scrollHeight <= el.clientHeight) {\n        return;\n      }\n      if (!isAtTop && dY > 0 || !isAtBottom && dY < 0) {\n        e.preventDefault();\n        (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync)(() => {\n          onChange(d => d + Math[method](dY, remainingScroll * sign));\n        });\n      } else if (/firefox/i.test(getUserAgent())) {\n        // Needed to propagate scrolling during momentum scrolling phase once\n        // it gets limited by the boundary. UX improvement, not critical.\n        el.scrollTop += dY;\n      }\n    }\n    const el = (scrollRef == null ? void 0 : scrollRef.current) || elements.floating;\n    if (open && el) {\n      el.addEventListener('wheel', onWheel);\n\n      // Wait for the position to be ready.\n      requestAnimationFrame(() => {\n        prevScrollTopRef.current = el.scrollTop;\n        if (overflowRef.current != null) {\n          initialOverflowRef.current = {\n            ...overflowRef.current\n          };\n        }\n      });\n      return () => {\n        prevScrollTopRef.current = null;\n        initialOverflowRef.current = null;\n        el.removeEventListener('wheel', onWheel);\n      };\n    }\n  }, [enabled, open, elements.floating, overflowRef, scrollRef, onChange]);\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    if (!enabled) {\n      return {};\n    }\n    return {\n      floating: {\n        onKeyDown() {\n          controlledScrollingRef.current = true;\n        },\n        onWheel() {\n          controlledScrollingRef.current = false;\n        },\n        onPointerMove() {\n          controlledScrollingRef.current = false;\n        },\n        onScroll() {\n          const el = (scrollRef == null ? void 0 : scrollRef.current) || elements.floating;\n          if (!overflowRef.current || !el || !controlledScrollingRef.current) {\n            return;\n          }\n          if (prevScrollTopRef.current !== null) {\n            const scrollDiff = el.scrollTop - prevScrollTopRef.current;\n            if (overflowRef.current.bottom < -0.5 && scrollDiff < -1 || overflowRef.current.top < -0.5 && scrollDiff > 1) {\n              (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync)(() => onChange(d => d + scrollDiff));\n            }\n          }\n\n          // [Firefox] Wait for the height change to have been applied.\n          requestAnimationFrame(() => {\n            prevScrollTopRef.current = el.scrollTop;\n          });\n        }\n      }\n    };\n  }, [enabled, overflowRef, elements.floating, scrollRef, onChange]);\n};\n\nfunction isPointInPolygon(point, polygon) {\n  const [x, y] = point;\n  let isInside = false;\n  const length = polygon.length;\n  for (let i = 0, j = length - 1; i < length; j = i++) {\n    const [xi, yi] = polygon[i] || [0, 0];\n    const [xj, yj] = polygon[j] || [0, 0];\n    const intersect = yi >= y !== yj >= y && x <= (xj - xi) * (y - yi) / (yj - yi) + xi;\n    if (intersect) {\n      isInside = !isInside;\n    }\n  }\n  return isInside;\n}\nfunction isInside(point, rect) {\n  return point[0] >= rect.x && point[0] <= rect.x + rect.width && point[1] >= rect.y && point[1] <= rect.y + rect.height;\n}\nfunction safePolygon(_temp) {\n  let {\n    restMs = 0,\n    buffer = 0.5,\n    blockPointerEvents = false\n  } = _temp === void 0 ? {} : _temp;\n  let timeoutId;\n  let isInsideRect = false;\n  let hasLanded = false;\n  const fn = _ref => {\n    let {\n      x,\n      y,\n      placement,\n      elements,\n      onClose,\n      nodeId,\n      tree\n    } = _ref;\n    return function onMouseMove(event) {\n      function close() {\n        clearTimeout(timeoutId);\n        onClose();\n      }\n      clearTimeout(timeoutId);\n      if (!elements.domReference || !elements.floating || placement == null || x == null || y == null) {\n        return;\n      }\n      const {\n        clientX,\n        clientY\n      } = event;\n      const clientPoint = [clientX, clientY];\n      const target = getTarget(event);\n      const isLeave = event.type === 'mouseleave';\n      const isOverFloatingEl = contains(elements.floating, target);\n      const isOverReferenceEl = contains(elements.domReference, target);\n      const refRect = elements.domReference.getBoundingClientRect();\n      const rect = elements.floating.getBoundingClientRect();\n      const side = placement.split('-')[0];\n      const cursorLeaveFromRight = x > rect.right - rect.width / 2;\n      const cursorLeaveFromBottom = y > rect.bottom - rect.height / 2;\n      const isOverReferenceRect = isInside(clientPoint, refRect);\n      if (isOverFloatingEl) {\n        hasLanded = true;\n        if (!isLeave) {\n          return;\n        }\n      }\n      if (isOverReferenceEl) {\n        hasLanded = false;\n      }\n      if (isOverReferenceEl && !isLeave) {\n        hasLanded = true;\n        return;\n      }\n\n      // Prevent overlapping floating element from being stuck in an open-close\n      // loop: https://github.com/floating-ui/floating-ui/issues/1910\n      if (isLeave && isElement(event.relatedTarget) && contains(elements.floating, event.relatedTarget)) {\n        return;\n      }\n\n      // If any nested child is open, abort.\n      if (tree && getChildren(tree.nodesRef.current, nodeId).some(_ref2 => {\n        let {\n          context\n        } = _ref2;\n        return context == null ? void 0 : context.open;\n      })) {\n        return;\n      }\n\n      // If the pointer is leaving from the opposite side, the \"buffer\" logic\n      // creates a point where the floating element remains open, but should be\n      // ignored.\n      // A constant of 1 handles floating point rounding errors.\n      if (side === 'top' && y >= refRect.bottom - 1 || side === 'bottom' && y <= refRect.top + 1 || side === 'left' && x >= refRect.right - 1 || side === 'right' && x <= refRect.left + 1) {\n        return close();\n      }\n\n      // Ignore when the cursor is within the rectangular trough between the\n      // two elements. Since the triangle is created from the cursor point,\n      // which can start beyond the ref element's edge, traversing back and\n      // forth from the ref to the floating element can cause it to close. This\n      // ensures it always remains open in that case.\n      let rectPoly = [];\n      switch (side) {\n        case 'top':\n          rectPoly = [[rect.left, refRect.top + 1], [rect.left, rect.bottom - 1], [rect.right, rect.bottom - 1], [rect.right, refRect.top + 1]];\n          isInsideRect = clientX >= rect.left && clientX <= rect.right && clientY >= rect.top && clientY <= refRect.top + 1;\n          break;\n        case 'bottom':\n          rectPoly = [[rect.left, rect.top + 1], [rect.left, refRect.bottom - 1], [rect.right, refRect.bottom - 1], [rect.right, rect.top + 1]];\n          isInsideRect = clientX >= rect.left && clientX <= rect.right && clientY >= refRect.bottom - 1 && clientY <= rect.bottom;\n          break;\n        case 'left':\n          rectPoly = [[rect.right - 1, rect.bottom], [rect.right - 1, rect.top], [refRect.left + 1, rect.top], [refRect.left + 1, rect.bottom]];\n          isInsideRect = clientX >= rect.left && clientX <= refRect.left + 1 && clientY >= rect.top && clientY <= rect.bottom;\n          break;\n        case 'right':\n          rectPoly = [[refRect.right - 1, rect.bottom], [refRect.right - 1, rect.top], [rect.left + 1, rect.top], [rect.left + 1, rect.bottom]];\n          isInsideRect = clientX >= refRect.right - 1 && clientX <= rect.right && clientY >= rect.top && clientY <= rect.bottom;\n          break;\n      }\n      function getPolygon(_ref3) {\n        let [x, y] = _ref3;\n        const isFloatingWider = rect.width > refRect.width;\n        const isFloatingTaller = rect.height > refRect.height;\n        switch (side) {\n          case 'top':\n            {\n              const cursorPointOne = [isFloatingWider ? x + buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y + buffer + 1];\n              const cursorPointTwo = [isFloatingWider ? x - buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y + buffer + 1];\n              const commonPoints = [[rect.left, cursorLeaveFromRight ? rect.bottom - buffer : isFloatingWider ? rect.bottom - buffer : rect.top], [rect.right, cursorLeaveFromRight ? isFloatingWider ? rect.bottom - buffer : rect.top : rect.bottom - buffer]];\n              return [cursorPointOne, cursorPointTwo, ...commonPoints];\n            }\n          case 'bottom':\n            {\n              const cursorPointOne = [isFloatingWider ? x + buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y - buffer];\n              const cursorPointTwo = [isFloatingWider ? x - buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y - buffer];\n              const commonPoints = [[rect.left, cursorLeaveFromRight ? rect.top + buffer : isFloatingWider ? rect.top + buffer : rect.bottom], [rect.right, cursorLeaveFromRight ? isFloatingWider ? rect.top + buffer : rect.bottom : rect.top + buffer]];\n              return [cursorPointOne, cursorPointTwo, ...commonPoints];\n            }\n          case 'left':\n            {\n              const cursorPointOne = [x + buffer + 1, isFloatingTaller ? y + buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];\n              const cursorPointTwo = [x + buffer + 1, isFloatingTaller ? y - buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];\n              const commonPoints = [[cursorLeaveFromBottom ? rect.right - buffer : isFloatingTaller ? rect.right - buffer : rect.left, rect.top], [cursorLeaveFromBottom ? isFloatingTaller ? rect.right - buffer : rect.left : rect.right - buffer, rect.bottom]];\n              return [...commonPoints, cursorPointOne, cursorPointTwo];\n            }\n          case 'right':\n            {\n              const cursorPointOne = [x - buffer, isFloatingTaller ? y + buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];\n              const cursorPointTwo = [x - buffer, isFloatingTaller ? y - buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];\n              const commonPoints = [[cursorLeaveFromBottom ? rect.left + buffer : isFloatingTaller ? rect.left + buffer : rect.right, rect.top], [cursorLeaveFromBottom ? isFloatingTaller ? rect.left + buffer : rect.right : rect.left + buffer, rect.bottom]];\n              return [cursorPointOne, cursorPointTwo, ...commonPoints];\n            }\n        }\n      }\n      const poly = isInsideRect ? rectPoly : getPolygon([x, y]);\n      if (isInsideRect) {\n        return;\n      } else if (hasLanded && !isOverReferenceRect) {\n        return close();\n      }\n      if (!isPointInPolygon([clientX, clientY], poly)) {\n        close();\n      } else if (restMs && !hasLanded) {\n        timeoutId = setTimeout(close, restMs);\n      }\n    };\n  };\n  fn.__options = {\n    blockPointerEvents\n  };\n  return fn;\n}\n\n/**\n * Provides data to position a floating element and context to add interactions.\n * @see https://floating-ui.com/docs/react\n */\nfunction useFloating(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    open = false,\n    onOpenChange: unstable_onOpenChange,\n    nodeId\n  } = options;\n  const position = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_2__.useFloating)(options);\n  const tree = useFloatingTree();\n  const domReferenceRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const dataRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef({});\n  const events = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => createPubSub())[0];\n  const [domReference, setDomReference] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n  const setPositionReference = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(node => {\n    const positionReference = isElement(node) ? {\n      getBoundingClientRect: () => node.getBoundingClientRect(),\n      contextElement: node\n    } : node;\n    position.refs.setReference(positionReference);\n  }, [position.refs]);\n  const setReference = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(node => {\n    if (isElement(node) || node === null) {\n      domReferenceRef.current = node;\n      setDomReference(node);\n    }\n\n    // Backwards-compatibility for passing a virtual element to `reference`\n    // after it has set the DOM reference.\n    if (isElement(position.refs.reference.current) || position.refs.reference.current === null ||\n    // Don't allow setting virtual elements using the old technique back to\n    // `null` to support `positionReference` + an unstable `reference`\n    // callback ref.\n    node !== null && !isElement(node)) {\n      position.refs.setReference(node);\n    }\n  }, [position.refs]);\n  const refs = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n    ...position.refs,\n    setReference,\n    setPositionReference,\n    domReference: domReferenceRef\n  }), [position.refs, setReference, setPositionReference]);\n  const elements = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n    ...position.elements,\n    domReference: domReference\n  }), [position.elements, domReference]);\n  const onOpenChange = useEvent(unstable_onOpenChange);\n  const context = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n    ...position,\n    refs,\n    elements,\n    dataRef,\n    nodeId,\n    events,\n    open,\n    onOpenChange\n  }), [position, nodeId, events, open, onOpenChange, refs, elements]);\n  index(() => {\n    const node = tree == null ? void 0 : tree.nodesRef.current.find(node => node.id === nodeId);\n    if (node) {\n      node.context = context;\n    }\n  });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n    ...position,\n    context,\n    refs,\n    reference: setReference,\n    positionReference: setPositionReference\n  }), [position, refs, context, setReference, setPositionReference]);\n}\n\nfunction mergeProps(userProps, propsList, elementKey) {\n  const map = new Map();\n  return {\n    ...(elementKey === 'floating' && {\n      tabIndex: -1\n    }),\n    ...userProps,\n    ...propsList.map(value => value ? value[elementKey] : null).concat(userProps).reduce((acc, props) => {\n      if (!props) {\n        return acc;\n      }\n      Object.entries(props).forEach(_ref => {\n        let [key, value] = _ref;\n        if (key.indexOf('on') === 0) {\n          if (!map.has(key)) {\n            map.set(key, []);\n          }\n          if (typeof value === 'function') {\n            var _map$get;\n            (_map$get = map.get(key)) == null ? void 0 : _map$get.push(value);\n            acc[key] = function () {\n              var _map$get2;\n              for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n                args[_key] = arguments[_key];\n              }\n              (_map$get2 = map.get(key)) == null ? void 0 : _map$get2.forEach(fn => fn(...args));\n            };\n          }\n        } else {\n          acc[key] = value;\n        }\n      });\n      return acc;\n    }, {})\n  };\n}\nconst useInteractions = function (propsList) {\n  if (propsList === void 0) {\n    propsList = [];\n  }\n  // The dependencies are a dynamic array, so we can't use the linter's\n  // suggestion to add it to the deps array.\n  const deps = propsList;\n  const getReferenceProps = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(userProps => mergeProps(userProps, propsList, 'reference'),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  deps);\n  const getFloatingProps = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(userProps => mergeProps(userProps, propsList, 'floating'),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  deps);\n  const getItemProps = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(userProps => mergeProps(userProps, propsList, 'item'),\n  // Granularly check for `item` changes, because the `getItemProps` getter\n  // should be as referentially stable as possible since it may be passed as\n  // a prop to many components. All `item` key values must therefore be\n  // memoized.\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  propsList.map(key => key == null ? void 0 : key.item));\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n    getReferenceProps,\n    getFloatingProps,\n    getItemProps\n  }), [getReferenceProps, getFloatingProps, getItemProps]);\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/.pnpm/@floating-ui+react@0.19.2_r_d515816f1ed88aedf415600154767d9b/node_modules/@floating-ui/react/dist/floating-ui.react.esm.js\n");

/***/ })

};
;
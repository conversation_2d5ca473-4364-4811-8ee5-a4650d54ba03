import React, { useState } from 'react';
import { 
  HomeIcon, 
  CogIcon, 
  ChartBarIcon, 
  ShieldCheckIcon, 
  CloudIcon, 
  BoltIcon 
} from '@heroicons/react/24/solid';

export const Footer: React.FC = () => {
  const [activeButton, setActiveButton] = useState<string | null>(null);

  const footerButtons = [
    { id: 'home', icon: HomeIcon, label: 'Home', color: 'text-blue-400' },
    { id: 'analytics', icon: ChartBarIcon, label: 'Analytics', color: 'text-green-400' },
    { id: 'security', icon: ShieldCheckIcon, label: 'Security', color: 'text-cyan-400' },
    { id: 'cloud', icon: CloudIcon, label: 'Cloud', color: 'text-purple-400' },
    { id: 'performance', icon: BoltIcon, label: 'Performance', color: 'text-yellow-400' },
    { id: 'settings', icon: CogIcon, label: 'Settings', color: 'text-red-400' }
  ];

  const handleButtonClick = (buttonId: string) => {
    setActiveButton(buttonId);
    // Add haptic feedback simulation
    if (navigator.vibrate) {
      navigator.vibrate(50);
    }
    
    // Reset active state after animation
    setTimeout(() => setActiveButton(null), 200);
  };

  return (
    <footer className="bg-panel-bg border border-gray-700/50 responsive-border-radius responsive-spacing-sm flex items-center justify-between responsive-text-sm w-full">
      {/* Left Section - System Status */}
      <div className="flex items-center responsive-gap">
        <div className="flex items-center responsive-gap-sm">
          <div
            className="bg-green-400 rounded-full animate-pulse"
            style={{
              width: 'calc(var(--base-icon-size) * 0.3)',
              height: 'calc(var(--base-icon-size) * 0.3)'
            }}
          />
          <div className="responsive-text-xs text-gray-400">SYSTEM ONLINE</div>
        </div>
        <div className="flex items-center responsive-gap-sm">
          <div
            className="bg-blue-400 rounded-full"
            style={{
              width: 'calc(var(--base-icon-size) * 0.3)',
              height: 'calc(var(--base-icon-size) * 0.3)'
            }}
          />
          <div className="responsive-text-xs text-gray-400">SECURE CONNECTION</div>
        </div>
      </div>

      {/* Center Section - iOS Style Buttons */}
      <div className="flex items-center responsive-gap-sm">
        {footerButtons.map((button) => {
          const IconComponent = button.icon;
          const isActive = activeButton === button.id;

          return (
            <button
              key={button.id}
              onClick={() => handleButtonClick(button.id)}
              className={`
                relative group flex flex-col items-center justify-center
                responsive-border-radius transition-all duration-200 ease-out
                ${isActive
                  ? 'bg-gray-700/80 scale-95 shadow-inner'
                  : 'bg-gray-800/40 hover:bg-gray-700/60 hover:scale-105'
                }
                border border-gray-600/30 hover:border-gray-500/50
                backdrop-blur-sm
              `}
              style={{
                width: 'calc(var(--base-button-height) * 1.2)',
                height: 'calc(var(--base-button-height) * 1.2)'
              }}
              title={button.label}
            >
              {/* Glow effect */}
              <div className={`
                absolute inset-0 responsive-border-radius opacity-0 group-hover:opacity-20 transition-opacity duration-300
                bg-gradient-to-br from-white to-transparent
              `} />

              {/* Icon */}
              <IconComponent
                className={`
                  transition-all duration-200
                  ${isActive ? 'scale-90' : 'group-hover:scale-110'}
                  ${button.color}
                `}
                style={{
                  width: 'calc(var(--base-icon-size) * 0.8)',
                  height: 'calc(var(--base-icon-size) * 0.8)'
                }}
              />
              
              {/* Active indicator */}
              {isActive && (
                <div className="absolute -bottom-1 w-1 h-1 bg-cyan-400 rounded-full animate-pulse" />
              )}
            </button>
          );
        })}
      </div>

      {/* Right Section - System Info */}
      <div className="flex items-center gap-4">
        <div className="text-xs text-gray-400">
          CPU: <span className="text-cyan-400">45%</span>
        </div>
        <div className="text-xs text-gray-400">
          RAM: <span className="text-green-400">68%</span>
        </div>
        <div className="text-xs text-gray-400">
          {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </div>
      </div>
    </footer>
  );
};

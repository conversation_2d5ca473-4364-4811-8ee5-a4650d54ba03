
export type SystemDiagnosticsData = {
    cpuLoad: number;
    ramUsage: number;
};

export type NetworkStatusData = {
    status: string;
    latency: number;
    download: number;
    upload: number;
};

export const getToolData = () => {
    const diagnostics: SystemDiagnosticsData = {
        cpuLoad: 68,
        ramUsage: 84
    };
    const network: NetworkStatusData = {
        status: "Connected",
        latency: 12,
        download: 894.2,
        upload: 45.6
    };
    return { diagnostics, network };
}

export const runSpeedTest = async () => {
    // Simulate network delay for a realistic speed test
    await new Promise(resolve => setTimeout(resolve, 1500));
    return {
        download: Math.round(Math.random() * (950 - 700) + 700),
        upload: Math.round(Math.random() * (60 - 30) + 30),
        latency: Math.round(Math.random() * (20-8) + 8)
    }
}

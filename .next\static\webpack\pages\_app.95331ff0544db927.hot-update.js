"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[1]!./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[2]!./styles/globals.css":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[1]!./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[2]!./styles/globals.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_pnpm_next_14_2_3_react_dom_18_2_0_react_18_2_0_react_18_2_0_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_pnpm_next_14_2_3_react_dom_18_2_0_react_18_2_0_react_18_2_0_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_pnpm_next_14_2_3_react_dom_18_2_0_react_18_2_0_react_18_2_0_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_pnpm_next_14_2_3_react_dom_18_2_0_react_18_2_0_react_18_2_0_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');\\n\\n*, ::before, ::after{\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n::backdrop{\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*/\\n\\n/*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\n\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\"; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\n\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\n.container{\\n  width: 100%;\\n}\\n@media (min-width: 640px){\\n\\n  .container{\\n    max-width: 640px;\\n  }\\n}\\n@media (min-width: 768px){\\n\\n  .container{\\n    max-width: 768px;\\n  }\\n}\\n@media (min-width: 1024px){\\n\\n  .container{\\n    max-width: 1024px;\\n  }\\n}\\n@media (min-width: 1280px){\\n\\n  .container{\\n    max-width: 1280px;\\n  }\\n}\\n@media (min-width: 1536px){\\n\\n  .container{\\n    max-width: 1536px;\\n  }\\n}\\n.fixed{\\n  position: fixed;\\n}\\n.absolute{\\n  position: absolute;\\n}\\n.relative{\\n  position: relative;\\n}\\n.inset-0{\\n  inset: 0px;\\n}\\n.-bottom-1{\\n  bottom: -0.25rem;\\n}\\n.-left-1\\\\/2{\\n  left: -50%;\\n}\\n.-right-1{\\n  right: -0.25rem;\\n}\\n.-top-1{\\n  top: -0.25rem;\\n}\\n.-top-1\\\\/2{\\n  top: -50%;\\n}\\n.left-0{\\n  left: 0px;\\n}\\n.left-1\\\\/2{\\n  left: 50%;\\n}\\n.top-1\\\\/2{\\n  top: 50%;\\n}\\n.z-10{\\n  z-index: 10;\\n}\\n.z-50{\\n  z-index: 50;\\n}\\n.col-span-2{\\n  grid-column: span 2 / span 2;\\n}\\n.mx-auto{\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n.\\\\!mt-6{\\n  margin-top: 1.5rem !important;\\n}\\n.mb-1{\\n  margin-bottom: 0.25rem;\\n}\\n.mb-2{\\n  margin-bottom: 0.5rem;\\n}\\n.mb-3{\\n  margin-bottom: 0.75rem;\\n}\\n.mb-4{\\n  margin-bottom: 1rem;\\n}\\n.mb-6{\\n  margin-bottom: 1.5rem;\\n}\\n.mb-8{\\n  margin-bottom: 2rem;\\n}\\n.mr-2{\\n  margin-right: 0.5rem;\\n}\\n.mt-1{\\n  margin-top: 0.25rem;\\n}\\n.mt-2{\\n  margin-top: 0.5rem;\\n}\\n.mt-3{\\n  margin-top: 0.75rem;\\n}\\n.mt-4{\\n  margin-top: 1rem;\\n}\\n.inline-block{\\n  display: inline-block;\\n}\\n.flex{\\n  display: flex;\\n}\\n.table{\\n  display: table;\\n}\\n.grid{\\n  display: grid;\\n}\\n.hidden{\\n  display: none;\\n}\\n.h-0\\\\.5{\\n  height: 0.125rem;\\n}\\n.h-1{\\n  height: 0.25rem;\\n}\\n.h-10{\\n  height: 2.5rem;\\n}\\n.h-12{\\n  height: 3rem;\\n}\\n.h-14{\\n  height: 3.5rem;\\n}\\n.h-16{\\n  height: 4rem;\\n}\\n.h-2{\\n  height: 0.5rem;\\n}\\n.h-24{\\n  height: 6rem;\\n}\\n.h-3{\\n  height: 0.75rem;\\n}\\n.h-32{\\n  height: 8rem;\\n}\\n.h-4{\\n  height: 1rem;\\n}\\n.h-40{\\n  height: 10rem;\\n}\\n.h-48{\\n  height: 12rem;\\n}\\n.h-5{\\n  height: 1.25rem;\\n}\\n.h-6{\\n  height: 1.5rem;\\n}\\n.h-64{\\n  height: 16rem;\\n}\\n.h-8{\\n  height: 2rem;\\n}\\n.h-\\\\[200\\\\%\\\\]{\\n  height: 200%;\\n}\\n.h-full{\\n  height: 100%;\\n}\\n.max-h-64{\\n  max-height: 16rem;\\n}\\n.min-h-0{\\n  min-height: 0px;\\n}\\n.w-1{\\n  width: 0.25rem;\\n}\\n.w-1\\\\/4{\\n  width: 25%;\\n}\\n.w-10{\\n  width: 2.5rem;\\n}\\n.w-12{\\n  width: 3rem;\\n}\\n.w-14{\\n  width: 3.5rem;\\n}\\n.w-16{\\n  width: 4rem;\\n}\\n.w-2{\\n  width: 0.5rem;\\n}\\n.w-24{\\n  width: 6rem;\\n}\\n.w-3{\\n  width: 0.75rem;\\n}\\n.w-32{\\n  width: 8rem;\\n}\\n.w-4{\\n  width: 1rem;\\n}\\n.w-5{\\n  width: 1.25rem;\\n}\\n.w-6{\\n  width: 1.5rem;\\n}\\n.w-8{\\n  width: 2rem;\\n}\\n.w-\\\\[200\\\\%\\\\]{\\n  width: 200%;\\n}\\n.w-full{\\n  width: 100%;\\n}\\n.max-w-md{\\n  max-width: 28rem;\\n}\\n.max-w-sm{\\n  max-width: 24rem;\\n}\\n.flex-1{\\n  flex: 1 1 0%;\\n}\\n.flex-shrink-0{\\n  flex-shrink: 0;\\n}\\n.flex-grow{\\n  flex-grow: 1;\\n}\\n.-translate-x-1\\\\/2{\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.-translate-y-1\\\\/2{\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.translate-x-0{\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.translate-x-5{\\n  --tw-translate-x: 1.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.scale-105{\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.scale-90{\\n  --tw-scale-x: .9;\\n  --tw-scale-y: .9;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.scale-95{\\n  --tw-scale-x: .95;\\n  --tw-scale-y: .95;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.transform{\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n@keyframes pulse{\\n\\n  50%{\\n    opacity: .5;\\n  }\\n}\\n.animate-pulse{\\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\n@keyframes spin{\\n\\n  to{\\n    transform: rotate(360deg);\\n  }\\n}\\n.animate-spin{\\n  animation: spin 1s linear infinite;\\n}\\n.cursor-pointer{\\n  cursor: pointer;\\n}\\n.resize{\\n  resize: both;\\n}\\n.grid-cols-1{\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\n.grid-cols-2{\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\n.grid-cols-3{\\n  grid-template-columns: repeat(3, minmax(0, 1fr));\\n}\\n.grid-cols-4{\\n  grid-template-columns: repeat(4, minmax(0, 1fr));\\n}\\n.flex-col{\\n  flex-direction: column;\\n}\\n.items-start{\\n  align-items: flex-start;\\n}\\n.items-end{\\n  align-items: flex-end;\\n}\\n.items-center{\\n  align-items: center;\\n}\\n.justify-end{\\n  justify-content: flex-end;\\n}\\n.justify-center{\\n  justify-content: center;\\n}\\n.justify-between{\\n  justify-content: space-between;\\n}\\n.justify-around{\\n  justify-content: space-around;\\n}\\n.gap-1{\\n  gap: 0.25rem;\\n}\\n.gap-2{\\n  gap: 0.5rem;\\n}\\n.gap-3{\\n  gap: 0.75rem;\\n}\\n.gap-4{\\n  gap: 1rem;\\n}\\n.gap-6{\\n  gap: 1.5rem;\\n}\\n.space-y-1 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\n.space-y-2 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-3 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\n.space-y-4 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\n.overflow-auto{\\n  overflow: auto;\\n}\\n.overflow-hidden{\\n  overflow: hidden;\\n}\\n.overflow-y-auto{\\n  overflow-y: auto;\\n}\\n.truncate{\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.rounded-full{\\n  border-radius: 9999px;\\n}\\n.rounded-lg{\\n  border-radius: 0.5rem;\\n}\\n.rounded-md{\\n  border-radius: 0.375rem;\\n}\\n.rounded-sm{\\n  border-radius: 0.125rem;\\n}\\n.rounded-xl{\\n  border-radius: 0.75rem;\\n}\\n.rounded-r-full{\\n  border-top-right-radius: 9999px;\\n  border-bottom-right-radius: 9999px;\\n}\\n.border{\\n  border-width: 1px;\\n}\\n.border-2{\\n  border-width: 2px;\\n}\\n.border-b-2{\\n  border-bottom-width: 2px;\\n}\\n.border-t{\\n  border-top-width: 1px;\\n}\\n.border-blue-400\\\\/30{\\n  border-color: rgb(96 165 250 / 0.3);\\n}\\n.border-blue-500\\\\/30{\\n  border-color: rgb(59 130 246 / 0.3);\\n}\\n.border-brand-cyan{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(0 255 255 / var(--tw-border-opacity, 1));\\n}\\n.border-cyan-400{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(34 211 238 / var(--tw-border-opacity, 1));\\n}\\n.border-cyan-400\\\\/30{\\n  border-color: rgb(34 211 238 / 0.3);\\n}\\n.border-cyan-500\\\\/30{\\n  border-color: rgb(6 182 212 / 0.3);\\n}\\n.border-cyan-500\\\\/50{\\n  border-color: rgb(6 182 212 / 0.5);\\n}\\n.border-gray-400\\\\/30{\\n  border-color: rgb(156 163 175 / 0.3);\\n}\\n.border-gray-500\\\\/40{\\n  border-color: rgb(107 114 128 / 0.4);\\n}\\n.border-gray-600{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-600\\\\/30{\\n  border-color: rgb(75 85 99 / 0.3);\\n}\\n.border-gray-700{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-700\\\\/30{\\n  border-color: rgb(55 65 81 / 0.3);\\n}\\n.border-gray-700\\\\/50{\\n  border-color: rgb(55 65 81 / 0.5);\\n}\\n.border-green-400\\\\/30{\\n  border-color: rgb(74 222 128 / 0.3);\\n}\\n.border-green-500\\\\/30{\\n  border-color: rgb(34 197 94 / 0.3);\\n}\\n.border-pink-400\\\\/30{\\n  border-color: rgb(244 114 182 / 0.3);\\n}\\n.border-pink-500\\\\/90{\\n  border-color: rgb(236 72 153 / 0.9);\\n}\\n.border-purple-400\\\\/30{\\n  border-color: rgb(192 132 252 / 0.3);\\n}\\n.border-purple-400\\\\/40{\\n  border-color: rgb(192 132 252 / 0.4);\\n}\\n.border-purple-500\\\\/30{\\n  border-color: rgb(168 85 247 / 0.3);\\n}\\n.border-red-400\\\\/30{\\n  border-color: rgb(248 113 113 / 0.3);\\n}\\n.border-red-500\\\\/30{\\n  border-color: rgb(239 68 68 / 0.3);\\n}\\n.border-yellow-400\\\\/30{\\n  border-color: rgb(250 204 21 / 0.3);\\n}\\n.border-yellow-400\\\\/50{\\n  border-color: rgb(250 204 21 / 0.5);\\n}\\n.border-yellow-500\\\\/30{\\n  border-color: rgb(234 179 8 / 0.3);\\n}\\n.bg-black\\\\/60{\\n  background-color: rgb(0 0 0 / 0.6);\\n}\\n.bg-black\\\\/70{\\n  background-color: rgb(0 0 0 / 0.7);\\n}\\n.bg-blue-400{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-500\\\\/20{\\n  background-color: rgb(59 130 246 / 0.2);\\n}\\n.bg-brand-cyan{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-brand-cyan\\\\/20{\\n  background-color: rgb(0 255 255 / 0.2);\\n}\\n.bg-brand-pink\\\\/20{\\n  background-color: rgb(255 0 127 / 0.2);\\n}\\n.bg-container-bg{\\n  background-color: rgba(10, 20, 35, 0.7);\\n}\\n.bg-cyan-400{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 211 238 / var(--tw-bg-opacity, 1));\\n}\\n.bg-cyan-400\\\\/10{\\n  background-color: rgb(34 211 238 / 0.1);\\n}\\n.bg-cyan-400\\\\/20{\\n  background-color: rgb(34 211 238 / 0.2);\\n}\\n.bg-cyan-400\\\\/50{\\n  background-color: rgb(34 211 238 / 0.5);\\n}\\n.bg-cyan-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(6 182 212 / var(--tw-bg-opacity, 1));\\n}\\n.bg-cyan-500\\\\/20{\\n  background-color: rgb(6 182 212 / 0.2);\\n}\\n.bg-cyan-500\\\\/30{\\n  background-color: rgb(6 182 212 / 0.3);\\n}\\n.bg-cyan-500\\\\/50{\\n  background-color: rgb(6 182 212 / 0.5);\\n}\\n.bg-emerald-500\\\\/20{\\n  background-color: rgb(16 185 129 / 0.2);\\n}\\n.bg-gray-200\\\\/20{\\n  background-color: rgb(229 231 235 / 0.2);\\n}\\n.bg-gray-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-500\\\\/20{\\n  background-color: rgb(107 114 128 / 0.2);\\n}\\n.bg-gray-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-700{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-700\\\\/30{\\n  background-color: rgb(55 65 81 / 0.3);\\n}\\n.bg-gray-700\\\\/50{\\n  background-color: rgb(55 65 81 / 0.5);\\n}\\n.bg-gray-700\\\\/60{\\n  background-color: rgb(55 65 81 / 0.6);\\n}\\n.bg-gray-700\\\\/80{\\n  background-color: rgb(55 65 81 / 0.8);\\n}\\n.bg-gray-800\\\\/40{\\n  background-color: rgb(31 41 55 / 0.4);\\n}\\n.bg-green-400{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-500\\\\/20{\\n  background-color: rgb(34 197 94 / 0.2);\\n}\\n.bg-panel-bg{\\n  background-color: rgba(25, 40, 60, 0.6);\\n}\\n.bg-pink-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(236 72 153 / var(--tw-bg-opacity, 1));\\n}\\n.bg-pink-500\\\\/20{\\n  background-color: rgb(236 72 153 / 0.2);\\n}\\n.bg-pink-500\\\\/80{\\n  background-color: rgb(236 72 153 / 0.8);\\n}\\n.bg-purple-500\\\\/20{\\n  background-color: rgb(168 85 247 / 0.2);\\n}\\n.bg-red-500\\\\/20{\\n  background-color: rgb(239 68 68 / 0.2);\\n}\\n.bg-white{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-white\\\\/20{\\n  background-color: rgb(255 255 255 / 0.2);\\n}\\n.bg-yellow-400{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-500\\\\/20{\\n  background-color: rgb(234 179 8 / 0.2);\\n}\\n.bg-\\\\[radial-gradient\\\\(circle_at_center\\\\2c _rgba\\\\(255\\\\2c 0\\\\2c 127\\\\2c 0\\\\.5\\\\)\\\\2c _transparent_40\\\\%\\\\)\\\\]{\\n  background-image: radial-gradient(circle at center, rgba(255,0,127,0.5), transparent 40%);\\n}\\n.bg-gradient-to-br{\\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\\n}\\n.bg-gradient-to-r{\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n}\\n.from-\\\\[\\\\#FF007F\\\\]{\\n  --tw-gradient-from: #FF007F var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(255 0 127 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-400\\\\/20{\\n  --tw-gradient-from: rgb(96 165 250 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-500{\\n  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-900\\\\/20{\\n  --tw-gradient-from: rgb(30 58 138 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-cyan-400{\\n  --tw-gradient-from: #22d3ee var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 211 238 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-cyan-400\\\\/20{\\n  --tw-gradient-from: rgb(34 211 238 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 211 238 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-cyan-500{\\n  --tw-gradient-from: #06b6d4 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(6 182 212 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-cyan-900\\\\/20{\\n  --tw-gradient-from: rgb(22 78 99 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(22 78 99 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-gray-800\\\\/40{\\n  --tw-gradient-from: rgb(31 41 55 / 0.4) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-gray-800\\\\/60{\\n  --tw-gradient-from: rgb(31 41 55 / 0.6) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-gray-900\\\\/40{\\n  --tw-gradient-from: rgb(17 24 39 / 0.4) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-green-400\\\\/20{\\n  --tw-gradient-from: rgb(74 222 128 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(74 222 128 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-green-500{\\n  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-green-900\\\\/20{\\n  --tw-gradient-from: rgb(20 83 45 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(20 83 45 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-purple-400\\\\/20{\\n  --tw-gradient-from: rgb(192 132 252 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(192 132 252 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-purple-500{\\n  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-purple-900\\\\/20{\\n  --tw-gradient-from: rgb(88 28 135 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-purple-900\\\\/30{\\n  --tw-gradient-from: rgb(88 28 135 / 0.3) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-red-500{\\n  --tw-gradient-from: #ef4444 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-red-900\\\\/20{\\n  --tw-gradient-from: rgb(127 29 29 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(127 29 29 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-white{\\n  --tw-gradient-from: #fff var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-yellow-400\\\\/20{\\n  --tw-gradient-from: rgb(250 204 21 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(250 204 21 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-yellow-500{\\n  --tw-gradient-from: #eab308 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(234 179 8 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-yellow-900\\\\/20{\\n  --tw-gradient-from: rgb(113 63 18 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(113 63 18 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.to-\\\\[\\\\#d6006a\\\\]{\\n  --tw-gradient-to: #d6006a var(--tw-gradient-to-position);\\n}\\n.to-blue-500{\\n  --tw-gradient-to: #3b82f6 var(--tw-gradient-to-position);\\n}\\n.to-blue-500\\\\/20{\\n  --tw-gradient-to: rgb(59 130 246 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-blue-600{\\n  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);\\n}\\n.to-blue-600\\\\/20{\\n  --tw-gradient-to: rgb(37 99 235 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-blue-800\\\\/20{\\n  --tw-gradient-to: rgb(30 64 175 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-blue-900\\\\/20{\\n  --tw-gradient-to: rgb(30 58 138 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-blue-900\\\\/30{\\n  --tw-gradient-to: rgb(30 58 138 / 0.3) var(--tw-gradient-to-position);\\n}\\n.to-cyan-600{\\n  --tw-gradient-to: #0891b2 var(--tw-gradient-to-position);\\n}\\n.to-cyan-800\\\\/20{\\n  --tw-gradient-to: rgb(21 94 117 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-cyan-900\\\\/20{\\n  --tw-gradient-to: rgb(22 78 99 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-emerald-600{\\n  --tw-gradient-to: #059669 var(--tw-gradient-to-position);\\n}\\n.to-emerald-900\\\\/20{\\n  --tw-gradient-to: rgb(6 78 59 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-fuchsia-500{\\n  --tw-gradient-to: #d946ef var(--tw-gradient-to-position);\\n}\\n.to-gray-700\\\\/40{\\n  --tw-gradient-to: rgb(55 65 81 / 0.4) var(--tw-gradient-to-position);\\n}\\n.to-gray-700\\\\/60{\\n  --tw-gradient-to: rgb(55 65 81 / 0.6) var(--tw-gradient-to-position);\\n}\\n.to-gray-800\\\\/40{\\n  --tw-gradient-to: rgb(31 41 55 / 0.4) var(--tw-gradient-to-position);\\n}\\n.to-gray-900\\\\/60{\\n  --tw-gradient-to: rgb(17 24 39 / 0.6) var(--tw-gradient-to-position);\\n}\\n.to-green-600\\\\/20{\\n  --tw-gradient-to: rgb(22 163 74 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-green-800\\\\/20{\\n  --tw-gradient-to: rgb(22 101 52 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-orange-600{\\n  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);\\n}\\n.to-orange-900\\\\/20{\\n  --tw-gradient-to: rgb(124 45 18 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-pink-600{\\n  --tw-gradient-to: #db2777 var(--tw-gradient-to-position);\\n}\\n.to-pink-900\\\\/20{\\n  --tw-gradient-to: rgb(131 24 67 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-purple-600{\\n  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);\\n}\\n.to-purple-600\\\\/20{\\n  --tw-gradient-to: rgb(147 51 234 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-purple-800\\\\/20{\\n  --tw-gradient-to: rgb(107 33 168 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-purple-900\\\\/20{\\n  --tw-gradient-to: rgb(88 28 135 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-transparent{\\n  --tw-gradient-to: transparent var(--tw-gradient-to-position);\\n}\\n.to-yellow-600\\\\/20{\\n  --tw-gradient-to: rgb(202 138 4 / 0.2) var(--tw-gradient-to-position);\\n}\\n.object-cover{\\n  -o-object-fit: cover;\\n     object-fit: cover;\\n}\\n.p-0{\\n  padding: 0px;\\n}\\n.p-1{\\n  padding: 0.25rem;\\n}\\n.p-2{\\n  padding: 0.5rem;\\n}\\n.p-3{\\n  padding: 0.75rem;\\n}\\n.p-4{\\n  padding: 1rem;\\n}\\n.p-6{\\n  padding: 1.5rem;\\n}\\n.px-2{\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\n.px-3{\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\n.px-4{\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n.py-1{\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\n.py-1\\\\.5{\\n  padding-top: 0.375rem;\\n  padding-bottom: 0.375rem;\\n}\\n.py-2{\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\n.pb-1{\\n  padding-bottom: 0.25rem;\\n}\\n.pt-2{\\n  padding-top: 0.5rem;\\n}\\n.pt-4{\\n  padding-top: 1rem;\\n}\\n.text-left{\\n  text-align: left;\\n}\\n.text-center{\\n  text-align: center;\\n}\\n.text-right{\\n  text-align: right;\\n}\\n.font-poppins{\\n  font-family: Poppins, sans-serif;\\n}\\n.text-2xl{\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\n.text-3xl{\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\n.text-\\\\[10px\\\\]{\\n  font-size: 10px;\\n}\\n.text-base{\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\n.text-lg{\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\n.text-sm{\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n.text-xl{\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n.text-xs{\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\n.font-bold{\\n  font-weight: 700;\\n}\\n.font-medium{\\n  font-weight: 500;\\n}\\n.font-normal{\\n  font-weight: 400;\\n}\\n.font-semibold{\\n  font-weight: 600;\\n}\\n.uppercase{\\n  text-transform: uppercase;\\n}\\n.capitalize{\\n  text-transform: capitalize;\\n}\\n.leading-relaxed{\\n  line-height: 1.625;\\n}\\n.tracking-wider{\\n  letter-spacing: 0.05em;\\n}\\n.text-\\\\[\\\\#A0A0B0\\\\]{\\n  --tw-text-opacity: 1;\\n  color: rgb(160 160 176 / var(--tw-text-opacity, 1));\\n}\\n.text-\\\\[\\\\#E0E0E0\\\\]{\\n  --tw-text-opacity: 1;\\n  color: rgb(224 224 224 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\\n}\\n.text-brand-cyan{\\n  --tw-text-opacity: 1;\\n  color: rgb(0 255 255 / var(--tw-text-opacity, 1));\\n}\\n.text-cyan-300{\\n  --tw-text-opacity: 1;\\n  color: rgb(103 232 249 / var(--tw-text-opacity, 1));\\n}\\n.text-cyan-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(34 211 238 / var(--tw-text-opacity, 1));\\n}\\n.text-emerald-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(52 211 153 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-300{\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-500{\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-900{\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\n.text-green-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\\n}\\n.text-indigo-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(129 140 248 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(251 146 60 / var(--tw-text-opacity, 1));\\n}\\n.text-pink-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(244 114 182 / var(--tw-text-opacity, 1));\\n}\\n.text-purple-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(192 132 252 / var(--tw-text-opacity, 1));\\n}\\n.text-red-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\\n}\\n.text-teal-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(45 212 191 / var(--tw-text-opacity, 1));\\n}\\n.text-white{\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\\n}\\n.opacity-0{\\n  opacity: 0;\\n}\\n.opacity-30{\\n  opacity: 0.3;\\n}\\n.opacity-50{\\n  opacity: 0.5;\\n}\\n.opacity-90{\\n  opacity: 0.9;\\n}\\n.shadow-inner{\\n  --tw-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: inset 0 2px 4px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-lg{\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-md{\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.blur-sm{\\n  --tw-blur: blur(4px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.drop-shadow{\\n  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.filter{\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.backdrop-blur-sm{\\n  --tw-backdrop-blur: blur(4px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.backdrop-blur-xl{\\n  --tw-backdrop-blur: blur(24px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.backdrop-filter{\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.transition{\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-all{\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-colors{\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-opacity{\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-transform{\\n  transition-property: transform;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.duration-200{\\n  transition-duration: 200ms;\\n}\\n.duration-300{\\n  transition-duration: 300ms;\\n}\\n.duration-500{\\n  transition-duration: 500ms;\\n}\\n.ease-in-out{\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.ease-out{\\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n}\\n\\n/* CSS Custom Properties for Responsive Scaling - Optimized for Content Fit */\\n:root {\\n  --scale-factor: 1;\\n  --content-scale: 21;\\n  --base-font-size: calc(14px * var(--content-scale));\\n  --base-spacing: calc(6px * var(--content-scale));\\n  --base-border-radius: calc(8px * var(--content-scale));\\n  --base-icon-size: calc(20px * var(--content-scale));\\n  --base-button-height: calc(36px * var(--content-scale));\\n  --base-card-padding: calc(1px * var(--content-scale));\\n  --base-gap: calc(6px * var(--content-scale));\\n  --header-height: calc(60px * var(--content-scale));\\n  --footer-height: calc(50px * var(--content-scale));\\n  --sidebar-width: calc(80px * var(--content-scale));\\n\\n  /* HUD/HDR Color Palette */\\n  --hud-bg-primary: #311861;\\n  --hud-bg-secondary: #22184f;\\n  --hud-bg-panel: rgba(15, 20, 25, 0.85);\\n  --hud-bg-card: rgba(20, 30, 45, 0.6);\\n  --hud-border: rgba(0, 207, 255, 0.2);\\n  --hud-border-glow: rgba(0, 207, 255, 0.4);\\n\\n  /* HUD Accent Colors */\\n  --hud-neon-blue: #00cfff;\\n  --hud-aqua-green: #00ffbd;\\n  --hud-energy-orange: #ffa500;\\n  --hud-electric-purple: #bc13fe;\\n  --hud-deep-red: #ff4b4b;\\n  --hud-bright-white: #ffffff;\\n  --hud-silver: #b0b8c4;\\n\\n  /* Glassmorphism Effects */\\n  --glass-bg: rgba(255, 255, 255, 0.05);\\n  --glass-border: rgba(255, 255, 255, 0.1);\\n  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\\n}\\n\\n/* Responsive scaling variables - now handled by --content-scale */\\n@media (max-width: 1920px) and (min-width: 1600px) {\\n  :root {\\n    --scale-factor: 0.9;\\n    --content-scale: 0.9;\\n  }\\n}\\n\\n@media (max-width: 1599px) and (min-width: 1400px) {\\n  :root {\\n    --scale-factor: 0.8;\\n    --content-scale: 0.8;\\n  }\\n}\\n\\n@media (max-width: 1399px) and (min-width: 1200px) {\\n  :root {\\n    --scale-factor: 0.7;\\n    --content-scale: 0.7;\\n  }\\n}\\n\\n@media (max-width: 1199px) and (min-width: 1000px) {\\n  :root {\\n    --scale-factor: 0.6;\\n    --content-scale: 0.6;\\n  }\\n}\\n\\n@media (max-width: 999px) {\\n  :root {\\n    --scale-factor: 0.5;\\n    --content-scale: 0.5;\\n  }\\n}\\n\\n/* Ensure base styles are applied */\\n* {\\n  box-sizing: border-box;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nhtml, body {\\n  height: 100vh;\\n  width: 100vw;\\n  overflow: hidden;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nbody {\\n  font-family: 'Poppins', sans-serif;\\n  font-size: var(--base-font-size);\\n  line-height: 1.5;\\n}\\n\\n#__next {\\n  height: 100vh;\\n  width: 100vw;\\n  overflow: hidden;\\n}\\n\\n.main-background {\\n  background: linear-gradient(135deg, var(--hud-bg-primary) 0%, var(--hud-bg-secondary) 50%, #0a0f1c 100%);\\n  background-attachment: fixed;\\n  position: relative;\\n}\\n\\n.main-background::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(ellipse at 20% 30%, rgba(0, 207, 255, 0.1) 0%, transparent 50%),\\n              radial-gradient(ellipse at 80% 70%, rgba(188, 19, 254, 0.08) 0%, transparent 50%),\\n              radial-gradient(ellipse at 50% 50%, rgba(0, 255, 189, 0.05) 0%, transparent 70%);\\n  pointer-events: none;\\n}\\n\\n/* Global Responsive Classes */\\n.responsive-text {\\n  font-size: var(--base-font-size);\\n}\\n\\n.responsive-text-sm {\\n  font-size: calc(var(--base-font-size) * 0.875);\\n}\\n\\n.responsive-text-xs {\\n  font-size: calc(var(--base-font-size) * 0.75);\\n}\\n\\n.responsive-text-lg {\\n  font-size: calc(var(--base-font-size) * 1.125);\\n}\\n\\n.responsive-text-xl {\\n  font-size: calc(var(--base-font-size) * 1.25);\\n}\\n\\n.responsive-text-2xl {\\n  font-size: calc(var(--base-font-size) * 1.5);\\n}\\n\\n.responsive-spacing {\\n  padding: var(--base-spacing);\\n}\\n\\n.responsive-spacing-sm {\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.responsive-spacing-lg {\\n  padding: calc(var(--base-spacing) * 1.5);\\n}\\n\\n.responsive-gap {\\n  gap: var(--base-gap);\\n}\\n\\n.responsive-gap-sm {\\n  gap: calc(var(--base-gap) * 0.5);\\n}\\n\\n.responsive-gap-lg {\\n  gap: calc(var(--base-gap) * 1.5);\\n}\\n\\n.responsive-border-radius {\\n  border-radius: var(--base-border-radius);\\n}\\n\\n.responsive-icon {\\n  width: var(--base-icon-size);\\n  height: var(--base-icon-size);\\n}\\n\\n.responsive-button {\\n  height: var(--base-button-height);\\n  padding: 0 var(--base-spacing);\\n  font-size: var(--base-font-size);\\n  border-radius: var(--base-border-radius);\\n}\\n\\n.responsive-card {\\n  padding: var(--base-card-padding);\\n  border-radius: calc(var(--base-border-radius) * 1.5);\\n}\\n\\n/* Enhanced HUD Card System */\\n.hud-card {\\n  background: var(--hud-bg-card);\\n  border: 1px solid var(--hud-border);\\n  border-radius: calc(var(--base-border-radius) * 1.5);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  box-shadow: var(--glass-shadow);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.hud-card::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(90deg, transparent, var(--hud-neon-blue), transparent);\\n  opacity: 0.6;\\n}\\n\\n.hud-card:hover {\\n  border-color: var(--hud-border-glow);\\n  box-shadow: 0 8px 40px rgba(0, 207, 255, 0.15), var(--glass-shadow);\\n  transform: translateY(-2px);\\n}\\n\\n/* Compact metric cards */\\n.metric-card {\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  border-radius: var(--base-border-radius);\\n  padding: calc(var(--base-spacing) * 2);\\n  text-align: center;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.metric-card::after {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(0, 207, 255, 0.05) 0%, rgba(0, 255, 189, 0.05) 100%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.metric-card:hover::after {\\n  opacity: 1;\\n}\\n\\n.metric-card:hover {\\n  border-color: var(--hud-neon-blue);\\n  box-shadow: 0 4px 20px rgba(0, 207, 255, 0.2);\\n  transform: scale(1.02);\\n}\\n\\n/* Status badges with glow effects */\\n.status-badge {\\n  padding: calc(var(--base-spacing) * 0.5) calc(var(--base-spacing) * 1);\\n  border-radius: calc(var(--base-border-radius) * 2);\\n  font-size: calc(10px * var(--content-scale));\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  border: 1px solid;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.status-badge.active {\\n  background: rgba(0, 255, 189, 0.2);\\n  color: var(--hud-aqua-green);\\n  border-color: var(--hud-aqua-green);\\n  box-shadow: 0 0 10px rgba(0, 255, 189, 0.3);\\n}\\n\\n.status-badge.beta {\\n  background: rgba(255, 165, 0, 0.2);\\n  color: var(--hud-energy-orange);\\n  border-color: var(--hud-energy-orange);\\n  box-shadow: 0 0 10px rgba(255, 165, 0, 0.3);\\n}\\n\\n.status-badge.live {\\n  background: rgba(0, 207, 255, 0.2);\\n  color: var(--hud-neon-blue);\\n  border-color: var(--hud-neon-blue);\\n  box-shadow: 0 0 10px rgba(0, 207, 255, 0.3);\\n}\\n\\n.status-badge.testing {\\n  background: rgba(188, 19, 254, 0.2);\\n  color: var(--hud-electric-purple);\\n  border-color: var(--hud-electric-purple);\\n  box-shadow: 0 0 10px rgba(188, 19, 254, 0.3);\\n}\\n\\n/* Enhanced Header Styling */\\n.header-hud {\\n  background: var(--hud-bg-panel);\\n  border: 1px solid var(--hud-border);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n  position: relative;\\n}\\n\\n.header-hud::before {\\n  content: '';\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(90deg, transparent, var(--hud-neon-blue), transparent);\\n  opacity: 0.4;\\n}\\n\\n/* Enhanced Department Buttons */\\n.dept-button {\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.dept-button:hover {\\n  background: rgba(0, 207, 255, 0.1);\\n  border-color: var(--hud-border-glow);\\n  box-shadow: 0 4px 20px rgba(0, 207, 255, 0.2);\\n  transform: scale(1.05);\\n}\\n\\n.dept-button.active {\\n  background: linear-gradient(135deg, rgba(0, 207, 255, 0.2) 0%, rgba(0, 255, 189, 0.1) 100%);\\n  border-color: var(--hud-neon-blue);\\n  box-shadow: 0 0 20px rgba(0, 207, 255, 0.3);\\n}\\n\\n.dept-button.active::after {\\n  content: '';\\n  position: absolute;\\n  inset: 0;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\\n  pointer-events: none;\\n}\\n\\n/* Dashboard Auto-Scale - Scales ALL content including text, icons, spacing */\\n.dashboard-auto-scale {\\n  transform-origin: top left;\\n  width: 100vw !important;\\n  height: 100vh !important;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  overflow: hidden;\\n  transition: transform 0.3s ease-out;\\n}\\n\\n/* Scale everything - content, text, icons, spacing */\\n@media (max-width: 1920px) and (min-width: 1600px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.9);\\n    width: calc(100vw / 0.9) !important;\\n    height: calc(100vh / 0.9) !important;\\n  }\\n  :root {\\n    --content-scale: 0.9;\\n  }\\n}\\n\\n@media (max-width: 1599px) and (min-width: 1400px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.8);\\n    width: calc(100vw / 0.8) !important;\\n    height: calc(100vh / 0.8) !important;\\n  }\\n  :root {\\n    --content-scale: 0.8;\\n  }\\n}\\n\\n@media (max-width: 1399px) and (min-width: 1200px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.7);\\n    width: calc(100vw / 0.7) !important;\\n    height: calc(100vh / 0.7) !important;\\n  }\\n  :root {\\n    --content-scale: 0.7;\\n  }\\n}\\n\\n@media (max-width: 1199px) and (min-width: 1000px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.6);\\n    width: calc(100vw / 0.6) !important;\\n    height: calc(100vh / 0.6) !important;\\n  }\\n  :root {\\n    --content-scale: 0.6;\\n  }\\n}\\n\\n@media (max-width: 999px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.5);\\n    width: calc(100vw / 0.5) !important;\\n    height: calc(100vh / 0.5) !important;\\n  }\\n  :root {\\n    --content-scale: 0.5;\\n  }\\n}\\n\\n/* Default content scale */\\n:root {\\n  --content-scale: 1;\\n}\\n\\n/* Universal content scaling - applies to ALL elements */\\n* {\\n  font-size: inherit;\\n}\\n\\n/* Scale all text elements in main content */\\n.main-section h1, .main-section h2, .main-section h3, .main-section h4, .main-section h5, .main-section h6,\\n.main-section p, .main-section span, .main-section div, .main-section button,\\n.main-section input, .main-section textarea, .main-section select, .main-section label, .main-section a {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale card content specifically */\\n.card-content * {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale chart text and numbers */\\n.recharts-text, .recharts-label, .chart-text, .metric-value, .metric-label {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale all icons and images */\\nsvg, img, .icon {\\n  width: calc(var(--base-icon-size));\\n  height: calc(var(--base-icon-size));\\n}\\n\\n/* Scale all spacing */\\n.p-1 { padding: calc(4px * var(--content-scale)) !important; }\\n.p-2 { padding: calc(8px * var(--content-scale)) !important; }\\n.p-3 { padding: calc(12px * var(--content-scale)) !important; }\\n.p-4 { padding: calc(16px * var(--content-scale)) !important; }\\n.p-5 { padding: calc(20px * var(--content-scale)) !important; }\\n.p-6 { padding: calc(24px * var(--content-scale)) !important; }\\n\\n.m-1 { margin: calc(4px * var(--content-scale)) !important; }\\n.m-2 { margin: calc(8px * var(--content-scale)) !important; }\\n.m-3 { margin: calc(12px * var(--content-scale)) !important; }\\n.m-4 { margin: calc(16px * var(--content-scale)) !important; }\\n.m-5 { margin: calc(20px * var(--content-scale)) !important; }\\n.m-6 { margin: calc(24px * var(--content-scale)) !important; }\\n\\n/* Scale gaps */\\n.gap-1 { gap: calc(4px * var(--content-scale)) !important; }\\n.gap-2 { gap: calc(8px * var(--content-scale)) !important; }\\n.gap-3 { gap: calc(12px * var(--content-scale)) !important; }\\n.gap-4 { gap: calc(16px * var(--content-scale)) !important; }\\n.gap-5 { gap: calc(20px * var(--content-scale)) !important; }\\n.gap-6 { gap: calc(24px * var(--content-scale)) !important; }\\n\\n/* Scale border radius */\\n.rounded { border-radius: calc(4px * var(--content-scale)) !important; }\\n.rounded-md { border-radius: calc(6px * var(--content-scale)) !important; }\\n.rounded-lg { border-radius: calc(8px * var(--content-scale)) !important; }\\n.rounded-xl { border-radius: calc(12px * var(--content-scale)) !important; }\\n.rounded-2xl { border-radius: calc(16px * var(--content-scale)) !important; }\\n\\n@keyframes fade-in-up {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n.animate-fade-in-up {\\n  animation: fade-in-up 0.3s ease-out forwards;\\n}\\n\\n@keyframes fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n.animate-fadeIn {\\n  animation: fadeIn 0.5s ease-in-out;\\n}\\n\\n/* Override Tailwind text sizes with content scaling */\\n.text-xs { font-size: calc(12px * var(--content-scale)) !important; }\\n.text-sm { font-size: calc(14px * var(--content-scale)) !important; }\\n.text-base { font-size: calc(16px * var(--content-scale)) !important; }\\n.text-lg { font-size: calc(18px * var(--content-scale)) !important; }\\n.text-xl { font-size: calc(20px * var(--content-scale)) !important; }\\n.text-2xl { font-size: calc(24px * var(--content-scale)) !important; }\\n.text-3xl { font-size: calc(30px * var(--content-scale)) !important; }\\n.text-4xl { font-size: calc(36px * var(--content-scale)) !important; }\\n\\n/* Override Tailwind width/height for icons */\\n.w-3 { width: calc(12px * var(--content-scale)) !important; }\\n.w-4 { width: calc(16px * var(--content-scale)) !important; }\\n.w-5 { width: calc(20px * var(--content-scale)) !important; }\\n.w-6 { width: calc(24px * var(--content-scale)) !important; }\\n.w-8 { width: calc(32px * var(--content-scale)) !important; }\\n.w-10 { width: calc(40px * var(--content-scale)) !important; }\\n.w-12 { width: calc(48px * var(--content-scale)) !important; }\\n\\n.h-3 { height: calc(12px * var(--content-scale)) !important; }\\n.h-4 { height: calc(16px * var(--content-scale)) !important; }\\n.h-5 { height: calc(20px * var(--content-scale)) !important; }\\n.h-6 { height: calc(24px * var(--content-scale)) !important; }\\n.h-8 { height: calc(32px * var(--content-scale)) !important; }\\n.h-10 { height: calc(40px * var(--content-scale)) !important; }\\n.h-12 { height: calc(48px * var(--content-scale)) !important; }\\n\\n/* Optimized Layout Classes for Perfect Content Fit */\\n.main-layout {\\n  height: 100vh;\\n  width: 100vw;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.content-area {\\n  flex: 1;\\n  display: flex;\\n  min-height: 0;\\n  overflow: hidden;\\n}\\n\\n.main-content {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 0;\\n  overflow: hidden;\\n}\\n\\n.header-section {\\n  height: var(--header-height);\\n  flex-shrink: 0;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.main-section {\\n  flex: 1;\\n  min-height: 0;\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.footer-section {\\n  height: calc(var(--footer-height) * 1.5);\\n  flex-shrink: 0;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.sidebar-left {\\n  width: var(--sidebar-width);\\n  flex-shrink: 0;\\n}\\n\\n.sidebar-right {\\n  width: var(--sidebar-width);\\n  flex-shrink: 0;\\n}\\n\\n/* Enhanced Dashboard Grid for No-Scroll Layout */\\n.dashboard-grid {\\n  display: grid;\\n  gap: calc(var(--base-gap) * 0.75);\\n  height: 100%;\\n  overflow: hidden;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.dashboard-grid-teacher {\\n  grid-template-rows: auto 1fr auto;\\n  grid-template-columns: 1fr;\\n}\\n\\n.dashboard-grid-school {\\n  grid-template-rows: auto 1fr;\\n  grid-template-columns: 1fr 1fr;\\n  gap: calc(var(--base-gap) * 1);\\n}\\n\\n/* Compact content areas */\\n.content-compact {\\n  max-height: calc(100vh - var(--header-height) - var(--footer-height) - calc(var(--base-spacing) * 4));\\n  overflow-y: auto;\\n}\\n\\n.content-no-scroll {\\n  height: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n/* Enhanced Animations */\\n@keyframes pulse-glow {\\n  0%, 100% {\\n    box-shadow: 0 0 5px rgba(0, 207, 255, 0.3);\\n  }\\n  50% {\\n    box-shadow: 0 0 20px rgba(0, 207, 255, 0.6), 0 0 30px rgba(0, 207, 255, 0.4);\\n  }\\n}\\n\\n@keyframes slide-in-up {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes fade-in-scale {\\n  from {\\n    opacity: 0;\\n    transform: scale(0.95);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n}\\n\\n.animate-pulse-glow {\\n  animation: pulse-glow 2s ease-in-out infinite;\\n}\\n\\n.animate-slide-in-up {\\n  animation: slide-in-up 0.5s ease-out;\\n}\\n\\n.animate-fade-in-scale {\\n  animation: fade-in-scale 0.3s ease-out;\\n}\\n\\n/* Hover Effects */\\n.hover-lift {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.hover-lift:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 207, 255, 0.2);\\n}\\n\\n/* Progress Bar Animations */\\n.progress-bar {\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.progress-bar::after {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\\n  animation: shimmer 2s infinite;\\n}\\n\\n@keyframes shimmer {\\n  0% {\\n    left: -100%;\\n  }\\n  100% {\\n    left: 100%;\\n  }\\n}\\n\\n/* Optimized Card Grid */\\n.card-grid {\\n  display: grid;\\n  gap: var(--base-gap);\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n.card-grid-2 {\\n  grid-template-columns: 1fr 1fr;\\n}\\n\\n.card-grid-3 {\\n  grid-template-columns: 1fr 1fr 1fr;\\n}\\n\\n.card-grid-4 {\\n  grid-template-columns: 1fr 1fr 1fr 1fr;\\n}\\n\\n.card-grid-auto {\\n  grid-template-columns: repeat(auto-fit, minmax(calc(250px * var(--content-scale)), 1fr));\\n}\\n\\n/* Optimized Card Sizing */\\n.card-compact {\\n  padding: calc(var(--base-card-padding) * 0.75);\\n  min-height: calc(120px * var(--content-scale));\\n}\\n\\n.card-standard {\\n  padding: var(--base-card-padding);\\n  min-height: calc(160px * var(--content-scale));\\n}\\n\\n.card-large {\\n  padding: calc(var(--base-card-padding) * 1.25);\\n  min-height: calc(200px * var(--content-scale));\\n}\\n\\n/* Neumorphic HUD Subnav Styles */\\n.subnav-hud {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  padding: calc(var(--base-spacing) * 1.5) 0;\\n  margin: 0 auto;\\n  max-width: calc(72px * 8 + var(--base-gap) * 7); /* 8 buttons with gaps */\\n}\\n\\n.subnav-button {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  width: calc(72px * var(--content-scale));\\n  height: calc(72px * var(--content-scale));\\n  margin: 0 calc(var(--base-gap) * 0.5);\\n  border-radius: calc(16px * var(--content-scale));\\n  background: linear-gradient(145deg, #2a2a3a, #1a1a2a);\\n  box-shadow:\\n    calc(8px * var(--content-scale)) calc(8px * var(--content-scale)) calc(16px * var(--content-scale)) rgba(0, 0, 0, 0.4),\\n    calc(-4px * var(--content-scale)) calc(-4px * var(--content-scale)) calc(8px * var(--content-scale)) rgba(255, 255, 255, 0.05);\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.subnav-button:hover {\\n  transform: translateY(calc(-2px * var(--content-scale)));\\n  box-shadow:\\n    calc(12px * var(--content-scale)) calc(12px * var(--content-scale)) calc(24px * var(--content-scale)) rgba(0, 0, 0, 0.5),\\n    calc(-6px * var(--content-scale)) calc(-6px * var(--content-scale)) calc(12px * var(--content-scale)) rgba(255, 255, 255, 0.08);\\n}\\n\\n.subnav-button.active {\\n  background: linear-gradient(145deg, #3a4a6a, #2a3a5a);\\n  box-shadow:\\n    inset calc(4px * var(--content-scale)) calc(4px * var(--content-scale)) calc(8px * var(--content-scale)) rgba(0, 0, 0, 0.3),\\n    inset calc(-2px * var(--content-scale)) calc(-2px * var(--content-scale)) calc(4px * var(--content-scale)) rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(100, 200, 255, 0.3);\\n}\\n\\n.subnav-icon {\\n  width: calc(24px * var(--content-scale));\\n  height: calc(24px * var(--content-scale));\\n  margin-bottom: calc(4px * var(--content-scale));\\n  color: #a0a0b0;\\n  transition: color 0.3s ease;\\n}\\n\\n.subnav-button:hover .subnav-icon,\\n.subnav-button.active .subnav-icon {\\n  color: #64b5f6;\\n}\\n\\n.subnav-label {\\n  font-size: calc(10px * var(--content-scale));\\n  font-weight: 500;\\n  color: #a0a0b0;\\n  text-align: center;\\n  line-height: 1.2;\\n  transition: color 0.3s ease;\\n}\\n\\n.subnav-button:hover .subnav-label,\\n.subnav-button.active .subnav-label {\\n  color: #64b5f6;\\n}\\n\\n/* HUD Glow Effect */\\n.subnav-button::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(circle at center, rgba(100, 181, 246, 0.1) 0%, transparent 70%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n  border-radius: inherit;\\n}\\n\\n.subnav-button:hover::before,\\n.subnav-button.active::before {\\n  opacity: 1;\\n}\\n\\n/* Enhanced HUD Vertical Layout for Left Sidebar */\\n.subnav-hud-vertical {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: stretch;\\n  justify-content: flex-start;\\n  gap: calc(var(--base-gap) * 0.75);\\n  padding: calc(var(--base-spacing) * 1);\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n/* Enhanced subnav button with glassmorphism */\\n.subnav-hud-vertical .subnav-button {\\n  width: 100%;\\n  height: calc(42px * var(--content-scale));\\n  margin: 0;\\n  flex-shrink: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  padding: calc(var(--base-spacing) * 1) calc(var(--base-spacing) * 1.5);\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  border-radius: var(--base-border-radius);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover {\\n  background: rgba(0, 207, 255, 0.1);\\n  border-color: var(--hud-border-glow);\\n  transform: translateX(4px);\\n  box-shadow: 0 4px 20px rgba(0, 207, 255, 0.2);\\n}\\n\\n.subnav-hud-vertical .subnav-button.active {\\n  background: linear-gradient(135deg, rgba(0, 207, 255, 0.2) 0%, rgba(0, 255, 189, 0.1) 100%);\\n  border-color: var(--hud-neon-blue);\\n  box-shadow: 0 0 20px rgba(0, 207, 255, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n}\\n\\n/* Enhanced subnav icon */\\n.subnav-hud-vertical .subnav-icon {\\n  width: calc(18px * var(--content-scale));\\n  height: calc(18px * var(--content-scale));\\n  margin-right: calc(var(--base-spacing) * 1.5);\\n  color: var(--hud-silver);\\n  transition: all 0.3s ease;\\n  flex-shrink: 0;\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover .subnav-icon {\\n  color: var(--hud-neon-blue);\\n  filter: drop-shadow(0 0 8px rgba(0, 207, 255, 0.6));\\n}\\n\\n.subnav-hud-vertical .subnav-button.active .subnav-icon {\\n  color: var(--hud-bright-white);\\n  filter: drop-shadow(0 0 12px rgba(0, 207, 255, 0.8));\\n}\\n\\n/* Enhanced subnav label - full text display */\\n.subnav-hud-vertical .subnav-label {\\n  color: var(--hud-silver);\\n  font-size: calc(12px * var(--content-scale));\\n  font-weight: 500;\\n  line-height: 1.2;\\n  white-space: nowrap;\\n  transition: all 0.3s ease;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover .subnav-label {\\n  color: var(--hud-bright-white);\\n  text-shadow: 0 0 8px rgba(0, 207, 255, 0.6);\\n}\\n\\n.subnav-hud-vertical .subnav-button.active .subnav-label {\\n  color: var(--hud-bright-white);\\n  font-weight: 600;\\n  text-shadow: 0 0 12px rgba(0, 207, 255, 0.8);\\n}\\n\\n.hover\\\\:scale-105:hover{\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.hover\\\\:border-cyan-400\\\\/50:hover{\\n  border-color: rgb(34 211 238 / 0.5);\\n}\\n\\n.hover\\\\:border-cyan-400\\\\/60:hover{\\n  border-color: rgb(34 211 238 / 0.6);\\n}\\n\\n.hover\\\\:border-gray-500\\\\/50:hover{\\n  border-color: rgb(107 114 128 / 0.5);\\n}\\n\\n.hover\\\\:bg-cyan-500\\\\/50:hover{\\n  background-color: rgb(6 182 212 / 0.5);\\n}\\n\\n.hover\\\\:bg-gray-700\\\\/30:hover{\\n  background-color: rgb(55 65 81 / 0.3);\\n}\\n\\n.hover\\\\:bg-gray-700\\\\/50:hover{\\n  background-color: rgb(55 65 81 / 0.5);\\n}\\n\\n.hover\\\\:bg-gray-700\\\\/60:hover{\\n  background-color: rgb(55 65 81 / 0.6);\\n}\\n\\n.hover\\\\:bg-gray-800\\\\/40:hover{\\n  background-color: rgb(31 41 55 / 0.4);\\n}\\n\\n.hover\\\\:bg-pink-500\\\\/90:hover{\\n  background-color: rgb(236 72 153 / 0.9);\\n}\\n\\n.hover\\\\:bg-white\\\\/30:hover{\\n  background-color: rgb(255 255 255 / 0.3);\\n}\\n\\n.hover\\\\:text-white:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:shadow-lg:hover{\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.hover\\\\:shadow-cyan-400\\\\/20:hover{\\n  --tw-shadow-color: rgb(34 211 238 / 0.2);\\n  --tw-shadow: var(--tw-shadow-colored);\\n}\\n\\n.focus\\\\:outline-none:focus{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n\\n.focus-visible\\\\:ring-1:focus-visible{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n\\n.focus-visible\\\\:ring-2:focus-visible{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n\\n.focus-visible\\\\:ring-brand-cyan:focus-visible{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(0 255 255 / var(--tw-ring-opacity, 1));\\n}\\n\\n.focus-visible\\\\:ring-white:focus-visible{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity, 1));\\n}\\n\\n.focus-visible\\\\:ring-offset-2:focus-visible{\\n  --tw-ring-offset-width: 2px;\\n}\\n\\n.focus-visible\\\\:ring-offset-gray-800:focus-visible{\\n  --tw-ring-offset-color: #1f2937;\\n}\\n\\n.disabled\\\\:cursor-not-allowed:disabled{\\n  cursor: not-allowed;\\n}\\n\\n.disabled\\\\:cursor-wait:disabled{\\n  cursor: wait;\\n}\\n\\n.disabled\\\\:opacity-50:disabled{\\n  opacity: 0.5;\\n}\\n\\n.group:hover .group-hover\\\\:scale-105{\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.group:hover .group-hover\\\\:scale-110{\\n  --tw-scale-x: 1.1;\\n  --tw-scale-y: 1.1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.group:hover .group-hover\\\\:text-cyan-300{\\n  --tw-text-opacity: 1;\\n  color: rgb(103 232 249 / var(--tw-text-opacity, 1));\\n}\\n\\n.group:hover .group-hover\\\\:text-white{\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n\\n.group:hover .group-hover\\\\:opacity-20{\\n  opacity: 0.2;\\n}\\n\\n@media (prefers-reduced-motion: no-preference){\\n\\n  @keyframes pulse{\\n\\n    50%{\\n      opacity: .5;\\n    }\\n  }\\n\\n  .motion-safe\\\\:animate-pulse{\\n    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n  }\\n}\\n\\n@media (min-width: 640px){\\n\\n  .sm\\\\:mr-3{\\n    margin-right: 0.75rem;\\n  }\\n\\n  .sm\\\\:mt-4{\\n    margin-top: 1rem;\\n  }\\n\\n  .sm\\\\:h-10{\\n    height: 2.5rem;\\n  }\\n\\n  .sm\\\\:h-12{\\n    height: 3rem;\\n  }\\n\\n  .sm\\\\:h-16{\\n    height: 4rem;\\n  }\\n\\n  .sm\\\\:h-3{\\n    height: 0.75rem;\\n  }\\n\\n  .sm\\\\:h-36{\\n    height: 9rem;\\n  }\\n\\n  .sm\\\\:h-4{\\n    height: 1rem;\\n  }\\n\\n  .sm\\\\:h-40{\\n    height: 10rem;\\n  }\\n\\n  .sm\\\\:h-48{\\n    height: 12rem;\\n  }\\n\\n  .sm\\\\:h-6{\\n    height: 1.5rem;\\n  }\\n\\n  .sm\\\\:h-8{\\n    height: 2rem;\\n  }\\n\\n  .sm\\\\:w-10{\\n    width: 2.5rem;\\n  }\\n\\n  .sm\\\\:w-12{\\n    width: 3rem;\\n  }\\n\\n  .sm\\\\:w-16{\\n    width: 4rem;\\n  }\\n\\n  .sm\\\\:w-3{\\n    width: 0.75rem;\\n  }\\n\\n  .sm\\\\:w-36{\\n    width: 9rem;\\n  }\\n\\n  .sm\\\\:w-4{\\n    width: 1rem;\\n  }\\n\\n  .sm\\\\:w-40{\\n    width: 10rem;\\n  }\\n\\n  .sm\\\\:w-6{\\n    width: 1.5rem;\\n  }\\n\\n  .sm\\\\:w-8{\\n    width: 2rem;\\n  }\\n\\n  .sm\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .sm\\\\:gap-4{\\n    gap: 1rem;\\n  }\\n\\n  .sm\\\\:gap-6{\\n    gap: 1.5rem;\\n  }\\n\\n  .sm\\\\:p-2{\\n    padding: 0.5rem;\\n  }\\n\\n  .sm\\\\:p-3{\\n    padding: 0.75rem;\\n  }\\n\\n  .sm\\\\:p-4{\\n    padding: 1rem;\\n  }\\n\\n  .sm\\\\:p-6{\\n    padding: 1.5rem;\\n  }\\n\\n  .sm\\\\:px-4{\\n    padding-left: 1rem;\\n    padding-right: 1rem;\\n  }\\n\\n  .sm\\\\:py-2{\\n    padding-top: 0.5rem;\\n    padding-bottom: 0.5rem;\\n  }\\n\\n  .sm\\\\:text-base{\\n    font-size: 1rem;\\n    line-height: 1.5rem;\\n  }\\n\\n  .sm\\\\:text-lg{\\n    font-size: 1.125rem;\\n    line-height: 1.75rem;\\n  }\\n\\n  .sm\\\\:text-sm{\\n    font-size: 0.875rem;\\n    line-height: 1.25rem;\\n  }\\n\\n  .sm\\\\:text-xl{\\n    font-size: 1.25rem;\\n    line-height: 1.75rem;\\n  }\\n\\n  .sm\\\\:text-xs{\\n    font-size: 0.75rem;\\n    line-height: 1rem;\\n  }\\n}\\n\\n@media (min-width: 768px){\\n\\n  .md\\\\:col-span-2{\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .md\\\\:grid-cols-2{\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-4{\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n}\\n\\n@media (min-width: 1024px){\\n\\n  .lg\\\\:col-span-12{\\n    grid-column: span 12 / span 12;\\n  }\\n\\n  .lg\\\\:col-span-2{\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .lg\\\\:col-span-3{\\n    grid-column: span 3 / span 3;\\n  }\\n\\n  .lg\\\\:col-span-4{\\n    grid-column: span 4 / span 4;\\n  }\\n\\n  .lg\\\\:col-span-5{\\n    grid-column: span 5 / span 5;\\n  }\\n\\n  .lg\\\\:col-span-7{\\n    grid-column: span 7 / span 7;\\n  }\\n\\n  .lg\\\\:h-40{\\n    height: 10rem;\\n  }\\n\\n  .lg\\\\:h-48{\\n    height: 12rem;\\n  }\\n\\n  .lg\\\\:w-40{\\n    width: 10rem;\\n  }\\n\\n  .lg\\\\:w-48{\\n    width: 12rem;\\n  }\\n\\n  .lg\\\\:grid-cols-12{\\n    grid-template-columns: repeat(12, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-4{\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:text-xl{\\n    font-size: 1.25rem;\\n    line-height: 1.75rem;\\n  }\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA,qGAAqG;;AAErG;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;;CAAc;;AAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,+HAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,+GAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;;AAAd;EAAA,aAAc;AAAA;AACd;EAAA;AAAoB;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AACpB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,yBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,2EAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yDAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;KAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gDAAmB;EAAnB,6DAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,6EAAmB;EAAnB,iGAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kGAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,gKAAmB;EAAnB,wJAAmB;EAAnB,iLAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;;AAEnB,6EAA6E;AAC7E;EACE,iBAAiB;EACjB,mBAAmB;EACnB,mDAAmD;EACnD,gDAAgD;EAChD,sDAAsD;EACtD,mDAAmD;EACnD,uDAAuD;EACvD,qDAAqD;EACrD,4CAA4C;EAC5C,kDAAkD;EAClD,kDAAkD;EAClD,kDAAkD;;EAElD,0BAA0B;EAC1B,yBAAyB;EACzB,2BAA2B;EAC3B,sCAAsC;EACtC,oCAAoC;EACpC,oCAAoC;EACpC,yCAAyC;;EAEzC,sBAAsB;EACtB,wBAAwB;EACxB,yBAAyB;EACzB,4BAA4B;EAC5B,8BAA8B;EAC9B,uBAAuB;EACvB,2BAA2B;EAC3B,qBAAqB;;EAErB,0BAA0B;EAC1B,qCAAqC;EACrC,wCAAwC;EACxC,6CAA6C;AAC/C;;AAEA,kEAAkE;AAClE;EACE;IACE,mBAAmB;IACnB,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,mBAAmB;IACnB,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,mBAAmB;IACnB,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,mBAAmB;IACnB,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,mBAAmB;IACnB,oBAAoB;EACtB;AACF;;AAEA,mCAAmC;AACnC;EACE,sBAAsB;EACtB,SAAS;EACT,UAAU;AACZ;;AAEA;EACE,aAAa;EACb,YAAY;EACZ,gBAAgB;EAChB,SAAS;EACT,UAAU;AACZ;;AAEA;EACE,kCAAkC;EAClC,gCAAgC;EAChC,gBAAgB;AAClB;;AAEA;EACE,aAAa;EACb,YAAY;EACZ,gBAAgB;AAClB;;AAEA;EACE,wGAAwG;EACxG,4BAA4B;EAC5B,kBAAkB;AACpB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT;;8FAE4F;EAC5F,oBAAoB;AACtB;;AAEA,8BAA8B;AAC9B;EACE,gCAAgC;AAClC;;AAEA;EACE,8CAA8C;AAChD;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,8CAA8C;AAChD;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4BAA4B;AAC9B;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;EACE,oBAAoB;AACtB;;AAEA;EACE,gCAAgC;AAClC;;AAEA;EACE,gCAAgC;AAClC;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;EACE,4BAA4B;EAC5B,6BAA6B;AAC/B;;AAEA;EACE,iCAAiC;EACjC,8BAA8B;EAC9B,gCAAgC;EAChC,wCAAwC;AAC1C;;AAEA;EACE,iCAAiC;EACjC,oDAAoD;AACtD;;AAEA,6BAA6B;AAC7B;EACE,8BAA8B;EAC9B,mCAAmC;EACnC,oDAAoD;EACpD,mCAA2B;UAA3B,2BAA2B;EAC3B,+BAA+B;EAC/B,iDAAiD;EACjD,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,WAAW;EACX,kFAAkF;EAClF,YAAY;AACd;;AAEA;EACE,oCAAoC;EACpC,mEAAmE;EACnE,2BAA2B;AAC7B;;AAEA,yBAAyB;AACzB;EACE,2BAA2B;EAC3B,qCAAqC;EACrC,wCAAwC;EACxC,sCAAsC;EACtC,kBAAkB;EAClB,yBAAyB;EACzB,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT,6FAA6F;EAC7F,UAAU;EACV,6BAA6B;AAC/B;;AAEA;EACE,UAAU;AACZ;;AAEA;EACE,kCAAkC;EAClC,6CAA6C;EAC7C,sBAAsB;AACxB;;AAEA,oCAAoC;AACpC;EACE,sEAAsE;EACtE,kDAAkD;EAClD,4CAA4C;EAC5C,gBAAgB;EAChB,yBAAyB;EACzB,qBAAqB;EACrB,iBAAiB;EACjB,mCAA2B;UAA3B,2BAA2B;EAC3B,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,kCAAkC;EAClC,4BAA4B;EAC5B,mCAAmC;EACnC,2CAA2C;AAC7C;;AAEA;EACE,kCAAkC;EAClC,+BAA+B;EAC/B,sCAAsC;EACtC,2CAA2C;AAC7C;;AAEA;EACE,kCAAkC;EAClC,2BAA2B;EAC3B,kCAAkC;EAClC,2CAA2C;AAC7C;;AAEA;EACE,mCAAmC;EACnC,iCAAiC;EACjC,wCAAwC;EACxC,4CAA4C;AAC9C;;AAEA,4BAA4B;AAC5B;EACE,+BAA+B;EAC/B,mCAAmC;EACnC,mCAA2B;UAA3B,2BAA2B;EAC3B,yCAAyC;EACzC,kBAAkB;AACpB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,SAAS;EACT,OAAO;EACP,QAAQ;EACR,WAAW;EACX,kFAAkF;EAClF,YAAY;AACd;;AAEA,gCAAgC;AAChC;EACE,2BAA2B;EAC3B,qCAAqC;EACrC,mCAA2B;UAA3B,2BAA2B;EAC3B,iDAAiD;EACjD,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,kCAAkC;EAClC,oCAAoC;EACpC,6CAA6C;EAC7C,sBAAsB;AACxB;;AAEA;EACE,2FAA2F;EAC3F,kCAAkC;EAClC,2CAA2C;AAC7C;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,QAAQ;EACR,iFAAiF;EACjF,oBAAoB;AACtB;;AAEA,6EAA6E;AAC7E;EACE,0BAA0B;EAC1B,uBAAuB;EACvB,wBAAwB;EACxB,eAAe;EACf,MAAM;EACN,OAAO;EACP,gBAAgB;EAChB,mCAAmC;AACrC;;AAEA,qDAAqD;AACrD;EACE;IACE,qBAAqB;IACrB,mCAAmC;IACnC,oCAAoC;EACtC;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,mCAAmC;IACnC,oCAAoC;EACtC;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,mCAAmC;IACnC,oCAAoC;EACtC;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,mCAAmC;IACnC,oCAAoC;EACtC;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,mCAAmC;IACnC,oCAAoC;EACtC;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA,0BAA0B;AAC1B;EACE,kBAAkB;AACpB;;AAEA,wDAAwD;AACxD;EACE,kBAAkB;AACpB;;AAEA,4CAA4C;AAC5C;;;EAGE,wEAAwE;AAC1E;;AAEA,oCAAoC;AACpC;EACE,wEAAwE;AAC1E;;AAEA,iCAAiC;AACjC;EACE,wEAAwE;AAC1E;;AAEA,+BAA+B;AAC/B;EACE,kCAAkC;EAClC,mCAAmC;AACrC;;AAEA,sBAAsB;AACtB,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,qDAAqD,EAAE;AAC9D,OAAO,qDAAqD,EAAE;AAC9D,OAAO,qDAAqD,EAAE;AAC9D,OAAO,qDAAqD,EAAE;;AAE9D,OAAO,mDAAmD,EAAE;AAC5D,OAAO,mDAAmD,EAAE;AAC5D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;;AAE7D,eAAe;AACf,SAAS,gDAAgD,EAAE;AAC3D,SAAS,gDAAgD,EAAE;AAC3D,SAAS,iDAAiD,EAAE;AAC5D,SAAS,iDAAiD,EAAE;AAC5D,SAAS,iDAAiD,EAAE;AAC5D,SAAS,iDAAiD,EAAE;;AAE5D,wBAAwB;AACxB,WAAW,0DAA0D,EAAE;AACvE,cAAc,0DAA0D,EAAE;AAC1E,cAAc,0DAA0D,EAAE;AAC1E,cAAc,2DAA2D,EAAE;AAC3E,eAAe,2DAA2D,EAAE;;AAE5E;EACE;IACE,UAAU;IACV,2BAA2B;EAC7B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE;IACE,UAAU;EACZ;EACA;IACE,UAAU;EACZ;AACF;;AAEA;EACE,kCAAkC;AACpC;;AAEA,sDAAsD;AACtD,WAAW,uDAAuD,EAAE;AACpE,WAAW,uDAAuD,EAAE;AACpE,aAAa,uDAAuD,EAAE;AACtE,WAAW,uDAAuD,EAAE;AACpE,WAAW,uDAAuD,EAAE;AACpE,YAAY,uDAAuD,EAAE;AACrE,YAAY,uDAAuD,EAAE;AACrE,YAAY,uDAAuD,EAAE;;AAErE,6CAA6C;AAC7C,OAAO,mDAAmD,EAAE;AAC5D,OAAO,mDAAmD,EAAE;AAC5D,OAAO,mDAAmD,EAAE;AAC5D,OAAO,mDAAmD,EAAE;AAC5D,OAAO,mDAAmD,EAAE;AAC5D,QAAQ,mDAAmD,EAAE;AAC7D,QAAQ,mDAAmD,EAAE;;AAE7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,QAAQ,oDAAoD,EAAE;AAC9D,QAAQ,oDAAoD,EAAE;;AAE9D,qDAAqD;AACrD;EACE,aAAa;EACb,YAAY;EACZ,aAAa;EACb,sBAAsB;EACtB,gBAAgB;AAClB;;AAEA;EACE,OAAO;EACP,aAAa;EACb,aAAa;EACb,gBAAgB;AAClB;;AAEA;EACE,OAAO;EACP,aAAa;EACb,sBAAsB;EACtB,aAAa;EACb,gBAAgB;AAClB;;AAEA;EACE,4BAA4B;EAC5B,cAAc;EACd,wCAAwC;AAC1C;;AAEA;EACE,OAAO;EACP,aAAa;EACb,gBAAgB;EAChB,kBAAkB;EAClB,wCAAwC;AAC1C;;AAEA;EACE,wCAAwC;EACxC,cAAc;EACd,wCAAwC;AAC1C;;AAEA;EACE,2BAA2B;EAC3B,cAAc;AAChB;;AAEA;EACE,2BAA2B;EAC3B,cAAc;AAChB;;AAEA,iDAAiD;AACjD;EACE,aAAa;EACb,iCAAiC;EACjC,YAAY;EACZ,gBAAgB;EAChB,wCAAwC;AAC1C;;AAEA;EACE,iCAAiC;EACjC,0BAA0B;AAC5B;;AAEA;EACE,4BAA4B;EAC5B,8BAA8B;EAC9B,8BAA8B;AAChC;;AAEA,0BAA0B;AAC1B;EACE,qGAAqG;EACrG,gBAAgB;AAClB;;AAEA;EACE,YAAY;EACZ,gBAAgB;EAChB,aAAa;EACb,sBAAsB;AACxB;;AAEA,wBAAwB;AACxB;EACE;IACE,0CAA0C;EAC5C;EACA;IACE,4EAA4E;EAC9E;AACF;;AAEA;EACE;IACE,UAAU;IACV,2BAA2B;EAC7B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE;IACE,UAAU;IACV,sBAAsB;EACxB;EACA;IACE,UAAU;IACV,mBAAmB;EACrB;AACF;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,oCAAoC;AACtC;;AAEA;EACE,sCAAsC;AACxC;;AAEA,kBAAkB;AAClB;EACE,iDAAiD;AACnD;;AAEA;EACE,2BAA2B;EAC3B,0EAA0E;AAC5E;;AAEA,4BAA4B;AAC5B;EACE,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,WAAW;EACX,WAAW;EACX,YAAY;EACZ,sFAAsF;EACtF,8BAA8B;AAChC;;AAEA;EACE;IACE,WAAW;EACb;EACA;IACE,UAAU;EACZ;AACF;;AAEA,wBAAwB;AACxB;EACE,aAAa;EACb,oBAAoB;EACpB,YAAY;EACZ,gBAAgB;AAClB;;AAEA;EACE,8BAA8B;AAChC;;AAEA;EACE,kCAAkC;AACpC;;AAEA;EACE,sCAAsC;AACxC;;AAEA;EACE,wFAAwF;AAC1F;;AAEA,0BAA0B;AAC1B;EACE,8CAA8C;EAC9C,8CAA8C;AAChD;;AAEA;EACE,iCAAiC;EACjC,8CAA8C;AAChD;;AAEA;EACE,8CAA8C;EAC9C,8CAA8C;AAChD;;AAEA,iCAAiC;AACjC;EACE,aAAa;EACb,uBAAuB;EACvB,mBAAmB;EACnB,0CAA0C;EAC1C,cAAc;EACd,+CAA+C,EAAE,wBAAwB;AAC3E;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,mBAAmB;EACnB,uBAAuB;EACvB,wCAAwC;EACxC,yCAAyC;EACzC,qCAAqC;EACrC,gDAAgD;EAChD,qDAAqD;EACrD;;kIAEgI;EAChI,0CAA0C;EAC1C,yBAAyB;EACzB,eAAe;EACf,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,wDAAwD;EACxD;;mIAEiI;AACnI;;AAEA;EACE,qDAAqD;EACrD;;uIAEqI;EACrI,0CAA0C;AAC5C;;AAEA;EACE,wCAAwC;EACxC,yCAAyC;EACzC,+CAA+C;EAC/C,cAAc;EACd,2BAA2B;AAC7B;;AAEA;;EAEE,cAAc;AAChB;;AAEA;EACE,4CAA4C;EAC5C,gBAAgB;EAChB,cAAc;EACd,kBAAkB;EAClB,gBAAgB;EAChB,2BAA2B;AAC7B;;AAEA;;EAEE,cAAc;AAChB;;AAEA,oBAAoB;AACpB;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT,2FAA2F;EAC3F,UAAU;EACV,6BAA6B;EAC7B,sBAAsB;AACxB;;AAEA;;EAEE,UAAU;AACZ;;AAEA,kDAAkD;AAClD;EACE,aAAa;EACb,sBAAsB;EACtB,oBAAoB;EACpB,2BAA2B;EAC3B,iCAAiC;EACjC,sCAAsC;EACtC,YAAY;EACZ,gBAAgB;AAClB;;AAEA,8CAA8C;AAC9C;EACE,WAAW;EACX,yCAAyC;EACzC,SAAS;EACT,cAAc;EACd,aAAa;EACb,mBAAmB;EACnB,2BAA2B;EAC3B,sEAAsE;EACtE,2BAA2B;EAC3B,qCAAqC;EACrC,wCAAwC;EACxC,mCAA2B;UAA3B,2BAA2B;EAC3B,iDAAiD;EACjD,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,kCAAkC;EAClC,oCAAoC;EACpC,0BAA0B;EAC1B,6CAA6C;AAC/C;;AAEA;EACE,2FAA2F;EAC3F,kCAAkC;EAClC,mFAAmF;AACrF;;AAEA,yBAAyB;AACzB;EACE,wCAAwC;EACxC,yCAAyC;EACzC,6CAA6C;EAC7C,wBAAwB;EACxB,yBAAyB;EACzB,cAAc;AAChB;;AAEA;EACE,2BAA2B;EAC3B,mDAAmD;AACrD;;AAEA;EACE,8BAA8B;EAC9B,oDAAoD;AACtD;;AAEA,8CAA8C;AAC9C;EACE,wBAAwB;EACxB,4CAA4C;EAC5C,gBAAgB;EAChB,gBAAgB;EAChB,mBAAmB;EACnB,yBAAyB;EACzB,yCAAyC;AAC3C;;AAEA;EACE,8BAA8B;EAC9B,2CAA2C;AAC7C;;AAEA;EACE,8BAA8B;EAC9B,gBAAgB;EAChB,4CAA4C;AAC9C;;AAx5BA;EAAA,kBAy5BA;EAz5BA,kBAy5BA;EAz5BA;AAy5BA;;AAz5BA;EAAA;AAy5BA;;AAz5BA;EAAA;AAy5BA;;AAz5BA;EAAA;AAy5BA;;AAz5BA;EAAA;AAy5BA;;AAz5BA;EAAA;AAy5BA;;AAz5BA;EAAA;AAy5BA;;AAz5BA;EAAA;AAy5BA;;AAz5BA;EAAA;AAy5BA;;AAz5BA;EAAA;AAy5BA;;AAz5BA;EAAA;AAy5BA;;AAz5BA;EAAA,oBAy5BA;EAz5BA;AAy5BA;;AAz5BA;EAAA,+EAy5BA;EAz5BA,mGAy5BA;EAz5BA;AAy5BA;;AAz5BA;EAAA,wCAy5BA;EAz5BA;AAy5BA;;AAz5BA;EAAA,8BAy5BA;EAz5BA;AAy5BA;;AAz5BA;EAAA,2GAy5BA;EAz5BA,yGAy5BA;EAz5BA;AAy5BA;;AAz5BA;EAAA,2GAy5BA;EAz5BA,yGAy5BA;EAz5BA;AAy5BA;;AAz5BA;EAAA,oBAy5BA;EAz5BA;AAy5BA;;AAz5BA;EAAA,oBAy5BA;EAz5BA;AAy5BA;;AAz5BA;EAAA;AAy5BA;;AAz5BA;EAAA;AAy5BA;;AAz5BA;EAAA;AAy5BA;;AAz5BA;EAAA;AAy5BA;;AAz5BA;EAAA;AAy5BA;;AAz5BA;EAAA,kBAy5BA;EAz5BA,kBAy5BA;EAz5BA;AAy5BA;;AAz5BA;EAAA,iBAy5BA;EAz5BA,iBAy5BA;EAz5BA;AAy5BA;;AAz5BA;EAAA,oBAy5BA;EAz5BA;AAy5BA;;AAz5BA;EAAA,oBAy5BA;EAz5BA;AAy5BA;;AAz5BA;EAAA;AAy5BA;;AAz5BA;;EAAA;;IAAA;MAAA;IAy5BA;EAAA;;EAz5BA;IAAA;EAy5BA;AAAA;;AAz5BA;;EAAA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA,kBAy5BA;IAz5BA;EAy5BA;;EAz5BA;IAAA,mBAy5BA;IAz5BA;EAy5BA;;EAz5BA;IAAA,eAy5BA;IAz5BA;EAy5BA;;EAz5BA;IAAA,mBAy5BA;IAz5BA;EAy5BA;;EAz5BA;IAAA,mBAy5BA;IAz5BA;EAy5BA;;EAz5BA;IAAA,kBAy5BA;IAz5BA;EAy5BA;;EAz5BA;IAAA,kBAy5BA;IAz5BA;EAy5BA;AAAA;;AAz5BA;;EAAA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;AAAA;;AAz5BA;;EAAA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA;EAy5BA;;EAz5BA;IAAA,kBAy5BA;IAz5BA;EAy5BA;AAAA\",\"sourcesContent\":[\"@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');\\n\\n@tailwind base;\\n@tailwind components;\\n@tailwind utilities;\\n\\n/* CSS Custom Properties for Responsive Scaling - Optimized for Content Fit */\\n:root {\\n  --scale-factor: 1;\\n  --content-scale: 21;\\n  --base-font-size: calc(14px * var(--content-scale));\\n  --base-spacing: calc(6px * var(--content-scale));\\n  --base-border-radius: calc(8px * var(--content-scale));\\n  --base-icon-size: calc(20px * var(--content-scale));\\n  --base-button-height: calc(36px * var(--content-scale));\\n  --base-card-padding: calc(1px * var(--content-scale));\\n  --base-gap: calc(6px * var(--content-scale));\\n  --header-height: calc(60px * var(--content-scale));\\n  --footer-height: calc(50px * var(--content-scale));\\n  --sidebar-width: calc(80px * var(--content-scale));\\n\\n  /* HUD/HDR Color Palette */\\n  --hud-bg-primary: #311861;\\n  --hud-bg-secondary: #22184f;\\n  --hud-bg-panel: rgba(15, 20, 25, 0.85);\\n  --hud-bg-card: rgba(20, 30, 45, 0.6);\\n  --hud-border: rgba(0, 207, 255, 0.2);\\n  --hud-border-glow: rgba(0, 207, 255, 0.4);\\n\\n  /* HUD Accent Colors */\\n  --hud-neon-blue: #00cfff;\\n  --hud-aqua-green: #00ffbd;\\n  --hud-energy-orange: #ffa500;\\n  --hud-electric-purple: #bc13fe;\\n  --hud-deep-red: #ff4b4b;\\n  --hud-bright-white: #ffffff;\\n  --hud-silver: #b0b8c4;\\n\\n  /* Glassmorphism Effects */\\n  --glass-bg: rgba(255, 255, 255, 0.05);\\n  --glass-border: rgba(255, 255, 255, 0.1);\\n  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\\n}\\n\\n/* Responsive scaling variables - now handled by --content-scale */\\n@media (max-width: 1920px) and (min-width: 1600px) {\\n  :root {\\n    --scale-factor: 0.9;\\n    --content-scale: 0.9;\\n  }\\n}\\n\\n@media (max-width: 1599px) and (min-width: 1400px) {\\n  :root {\\n    --scale-factor: 0.8;\\n    --content-scale: 0.8;\\n  }\\n}\\n\\n@media (max-width: 1399px) and (min-width: 1200px) {\\n  :root {\\n    --scale-factor: 0.7;\\n    --content-scale: 0.7;\\n  }\\n}\\n\\n@media (max-width: 1199px) and (min-width: 1000px) {\\n  :root {\\n    --scale-factor: 0.6;\\n    --content-scale: 0.6;\\n  }\\n}\\n\\n@media (max-width: 999px) {\\n  :root {\\n    --scale-factor: 0.5;\\n    --content-scale: 0.5;\\n  }\\n}\\n\\n/* Ensure base styles are applied */\\n* {\\n  box-sizing: border-box;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nhtml, body {\\n  height: 100vh;\\n  width: 100vw;\\n  overflow: hidden;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nbody {\\n  font-family: 'Poppins', sans-serif;\\n  font-size: var(--base-font-size);\\n  line-height: 1.5;\\n}\\n\\n#__next {\\n  height: 100vh;\\n  width: 100vw;\\n  overflow: hidden;\\n}\\n\\n.main-background {\\n  background: linear-gradient(135deg, var(--hud-bg-primary) 0%, var(--hud-bg-secondary) 50%, #0a0f1c 100%);\\n  background-attachment: fixed;\\n  position: relative;\\n}\\n\\n.main-background::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(ellipse at 20% 30%, rgba(0, 207, 255, 0.1) 0%, transparent 50%),\\n              radial-gradient(ellipse at 80% 70%, rgba(188, 19, 254, 0.08) 0%, transparent 50%),\\n              radial-gradient(ellipse at 50% 50%, rgba(0, 255, 189, 0.05) 0%, transparent 70%);\\n  pointer-events: none;\\n}\\n\\n/* Global Responsive Classes */\\n.responsive-text {\\n  font-size: var(--base-font-size);\\n}\\n\\n.responsive-text-sm {\\n  font-size: calc(var(--base-font-size) * 0.875);\\n}\\n\\n.responsive-text-xs {\\n  font-size: calc(var(--base-font-size) * 0.75);\\n}\\n\\n.responsive-text-lg {\\n  font-size: calc(var(--base-font-size) * 1.125);\\n}\\n\\n.responsive-text-xl {\\n  font-size: calc(var(--base-font-size) * 1.25);\\n}\\n\\n.responsive-text-2xl {\\n  font-size: calc(var(--base-font-size) * 1.5);\\n}\\n\\n.responsive-spacing {\\n  padding: var(--base-spacing);\\n}\\n\\n.responsive-spacing-sm {\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.responsive-spacing-lg {\\n  padding: calc(var(--base-spacing) * 1.5);\\n}\\n\\n.responsive-gap {\\n  gap: var(--base-gap);\\n}\\n\\n.responsive-gap-sm {\\n  gap: calc(var(--base-gap) * 0.5);\\n}\\n\\n.responsive-gap-lg {\\n  gap: calc(var(--base-gap) * 1.5);\\n}\\n\\n.responsive-border-radius {\\n  border-radius: var(--base-border-radius);\\n}\\n\\n.responsive-icon {\\n  width: var(--base-icon-size);\\n  height: var(--base-icon-size);\\n}\\n\\n.responsive-button {\\n  height: var(--base-button-height);\\n  padding: 0 var(--base-spacing);\\n  font-size: var(--base-font-size);\\n  border-radius: var(--base-border-radius);\\n}\\n\\n.responsive-card {\\n  padding: var(--base-card-padding);\\n  border-radius: calc(var(--base-border-radius) * 1.5);\\n}\\n\\n/* Enhanced HUD Card System */\\n.hud-card {\\n  background: var(--hud-bg-card);\\n  border: 1px solid var(--hud-border);\\n  border-radius: calc(var(--base-border-radius) * 1.5);\\n  backdrop-filter: blur(20px);\\n  box-shadow: var(--glass-shadow);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.hud-card::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(90deg, transparent, var(--hud-neon-blue), transparent);\\n  opacity: 0.6;\\n}\\n\\n.hud-card:hover {\\n  border-color: var(--hud-border-glow);\\n  box-shadow: 0 8px 40px rgba(0, 207, 255, 0.15), var(--glass-shadow);\\n  transform: translateY(-2px);\\n}\\n\\n/* Compact metric cards */\\n.metric-card {\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  border-radius: var(--base-border-radius);\\n  padding: calc(var(--base-spacing) * 2);\\n  text-align: center;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.metric-card::after {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(0, 207, 255, 0.05) 0%, rgba(0, 255, 189, 0.05) 100%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.metric-card:hover::after {\\n  opacity: 1;\\n}\\n\\n.metric-card:hover {\\n  border-color: var(--hud-neon-blue);\\n  box-shadow: 0 4px 20px rgba(0, 207, 255, 0.2);\\n  transform: scale(1.02);\\n}\\n\\n/* Status badges with glow effects */\\n.status-badge {\\n  padding: calc(var(--base-spacing) * 0.5) calc(var(--base-spacing) * 1);\\n  border-radius: calc(var(--base-border-radius) * 2);\\n  font-size: calc(10px * var(--content-scale));\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  border: 1px solid;\\n  backdrop-filter: blur(10px);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.status-badge.active {\\n  background: rgba(0, 255, 189, 0.2);\\n  color: var(--hud-aqua-green);\\n  border-color: var(--hud-aqua-green);\\n  box-shadow: 0 0 10px rgba(0, 255, 189, 0.3);\\n}\\n\\n.status-badge.beta {\\n  background: rgba(255, 165, 0, 0.2);\\n  color: var(--hud-energy-orange);\\n  border-color: var(--hud-energy-orange);\\n  box-shadow: 0 0 10px rgba(255, 165, 0, 0.3);\\n}\\n\\n.status-badge.live {\\n  background: rgba(0, 207, 255, 0.2);\\n  color: var(--hud-neon-blue);\\n  border-color: var(--hud-neon-blue);\\n  box-shadow: 0 0 10px rgba(0, 207, 255, 0.3);\\n}\\n\\n.status-badge.testing {\\n  background: rgba(188, 19, 254, 0.2);\\n  color: var(--hud-electric-purple);\\n  border-color: var(--hud-electric-purple);\\n  box-shadow: 0 0 10px rgba(188, 19, 254, 0.3);\\n}\\n\\n/* Enhanced Header Styling */\\n.header-hud {\\n  background: var(--hud-bg-panel);\\n  border: 1px solid var(--hud-border);\\n  backdrop-filter: blur(20px);\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n  position: relative;\\n}\\n\\n.header-hud::before {\\n  content: '';\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(90deg, transparent, var(--hud-neon-blue), transparent);\\n  opacity: 0.4;\\n}\\n\\n/* Enhanced Department Buttons */\\n.dept-button {\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  backdrop-filter: blur(10px);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.dept-button:hover {\\n  background: rgba(0, 207, 255, 0.1);\\n  border-color: var(--hud-border-glow);\\n  box-shadow: 0 4px 20px rgba(0, 207, 255, 0.2);\\n  transform: scale(1.05);\\n}\\n\\n.dept-button.active {\\n  background: linear-gradient(135deg, rgba(0, 207, 255, 0.2) 0%, rgba(0, 255, 189, 0.1) 100%);\\n  border-color: var(--hud-neon-blue);\\n  box-shadow: 0 0 20px rgba(0, 207, 255, 0.3);\\n}\\n\\n.dept-button.active::after {\\n  content: '';\\n  position: absolute;\\n  inset: 0;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\\n  pointer-events: none;\\n}\\n\\n/* Dashboard Auto-Scale - Scales ALL content including text, icons, spacing */\\n.dashboard-auto-scale {\\n  transform-origin: top left;\\n  width: 100vw !important;\\n  height: 100vh !important;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  overflow: hidden;\\n  transition: transform 0.3s ease-out;\\n}\\n\\n/* Scale everything - content, text, icons, spacing */\\n@media (max-width: 1920px) and (min-width: 1600px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.9);\\n    width: calc(100vw / 0.9) !important;\\n    height: calc(100vh / 0.9) !important;\\n  }\\n  :root {\\n    --content-scale: 0.9;\\n  }\\n}\\n\\n@media (max-width: 1599px) and (min-width: 1400px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.8);\\n    width: calc(100vw / 0.8) !important;\\n    height: calc(100vh / 0.8) !important;\\n  }\\n  :root {\\n    --content-scale: 0.8;\\n  }\\n}\\n\\n@media (max-width: 1399px) and (min-width: 1200px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.7);\\n    width: calc(100vw / 0.7) !important;\\n    height: calc(100vh / 0.7) !important;\\n  }\\n  :root {\\n    --content-scale: 0.7;\\n  }\\n}\\n\\n@media (max-width: 1199px) and (min-width: 1000px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.6);\\n    width: calc(100vw / 0.6) !important;\\n    height: calc(100vh / 0.6) !important;\\n  }\\n  :root {\\n    --content-scale: 0.6;\\n  }\\n}\\n\\n@media (max-width: 999px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.5);\\n    width: calc(100vw / 0.5) !important;\\n    height: calc(100vh / 0.5) !important;\\n  }\\n  :root {\\n    --content-scale: 0.5;\\n  }\\n}\\n\\n/* Default content scale */\\n:root {\\n  --content-scale: 1;\\n}\\n\\n/* Universal content scaling - applies to ALL elements */\\n* {\\n  font-size: inherit;\\n}\\n\\n/* Scale all text elements in main content */\\n.main-section h1, .main-section h2, .main-section h3, .main-section h4, .main-section h5, .main-section h6,\\n.main-section p, .main-section span, .main-section div, .main-section button,\\n.main-section input, .main-section textarea, .main-section select, .main-section label, .main-section a {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale card content specifically */\\n.card-content * {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale chart text and numbers */\\n.recharts-text, .recharts-label, .chart-text, .metric-value, .metric-label {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale all icons and images */\\nsvg, img, .icon {\\n  width: calc(var(--base-icon-size));\\n  height: calc(var(--base-icon-size));\\n}\\n\\n/* Scale all spacing */\\n.p-1 { padding: calc(4px * var(--content-scale)) !important; }\\n.p-2 { padding: calc(8px * var(--content-scale)) !important; }\\n.p-3 { padding: calc(12px * var(--content-scale)) !important; }\\n.p-4 { padding: calc(16px * var(--content-scale)) !important; }\\n.p-5 { padding: calc(20px * var(--content-scale)) !important; }\\n.p-6 { padding: calc(24px * var(--content-scale)) !important; }\\n\\n.m-1 { margin: calc(4px * var(--content-scale)) !important; }\\n.m-2 { margin: calc(8px * var(--content-scale)) !important; }\\n.m-3 { margin: calc(12px * var(--content-scale)) !important; }\\n.m-4 { margin: calc(16px * var(--content-scale)) !important; }\\n.m-5 { margin: calc(20px * var(--content-scale)) !important; }\\n.m-6 { margin: calc(24px * var(--content-scale)) !important; }\\n\\n/* Scale gaps */\\n.gap-1 { gap: calc(4px * var(--content-scale)) !important; }\\n.gap-2 { gap: calc(8px * var(--content-scale)) !important; }\\n.gap-3 { gap: calc(12px * var(--content-scale)) !important; }\\n.gap-4 { gap: calc(16px * var(--content-scale)) !important; }\\n.gap-5 { gap: calc(20px * var(--content-scale)) !important; }\\n.gap-6 { gap: calc(24px * var(--content-scale)) !important; }\\n\\n/* Scale border radius */\\n.rounded { border-radius: calc(4px * var(--content-scale)) !important; }\\n.rounded-md { border-radius: calc(6px * var(--content-scale)) !important; }\\n.rounded-lg { border-radius: calc(8px * var(--content-scale)) !important; }\\n.rounded-xl { border-radius: calc(12px * var(--content-scale)) !important; }\\n.rounded-2xl { border-radius: calc(16px * var(--content-scale)) !important; }\\n\\n@keyframes fade-in-up {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n.animate-fade-in-up {\\n  animation: fade-in-up 0.3s ease-out forwards;\\n}\\n\\n@keyframes fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n.animate-fadeIn {\\n  animation: fadeIn 0.5s ease-in-out;\\n}\\n\\n/* Override Tailwind text sizes with content scaling */\\n.text-xs { font-size: calc(12px * var(--content-scale)) !important; }\\n.text-sm { font-size: calc(14px * var(--content-scale)) !important; }\\n.text-base { font-size: calc(16px * var(--content-scale)) !important; }\\n.text-lg { font-size: calc(18px * var(--content-scale)) !important; }\\n.text-xl { font-size: calc(20px * var(--content-scale)) !important; }\\n.text-2xl { font-size: calc(24px * var(--content-scale)) !important; }\\n.text-3xl { font-size: calc(30px * var(--content-scale)) !important; }\\n.text-4xl { font-size: calc(36px * var(--content-scale)) !important; }\\n\\n/* Override Tailwind width/height for icons */\\n.w-3 { width: calc(12px * var(--content-scale)) !important; }\\n.w-4 { width: calc(16px * var(--content-scale)) !important; }\\n.w-5 { width: calc(20px * var(--content-scale)) !important; }\\n.w-6 { width: calc(24px * var(--content-scale)) !important; }\\n.w-8 { width: calc(32px * var(--content-scale)) !important; }\\n.w-10 { width: calc(40px * var(--content-scale)) !important; }\\n.w-12 { width: calc(48px * var(--content-scale)) !important; }\\n\\n.h-3 { height: calc(12px * var(--content-scale)) !important; }\\n.h-4 { height: calc(16px * var(--content-scale)) !important; }\\n.h-5 { height: calc(20px * var(--content-scale)) !important; }\\n.h-6 { height: calc(24px * var(--content-scale)) !important; }\\n.h-8 { height: calc(32px * var(--content-scale)) !important; }\\n.h-10 { height: calc(40px * var(--content-scale)) !important; }\\n.h-12 { height: calc(48px * var(--content-scale)) !important; }\\n\\n/* Optimized Layout Classes for Perfect Content Fit */\\n.main-layout {\\n  height: 100vh;\\n  width: 100vw;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.content-area {\\n  flex: 1;\\n  display: flex;\\n  min-height: 0;\\n  overflow: hidden;\\n}\\n\\n.main-content {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 0;\\n  overflow: hidden;\\n}\\n\\n.header-section {\\n  height: var(--header-height);\\n  flex-shrink: 0;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.main-section {\\n  flex: 1;\\n  min-height: 0;\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.footer-section {\\n  height: calc(var(--footer-height) * 1.5);\\n  flex-shrink: 0;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.sidebar-left {\\n  width: var(--sidebar-width);\\n  flex-shrink: 0;\\n}\\n\\n.sidebar-right {\\n  width: var(--sidebar-width);\\n  flex-shrink: 0;\\n}\\n\\n/* Enhanced Dashboard Grid for No-Scroll Layout */\\n.dashboard-grid {\\n  display: grid;\\n  gap: calc(var(--base-gap) * 0.75);\\n  height: 100%;\\n  overflow: hidden;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.dashboard-grid-teacher {\\n  grid-template-rows: auto 1fr auto;\\n  grid-template-columns: 1fr;\\n}\\n\\n.dashboard-grid-school {\\n  grid-template-rows: auto 1fr;\\n  grid-template-columns: 1fr 1fr;\\n  gap: calc(var(--base-gap) * 1);\\n}\\n\\n/* Compact content areas */\\n.content-compact {\\n  max-height: calc(100vh - var(--header-height) - var(--footer-height) - calc(var(--base-spacing) * 4));\\n  overflow-y: auto;\\n}\\n\\n.content-no-scroll {\\n  height: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n/* Enhanced Animations */\\n@keyframes pulse-glow {\\n  0%, 100% {\\n    box-shadow: 0 0 5px rgba(0, 207, 255, 0.3);\\n  }\\n  50% {\\n    box-shadow: 0 0 20px rgba(0, 207, 255, 0.6), 0 0 30px rgba(0, 207, 255, 0.4);\\n  }\\n}\\n\\n@keyframes slide-in-up {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes fade-in-scale {\\n  from {\\n    opacity: 0;\\n    transform: scale(0.95);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n}\\n\\n.animate-pulse-glow {\\n  animation: pulse-glow 2s ease-in-out infinite;\\n}\\n\\n.animate-slide-in-up {\\n  animation: slide-in-up 0.5s ease-out;\\n}\\n\\n.animate-fade-in-scale {\\n  animation: fade-in-scale 0.3s ease-out;\\n}\\n\\n/* Hover Effects */\\n.hover-lift {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.hover-lift:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 207, 255, 0.2);\\n}\\n\\n/* Progress Bar Animations */\\n.progress-bar {\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.progress-bar::after {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\\n  animation: shimmer 2s infinite;\\n}\\n\\n@keyframes shimmer {\\n  0% {\\n    left: -100%;\\n  }\\n  100% {\\n    left: 100%;\\n  }\\n}\\n\\n/* Optimized Card Grid */\\n.card-grid {\\n  display: grid;\\n  gap: var(--base-gap);\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n.card-grid-2 {\\n  grid-template-columns: 1fr 1fr;\\n}\\n\\n.card-grid-3 {\\n  grid-template-columns: 1fr 1fr 1fr;\\n}\\n\\n.card-grid-4 {\\n  grid-template-columns: 1fr 1fr 1fr 1fr;\\n}\\n\\n.card-grid-auto {\\n  grid-template-columns: repeat(auto-fit, minmax(calc(250px * var(--content-scale)), 1fr));\\n}\\n\\n/* Optimized Card Sizing */\\n.card-compact {\\n  padding: calc(var(--base-card-padding) * 0.75);\\n  min-height: calc(120px * var(--content-scale));\\n}\\n\\n.card-standard {\\n  padding: var(--base-card-padding);\\n  min-height: calc(160px * var(--content-scale));\\n}\\n\\n.card-large {\\n  padding: calc(var(--base-card-padding) * 1.25);\\n  min-height: calc(200px * var(--content-scale));\\n}\\n\\n/* Neumorphic HUD Subnav Styles */\\n.subnav-hud {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  padding: calc(var(--base-spacing) * 1.5) 0;\\n  margin: 0 auto;\\n  max-width: calc(72px * 8 + var(--base-gap) * 7); /* 8 buttons with gaps */\\n}\\n\\n.subnav-button {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  width: calc(72px * var(--content-scale));\\n  height: calc(72px * var(--content-scale));\\n  margin: 0 calc(var(--base-gap) * 0.5);\\n  border-radius: calc(16px * var(--content-scale));\\n  background: linear-gradient(145deg, #2a2a3a, #1a1a2a);\\n  box-shadow:\\n    calc(8px * var(--content-scale)) calc(8px * var(--content-scale)) calc(16px * var(--content-scale)) rgba(0, 0, 0, 0.4),\\n    calc(-4px * var(--content-scale)) calc(-4px * var(--content-scale)) calc(8px * var(--content-scale)) rgba(255, 255, 255, 0.05);\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.subnav-button:hover {\\n  transform: translateY(calc(-2px * var(--content-scale)));\\n  box-shadow:\\n    calc(12px * var(--content-scale)) calc(12px * var(--content-scale)) calc(24px * var(--content-scale)) rgba(0, 0, 0, 0.5),\\n    calc(-6px * var(--content-scale)) calc(-6px * var(--content-scale)) calc(12px * var(--content-scale)) rgba(255, 255, 255, 0.08);\\n}\\n\\n.subnav-button.active {\\n  background: linear-gradient(145deg, #3a4a6a, #2a3a5a);\\n  box-shadow:\\n    inset calc(4px * var(--content-scale)) calc(4px * var(--content-scale)) calc(8px * var(--content-scale)) rgba(0, 0, 0, 0.3),\\n    inset calc(-2px * var(--content-scale)) calc(-2px * var(--content-scale)) calc(4px * var(--content-scale)) rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(100, 200, 255, 0.3);\\n}\\n\\n.subnav-icon {\\n  width: calc(24px * var(--content-scale));\\n  height: calc(24px * var(--content-scale));\\n  margin-bottom: calc(4px * var(--content-scale));\\n  color: #a0a0b0;\\n  transition: color 0.3s ease;\\n}\\n\\n.subnav-button:hover .subnav-icon,\\n.subnav-button.active .subnav-icon {\\n  color: #64b5f6;\\n}\\n\\n.subnav-label {\\n  font-size: calc(10px * var(--content-scale));\\n  font-weight: 500;\\n  color: #a0a0b0;\\n  text-align: center;\\n  line-height: 1.2;\\n  transition: color 0.3s ease;\\n}\\n\\n.subnav-button:hover .subnav-label,\\n.subnav-button.active .subnav-label {\\n  color: #64b5f6;\\n}\\n\\n/* HUD Glow Effect */\\n.subnav-button::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(circle at center, rgba(100, 181, 246, 0.1) 0%, transparent 70%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n  border-radius: inherit;\\n}\\n\\n.subnav-button:hover::before,\\n.subnav-button.active::before {\\n  opacity: 1;\\n}\\n\\n/* Enhanced HUD Vertical Layout for Left Sidebar */\\n.subnav-hud-vertical {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: stretch;\\n  justify-content: flex-start;\\n  gap: calc(var(--base-gap) * 0.75);\\n  padding: calc(var(--base-spacing) * 1);\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n/* Enhanced subnav button with glassmorphism */\\n.subnav-hud-vertical .subnav-button {\\n  width: 100%;\\n  height: calc(42px * var(--content-scale));\\n  margin: 0;\\n  flex-shrink: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  padding: calc(var(--base-spacing) * 1) calc(var(--base-spacing) * 1.5);\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  border-radius: var(--base-border-radius);\\n  backdrop-filter: blur(10px);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover {\\n  background: rgba(0, 207, 255, 0.1);\\n  border-color: var(--hud-border-glow);\\n  transform: translateX(4px);\\n  box-shadow: 0 4px 20px rgba(0, 207, 255, 0.2);\\n}\\n\\n.subnav-hud-vertical .subnav-button.active {\\n  background: linear-gradient(135deg, rgba(0, 207, 255, 0.2) 0%, rgba(0, 255, 189, 0.1) 100%);\\n  border-color: var(--hud-neon-blue);\\n  box-shadow: 0 0 20px rgba(0, 207, 255, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n}\\n\\n/* Enhanced subnav icon */\\n.subnav-hud-vertical .subnav-icon {\\n  width: calc(18px * var(--content-scale));\\n  height: calc(18px * var(--content-scale));\\n  margin-right: calc(var(--base-spacing) * 1.5);\\n  color: var(--hud-silver);\\n  transition: all 0.3s ease;\\n  flex-shrink: 0;\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover .subnav-icon {\\n  color: var(--hud-neon-blue);\\n  filter: drop-shadow(0 0 8px rgba(0, 207, 255, 0.6));\\n}\\n\\n.subnav-hud-vertical .subnav-button.active .subnav-icon {\\n  color: var(--hud-bright-white);\\n  filter: drop-shadow(0 0 12px rgba(0, 207, 255, 0.8));\\n}\\n\\n/* Enhanced subnav label - full text display */\\n.subnav-hud-vertical .subnav-label {\\n  color: var(--hud-silver);\\n  font-size: calc(12px * var(--content-scale));\\n  font-weight: 500;\\n  line-height: 1.2;\\n  white-space: nowrap;\\n  transition: all 0.3s ease;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover .subnav-label {\\n  color: var(--hud-bright-white);\\n  text-shadow: 0 0 8px rgba(0, 207, 255, 0.6);\\n}\\n\\n.subnav-hud-vertical .subnav-button.active .subnav-label {\\n  color: var(--hud-bright-white);\\n  font-weight: 600;\\n  text-shadow: 0 0 12px rgba(0, 207, 255, 0.8);\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[1]!./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[2]!./styles/globals.css\n"));

/***/ })

});
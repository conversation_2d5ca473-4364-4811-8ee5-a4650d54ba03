"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/PerseusRenderer.tsx":
/*!********************************************!*\
  !*** ./src/components/PerseusRenderer.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PerseusRenderer: function() { return /* binding */ PerseusRenderer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst PerseusRenderer = (param)=>{\n    let { widgetType, widgetData } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!containerRef.current) return;\n        // Clear previous content\n        containerRef.current.innerHTML = \"\";\n        // Render based on widget type\n        switch(widgetType){\n            case \"numeric-input\":\n                renderNumericInput(containerRef.current);\n                break;\n            case \"categorizer\":\n                renderCategorizer(containerRef.current);\n                break;\n            case \"matcher\":\n                renderMatcher(containerRef.current);\n                break;\n            case \"orderer\":\n                renderOrderer(containerRef.current);\n                break;\n            case \"radio\":\n                renderRadio(containerRef.current);\n                break;\n            case \"expression\":\n                renderExpression(containerRef.current);\n                break;\n            default:\n                renderPlaceholder(containerRef.current, widgetType);\n        }\n    }, [\n        widgetType,\n        widgetData\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"perseus-widget bg-white rounded-lg p-6 min-h-[400px]\",\n        style: {\n            fontFamily: \"Arial, sans-serif\"\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\PerseusRenderer.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PerseusRenderer, \"8puyVO4ts1RhCfXUmci3vLI3Njw=\");\n_c = PerseusRenderer;\n// Numeric Input Widget\nfunction renderNumericInput(container) {\n    container.innerHTML = '\\n    <div class=\"numeric-input-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Math Problem</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">What is 15 + 27?</p>\\n      <input \\n        type=\"number\" \\n        placeholder=\"Enter your answer\"\\n        style=\"\\n          padding: 10px; \\n          border: 2px solid #ddd; \\n          border-radius: 5px; \\n          font-size: 16px;\\n          width: 200px;\\n          margin-right: 10px;\\n        \"\\n        id=\"numeric-answer\"\\n      />\\n      <button \\n        onclick=\"checkNumericAnswer()\"\\n        style=\"\\n          background: #1c4f82; \\n          color: white; \\n          padding: 10px 20px; \\n          border: none; \\n          border-radius: 5px;\\n          cursor: pointer;\\n          font-size: 16px;\\n        \"\\n      >Check Answer</button>\\n      <div id=\"numeric-feedback\" style=\"margin-top: 15px; font-weight: bold;\"></div>\\n    </div>\\n  ';\n    // Add event handler\n    window.checkNumericAnswer = ()=>{\n        const input = document.getElementById(\"numeric-answer\");\n        const feedback = document.getElementById(\"numeric-feedback\");\n        if (input && feedback) {\n            const answer = parseInt(input.value);\n            if (answer === 42) {\n                feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Correct! Great job!</span>';\n            } else {\n                feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Try again. Think about 15 + 27.</span>';\n            }\n        }\n    };\n}\n// Categorizer Widget\nfunction renderCategorizer(container) {\n    container.innerHTML = '\\n    <div class=\"categorizer-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Categorize Animals</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">Drag each animal to the correct category:</p>\\n      \\n      <div style=\"display: flex; gap: 20px; margin-bottom: 20px;\">\\n        <div class=\"category-box\" style=\"\\n          border: 2px dashed #1c4f82; \\n          padding: 15px; \\n          min-height: 100px; \\n          width: 150px;\\n          border-radius: 8px;\\n          background: #f8f9fa;\\n        \">\\n          <h4 style=\"margin: 0 0 10px 0; color: #1c4f82;\">Mammals</h4>\\n          <div id=\"mammals-drop\" class=\"drop-zone\"></div>\\n        </div>\\n        \\n        <div class=\"category-box\" style=\"\\n          border: 2px dashed #28a745; \\n          padding: 15px; \\n          min-height: 100px; \\n          width: 150px;\\n          border-radius: 8px;\\n          background: #f8f9fa;\\n        \">\\n          <h4 style=\"margin: 0 0 10px 0; color: #28a745;\">Birds</h4>\\n          <div id=\"birds-drop\" class=\"drop-zone\"></div>\\n        </div>\\n      </div>\\n      \\n      <div style=\"display: flex; gap: 10px; flex-wrap: wrap;\">\\n        <div class=\"draggable-item\" draggable=\"true\" data-category=\"mammals\" style=\"\\n          background: #e9ecef; \\n          padding: 8px 12px; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">\\uD83D\\uDC15 Dog</div>\\n        <div class=\"draggable-item\" draggable=\"true\" data-category=\"birds\" style=\"\\n          background: #e9ecef; \\n          padding: 8px 12px; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">\\uD83E\\uDD85 Eagle</div>\\n        <div class=\"draggable-item\" draggable=\"true\" data-category=\"mammals\" style=\"\\n          background: #e9ecef; \\n          padding: 8px 12px; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">\\uD83D\\uDC31 Cat</div>\\n        <div class=\"draggable-item\" draggable=\"true\" data-category=\"birds\" style=\"\\n          background: #e9ecef; \\n          padding: 8px 12px; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">\\uD83D\\uDC26 Sparrow</div>\\n      </div>\\n      \\n      <button \\n        onclick=\"checkCategorizer()\"\\n        style=\"\\n          background: #1c4f82; \\n          color: white; \\n          padding: 10px 20px; \\n          border: none; \\n          border-radius: 5px;\\n          cursor: pointer;\\n          font-size: 16px;\\n          margin-top: 20px;\\n        \"\\n      >Check Categories</button>\\n      <div id=\"categorizer-feedback\" style=\"margin-top: 15px; font-weight: bold;\"></div>\\n    </div>\\n  ';\n    // Add drag and drop functionality\n    setupDragAndDrop();\n}\nfunction setupDragAndDrop() {\n    const draggables = document.querySelectorAll(\".draggable-item\");\n    const dropZones = document.querySelectorAll(\".drop-zone\");\n    draggables.forEach((draggable)=>{\n        draggable.addEventListener(\"dragstart\", (e)=>{\n            var _e_dataTransfer, _e_dataTransfer1;\n            (_e_dataTransfer = e.dataTransfer) === null || _e_dataTransfer === void 0 ? void 0 : _e_dataTransfer.setData(\"text/plain\", draggable.outerHTML);\n            (_e_dataTransfer1 = e.dataTransfer) === null || _e_dataTransfer1 === void 0 ? void 0 : _e_dataTransfer1.setData(\"category\", draggable.dataset.category || \"\");\n        });\n    });\n    dropZones.forEach((zone)=>{\n        zone.addEventListener(\"dragover\", (e)=>{\n            e.preventDefault();\n        });\n        zone.addEventListener(\"drop\", (e)=>{\n            var _e_dataTransfer, _e_dataTransfer1;\n            e.preventDefault();\n            const html = (_e_dataTransfer = e.dataTransfer) === null || _e_dataTransfer === void 0 ? void 0 : _e_dataTransfer.getData(\"text/plain\");\n            const category = (_e_dataTransfer1 = e.dataTransfer) === null || _e_dataTransfer1 === void 0 ? void 0 : _e_dataTransfer1.getData(\"category\");\n            if (html && zone.id.includes(category || \"\")) {\n                zone.innerHTML += html;\n            }\n        });\n    });\n    window.checkCategorizer = ()=>{\n        const mammalsZone = document.getElementById(\"mammals-drop\");\n        const birdsZone = document.getElementById(\"birds-drop\");\n        const feedback = document.getElementById(\"categorizer-feedback\");\n        if (mammalsZone && birdsZone && feedback) {\n            const mammalsCount = mammalsZone.children.length;\n            const birdsCount = birdsZone.children.length;\n            if (mammalsCount === 2 && birdsCount === 2) {\n                feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Perfect categorization!</span>';\n            } else {\n                feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Some animals are in wrong categories. Try again!</span>';\n            }\n        }\n    };\n}\n// Matcher Widget\nfunction renderMatcher(container) {\n    container.innerHTML = '\\n    <div class=\"matcher-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Match Words with Definitions</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">Click on a word, then click on its matching definition:</p>\\n      \\n      <div style=\"display: flex; gap: 40px;\">\\n        <div>\\n          <h4 style=\"color: #1c4f82;\">Words</h4>\\n          <div class=\"match-item\" data-match=\"photosynthesis\" onclick=\"selectMatch(this)\" style=\"\\n            background: #e9ecef; \\n            padding: 10px; \\n            margin: 5px 0; \\n            border-radius: 5px; \\n            cursor: pointer;\\n            border: 2px solid transparent;\\n          \">Photosynthesis</div>\\n          <div class=\"match-item\" data-match=\"gravity\" onclick=\"selectMatch(this)\" style=\"\\n            background: #e9ecef; \\n            padding: 10px; \\n            margin: 5px 0; \\n            border-radius: 5px; \\n            cursor: pointer;\\n            border: 2px solid transparent;\\n          \">Gravity</div>\\n          <div class=\"match-item\" data-match=\"democracy\" onclick=\"selectMatch(this)\" style=\"\\n            background: #e9ecef; \\n            padding: 10px; \\n            margin: 5px 0; \\n            border-radius: 5px; \\n            cursor: pointer;\\n            border: 2px solid transparent;\\n          \">Democracy</div>\\n        </div>\\n        \\n        <div>\\n          <h4 style=\"color: #28a745;\">Definitions</h4>\\n          <div class=\"match-item\" data-match=\"gravity\" onclick=\"selectMatch(this)\" style=\"\\n            background: #e9ecef; \\n            padding: 10px; \\n            margin: 5px 0; \\n            border-radius: 5px; \\n            cursor: pointer;\\n            border: 2px solid transparent;\\n          \">Force that pulls objects toward Earth</div>\\n          <div class=\"match-item\" data-match=\"photosynthesis\" onclick=\"selectMatch(this)\" style=\"\\n            background: #e9ecef; \\n            padding: 10px; \\n            margin: 5px 0; \\n            border-radius: 5px; \\n            cursor: pointer;\\n            border: 2px solid transparent;\\n          \">Process plants use to make food from sunlight</div>\\n          <div class=\"match-item\" data-match=\"democracy\" onclick=\"selectMatch(this)\" style=\"\\n            background: #e9ecef; \\n            padding: 10px; \\n            margin: 5px 0; \\n            border-radius: 5px; \\n            cursor: pointer;\\n            border: 2px solid transparent;\\n          \">Government by the people</div>\\n        </div>\\n      </div>\\n      \\n      <div id=\"matcher-feedback\" style=\"margin-top: 20px; font-weight: bold;\"></div>\\n    </div>\\n  ';\n    let selectedItems = [];\n    window.selectMatch = (element)=>{\n        if (selectedItems.length === 0) {\n            element.style.border = \"2px solid #1c4f82\";\n            selectedItems.push(element);\n        } else if (selectedItems.length === 1) {\n            const first = selectedItems[0];\n            const feedback = document.getElementById(\"matcher-feedback\");\n            if (first.dataset.match === element.dataset.match) {\n                first.style.background = \"#d4edda\";\n                element.style.background = \"#d4edda\";\n                first.style.border = \"2px solid #28a745\";\n                element.style.border = \"2px solid #28a745\";\n                if (feedback) feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Correct match!</span>';\n            } else {\n                first.style.border = \"2px solid #dc3545\";\n                element.style.border = \"2px solid #dc3545\";\n                if (feedback) feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Try again!</span>';\n                setTimeout(()=>{\n                    first.style.border = \"2px solid transparent\";\n                    element.style.border = \"2px solid transparent\";\n                }, 1000);\n            }\n            selectedItems = [];\n        }\n    };\n}\n// Orderer Widget\nfunction renderOrderer(container) {\n    container.innerHTML = '\\n    <div class=\"orderer-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Put in Chronological Order</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">Drag to arrange these historical events in order:</p>\\n      \\n      <div id=\"sortable-list\" style=\"min-height: 200px;\">\\n        <div class=\"sortable-item\" draggable=\"true\" data-order=\"3\" style=\"\\n          background: #e9ecef; \\n          padding: 15px; \\n          margin: 10px 0; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">World War II ends (1945)</div>\\n        <div class=\"sortable-item\" draggable=\"true\" data-order=\"1\" style=\"\\n          background: #e9ecef; \\n          padding: 15px; \\n          margin: 10px 0; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">American Civil War begins (1861)</div>\\n        <div class=\"sortable-item\" draggable=\"true\" data-order=\"2\" style=\"\\n          background: #e9ecef; \\n          padding: 15px; \\n          margin: 10px 0; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">World War I begins (1914)</div>\\n        <div class=\"sortable-item\" draggable=\"true\" data-order=\"4\" style=\"\\n          background: #e9ecef; \\n          padding: 15px; \\n          margin: 10px 0; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">Moon landing (1969)</div>\\n      </div>\\n      \\n      <button \\n        onclick=\"checkOrder()\"\\n        style=\"\\n          background: #1c4f82; \\n          color: white; \\n          padding: 10px 20px; \\n          border: none; \\n          border-radius: 5px;\\n          cursor: pointer;\\n          font-size: 16px;\\n          margin-top: 20px;\\n        \"\\n      >Check Order</button>\\n      <div id=\"orderer-feedback\" style=\"margin-top: 15px; font-weight: bold;\"></div>\\n    </div>\\n  ';\n    setupSortable();\n}\nfunction setupSortable() {\n    const sortableList = document.getElementById(\"sortable-list\");\n    if (!sortableList) return;\n    let draggedElement = null;\n    sortableList.addEventListener(\"dragstart\", (e)=>{\n        draggedElement = e.target;\n        if (draggedElement) {\n            draggedElement.style.opacity = \"0.5\";\n        }\n    });\n    sortableList.addEventListener(\"dragend\", (e)=>{\n        if (draggedElement) {\n            draggedElement.style.opacity = \"1\";\n            draggedElement = null;\n        }\n    });\n    sortableList.addEventListener(\"dragover\", (e)=>{\n        e.preventDefault();\n    });\n    sortableList.addEventListener(\"drop\", (e)=>{\n        e.preventDefault();\n        const target = e.target;\n        if (target && target.classList.contains(\"sortable-item\") && draggedElement) {\n            const rect = target.getBoundingClientRect();\n            const midpoint = rect.top + rect.height / 2;\n            if (e.clientY < midpoint) {\n                sortableList.insertBefore(draggedElement, target);\n            } else {\n                sortableList.insertBefore(draggedElement, target.nextSibling);\n            }\n        }\n    });\n    window.checkOrder = ()=>{\n        const items = Array.from(document.querySelectorAll(\".sortable-item\"));\n        const feedback = document.getElementById(\"orderer-feedback\");\n        let isCorrect = true;\n        items.forEach((item, index)=>{\n            const expectedOrder = parseInt(item.dataset.order || \"0\");\n            if (expectedOrder !== index + 1) {\n                isCorrect = false;\n            }\n        });\n        if (feedback) {\n            if (isCorrect) {\n                feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Perfect chronological order!</span>';\n            } else {\n                feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Not quite right. Check the dates!</span>';\n            }\n        }\n    };\n}\n// Radio (Multiple Choice) Widget\nfunction renderRadio(container) {\n    container.innerHTML = '\\n    <div class=\"radio-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Multiple Choice Question</h3>\\n      <p style=\"color: #333; margin-bottom: 15px; font-size: 16px;\">What is the capital of France?</p>\\n      \\n      <div style=\"margin-bottom: 20px;\">\\n        <label style=\"display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 5px; background: #f8f9fa;\">\\n          <input type=\"radio\" name=\"capital\" value=\"london\" style=\"margin-right: 10px;\"> London\\n        </label>\\n        <label style=\"display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 5px; background: #f8f9fa;\">\\n          <input type=\"radio\" name=\"capital\" value=\"paris\" style=\"margin-right: 10px;\"> Paris\\n        </label>\\n        <label style=\"display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 5px; background: #f8f9fa;\">\\n          <input type=\"radio\" name=\"capital\" value=\"berlin\" style=\"margin-right: 10px;\"> Berlin\\n        </label>\\n        <label style=\"display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 5px; background: #f8f9fa;\">\\n          <input type=\"radio\" name=\"capital\" value=\"madrid\" style=\"margin-right: 10px;\"> Madrid\\n        </label>\\n      </div>\\n      \\n      <button \\n        onclick=\"checkRadio()\"\\n        style=\"\\n          background: #1c4f82; \\n          color: white; \\n          padding: 10px 20px; \\n          border: none; \\n          border-radius: 5px;\\n          cursor: pointer;\\n          font-size: 16px;\\n        \"\\n      >Submit Answer</button>\\n      <div id=\"radio-feedback\" style=\"margin-top: 15px; font-weight: bold;\"></div>\\n    </div>\\n  ';\n    window.checkRadio = ()=>{\n        const selected = document.querySelector('input[name=\"capital\"]:checked');\n        const feedback = document.getElementById(\"radio-feedback\");\n        if (!selected) {\n            if (feedback) feedback.innerHTML = '<span style=\"color: #ffc107;\">Please select an answer.</span>';\n            return;\n        }\n        if (feedback) {\n            if (selected.value === \"paris\") {\n                feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Correct! Paris is the capital of France.</span>';\n            } else {\n                feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Incorrect. The capital of France is Paris.</span>';\n            }\n        }\n    };\n}\n// Expression Widget\nfunction renderExpression(container) {\n    container.innerHTML = '\\n    <div class=\"expression-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Solve the Expression</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">Simplify: 2x + 3x - 5</p>\\n      \\n      <input \\n        type=\"text\" \\n        placeholder=\"Enter simplified expression\"\\n        style=\"\\n          padding: 10px; \\n          border: 2px solid #ddd; \\n          border-radius: 5px; \\n          font-size: 16px;\\n          width: 250px;\\n          margin-right: 10px;\\n        \"\\n        id=\"expression-answer\"\\n      />\\n      <button \\n        onclick=\"checkExpression()\"\\n        style=\"\\n          background: #1c4f82; \\n          color: white; \\n          padding: 10px 20px; \\n          border: none; \\n          border-radius: 5px;\\n          cursor: pointer;\\n          font-size: 16px;\\n        \"\\n      >Check Answer</button>\\n      \\n      <div style=\"margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;\">\\n        <p style=\"margin: 0; color: #666; font-size: 14px;\">\\n          <strong>Hint:</strong> Combine like terms (terms with the same variable)\\n        </p>\\n      </div>\\n      \\n      <div id=\"expression-feedback\" style=\"margin-top: 15px; font-weight: bold;\"></div>\\n    </div>\\n  ';\n    window.checkExpression = ()=>{\n        const input = document.getElementById(\"expression-answer\");\n        const feedback = document.getElementById(\"expression-feedback\");\n        if (input && feedback) {\n            const answer = input.value.toLowerCase().replace(/\\s/g, \"\");\n            const correctAnswers = [\n                \"5x-5\",\n                \"5x+-5\",\n                \"-5+5x\"\n            ];\n            if (correctAnswers.includes(answer)) {\n                feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Excellent! 2x + 3x - 5 = 5x - 5</span>';\n            } else {\n                feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Not quite. Remember to combine like terms: 2x + 3x = 5x</span>';\n            }\n        }\n    };\n}\n// Placeholder for unsupported widgets\nfunction renderPlaceholder(container, widgetType) {\n    container.innerHTML = '\\n    <div style=\"text-align: center; padding: 40px;\">\\n      <div style=\"font-size: 48px; margin-bottom: 20px;\">\\uD83C\\uDFAE</div>\\n      <h3 style=\"color: #1c4f82; margin-bottom: 15px;\">Perseus Widget: '.concat(widgetType, '</h3>\\n      <p style=\"color: #666; margin-bottom: 20px;\">This educational widget is being loaded...</p>\\n      <div style=\"background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #1c4f82;\">\\n        <p style=\"margin: 0; color: #333; font-size: 14px;\">\\n          Real Khan Academy Perseus widget integration in progress.\\n          This will render the actual interactive educational content.\\n        </p>\\n      </div>\\n    </div>\\n  ');\n}\nvar _c;\n$RefreshReg$(_c, \"PerseusRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/PerseusRenderer.tsx\n"));

/***/ }),

/***/ "./src/views/Gamification/GamificationView.tsx":
/*!*****************************************************!*\
  !*** ./src/views/Gamification/GamificationView.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GamificationView: function() { return /* binding */ GamificationView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_PerseusRenderer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/PerseusRenderer */ \"./src/components/PerseusRenderer.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n// Educational game categories with real Perseus widgets\nconst gameCategories = {\n    math: {\n        name: \"Mathematics\",\n        icon: \"\\uD83D\\uDD22\",\n        games: [\n            {\n                id: \"numeric-input\",\n                name: \"Number Practice\",\n                description: \"Practice number input and calculations\"\n            },\n            {\n                id: \"expression\",\n                name: \"Math Expressions\",\n                description: \"Work with mathematical expressions\"\n            },\n            {\n                id: \"grapher\",\n                name: \"Graph Explorer\",\n                description: \"Interactive graphing exercises\"\n            },\n            {\n                id: \"matrix\",\n                name: \"Matrix Operations\",\n                description: \"Learn matrix mathematics\"\n            }\n        ]\n    },\n    science: {\n        name: \"Science\",\n        icon: \"\\uD83D\\uDD2C\",\n        games: [\n            {\n                id: \"molecule\",\n                name: \"Molecule Builder\",\n                description: \"Build and explore molecular structures\"\n            },\n            {\n                id: \"phet-simulation\",\n                name: \"Physics Simulations\",\n                description: \"Interactive physics experiments\"\n            },\n            {\n                id: \"interactive-graph\",\n                name: \"Data Analysis\",\n                description: \"Analyze scientific data with graphs\"\n            }\n        ]\n    },\n    language: {\n        name: \"Language Arts\",\n        icon: \"\\uD83D\\uDCDA\",\n        games: [\n            {\n                id: \"passage\",\n                name: \"Reading Comprehension\",\n                description: \"Practice reading and understanding texts\"\n            },\n            {\n                id: \"categorizer\",\n                name: \"Word Categorizer\",\n                description: \"Categorize words and concepts\"\n            },\n            {\n                id: \"matcher\",\n                name: \"Word Matching\",\n                description: \"Match words with their meanings\"\n            }\n        ]\n    },\n    logic: {\n        name: \"Logic & Reasoning\",\n        icon: \"\\uD83E\\uDDE9\",\n        games: [\n            {\n                id: \"orderer\",\n                name: \"Sequence Ordering\",\n                description: \"Put items in logical order\"\n            },\n            {\n                id: \"sorter\",\n                name: \"Logic Sorter\",\n                description: \"Sort items by logical rules\"\n            },\n            {\n                id: \"radio\",\n                name: \"Multiple Choice Logic\",\n                description: \"Logical reasoning questions\"\n            }\n        ]\n    },\n    programming: {\n        name: \"Programming\",\n        icon: \"\\uD83D\\uDCBB\",\n        games: [\n            {\n                id: \"cs-program\",\n                name: \"Computer Science\",\n                description: \"Learn programming concepts\"\n            },\n            {\n                id: \"python-program\",\n                name: \"Python Programming\",\n                description: \"Interactive Python coding\"\n            }\n        ]\n    }\n};\nconst GamificationView = ()=>{\n    var _gameCategories_selectedCategory;\n    _s();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"math\");\n    const [activeGame, setActiveGame] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleGameClick = (gameId)=>{\n        setActiveGame(gameId);\n    };\n    if (activeGame) {\n        // Find the game details\n        let gameDetails = null;\n        let categoryName = \"\";\n        for (const [catKey, category] of Object.entries(gameCategories)){\n            const game = category.games.find((g)=>g.id === activeGame);\n            if (game) {\n                gameDetails = game;\n                categoryName = category.name;\n                break;\n            }\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full bg-panel-bg rounded-xl p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-white text-2xl font-bold\",\n                                    children: gameDetails === null || gameDetails === void 0 ? void 0 : gameDetails.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: categoryName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveGame(null),\n                            className: \"bg-gray-800/60 hover:bg-gray-700/60 text-white px-4 py-2 rounded-lg transition-colors duration-200\",\n                            children: \"← Back to Games\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full bg-gray-900/50 rounded-lg overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PerseusRenderer__WEBPACK_IMPORTED_MODULE_2__.PerseusRenderer, {\n                        widgetType: activeGame\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-panel-bg rounded-xl p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-white text-3xl font-bold mb-2\",\n                        children: \"\\uD83C\\uDFAE Educational Games\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Interactive learning games powered by Khan Academy Perseus\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2 mb-6 overflow-x-auto\",\n                children: Object.entries(gameCategories).map((param)=>{\n                    let [key, category] = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setSelectedCategory(key),\n                        className: \"flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 whitespace-nowrap \".concat(selectedCategory === key ? \"bg-blue-500/20 text-blue-400 border border-blue-400/30\" : \"bg-gray-800/40 text-gray-300 hover:bg-gray-700/60 hover:text-white\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: category.icon\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: category.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, key, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8\",\n                children: (_gameCategories_selectedCategory = gameCategories[selectedCategory]) === null || _gameCategories_selectedCategory === void 0 ? void 0 : _gameCategories_selectedCategory.games.map((game)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onClick: ()=>handleGameClick(game.id),\n                        className: \"bg-gray-800/40 border border-gray-700/50 rounded-xl p-4 hover:bg-gray-700/60 transition-all duration-300 cursor-pointer hover:scale-105 hover:border-blue-400/30\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl\",\n                                        children: \"\\uD83C\\uDFAF\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-white font-semibold\",\n                                                children: game.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: game.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-400 text-xs font-medium bg-blue-500/20 px-2 py-1 rounded\",\n                                        children: game.id\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-500 text-xs\",\n                                        children: \"Perseus Widget\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, game.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/40 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-green-400\",\n                                children: \"24\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"Games Available\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/40 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-blue-400\",\n                                children: \"5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"Categories\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/40 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-purple-400\",\n                                children: \"Perseus\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"Powered by\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/40 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-yellow-400\",\n                                children: \"Khan\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"Academy\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, undefined);\n};\n_s(GamificationView, \"yl+kFN6qiYBJhANaYdRXZ3u4o8o=\");\n_c = GamificationView;\nvar _c;\n$RefreshReg$(_c, \"GamificationView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/Gamification/GamificationView.tsx\n"));

/***/ })

});
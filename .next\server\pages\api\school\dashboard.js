"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/school/dashboard";
exports.ids = ["pages/api/school/dashboard"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool%2Fdashboard&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool%5Cdashboard.ts&middlewareConfigBase64=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool%2Fdashboard&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool%5Cdashboard.ts&middlewareConfigBase64=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_school_dashboard_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\school\\dashboard.ts */ \"(api)/./pages/api/school/dashboard.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_school_dashboard_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_school_dashboard_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/school/dashboard\",\n        pathname: \"/api/school/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_school_dashboard_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool%2Fdashboard&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool%5Cdashboard.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./pages/api/school/dashboard.ts":
/*!***************************************!*\
  !*** ./pages/api/school/dashboard.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n// In-memory data store for now (will be replaced with real database later)\nconst schoolData = {\n    overview: {\n        totalStudents: 2847,\n        totalTeachers: 156,\n        virtualRealityClasses: 23,\n        aiTutorSessions: 847\n    },\n    innovations: [\n        {\n            title: \"AI-Powered Personalized Learning\",\n            icon: \"\\uD83E\\uDD16\",\n            status: \"Active\",\n            impact: \"84% improvement in learning outcomes\"\n        },\n        {\n            title: \"Holographic Classrooms\",\n            icon: \"\\uD83C\\uDFAD\",\n            status: \"Beta\",\n            impact: \"87% engagement increase\"\n        },\n        {\n            title: \"Blockchain Credentials\",\n            icon: \"⛓️\",\n            status: \"Live\",\n            impact: \"100% fraud prevention\"\n        },\n        {\n            title: \"Emotion Recognition System\",\n            icon: \"\\uD83D\\uDE0A\",\n            status: \"Testing\",\n            impact: \"92% accuracy in mood detection\"\n        },\n        {\n            title: \"Quantum Computing Lab\",\n            icon: \"⚛️\",\n            status: \"Active\",\n            impact: \"340% faster problem solving\"\n        },\n        {\n            title: \"Neural Interface Learning\",\n            icon: \"\\uD83E\\uDDE0\",\n            status: \"Beta\",\n            impact: \"Direct knowledge transfer\"\n        }\n    ],\n    departments: [\n        {\n            name: \"AI & Robotics\",\n            color: \"bg-blue-500/20 text-blue-400\",\n            students: 412,\n            teachers: 18,\n            innovation: \"Neural Networks Lab\"\n        },\n        {\n            name: \"Quantum Computing\",\n            color: \"bg-green-500/20 text-green-400\",\n            students: 298,\n            teachers: 16,\n            innovation: \"Quantum Simulators\"\n        },\n        {\n            name: \"Bioengineering\",\n            color: \"bg-purple-500/20 text-purple-400\",\n            students: 267,\n            teachers: 12,\n            innovation: \"Gene Editing Lab\"\n        },\n        {\n            name: \"Space Sciences\",\n            color: \"bg-pink-500/20 text-pink-400\",\n            students: 189,\n            teachers: 11,\n            innovation: \"Mars Simulation\"\n        },\n        {\n            name: \"Metaverse Studies\",\n            color: \"bg-cyan-500/20 text-cyan-400\",\n            students: 345,\n            teachers: 14,\n            innovation: \"Virtual Reality Campus\"\n        },\n        {\n            name: \"Environmental Tech\",\n            color: \"bg-emerald-500/20 text-emerald-400\",\n            students: 223,\n            teachers: 9,\n            innovation: \"Carbon Neutral Systems\"\n        }\n    ],\n    recentEvents: [\n        {\n            title: \"Global VR Science Fair\",\n            date: \"March 25\",\n            type: \"Virtual Reality Meeting\",\n            participants: 50000\n        },\n        {\n            title: \"AI Ethics Symposium\",\n            date: \"March 30\",\n            type: \"Family Workshop\",\n            participants: 25000\n        },\n        {\n            title: \"Quantum Computing Workshop\",\n            date: \"April 5\",\n            type: \"Student Presentation\",\n            participants: 15000\n        },\n        {\n            title: \"Holographic Teacher Training\",\n            date: \"April 10\",\n            type: \"Professional Development\",\n            participants: 5000\n        },\n        {\n            title: \"Mars Colony Simulation\",\n            date: \"April 15\",\n            type: \"Immersive Experience\",\n            participants: 8000\n        }\n    ]\n};\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        // Simulate some processing time\n        await new Promise((resolve)=>setTimeout(resolve, 100));\n        res.status(200).json(schoolData);\n    } catch (error) {\n        console.error(\"API error:\", error);\n        res.status(500).json({\n            message: \"Internal server error\",\n            error:  true ? error.message : 0\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/school/dashboard.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0"], () => (__webpack_exec__("(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool%2Fdashboard&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool%5Cdashboard.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _src_components_Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../src/components/Sidebar */ \"./src/components/Sidebar.tsx\");\n/* harmony import */ var _src_components_Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../src/components/Header */ \"./src/components/Header.tsx\");\n/* harmony import */ var _src_components_Footer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../src/components/Footer */ \"./src/components/Footer.tsx\");\n/* harmony import */ var _src_components_RightSidebar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../src/components/RightSidebar */ \"./src/components/RightSidebar.tsx\");\n/* harmony import */ var _src_views_School_SchoolView__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../src/views/School/SchoolView */ \"./src/views/School/SchoolView.tsx\");\n/* harmony import */ var _src_components_BackButton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../src/components/BackButton */ \"./src/components/BackButton.tsx\");\n/* harmony import */ var _src_views_Gamification_GamificationView__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../src/views/Gamification/GamificationView */ \"./src/views/Gamification/GamificationView.tsx\");\n/* harmony import */ var _src_views_Coder_CoderView__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../src/views/Coder/CoderView */ \"./src/views/Coder/CoderView.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst LoadingComponent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-grow flex items-center justify-center text-cyan-400\",\n        children: \"Loading View...\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n        lineNumber: 14,\n        columnNumber: 32\n    }, undefined);\n_c = LoadingComponent;\n// Dynamically import views for code splitting and performance\nconst viewComponents = {\n    Dashboard: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Dashboard_DashboardView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Dashboard/DashboardView */ \"./src/views/Dashboard/DashboardView.tsx\")).then((mod)=>mod.DashboardView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Dashboard/DashboardView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    School: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/School/SchoolView */ \"./src/views/School/SchoolView.tsx\")).then((mod)=>mod.SchoolView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/School/SchoolView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Tool: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Tool_ToolView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Tool/ToolView */ \"./src/views/Tool/ToolView.tsx\")).then((mod)=>mod.ToolView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Tool/ToolView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Market: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Market_MarketView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Market/MarketView */ \"./src/views/Market/MarketView.tsx\")).then((mod)=>mod.MarketView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Market/MarketView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Bookstore: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Bookstore_BookstoreView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Bookstore/BookstoreView */ \"./src/views/Bookstore/BookstoreView.tsx\")).then((mod)=>mod.BookstoreView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Bookstore/BookstoreView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Concierge: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Concierge_ConciergeView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Concierge/ConciergeView */ \"./src/views/Concierge/ConciergeView.tsx\")).then((mod)=>mod.ConciergeView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Concierge/ConciergeView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Analytic: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Analytic_AnalyticView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Analytic/AnalyticView */ \"./src/views/Analytic/AnalyticView.tsx\")).then((mod)=>mod.AnalyticView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Analytic/AnalyticView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Setting: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Setting_SettingView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Setting/SettingView */ \"./src/views/Setting/SettingView.tsx\")).then((mod)=>mod.SettingView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Setting/SettingView\"\n            ]\n        },\n        loading: LoadingComponent\n    })\n};\nconst HomePage = ()=>{\n    _s();\n    const [activeView, setActiveView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Dashboard\");\n    const [activeDepartment, setActiveDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"school\");\n    const [activeSubSection, setActiveSubSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"dashboard\");\n    const [activeApp, setActiveApp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const ActiveComponent = viewComponents[activeView] || viewComponents.Dashboard;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Eyes Shield UI\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"A futuristic, high-tech dashboard UI with a glassmorphism theme, displaying various data visualizations and system statuses on a semi-transparent, glowing panel.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1.0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"main-background font-poppins text-[#E0E0E0] dashboard-auto-scale main-layout\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/60 backdrop-blur-xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 bg-container-bg content-area\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Sidebar__WEBPACK_IMPORTED_MODULE_4__.Sidebar, {\n                                activeView: activeView,\n                                setActiveView: (view)=>setActiveView(view),\n                                activeApp: activeApp,\n                                setActiveApp: setActiveApp\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"main-content\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"header-section\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Header__WEBPACK_IMPORTED_MODULE_5__.Header, {\n                                            showSchoolButtons: activeView === \"School\" && !activeApp,\n                                            activeDepartment: activeDepartment,\n                                            setActiveDepartment: setActiveDepartment,\n                                            activeSubSection: activeView === \"School\" ? activeSubSection : undefined\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"main-section\",\n                                        children: activeApp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-full relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_BackButton__WEBPACK_IMPORTED_MODULE_9__.BackButton, {\n                                                    onBack: ()=>setActiveApp(\"\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                activeApp === \"gamification\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_views_Gamification_GamificationView__WEBPACK_IMPORTED_MODULE_10__.GamificationView, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 52\n                                                }, undefined),\n                                                activeApp === \"coder\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_views_Coder_CoderView__WEBPACK_IMPORTED_MODULE_11__.CoderView, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                activeApp === \"media\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-full flex items-center justify-center text-blue-400 text-2xl\",\n                                                    children: \"\\uD83C\\uDFA5 Media Hub Coming Soon\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                activeApp === \"studio\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-full flex items-center justify-center text-blue-400 text-2xl\",\n                                                    children: \"\\uD83E\\uDDD1‍\\uD83C\\uDFA8 Studio Coming Soon\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 46\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 17\n                                        }, undefined) : activeView === \"School\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_views_School_SchoolView__WEBPACK_IMPORTED_MODULE_8__.SchoolView, {\n                                            activeDepartment: activeDepartment,\n                                            setActiveDepartment: setActiveDepartment,\n                                            activeSubSection: activeSubSection,\n                                            setActiveSubSection: setActiveSubSection\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveComponent, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"footer-section\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Footer__WEBPACK_IMPORTED_MODULE_6__.Footer, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_RightSidebar__WEBPACK_IMPORTED_MODULE_7__.RightSidebar, {\n                                activeView: activeView,\n                                setActiveView: (view)=>setActiveView(view)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(HomePage, \"vJKLUvkt3e+p8xjLwRpFw6LnEJM=\");\n_c1 = HomePage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HomePage);\nvar _c, _c1;\n$RefreshReg$(_c, \"LoadingComponent\");\n$RefreshReg$(_c1, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n"));

/***/ }),

/***/ "./src/views/Coder/CoderView.tsx":
/*!***************************************!*\
  !*** ./src/views/Coder/CoderView.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CoderView: function() { return /* binding */ CoderView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst FileTreeNode = (param)=>{\n    let { item, level, onFileClick } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(item.isOpen || false);\n    const handleClick = ()=>{\n        if (item.type === \"folder\") {\n            setIsOpen(!isOpen);\n        } else {\n            onFileClick(item.name);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2 px-2 py-1 hover:bg-gray-700/50 cursor-pointer text-sm\",\n                style: {\n                    paddingLeft: \"\".concat(level * 16 + 8, \"px\")\n                },\n                onClick: handleClick,\n                children: [\n                    item.type === \"folder\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-400\",\n                        children: isOpen ? \"\\uD83D\\uDCC2\" : \"\\uD83D\\uDCC1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, undefined),\n                    item.type === \"file\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-blue-400\",\n                        children: \"\\uD83D\\uDCC4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-300\",\n                        children: item.name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined),\n            item.type === \"folder\" && isOpen && item.children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: item.children.map((child, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileTreeNode, {\n                        item: child,\n                        level: level + 1,\n                        onFileClick: onFileClick\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FileTreeNode, \"1NLt9oXF2DSJYlKhLhMlvItPqek=\");\n_c = FileTreeNode;\nconst CoderView = ()=>{\n    _s1();\n    const [activeFile, setActiveFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"index.tsx\");\n    const [openFiles, setOpenFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"index.tsx\",\n        \"styles.css\"\n    ]);\n    const [showTerminal, setShowTerminal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sidebarTab, setSidebarTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"files\");\n    const fileTree = [\n        {\n            name: \"src\",\n            type: \"folder\",\n            isOpen: true,\n            children: [\n                {\n                    name: \"components\",\n                    type: \"folder\",\n                    children: [\n                        {\n                            name: \"Header.tsx\",\n                            type: \"file\"\n                        },\n                        {\n                            name: \"Sidebar.tsx\",\n                            type: \"file\"\n                        },\n                        {\n                            name: \"Footer.tsx\",\n                            type: \"file\"\n                        }\n                    ]\n                },\n                {\n                    name: \"views\",\n                    type: \"folder\",\n                    children: [\n                        {\n                            name: \"Dashboard.tsx\",\n                            type: \"file\"\n                        },\n                        {\n                            name: \"Settings.tsx\",\n                            type: \"file\"\n                        }\n                    ]\n                },\n                {\n                    name: \"index.tsx\",\n                    type: \"file\"\n                },\n                {\n                    name: \"App.tsx\",\n                    type: \"file\"\n                }\n            ]\n        },\n        {\n            name: \"public\",\n            type: \"folder\",\n            children: [\n                {\n                    name: \"favicon.ico\",\n                    type: \"file\"\n                },\n                {\n                    name: \"index.html\",\n                    type: \"file\"\n                }\n            ]\n        },\n        {\n            name: \"package.json\",\n            type: \"file\"\n        },\n        {\n            name: \"README.md\",\n            type: \"file\"\n        },\n        {\n            name: \"styles.css\",\n            type: \"file\"\n        }\n    ];\n    const sampleCode = {\n        \"index.tsx\": \"import React from 'react';\\nimport ReactDOM from 'react-dom/client';\\nimport App from './App';\\nimport './styles.css';\\n\\nconst root = ReactDOM.createRoot(\\n  document.getElementById('root') as HTMLElement\\n);\\n\\nroot.render(\\n  <React.StrictMode>\\n    <App />\\n  </React.StrictMode>\\n);\",\n        \"styles.css\": \"/* Global Styles */\\nbody {\\n  margin: 0;\\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto';\\n  background-color: #1a1a1a;\\n  color: #ffffff;\\n}\\n\\n.container {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 20px;\\n}\",\n        \"App.tsx\": \"import React from 'react';\\n\\nfunction App() {\\n  return (\\n    <div className=\\\"container\\\">\\n      <h1>Welcome to the IDE</h1>\\n      <p>Start coding your next project!</p>\\n    </div>\\n  );\\n}\\n\\nexport default App;\"\n    };\n    const handleFileClick = (fileName)=>{\n        setActiveFile(fileName);\n        if (!openFiles.includes(fileName)) {\n            setOpenFiles([\n                ...openFiles,\n                fileName\n            ]);\n        }\n    };\n    const closeFile = (fileName)=>{\n        const newOpenFiles = openFiles.filter((f)=>f !== fileName);\n        setOpenFiles(newOpenFiles);\n        if (activeFile === fileName && newOpenFiles.length > 0) {\n            setActiveFile(newOpenFiles[newOpenFiles.length - 1]);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-panel-bg rounded-xl overflow-hidden flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-64 bg-gray-900/50 border-r border-gray-700/50 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex border-b border-gray-700/50\",\n                        children: [\n                            {\n                                id: \"files\",\n                                icon: \"\\uD83D\\uDCC1\",\n                                label: \"Files\"\n                            },\n                            {\n                                id: \"git\",\n                                icon: \"\\uD83D\\uDD00\",\n                                label: \"Git\"\n                            },\n                            {\n                                id: \"extensions\",\n                                icon: \"\\uD83E\\uDDE9\",\n                                label: \"Extensions\"\n                            }\n                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSidebarTab(tab.id),\n                                className: \"flex-1 p-3 text-sm font-medium transition-colors duration-200 \".concat(sidebarTab === tab.id ? \"bg-blue-500/20 text-blue-400 border-b-2 border-blue-400\" : \"text-gray-400 hover:text-white hover:bg-gray-800/60\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-2\",\n                                        children: tab.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    tab.label\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: [\n                            sidebarTab === \"files\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-gray-400 text-xs uppercase font-semibold mb-2 px-2\",\n                                        children: \"Explorer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    fileTree.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileTreeNode, {\n                                            item: item,\n                                            level: 0,\n                                            onFileClick: handleFileClick\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, undefined),\n                            sidebarTab === \"git\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-white font-semibold mb-4\",\n                                        children: \"Source Control\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-green-400 text-sm\",\n                                                children: \"✓ 3 files staged\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-yellow-400 text-sm\",\n                                                children: \"⚠ 2 files modified\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-red-400 text-sm\",\n                                                children: \"✗ 1 file deleted\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, undefined),\n                            sidebarTab === \"extensions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-white font-semibold mb-4\",\n                                        children: \"Extensions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-blue-500 rounded flex items-center justify-center text-xs\",\n                                                        children: \"TS\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white text-sm\",\n                                                                children: \"TypeScript\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-400 text-xs\",\n                                                                children: \"Installed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                                lineNumber: 222,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-green-500 rounded flex items-center justify-center text-xs\",\n                                                        children: \"ES\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white text-sm\",\n                                                                children: \"ESLint\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-400 text-xs\",\n                                                                children: \"Installed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-800/40 border-b border-gray-700/50\",\n                        children: openFiles.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 px-4 py-2 border-r border-gray-700/50 cursor-pointer \".concat(activeFile === file ? \"bg-panel-bg text-white\" : \"text-gray-400 hover:text-white hover:bg-gray-800/60\"),\n                                onClick: ()=>setActiveFile(file),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: file\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            closeFile(file);\n                                        },\n                                        className: \"text-gray-500 hover:text-white\",\n                                        children: \"\\xd7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, file, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gray-900/30 p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"text-gray-300 text-sm font-mono leading-relaxed\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    children: sampleCode[activeFile] || \"// File content will be loaded here\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 9\n                    }, undefined),\n                    showTerminal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-48 bg-black/80 border-t border-gray-700/50 p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-semibold\",\n                                        children: \"Terminal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowTerminal(false),\n                                        className: \"text-gray-400 hover:text-white\",\n                                        children: \"\\xd7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-green-400 font-mono text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: \"$ npm start\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: \"Starting development server...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: \"Server running on http://localhost:3000\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"$ \"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-transparent border-none outline-none text-green-400 ml-1\",\n                                                children: \"|\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-80 bg-gray-900/50 border-l border-gray-700/50 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-white font-semibold mb-4\",\n                        children: \"\\uD83E\\uDD16 AI Assistant\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800/40 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-400 text-sm font-medium mb-1\",\n                                        children: \"Code Suggestion\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Consider adding error handling to your React component.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800/40 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-green-400 text-sm font-medium mb-1\",\n                                        children: \"Performance Tip\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Use React.memo() to optimize component re-renders.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowTerminal(!showTerminal),\n                            className: \"w-full bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 py-2 px-4 rounded-lg transition-colors duration-200\",\n                            children: showTerminal ? \"Hide Terminal\" : \"Show Terminal\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 301,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CoderView, \"1kPL0zpUX1PfGchY7IJ8nZYT3cM=\");\n_c1 = CoderView;\nvar _c, _c1;\n$RefreshReg$(_c, \"FileTreeNode\");\n$RefreshReg$(_c1, \"CoderView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/Coder/CoderView.tsx\n"));

/***/ }),

/***/ "./src/views/Gamification/GamificationView.tsx":
/*!*****************************************************!*\
  !*** ./src/views/Gamification/GamificationView.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GamificationView: function() { return /* binding */ GamificationView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst GameCard = (param)=>{\n    let { title, description, icon, difficulty, category, onClick } = param;\n    const difficultyColors = {\n        Easy: \"bg-green-500/20 text-green-400 border-green-400/30\",\n        Medium: \"bg-yellow-500/20 text-yellow-400 border-yellow-400/30\",\n        Hard: \"bg-red-500/20 text-red-400 border-red-400/30\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onClick: onClick,\n        className: \"bg-panel-bg border border-gray-700/50 rounded-xl p-6 hover:bg-gray-800/60 transition-all duration-300 cursor-pointer hover:scale-105 hover:border-blue-400/30\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-4 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-4xl\",\n                        children: icon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-white font-semibold text-lg\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-blue-400 text-sm font-medium\",\n                        children: category\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"px-3 py-1 rounded-full text-xs font-medium border \".concat(difficultyColors[difficulty]),\n                        children: difficulty\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n_c = GameCard;\nconst GamificationView = ()=>{\n    _s();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All\");\n    const [activeGame, setActiveGame] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const categories = [\n        \"All\",\n        \"Math\",\n        \"Language\",\n        \"Logic\",\n        \"Memory\",\n        \"Typing\"\n    ];\n    const games = [\n        {\n            id: \"sudoku\",\n            title: \"Sudoku Master\",\n            description: \"Classic number puzzle game with multiple difficulty levels\",\n            icon: \"\\uD83D\\uDD22\",\n            difficulty: \"Medium\",\n            category: \"Logic\"\n        },\n        {\n            id: \"memory-match\",\n            title: \"Memory Match\",\n            description: \"Flip cards and match pairs to improve memory skills\",\n            icon: \"\\uD83E\\uDDE0\",\n            difficulty: \"Easy\",\n            category: \"Memory\"\n        },\n        {\n            id: \"typing-speed\",\n            title: \"Typing Speed Test\",\n            description: \"Improve your typing speed and accuracy\",\n            icon: \"⌨️\",\n            difficulty: \"Easy\",\n            category: \"Typing\"\n        },\n        {\n            id: \"spelling-bee\",\n            title: \"Spelling Bee\",\n            description: \"Test and improve your spelling skills\",\n            icon: \"\\uD83D\\uDC1D\",\n            difficulty: \"Medium\",\n            category: \"Language\"\n        },\n        {\n            id: \"math-race\",\n            title: \"Math Race\",\n            description: \"Solve math problems as fast as you can\",\n            icon: \"\\uD83C\\uDFC3‍♂️\",\n            difficulty: \"Hard\",\n            category: \"Math\"\n        },\n        {\n            id: \"logic-puzzles\",\n            title: \"Logic Puzzles\",\n            description: \"Challenge your logical thinking with brain teasers\",\n            icon: \"\\uD83E\\uDDE9\",\n            difficulty: \"Hard\",\n            category: \"Logic\"\n        }\n    ];\n    const filteredGames = selectedCategory === \"All\" ? games : games.filter((game)=>game.category === selectedCategory);\n    const handleGameClick = (gameId)=>{\n        setActiveGame(gameId);\n        // Here we would load the actual game component\n        console.log(\"Loading game: \".concat(gameId));\n    };\n    if (activeGame) {\n        var _games_find, _games_find1;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full bg-panel-bg rounded-xl p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-white text-2xl font-bold\",\n                            children: (_games_find = games.find((g)=>g.id === activeGame)) === null || _games_find === void 0 ? void 0 : _games_find.title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveGame(null),\n                            className: \"bg-gray-800/60 hover:bg-gray-700/60 text-white px-4 py-2 rounded-lg transition-colors duration-200\",\n                            children: \"← Back to Games\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: (_games_find1 = games.find((g)=>g.id === activeGame)) === null || _games_find1 === void 0 ? void 0 : _games_find1.icon\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-white text-xl mb-2\",\n                                children: \"Game Loading...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: [\n                                    activeGame,\n                                    \" implementation will be added here using Phaser.js\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-panel-bg rounded-xl p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-white text-3xl font-bold mb-2\",\n                        children: \"\\uD83C\\uDFAE Learning Games\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Choose a game to start learning and having fun!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2 mb-6 overflow-x-auto\",\n                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setSelectedCategory(category),\n                        className: \"px-4 py-2 rounded-lg font-medium transition-all duration-200 whitespace-nowrap \".concat(selectedCategory === category ? \"bg-blue-500/20 text-blue-400 border border-blue-400/30\" : \"bg-gray-800/40 text-gray-300 hover:bg-gray-700/60 hover:text-white\"),\n                        children: category\n                    }, category, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: filteredGames.map((game)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                        title: game.title,\n                        description: game.description,\n                        icon: game.icon,\n                        difficulty: game.difficulty,\n                        category: game.category,\n                        onClick: ()=>handleGameClick(game.id)\n                    }, game.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/40 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-green-400\",\n                                children: \"12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"Games Completed\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/40 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-blue-400\",\n                                children: \"1,247\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"Total Score\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/40 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-purple-400\",\n                                children: \"Level 8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"Current Level\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n        lineNumber: 140,\n        columnNumber: 5\n    }, undefined);\n};\n_s(GamificationView, \"0kp5nHpSjeAGiBbrssEYdv71Uj8=\");\n_c1 = GamificationView;\nvar _c, _c1;\n$RefreshReg$(_c, \"GameCard\");\n$RefreshReg$(_c1, \"GamificationView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/Gamification/GamificationView.tsx\n"));

/***/ })

});
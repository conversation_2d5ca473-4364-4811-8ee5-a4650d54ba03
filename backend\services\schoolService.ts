
export type Course = {
  name: string;
  progress: number;
  color: 'cyan' | 'pink' | 'yellow';
};

export type Assignment = {
  name: string;
  due: string;
  course: string;
};

export type AcademicOverview = {
    gpa: string;
    credits: number;
    assignmentsDue: number;
}

export const getSchoolData = () => {
  const overview: AcademicOverview = {
      gpa: "3.85",
      credits: 12,
      assignmentsDue: 8
  };
  const courses: Course[] = [
    { name: 'Quantum Physics', progress: 75, color: 'cyan' },
    { name: 'Astrobiology', progress: 40, color: 'pink' },
    { name: 'Advanced AI', progress: 90, color: 'yellow' },
  ];
  const assignments: Assignment[] = [
    { name: 'Thesis on Dark Matter', due: '3 days', course: 'Quantum Physics' },
    { name: 'Lab Report: Martian Soil', due: '1 week', course: 'Astrobiology' },
    { name: 'Final Project: Neural Network', due: '2 weeks', course: 'Advanced AI' },
  ];
  return { overview, courses, assignments };
};

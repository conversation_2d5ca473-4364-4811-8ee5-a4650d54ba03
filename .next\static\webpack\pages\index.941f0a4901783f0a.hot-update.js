"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/views/School/departments/TeacherDashboard.tsx":
/*!***********************************************************!*\
  !*** ./src/views/School/departments/TeacherDashboard.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TeacherDashboard: function() { return /* binding */ TeacherDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../components/Card */ \"./src/components/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BookOpenIcon_ChartBarIcon_ClipboardDocumentListIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BookOpenIcon,ChartBarIcon,ClipboardDocumentListIcon,UserGroupIcon!=!@heroicons/react/24/solid */ \"__barrel_optimize__?names=AcademicCapIcon,BookOpenIcon,ChartBarIcon,ClipboardDocumentListIcon,UserGroupIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/solid/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst TeacherDashboard = (param)=>{\n    let { activeSubSection } = param;\n    _s();\n    const [teacherData, setTeacherData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchTeacherData = async ()=>{\n            try {\n                setLoading(true);\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                const mockData = {\n                    overview: {\n                        totalClasses: 6,\n                        totalStudents: 142,\n                        avgGrade: 87.5,\n                        pendingAssignments: 23,\n                        aiAssistantInteractions: 847,\n                        emotionalWellnessScore: 94,\n                        adaptiveLearningPaths: 156,\n                        hologramLessons: 12\n                    },\n                    classes: [\n                        {\n                            name: \"Quantum Physics VR\",\n                            students: 28,\n                            grade: \"Grade 11\",\n                            color: \"bg-blue-500/20 text-blue-400\",\n                            aiTutor: \"Einstein AI\",\n                            engagement: 96\n                        },\n                        {\n                            name: \"Neural Network Programming\",\n                            students: 24,\n                            grade: \"Grade 12\",\n                            color: \"bg-green-500/20 text-green-400\",\n                            aiTutor: \"Turing AI\",\n                            engagement: 94\n                        },\n                        {\n                            name: \"Bioengineering Lab\",\n                            students: 32,\n                            grade: \"Grade 10\",\n                            color: \"bg-purple-500/20 text-purple-400\",\n                            aiTutor: \"Darwin AI\",\n                            engagement: 92\n                        }\n                    ],\n                    aiInsights: [\n                        {\n                            student: \"Alex Chen\",\n                            insight: \"Shows exceptional pattern recognition in quantum mechanics\",\n                            confidence: 97,\n                            action: \"Advanced track recommended\"\n                        },\n                        {\n                            student: \"Maya Patel\",\n                            insight: \"Emotional stress detected during complex problems\",\n                            confidence: 89,\n                            action: \"Wellness check scheduled\"\n                        },\n                        {\n                            student: \"Jordan Smith\",\n                            insight: \"Learning style: Visual-Kinesthetic hybrid\",\n                            confidence: 94,\n                            action: \"VR modules assigned\"\n                        }\n                    ],\n                    recentAssignments: [\n                        {\n                            title: \"Quantum Entanglement Simulation\",\n                            class: \"Quantum Physics VR\",\n                            dueDate: \"March 18\",\n                            submitted: 24,\n                            total: 28,\n                            aiGraded: 18,\n                            avgTime: \"2.3h\"\n                        },\n                        {\n                            title: \"Neural Network Architecture\",\n                            class: \"Neural Network Programming\",\n                            dueDate: \"March 20\",\n                            submitted: 20,\n                            total: 24,\n                            aiGraded: 15,\n                            avgTime: \"3.1h\"\n                        },\n                        {\n                            title: \"Gene Editing Ethics Debate\",\n                            class: \"Bioengineering Lab\",\n                            dueDate: \"March 25\",\n                            submitted: 15,\n                            total: 32,\n                            aiGraded: 8,\n                            avgTime: \"1.8h\"\n                        }\n                    ]\n                };\n                setTeacherData(mockData);\n            } catch (error) {\n                console.error(\"Failed to fetch teacher data:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchTeacherData();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-cyan-400\",\n                children: \"Loading Teacher Dashboard...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, undefined);\n    }\n    const renderContent = ()=>{\n        switch(activeSubSection){\n            case \"dashboard\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-12 gap-3 h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                title: \"\\uD83E\\uDD16 AI-Enhanced Teaching Hub\",\n                                className: \"hud-card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-4 gap-3 p-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"metric-card hover-lift animate-fade-in-scale\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-gradient-to-br from-green-400/20 to-green-600/20 rounded-lg flex items-center justify-center mx-auto mb-2 border border-green-400/30 animate-pulse-glow\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BookOpenIcon_ChartBarIcon_ClipboardDocumentListIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.AcademicCapIcon, {\n                                                        className: \"w-5 h-5 text-green-400\",\n                                                        style: {\n                                                            filter: \"drop-shadow(0 0 6px rgba(34, 197, 94, 0.6))\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl font-bold text-white mb-1\",\n                                                    style: {\n                                                        textShadow: \"0 0 10px rgba(255, 255, 255, 0.5)\"\n                                                    },\n                                                    children: teacherData.overview.hologramLessons\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-300 font-medium\",\n                                                    children: \"Hologram Lessons\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"metric-card hover-lift animate-fade-in-scale\",\n                                            style: {\n                                                animationDelay: \"0.1s\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-gradient-to-br from-blue-400/20 to-blue-600/20 rounded-lg flex items-center justify-center mx-auto mb-2 border border-blue-400/30\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BookOpenIcon_ChartBarIcon_ClipboardDocumentListIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.UserGroupIcon, {\n                                                        className: \"w-5 h-5 text-blue-400\",\n                                                        style: {\n                                                            filter: \"drop-shadow(0 0 6px rgba(59, 130, 246, 0.6))\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl font-bold text-white mb-1\",\n                                                    style: {\n                                                        textShadow: \"0 0 10px rgba(255, 255, 255, 0.5)\"\n                                                    },\n                                                    children: teacherData.overview.aiAssistantInteractions\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-300 font-medium\",\n                                                    children: \"AI Interactions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"metric-card hover-lift animate-fade-in-scale\",\n                                            style: {\n                                                animationDelay: \"0.2s\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-gradient-to-br from-yellow-400/20 to-yellow-600/20 rounded-lg flex items-center justify-center mx-auto mb-2 border border-yellow-400/30\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BookOpenIcon_ChartBarIcon_ClipboardDocumentListIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.ChartBarIcon, {\n                                                        className: \"w-5 h-5 text-yellow-400\",\n                                                        style: {\n                                                            filter: \"drop-shadow(0 0 6px rgba(234, 179, 8, 0.6))\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl font-bold text-white mb-1\",\n                                                    style: {\n                                                        textShadow: \"0 0 10px rgba(255, 255, 255, 0.5)\"\n                                                    },\n                                                    children: [\n                                                        teacherData.overview.emotionalWellnessScore,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-300 font-medium\",\n                                                    children: \"Wellness Score\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"metric-card hover-lift animate-fade-in-scale\",\n                                            style: {\n                                                animationDelay: \"0.3s\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-gradient-to-br from-purple-400/20 to-purple-600/20 rounded-lg flex items-center justify-center mx-auto mb-2 border border-purple-400/30\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BookOpenIcon_ChartBarIcon_ClipboardDocumentListIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.ClipboardDocumentListIcon, {\n                                                        className: \"w-5 h-5 text-purple-400\",\n                                                        style: {\n                                                            filter: \"drop-shadow(0 0 6px rgba(147, 51, 234, 0.6))\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl font-bold text-white mb-1\",\n                                                    style: {\n                                                        textShadow: \"0 0 10px rgba(255, 255, 255, 0.5)\"\n                                                    },\n                                                    children: teacherData.overview.adaptiveLearningPaths\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-300 font-medium\",\n                                                    children: \"Learning Paths\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-7\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                title: \"\\uD83E\\uDDE0 AI Student Insights\",\n                                className: \"hud-card h-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 p-3 max-h-64 overflow-y-auto\",\n                                    children: teacherData.aiInsights.map((insight, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 rounded-lg bg-gradient-to-r from-purple-900/30 to-blue-900/30 border border-purple-400/40 backdrop-filter backdrop-blur-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-semibold text-white\",\n                                                            children: insight.student\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"status-badge active text-xs\",\n                                                            children: [\n                                                                insight.confidence,\n                                                                \"% confidence\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                            lineNumber: 122,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-300 mb-2 leading-relaxed\",\n                                                    children: insight.insight\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-cyan-400 font-medium\",\n                                                    children: [\n                                                        \"\\uD83D\\uDCA1 \",\n                                                        insight.action\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                title: \"\\uD83D\\uDE80 Next-Gen Classes\",\n                                className: \"hud-card h-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 p-3\",\n                                    children: teacherData.classes.map((cls, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 rounded-lg \".concat(cls.color.split(\" \")[0], \" border border-gray-500/40 backdrop-filter backdrop-blur-sm transition-all duration-300 hover:scale-102 hover:border-cyan-400/60\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold text-sm \".concat(cls.color.split(\" \").slice(1).join(\" \")),\n                                                                children: cls.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400 mt-1\",\n                                                                children: [\n                                                                    cls.grade,\n                                                                    \" • \",\n                                                                    cls.students,\n                                                                    \" students\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                                lineNumber: 143,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-cyan-400 font-medium\",\n                                                                        children: [\n                                                                            \"\\uD83E\\uDD16 \",\n                                                                            cls.aiTutor\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                                        lineNumber: 145,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-1 h-1 bg-gray-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                                        lineNumber: 146,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-green-400 font-medium\",\n                                                                        children: [\n                                                                            cls.engagement,\n                                                                            \"% engaged\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                                        lineNumber: 147,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                                lineNumber: 144,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 rounded-lg \".concat(cls.color.split(\" \")[0], \" flex items-center justify-center border border-gray-400/30\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BookOpenIcon_ChartBarIcon_ClipboardDocumentListIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.BookOpenIcon, {\n                                                            className: \"w-4 h-4 \".concat(cls.color.split(\" \").slice(1).join(\" \")),\n                                                            style: {\n                                                                filter: \"drop-shadow(0 0 4px rgba(59, 130, 246, 0.4))\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                title: \"\\uD83C\\uDFAF AI-Graded Assignments\",\n                                className: \"hud-card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 p-3\",\n                                    children: teacherData.recentAssignments.map((assignment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 rounded-lg bg-gradient-to-br from-gray-800/60 to-gray-700/60 border border-gray-500/40 backdrop-filter backdrop-blur-sm transition-all duration-300 hover:border-cyan-400/60 hover:scale-102\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-semibold text-white mb-2\",\n                                                    children: assignment.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-400 mb-3\",\n                                                    children: [\n                                                        assignment.class,\n                                                        \" • Due: \",\n                                                        assignment.dueDate\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 bg-gray-700/60 rounded-full h-2 border border-gray-600/30 progress-bar\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gradient-to-r from-cyan-400 to-blue-500 h-2 rounded-full transition-all duration-500 shadow-lg\",\n                                                                style: {\n                                                                    width: \"\".concat(assignment.submitted / assignment.total * 100, \"%\"),\n                                                                    boxShadow: \"0 0 8px rgba(0, 207, 255, 0.6)\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                                lineNumber: 172,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-300 font-medium\",\n                                                            children: [\n                                                                assignment.submitted,\n                                                                \"/\",\n                                                                assignment.total\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-purple-400 font-medium\",\n                                                            children: [\n                                                                \"\\uD83E\\uDD16 \",\n                                                                assignment.aiGraded,\n                                                                \" AI-graded\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-yellow-400 font-medium\",\n                                                            children: [\n                                                                \"⏱️ Avg: \",\n                                                                assignment.avgTime\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, undefined);\n            case \"classes\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"My Classes\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Class Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Manage your classes, view student rosters, and track attendance.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 11\n                }, undefined);\n            case \"students\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Student Management\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Student Overview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"View student profiles, track progress, and manage communications.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 11\n                }, undefined);\n            case \"curriculum\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Curriculum Planning\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Curriculum Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Plan lessons, manage curriculum standards, and track learning objectives.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 11\n                }, undefined);\n            case \"assignments\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Assignment Center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Assignment Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Create, distribute, and grade assignments across all your classes.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 11\n                }, undefined);\n            case \"grades\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Grade Book\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Grade Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Record grades, generate reports, and track student performance.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 11\n                }, undefined);\n            case \"resources\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Teaching Resources\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Resource Library\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Access teaching materials, lesson plans, and educational resources.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 11\n                }, undefined);\n            case \"settings\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Teacher Settings\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Personal Settings\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Configure your teaching preferences and account settings.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Teacher Dashboard\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Welcome to Teacher Portal\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Select a section from the navigation to get started.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"dashboard-grid dashboard-grid-teacher content-no-scroll\",\n        children: renderContent()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n        lineNumber: 279,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TeacherDashboard, \"i1gheQWXW7purOEwZqF0fYPpwyw=\");\n_c = TeacherDashboard;\nvar _c;\n$RefreshReg$(_c, \"TeacherDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/School/departments/TeacherDashboard.tsx\n"));

/***/ })

});
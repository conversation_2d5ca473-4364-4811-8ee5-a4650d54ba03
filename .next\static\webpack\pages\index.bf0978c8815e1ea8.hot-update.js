"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _icons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons */ \"./src/components/icons.tsx\");\n\nvar _s = $RefreshSig$();\n\n\nconst NavItem = (param)=>{\n    let { icon, label, active, onClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        \"aria-label\": label,\n        \"aria-pressed\": active,\n        className: \"w-full flex items-center justify-center responsive-border-radius transition-colors duration-200 relative \".concat(active ? \"bg-brand-cyan/20 text-white\" : \"text-gray-400 hover:bg-gray-700/50 hover:text-white\"),\n        style: {\n            padding: \"calc(var(--base-spacing) * 0.5)\",\n            height: \"var(--base-button-height)\"\n        },\n        children: [\n            active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-0 top-1/2 -translate-y-1/2 bg-brand-cyan rounded-r-full\",\n                style: {\n                    height: \"calc(var(--base-button-height) * 0.6)\",\n                    width: \"3px\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, undefined),\n            icon\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n_c = NavItem;\nconst Sidebar = (param)=>{\n    let { activeView, setActiveView, activeApp, setActiveApp } = param;\n    _s();\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isPinned, setIsPinned] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Main function buttons for the collapsible sidebar\n    const functionButtons = [\n        {\n            id: \"gamification\",\n            label: \"Gamification\",\n            icon: \"\\uD83C\\uDFAE\",\n            description: \"Learning Games\"\n        },\n        {\n            id: \"coder\",\n            label: \"Coder\",\n            icon: \"\\uD83D\\uDCBB\",\n            description: \"IDE View\"\n        },\n        {\n            id: \"media\",\n            label: \"Media\",\n            icon: \"\\uD83C\\uDFA5\",\n            description: \"Media Hub\"\n        },\n        {\n            id: \"studio\",\n            label: \"Studio\",\n            icon: \"\\uD83E\\uDDD1‍\\uD83C\\uDFA8\",\n            description: \"Creative Workspace\"\n        }\n    ];\n    // Original navigation items (when collapsed)\n    const navItems = [\n        {\n            id: \"Dashboard\",\n            label: \"Home\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.HomeIcon, {\n                className: \"responsive-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 99,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: \"Analytic\",\n            label: \"Analytics\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.ChartBarIcon, {\n                className: \"responsive-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 104,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: \"Tool\",\n            label: \"Modules\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.GridIcon, {\n                className: \"responsive-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 109,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: \"Setting\",\n            label: \"Profile\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.UserCircleIcon, {\n                className: \"responsive-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 114,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: \"Setting\",\n            label: \"Settings\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.SettingsIcon, {\n                className: \"responsive-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 119,\n                columnNumber: 13\n            }, undefined)\n        }\n    ];\n    const handleFunctionButtonClick = (appId)=>{\n        if (setActiveApp) {\n            setActiveApp(appId);\n        }\n        // Auto-collapse unless pinned\n        if (!isPinned) {\n            setIsCollapsed(true);\n        }\n    };\n    const toggleSidebar = ()=>{\n        setIsCollapsed(!isCollapsed);\n    };\n    const togglePin = ()=>{\n        setIsPinned(!isPinned);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: toggleSidebar,\n                className: \"fixed top-4 left-4 z-50 bg-panel-bg border border-gray-700/50 backdrop-blur-xl rounded-lg p-2 hover:bg-gray-800/60 transition-all duration-300 hover:scale-105\",\n                style: {\n                    boxShadow: \"var(--glass-shadow)\",\n                    width: \"calc(var(--base-button-height) * 1.2)\",\n                    height: \"calc(var(--base-button-height) * 1.2)\"\n                },\n                children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.Bars3Icon, {\n                    className: \"w-6 h-6 text-accent-blue\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.XMarkIcon, {\n                    className: \"w-6 h-6 text-accent-blue\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"fixed left-0 top-0 h-full bg-panel-bg border-r border-gray-700/50 backdrop-blur-xl z-40 transition-all duration-300 \".concat(isCollapsed ? \"-translate-x-full\" : \"translate-x-0\"),\n                style: {\n                    width: \"calc(280px * var(--content-scale))\",\n                    boxShadow: \"var(--glass-shadow)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center text-white w-10 h-10\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.ShieldCheckIcon, {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-white font-semibold text-lg\",\n                                                    children: \"Functions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Choose your workspace\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: togglePin,\n                                    className: \"p-2 rounded-lg transition-all duration-200 \".concat(isPinned ? \"bg-blue-500/20 text-blue-400\" : \"text-gray-400 hover:text-white hover:bg-gray-800/60\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.PinIcon, {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 mb-6\",\n                            children: functionButtons.map((button)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleFunctionButtonClick(button.id),\n                                    className: \"w-full flex items-center gap-4 p-4 rounded-xl transition-all duration-300 group \".concat(activeApp === button.id ? \"bg-blue-500/20 border border-blue-400/30 shadow-lg shadow-blue-500/10\" : \"bg-gray-800/40 border border-gray-700/50 hover:bg-blue-500/10 hover:border-blue-400/20\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl\",\n                                            children: button.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold \".concat(activeApp === button.id ? \"text-blue-400\" : \"text-white\"),\n                                                    children: button.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: button.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 rounded-full transition-all duration-300 \".concat(activeApp === button.id ? \"bg-blue-400\" : \"bg-transparent\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, button.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-glass-border mb-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-gray-400 text-sm font-medium mb-3\",\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, undefined),\n                                navItems.slice(0, 3).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setActiveView(item.id);\n                                            if (setActiveApp) setActiveApp(\"\");\n                                            if (!isPinned) setIsCollapsed(true);\n                                        },\n                                        className: \"w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200 \".concat(activeView === item.id && !activeApp ? \"bg-blue-500/20 text-blue-400\" : \"text-gray-300 hover:text-white hover:bg-gray-800/60\"),\n                                        children: [\n                                            item.icon,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: item.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, item.label, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, undefined))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-auto space-y-2\",\n                            children: navItems.slice(3).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setActiveView(item.id);\n                                        if (setActiveApp) setActiveApp(\"\");\n                                        if (!isPinned) setIsCollapsed(true);\n                                    },\n                                    className: \"w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200 \".concat(activeView === item.id && item.label === \"Settings\" && !activeApp ? \"bg-blue-500/20 text-blue-400\" : \"text-gray-300 hover:text-white hover:bg-gray-800/60\"),\n                                    children: [\n                                        item.icon,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, item.label, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, undefined),\n            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/20 backdrop-blur-sm z-30\",\n                onClick: ()=>setIsCollapsed(true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 274,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"bg-panel-bg border border-gray-700/50 responsive-border-radius sidebar-left flex flex-col items-center justify-between transition-all duration-300 \".concat(isCollapsed ? \"opacity-100\" : \"opacity-0 pointer-events-none\"),\n                style: {\n                    padding: \"calc(var(--base-spacing) * 0.75)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center w-full\",\n                        style: {\n                            gap: \"calc(var(--base-gap) * 0.75)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-cyan-400 to-fuchsia-500 rounded-full flex items-center justify-center text-white\",\n                                style: {\n                                    width: \"calc(var(--base-icon-size) * 1.25)\",\n                                    height: \"calc(var(--base-icon-size) * 1.25)\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.ShieldCheckIcon, {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full border-t border-gray-700\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, undefined),\n                            navItems.slice(0, 3).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavItem, {\n                                    icon: item.icon,\n                                    label: item.label,\n                                    active: activeView === item.id && !activeApp,\n                                    onClick: ()=>{\n                                        setActiveView(item.id);\n                                        if (setActiveApp) setActiveApp(\"\");\n                                    }\n                                }, item.label, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, undefined))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 w-full\",\n                        children: navItems.slice(3).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavItem, {\n                                icon: item.icon,\n                                label: item.label,\n                                active: activeView === item.id && item.label === \"Settings\" && !activeApp,\n                                onClick: ()=>{\n                                    setActiveView(item.id);\n                                    if (setActiveApp) setActiveApp(\"\");\n                                }\n                            }, item.label, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(Sidebar, \"04YrB/mgoahSJlsQvMs++y5tQMc=\");\n_c1 = Sidebar;\nvar _c, _c1;\n$RefreshReg$(_c, \"NavItem\");\n$RefreshReg$(_c1, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Sidebar.tsx\n"));

/***/ })

});
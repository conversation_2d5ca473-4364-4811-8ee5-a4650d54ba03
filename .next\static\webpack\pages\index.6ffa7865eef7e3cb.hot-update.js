"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "__barrel_optimize__?names=BoltIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/solid/esm/index.js":
/*!*********************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BoltIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/solid/esm/index.js ***!
  \*********************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BoltIcon: function() { return /* reexport safe */ _BoltIcon_js__WEBPACK_IMPORTED_MODULE_0__["default"]; }
/* harmony export */ });
/* harmony import */ var _BoltIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BoltIcon.js */ "./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/solid/esm/BoltIcon.js");



/***/ }),

/***/ "./src/views/School/departments/SettingDashboard.tsx":
/*!***********************************************************!*\
  !*** ./src/views/School/departments/SettingDashboard.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SettingDashboard: function() { return /* binding */ SettingDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../components/Card */ \"./src/components/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon!=!@heroicons/react/24/solid */ \"__barrel_optimize__?names=BoltIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/solid/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst SettingDashboard = (param)=>{\n    let { activeSubSection } = param;\n    _s();\n    const [settingsData, setSettingsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSettingsData = async ()=>{\n            try {\n                setLoading(true);\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                const mockData = {\n                    systemStatus: {\n                        quantumProcessors: \"Online\",\n                        neuralNetworks: \"Optimized\",\n                        hologramProjectors: \"Active\",\n                        blockchainNodes: \"Synchronized\",\n                        aiModels: \"Learning\",\n                        metaverseGateway: \"Connected\",\n                        carbonFootprint: \"Neutral\",\n                        securityLevel: \"Maximum\"\n                    },\n                    aiSettings: [\n                        {\n                            name: \"Learning Algorithm Optimization\",\n                            status: true,\n                            performance: 97.2,\n                            description: \"Adaptive learning path generation\"\n                        },\n                        {\n                            name: \"Emotional Intelligence Monitoring\",\n                            status: true,\n                            performance: 94.8,\n                            description: \"Student wellbeing analysis\"\n                        },\n                        {\n                            name: \"Quantum Computing Integration\",\n                            status: true,\n                            performance: 99.1,\n                            description: \"Advanced problem solving\"\n                        },\n                        {\n                            name: \"Holographic Teacher Deployment\",\n                            status: false,\n                            performance: 0,\n                            description: \"Virtual instructor system\"\n                        }\n                    ],\n                    securitySettings: [\n                        {\n                            name: \"Quantum Encryption\",\n                            level: \"Maximum\",\n                            status: \"Active\",\n                            strength: \"2048-qubit\"\n                        },\n                        {\n                            name: \"Biometric Authentication\",\n                            level: \"High\",\n                            status: \"Active\",\n                            strength: \"Neural pattern\"\n                        },\n                        {\n                            name: \"Blockchain Verification\",\n                            level: \"Maximum\",\n                            status: \"Active\",\n                            strength: \"Immutable ledger\"\n                        },\n                        {\n                            name: \"AI Threat Detection\",\n                            level: \"Adaptive\",\n                            status: \"Learning\",\n                            strength: \"Predictive analysis\"\n                        }\n                    ],\n                    performanceMetrics: [\n                        {\n                            metric: \"System Efficiency\",\n                            value: 97.8,\n                            target: 95,\n                            color: \"text-green-400\"\n                        },\n                        {\n                            metric: \"AI Response Time\",\n                            value: 0.003,\n                            target: 0.01,\n                            unit: \"seconds\",\n                            color: \"text-blue-400\"\n                        },\n                        {\n                            metric: \"Quantum Coherence\",\n                            value: 99.2,\n                            target: 98,\n                            color: \"text-purple-400\"\n                        },\n                        {\n                            metric: \"Neural Network Accuracy\",\n                            value: 96.7,\n                            target: 95,\n                            color: \"text-cyan-400\"\n                        }\n                    ],\n                    environmentalSettings: [\n                        {\n                            setting: \"Carbon Neutral Operations\",\n                            status: \"Active\",\n                            impact: \"100% renewable energy\"\n                        },\n                        {\n                            setting: \"Quantum Cooling System\",\n                            status: \"Optimized\",\n                            impact: \"60% energy reduction\"\n                        },\n                        {\n                            setting: \"AI Power Management\",\n                            status: \"Learning\",\n                            impact: \"Adaptive consumption\"\n                        },\n                        {\n                            setting: \"Hologram Efficiency Mode\",\n                            status: \"Active\",\n                            impact: \"40% power savings\"\n                        }\n                    ]\n                };\n                setSettingsData(mockData);\n            } catch (error) {\n                console.error(\"Failed to fetch settings data:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchSettingsData();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-cyan-400\",\n                children: \"Loading Quantum Settings...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, undefined);\n    }\n    const renderContent = ()=>{\n        switch(activeSubSection){\n            case \"dashboard\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"⚡ Quantum System Status\",\n                            className: \"lg:col-span-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-4 p-4\",\n                                children: Object.entries(settingsData.systemStatus).map((param, index)=>{\n                                    let [key, value] = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-2 \".concat(value === \"Online\" || value === \"Active\" || value === \"Connected\" || value === \"Optimized\" || value === \"Synchronized\" || value === \"Learning\" || value === \"Neutral\" || value === \"Maximum\" ? \"bg-gradient-to-br from-green-500 to-emerald-600\" : \"bg-gradient-to-br from-red-500 to-pink-600\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.BoltIcon, {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-bold \".concat(value === \"Online\" || value === \"Active\" || value === \"Connected\" || value === \"Optimized\" || value === \"Synchronized\" || value === \"Learning\" || value === \"Neutral\" || value === \"Maximum\" ? \"text-green-400\" : \"text-red-400\"),\n                                                children: value\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-400 capitalize\",\n                                                children: key.replace(/([A-Z])/g, \" $1\").trim()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"\\uD83E\\uDD16 AI System Controls\",\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 p-4\",\n                                children: settingsData.aiSettings.map((setting, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg bg-gradient-to-r from-gray-800/40 to-gray-700/40 border border-gray-600/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold text-white\",\n                                                                children: setting.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                                                lineNumber: 121,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: setting.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                                                lineNumber: 122,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-bold \".concat(setting.status ? \"text-green-400\" : \"text-gray-400\"),\n                                                                children: setting.performance > 0 ? \"\".concat(setting.performance, \"%\") : \"Offline\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-4 h-4 rounded-full \".concat(setting.status ? \"bg-green-400\" : \"bg-gray-600\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                                                lineNumber: 128,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            setting.status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-gray-700 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-cyan-400 to-blue-500 h-2 rounded-full\",\n                                                    style: {\n                                                        width: \"\".concat(setting.performance, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"\\uD83D\\uDEE1️ Quantum Security\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 p-4\",\n                                children: settingsData.securitySettings.map((security, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg bg-gradient-to-r from-purple-900/20 to-blue-900/20 border border-purple-500/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-white\",\n                                                        children: security.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs \".concat(security.status === \"Active\" ? \"bg-green-500/20 text-green-400\" : security.status === \"Learning\" ? \"bg-blue-500/20 text-blue-400\" : \"bg-yellow-500/20 text-yellow-400\"),\n                                                        children: security.status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center text-xs\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-400\",\n                                                        children: [\n                                                            security.level,\n                                                            \" Level\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-cyan-400\",\n                                                        children: security.strength\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 11\n                }, undefined);\n            case \"analytics\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"\\uD83D\\uDCCA Performance Analytics\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: settingsData.performanceMetrics.map((metric, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 rounded-lg bg-gradient-to-r from-gray-800/40 to-gray-700/40 border border-gray-600/30\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-white mb-2\",\n                                            children: metric.metric\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl font-bold \".concat(metric.color),\n                                                    children: [\n                                                        metric.value,\n                                                        metric.unit || \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: [\n                                                        \"Target: \",\n                                                        metric.target,\n                                                        metric.unit || \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-2 rounded-full \".concat(metric.color.replace(\"text-\", \"bg-\")),\n                                                style: {\n                                                    width: \"\".concat(Math.min(metric.value / metric.target * 100, 100), \"%\")\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 19\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 11\n                }, undefined);\n            case \"settings\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"\\uD83C\\uDF31 Environmental Controls\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: settingsData.environmentalSettings.map((env, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 rounded-lg bg-gradient-to-r from-green-900/20 to-emerald-900/20 border border-green-500/30\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-white\",\n                                                    children: env.setting\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-3 py-1 rounded-full text-sm \".concat(env.status === \"Active\" ? \"bg-green-500/20 text-green-400\" : env.status === \"Optimized\" ? \"bg-blue-500/20 text-blue-400\" : \"bg-yellow-500/20 text-yellow-400\"),\n                                                    children: env.status\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-cyan-400\",\n                                            children: [\n                                                \"\\uD83D\\uDCA1 \",\n                                                env.impact\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 19\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Quantum System Settings\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Future System Controls\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Quantum-powered settings with AI optimization and environmental consciousness.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 h-full\",\n        children: renderContent()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SettingDashboard.tsx\",\n        lineNumber: 237,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SettingDashboard, \"s2ZFvxpUuPfix9fgA2HwE9eVfB4=\");\n_c = SettingDashboard;\nvar _c;\n$RefreshReg$(_c, \"SettingDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/School/departments/SettingDashboard.tsx\n"));

/***/ })

});

import type { NextApiRequest, NextApiResponse } from 'next';
import { generateStructuredData, Type } from '../../backend/lib/gemini';

export type SystemDiagnosticsData = {
    cpuLoad: number;
    ramUsage: number;
};
export type NetworkStatusData = {
    status: string;
    latency: number;
    download: number;
    upload: number;
};
type ToolData = {
    diagnostics: SystemDiagnosticsData;
    network: NetworkStatusData;
};

const toolDataSchema = {
    type: Type.OBJECT,
    properties: {
        diagnostics: {
            type: Type.OBJECT,
            properties: {
                cpuLoad: { type: Type.INTEGER, description: "A plausible CPU load percentage (e.g., 30-90)." },
                ramUsage: { type: Type.INTEGER, description: "A plausible RAM usage percentage (e.g., 50-95)." }
            },
            required: ["cpuLoad", "ramUsage"]
        },
        network: {
            type: Type.OBJECT,
            properties: {
                status: { type: Type.STRING, description: "Network status, typically 'Connected'." },
                latency: { type: Type.INTEGER, description: "Network latency in ms (e.g., 8-50)." },
                download: { type: Type.NUMBER, description: "Download speed in Mbps (e.g., 100.0-900.0)." },
                upload: { type: Type.NUMBER, description: "Upload speed in Mbps (e.g., 10.0-50.0)." }
            },
            required: ["status", "latency", "download", "upload"]
        }
    },
    required: ["diagnostics", "network"]
};

const speedTestSchema = {
  type: Type.OBJECT,
  properties: {
    latency: { type: Type.INTEGER, description: "A new network latency in ms (e.g., 8-50)." },
    download: { type: Type.NUMBER, description: "A new download speed in Mbps (e.g., 100.0-900.0)." },
    upload: { type: Type.NUMBER, description: "A new upload speed in Mbps (e.g., 10.0-50.0)." }
  },
  required: ["latency", "download", "upload"]
};


export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method === 'GET') {
    try {
        const prompt = "Generate plausible data for a system tools dashboard, including diagnostics (CPU, RAM) and initial network status.";
        const data = await generateStructuredData<ToolData>(prompt, toolDataSchema);
        res.status(200).json(data);
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: 'Failed to fetch tool data from AI.' });
    }
  } else if (req.method === 'POST') {
    if(req.body.action === 'runSpeedTest') {
        try {
            await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate test duration
            const prompt = "Generate plausible results for a network speed test. Provide latency, download, and upload speeds.";
            const results = await generateStructuredData<Omit<NetworkStatusData, 'status'>>(prompt, speedTestSchema);
            res.status(200).json(results);
        } catch (error) {
            console.error(error);
            res.status(500).json({ error: 'Speed test failed.' });
        }
    } else {
        res.status(400).json({ error: 'Invalid action' });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}

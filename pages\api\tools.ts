
import type { NextApiRequest, NextApiResponse } from 'next';
import { executeQuerySingle, initializeDatabase, seedDatabase } from '../../backend/lib/database';

export type SystemDiagnosticsData = {
    cpuLoad: number;
    ramUsage: number;
};
export type NetworkStatusData = {
    status: string;
    latency: number;
    download: number;
    upload: number;
};
type ToolData = {
    diagnostics: SystemDiagnosticsData;
    network: NetworkStatusData;
};




export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method === 'GET') {
    try {
        // Initialize database if needed
        await initializeDatabase();
        await seedDatabase();

        // Get system diagnostics from database
        const diagnostics = await executeQuerySingle<{cpu_load: number, ram_usage: number}>(`
          SELECT cpu_load, ram_usage FROM system_diagnostics
          ORDER BY created_at DESC
          LIMIT 1
        `);

        const data: ToolData = {
          diagnostics: {
            cpuLoad: diagnostics?.cpu_load || 45.2,
            ramUsage: diagnostics?.ram_usage || 67.8
          },
          network: {
            status: 'Connected',
            latency: 23,
            download: 850.5,
            upload: 42.3
          }
        };

        res.status(200).json(data);
    } catch (error) {
        console.error('Tools API error:', error);

        // Fallback data if database fails
        const fallbackData: ToolData = {
          diagnostics: {
            cpuLoad: 45.2,
            ramUsage: 67.8
          },
          network: {
            status: 'Connected',
            latency: 23,
            download: 850.5,
            upload: 42.3
          }
        };
        res.status(200).json(fallbackData);
    }
  } else if (req.method === 'POST') {
    if(req.body.action === 'runSpeedTest') {
        try {
            await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate test duration

            // Generate realistic speed test results
            const results = {
              latency: Math.floor(Math.random() * 30) + 10, // 10-40ms
              download: Math.floor(Math.random() * 500) + 300, // 300-800 Mbps
              upload: Math.floor(Math.random() * 30) + 20 // 20-50 Mbps
            };

            res.status(200).json(results);
        } catch (error) {
            console.error(error);
            res.status(500).json({ error: 'Speed test failed.' });
        }
    } else {
        res.status(400).json({ error: 'Invalid action' });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}

/********************************************************************************
 * Copyright (C) 2017 TypeFox and others.
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License v. 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * This Source Code may also be made available under the following Secondary
 * Licenses when the conditions for such availability set forth in the Eclipse
 * Public License v. 2.0 are satisfied: GNU General Public License, version 2
 * with the GNU Classpath Exception which is available at
 * https://www.gnu.org/software/classpath/license.html.
 *
 * SPDX-License-Identifier: EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0
 ********************************************************************************/

.terminal-container {
  width: 100%;
  height: 100%;
  padding: var(--theia-code-padding);
  background: var(--theia-terminal-background);
}

.xterm .xterm-screen canvas {
  /* fix random 1px white border on terminal in Firefox. See https://github.com/eclipse-theia/theia/issues/4665 */
  border: 1px solid var(--theia-terminal-background);
}

.terminal-container .xterm .xterm-helper-textarea {
  /* fix secondary cursor-like issue. See https://github.com/eclipse-theia/theia/issues/8158 */
  opacity: 0 !important;
}

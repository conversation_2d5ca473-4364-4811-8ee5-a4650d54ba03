// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`rendering supports mobile rendering: mobile render 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        <PERSON><PERSON><PERSON> works in a hospital lab.
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="1"
    >
      <div
        class="paragraph"
      >
        To project blood quantities, he wants to know the probability that more than 
        <span
          style="white-space: nowrap;"
        >
          <span />
          <span
            class="mock-TeX"
          >
            1
          </span>
          <span />
        </span>
         of the next 
        <span
          style="white-space: nowrap;"
        >
          <span />
          <span
            class="mock-TeX"
          >
            7
          </span>
          <span />
        </span>
         donors will have type-A blood. From his previous work, <PERSON><PERSON> knows that 
        <span
          style="white-space: nowrap;"
        >
          <span />
          <span
            class="mock-TeX"
          >
            \\dfrac14
          </span>
          <span />
        </span>
         of donors have type-A blood.
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="2"
    >
      <div
        class="paragraph"
      >
        <PERSON><PERSON><PERSON> uses a computer to produce many samples that simulate the next 
        <span
          style="white-space: nowrap;"
        >
          <span />
          <span
            class="mock-TeX"
          >
            7
          </span>
          <span />
        </span>
         donors. The first 
        <span
          style="white-space: nowrap;"
        >
          <span />
          <span
            class="mock-TeX"
          >
            8
          </span>
          <span />
        </span>
         samples are shown in the table below where "
        <span
          style="white-space: nowrap;"
        >
          <span />
          <span
            class="mock-TeX"
          >
            \\text{\\red{A}}
          </span>
          <span />
        </span>
        " represents a donor 
        <em>
          with
        </em>
         type-A blood, and "
        <span
          style="white-space: nowrap;"
        >
          <span />
          <span
            class="mock-TeX"
          >
            \\text{\\blue{Z}}
          </span>
          <span />
        </span>
        " represents a donor 
        <em>
          without
        </em>
         type-A blood.
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="3"
    >
      <div
        class="paragraph"
      >
        <strong>
          Based on the samples below, estimate the probability that  more than 
          <span
            style="white-space: nowrap;"
          >
            <span />
            <span
              class="mock-TeX"
            >
              1
            </span>
            <span />
          </span>
           of the next 
          <span
            style="white-space: nowrap;"
          >
            <span />
            <span
              class="mock-TeX"
            >
              7
            </span>
            <span />
          </span>
           donors will have type-A blood.
        </strong>
         If necessary, round your answer to the nearest hundredth. 
        <div
          class="perseus-widget-container widget-nohighlight widget-inline-block"
        >
          <div
            aria-label="Math input box Tap with one or two fingers to open keyboard"
            class="initial_4qg14c-o_O-input_1n1c032"
            role="textbox"
          >
            <div
              class="keypad-input"
              tabindex="0"
            >
              <div
                class="mq-editable-field mq-math-mode"
                style="background-color: white; min-height: 44px; min-width: 64px; max-width: 128px; box-sizing: border-box; position: relative; border-style: solid; border-color: rgba(33,36,44,0.50); border-radius: 4px; color: rgb(33, 36, 44); border-width: 1px;"
              >
                <span
                  class="mq-textarea"
                >
                  <span
                    aria-label="Math Input:"
                  />
                </span>
                <span
                  aria-hidden="true"
                  class="mq-root-block mq-empty"
                  style="padding: 10px 13px 8px 13px; font-size: 18pt;"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="4"
    >
      <div
        class="paragraph"
      >
        <em>
          Note: This a small sample to practice with. A larger sample could give a much better estimate.
        </em>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="5"
    >
      <table>
        <thead>
          <tr>
            <th
              scope="col"
              style="text-align: center;"
            />
            <th
              scope="col"
              style="text-align: center;"
            >
              Sample
            </th>
            <th
              scope="col"
            />
          </tr>
        </thead>
        <tbody>
          <tr>
            <td
              style="text-align: center;"
            >
              <span
                style="white-space: nowrap;"
              >
                <span />
                <span
                  class="mock-TeX"
                >
                  1
                </span>
                <span />
              </span>
            </td>
            <td
              style="text-align: center;"
            >
              <span
                style="white-space: nowrap;"
              >
                <span />
                <span
                  class="mock-TeX"
                >
                  \\text{\\blue{Z}, \\blue{Z}, \\blue{Z}, \\blue{Z}, \\red{A}, \\blue{Z}, \\blue{Z}}
                </span>
                <span />
              </span>
            </td>
          </tr>
          <tr>
            <td
              style="text-align: center;"
            >
              <span
                style="white-space: nowrap;"
              >
                <span />
                <span
                  class="mock-TeX"
                >
                  2
                </span>
                <span />
              </span>
            </td>
            <td
              style="text-align: center;"
            >
              <span
                style="white-space: nowrap;"
              >
                <span />
                <span
                  class="mock-TeX"
                >
                  \\text{\\blue{Z}, \\blue{Z}, \\blue{Z}, \\blue{Z}, \\blue{Z}, \\blue{Z}, \\blue{Z}}
                </span>
                <span />
              </span>
            </td>
          </tr>
          <tr>
            <td
              style="text-align: center;"
            >
              <span
                style="white-space: nowrap;"
              >
                <span />
                <span
                  class="mock-TeX"
                >
                  3
                </span>
                <span />
              </span>
            </td>
            <td
              style="text-align: center;"
            >
              <span
                style="white-space: nowrap;"
              >
                <span />
                <span
                  class="mock-TeX"
                >
                  \\text{\\blue{Z}, \\blue{Z}, \\red{A}, \\blue{Z}, \\blue{Z}, \\blue{Z}, \\blue{Z}}
                </span>
                <span />
              </span>
            </td>
          </tr>
          <tr>
            <td
              style="text-align: center;"
            >
              <span
                style="white-space: nowrap;"
              >
                <span />
                <span
                  class="mock-TeX"
                >
                  4
                </span>
                <span />
              </span>
            </td>
            <td
              style="text-align: center;"
            >
              <span
                style="white-space: nowrap;"
              >
                <span />
                <span
                  class="mock-TeX"
                >
                  \\text{\\red{A}, \\red{A}, \\blue{Z}, \\blue{Z}, \\blue{Z}, \\blue{Z}, \\blue{Z}}
                </span>
                <span />
              </span>
            </td>
          </tr>
          <tr>
            <td
              style="text-align: center;"
            >
              <span
                style="white-space: nowrap;"
              >
                <span />
                <span
                  class="mock-TeX"
                >
                  5
                </span>
                <span />
              </span>
            </td>
            <td
              style="text-align: center;"
            >
              <span
                style="white-space: nowrap;"
              >
                <span />
                <span
                  class="mock-TeX"
                >
                  \\text{\\blue{Z}, \\blue{Z}, \\blue{Z}, \\blue{Z}, \\blue{Z}, \\red{A}, \\red{A}}
                </span>
                <span />
              </span>
            </td>
          </tr>
          <tr>
            <td
              style="text-align: center;"
            >
              <span
                style="white-space: nowrap;"
              >
                <span />
                <span
                  class="mock-TeX"
                >
                  6
                </span>
                <span />
              </span>
            </td>
            <td
              style="text-align: center;"
            >
              <span
                style="white-space: nowrap;"
              >
                <span />
                <span
                  class="mock-TeX"
                >
                  \\text{\\blue{Z}, \\red{A}, \\red{A}, \\blue{Z}, \\blue{Z}, \\blue{Z}, \\blue{Z}}
                </span>
                <span />
              </span>
            </td>
          </tr>
          <tr>
            <td
              style="text-align: center;"
            >
              <span
                style="white-space: nowrap;"
              >
                <span />
                <span
                  class="mock-TeX"
                >
                  7
                </span>
                <span />
              </span>
            </td>
            <td
              style="text-align: center;"
            >
              <span
                style="white-space: nowrap;"
              >
                <span />
                <span
                  class="mock-TeX"
                >
                  \\text{\\blue{Z}, \\red{A}, \\blue{Z}, \\blue{Z}, \\blue{Z}, \\red{A}, \\blue{Z}}
                </span>
                <span />
              </span>
            </td>
          </tr>
          <tr>
            <td
              style="text-align: center;"
            >
              <span
                style="white-space: nowrap;"
              >
                <span />
                <span
                  class="mock-TeX"
                >
                  8
                </span>
                <span />
              </span>
            </td>
            <td
              style="text-align: center;"
            >
              <span
                style="white-space: nowrap;"
              >
                <span />
                <span
                  class="mock-TeX"
                >
                  \\text{\\blue{Z}, \\blue{Z}, \\blue{Z}, \\blue{Z}, \\red{A}, \\blue{Z}, \\blue{Z}}
                </span>
                <span />
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
`;

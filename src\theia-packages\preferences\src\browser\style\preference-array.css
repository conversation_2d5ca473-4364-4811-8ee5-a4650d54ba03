/********************************************************************************
 * Copyright (C) 2020 <PERSON> and others.
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License v. 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * This Source Code may also be made available under the following Secondary
 * Licenses when the conditions for such availability set forth in the Eclipse
 * Public License v. 2.0 are satisfied: GNU General Public License, version 2
 * with the GNU Classpath Exception which is available at
 * https://www.gnu.org/software/classpath/license.html.
 *
 * SPDX-License-Identifier: EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0
 ********************************************************************************/

.theia-settings-container .preference-array {
  list-style: none;
  padding: 0;
}

.theia-settings-container .preference-array-element {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: calc(var(--theia-ui-padding) / 2) var(--theia-ui-padding);
  border-bottom: var(--theia-panel-border) 2px solid;
}

.theia-settings-container .pref-input li:nth-last-child(2) {
  border-bottom: none;
}

.theia-settings-container .pref-input li:last-child {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.theia-settings-container .preference-array-element:hover {
  background-color: rgba(50%, 50%, 50%, 0.1);
}

.theia-settings-container .preference-array-element-btn {
  width: 1.5em;
  height: 1.5em;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.theia-settings-container
  .preference-array-element
  .preference-array-element-btn {
  opacity: 0;
}

.theia-settings-container
  .preference-array-element:hover
  .preference-array-element-btn {
  opacity: 1;
}

.theia-settings-container .preference-array-element-btn:hover {
  background-color: rgba(50%, 50%, 50%, 0.1);
  cursor: pointer;
}

.theia-settings-container .preference-array .codicon.codicon-add {
  margin-left: calc((var(--theia-icon-size) + 4px) * -1);
  margin-right: 4px;
  width: var(--theia-icon-size);
  height: var(--theia-icon-size);
}

.theia-settings-container .preference-array-input {
  padding-right: calc(var(--theia-icon-size) + var(--theia-ui-padding));
  width: 100%;
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/views/Coder/CoderView.tsx":
/*!***************************************!*\
  !*** ./src/views/Coder/CoderView.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CoderView: function() { return /* binding */ CoderView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst FileTreeNode = (param)=>{\n    let { item, level, onFileClick } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(item.isOpen || false);\n    const handleClick = ()=>{\n        if (item.type === \"folder\") {\n            setIsOpen(!isOpen);\n        } else {\n            onFileClick(item.name);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2 px-2 py-1 hover:bg-gray-700/50 cursor-pointer text-sm\",\n                style: {\n                    paddingLeft: \"\".concat(level * 16 + 8, \"px\")\n                },\n                onClick: handleClick,\n                children: [\n                    item.type === \"folder\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-400\",\n                        children: isOpen ? \"\\uD83D\\uDCC2\" : \"\\uD83D\\uDCC1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, undefined),\n                    item.type === \"file\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-blue-400\",\n                        children: \"\\uD83D\\uDCC4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-300\",\n                        children: item.name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined),\n            item.type === \"folder\" && isOpen && item.children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: item.children.map((child, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileTreeNode, {\n                        item: child,\n                        level: level + 1,\n                        onFileClick: onFileClick\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 44,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FileTreeNode, \"1NLt9oXF2DSJYlKhLhMlvItPqek=\");\n_c = FileTreeNode;\nconst CoderView = ()=>{\n    _s1();\n    const [activeFile, setActiveFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"App.tsx\");\n    const [openFiles, setOpenFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"App.tsx\"\n    ]);\n    const [showTerminal, setShowTerminal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sidebarTab, setSidebarTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"files\");\n    const [files, setFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"App.tsx\": \"import React, { useState } from 'react';\\nimport './App.css';\\n\\nfunction App() {\\n  const [count, setCount] = useState(0);\\n\\n  return (\\n    <div className=\\\"App\\\">\\n      <header className=\\\"App-header\\\">\\n        <h1>Welcome to React IDE</h1>\\n        <p>Count: {count}</p>\\n        <button onClick={() => setCount(count + 1)}>\\n          Increment\\n        </button>\\n        <button onClick={() => setCount(count - 1)}>\\n          Decrement\\n        </button>\\n      </header>\\n    </div>\\n  );\\n}\\n\\nexport default App;\",\n        \"App.css\": \"body {\\n  margin: 0;\\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\\n    sans-serif;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n.App {\\n  text-align: center;\\n}\\n\\n.App-header {\\n  background-color: #282c34;\\n  padding: 20px;\\n  color: white;\\n  min-height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: calc(10px + 2vmin);\\n}\\n\\nbutton {\\n  background-color: #61dafb;\\n  border: none;\\n  padding: 10px 20px;\\n  margin: 10px;\\n  border-radius: 5px;\\n  cursor: pointer;\\n  font-size: 16px;\\n}\\n\\nbutton:hover {\\n  background-color: #21a1c4;\\n}\",\n        \"package.json\": '{\\n  \"name\": \"react-ide-project\",\\n  \"version\": \"1.0.0\",\\n  \"private\": true,\\n  \"dependencies\": {\\n    \"react\": \"^18.2.0\",\\n    \"react-dom\": \"^18.2.0\",\\n    \"typescript\": \"^4.9.5\"\\n  },\\n  \"scripts\": {\\n    \"start\": \"react-scripts start\",\\n    \"build\": \"react-scripts build\",\\n    \"test\": \"react-scripts test\",\\n    \"eject\": \"react-scripts eject\"\\n  },\\n  \"eslintConfig\": {\\n    \"extends\": [\\n      \"react-app\",\\n      \"react-app/jest\"\\n    ]\\n  },\\n  \"browserslist\": {\\n    \"production\": [\\n      \">0.2%\",\\n      \"not dead\",\\n      \"not op_mini all\"\\n    ],\\n    \"development\": [\\n      \"last 1 chrome version\",\\n      \"last 1 firefox version\",\\n      \"last 1 safari version\"\\n    ]\\n  }\\n}',\n        \"README.md\": \"# React IDE Project\\n\\nThis is a sample React project created in the IDE.\\n\\n## Available Scripts\\n\\n- `npm start` - Runs the app in development mode\\n- `npm build` - Builds the app for production\\n- `npm test` - Launches the test runner\\n\\n## Features\\n\\n- React with TypeScript\\n- Modern CSS styling\\n- Interactive components\\n- Hot reloading\\n\\n## Getting Started\\n\\n1. Install dependencies: `npm install`\\n2. Start the development server: `npm start`\\n3. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.\\n\"\n    });\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileTree = [\n        {\n            name: \"src\",\n            type: \"folder\",\n            isOpen: true,\n            children: [\n                {\n                    name: \"App.tsx\",\n                    type: \"file\"\n                },\n                {\n                    name: \"App.css\",\n                    type: \"file\"\n                },\n                {\n                    name: \"index.tsx\",\n                    type: \"file\"\n                }\n            ]\n        },\n        {\n            name: \"public\",\n            type: \"folder\",\n            children: [\n                {\n                    name: \"index.html\",\n                    type: \"file\"\n                },\n                {\n                    name: \"favicon.ico\",\n                    type: \"file\"\n                }\n            ]\n        },\n        {\n            name: \"package.json\",\n            type: \"file\"\n        },\n        {\n            name: \"README.md\",\n            type: \"file\"\n        }\n    ];\n    const handleEditorDidMount = (editor, monaco)=>{\n        editorRef.current = editor;\n        // Configure Monaco Editor\n        monaco.editor.defineTheme(\"vs-dark-custom\", {\n            base: \"vs-dark\",\n            inherit: true,\n            rules: [],\n            colors: {\n                \"editor.background\": \"#1e1e1e\",\n                \"editor.foreground\": \"#d4d4d4\",\n                \"editorLineNumber.foreground\": \"#858585\",\n                \"editor.selectionBackground\": \"#264f78\",\n                \"editor.inactiveSelectionBackground\": \"#3a3d41\"\n            }\n        });\n        monaco.editor.setTheme(\"vs-dark-custom\");\n    };\n    const handleEditorChange = (value)=>{\n        if (value !== undefined && activeFile) {\n            setFiles((prev)=>({\n                    ...prev,\n                    [activeFile]: value\n                }));\n        }\n    };\n    const handleFileClick = (fileName)=>{\n        // Only open files that exist in our files object\n        if (files[fileName]) {\n            setActiveFile(fileName);\n            if (!openFiles.includes(fileName)) {\n                setOpenFiles([\n                    ...openFiles,\n                    fileName\n                ]);\n            }\n        }\n    };\n    const closeFile = (fileName)=>{\n        const newOpenFiles = openFiles.filter((f)=>f !== fileName);\n        setOpenFiles(newOpenFiles);\n        if (activeFile === fileName && newOpenFiles.length > 0) {\n            setActiveFile(newOpenFiles[newOpenFiles.length - 1]);\n        } else if (newOpenFiles.length === 0) {\n            setActiveFile(\"\");\n        }\n    };\n    const getFileLanguage = (fileName)=>{\n        const ext = fileName.split(\".\").pop();\n        switch(ext){\n            case \"tsx\":\n            case \"ts\":\n                return \"typescript\";\n            case \"jsx\":\n            case \"js\":\n                return \"javascript\";\n            case \"css\":\n                return \"css\";\n            case \"json\":\n                return \"json\";\n            case \"md\":\n                return \"markdown\";\n            case \"html\":\n                return \"html\";\n            default:\n                return \"plaintext\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-panel-bg rounded-xl overflow-hidden flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-64 bg-gray-900/50 border-r border-gray-700/50 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex border-b border-gray-700/50\",\n                        children: [\n                            {\n                                id: \"files\",\n                                icon: \"\\uD83D\\uDCC1\",\n                                label: \"Files\"\n                            },\n                            {\n                                id: \"git\",\n                                icon: \"\\uD83D\\uDD00\",\n                                label: \"Git\"\n                            },\n                            {\n                                id: \"extensions\",\n                                icon: \"\\uD83E\\uDDE9\",\n                                label: \"Extensions\"\n                            }\n                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSidebarTab(tab.id),\n                                className: \"flex-1 p-3 text-sm font-medium transition-colors duration-200 \".concat(sidebarTab === tab.id ? \"bg-blue-500/20 text-blue-400 border-b-2 border-blue-400\" : \"text-gray-400 hover:text-white hover:bg-gray-800/60\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-2\",\n                                        children: tab.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    tab.label\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: [\n                            sidebarTab === \"files\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-gray-400 text-xs uppercase font-semibold mb-2 px-2\",\n                                        children: \"Explorer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    fileTree.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileTreeNode, {\n                                            item: item,\n                                            level: 0,\n                                            onFileClick: handleFileClick\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 17\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, undefined),\n                            sidebarTab === \"git\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-white font-semibold mb-4\",\n                                        children: \"Source Control\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-green-400 text-sm\",\n                                                children: \"✓ 3 files staged\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-yellow-400 text-sm\",\n                                                children: \"⚠ 2 files modified\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-red-400 text-sm\",\n                                                children: \"✗ 1 file deleted\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, undefined),\n                            sidebarTab === \"extensions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-white font-semibold mb-4\",\n                                        children: \"Extensions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-blue-500 rounded flex items-center justify-center text-xs\",\n                                                        children: \"TS\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white text-sm\",\n                                                                children: \"TypeScript\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-400 text-xs\",\n                                                                children: \"Installed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-green-500 rounded flex items-center justify-center text-xs\",\n                                                        children: \"ES\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white text-sm\",\n                                                                children: \"ESLint\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-400 text-xs\",\n                                                                children: \"Installed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-800/40 border-b border-gray-700/50\",\n                        children: openFiles.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 px-4 py-2 border-r border-gray-700/50 cursor-pointer \".concat(activeFile === file ? \"bg-panel-bg text-white\" : \"text-gray-400 hover:text-white hover:bg-gray-800/60\"),\n                                onClick: ()=>setActiveFile(file),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: file\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            closeFile(file);\n                                        },\n                                        className: \"text-gray-500 hover:text-white\",\n                                        children: \"\\xd7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, file, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gray-900/30 p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"text-gray-300 text-sm font-mono leading-relaxed\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    children: sampleCode[activeFile] || \"// File content will be loaded here\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                            lineNumber: 387,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 9\n                    }, undefined),\n                    showTerminal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-48 bg-black/80 border-t border-gray-700/50 p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-semibold\",\n                                        children: \"Terminal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowTerminal(false),\n                                        className: \"text-gray-400 hover:text-white\",\n                                        children: \"\\xd7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-green-400 font-mono text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: \"$ npm start\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: \"Starting development server...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: \"Server running on http://localhost:3000\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"$ \"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-transparent border-none outline-none text-green-400 ml-1\",\n                                                children: \"|\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 358,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-80 bg-gray-900/50 border-l border-gray-700/50 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-white font-semibold mb-4\",\n                        children: \"\\uD83E\\uDD16 AI Assistant\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800/40 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-400 text-sm font-medium mb-1\",\n                                        children: \"Code Suggestion\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Consider adding error handling to your React component.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800/40 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-green-400 text-sm font-medium mb-1\",\n                                        children: \"Performance Tip\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Use React.memo() to optimize component re-renders.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowTerminal(!showTerminal),\n                            className: \"w-full bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 py-2 px-4 rounded-lg transition-colors duration-200\",\n                            children: showTerminal ? \"Hide Terminal\" : \"Show Terminal\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                            lineNumber: 438,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 437,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 420,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n        lineNumber: 281,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CoderView, \"ulaYZ8G61Myo3h5ijmvuoCZ0cGQ=\");\n_c1 = CoderView;\nvar _c, _c1;\n$RefreshReg$(_c, \"FileTreeNode\");\n$RefreshReg$(_c1, \"CoderView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/Coder/CoderView.tsx\n"));

/***/ })

});
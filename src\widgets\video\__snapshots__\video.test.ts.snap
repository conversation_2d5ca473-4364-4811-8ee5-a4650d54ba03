// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`video widget should snapshot on mobile: first mobile render 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        Watch the Biogeography: Where Life Lives video to find the answer.
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="1"
    >
      <div
        class="paragraph"
      >
        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="default_xu2jcg"
          >
            <div
              class="fixed-to-responsive"
              style="max-width: 1280px; max-height: 720px;"
            >
              <div
                style="padding-bottom: 56.25%;"
              />
              <div
                class="default_xu2jcg-o_O-srOnly_19bpjuy"
              >
                Khan Academy video wrapper
              </div>
              <iframe
                allowfullscreen=""
                class="perseus-video-widget"
                height="720"
                sandbox="allow-same-origin allow-scripts"
                src="https://www.khanacademy.org/embed_video?slug=biogeography-where-life-lives&internal_video_only=1"
                width="1280"
              />
            </div>
            <div
              class="default_xu2jcg-o_O-transcriptLink_18xwqfz"
            >
              <span
                class="text_f1191h"
              />
              <div
                aria-hidden="true"
                class="default_xu2jcg-o_O-inlineStyles_sc01dy"
              />
              <a
                class="shared_17fj7pd-o_O-rest_1tt6owc visited-no-recolor"
                href="/transcript/videoNotFound"
                rel="noopener noreferrer"
                target="_blank"
              >
                See video transcript
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`video widget should snapshot: first render 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        Watch the Biogeography: Where Life Lives video to find the answer.
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="1"
    >
      <div
        class="paragraph"
      >
        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="default_xu2jcg"
          >
            <div
              class="fixed-to-responsive"
              style="max-width: 1280px; max-height: 720px;"
            >
              <div
                style="padding-bottom: 56.25%;"
              />
              <div
                class="default_xu2jcg-o_O-srOnly_19bpjuy"
              >
                Khan Academy video wrapper
              </div>
              <iframe
                allowfullscreen=""
                class="perseus-video-widget"
                height="720"
                sandbox="allow-same-origin allow-scripts"
                src="https://www.khanacademy.org/embed_video?slug=biogeography-where-life-lives&internal_video_only=1"
                width="1280"
              />
            </div>
            <div
              class="default_xu2jcg-o_O-transcriptLink_18xwqfz"
            >
              <span
                class="text_f1191h"
              />
              <div
                aria-hidden="true"
                class="default_xu2jcg-o_O-inlineStyles_sc01dy"
              />
              <a
                class="shared_17fj7pd-o_O-rest_1tt6owc visited-no-recolor"
                href="/transcript/videoNotFound"
                rel="noopener noreferrer"
                target="_blank"
              >
                See video transcript
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

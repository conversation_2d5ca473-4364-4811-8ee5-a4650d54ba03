// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SvgImage should load and render a localized graphie svg 1`] = `
<div>
  <div
    class="unresponsive-svg-image"
  >
    <img
      alt="svg image"
      role="button"
      src="mockStaticUrl(https://ka-perseus-graphie.s3.amazonaws.com/ccefe63aa1bd05f1d11123f72790a49378d2e42b.svg)"
      tabindex="0"
    />
  </div>
</div>
`;

exports[`SvgImage should load and render a normal graphie svg 1`] = `
<div>
  <div
    class="unresponsive-svg-image"
  >
    <img
      alt="svg image"
      role="button"
      src="mockStaticUrl(https://ka-perseus-graphie.s3.amazonaws.com/ccefe63aa1bd05f1d11123f72790a49378d2e42b.svg)"
      tabindex="0"
    />
  </div>
</div>
`;

exports[`SvgImage should load and render a png 1`] = `
<div>
  <img
    alt="png image"
    role="button"
    src="mockStaticUrl(http://localhost/sample.png)"
    tabindex="0"
  />
</div>
`;

exports[`SvgImage should render a spinner initially 1`] = `
<div>
  <span
    style="top: 0px; left: 0px; width: 100%; height: 100%; position: absolute; min-width: 20px; display: flex; justify-content: center; align-content: center;"
  >
    <div
      class="default_xu2jcg-o_O-spinnerContainer_agrn11"
    >
      <svg
        height="48"
        viewBox="0 0 48 48"
        width="48"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          class="loadingSpinner_1dvgeb3-o_O-inlineStyles_8njutv"
          d="M44.19 23.455a1.91 1.91 0 1 1 3.801 0h.003c.004.18.006.363.006.545 0 13.255-10.745 24-24 24S0 37.255 0 24 10.745 0 24 0c.182 0 .364.002.545.006V.01a1.91 1.91 0 1 1 0 3.801v.015A20.564 20.564 0 0 0 24 3.818C12.854 3.818 3.818 12.854 3.818 24c0 11.146 9.036 20.182 20.182 20.182 11.146 0 20.182-9.036 20.182-20.182 0-.182-.003-.364-.007-.545h.015z"
          fill-rule="nonzero"
        />
      </svg>
    </div>
  </span>
</div>
`;

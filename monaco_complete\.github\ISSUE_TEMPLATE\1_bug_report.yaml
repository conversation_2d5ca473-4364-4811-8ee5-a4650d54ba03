name: Bug Report
description: File a bug report
title: '[Bug] '
body:
  - type: markdown
    attributes:
      value: |
        To help us processing your bug report, please fill out this form.
  - type: checkboxes
    id: reproducible-in-vscode
    attributes:
      label: Reproducible in vscode.dev or in VS Code Desktop?
      description: Can you reproduce the bug in [vscode.dev](https://vscode.dev) or in VS Code Desktop? If so, please create [an issue in the VS Code repository](https://github.com/microsoft/vscode/issues). **VS Code issues are usually looked at within a couple of days.**
      options:
        - label: Not reproducible in [vscode.dev](https://vscode.dev) or VS Code Desktop
          required: true
  - type: checkboxes
    id: reproducible-in-monaco-playground
    attributes:
      label: Reproducible in the monaco editor playground?
      description: Can you reproduce the bug in [the monaco editor playground](https://microsoft.github.io/monaco-editor/playground.html)? A minimal reproducible example will make it significantly easier for us to get this bug fixed.
      options:
        - label: Not reproducible in [the monaco editor playground](https://microsoft.github.io/monaco-editor/playground.html)
  - type: textarea
    id: playgroundLink
    attributes:
      label: Monaco Editor Playground Link
      description: Please share the link to the [monaco editor playground](https://microsoft.github.io/monaco-editor/playground.html) after you entered your example. In case of regressions, please also provide the first broken version.

  - type: textarea
    id: playgroundSourceCode
    attributes:
      label: Monaco Editor Playground Code
      description: Please provide the code from the monaco editor playground example.
      render: typescript
  - type: textarea
    id: steps
    attributes:
      label: Reproduction Steps
      description: Please describe the steps (in the playground) that lead to the problematic behavior
  - type: textarea
    id: actual-behavior
    attributes:
      label: Actual (Problematic) Behavior
      description: Please describe the actual (problematic) behavior, as observed in the playground.

  - type: textarea
    id: expected-behavior
    attributes:
      label: Expected Behavior
      description: Please describe the expected behavior.

  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Please provide additional context.

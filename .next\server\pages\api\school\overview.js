"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/school/overview";
exports.ids = ["pages/api/school/overview"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool%2Foverview&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool%5Coverview.ts&middlewareConfigBase64=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool%2Foverview&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool%5Coverview.ts&middlewareConfigBase64=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_school_overview_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\school\\overview.ts */ \"(api)/./pages/api/school/overview.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_school_overview_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_school_overview_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/school/overview\",\n        pathname: \"/api/school/overview\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_school_overview_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool%2Foverview&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool%5Coverview.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./pages/api/school/overview.ts":
/*!**************************************!*\
  !*** ./pages/api/school/overview.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n// In-memory data store for School Overview\nconst overviewData = {\n    schoolInfo: {\n        name: \"Future Academy of Quantum Learning\",\n        established: \"2024\",\n        motto: \"Shaping Tomorrow's Minds Today\",\n        accreditation: \"Global AI Education Council\",\n        ranking: \"#1 Futuristic School Worldwide\",\n        campusType: \"Hybrid Physical-Metaverse\",\n        timeZone: \"Global (24/7 Virtual Access)\"\n    },\n    statistics: {\n        totalStudents: 2847,\n        totalTeachers: 156,\n        totalStaff: 89,\n        departments: 12,\n        virtualClassrooms: 45,\n        aiTutors: 23,\n        holoLabs: 8,\n        quantumComputers: 4,\n        globalPartners: 67,\n        graduationRate: 98.7,\n        employmentRate: 99.2,\n        averageStartingSalary: 125000\n    },\n    achievements: [\n        {\n            title: \"First School with Quantum Computing Lab\",\n            year: \"2024\",\n            description: \"Pioneered quantum education for high school students\",\n            icon: \"⚛️\",\n            category: \"Innovation\"\n        },\n        {\n            title: \"AI Ethics Education Leader\",\n            year: \"2024\",\n            description: \"Developed comprehensive AI ethics curriculum\",\n            icon: \"\\uD83E\\uDD16\",\n            category: \"Curriculum\"\n        },\n        {\n            title: \"Carbon Neutral Campus\",\n            year: \"2024\",\n            description: \"Achieved 100% renewable energy and carbon neutrality\",\n            icon: \"\\uD83C\\uDF31\",\n            category: \"Environment\"\n        },\n        {\n            title: \"Global Metaverse Education Pioneer\",\n            year: \"2024\",\n            description: \"First school to offer full metaverse learning experience\",\n            icon: \"\\uD83C\\uDF10\",\n            category: \"Technology\"\n        },\n        {\n            title: \"Neural Interface Learning Beta\",\n            year: \"2024\",\n            description: \"Testing direct brain-computer learning interfaces\",\n            icon: \"\\uD83E\\uDDE0\",\n            category: \"Research\"\n        },\n        {\n            title: \"Holographic Teacher Program\",\n            year: \"2024\",\n            description: \"Deployed AI holographic teachers across all subjects\",\n            icon: \"\\uD83D\\uDC68‍\\uD83C\\uDFEB\",\n            category: \"Innovation\"\n        }\n    ],\n    facilities: [\n        {\n            name: \"Quantum Computing Center\",\n            description: \"4 quantum computers for advanced physics and mathematics\",\n            capacity: \"32 students\",\n            features: [\n                \"2048-qubit processors\",\n                \"Quantum simulators\",\n                \"Entanglement labs\"\n            ],\n            status: \"Active\",\n            utilization: 87\n        },\n        {\n            name: \"Holographic Learning Pods\",\n            description: \"Immersive 3D learning environments with AI tutors\",\n            capacity: \"200 students\",\n            features: [\n                \"360\\xb0 holographic displays\",\n                \"Haptic feedback\",\n                \"Neural interfaces\"\n            ],\n            status: \"Active\",\n            utilization: 94\n        },\n        {\n            name: \"Bioengineering Laboratory\",\n            description: \"Gene editing and synthetic biology research facility\",\n            capacity: \"48 students\",\n            features: [\n                \"CRISPR stations\",\n                \"Bioreactors\",\n                \"DNA sequencers\"\n            ],\n            status: \"Active\",\n            utilization: 76\n        },\n        {\n            name: \"Space Simulation Chamber\",\n            description: \"Mars colony simulation and space technology testing\",\n            capacity: \"24 students\",\n            features: [\n                \"Zero-gravity simulation\",\n                \"Mars atmosphere\",\n                \"Space suits\"\n            ],\n            status: \"Active\",\n            utilization: 82\n        },\n        {\n            name: \"Metaverse Campus\",\n            description: \"Virtual reality campus accessible globally\",\n            capacity: \"Unlimited\",\n            features: [\n                \"VR classrooms\",\n                \"Virtual labs\",\n                \"Global connectivity\"\n            ],\n            status: \"Active\",\n            utilization: 91\n        },\n        {\n            name: \"AI Ethics Center\",\n            description: \"Research and education on artificial intelligence ethics\",\n            capacity: \"60 students\",\n            features: [\n                \"AI simulation rooms\",\n                \"Ethics debate halls\",\n                \"Research labs\"\n            ],\n            status: \"Active\",\n            utilization: 68\n        }\n    ],\n    partnerships: [\n        {\n            organization: \"Quantum Research Institute\",\n            type: \"Research Partnership\",\n            description: \"Collaborative quantum computing research and education\",\n            established: \"2024\",\n            benefits: [\n                \"Access to quantum computers\",\n                \"Research opportunities\",\n                \"Internships\"\n            ]\n        },\n        {\n            organization: \"Global AI Consortium\",\n            type: \"Educational Alliance\",\n            description: \"AI ethics and safety education collaboration\",\n            established: \"2024\",\n            benefits: [\n                \"Curriculum development\",\n                \"Expert lectures\",\n                \"Certification programs\"\n            ]\n        },\n        {\n            organization: \"Mars Colony Foundation\",\n            type: \"Space Education\",\n            description: \"Space colonization and technology education\",\n            established: \"2024\",\n            benefits: [\n                \"Simulation access\",\n                \"Astronaut training\",\n                \"Space internships\"\n            ]\n        },\n        {\n            organization: \"Metaverse Education Network\",\n            type: \"Technology Partnership\",\n            description: \"Virtual reality and metaverse learning platforms\",\n            established: \"2024\",\n            benefits: [\n                \"VR technology\",\n                \"Global classrooms\",\n                \"Virtual field trips\"\n            ]\n        }\n    ],\n    upcomingProjects: [\n        {\n            name: \"Neural Interface Learning System\",\n            description: \"Direct brain-computer learning interface for accelerated education\",\n            timeline: \"Q2 2024\",\n            budget: \"$2.5M\",\n            status: \"In Development\",\n            expectedImpact: \"300% faster learning\"\n        },\n        {\n            name: \"Quantum Internet Campus\",\n            description: \"Ultra-secure quantum communication network\",\n            timeline: \"Q3 2024\",\n            budget: \"$1.8M\",\n            status: \"Planning\",\n            expectedImpact: \"Unhackable communications\"\n        },\n        {\n            name: \"Time Dilation Learning Chambers\",\n            description: \"Relativistic time manipulation for extended learning sessions\",\n            timeline: \"Q4 2024\",\n            budget: \"$5.2M\",\n            status: \"Research Phase\",\n            expectedImpact: \"Subjective time extension\"\n        }\n    ]\n};\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        // Simulate some processing time\n        await new Promise((resolve)=>setTimeout(resolve, 150));\n        res.status(200).json(overviewData);\n    } catch (error) {\n        console.error(\"API error:\", error);\n        res.status(500).json({\n            message: \"Internal server error\",\n            error:  true ? error.message : 0\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/school/overview.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0"], () => (__webpack_exec__("(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool%2Foverview&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool%5Coverview.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();
###############################################################################################
#  Copyright (c) Microsoft Corporation. All rights reserved.
#  Licensed under the MIT License. See License.txt in the project root for license information.
###############################################################################################
name: $(Date:yyyyMMdd)$(Rev:.r)

trigger: none
pr: none

resources:
  repositories:
    - repository: templates
      type: github
      name: microsoft/vscode-engineering
      ref: main
      endpoint: Monaco

parameters:
  - name: publishMonacoEditorCore
    displayName: 🚀 Publish Monaco Editor Core
    type: boolean
    default: false
  - name: publishMonacoEditor
    displayName: 🚀 Publish Monaco Editor
    type: boolean
    default: false
  - name: publishWebpackPlugin
    displayName: 🚀 Publish Webpack Plugin
    type: boolean
    default: false

extends:
  template: azure-pipelines/npm-package/pipeline.yml@templates
  parameters:
    cgIgnoreDirectories: $(Build.SourcesDirectory)/dependencies/vscode
    npmPackages:
      - name: monaco-editor-core
        workingDirectory: $(Build.SourcesDirectory)/dependencies/vscode/out-monaco-editor-core
        testPlatforms: []
        skipAPIScan: true # package build requires Linux
        buildSteps:
          - script: sudo apt install -y libkrb5-dev
            displayName: Install libkrb5-dev

          - script: npm ci
            displayName: Install NPM dependencies

          - script: yarn ts-node ./scripts/ci/monaco-editor-core-prepare stable
            displayName: Setup, Build & Test monaco-editor-core

        tag: latest
        ghCreateTag: false
        publishPackage: ${{ parameters.publishMonacoEditorCore }}
        publishRequiresApproval: false

      - name: monaco-editor
        dependsOn: monaco-editor-core
        workingDirectory: $(Build.SourcesDirectory)/out/monaco-editor
        testPlatforms: []
        skipAPIScan: true # package build requires Linux
        buildSteps:
          - script: npm ci
            displayName: Install NPM dependencies

          - script: yarn ts-node ./scripts/ci/monaco-editor-prepare stable
            displayName: Setup, Build & Test monaco-editor

        tag: latest
        publishPackage: ${{ parameters.publishMonacoEditor }}
        publishRequiresApproval: false

      - name: monaco-editor-webpack-plugin
        dependsOn: monaco-editor
        workingDirectory: $(Build.SourcesDirectory)/webpack-plugin
        testPlatforms: []
        packagePlatform: Windows
        buildSteps:
          - script: npm ci
            displayName: Install NPM dependencies
            workingDirectory: $(Build.SourcesDirectory)/webpack-plugin

          - script: npm run compile
            displayName: Build plugin
            workingDirectory: $(Build.SourcesDirectory)/webpack-plugin

        tag: latest
        ghCreateTag: false
        publishPackage: ${{ parameters.publishWebpackPlugin }}
        publishRequiresApproval: false

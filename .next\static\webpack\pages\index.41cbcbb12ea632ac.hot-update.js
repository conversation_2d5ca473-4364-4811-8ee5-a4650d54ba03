"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/views/School/SchoolView.tsx":
/*!*****************************************!*\
  !*** ./src/views/School/SchoolView.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SchoolView: function() { return /* binding */ SchoolView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SchoolSubNav__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/SchoolSubNav */ \"./src/components/SchoolSubNav.tsx\");\n/* harmony import */ var _SchoolContent__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SchoolContent */ \"./src/views/School/SchoolContent.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst SchoolView = (param)=>{\n    let { activeDepartment, setActiveDepartment } = param;\n    _s();\n    const [activeSubSection, setActiveSubSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"dashboard\");\n    // Reset sub-section when department changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setActiveSubSection(\"dashboard\");\n    }, [\n        activeDepartment\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex gap-3 h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SchoolSubNav__WEBPACK_IMPORTED_MODULE_2__.SchoolSubNav, {\n                department: activeDepartment,\n                activeSubSection: activeSubSection,\n                setActiveSubSection: setActiveSubSection\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\SchoolView.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 min-h-0 overflow-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SchoolContent__WEBPACK_IMPORTED_MODULE_3__.SchoolContent, {\n                    activeDepartment: activeDepartment,\n                    activeSubSection: activeSubSection\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\SchoolView.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\SchoolView.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\SchoolView.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SchoolView, \"duBur7MvY59GWalCqSCZkyRhAq4=\");\n_c = SchoolView;\nvar _c;\n$RefreshReg$(_c, \"SchoolView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/School/SchoolView.tsx\n"));

/***/ })

});
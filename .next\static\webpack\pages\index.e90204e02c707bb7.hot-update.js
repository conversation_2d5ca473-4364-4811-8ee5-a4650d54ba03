"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/PerseusRenderer.tsx":
/*!********************************************!*\
  !*** ./src/components/PerseusRenderer.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PerseusRenderer: function() { return /* binding */ PerseusRenderer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst PerseusRenderer = (param)=>{\n    let { widgetType, widgetData } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!containerRef.current) return;\n        // Clear previous content\n        containerRef.current.innerHTML = \"\";\n        // Render based on widget type\n        switch(widgetType){\n            case \"numeric-input\":\n                renderNumericInput(containerRef.current);\n                break;\n            case \"categorizer\":\n                renderCategorizer(containerRef.current);\n                break;\n            case \"matcher\":\n                renderMatcher(containerRef.current);\n                break;\n            case \"orderer\":\n                renderOrderer(containerRef.current);\n                break;\n            case \"radio\":\n                renderRadio(containerRef.current);\n                break;\n            case \"expression\":\n                renderExpression(containerRef.current);\n                break;\n            case \"grapher\":\n                renderGrapher(containerRef.current);\n                break;\n            case \"matrix\":\n                renderMatrix(containerRef.current);\n                break;\n            case \"molecule\":\n                renderMolecule(containerRef.current);\n                break;\n            case \"phet-simulation\":\n                renderPhetSimulation(containerRef.current);\n                break;\n            case \"interactive-graph\":\n                renderInteractiveGraph(containerRef.current);\n                break;\n            case \"passage\":\n                renderPassage(containerRef.current);\n                break;\n            case \"sorter\":\n                renderSorter(containerRef.current);\n                break;\n            case \"cs-program\":\n                renderCSProgram(containerRef.current);\n                break;\n            case \"python-program\":\n                renderPythonProgram(containerRef.current);\n                break;\n            default:\n                renderPlaceholder(containerRef.current, widgetType);\n        }\n    }, [\n        widgetType,\n        widgetData\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"perseus-widget bg-white rounded-lg p-6 min-h-[400px]\",\n        style: {\n            fontFamily: \"Arial, sans-serif\"\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\PerseusRenderer.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PerseusRenderer, \"8puyVO4ts1RhCfXUmci3vLI3Njw=\");\n_c = PerseusRenderer;\n// Numeric Input Widget\nfunction renderNumericInput(container) {\n    container.innerHTML = '\\n    <div class=\"numeric-input-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Math Problem</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">What is 15 + 27?</p>\\n      <input \\n        type=\"number\" \\n        placeholder=\"Enter your answer\"\\n        style=\"\\n          padding: 10px; \\n          border: 2px solid #ddd; \\n          border-radius: 5px; \\n          font-size: 16px;\\n          width: 200px;\\n          margin-right: 10px;\\n        \"\\n        id=\"numeric-answer\"\\n      />\\n      <button \\n        onclick=\"checkNumericAnswer()\"\\n        style=\"\\n          background: #1c4f82; \\n          color: white; \\n          padding: 10px 20px; \\n          border: none; \\n          border-radius: 5px;\\n          cursor: pointer;\\n          font-size: 16px;\\n        \"\\n      >Check Answer</button>\\n      <div id=\"numeric-feedback\" style=\"margin-top: 15px; font-weight: bold;\"></div>\\n    </div>\\n  ';\n    // Add event handler\n    window.checkNumericAnswer = ()=>{\n        const input = document.getElementById(\"numeric-answer\");\n        const feedback = document.getElementById(\"numeric-feedback\");\n        if (input && feedback) {\n            const answer = parseInt(input.value);\n            if (answer === 42) {\n                feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Correct! Great job!</span>';\n            } else {\n                feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Try again. Think about 15 + 27.</span>';\n            }\n        }\n    };\n}\n// Categorizer Widget\nfunction renderCategorizer(container) {\n    container.innerHTML = '\\n    <div class=\"categorizer-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Categorize Animals</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">Drag each animal to the correct category:</p>\\n      \\n      <div style=\"display: flex; gap: 20px; margin-bottom: 20px;\">\\n        <div class=\"category-box\" style=\"\\n          border: 2px dashed #1c4f82; \\n          padding: 15px; \\n          min-height: 100px; \\n          width: 150px;\\n          border-radius: 8px;\\n          background: #f8f9fa;\\n        \">\\n          <h4 style=\"margin: 0 0 10px 0; color: #1c4f82;\">Mammals</h4>\\n          <div id=\"mammals-drop\" class=\"drop-zone\"></div>\\n        </div>\\n        \\n        <div class=\"category-box\" style=\"\\n          border: 2px dashed #28a745; \\n          padding: 15px; \\n          min-height: 100px; \\n          width: 150px;\\n          border-radius: 8px;\\n          background: #f8f9fa;\\n        \">\\n          <h4 style=\"margin: 0 0 10px 0; color: #28a745;\">Birds</h4>\\n          <div id=\"birds-drop\" class=\"drop-zone\"></div>\\n        </div>\\n      </div>\\n      \\n      <div style=\"display: flex; gap: 10px; flex-wrap: wrap;\">\\n        <div class=\"draggable-item\" draggable=\"true\" data-category=\"mammals\" style=\"\\n          background: #e9ecef; \\n          padding: 8px 12px; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">\\uD83D\\uDC15 Dog</div>\\n        <div class=\"draggable-item\" draggable=\"true\" data-category=\"birds\" style=\"\\n          background: #e9ecef; \\n          padding: 8px 12px; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">\\uD83E\\uDD85 Eagle</div>\\n        <div class=\"draggable-item\" draggable=\"true\" data-category=\"mammals\" style=\"\\n          background: #e9ecef; \\n          padding: 8px 12px; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">\\uD83D\\uDC31 Cat</div>\\n        <div class=\"draggable-item\" draggable=\"true\" data-category=\"birds\" style=\"\\n          background: #e9ecef; \\n          padding: 8px 12px; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">\\uD83D\\uDC26 Sparrow</div>\\n      </div>\\n      \\n      <button \\n        onclick=\"checkCategorizer()\"\\n        style=\"\\n          background: #1c4f82; \\n          color: white; \\n          padding: 10px 20px; \\n          border: none; \\n          border-radius: 5px;\\n          cursor: pointer;\\n          font-size: 16px;\\n          margin-top: 20px;\\n        \"\\n      >Check Categories</button>\\n      <div id=\"categorizer-feedback\" style=\"margin-top: 15px; font-weight: bold;\"></div>\\n    </div>\\n  ';\n    // Add drag and drop functionality\n    setupDragAndDrop();\n}\nfunction setupDragAndDrop() {\n    const draggables = document.querySelectorAll(\".draggable-item\");\n    const dropZones = document.querySelectorAll(\".drop-zone\");\n    draggables.forEach((draggable)=>{\n        draggable.addEventListener(\"dragstart\", (e)=>{\n            var _e_dataTransfer, _e_dataTransfer1;\n            (_e_dataTransfer = e.dataTransfer) === null || _e_dataTransfer === void 0 ? void 0 : _e_dataTransfer.setData(\"text/plain\", draggable.outerHTML);\n            (_e_dataTransfer1 = e.dataTransfer) === null || _e_dataTransfer1 === void 0 ? void 0 : _e_dataTransfer1.setData(\"category\", draggable.dataset.category || \"\");\n        });\n    });\n    dropZones.forEach((zone)=>{\n        zone.addEventListener(\"dragover\", (e)=>{\n            e.preventDefault();\n        });\n        zone.addEventListener(\"drop\", (e)=>{\n            var _e_dataTransfer, _e_dataTransfer1;\n            e.preventDefault();\n            const html = (_e_dataTransfer = e.dataTransfer) === null || _e_dataTransfer === void 0 ? void 0 : _e_dataTransfer.getData(\"text/plain\");\n            const category = (_e_dataTransfer1 = e.dataTransfer) === null || _e_dataTransfer1 === void 0 ? void 0 : _e_dataTransfer1.getData(\"category\");\n            if (html && zone.id.includes(category || \"\")) {\n                zone.innerHTML += html;\n            }\n        });\n    });\n    window.checkCategorizer = ()=>{\n        const mammalsZone = document.getElementById(\"mammals-drop\");\n        const birdsZone = document.getElementById(\"birds-drop\");\n        const feedback = document.getElementById(\"categorizer-feedback\");\n        if (mammalsZone && birdsZone && feedback) {\n            const mammalsCount = mammalsZone.children.length;\n            const birdsCount = birdsZone.children.length;\n            if (mammalsCount === 2 && birdsCount === 2) {\n                feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Perfect categorization!</span>';\n            } else {\n                feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Some animals are in wrong categories. Try again!</span>';\n            }\n        }\n    };\n}\n// Matcher Widget\nfunction renderMatcher(container) {\n    container.innerHTML = '\\n    <div class=\"matcher-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Match Words with Definitions</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">Click on a word, then click on its matching definition:</p>\\n      \\n      <div style=\"display: flex; gap: 40px;\">\\n        <div>\\n          <h4 style=\"color: #1c4f82;\">Words</h4>\\n          <div class=\"match-item\" data-match=\"photosynthesis\" onclick=\"selectMatch(this)\" style=\"\\n            background: #e9ecef; \\n            padding: 10px; \\n            margin: 5px 0; \\n            border-radius: 5px; \\n            cursor: pointer;\\n            border: 2px solid transparent;\\n          \">Photosynthesis</div>\\n          <div class=\"match-item\" data-match=\"gravity\" onclick=\"selectMatch(this)\" style=\"\\n            background: #e9ecef; \\n            padding: 10px; \\n            margin: 5px 0; \\n            border-radius: 5px; \\n            cursor: pointer;\\n            border: 2px solid transparent;\\n          \">Gravity</div>\\n          <div class=\"match-item\" data-match=\"democracy\" onclick=\"selectMatch(this)\" style=\"\\n            background: #e9ecef; \\n            padding: 10px; \\n            margin: 5px 0; \\n            border-radius: 5px; \\n            cursor: pointer;\\n            border: 2px solid transparent;\\n          \">Democracy</div>\\n        </div>\\n        \\n        <div>\\n          <h4 style=\"color: #28a745;\">Definitions</h4>\\n          <div class=\"match-item\" data-match=\"gravity\" onclick=\"selectMatch(this)\" style=\"\\n            background: #e9ecef; \\n            padding: 10px; \\n            margin: 5px 0; \\n            border-radius: 5px; \\n            cursor: pointer;\\n            border: 2px solid transparent;\\n          \">Force that pulls objects toward Earth</div>\\n          <div class=\"match-item\" data-match=\"photosynthesis\" onclick=\"selectMatch(this)\" style=\"\\n            background: #e9ecef; \\n            padding: 10px; \\n            margin: 5px 0; \\n            border-radius: 5px; \\n            cursor: pointer;\\n            border: 2px solid transparent;\\n          \">Process plants use to make food from sunlight</div>\\n          <div class=\"match-item\" data-match=\"democracy\" onclick=\"selectMatch(this)\" style=\"\\n            background: #e9ecef; \\n            padding: 10px; \\n            margin: 5px 0; \\n            border-radius: 5px; \\n            cursor: pointer;\\n            border: 2px solid transparent;\\n          \">Government by the people</div>\\n        </div>\\n      </div>\\n      \\n      <div id=\"matcher-feedback\" style=\"margin-top: 20px; font-weight: bold;\"></div>\\n    </div>\\n  ';\n    let selectedItems = [];\n    window.selectMatch = (element)=>{\n        if (selectedItems.length === 0) {\n            element.style.border = \"2px solid #1c4f82\";\n            selectedItems.push(element);\n        } else if (selectedItems.length === 1) {\n            const first = selectedItems[0];\n            const feedback = document.getElementById(\"matcher-feedback\");\n            if (first.dataset.match === element.dataset.match) {\n                first.style.background = \"#d4edda\";\n                element.style.background = \"#d4edda\";\n                first.style.border = \"2px solid #28a745\";\n                element.style.border = \"2px solid #28a745\";\n                if (feedback) feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Correct match!</span>';\n            } else {\n                first.style.border = \"2px solid #dc3545\";\n                element.style.border = \"2px solid #dc3545\";\n                if (feedback) feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Try again!</span>';\n                setTimeout(()=>{\n                    first.style.border = \"2px solid transparent\";\n                    element.style.border = \"2px solid transparent\";\n                }, 1000);\n            }\n            selectedItems = [];\n        }\n    };\n}\n// Orderer Widget\nfunction renderOrderer(container) {\n    container.innerHTML = '\\n    <div class=\"orderer-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Put in Chronological Order</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">Drag to arrange these historical events in order:</p>\\n      \\n      <div id=\"sortable-list\" style=\"min-height: 200px;\">\\n        <div class=\"sortable-item\" draggable=\"true\" data-order=\"3\" style=\"\\n          background: #e9ecef; \\n          padding: 15px; \\n          margin: 10px 0; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">World War II ends (1945)</div>\\n        <div class=\"sortable-item\" draggable=\"true\" data-order=\"1\" style=\"\\n          background: #e9ecef; \\n          padding: 15px; \\n          margin: 10px 0; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">American Civil War begins (1861)</div>\\n        <div class=\"sortable-item\" draggable=\"true\" data-order=\"2\" style=\"\\n          background: #e9ecef; \\n          padding: 15px; \\n          margin: 10px 0; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">World War I begins (1914)</div>\\n        <div class=\"sortable-item\" draggable=\"true\" data-order=\"4\" style=\"\\n          background: #e9ecef; \\n          padding: 15px; \\n          margin: 10px 0; \\n          border-radius: 5px; \\n          cursor: move;\\n          border: 1px solid #ced4da;\\n        \">Moon landing (1969)</div>\\n      </div>\\n      \\n      <button \\n        onclick=\"checkOrder()\"\\n        style=\"\\n          background: #1c4f82; \\n          color: white; \\n          padding: 10px 20px; \\n          border: none; \\n          border-radius: 5px;\\n          cursor: pointer;\\n          font-size: 16px;\\n          margin-top: 20px;\\n        \"\\n      >Check Order</button>\\n      <div id=\"orderer-feedback\" style=\"margin-top: 15px; font-weight: bold;\"></div>\\n    </div>\\n  ';\n    setupSortable();\n}\nfunction setupSortable() {\n    const sortableList = document.getElementById(\"sortable-list\");\n    if (!sortableList) return;\n    let draggedElement = null;\n    sortableList.addEventListener(\"dragstart\", (e)=>{\n        draggedElement = e.target;\n        if (draggedElement) {\n            draggedElement.style.opacity = \"0.5\";\n        }\n    });\n    sortableList.addEventListener(\"dragend\", (e)=>{\n        if (draggedElement) {\n            draggedElement.style.opacity = \"1\";\n            draggedElement = null;\n        }\n    });\n    sortableList.addEventListener(\"dragover\", (e)=>{\n        e.preventDefault();\n    });\n    sortableList.addEventListener(\"drop\", (e)=>{\n        e.preventDefault();\n        const target = e.target;\n        if (target && target.classList.contains(\"sortable-item\") && draggedElement) {\n            const rect = target.getBoundingClientRect();\n            const midpoint = rect.top + rect.height / 2;\n            if (e.clientY < midpoint) {\n                sortableList.insertBefore(draggedElement, target);\n            } else {\n                sortableList.insertBefore(draggedElement, target.nextSibling);\n            }\n        }\n    });\n    window.checkOrder = ()=>{\n        const items = Array.from(document.querySelectorAll(\".sortable-item\"));\n        const feedback = document.getElementById(\"orderer-feedback\");\n        let isCorrect = true;\n        items.forEach((item, index)=>{\n            const expectedOrder = parseInt(item.dataset.order || \"0\");\n            if (expectedOrder !== index + 1) {\n                isCorrect = false;\n            }\n        });\n        if (feedback) {\n            if (isCorrect) {\n                feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Perfect chronological order!</span>';\n            } else {\n                feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Not quite right. Check the dates!</span>';\n            }\n        }\n    };\n}\n// Radio (Multiple Choice) Widget\nfunction renderRadio(container) {\n    container.innerHTML = '\\n    <div class=\"radio-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Multiple Choice Question</h3>\\n      <p style=\"color: #333; margin-bottom: 15px; font-size: 16px;\">What is the capital of France?</p>\\n      \\n      <div style=\"margin-bottom: 20px;\">\\n        <label style=\"display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 5px; background: #f8f9fa;\">\\n          <input type=\"radio\" name=\"capital\" value=\"london\" style=\"margin-right: 10px;\"> London\\n        </label>\\n        <label style=\"display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 5px; background: #f8f9fa;\">\\n          <input type=\"radio\" name=\"capital\" value=\"paris\" style=\"margin-right: 10px;\"> Paris\\n        </label>\\n        <label style=\"display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 5px; background: #f8f9fa;\">\\n          <input type=\"radio\" name=\"capital\" value=\"berlin\" style=\"margin-right: 10px;\"> Berlin\\n        </label>\\n        <label style=\"display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 5px; background: #f8f9fa;\">\\n          <input type=\"radio\" name=\"capital\" value=\"madrid\" style=\"margin-right: 10px;\"> Madrid\\n        </label>\\n      </div>\\n      \\n      <button \\n        onclick=\"checkRadio()\"\\n        style=\"\\n          background: #1c4f82; \\n          color: white; \\n          padding: 10px 20px; \\n          border: none; \\n          border-radius: 5px;\\n          cursor: pointer;\\n          font-size: 16px;\\n        \"\\n      >Submit Answer</button>\\n      <div id=\"radio-feedback\" style=\"margin-top: 15px; font-weight: bold;\"></div>\\n    </div>\\n  ';\n    window.checkRadio = ()=>{\n        const selected = document.querySelector('input[name=\"capital\"]:checked');\n        const feedback = document.getElementById(\"radio-feedback\");\n        if (!selected) {\n            if (feedback) feedback.innerHTML = '<span style=\"color: #ffc107;\">Please select an answer.</span>';\n            return;\n        }\n        if (feedback) {\n            if (selected.value === \"paris\") {\n                feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Correct! Paris is the capital of France.</span>';\n            } else {\n                feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Incorrect. The capital of France is Paris.</span>';\n            }\n        }\n    };\n}\n// Expression Widget\nfunction renderExpression(container) {\n    container.innerHTML = '\\n    <div class=\"expression-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Solve the Expression</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">Simplify: 2x + 3x - 5</p>\\n      \\n      <input \\n        type=\"text\" \\n        placeholder=\"Enter simplified expression\"\\n        style=\"\\n          padding: 10px; \\n          border: 2px solid #ddd; \\n          border-radius: 5px; \\n          font-size: 16px;\\n          width: 250px;\\n          margin-right: 10px;\\n        \"\\n        id=\"expression-answer\"\\n      />\\n      <button \\n        onclick=\"checkExpression()\"\\n        style=\"\\n          background: #1c4f82; \\n          color: white; \\n          padding: 10px 20px; \\n          border: none; \\n          border-radius: 5px;\\n          cursor: pointer;\\n          font-size: 16px;\\n        \"\\n      >Check Answer</button>\\n      \\n      <div style=\"margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;\">\\n        <p style=\"margin: 0; color: #666; font-size: 14px;\">\\n          <strong>Hint:</strong> Combine like terms (terms with the same variable)\\n        </p>\\n      </div>\\n      \\n      <div id=\"expression-feedback\" style=\"margin-top: 15px; font-weight: bold;\"></div>\\n    </div>\\n  ';\n    window.checkExpression = ()=>{\n        const input = document.getElementById(\"expression-answer\");\n        const feedback = document.getElementById(\"expression-feedback\");\n        if (input && feedback) {\n            const answer = input.value.toLowerCase().replace(/\\s/g, \"\");\n            const correctAnswers = [\n                \"5x-5\",\n                \"5x+-5\",\n                \"-5+5x\"\n            ];\n            if (correctAnswers.includes(answer)) {\n                feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Excellent! 2x + 3x - 5 = 5x - 5</span>';\n            } else {\n                feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Not quite. Remember to combine like terms: 2x + 3x = 5x</span>';\n            }\n        }\n    };\n}\n// Grapher Widget - Interactive Math Graphing\nfunction renderGrapher(container) {\n    container.innerHTML = '\\n    <div class=\"grapher-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Interactive Function Grapher</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">Enter a function and see it graphed in real-time:</p>\\n\\n      <div style=\"display: flex; gap: 20px; margin-bottom: 20px;\">\\n        <div>\\n          <label style=\"display: block; margin-bottom: 5px; font-weight: bold;\">Function:</label>\\n          <input\\n            type=\"text\"\\n            id=\"function-input\"\\n            placeholder=\"e.g., x^2, sin(x), 2*x+1\"\\n            style=\"padding: 8px; border: 2px solid #ddd; border-radius: 5px; width: 200px;\"\\n          />\\n          <button\\n            onclick=\"plotFunction()\"\\n            style=\"margin-left: 10px; background: #1c4f82; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;\"\\n          >Plot</button>\\n        </div>\\n      </div>\\n\\n      <canvas id=\"graph-canvas\" width=\"500\" height=\"400\" style=\"border: 2px solid #ddd; background: white; border-radius: 5px;\"></canvas>\\n\\n      <div style=\"margin-top: 15px;\">\\n        <p style=\"color: #666; font-size: 14px;\">\\n          <strong>Try these functions:</strong> x^2, sin(x), cos(x), x^3-2*x, abs(x), sqrt(x)\\n        </p>\\n      </div>\\n    </div>\\n  ';\n    window.plotFunction = ()=>{\n        const input = document.getElementById(\"function-input\");\n        const canvas = document.getElementById(\"graph-canvas\");\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx || !input.value) return;\n        // Clear canvas\n        ctx.clearRect(0, 0, canvas.width, canvas.height);\n        // Draw axes\n        ctx.strokeStyle = \"#ccc\";\n        ctx.lineWidth = 1;\n        // Grid\n        for(let i = 0; i <= 20; i++){\n            const x = i / 20 * canvas.width;\n            const y = i / 20 * canvas.height;\n            ctx.beginPath();\n            ctx.moveTo(x, 0);\n            ctx.lineTo(x, canvas.height);\n            ctx.stroke();\n            ctx.beginPath();\n            ctx.moveTo(0, y);\n            ctx.lineTo(canvas.width, y);\n            ctx.stroke();\n        }\n        // Main axes\n        ctx.strokeStyle = \"#333\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.moveTo(canvas.width / 2, 0);\n        ctx.lineTo(canvas.width / 2, canvas.height);\n        ctx.moveTo(0, canvas.height / 2);\n        ctx.lineTo(canvas.width, canvas.height / 2);\n        ctx.stroke();\n        // Plot function\n        ctx.strokeStyle = \"#1c4f82\";\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        const func = input.value.toLowerCase().replace(/\\^/g, \"**\").replace(/sin/g, \"Math.sin\").replace(/cos/g, \"Math.cos\").replace(/sqrt/g, \"Math.sqrt\").replace(/abs/g, \"Math.abs\");\n        let firstPoint = true;\n        for(let px = 0; px < canvas.width; px++){\n            const x = (px - canvas.width / 2) / 20; // Scale\n            try {\n                const y = eval(func.replace(/x/g, x.toString()));\n                const py = canvas.height / 2 - y * 20; // Scale and flip\n                if (py >= 0 && py <= canvas.height) {\n                    if (firstPoint) {\n                        ctx.moveTo(px, py);\n                        firstPoint = false;\n                    } else {\n                        ctx.lineTo(px, py);\n                    }\n                }\n            } catch (e) {\n            // Skip invalid points\n            }\n        }\n        ctx.stroke();\n    };\n}\n// Matrix Operations Widget\nfunction renderMatrix(container) {\n    container.innerHTML = '\\n    <div class=\"matrix-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Matrix Operations</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">Enter two 2x2 matrices and perform operations:</p>\\n\\n      <div style=\"display: flex; gap: 30px; align-items: center; margin-bottom: 20px;\">\\n        <div>\\n          <h4>Matrix A:</h4>\\n          <div style=\"display: grid; grid-template-columns: 1fr 1fr; gap: 5px; width: 120px;\">\\n            <input type=\"number\" id=\"a11\" placeholder=\"a11\" style=\"padding: 5px; border: 1px solid #ddd; text-align: center;\">\\n            <input type=\"number\" id=\"a12\" placeholder=\"a12\" style=\"padding: 5px; border: 1px solid #ddd; text-align: center;\">\\n            <input type=\"number\" id=\"a21\" placeholder=\"a21\" style=\"padding: 5px; border: 1px solid #ddd; text-align: center;\">\\n            <input type=\"number\" id=\"a22\" placeholder=\"a22\" style=\"padding: 5px; border: 1px solid #ddd; text-align: center;\">\\n          </div>\\n        </div>\\n\\n        <div>\\n          <h4>Matrix B:</h4>\\n          <div style=\"display: grid; grid-template-columns: 1fr 1fr; gap: 5px; width: 120px;\">\\n            <input type=\"number\" id=\"b11\" placeholder=\"b11\" style=\"padding: 5px; border: 1px solid #ddd; text-align: center;\">\\n            <input type=\"number\" id=\"b12\" placeholder=\"b12\" style=\"padding: 5px; border: 1px solid #ddd; text-align: center;\">\\n            <input type=\"number\" id=\"b21\" placeholder=\"b21\" style=\"padding: 5px; border: 1px solid #ddd; text-align: center;\">\\n            <input type=\"number\" id=\"b22\" placeholder=\"b22\" style=\"padding: 5px; border: 1px solid #ddd; text-align: center;\">\\n          </div>\\n        </div>\\n\\n        <div>\\n          <h4>Result:</h4>\\n          <div id=\"matrix-result\" style=\"display: grid; grid-template-columns: 1fr 1fr; gap: 5px; width: 120px;\">\\n            <div style=\"padding: 5px; border: 1px solid #ddd; text-align: center; background: #f8f9fa;\">-</div>\\n            <div style=\"padding: 5px; border: 1px solid #ddd; text-align: center; background: #f8f9fa;\">-</div>\\n            <div style=\"padding: 5px; border: 1px solid #ddd; text-align: center; background: #f8f9fa;\">-</div>\\n            <div style=\"padding: 5px; border: 1px solid #ddd; text-align: center; background: #f8f9fa;\">-</div>\\n          </div>\\n        </div>\\n      </div>\\n\\n      <div style=\"display: flex; gap: 10px; margin-bottom: 20px;\">\\n        <button onclick=\"matrixAdd()\" style=\"background: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;\">A + B</button>\\n        <button onclick=\"matrixSubtract()\" style=\"background: #dc3545; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;\">A - B</button>\\n        <button onclick=\"matrixMultiply()\" style=\"background: #1c4f82; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;\">A \\xd7 B</button>\\n        <button onclick=\"matrixDeterminant()\" style=\"background: #6f42c1; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;\">Det(A)</button>\\n      </div>\\n\\n      <div id=\"matrix-explanation\" style=\"background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #1c4f82;\">\\n        <p style=\"margin: 0; color: #333;\">Select an operation to see the calculation steps.</p>\\n      </div>\\n    </div>\\n  ';\n    const getMatrix = (prefix)=>{\n        return [\n            [\n                parseFloat(document.getElementById(\"\".concat(prefix, \"11\")).value) || 0,\n                parseFloat(document.getElementById(\"\".concat(prefix, \"12\")).value) || 0\n            ],\n            [\n                parseFloat(document.getElementById(\"\".concat(prefix, \"21\")).value) || 0,\n                parseFloat(document.getElementById(\"\".concat(prefix, \"22\")).value) || 0\n            ]\n        ];\n    };\n    const displayResult = (result)=>{\n        const resultDiv = document.getElementById(\"matrix-result\");\n        if (resultDiv) {\n            resultDiv.innerHTML = '\\n        <div style=\"padding: 5px; border: 1px solid #ddd; text-align: center; background: #d4edda;\">'.concat(result[0][0], '</div>\\n        <div style=\"padding: 5px; border: 1px solid #ddd; text-align: center; background: #d4edda;\">').concat(result[0][1], '</div>\\n        <div style=\"padding: 5px; border: 1px solid #ddd; text-align: center; background: #d4edda;\">').concat(result[1][0], '</div>\\n        <div style=\"padding: 5px; border: 1px solid #ddd; text-align: center; background: #d4edda;\">').concat(result[1][1], \"</div>\\n      \");\n        }\n    };\n    window.matrixAdd = ()=>{\n        const A = getMatrix(\"a\");\n        const B = getMatrix(\"b\");\n        const result = [\n            [\n                A[0][0] + B[0][0],\n                A[0][1] + B[0][1]\n            ],\n            [\n                A[1][0] + B[1][0],\n                A[1][1] + B[1][1]\n            ]\n        ];\n        displayResult(result);\n        const explanation = document.getElementById(\"matrix-explanation\");\n        if (explanation) {\n            explanation.innerHTML = '<p style=\"margin: 0; color: #333;\"><strong>Addition:</strong> Add corresponding elements: ['.concat(A[0][0], \"+\").concat(B[0][0], \", \").concat(A[0][1], \"+\").concat(B[0][1], \"], [\").concat(A[1][0], \"+\").concat(B[1][0], \", \").concat(A[1][1], \"+\").concat(B[1][1], \"]</p>\");\n        }\n    };\n    window.matrixSubtract = ()=>{\n        const A = getMatrix(\"a\");\n        const B = getMatrix(\"b\");\n        const result = [\n            [\n                A[0][0] - B[0][0],\n                A[0][1] - B[0][1]\n            ],\n            [\n                A[1][0] - B[1][0],\n                A[1][1] - B[1][1]\n            ]\n        ];\n        displayResult(result);\n        const explanation = document.getElementById(\"matrix-explanation\");\n        if (explanation) {\n            explanation.innerHTML = '<p style=\"margin: 0; color: #333;\"><strong>Subtraction:</strong> Subtract corresponding elements: ['.concat(A[0][0], \"-\").concat(B[0][0], \", \").concat(A[0][1], \"-\").concat(B[0][1], \"], [\").concat(A[1][0], \"-\").concat(B[1][0], \", \").concat(A[1][1], \"-\").concat(B[1][1], \"]</p>\");\n        }\n    };\n    window.matrixMultiply = ()=>{\n        const A = getMatrix(\"a\");\n        const B = getMatrix(\"b\");\n        const result = [\n            [\n                A[0][0] * B[0][0] + A[0][1] * B[1][0],\n                A[0][0] * B[0][1] + A[0][1] * B[1][1]\n            ],\n            [\n                A[1][0] * B[0][0] + A[1][1] * B[1][0],\n                A[1][0] * B[0][1] + A[1][1] * B[1][1]\n            ]\n        ];\n        displayResult(result);\n        const explanation = document.getElementById(\"matrix-explanation\");\n        if (explanation) {\n            explanation.innerHTML = '<p style=\"margin: 0; color: #333;\"><strong>Multiplication:</strong> Row \\xd7 Column: ['.concat(A[0][0], \"\\xd7\").concat(B[0][0], \"+\").concat(A[0][1], \"\\xd7\").concat(B[1][0], \", \").concat(A[0][0], \"\\xd7\").concat(B[0][1], \"+\").concat(A[0][1], \"\\xd7\").concat(B[1][1], \"]</p>\");\n        }\n    };\n    window.matrixDeterminant = ()=>{\n        const A = getMatrix(\"a\");\n        const det = A[0][0] * A[1][1] - A[0][1] * A[1][0];\n        const resultDiv = document.getElementById(\"matrix-result\");\n        if (resultDiv) {\n            resultDiv.innerHTML = '\\n        <div style=\"padding: 20px; border: 1px solid #ddd; text-align: center; background: #d4edda; grid-column: 1/3; font-size: 18px; font-weight: bold;\">Det = '.concat(det, '</div>\\n        <div style=\"padding: 5px; border: 1px solid #ddd; text-align: center; background: #f8f9fa;\">-</div>\\n        <div style=\"padding: 5px; border: 1px solid #ddd; text-align: center; background: #f8f9fa;\">-</div>\\n      ');\n        }\n        const explanation = document.getElementById(\"matrix-explanation\");\n        if (explanation) {\n            explanation.innerHTML = '<p style=\"margin: 0; color: #333;\"><strong>Determinant:</strong> ad - bc = '.concat(A[0][0], \"\\xd7\").concat(A[1][1], \" - \").concat(A[0][1], \"\\xd7\").concat(A[1][0], \" = \").concat(det, \"</p>\");\n        }\n    };\n}\n// Molecule Builder Widget\nfunction renderMolecule(container) {\n    container.innerHTML = '\\n    <div class=\"molecule-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Molecule Builder</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">Build molecules by connecting atoms:</p>\\n\\n      <div style=\"display: flex; gap: 20px;\">\\n        <div style=\"flex: 1;\">\\n          <h4>Available Atoms:</h4>\\n          <div style=\"display: flex; gap: 10px; margin-bottom: 20px;\">\\n            <button onclick=\"addAtom(\\'H\\')\" style=\"background: #ff6b6b; color: white; padding: 10px; border: none; border-radius: 50%; width: 50px; height: 50px; cursor: pointer; font-weight: bold;\">H</button>\\n            <button onclick=\"addAtom(\\'C\\')\" style=\"background: #4ecdc4; color: white; padding: 10px; border: none; border-radius: 50%; width: 50px; height: 50px; cursor: pointer; font-weight: bold;\">C</button>\\n            <button onclick=\"addAtom(\\'O\\')\" style=\"background: #45b7d1; color: white; padding: 10px; border: none; border-radius: 50%; width: 50px; height: 50px; cursor: pointer; font-weight: bold;\">O</button>\\n            <button onclick=\"addAtom(\\'N\\')\" style=\"background: #96ceb4; color: white; padding: 10px; border: none; border-radius: 50%; width: 50px; height: 50px; cursor: pointer; font-weight: bold;\">N</button>\\n          </div>\\n\\n          <canvas id=\"molecule-canvas\" width=\"400\" height=\"300\" style=\"border: 2px solid #ddd; background: white; border-radius: 5px; cursor: crosshair;\"></canvas>\\n\\n          <div style=\"margin-top: 10px;\">\\n            <button onclick=\"clearMolecule()\" style=\"background: #dc3545; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;\">Clear</button>\\n            <button onclick=\"identifyMolecule()\" style=\"background: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;\">Identify Molecule</button>\\n          </div>\\n        </div>\\n\\n        <div style=\"width: 200px;\">\\n          <h4>Molecule Info:</h4>\\n          <div id=\"molecule-info\" style=\"background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #1c4f82;\">\\n            <p style=\"margin: 0; color: #333;\">Click atoms to add them to the canvas, then click \"Identify Molecule\"</p>\\n          </div>\\n        </div>\\n      </div>\\n    </div>\\n  ';\n    let atoms = [];\n    let selectedAtomType = \"H\";\n    window.addAtom = (type)=>{\n        selectedAtomType = type;\n        const info = document.getElementById(\"molecule-info\");\n        if (info) {\n            info.innerHTML = '<p style=\"margin: 0; color: #333;\">Selected: <strong>'.concat(type, \"</strong><br>Click on canvas to place atom</p>\");\n        }\n    };\n    const canvas = document.getElementById(\"molecule-canvas\");\n    if (canvas) {\n        canvas.addEventListener(\"click\", (e)=>{\n            const rect = canvas.getBoundingClientRect();\n            const x = e.clientX - rect.left;\n            const y = e.clientY - rect.top;\n            atoms.push({\n                x,\n                y,\n                type: selectedAtomType\n            });\n            drawMolecule();\n        });\n    }\n    const drawMolecule = ()=>{\n        const canvas = document.getElementById(\"molecule-canvas\");\n        const ctx = canvas === null || canvas === void 0 ? void 0 : canvas.getContext(\"2d\");\n        if (!ctx) return;\n        ctx.clearRect(0, 0, canvas.width, canvas.height);\n        // Draw bonds (simple lines between nearby atoms)\n        ctx.strokeStyle = \"#333\";\n        ctx.lineWidth = 2;\n        for(let i = 0; i < atoms.length; i++){\n            for(let j = i + 1; j < atoms.length; j++){\n                const dist = Math.sqrt((atoms[i].x - atoms[j].x) ** 2 + (atoms[i].y - atoms[j].y) ** 2);\n                if (dist < 80) {\n                    ctx.beginPath();\n                    ctx.moveTo(atoms[i].x, atoms[i].y);\n                    ctx.lineTo(atoms[j].x, atoms[j].y);\n                    ctx.stroke();\n                }\n            }\n        }\n        // Draw atoms\n        atoms.forEach((atom)=>{\n            const colors = {\n                H: \"#ff6b6b\",\n                C: \"#4ecdc4\",\n                O: \"#45b7d1\",\n                N: \"#96ceb4\"\n            };\n            ctx.fillStyle = colors[atom.type] || \"#ccc\";\n            ctx.beginPath();\n            ctx.arc(atom.x, atom.y, 20, 0, 2 * Math.PI);\n            ctx.fill();\n            ctx.fillStyle = \"white\";\n            ctx.font = \"bold 14px Arial\";\n            ctx.textAlign = \"center\";\n            ctx.fillText(atom.type, atom.x, atom.y + 5);\n        });\n    };\n    window.clearMolecule = ()=>{\n        atoms = [];\n        drawMolecule();\n        const info = document.getElementById(\"molecule-info\");\n        if (info) {\n            info.innerHTML = '<p style=\"margin: 0; color: #333;\">Canvas cleared. Select an atom and start building!</p>';\n        }\n    };\n    window.identifyMolecule = ()=>{\n        const counts = atoms.reduce((acc, atom)=>{\n            acc[atom.type] = (acc[atom.type] || 0) + 1;\n            return acc;\n        }, {});\n        let moleculeName = \"Unknown\";\n        const formula = Object.entries(counts).map((param)=>{\n            let [type, count] = param;\n            return count > 1 ? \"\".concat(type).concat(count) : type;\n        }).join(\"\");\n        // Simple molecule identification\n        if (formula === \"H2O\") moleculeName = \"Water\";\n        else if (formula === \"CO2\") moleculeName = \"Carbon Dioxide\";\n        else if (formula === \"CH4\") moleculeName = \"Methane\";\n        else if (formula === \"NH3\") moleculeName = \"Ammonia\";\n        else if (formula === \"H2\") moleculeName = \"Hydrogen Gas\";\n        else if (formula === \"O2\") moleculeName = \"Oxygen Gas\";\n        const info = document.getElementById(\"molecule-info\");\n        if (info) {\n            info.innerHTML = '\\n        <p style=\"margin: 0; color: #333;\">\\n          <strong>Formula:</strong> '.concat(formula, \"<br>\\n          <strong>Name:</strong> \").concat(moleculeName, \"<br>\\n          <strong>Atoms:</strong> \").concat(atoms.length, \"\\n        </p>\\n      \");\n        }\n    };\n}\n// Reading Passage Widget\nfunction renderPassage(container) {\n    container.innerHTML = '\\n    <div class=\"passage-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Reading Comprehension</h3>\\n\\n      <div style=\"background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #1c4f82;\">\\n        <h4 style=\"color: #1c4f82; margin-top: 0;\">The Water Cycle</h4>\\n        <p style=\"line-height: 1.6; color: #333;\">\\n          The water cycle is the continuous movement of water on, above, and below the surface of the Earth.\\n          Water evaporates from oceans, lakes, and rivers due to heat from the sun. This water vapor rises into\\n          the atmosphere where it cools and condenses into tiny droplets, forming clouds. When these droplets\\n          become too heavy, they fall back to Earth as precipitation in the form of rain, snow, or hail.\\n          Some of this water flows into rivers and streams, eventually returning to the oceans, while some\\n          seeps into the ground to become groundwater. This process repeats continuously, making the water\\n          cycle essential for all life on Earth.\\n        </p>\\n      </div>\\n\\n      <div style=\"background: white; padding: 20px; border-radius: 8px; border: 2px solid #ddd;\">\\n        <h4 style=\"color: #1c4f82; margin-top: 0;\">Comprehension Questions</h4>\\n\\n        <div style=\"margin-bottom: 20px;\">\\n          <p style=\"font-weight: bold; margin-bottom: 10px;\">1. What causes water to evaporate from oceans and lakes?</p>\\n          <label style=\"display: block; margin: 5px 0; cursor: pointer;\">\\n            <input type=\"radio\" name=\"q1\" value=\"wind\" style=\"margin-right: 8px;\"> Wind\\n          </label>\\n          <label style=\"display: block; margin: 5px 0; cursor: pointer;\">\\n            <input type=\"radio\" name=\"q1\" value=\"heat\" style=\"margin-right: 8px;\"> Heat from the sun\\n          </label>\\n          <label style=\"display: block; margin: 5px 0; cursor: pointer;\">\\n            <input type=\"radio\" name=\"q1\" value=\"gravity\" style=\"margin-right: 8px;\"> Gravity\\n          </label>\\n        </div>\\n\\n        <div style=\"margin-bottom: 20px;\">\\n          <p style=\"font-weight: bold; margin-bottom: 10px;\">2. What happens when water vapor cools in the atmosphere?</p>\\n          <label style=\"display: block; margin: 5px 0; cursor: pointer;\">\\n            <input type=\"radio\" name=\"q2\" value=\"disappears\" style=\"margin-right: 8px;\"> It disappears\\n          </label>\\n          <label style=\"display: block; margin: 5px 0; cursor: pointer;\">\\n            <input type=\"radio\" name=\"q2\" value=\"condenses\" style=\"margin-right: 8px;\"> It condenses into droplets\\n          </label>\\n          <label style=\"display: block; margin: 5px 0; cursor: pointer;\">\\n            <input type=\"radio\" name=\"q2\" value=\"heats\" style=\"margin-right: 8px;\"> It heats up more\\n          </label>\\n        </div>\\n\\n        <div style=\"margin-bottom: 20px;\">\\n          <p style=\"font-weight: bold; margin-bottom: 10px;\">3. List three forms of precipitation mentioned in the passage:</p>\\n          <input type=\"text\" id=\"precipitation-answer\" placeholder=\"Enter three forms separated by commas\"\\n                 style=\"width: 100%; padding: 10px; border: 2px solid #ddd; border-radius: 5px;\">\\n        </div>\\n\\n        <button onclick=\"checkPassageAnswers()\"\\n                style=\"background: #1c4f82; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;\">\\n          Check Answers\\n        </button>\\n\\n        <div id=\"passage-feedback\" style=\"margin-top: 15px; font-weight: bold;\"></div>\\n      </div>\\n    </div>\\n  ';\n    window.checkPassageAnswers = ()=>{\n        const q1 = document.querySelector('input[name=\"q1\"]:checked');\n        const q2 = document.querySelector('input[name=\"q2\"]:checked');\n        const q3 = document.getElementById(\"precipitation-answer\");\n        const feedback = document.getElementById(\"passage-feedback\");\n        let score = 0;\n        let results = [];\n        if ((q1 === null || q1 === void 0 ? void 0 : q1.value) === \"heat\") {\n            score++;\n            results.push(\"✓ Question 1: Correct!\");\n        } else {\n            results.push(\"✗ Question 1: The sun's heat causes evaporation.\");\n        }\n        if ((q2 === null || q2 === void 0 ? void 0 : q2.value) === \"condenses\") {\n            score++;\n            results.push(\"✓ Question 2: Correct!\");\n        } else {\n            results.push(\"✗ Question 2: Water vapor condenses into droplets.\");\n        }\n        const precipitationAnswer = (q3 === null || q3 === void 0 ? void 0 : q3.value.toLowerCase()) || \"\";\n        const hasRain = precipitationAnswer.includes(\"rain\");\n        const hasSnow = precipitationAnswer.includes(\"snow\");\n        const hasHail = precipitationAnswer.includes(\"hail\");\n        if (hasRain && hasSnow && hasHail) {\n            score++;\n            results.push(\"✓ Question 3: Correct! Rain, snow, and hail.\");\n        } else {\n            results.push(\"✗ Question 3: The three forms are rain, snow, and hail.\");\n        }\n        if (feedback) {\n            const color = score === 3 ? \"#28a745\" : score >= 2 ? \"#ffc107\" : \"#dc3545\";\n            feedback.innerHTML = '\\n        <div style=\"color: '.concat(color, ';\">Score: ').concat(score, \"/3</div>\\n        \").concat(results.map((result)=>'<div style=\"margin: 5px 0;\">'.concat(result, \"</div>\")).join(\"\"), \"\\n      \");\n        }\n    };\n}\n// Programming Widget - CS Concepts\nfunction renderCSProgram(container) {\n    container.innerHTML = '\\n    <div class=\"cs-program-widget\">\\n      <h3 style=\"color: #1c4f82; margin-bottom: 20px; font-size: 18px;\">Computer Science Concepts</h3>\\n      <p style=\"color: #333; margin-bottom: 15px;\">Learn programming concepts through interactive exercises:</p>\\n\\n      <div style=\"display: flex; gap: 20px;\">\\n        <div style=\"flex: 1;\">\\n          <h4>Algorithm Challenge: Sorting</h4>\\n          <p style=\"color: #666; margin-bottom: 15px;\">Arrange these numbers in ascending order by dragging:</p>\\n\\n          <div id=\"sorting-container\" style=\"display: flex; gap: 10px; margin-bottom: 20px; min-height: 60px; padding: 10px; border: 2px dashed #ddd; border-radius: 5px;\">\\n            <div class=\"sort-item\" draggable=\"true\" data-value=\"7\" style=\"background: #ff6b6b; color: white; padding: 15px; border-radius: 5px; cursor: move; font-weight: bold; user-select: none;\">7</div>\\n            <div class=\"sort-item\" draggable=\"true\" data-value=\"3\" style=\"background: #4ecdc4; color: white; padding: 15px; border-radius: 5px; cursor: move; font-weight: bold; user-select: none;\">3</div>\\n            <div class=\"sort-item\" draggable=\"true\" data-value=\"9\" style=\"background: #45b7d1; color: white; padding: 15px; border-radius: 5px; cursor: move; font-weight: bold; user-select: none;\">9</div>\\n            <div class=\"sort-item\" draggable=\"true\" data-value=\"1\" style=\"background: #96ceb4; color: white; padding: 15px; border-radius: 5px; cursor: move; font-weight: bold; user-select: none;\">1</div>\\n            <div class=\"sort-item\" draggable=\"true\" data-value=\"5\" style=\"background: #feca57; color: white; padding: 15px; border-radius: 5px; cursor: move; font-weight: bold; user-select: none;\">5</div>\\n          </div>\\n\\n          <button onclick=\"checkSorting()\" style=\"background: #1c4f82; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;\">Check Order</button>\\n          <button onclick=\"shuffleNumbers()\" style=\"background: #6c757d; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;\">Shuffle</button>\\n\\n          <div id=\"sorting-feedback\" style=\"margin-top: 15px; font-weight: bold;\"></div>\\n        </div>\\n\\n        <div style=\"width: 300px;\">\\n          <h4>Binary Converter</h4>\\n          <p style=\"color: #666; margin-bottom: 15px;\">Convert between decimal and binary:</p>\\n\\n          <div style=\"margin-bottom: 15px;\">\\n            <label style=\"display: block; margin-bottom: 5px; font-weight: bold;\">Decimal:</label>\\n            <input type=\"number\" id=\"decimal-input\" placeholder=\"Enter decimal number\" min=\"0\" max=\"255\"\\n                   style=\"width: 100%; padding: 8px; border: 2px solid #ddd; border-radius: 5px;\">\\n          </div>\\n\\n          <div style=\"margin-bottom: 15px;\">\\n            <label style=\"display: block; margin-bottom: 5px; font-weight: bold;\">Binary:</label>\\n            <input type=\"text\" id=\"binary-input\" placeholder=\"Enter binary number\" pattern=\"[01]*\"\\n                   style=\"width: 100%; padding: 8px; border: 2px solid #ddd; border-radius: 5px;\">\\n          </div>\\n\\n          <div style=\"display: flex; gap: 10px; margin-bottom: 15px;\">\\n            <button onclick=\"convertToBinary()\" style=\"background: #28a745; color: white; padding: 8px 12px; border: none; border-radius: 5px; cursor: pointer; font-size: 12px;\">Dec→Bin</button>\\n            <button onclick=\"convertToDecimal()\" style=\"background: #dc3545; color: white; padding: 8px 12px; border: none; border-radius: 5px; cursor: pointer; font-size: 12px;\">Bin→Dec</button>\\n          </div>\\n\\n          <div id=\"binary-explanation\" style=\"background: #f8f9fa; padding: 10px; border-radius: 5px; border-left: 4px solid #1c4f82; font-size: 14px;\">\\n            <p style=\"margin: 0; color: #333;\">Enter a number and click convert to see the binary representation!</p>\\n          </div>\\n        </div>\\n      </div>\\n    </div>\\n  ';\n    // Setup drag and drop for sorting\n    const setupSorting = ()=>{\n        const container = document.getElementById(\"sorting-container\");\n        if (!container) return;\n        let draggedElement = null;\n        container.addEventListener(\"dragstart\", (e)=>{\n            draggedElement = e.target;\n            if (draggedElement) {\n                draggedElement.style.opacity = \"0.5\";\n            }\n        });\n        container.addEventListener(\"dragend\", (e)=>{\n            if (draggedElement) {\n                draggedElement.style.opacity = \"1\";\n                draggedElement = null;\n            }\n        });\n        container.addEventListener(\"dragover\", (e)=>{\n            e.preventDefault();\n        });\n        container.addEventListener(\"drop\", (e)=>{\n            e.preventDefault();\n            const target = e.target;\n            if (target && target.classList.contains(\"sort-item\") && draggedElement) {\n                const rect = target.getBoundingClientRect();\n                const midpoint = rect.left + rect.width / 2;\n                if (e.clientX < midpoint) {\n                    container.insertBefore(draggedElement, target);\n                } else {\n                    container.insertBefore(draggedElement, target.nextSibling);\n                }\n            }\n        });\n    };\n    setupSorting();\n    window.checkSorting = ()=>{\n        const items = Array.from(document.querySelectorAll(\".sort-item\"));\n        const values = items.map((item)=>parseInt(item.dataset.value || \"0\"));\n        const isCorrect = values.every((val, i)=>i === 0 || val >= values[i - 1]);\n        const feedback = document.getElementById(\"sorting-feedback\");\n        if (feedback) {\n            if (isCorrect) {\n                feedback.innerHTML = '<span style=\"color: #28a745;\">✓ Perfect! The numbers are in ascending order: ' + values.join(\" < \") + \"</span>\";\n            } else {\n                feedback.innerHTML = '<span style=\"color: #dc3545;\">✗ Not quite right. Try arranging from smallest to largest.</span>';\n            }\n        }\n    };\n    window.shuffleNumbers = ()=>{\n        const container = document.getElementById(\"sorting-container\");\n        if (!container) return;\n        const items = Array.from(container.children);\n        items.sort(()=>Math.random() - 0.5);\n        items.forEach((item)=>container.appendChild(item));\n        const feedback = document.getElementById(\"sorting-feedback\");\n        if (feedback) feedback.innerHTML = \"\";\n    };\n    window.convertToBinary = ()=>{\n        const decimalInput = document.getElementById(\"decimal-input\");\n        const binaryInput = document.getElementById(\"binary-input\");\n        const explanation = document.getElementById(\"binary-explanation\");\n        const decimal = parseInt(decimalInput.value);\n        if (isNaN(decimal) || decimal < 0) return;\n        const binary = decimal.toString(2);\n        binaryInput.value = binary;\n        if (explanation) {\n            explanation.innerHTML = '\\n        <p style=\"margin: 0; color: #333;\">\\n          <strong>'.concat(decimal, \"</strong> in binary is <strong>\").concat(binary, \"</strong><br>\\n          <small>Each position represents a power of 2: \").concat(binary.split(\"\").map((bit, i)=>\"\".concat(bit, \"\\xd72^\").concat(binary.length - 1 - i)).join(\" + \"), \"</small>\\n        </p>\\n      \");\n        }\n    };\n    window.convertToDecimal = ()=>{\n        const decimalInput = document.getElementById(\"decimal-input\");\n        const binaryInput = document.getElementById(\"binary-input\");\n        const explanation = document.getElementById(\"binary-explanation\");\n        const binary = binaryInput.value;\n        if (!/^[01]+$/.test(binary)) return;\n        const decimal = parseInt(binary, 2);\n        decimalInput.value = decimal.toString();\n        if (explanation) {\n            explanation.innerHTML = '\\n        <p style=\"margin: 0; color: #333;\">\\n          <strong>'.concat(binary, \"</strong> in decimal is <strong>\").concat(decimal, \"</strong><br>\\n          <small>Calculation: \").concat(binary.split(\"\").map((bit, i)=>\"\".concat(bit, \"\\xd7\").concat(Math.pow(2, binary.length - 1 - i))).join(\" + \"), \" = \").concat(decimal, \"</small>\\n        </p>\\n      \");\n        }\n    };\n}\n// Placeholder for unsupported widgets\nfunction renderPlaceholder(container, widgetType) {\n    container.innerHTML = '\\n    <div style=\"text-align: center; padding: 40px;\">\\n      <div style=\"font-size: 48px; margin-bottom: 20px;\">\\uD83C\\uDFAE</div>\\n      <h3 style=\"color: #1c4f82; margin-bottom: 15px;\">Perseus Widget: '.concat(widgetType, '</h3>\\n      <p style=\"color: #666; margin-bottom: 20px;\">This educational widget is being loaded...</p>\\n      <div style=\"background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #1c4f82;\">\\n        <p style=\"margin: 0; color: #333; font-size: 14px;\">\\n          Real Khan Academy Perseus widget integration in progress.\\n          This will render the actual interactive educational content.\\n        </p>\\n      </div>\\n    </div>\\n  ');\n}\nvar _c;\n$RefreshReg$(_c, \"PerseusRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/PerseusRenderer.tsx\n"));

/***/ })

});
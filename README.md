# Eyes Shield - Futuristic Dashboard UI

A production-ready, high-tech dashboard application with glassmorphism design, real-time data visualization, and AI-powered features.

## 🚀 Features

- **Real-time Dashboard** - Live system monitoring and analytics
- **AI Integration** - Google Gemini AI for intelligent data analysis
- **Database Integration** - MySQL database with Hostinger support
- **Responsive Design** - Optimized for desktop and mobile devices
- **Glassmorphism UI** - Modern, futuristic interface design
- **Real-time Updates** - Live data fetching and updates
- **Production Ready** - Optimized for deployment

## 🛠 Tech Stack

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS, Custom CSS
- **Database**: MySQL (Hostinger compatible)
- **AI**: Google Gemini API
- **Icons**: Heroicons
- **Package Manager**: pnpm

## 📋 Prerequisites

- Node.js 18+
- pnpm package manager
- MySQL database (Hostinger or local)
- Google Gemini API key

## 🔧 Installation

1. **Install dependencies**
   ```bash
   pnpm install
   ```

2. **Environment Setup**
   ```bash
   cp .env.example .env.local
   ```

3. **Configure Environment Variables**
   Edit `.env.local` with your actual values:
   ```env
   # Google Gemini API Key
   GEMINI_API_KEY=your_actual_gemini_api_key_here

   # Hostinger MySQL Database
   DB_HOST=your_hostinger_mysql_host
   DB_PORT=3306
   DB_USER=your_database_username
   DB_PASSWORD=your_database_password
   DB_NAME=your_database_name
   DB_SSL=true
   ```

4. **Start Development Server**
   ```bash
   pnpm dev
   ```

## 🗄️ Database Setup

The application automatically creates and seeds the database tables on first run.

## 🚀 Production Deployment

1. **Build the application**
   ```bash
   pnpm build
   ```

2. **Configure production environment variables**
3. **Deploy to Hostinger or your preferred hosting platform**

## 📱 Features Overview

### Dashboard Views
- **Analytics** - Real-time system metrics and charts
- **School** - Academic progress and course management
- **Tools** - System diagnostics and network testing
- **Market** - Financial data and product showcase
- **Bookstore** - Digital library and book management
- **Concierge** - Notifications and service requests

### AI Features
- **Intelligent Analysis** - AI-powered data insights
- **Mood Analysis** - Emotional state detection
- **Dynamic Content** - AI-generated dashboard widgets

## 🔒 Security

- Environment variables for sensitive data
- SSL database connections
- Input validation and sanitization
- Secure API endpoints

## 📄 License

MIT License

---

**Built with ❤️ for the future of dashboard interfaces**

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/views/Coder/CoderView.tsx":
/*!***************************************!*\
  !*** ./src/views/Coder/CoderView.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CoderView: function() { return /* binding */ CoderView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"./node_modules/.pnpm/@monaco-editor+react@4.7.0__1ce2f918ff59bd31eee90d0f34fee1f7/node_modules/@monaco-editor/react/dist/index.mjs\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst FileTreeNode = (param)=>{\n    let { item, level, onFileClick } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(item.isOpen || false);\n    const handleClick = ()=>{\n        if (item.type === \"folder\") {\n            setIsOpen(!isOpen);\n        } else {\n            onFileClick(item.name);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2 px-2 py-1 hover:bg-gray-700/50 cursor-pointer text-sm\",\n                style: {\n                    paddingLeft: \"\".concat(level * 16 + 8, \"px\")\n                },\n                onClick: handleClick,\n                children: [\n                    item.type === \"folder\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-400\",\n                        children: isOpen ? \"\\uD83D\\uDCC2\" : \"\\uD83D\\uDCC1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, undefined),\n                    item.type === \"file\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-blue-400\",\n                        children: \"\\uD83D\\uDCC4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-300\",\n                        children: item.name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined),\n            item.type === \"folder\" && isOpen && item.children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: item.children.map((child, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileTreeNode, {\n                        item: child,\n                        level: level + 1,\n                        onFileClick: onFileClick\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 44,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FileTreeNode, \"1NLt9oXF2DSJYlKhLhMlvItPqek=\");\n_c = FileTreeNode;\nconst CoderView = ()=>{\n    _s1();\n    const [activeFile, setActiveFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"App.tsx\");\n    const [openFiles, setOpenFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"App.tsx\"\n    ]);\n    const [showTerminal, setShowTerminal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sidebarTab, setSidebarTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"files\");\n    const [showNewFileDialog, setShowNewFileDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNewProjectDialog, setShowNewProjectDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newFileName, setNewFileName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [newProjectName, setNewProjectName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [terminals, setTerminals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"main\",\n            name: \"Terminal 1\",\n            output: [\n                \"Welcome to React IDE Terminal\",\n                \"$ npm install\",\n                \"✓ Dependencies installed successfully\",\n                \"$ npm start\",\n                \"Starting development server...\",\n                \"Local: http://localhost:3000\",\n                \"webpack compiled successfully\",\n                \"$ \"\n            ]\n        }\n    ]);\n    const [activeTerminal, setActiveTerminal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"main\");\n    const [terminalInput, setTerminalInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [files, setFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"App.tsx\": \"import React, { useState } from 'react';\\nimport './App.css';\\n\\nfunction App() {\\n  const [count, setCount] = useState(0);\\n\\n  return (\\n    <div className=\\\"App\\\">\\n      <header className=\\\"App-header\\\">\\n        <h1>Welcome to React IDE</h1>\\n        <p>Count: {count}</p>\\n        <button onClick={() => setCount(count + 1)}>\\n          Increment\\n        </button>\\n        <button onClick={() => setCount(count - 1)}>\\n          Decrement\\n        </button>\\n      </header>\\n    </div>\\n  );\\n}\\n\\nexport default App;\",\n        \"App.css\": \"body {\\n  margin: 0;\\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\\n    sans-serif;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n.App {\\n  text-align: center;\\n}\\n\\n.App-header {\\n  background-color: #282c34;\\n  padding: 20px;\\n  color: white;\\n  min-height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: calc(10px + 2vmin);\\n}\\n\\nbutton {\\n  background-color: #61dafb;\\n  border: none;\\n  padding: 10px 20px;\\n  margin: 10px;\\n  border-radius: 5px;\\n  cursor: pointer;\\n  font-size: 16px;\\n}\\n\\nbutton:hover {\\n  background-color: #21a1c4;\\n}\",\n        \"package.json\": '{\\n  \"name\": \"react-ide-project\",\\n  \"version\": \"1.0.0\",\\n  \"private\": true,\\n  \"dependencies\": {\\n    \"react\": \"^18.2.0\",\\n    \"react-dom\": \"^18.2.0\",\\n    \"typescript\": \"^4.9.5\"\\n  },\\n  \"scripts\": {\\n    \"start\": \"react-scripts start\",\\n    \"build\": \"react-scripts build\",\\n    \"test\": \"react-scripts test\",\\n    \"eject\": \"react-scripts eject\"\\n  },\\n  \"eslintConfig\": {\\n    \"extends\": [\\n      \"react-app\",\\n      \"react-app/jest\"\\n    ]\\n  },\\n  \"browserslist\": {\\n    \"production\": [\\n      \">0.2%\",\\n      \"not dead\",\\n      \"not op_mini all\"\\n    ],\\n    \"development\": [\\n      \"last 1 chrome version\",\\n      \"last 1 firefox version\",\\n      \"last 1 safari version\"\\n    ]\\n  }\\n}',\n        \"README.md\": \"# React IDE Project\\n\\nThis is a sample React project created in the IDE.\\n\\n## Available Scripts\\n\\n- `npm start` - Runs the app in development mode\\n- `npm build` - Builds the app for production\\n- `npm test` - Launches the test runner\\n\\n## Features\\n\\n- React with TypeScript\\n- Modern CSS styling\\n- Interactive components\\n- Hot reloading\\n\\n## Getting Started\\n\\n1. Install dependencies: `npm install`\\n2. Start the development server: `npm start`\\n3. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.\\n\"\n    });\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileTree = [\n        {\n            name: \"src\",\n            type: \"folder\",\n            isOpen: true,\n            children: [\n                {\n                    name: \"App.tsx\",\n                    type: \"file\"\n                },\n                {\n                    name: \"App.css\",\n                    type: \"file\"\n                },\n                {\n                    name: \"index.tsx\",\n                    type: \"file\"\n                }\n            ]\n        },\n        {\n            name: \"public\",\n            type: \"folder\",\n            children: [\n                {\n                    name: \"index.html\",\n                    type: \"file\"\n                },\n                {\n                    name: \"favicon.ico\",\n                    type: \"file\"\n                }\n            ]\n        },\n        {\n            name: \"package.json\",\n            type: \"file\"\n        },\n        {\n            name: \"README.md\",\n            type: \"file\"\n        }\n    ];\n    const handleEditorDidMount = (editor, monaco)=>{\n        editorRef.current = editor;\n        // Configure Monaco Editor\n        monaco.editor.defineTheme(\"vs-dark-custom\", {\n            base: \"vs-dark\",\n            inherit: true,\n            rules: [],\n            colors: {\n                \"editor.background\": \"#1e1e1e\",\n                \"editor.foreground\": \"#d4d4d4\",\n                \"editorLineNumber.foreground\": \"#858585\",\n                \"editor.selectionBackground\": \"#264f78\",\n                \"editor.inactiveSelectionBackground\": \"#3a3d41\"\n            }\n        });\n        monaco.editor.setTheme(\"vs-dark-custom\");\n    };\n    const handleEditorChange = (value)=>{\n        if (value !== undefined && activeFile) {\n            setFiles((prev)=>({\n                    ...prev,\n                    [activeFile]: value\n                }));\n        }\n    };\n    const handleFileClick = (fileName)=>{\n        // Only open files that exist in our files object\n        if (files[fileName]) {\n            setActiveFile(fileName);\n            if (!openFiles.includes(fileName)) {\n                setOpenFiles([\n                    ...openFiles,\n                    fileName\n                ]);\n            }\n        }\n    };\n    const closeFile = (fileName)=>{\n        const newOpenFiles = openFiles.filter((f)=>f !== fileName);\n        setOpenFiles(newOpenFiles);\n        if (activeFile === fileName && newOpenFiles.length > 0) {\n            setActiveFile(newOpenFiles[newOpenFiles.length - 1]);\n        } else if (newOpenFiles.length === 0) {\n            setActiveFile(\"\");\n        }\n    };\n    const getFileLanguage = (fileName)=>{\n        const ext = fileName.split(\".\").pop();\n        switch(ext){\n            case \"tsx\":\n            case \"ts\":\n                return \"typescript\";\n            case \"jsx\":\n            case \"js\":\n                return \"javascript\";\n            case \"css\":\n                return \"css\";\n            case \"json\":\n                return \"json\";\n            case \"md\":\n                return \"markdown\";\n            case \"html\":\n                return \"html\";\n            default:\n                return \"plaintext\";\n        }\n    };\n    // File Management Functions\n    const createNewFile = ()=>{\n        if (newFileName.trim()) {\n            const language = getFileLanguage(newFileName);\n            const template = getFileTemplate(language, newFileName);\n            setFiles((prev)=>({\n                    ...prev,\n                    [newFileName]: template\n                }));\n            setActiveFile(newFileName);\n            if (!openFiles.includes(newFileName)) {\n                setOpenFiles([\n                    ...openFiles,\n                    newFileName\n                ]);\n            }\n            setNewFileName(\"\");\n            setShowNewFileDialog(false);\n        }\n    };\n    const getFileTemplate = (language, fileName)=>{\n        switch(language){\n            case \"typescript\":\n                if (fileName.endsWith(\".tsx\")) {\n                    return \"import React from 'react';\\n\\ninterface \".concat(fileName.replace(\".tsx\", \"\"), \"Props {\\n  // Define your props here\\n}\\n\\nconst \").concat(fileName.replace(\".tsx\", \"\"), \": React.FC<\").concat(fileName.replace(\".tsx\", \"\"), \"Props> = () => {\\n  return (\\n    <div>\\n      <h1>Hello from \").concat(fileName.replace(\".tsx\", \"\"), \"</h1>\\n    </div>\\n  );\\n};\\n\\nexport default \").concat(fileName.replace(\".tsx\", \"\"), \";\");\n                }\n                return \"// \".concat(fileName, \"\\nexport interface Example {\\n  id: number;\\n  name: string;\\n}\\n\\nexport const exampleFunction = (): Example => {\\n  return {\\n    id: 1,\\n    name: 'Example'\\n  };\\n};\");\n            case \"javascript\":\n                return \"// \".concat(fileName, \"\\nfunction exampleFunction() {\\n  console.log('Hello from \").concat(fileName, \"');\\n}\\n\\nexport default exampleFunction;\");\n            case \"css\":\n                return \"/* \".concat(fileName, \" */\\n.container {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n\\n.title {\\n  font-size: 2rem;\\n  color: white;\\n  margin-bottom: 1rem;\\n}\");\n            case \"html\":\n                return '<!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>'.concat(fileName.replace(\".html\", \"\"), \"</title>\\n</head>\\n<body>\\n    <h1>Hello World</h1>\\n    <p>Welcome to \").concat(fileName, \"</p>\\n</body>\\n</html>\");\n            case \"json\":\n                return '{\\n  \"name\": \"'.concat(fileName.replace(\".json\", \"\"), '\",\\n  \"version\": \"1.0.0\",\\n  \"description\": \"Generated JSON file\"\\n}');\n            case \"markdown\":\n                return \"# \".concat(fileName.replace(\".md\", \"\"), \"\\n\\nThis is a new markdown file.\\n\\n## Features\\n\\n- Feature 1\\n- Feature 2\\n- Feature 3\\n\\n## Usage\\n\\n```javascript\\nconsole.log('Hello World');\\n```\");\n            default:\n                return \"// \".concat(fileName, \"\\n// Start coding here...\");\n        }\n    };\n    // Project Management Functions\n    const createNewProject = ()=>{\n        if (newProjectName.trim()) {\n            const projectFiles = {\n                [\"\".concat(newProjectName, \"/src/App.tsx\")]: \"import React from 'react';\\nimport './App.css';\\n\\nfunction App() {\\n  return (\\n    <div className=\\\"App\\\">\\n      <header className=\\\"App-header\\\">\\n        <h1>Welcome to \".concat(newProjectName, \"</h1>\\n        <p>Your new React project is ready!</p>\\n      </header>\\n    </div>\\n  );\\n}\\n\\nexport default App;\"),\n                [\"\".concat(newProjectName, \"/src/App.css\")]: \".App {\\n  text-align: center;\\n}\\n\\n.App-header {\\n  background-color: #282c34;\\n  padding: 20px;\\n  color: white;\\n  min-height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: calc(10px + 2vmin);\\n}\",\n                [\"\".concat(newProjectName, \"/package.json\")]: '{\\n  \"name\": \"'.concat(newProjectName.toLowerCase(), '\",\\n  \"version\": \"0.1.0\",\\n  \"private\": true,\\n  \"dependencies\": {\\n    \"react\": \"^18.2.0\",\\n    \"react-dom\": \"^18.2.0\",\\n    \"typescript\": \"^4.9.5\"\\n  },\\n  \"scripts\": {\\n    \"start\": \"react-scripts start\",\\n    \"build\": \"react-scripts build\",\\n    \"test\": \"react-scripts test\",\\n    \"eject\": \"react-scripts eject\"\\n  }\\n}'),\n                [\"\".concat(newProjectName, \"/README.md\")]: \"# \".concat(newProjectName, \"\\n\\nThis project was created with React IDE.\\n\\n## Available Scripts\\n\\n- `npm start` - Runs the app in development mode\\n- `npm build` - Builds the app for production\\n- `npm test` - Launches the test runner\\n\\n## Getting Started\\n\\n1. Install dependencies: `npm install`\\n2. Start the development server: `npm start`\\n3. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.\\n\")\n            };\n            setFiles((prev)=>({\n                    ...prev,\n                    ...projectFiles\n                }));\n            setActiveFile(\"\".concat(newProjectName, \"/src/App.tsx\"));\n            setOpenFiles([\n                \"\".concat(newProjectName, \"/src/App.tsx\")\n            ]);\n            setNewProjectName(\"\");\n            setShowNewProjectDialog(false);\n        }\n    };\n    // Terminal Management Functions\n    const createNewTerminal = ()=>{\n        const newTerminalId = \"terminal-\".concat(Date.now());\n        const newTerminal = {\n            id: newTerminalId,\n            name: \"Terminal \".concat(terminals.length + 1),\n            output: [\n                \"Welcome to Terminal \".concat(terminals.length + 1),\n                \"$ \"\n            ]\n        };\n        setTerminals([\n            ...terminals,\n            newTerminal\n        ]);\n        setActiveTerminal(newTerminalId);\n    };\n    const executeCommand = (command)=>{\n        const currentTerminal = terminals.find((t)=>t.id === activeTerminal);\n        if (!currentTerminal) return;\n        const updatedTerminals = terminals.map((terminal)=>{\n            if (terminal.id === activeTerminal) {\n                const newOutput = [\n                    ...terminal.output\n                ];\n                newOutput[newOutput.length - 1] = \"$ \".concat(command);\n                // Simulate command execution\n                const response = simulateCommand(command);\n                newOutput.push(...response);\n                newOutput.push(\"$ \");\n                return {\n                    ...terminal,\n                    output: newOutput\n                };\n            }\n            return terminal;\n        });\n        setTerminals(updatedTerminals);\n        setTerminalInput(\"\");\n    };\n    const simulateCommand = (command)=>{\n        const cmd = command.toLowerCase().trim();\n        if (cmd === \"ls\" || cmd === \"dir\") {\n            return [\n                \"App.tsx\",\n                \"App.css\",\n                \"package.json\",\n                \"README.md\"\n            ];\n        } else if (cmd === \"pwd\") {\n            return [\n                \"/workspace/my-project\"\n            ];\n        } else if (cmd.startsWith(\"npm install\")) {\n            return [\n                \"Installing dependencies...\",\n                \"✓ Dependencies installed successfully\"\n            ];\n        } else if (cmd === \"npm start\") {\n            return [\n                \"Starting development server...\",\n                \"Local: http://localhost:3000\",\n                \"webpack compiled successfully\"\n            ];\n        } else if (cmd === \"npm build\") {\n            return [\n                \"Building for production...\",\n                \"✓ Build completed successfully\"\n            ];\n        } else if (cmd === \"git status\") {\n            return [\n                \"On branch main\",\n                \"Your branch is up to date with origin/main\",\n                \"nothing to commit, working tree clean\"\n            ];\n        } else if (cmd.startsWith(\"git add\")) {\n            return [\n                \"Files staged for commit\"\n            ];\n        } else if (cmd.startsWith(\"git commit\")) {\n            return [\n                \"[main abc123] Your commit message\",\n                \"2 files changed, 10 insertions(+), 5 deletions(-)\"\n            ];\n        } else if (cmd === \"clear\" || cmd === \"cls\") {\n            return [\n                \"\"\n            ];\n        } else if (cmd === \"help\") {\n            return [\n                \"Available commands:\",\n                \"  ls/dir     - List files\",\n                \"  pwd        - Show current directory\",\n                \"  npm install - Install dependencies\",\n                \"  npm start  - Start development server\",\n                \"  npm build  - Build for production\",\n                \"  git status - Show git status\",\n                \"  git add    - Stage files\",\n                \"  git commit - Commit changes\",\n                \"  clear/cls  - Clear terminal\",\n                \"  help       - Show this help\"\n            ];\n        } else if (cmd === \"\") {\n            return [];\n        } else {\n            return [\n                \"Command not found: \".concat(command),\n                'Type \"help\" for available commands'\n            ];\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-panel-bg rounded-xl overflow-hidden flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-64 bg-gray-900/50 border-r border-gray-700/50 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex border-b border-gray-700/50\",\n                        children: [\n                            {\n                                id: \"files\",\n                                icon: \"\\uD83D\\uDCC1\",\n                                label: \"Files\"\n                            },\n                            {\n                                id: \"git\",\n                                icon: \"\\uD83D\\uDD00\",\n                                label: \"Git\"\n                            },\n                            {\n                                id: \"extensions\",\n                                icon: \"\\uD83E\\uDDE9\",\n                                label: \"Extensions\"\n                            }\n                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSidebarTab(tab.id),\n                                className: \"flex-1 p-3 text-sm font-medium transition-colors duration-200 \".concat(sidebarTab === tab.id ? \"bg-blue-500/20 text-blue-400 border-b-2 border-blue-400\" : \"text-gray-400 hover:text-white hover:bg-gray-800/60\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-2\",\n                                        children: tab.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 580,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    tab.label\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 571,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 565,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: [\n                            sidebarTab === \"files\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-gray-400 text-xs uppercase font-semibold mb-2 px-2\",\n                                        children: \"Explorer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 590,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    fileTree.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileTreeNode, {\n                                            item: item,\n                                            level: 0,\n                                            onFileClick: handleFileClick\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 592,\n                                            columnNumber: 17\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 13\n                            }, undefined),\n                            sidebarTab === \"git\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-white font-semibold mb-4\",\n                                        children: \"Source Control\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-green-400 text-sm\",\n                                                children: \"✓ 3 files staged\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-yellow-400 text-sm\",\n                                                children: \"⚠ 2 files modified\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-red-400 text-sm\",\n                                                children: \"✗ 1 file deleted\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 13\n                            }, undefined),\n                            sidebarTab === \"extensions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-white font-semibold mb-4\",\n                                        children: \"Extensions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 615,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-blue-500 rounded flex items-center justify-center text-xs\",\n                                                        children: \"TS\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 618,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white text-sm\",\n                                                                children: \"TypeScript\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                                lineNumber: 620,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-400 text-xs\",\n                                                                children: \"Installed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                                lineNumber: 621,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-green-500 rounded flex items-center justify-center text-xs\",\n                                                        children: \"ES\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white text-sm\",\n                                                                children: \"ESLint\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                                lineNumber: 627,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-400 text-xs\",\n                                                                children: \"Installed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                                lineNumber: 628,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 626,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 616,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 614,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 587,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 563,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-800/40 border-b border-gray-700/50\",\n                        children: openFiles.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 px-4 py-2 border-r border-gray-700/50 cursor-pointer \".concat(activeFile === file ? \"bg-panel-bg text-white\" : \"text-gray-400 hover:text-white hover:bg-gray-800/60\"),\n                                onClick: ()=>setActiveFile(file),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: file\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 651,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            closeFile(file);\n                                        },\n                                        className: \"text-gray-500 hover:text-white\",\n                                        children: \"\\xd7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, file, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 642,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 640,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: activeFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            height: \"100%\",\n                            language: getFileLanguage(activeFile),\n                            value: files[activeFile] || \"\",\n                            onChange: handleEditorChange,\n                            theme: \"vs-dark\",\n                            options: {\n                                fontSize: 14,\n                                fontFamily: \"Fira Code, Monaco, Consolas, monospace\",\n                                minimap: {\n                                    enabled: true\n                                },\n                                scrollBeyondLastLine: false,\n                                automaticLayout: true,\n                                tabSize: 2,\n                                insertSpaces: true,\n                                wordWrap: \"on\",\n                                lineNumbers: \"on\",\n                                renderWhitespace: \"selection\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                            lineNumber: 668,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-full bg-gray-900/30\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-6xl mb-4\",\n                                        children: \"\\uD83D\\uDCBB\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 690,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-white text-xl mb-2\",\n                                        children: \"Welcome to the IDE\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 691,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Select a file from the explorer to start coding\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 689,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                            lineNumber: 688,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 666,\n                        columnNumber: 9\n                    }, undefined),\n                    showTerminal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-48 bg-black border-t border-gray-700/50 flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between px-4 py-2 bg-gray-800/60 border-b border-gray-700/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-semibold text-sm\",\n                                                children: \"Terminal\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 703,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400 text-xs\",\n                                                children: \"bash\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 704,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 702,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowTerminal(false),\n                                            className: \"text-gray-400 hover:text-white text-sm px-2 py-1 rounded hover:bg-gray-700/50\",\n                                            children: \"\\xd7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 707,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 706,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 701,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-4 overflow-y-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-green-400 font-mono text-sm space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-400\",\n                                            children: \"Welcome to React IDE Terminal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 717,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"$ npm install\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 718,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-300\",\n                                            children: \"✓ Dependencies installed successfully\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 719,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"$ npm start\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 720,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-300\",\n                                            children: \"Starting development server...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 721,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-blue-400\",\n                                            children: \"Local:    http://localhost:3000\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 722,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-blue-400\",\n                                            children: \"Network:  http://*************:3000\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-300\",\n                                            children: \"webpack compiled successfully\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 724,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400\",\n                                                    children: \"$ \"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-transparent border-none outline-none text-green-400 ml-1 animate-pulse\",\n                                                    children: \"|\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                    lineNumber: 727,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 725,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                    lineNumber: 716,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 715,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 700,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 638,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-80 bg-gray-900/50 border-l border-gray-700/50 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-white font-semibold mb-4\",\n                        children: \"\\uD83E\\uDD16 AI Assistant\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 737,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800/40 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-400 text-sm font-medium mb-1\",\n                                        children: \"Code Suggestion\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 740,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Consider adding error handling to your React component.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 741,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 739,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800/40 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-green-400 text-sm font-medium mb-1\",\n                                        children: \"Performance Tip\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Use React.memo() to optimize component re-renders.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 747,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 745,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 738,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowTerminal(!showTerminal),\n                            className: \"w-full bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 py-2 px-4 rounded-lg transition-colors duration-200\",\n                            children: showTerminal ? \"Hide Terminal\" : \"Show Terminal\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                            lineNumber: 754,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 753,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 736,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n        lineNumber: 561,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CoderView, \"4uPe9AN0uU32KrKp+cFQwxJnCow=\");\n_c1 = CoderView;\nvar _c, _c1;\n$RefreshReg$(_c, \"FileTreeNode\");\n$RefreshReg$(_c1, \"CoderView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/Coder/CoderView.tsx\n"));

/***/ })

});
import type {Perseus<PERSON><PERSON><PERSON>} from "@khanacademy/perseus-core";

export const question1: PerseusRenderer = {
    content: "[[\u2603 cs-program 1]]\n\n",
    images: {},
    widgets: {
        "cs-program 1": {
            graded: true,
            version: {major: 0, minor: 0},
            static: false,
            type: "cs-program",
            options: {
                settings: [
                    {name: "", value: ""},
                    {name: "", value: ""},
                ],
                height: 540,
                programID: "6293105639817216",
                static: false,
                showButtons: false,
                showEditor: false,
            },
            alignment: "block",
        },
    },
};

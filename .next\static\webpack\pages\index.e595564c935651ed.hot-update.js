"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "__barrel_optimize__?names=AcademicCapIcon,ChartBarIcon,FireIcon,UserIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/solid/esm/index.js":
/*!***********************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=AcademicCapIcon,ChartBarIcon,FireIcon,UserIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/solid/esm/index.js ***!
  \***********************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AcademicCapIcon: function() { return /* reexport safe */ _AcademicCapIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   ChartBarIcon: function() { return /* reexport safe */ _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   FireIcon: function() { return /* reexport safe */ _FireIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; },\n/* harmony export */   UserIcon: function() { return /* reexport safe */ _UserIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _AcademicCapIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AcademicCapIcon.js */ \"./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/solid/esm/AcademicCapIcon.js\");\n/* harmony import */ var _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ChartBarIcon.js */ \"./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/solid/esm/ChartBarIcon.js\");\n/* harmony import */ var _FireIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./FireIcon.js */ \"./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/solid/esm/FireIcon.js\");\n/* harmony import */ var _UserIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./UserIcon.js */ \"./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/solid/esm/UserIcon.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BY2FkZW1pY0NhcEljb24sQ2hhcnRCYXJJY29uLEZpcmVJY29uLFVzZXJJY29uIT0hLi9ub2RlX21vZHVsZXMvLnBucG0vQGhlcm9pY29ucytyZWFjdEAyLjIuMF9yZWFjdEAxOC4yLjAvbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvc29saWQvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUNpRTtBQUNOO0FBQ1IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0BoZXJvaWNvbnMrcmVhY3RAMi4yLjBfcmVhY3RAMTguMi4wL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L3NvbGlkL2VzbS9pbmRleC5qcz8yYTBlIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBBY2FkZW1pY0NhcEljb24gfSBmcm9tIFwiLi9BY2FkZW1pY0NhcEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGFydEJhckljb24gfSBmcm9tIFwiLi9DaGFydEJhckljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBGaXJlSWNvbiB9IGZyb20gXCIuL0ZpcmVJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVXNlckljb24gfSBmcm9tIFwiLi9Vc2VySWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=AcademicCapIcon,ChartBarIcon,FireIcon,UserIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/solid/esm/index.js\n"));

/***/ }),

/***/ "./src/views/School/departments/StudentDashboard.tsx":
/*!***********************************************************!*\
  !*** ./src/views/School/departments/StudentDashboard.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StudentDashboard: function() { return /* binding */ StudentDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../components/Card */ \"./src/components/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_ChartBarIcon_FireIcon_UserIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,ChartBarIcon,FireIcon,UserIcon!=!@heroicons/react/24/solid */ \"__barrel_optimize__?names=AcademicCapIcon,ChartBarIcon,FireIcon,UserIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/solid/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst StudentDashboard = (param)=>{\n    let { activeSubSection } = param;\n    _s();\n    const [studentData, setStudentData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchStudentData = async ()=>{\n            try {\n                setLoading(true);\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                const mockData = {\n                    profile: {\n                        name: \"Alex Chen\",\n                        grade: \"Grade 11\",\n                        gpa: 3.94,\n                        rank: 12,\n                        totalStudents: 342,\n                        aiMentor: \"Socrates AI\",\n                        learningStyle: \"Visual-Kinesthetic\",\n                        emotionalState: \"Motivated\",\n                        neuralPathways: 847\n                    },\n                    courses: [\n                        {\n                            name: \"Quantum Physics VR\",\n                            progress: 87,\n                            grade: \"A-\",\n                            nextClass: \"Hologram Lab\",\n                            color: \"bg-blue-500/20 text-blue-400\",\n                            aiTutor: \"Einstein AI\"\n                        },\n                        {\n                            name: \"Neural Network Design\",\n                            progress: 92,\n                            grade: \"A\",\n                            nextClass: \"AI Ethics\",\n                            color: \"bg-green-500/20 text-green-400\",\n                            aiTutor: \"Turing AI\"\n                        },\n                        {\n                            name: \"Bioengineering Lab\",\n                            progress: 78,\n                            grade: \"B+\",\n                            nextClass: \"Gene Editing\",\n                            color: \"bg-purple-500/20 text-purple-400\",\n                            aiTutor: \"Darwin AI\"\n                        },\n                        {\n                            name: \"Space Colonization\",\n                            progress: 95,\n                            grade: \"A+\",\n                            nextClass: \"Mars Simulation\",\n                            color: \"bg-pink-500/20 text-pink-400\",\n                            aiTutor: \"Hawking AI\"\n                        }\n                    ],\n                    achievements: [\n                        {\n                            title: \"Quantum Entanglement Master\",\n                            icon: \"⚛️\",\n                            rarity: \"Legendary\",\n                            date: \"2024-03-15\"\n                        },\n                        {\n                            title: \"AI Ethics Champion\",\n                            icon: \"\\uD83E\\uDD16\",\n                            rarity: \"Epic\",\n                            date: \"2024-03-10\"\n                        },\n                        {\n                            title: \"Gene Editing Pioneer\",\n                            icon: \"\\uD83E\\uDDEC\",\n                            rarity: \"Rare\",\n                            date: \"2024-03-05\"\n                        },\n                        {\n                            title: \"Mars Colony Architect\",\n                            icon: \"\\uD83D\\uDE80\",\n                            rarity: \"Legendary\",\n                            date: \"2024-03-01\"\n                        }\n                    ],\n                    aiInsights: [\n                        {\n                            type: \"Learning Pattern\",\n                            insight: \"Peak performance during morning VR sessions\",\n                            confidence: 94\n                        },\n                        {\n                            type: \"Emotional State\",\n                            insight: \"Stress levels elevated before quantum physics exams\",\n                            confidence: 87\n                        },\n                        {\n                            type: \"Career Path\",\n                            insight: \"Strong aptitude for quantum computing research\",\n                            confidence: 96\n                        }\n                    ],\n                    upcomingEvents: [\n                        {\n                            title: \"Holographic Presentation\",\n                            course: \"Quantum Physics VR\",\n                            date: \"March 18\",\n                            type: \"Assessment\"\n                        },\n                        {\n                            title: \"AI Ethics Debate\",\n                            course: \"Neural Network Design\",\n                            date: \"March 20\",\n                            type: \"Discussion\"\n                        },\n                        {\n                            title: \"Mars Colony Design Challenge\",\n                            course: \"Space Colonization\",\n                            date: \"March 25\",\n                            type: \"Project\"\n                        }\n                    ]\n                };\n                setStudentData(mockData);\n            } catch (error) {\n                console.error(\"Failed to fetch student data:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchStudentData();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-cyan-400\",\n                children: \"Loading Student Portal...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                lineNumber: 78,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, undefined);\n    }\n    const renderContent = ()=>{\n        switch(activeSubSection){\n            case \"dashboard\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"\\uD83C\\uDF93 My AI-Enhanced Profile\",\n                            className: \"lg:col-span-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4 p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_ChartBarIcon_FireIcon_UserIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.AcademicCapIcon, {\n                                                        className: \"w-6 h-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                        lineNumber: 93,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: studentData.profile.gpa\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Neural GPA\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-green-500 to-cyan-600 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_ChartBarIcon_FireIcon_UserIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.ChartBarIcon, {\n                                                        className: \"w-6 h-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: [\n                                                        \"#\",\n                                                        studentData.profile.rank\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Global Rank\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_ChartBarIcon_FireIcon_UserIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.UserIcon, {\n                                                        className: \"w-6 h-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                        lineNumber: 107,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: studentData.profile.neuralPathways\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Neural Pathways\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_ChartBarIcon_FireIcon_UserIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.FireIcon, {\n                                                        className: \"w-6 h-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: studentData.profile.emotionalState\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"AI Mood Analysis\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-700/50 pt-4 mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"AI Mentor:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-cyan-400\",\n                                                children: [\n                                                    \"\\uD83E\\uDD16 \",\n                                                    studentData.profile.aiMentor\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Learning Style:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-purple-400\",\n                                                children: [\n                                                    \"\\uD83E\\uDDE0 \",\n                                                    studentData.profile.learningStyle\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"\\uD83D\\uDE80 My Quantum Courses\",\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 p-4\",\n                                children: studentData.courses.map((course, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg \".concat(course.color.split(\" \")[0], \" border border-gray-600/30\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold \".concat(course.color.split(\" \").slice(1).join(\" \")),\n                                                                children: course.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: [\n                                                                    \"Next: \",\n                                                                    course.nextClass\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                                lineNumber: 138,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-cyan-400 mt-1\",\n                                                                children: [\n                                                                    \"\\uD83E\\uDD16 \",\n                                                                    course.aiTutor\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg font-bold text-white\",\n                                                                children: course.grade\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: [\n                                                                    course.progress,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                                lineNumber: 143,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-gray-700 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 rounded-full \".concat(course.color.split(\" \")[0].replace(\"bg-\", \"bg-gradient-to-r from-\").replace(\"/20\", \"/60 to-\").replace(\"500\", \"400\")),\n                                                    style: {\n                                                        width: \"\".concat(course.progress, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"\\uD83C\\uDFC6 Achievement Gallery\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 p-4\",\n                                children: studentData.achievements.map((achievement, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg border \".concat(achievement.rarity === \"Legendary\" ? \"bg-gradient-to-r from-yellow-900/20 to-orange-900/20 border-yellow-500/30\" : achievement.rarity === \"Epic\" ? \"bg-gradient-to-r from-purple-900/20 to-pink-900/20 border-purple-500/30\" : \"bg-gradient-to-r from-blue-900/20 to-cyan-900/20 border-blue-500/30\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: achievement.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-white\",\n                                                            children: achievement.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs \".concat(achievement.rarity === \"Legendary\" ? \"bg-yellow-500/20 text-yellow-400\" : achievement.rarity === \"Epic\" ? \"bg-purple-500/20 text-purple-400\" : \"bg-blue-500/20 text-blue-400\"),\n                                                                    children: achievement.rarity\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                                    lineNumber: 171,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-400\",\n                                                                    children: achievement.date\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                                    lineNumber: 178,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Student Portal\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Welcome to the Future of Learning\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Select a section from the navigation to explore your AI-enhanced education journey.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 h-full\",\n        children: renderContent()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\StudentDashboard.tsx\",\n        lineNumber: 202,\n        columnNumber: 5\n    }, undefined);\n};\n_s(StudentDashboard, \"UeQ5NwJictIZdTJca2hamRjSxb8=\");\n_c = StudentDashboard;\nvar _c;\n$RefreshReg$(_c, \"StudentDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/School/departments/StudentDashboard.tsx\n"));

/***/ }),

/***/ "./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/solid/esm/FireIcon.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/solid/esm/FireIcon.js ***!
  \***********************************************************************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n\nfunction FireIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 24 24\",\n        fill: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        fillRule: \"evenodd\",\n        d: \"M12.963 2.286a.75.75 0 0 0-1.071-.136 9.742 9.742 0 0 0-3.539 6.176 7.547 7.547 0 0 1-1.705-1.715.75.75 0 0 0-1.152-.082A9 9 0 1 0 15.68 4.534a7.46 7.46 0 0 1-2.717-2.248ZM15.75 14.25a3.75 3.75 0 1 1-7.313-1.172c.628.465 1.35.81 2.133 1a5.99 5.99 0 0 1 1.925-3.546 3.75 3.75 0 0 1 3.255 3.718Z\",\n        clipRule: \"evenodd\"\n    }));\n}\n_c = FireIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(FireIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"FireIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/solid/esm/FireIcon.js\n"));

/***/ })

});
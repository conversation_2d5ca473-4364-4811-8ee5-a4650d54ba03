"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/books";
exports.ids = ["pages/api/books"];
exports.modules = {

/***/ "mysql2/promise":
/*!*********************************!*\
  !*** external "mysql2/promise" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("mysql2/promise");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fbooks&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cbooks.ts&middlewareConfigBase64=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fbooks&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cbooks.ts&middlewareConfigBase64=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_books_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\books.ts */ \"(api)/./pages/api/books.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_books_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_books_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/books\",\n        pathname: \"/api/books\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_books_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fbooks&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cbooks.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./backend/lib/database.ts":
/*!*********************************!*\
  !*** ./backend/lib/database.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   executeQuery: () => (/* binding */ executeQuery),\n/* harmony export */   executeQuerySingle: () => (/* binding */ executeQuerySingle),\n/* harmony export */   getConnection: () => (/* binding */ getConnection),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase),\n/* harmony export */   seedDatabase: () => (/* binding */ seedDatabase)\n/* harmony export */ });\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mysql2/promise */ \"mysql2/promise\");\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mysql2_promise__WEBPACK_IMPORTED_MODULE_0__);\n\nconst config = {\n    host: process.env.HOSTINGER_DB_HOST || process.env.DB_HOST || \"localhost\",\n    port: parseInt(process.env.HOSTINGER_DB_PORT || process.env.DB_PORT || \"3306\"),\n    user: process.env.HOSTINGER_DB_USER || process.env.DB_USER || \"\",\n    password: process.env.HOSTINGER_DB_PASSWORD || process.env.DB_PASSWORD || \"\",\n    database: process.env.HOSTINGER_DB_NAME || process.env.DB_NAME || \"\",\n    ssl: false\n};\nlet connection = null;\nasync function getConnection() {\n    if (!connection) {\n        try {\n            connection = await mysql2_promise__WEBPACK_IMPORTED_MODULE_0___default().createConnection(config);\n            console.log(\"✅ Database connected successfully\");\n        } catch (error) {\n            console.error(\"❌ Database connection failed:\", error);\n            throw error;\n        }\n    }\n    return connection;\n}\nasync function executeQuery(query, params = []) {\n    try {\n        const conn = await getConnection();\n        const [rows] = await conn.execute(query, params);\n        return rows;\n    } catch (error) {\n        console.error(\"Database query error:\", error);\n        throw error;\n    }\n}\nasync function executeQuerySingle(query, params = []) {\n    const results = await executeQuery(query, params);\n    return results.length > 0 ? results[0] : null;\n}\n// Initialize database tables if they don't exist\nasync function initializeDatabase() {\n    try {\n        const conn = await getConnection();\n        // Create analytics table\n        await conn.execute(`\n      CREATE TABLE IF NOT EXISTS analytics (\n        id INT AUTO_INCREMENT PRIMARY KEY,\n        metric_name VARCHAR(100) NOT NULL,\n        value DECIMAL(10,2) NOT NULL,\n        unit VARCHAR(20),\n        icon VARCHAR(50),\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n      )\n    `);\n        // Create system_diagnostics table\n        await conn.execute(`\n      CREATE TABLE IF NOT EXISTS system_diagnostics (\n        id INT AUTO_INCREMENT PRIMARY KEY,\n        cpu_load DECIMAL(5,2) NOT NULL,\n        ram_usage DECIMAL(5,2) NOT NULL,\n        disk_usage DECIMAL(5,2) DEFAULT 0,\n        network_status VARCHAR(20) DEFAULT 'active',\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create school_data table\n        await conn.execute(`\n      CREATE TABLE IF NOT EXISTS school_data (\n        id INT AUTO_INCREMENT PRIMARY KEY,\n        gpa DECIMAL(3,2) NOT NULL,\n        credits INT NOT NULL,\n        attendance DECIMAL(5,2) NOT NULL,\n        assignments_completed INT DEFAULT 0,\n        assignments_total INT DEFAULT 0,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n      )\n    `);\n        // Create market_data table\n        await conn.execute(`\n      CREATE TABLE IF NOT EXISTS market_data (\n        id INT AUTO_INCREMENT PRIMARY KEY,\n        featured_product_name VARCHAR(200) NOT NULL,\n        featured_product_price DECIMAL(10,2) NOT NULL,\n        featured_product_image VARCHAR(500),\n        top_mover_symbol VARCHAR(10) NOT NULL,\n        top_mover_change DECIMAL(10,2) NOT NULL,\n        top_mover_is_up BOOLEAN NOT NULL,\n        volume BIGINT DEFAULT 0,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n      )\n    `);\n        // Create books table\n        await conn.execute(`\n      CREATE TABLE IF NOT EXISTS books (\n        id INT AUTO_INCREMENT PRIMARY KEY,\n        title VARCHAR(200) NOT NULL,\n        author VARCHAR(100) NOT NULL,\n        price DECIMAL(8,2) NOT NULL,\n        cover_image VARCHAR(500),\n        genre VARCHAR(50),\n        rating DECIMAL(2,1) DEFAULT 0,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create notifications table\n        await conn.execute(`\n      CREATE TABLE IF NOT EXISTS notifications (\n        id INT AUTO_INCREMENT PRIMARY KEY,\n        title VARCHAR(200) NOT NULL,\n        message TEXT NOT NULL,\n        type VARCHAR(50) DEFAULT 'info',\n        is_read BOOLEAN DEFAULT FALSE,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        console.log(\"✅ Database tables initialized successfully\");\n    } catch (error) {\n        console.error(\"❌ Database initialization failed:\", error);\n        throw error;\n    }\n}\n// Seed initial data\nasync function seedDatabase() {\n    try {\n        // Check if data already exists\n        const analyticsCount = await executeQuerySingle(\"SELECT COUNT(*) as count FROM analytics\");\n        if (analyticsCount && analyticsCount.count === 0) {\n            // Seed analytics data\n            await executeQuery(`\n        INSERT INTO analytics (metric_name, value, unit, icon) VALUES\n        ('Revenue', 125000.50, 'USD', 'CurrencyDollarIcon'),\n        ('Users', 15420, 'count', 'UsersIcon'),\n        ('Growth', 23.5, '%', 'TrendingUpIcon'),\n        ('Conversion', 4.2, '%', 'ChartBarIcon')\n      `);\n            // Seed system diagnostics\n            await executeQuery(`\n        INSERT INTO system_diagnostics (cpu_load, ram_usage, disk_usage) VALUES\n        (45.2, 67.8, 23.1)\n      `);\n            // Seed school data\n            await executeQuery(`\n        INSERT INTO school_data (gpa, credits, attendance, assignments_completed, assignments_total) VALUES\n        (3.85, 120, 94.5, 28, 32)\n      `);\n            // Seed market data\n            await executeQuery(`\n        INSERT INTO market_data (featured_product_name, featured_product_price, top_mover_symbol, top_mover_change, top_mover_is_up, volume) VALUES\n        ('Quantum Computing Module', 2499.99, 'QTECH', 15.75, TRUE, 1250000)\n      `);\n            // Seed books\n            await executeQuery(`\n        INSERT INTO books (title, author, price, genre, rating) VALUES\n        ('Neural Networks Fundamentals', 'Dr. Sarah Chen', 89.99, 'Technology', 4.8),\n        ('Quantum Physics Made Simple', 'Prof. Michael Torres', 75.50, 'Science', 4.6),\n        ('AI Ethics in Practice', 'Dr. Aisha Patel', 65.00, 'Technology', 4.9),\n        ('Future of Computing', 'James Rodriguez', 55.99, 'Technology', 4.4),\n        ('Data Science Handbook', 'Lisa Wang', 79.99, 'Technology', 4.7),\n        ('Cybersecurity Essentials', 'Mark Thompson', 69.99, 'Technology', 4.5)\n      `);\n            // Seed notifications\n            await executeQuery(`\n        INSERT INTO notifications (title, message, type) VALUES\n        ('System Update Available', 'A new system update is ready for installation. Please schedule maintenance window.', 'info'),\n        ('Security Alert', 'Unusual login activity detected from new location. Please verify your account.', 'warning'),\n        ('Backup Completed', 'Daily backup process completed successfully. All data is secure.', 'success'),\n        ('Performance Alert', 'CPU usage has been above 80% for the last 30 minutes. Consider scaling resources.', 'warning'),\n        ('New Feature Released', 'Advanced analytics dashboard is now available in your control panel.', 'info')\n      `);\n            console.log(\"✅ Database seeded with initial data\");\n        }\n    } catch (error) {\n        console.error(\"❌ Database seeding failed:\", error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./backend/lib/database.ts\n");

/***/ }),

/***/ "(api)/./pages/api/books.ts":
/*!****************************!*\
  !*** ./pages/api/books.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _backend_lib_database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../backend/lib/database */ \"(api)/./backend/lib/database.ts\");\n\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        res.setHeader(\"Allow\", [\n            \"GET\"\n        ]);\n        return res.status(405).end(`Method ${req.method} Not Allowed`);\n    }\n    try {\n        // Initialize database if needed\n        await (0,_backend_lib_database__WEBPACK_IMPORTED_MODULE_0__.initializeDatabase)();\n        await (0,_backend_lib_database__WEBPACK_IMPORTED_MODULE_0__.seedDatabase)();\n        // Get books from database\n        const dbBooks = await (0,_backend_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(`\n      SELECT * FROM books\n      ORDER BY rating DESC\n      LIMIT 6\n    `);\n        if (dbBooks.length > 0) {\n            const books = dbBooks.map((book, index)=>({\n                    title: book.title,\n                    author: book.author,\n                    image: book.cover_image || `https://picsum.photos/seed/book${index}/300/400`\n                }));\n            res.status(200).json(books);\n        } else {\n            // Fallback data if no books exist\n            const fallbackBooks = [\n                {\n                    title: \"Neural Networks Fundamentals\",\n                    author: \"Dr. Sarah Chen\",\n                    image: \"https://picsum.photos/seed/book0/300/400\"\n                },\n                {\n                    title: \"Quantum Physics Made Simple\",\n                    author: \"Prof. Michael Torres\",\n                    image: \"https://picsum.photos/seed/book1/300/400\"\n                },\n                {\n                    title: \"AI Ethics in Practice\",\n                    author: \"Dr. Aisha Patel\",\n                    image: \"https://picsum.photos/seed/book2/300/400\"\n                },\n                {\n                    title: \"Future of Computing\",\n                    author: \"James Rodriguez\",\n                    image: \"https://picsum.photos/seed/book3/300/400\"\n                }\n            ];\n            res.status(200).json(fallbackBooks);\n        }\n    } catch (error) {\n        console.error(\"Books API error:\", error);\n        // Fallback data if database fails\n        const fallbackBooks = [\n            {\n                title: \"Neural Networks Fundamentals\",\n                author: \"Dr. Sarah Chen\",\n                image: \"https://picsum.photos/seed/book0/300/400\"\n            },\n            {\n                title: \"Quantum Physics Made Simple\",\n                author: \"Prof. Michael Torres\",\n                image: \"https://picsum.photos/seed/book1/300/400\"\n            },\n            {\n                title: \"AI Ethics in Practice\",\n                author: \"Dr. Aisha Patel\",\n                image: \"https://picsum.photos/seed/book2/300/400\"\n            },\n            {\n                title: \"Future of Computing\",\n                author: \"James Rodriguez\",\n                image: \"https://picsum.photos/seed/book3/300/400\"\n            }\n        ];\n        res.status(200).json(fallbackBooks);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/books.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0"], () => (__webpack_exec__("(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fbooks&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cbooks.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();
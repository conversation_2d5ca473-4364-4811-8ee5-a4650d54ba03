"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/views/School/departments/SchoolDashboard.tsx":
/*!**********************************************************!*\
  !*** ./src/views/School/departments/SchoolDashboard.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SchoolDashboard: function() { return /* binding */ SchoolDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../components/Card */ \"./src/components/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BuildingLibraryIcon,CalendarIcon,ChartBarIcon,UserGroupIcon!=!@heroicons/react/24/solid */ \"__barrel_optimize__?names=AcademicCapIcon,BuildingLibraryIcon,CalendarIcon,ChartBarIcon,UserGroupIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/solid/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst SchoolDashboard = (param)=>{\n    let { activeSubSection } = param;\n    _s();\n    const [schoolData, setSchoolData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSchoolData = async ()=>{\n            try {\n                setLoading(true);\n                const response = await fetch(\"/api/school/dashboard\");\n                if (!response.ok) {\n                    throw new Error(\"Failed to fetch school data\");\n                }\n                const schoolData = await response.json();\n                setSchoolData(schoolData);\n            } catch (error) {\n                console.error(\"Failed to fetch school data:\", error);\n                // Fallback to mock data if API fails\n                const mockData = {\n                    overview: {\n                        totalStudents: 1247,\n                        totalTeachers: 89,\n                        totalClasses: 156,\n                        averageGPA: 3.67,\n                        aiTutorSessions: 2847,\n                        virtualRealityClasses: 23,\n                        blockchainCertificates: 156,\n                        carbonFootprint: 2.3\n                    },\n                    departments: [\n                        {\n                            name: \"AI & Robotics\",\n                            students: 312,\n                            teachers: 18,\n                            color: \"bg-blue-500/20 text-blue-400\",\n                            innovation: \"Neural Networks Lab\"\n                        },\n                        {\n                            name: \"Quantum Computing\",\n                            students: 298,\n                            teachers: 15,\n                            color: \"bg-green-500/20 text-green-400\",\n                            innovation: \"Quantum Simulator\"\n                        },\n                        {\n                            name: \"Bioengineering\",\n                            students: 267,\n                            teachers: 12,\n                            color: \"bg-purple-500/20 text-purple-400\",\n                            innovation: \"Gene Editing Lab\"\n                        },\n                        {\n                            name: \"Space Sciences\",\n                            students: 189,\n                            teachers: 11,\n                            color: \"bg-pink-500/20 text-pink-400\",\n                            innovation: \"Mars Simulation\"\n                        }\n                    ],\n                    innovations: [\n                        {\n                            title: \"AI-Powered Personalized Learning\",\n                            status: \"Active\",\n                            impact: \"94% improvement\",\n                            icon: \"\\uD83E\\uDD16\"\n                        },\n                        {\n                            title: \"Holographic Classrooms\",\n                            status: \"Beta\",\n                            impact: \"87% engagement\",\n                            icon: \"\\uD83D\\uDD2E\"\n                        },\n                        {\n                            title: \"Blockchain Credentials\",\n                            status: \"Live\",\n                            impact: \"100% verified\",\n                            icon: \"⛓️\"\n                        },\n                        {\n                            title: \"Emotion Recognition System\",\n                            status: \"Testing\",\n                            impact: \"92% accuracy\",\n                            icon: \"\\uD83D\\uDE0A\"\n                        }\n                    ],\n                    recentEvents: [\n                        {\n                            title: \"Global Virtual Science Fair\",\n                            date: \"March 15\",\n                            type: \"Innovation\",\n                            participants: 47\n                        },\n                        {\n                            title: \"AI Ethics Symposium\",\n                            date: \"March 20\",\n                            type: \"Academic\",\n                            participants: 234\n                        },\n                        {\n                            title: \"Metaverse Graduation Ceremony\",\n                            date: \"March 25\",\n                            type: \"Celebration\",\n                            participants: 1200\n                        }\n                    ]\n                };\n                setSchoolData(mockData);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchSchoolData();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-cyan-400\",\n                children: \"Loading School Dashboard...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n            lineNumber: 76,\n            columnNumber: 7\n        }, undefined);\n    }\n    const renderContent = ()=>{\n        switch(activeSubSection){\n            case \"dashboard\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"\\uD83D\\uDE80 Future School Overview\",\n                            className: \"lg:col-span-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-4 p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.UserGroupIcon, {\n                                                    className: \"w-6 h-6 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: schoolData.overview.totalStudents\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Global Students\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.AcademicCapIcon, {\n                                                    className: \"w-6 h-6 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: schoolData.overview.totalTeachers\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"AI-Enhanced Teachers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.BuildingLibraryIcon, {\n                                                    className: \"w-6 h-6 text-purple-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: schoolData.overview.virtualRealityClasses\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"VR Classrooms\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.ChartBarIcon, {\n                                                    className: \"w-6 h-6 text-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: schoolData.overview.aiTutorSessions\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"AI Tutor Sessions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"\\uD83D\\uDD2C Innovation Hub\",\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 p-4\",\n                                children: schoolData.innovations.map((innovation, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg bg-gray-800/40 border border-gray-600/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg\",\n                                                                children: innovation.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 128,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-white\",\n                                                                children: innovation.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 129,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs \".concat(innovation.status === \"Active\" ? \"bg-green-500/20 text-green-400\" : innovation.status === \"Live\" ? \"bg-blue-500/20 text-blue-400\" : innovation.status === \"Beta\" ? \"bg-yellow-500/20 text-yellow-400\" : \"bg-purple-500/20 text-purple-400\"),\n                                                        children: innovation.status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: [\n                                                    \"Impact: \",\n                                                    innovation.impact\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"\\uD83C\\uDF1F Next-Gen Departments\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 p-4\",\n                                children: schoolData.departments.map((dept, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg \".concat(dept.color.split(\" \")[0], \" border border-gray-600/30\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold \".concat(dept.color.split(\" \").slice(1).join(\" \")),\n                                                            children: dept.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: [\n                                                                dept.students,\n                                                                \" students • \",\n                                                                dept.teachers,\n                                                                \" teachers\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-cyan-400 mt-1\",\n                                                            children: [\n                                                                \"\\uD83D\\uDE80 \",\n                                                                dept.innovation\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 rounded-lg \".concat(dept.color.split(\" \")[0], \" flex items-center justify-center\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.BuildingLibraryIcon, {\n                                                        className: \"w-4 h-4 \".concat(dept.color.split(\" \").slice(1).join(\" \"))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            title: \"\\uD83C\\uDF0D Global Events\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 p-4\",\n                                children: schoolData.recentEvents.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-3 p-3 rounded-lg hover:bg-gray-800/40 border border-gray-700/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BuildingLibraryIcon_CalendarIcon_ChartBarIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.CalendarIcon, {\n                                                    className: \"w-5 h-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-white\",\n                                                        children: event.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: [\n                                                            event.date,\n                                                            \" • \",\n                                                            event.type\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mt-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-cyan-400\",\n                                                                children: [\n                                                                    \"\\uD83D\\uDC65 \",\n                                                                    event.participants,\n                                                                    \" participants\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"w-1 h-1 bg-gray-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 179,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-green-400\",\n                                                                children: \"\\uD83C\\uDF10 Global\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 11\n                }, undefined);\n            case \"overview\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"School Overview\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Comprehensive School Overview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Detailed school statistics and performance metrics will be displayed here.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 11\n                }, undefined);\n            case \"departments\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Department Management\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Department Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Manage all school departments, their staff, and resources.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 11\n                }, undefined);\n            case \"facilities\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"School Facilities\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Facilities Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Monitor and manage school facilities, maintenance, and resources.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 11\n                }, undefined);\n            case \"events\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"School Events\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Event Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Plan, organize, and track school events and activities.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 11\n                }, undefined);\n            case \"announcements\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Announcements\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"School Announcements\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Create and manage school-wide announcements and communications.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 11\n                }, undefined);\n            case \"performance\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Performance Analytics\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"School Performance\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Analyze school performance metrics and academic achievements.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 11\n                }, undefined);\n            case \"settings\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"School Settings\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"School Configuration\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Configure school settings, policies, and system preferences.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"School Dashboard\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Welcome to School Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Select a section from the navigation to get started.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full p-2\",\n        children: renderContent()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\SchoolDashboard.tsx\",\n        lineNumber: 273,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SchoolDashboard, \"3VF4hM2Lppj/OmTac9JpUnmmDoQ=\");\n_c = SchoolDashboard;\nvar _c;\n$RefreshReg$(_c, \"SchoolDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/School/departments/SchoolDashboard.tsx\n"));

/***/ })

});
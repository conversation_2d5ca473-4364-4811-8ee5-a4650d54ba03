<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
	</head>
	<body>
		<h2>Monaco Editor Webpack Sample</h2>

		This sample shows how to load a small subset of the editor:
		<ul>
			<li>Only the core editor and the find widget</li>
			<li>Only the python language coloring</li>
		</ul>

		To run this sample, you need to:

		<pre>
$/> npm install .
$/> npm run simpleserver
$/browser-esm-webpack-small> npm run build
</pre
		>

		Then, <a href="./dist">open the ./dist folder</a>.
	</body>
</html>

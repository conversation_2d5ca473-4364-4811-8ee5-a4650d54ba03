
import React from 'react';
import { Card } from '../../components/Card';

export interface StatCardProps {
  icon: React.ReactNode;
  label: string;
  value: string;
  change: string;
  changeType: 'increase' | 'decrease';
}

export const StatCard: React.FC<StatCardProps> = ({ icon, label, value, change, changeType }) => {
  const changeColor = changeType === 'increase' ? 'text-green-400' : 'text-red-400';
  return (
    <Card>
      <div className="flex items-center gap-4">
        <div className="bg-gray-700/50 p-3 rounded-lg text-cyan-400">
          {icon}
        </div>
        <div>
          <p className="text-xs text-gray-400 uppercase">{label}</p>
          <p className="text-xl font-semibold text-white">{value}</p>
        </div>
      </div>
      <p className={`text-sm mt-2 ${changeColor}`}>{change}</p>
    </Card>
  );
};

{"name": "perseus", "version": "1.0.1", "main": "index.js", "repository": "**************:Khan/perseus.git", "license": "MIT", "private": true, "engines": {"node": ">=20"}, "devDependencies": {"@babel/core": "^7.23.2", "@babel/parser": "^7.26.3", "@babel/plugin-syntax-typescript": "^7.25.9", "@babel/traverse": "^7.26.4", "@changesets/changelog-github": "^0.4.8", "@changesets/cli": "^2.22.0", "@cypress/code-coverage": "^3.12.24", "@cypress/react": "^8.0.0", "@jest/globals": "^29.7.0", "@khanacademy/eslint-config": "^5.2.1", "@khanacademy/eslint-plugin": "^3.1.2", "@khanacademy/mathjax-renderer": "catalog:devDeps", "@khanacademy/wonder-blocks-accordion": "catalog:devDeps", "@khanacademy/wonder-blocks-button": "catalog:devDeps", "@khanacademy/wonder-blocks-core": "catalog:devDeps", "@khanacademy/wonder-blocks-icon": "catalog:devDeps", "@khanacademy/wonder-blocks-layout": "catalog:devDeps", "@khanacademy/wonder-blocks-popover": "catalog:devDeps", "@khanacademy/wonder-blocks-switch": "catalog:devDeps", "@khanacademy/wonder-blocks-tokens": "catalog:devDeps", "@khanacademy/wonder-blocks-typography": "catalog:devDeps", "@phosphor-icons/core": "catalog:devDeps", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-swc": "^0.4.0", "@sheerun/mutationobserver-shim": "^0.3.3", "@storybook/addon-a11y": "^9.0.4", "@storybook/addon-docs": "^9.0.5", "@storybook/addon-links": "^9.0.4", "@storybook/react-vite": "^9.0.4", "@swc-node/register": "^1.10.10", "@swc/core": "1.11.13", "@swc/helpers": "^0.5.17", "@swc/jest": "^0.2.37", "@testing-library/dom": "^10.3.1", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "29", "@types/jquery": "^3.5.16", "@types/node": "^20.11.1", "@types/react": "~18.2.64", "@types/react-dom": "~18.2.19", "@types/semver": "7.7.0", "@types/underscore": "^1.11.4", "@typescript-eslint/eslint-plugin": "^8.18.0", "@typescript-eslint/parser": "^8.18.0", "@vitejs/plugin-react-swc": "^3.8.1", "ancesdir": "^3.1.0", "aphrodite": "catalog:devDeps", "babel-plugin-jsx": "^1.2.0", "cross-env": "^5.2.0", "css-loader": "^6.8.1", "cypress": "^13.6.5", "cypress-jest-adapter": "^0.1.1", "cypress-real-events": "^1.12.0", "cypress-wait-until": "^3.0.1", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-typescript": "^3.5.5", "eslint-plugin-cypress": "^2.15.1", "eslint-plugin-disable": "^2.0.3", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jest": "28.9.0", "eslint-plugin-jsdoc": "^48.2.1", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-monorepo": "^0.3.2", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.1.0", "eslint-plugin-storybook": "^9.0.4", "eslint-plugin-testing-library": "^6.2.2", "fast-glob": "^3.2.11", "jest": "^29.7.0", "jest-css-modules-transform": "^4.4.2", "jest-environment-jsdom": "^29.7.0", "jest-serializer-html": "^7.1.0", "jest-specific-snapshot": "^5.0.0", "knip": "^5.33.3", "nyc": "^15.1.0", "pnpm": "^9.15.4", "postcss": "^8.4.49", "postcss-import": "^16.1.0", "postcss-url": "^10.1.3", "prettier": "^3", "react": "catalog:devDeps", "react-dom": "catalog:devDeps", "react-json-view": "^1.19.1", "react-popper": "^2.2.5", "react-router": "5.3.4", "react-router-dom": "5.3.4", "react-transition-group": "^4.4.1", "react-window": "^1.8.10", "rollup": "^4.37.0", "rollup-plugin-auto-external": "2.0.0", "rollup-plugin-executable-output": "^1.3.0", "rollup-plugin-filesize": "^10.0.0", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-preserve-shebangs": "^0.2.0", "semver": "^7.7.2", "sloc": "^0.2.1", "storybook": "^9.0.4", "style-loader": "^3.3.3", "swc_mut_cjs_exports": "^8.0.1", "tiny-invariant": "catalog:devDeps", "typescript": "5.7.2", "typescript-coverage-report": "^0.7.0", "typescript-plugin-css-modules": "^5.1.0", "vite": "catalog:devDeps", "vite-css-modules": "1.8.6", "vite-plugin-istanbul": "^5.0.0", "winston": "^3.7.2", "yaml": "^2.7.0"}, "scripts": {"gen:parsers": "pnpm --filter ./packages/kas gen:parsers", "prebuild": "pnpm gen:parsers", "build": "rollup --bundleConfigAsCjs --config config/build/rollup.config.js", "build:types": "tsc --build tsconfig-build.json", "build:prodsizecheck": "pnpm build --configEnvironment='production'", "watch": "pnpm build --watch", "clean": "./utils/clean.sh", "coverage": "./utils/test-with-coverage.sh", "dev": "pnpm --filter ./dev dev", "lint": "eslint . --ext .js --ext .jsx --ext .ts --ext .tsx", "lint:timing": "cross-env TIMING=1 pnpm lint", "publish:ci": "utils/pre-publish-check-ci.ts && git diff --stat --exit-code HEAD && pnpm build && pnpm build:types && changeset publish", "sloc": "sloc packages --exclude node_modules", "test": "jest", "storybook": "storybook dev -p 6006", "start": "storybook dev -p 6006", "build-storybook": "storybook build --stats-json", "cypress": "cypress open --component", "cypress:ci": "cross-env cypress run --component", "format": "prettier --write .", "typecheck": "tsc", "knip": "knip --config knip.config.ts", "extract-css": "node ./utils/extract-css.js"}, "nyc": {"report-dir": "coverage/cypress/"}, "packageManager": "pnpm@10.8.0", "pnpm": {"onlyBuiltDependencies": ["cypress", "esbuild", "@swc/core"], "patchedDependencies": {"postcss-url": "patches/postcss-url.patch"}}}

import React from 'react';
import {
    HomeIcon,
    AcademicCapIcon,
    WrenchScrewdriverIcon,
    ShoppingCartIcon,
    BookOpenIcon,
    BellIcon,
    ChartBarIcon,
    SettingsIcon
} from './icons';

const NavItem = ({ icon, label, active, onClick }: { icon: React.ReactNode, label: string, active: boolean, onClick: () => void }) => (
  <button
    onClick={onClick}
    className={`relative flex items-center justify-center p-3 rounded-lg transition-colors duration-200 w-full ${
      active ? 'bg-brand-pink/20 text-brand-cyan' : 'text-gray-400 hover:bg-gray-700/50 hover:text-white'
    }`}
    aria-label={label}
  >
    {active && (
      <span className="absolute left-0 top-1/2 -translate-y-1/2 h-5 w-1 bg-brand-cyan rounded-r-full motion-safe:animate-pulse"></span>
    )}
    {icon}
  </button>
);

interface RightSidebarProps {
    activeView: string;
    setActiveView: (view: string) => void;
}

export const RightSidebar: React.FC<RightSidebarProps> = ({ activeView, setActiveView }) => {
  const navItems = [
    { id: 'Dashboard', icon: <HomeIcon className="w-6 h-6" /> },
    { id: 'School', icon: <AcademicCapIcon className="w-6 h-6" /> },
    { id: 'Tool', icon: <WrenchScrewdriverIcon className="w-6 h-6" /> },
    { id: 'Market', icon: <ShoppingCartIcon className="w-6 h-6" /> },
    { id: 'Bookstore', icon: <BookOpenIcon className="w-6 h-6" /> },
    { id: 'Concierge', icon: <BellIcon className="w-6 h-6" /> },
    { id: 'Analytic', icon: <ChartBarIcon className="w-6 h-6" /> },
    { id: 'Setting', icon: <SettingsIcon className="w-6 h-6" /> },
  ];

  return (
    <aside className="bg-panel-bg border border-gray-700/50 rounded-xl p-2 flex flex-col items-center justify-center gap-4">
      {navItems.map(item => (
        <NavItem
          key={item.id}
          icon={item.icon}
          label={item.id}
          active={activeView === item.id}
          onClick={() => setActiveView(item.id)}
        />
      ))}
    </aside>
  );
};

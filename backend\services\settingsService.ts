
export type ProfileData = {
    username: string;
    email: string;
    avatar: string;
};

export type NotificationSettings = {
    emailAlerts: boolean;
    pushNotifications: boolean;
    systemUpdates: boolean;
};

export type SecuritySettings = {
    twoFactorAuth: boolean;
    biometricLock: boolean;
};

export type SettingsData = {
    profile: ProfileData;
    notifications: NotificationSettings;
    security: SecuritySettings;
};

// In-memory store for mock data
let settings: SettingsData = {
    profile: {
        username: "<PERSON><PERSON><PERSON>",
        email: "<EMAIL>",
        avatar: "https://i.pravatar.cc/150?u=faylur",
    },
    notifications: {
        emailAlerts: true,
        pushNotifications: true,
        systemUpdates: false,
    },
    security: {
        twoFactorAuth: true,
        biometricLock: false,
    }
};

export const getSettings = (): SettingsData => {
    return settings;
};

export const updateSettings = (newSettings: Partial<SettingsData>): SettingsData => {
    settings = {
        ...settings,
        ...newSettings,
        profile: { ...settings.profile, ...newSettings.profile },
        notifications: { ...settings.notifications, ...newSettings.notifications },
        security: { ...settings.security, ...newSettings.security },
    };
    console.log("Settings updated:", settings);
    return settings;
};

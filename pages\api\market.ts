
import type { NextApiRequest, NextApiResponse } from 'next';
import { executeQuerySingle, initializeDatabase, seedDatabase } from '../../backend/lib/database';

type FeaturedProductData = {
  image: string;
  category: string;
  name: string;
  price: string;
};

type MarketStatsData = {
    marketCap: string;
    volume24h: string;
    topMover: {
        name: string;
        change: string;
        isUp: boolean;
    };
};

type MarketData = {
    featuredProduct: FeaturedProductData;
    marketStats: MarketStatsData;
};

type DatabaseMarketData = {
  id: number;
  featured_product_name: string;
  featured_product_price: number;
  featured_product_image: string;
  top_mover_symbol: string;
  top_mover_change: number;
  top_mover_is_up: boolean;
  volume: number;
  created_at: string;
  updated_at: string;
};



export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<MarketData | { error: string }>
) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  try {
    // Initialize database if needed
    await initializeDatabase();
    await seedDatabase();

    // Get latest market data from database
    const marketData = await executeQuerySingle<DatabaseMarketData>(`
      SELECT * FROM market_data
      ORDER BY updated_at DESC
      LIMIT 1
    `);

    if (marketData) {
      const response: MarketData = {
        featuredProduct: {
          image: marketData.featured_product_image || "https://picsum.photos/seed/gadget/400/200",
          category: "Neural Interfaces",
          name: marketData.featured_product_name,
          price: `$${marketData.featured_product_price.toLocaleString()}`
        },
        marketStats: {
          marketCap: "$1.2T",
          volume24h: `$${(marketData.volume / 1000000).toFixed(1)}M`,
          topMover: {
            name: marketData.top_mover_symbol,
            change: `${marketData.top_mover_is_up ? '+' : ''}${marketData.top_mover_change}%`,
            isUp: marketData.top_mover_is_up
          }
        }
      };
      res.status(200).json(response);
    } else {
      // Fallback data if no data exists
      res.status(200).json({
        featuredProduct: {
          image: "https://picsum.photos/seed/gadget/400/200",
          category: "Neural Interfaces",
          name: "Quantum Computing Module",
          price: "$2,499.99"
        },
        marketStats: {
          marketCap: "$1.2T",
          volume24h: "$1.3M",
          topMover: {
            name: "QTECH",
            change: "+15.75%",
            isUp: true
          }
        }
      });
    }
  } catch (error) {
    console.error('Market API error:', error);

    // Fallback data if database fails
    res.status(200).json({
      featuredProduct: {
        image: "https://picsum.photos/seed/gadget/400/200",
        category: "Neural Interfaces",
        name: "Quantum Computing Module",
        price: "$2,499.99"
      },
      marketStats: {
        marketCap: "$1.2T",
        volume24h: "$1.3M",
        topMover: {
          name: "QTECH",
          change: "+15.75%",
          isUp: true
        }
      }
    });
  }
}


import type { NextApiRequest, NextApiResponse } from 'next';
import { generateStructuredData, Type } from '../../backend/lib/gemini';

type FeaturedProductData = {
  image: string;
  category: string;
  name: string;
  price: string;
};

type MarketStatsData = {
    marketCap: string;
    volume24h: string;
    topMover: {
        name: string;
        change: string;
        isUp: boolean;
    };
};

type MarketData = {
    featuredProduct: FeaturedProductData;
    marketStats: MarketStatsData;
};

const marketSchema = {
    type: Type.OBJECT,
    properties: {
        featuredProduct: {
            type: Type.OBJECT,
            properties: {
                category: { type: Type.STRING, description: "A category for a futuristic item, e.g., 'Cybernetics'." },
                name: { type: Type.STRING, description: "The name of a futuristic product, e.g., 'Neural Interface MK. V'." },
                price: { type: Type.STRING, description: "The price in a fictional currency, e.g., 'Ƀ 1.75'." }
            },
            required: ["category", "name", "price"]
        },
        marketStats: {
            type: Type.OBJECT,
            properties: {
                marketCap: { type: Type.STRING, description: "A large market cap value, e.g., 'Ƀ 1.5T'." },
                volume24h: { type: Type.STRING, description: "A large 24h volume value, e.g., 'Ƀ 90.2B'." },
                topMover: {
                    type: Type.OBJECT,
                    properties: {
                        name: { type: Type.STRING, description: "A 4-letter stock ticker, e.g., 'CYBR'." },
                        change: { type: Type.STRING, description: "A percentage change, e.g., '+15%'." },
                        isUp: { type: Type.BOOLEAN, description: "True if the change is positive, false otherwise." }
                    },
                    required: ["name", "change", "isUp"]
                }
            },
            required: ["marketCap", "volume24h", "topMover"]
        }
    },
    required: ["featuredProduct", "marketStats"]
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<MarketData | { error: string }>
) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  try {
    const prompt = "Generate data for a futuristic marketplace. Provide one featured product and some overall market statistics, including a top mover.";
    const data = await generateStructuredData<Omit<MarketData, 'featuredProduct.image'>>(prompt, marketSchema);
    
    const fullData: MarketData = {
      ...data,
      featuredProduct: {
        ...data.featuredProduct,
        image: "https://picsum.photos/seed/gadget/400/200"
      }
    };

    res.status(200).json(fullData);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Failed to fetch market data from AI.' });
  }
}

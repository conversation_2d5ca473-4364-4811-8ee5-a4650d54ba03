/********************************************************************************
 * Copyright (C) 2018 TypeFox and others.
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License v. 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * This Source Code may also be made available under the following Secondary
 * Licenses when the conditions for such availability set forth in the Eclipse
 * Public License v. 2.0 are satisfied: GNU General Public License, version 2
 * with the GNU Classpath Exception which is available at
 * https://www.gnu.org/software/classpath/license.html.
 *
 * SPDX-License-Identifier: EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0
 ********************************************************************************/

/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */

/* Original theme - Copyright (C) 2013 <PERSON>son http://chriskempson.com
/* released under the MIT License */
/* https://github.com/chriskempson/tomorrow-theme */

/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

/* Copied from https://github.com/microsoft/vscode/blob/08537497eecd3172390194693d3d7c0ec8f52b70/extensions/markdown-language-features/media/tomorrow.css
 * with modifications.
 */

/* This theme is used to style the output of the highlight.js library which is
 * licensed under the BSD-3-Clause. See https://github.com/highlightjs/highlight.js/blob/master/LICENSE
 */

/* Tomorrow Comment */
.theia-preview-widget .hljs-comment,
.theia-preview-widget .hljs-quote {
  color: #8e908c;
}

/* Tomorrow Red */
.theia-preview-widget .hljs-variable,
.theia-preview-widget .hljs-template-variable,
.theia-preview-widget .hljs-tag,
.theia-preview-widget .hljs-name,
.theia-preview-widget .hljs-selector-id,
.theia-preview-widget .hljs-selector-class,
.theia-preview-widget .hljs-regexp,
.theia-preview-widget .hljs-deletion {
  color: #c82829;
}

/* Tomorrow Orange */
.theia-preview-widget .hljs-number,
.theia-preview-widget .hljs-built_in,
.theia-preview-widget .hljs-builtin-name,
.theia-preview-widget .hljs-literal,
.theia-preview-widget .hljs-type,
.theia-preview-widget .hljs-params,
.theia-preview-widget .hljs-meta,
.theia-preview-widget .hljs-link {
  color: #f5871f;
}

/* Tomorrow Yellow */
.theia-preview-widget .hljs-attribute {
  color: #eab700;
}

/* Tomorrow Green */
.theia-preview-widget .hljs-string,
.theia-preview-widget .hljs-symbol,
.theia-preview-widget .hljs-bullet,
.theia-preview-widget .hljs-addition {
  color: #718c00;
}

/* Tomorrow Blue */
.theia-preview-widget .hljs-title,
.theia-preview-widget .hljs-section {
  color: #4271ae;
}

/* Tomorrow Purple */
.theia-preview-widget .hljs-keyword,
.theia-preview-widget .hljs-selector-tag {
  color: #8959a8;
}

.theia-preview-widget .hljs {
  display: block;
  overflow-x: auto;
  color: #4d4d4c;
  padding: 0.5em;
}

.theia-preview-widget .hljs-emphasis {
  font-style: italic;
}

.theia-preview-widget .hljs-strong {
  font-weight: bold;
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[1]!./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[2]!./styles/globals.css":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[1]!./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[2]!./styles/globals.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_pnpm_next_14_2_3_react_dom_18_2_0_react_18_2_0_react_18_2_0_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_pnpm_next_14_2_3_react_dom_18_2_0_react_18_2_0_react_18_2_0_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_pnpm_next_14_2_3_react_dom_18_2_0_react_18_2_0_react_18_2_0_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_pnpm_next_14_2_3_react_dom_18_2_0_react_18_2_0_react_18_2_0_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');\\n\\n*, ::before, ::after{\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n::backdrop{\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*/\\n\\n/*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\n\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\"; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\n\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\n.\\\\!container{\\n  width: 100% !important;\\n}\\n.container{\\n  width: 100%;\\n}\\n@media (min-width: 640px){\\n\\n  .\\\\!container{\\n    max-width: 640px !important;\\n  }\\n\\n  .container{\\n    max-width: 640px;\\n  }\\n}\\n@media (min-width: 768px){\\n\\n  .\\\\!container{\\n    max-width: 768px !important;\\n  }\\n\\n  .container{\\n    max-width: 768px;\\n  }\\n}\\n@media (min-width: 1024px){\\n\\n  .\\\\!container{\\n    max-width: 1024px !important;\\n  }\\n\\n  .container{\\n    max-width: 1024px;\\n  }\\n}\\n@media (min-width: 1280px){\\n\\n  .\\\\!container{\\n    max-width: 1280px !important;\\n  }\\n\\n  .container{\\n    max-width: 1280px;\\n  }\\n}\\n@media (min-width: 1536px){\\n\\n  .\\\\!container{\\n    max-width: 1536px !important;\\n  }\\n\\n  .container{\\n    max-width: 1536px;\\n  }\\n}\\n.\\\\!visible{\\n  visibility: visible !important;\\n}\\n.visible{\\n  visibility: visible;\\n}\\n.invisible{\\n  visibility: hidden;\\n}\\n.collapse{\\n  visibility: collapse;\\n}\\n.static{\\n  position: static;\\n}\\n.fixed{\\n  position: fixed;\\n}\\n.absolute{\\n  position: absolute;\\n}\\n.relative{\\n  position: relative;\\n}\\n.sticky{\\n  position: sticky;\\n}\\n.inset-0{\\n  inset: 0px;\\n}\\n.-bottom-1{\\n  bottom: -0.25rem;\\n}\\n.-left-1\\\\/2{\\n  left: -50%;\\n}\\n.-right-1{\\n  right: -0.25rem;\\n}\\n.-top-1{\\n  top: -0.25rem;\\n}\\n.-top-1\\\\/2{\\n  top: -50%;\\n}\\n.left-0{\\n  left: 0px;\\n}\\n.left-1\\\\/2{\\n  left: 50%;\\n}\\n.left-20{\\n  left: 5rem;\\n}\\n.right-4{\\n  right: 1rem;\\n}\\n.top-0{\\n  top: 0px;\\n}\\n.top-1\\\\/2{\\n  top: 50%;\\n}\\n.top-4{\\n  top: 1rem;\\n}\\n.isolate{\\n  isolation: isolate;\\n}\\n.z-10{\\n  z-index: 10;\\n}\\n.z-30{\\n  z-index: 30;\\n}\\n.z-40{\\n  z-index: 40;\\n}\\n.z-50{\\n  z-index: 50;\\n}\\n.col-span-2{\\n  grid-column: span 2 / span 2;\\n}\\n.mx-auto{\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n.\\\\!mt-6{\\n  margin-top: 1.5rem !important;\\n}\\n.mb-1{\\n  margin-bottom: 0.25rem;\\n}\\n.mb-2{\\n  margin-bottom: 0.5rem;\\n}\\n.mb-3{\\n  margin-bottom: 0.75rem;\\n}\\n.mb-4{\\n  margin-bottom: 1rem;\\n}\\n.mb-6{\\n  margin-bottom: 1.5rem;\\n}\\n.mb-8{\\n  margin-bottom: 2rem;\\n}\\n.ml-1{\\n  margin-left: 0.25rem;\\n}\\n.mr-2{\\n  margin-right: 0.5rem;\\n}\\n.mt-1{\\n  margin-top: 0.25rem;\\n}\\n.mt-2{\\n  margin-top: 0.5rem;\\n}\\n.mt-3{\\n  margin-top: 0.75rem;\\n}\\n.mt-4{\\n  margin-top: 1rem;\\n}\\n.mt-6{\\n  margin-top: 1.5rem;\\n}\\n.block{\\n  display: block;\\n}\\n.inline-block{\\n  display: inline-block;\\n}\\n.inline{\\n  display: inline;\\n}\\n.flex{\\n  display: flex;\\n}\\n.table{\\n  display: table;\\n}\\n.grid{\\n  display: grid;\\n}\\n.inline-grid{\\n  display: inline-grid;\\n}\\n.contents{\\n  display: contents;\\n}\\n.hidden{\\n  display: none;\\n}\\n.h-0\\\\.5{\\n  height: 0.125rem;\\n}\\n.h-1{\\n  height: 0.25rem;\\n}\\n.h-10{\\n  height: 2.5rem;\\n}\\n.h-12{\\n  height: 3rem;\\n}\\n.h-14{\\n  height: 3.5rem;\\n}\\n.h-16{\\n  height: 4rem;\\n}\\n.h-2{\\n  height: 0.5rem;\\n}\\n.h-24{\\n  height: 6rem;\\n}\\n.h-3{\\n  height: 0.75rem;\\n}\\n.h-32{\\n  height: 8rem;\\n}\\n.h-4{\\n  height: 1rem;\\n}\\n.h-40{\\n  height: 10rem;\\n}\\n.h-48{\\n  height: 12rem;\\n}\\n.h-5{\\n  height: 1.25rem;\\n}\\n.h-6{\\n  height: 1.5rem;\\n}\\n.h-64{\\n  height: 16rem;\\n}\\n.h-8{\\n  height: 2rem;\\n}\\n.h-\\\\[200\\\\%\\\\]{\\n  height: 200%;\\n}\\n.h-full{\\n  height: 100%;\\n}\\n.max-h-64{\\n  max-height: 16rem;\\n}\\n.min-h-0{\\n  min-height: 0px;\\n}\\n.min-h-\\\\[400px\\\\]{\\n  min-height: 400px;\\n}\\n.w-1{\\n  width: 0.25rem;\\n}\\n.w-1\\\\/4{\\n  width: 25%;\\n}\\n.w-10{\\n  width: 2.5rem;\\n}\\n.w-12{\\n  width: 3rem;\\n}\\n.w-14{\\n  width: 3.5rem;\\n}\\n.w-16{\\n  width: 4rem;\\n}\\n.w-2{\\n  width: 0.5rem;\\n}\\n.w-24{\\n  width: 6rem;\\n}\\n.w-3{\\n  width: 0.75rem;\\n}\\n.w-32{\\n  width: 8rem;\\n}\\n.w-4{\\n  width: 1rem;\\n}\\n.w-5{\\n  width: 1.25rem;\\n}\\n.w-6{\\n  width: 1.5rem;\\n}\\n.w-64{\\n  width: 16rem;\\n}\\n.w-8{\\n  width: 2rem;\\n}\\n.w-80{\\n  width: 20rem;\\n}\\n.w-\\\\[200\\\\%\\\\]{\\n  width: 200%;\\n}\\n.w-full{\\n  width: 100%;\\n}\\n.max-w-md{\\n  max-width: 28rem;\\n}\\n.max-w-sm{\\n  max-width: 24rem;\\n}\\n.flex-1{\\n  flex: 1 1 0%;\\n}\\n.flex-shrink-0{\\n  flex-shrink: 0;\\n}\\n.shrink{\\n  flex-shrink: 1;\\n}\\n.flex-grow{\\n  flex-grow: 1;\\n}\\n.grow{\\n  flex-grow: 1;\\n}\\n.-translate-x-1\\\\/2{\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.-translate-y-1\\\\/2{\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.translate-x-0{\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.translate-x-5{\\n  --tw-translate-x: 1.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.scale-105{\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.scale-90{\\n  --tw-scale-x: .9;\\n  --tw-scale-y: .9;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.scale-95{\\n  --tw-scale-x: .95;\\n  --tw-scale-y: .95;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.\\\\!transform{\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important;\\n}\\n.transform{\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n@keyframes pulse{\\n\\n  50%{\\n    opacity: .5;\\n  }\\n}\\n.animate-pulse{\\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\n@keyframes spin{\\n\\n  to{\\n    transform: rotate(360deg);\\n  }\\n}\\n.animate-spin{\\n  animation: spin 1s linear infinite;\\n}\\n.cursor-pointer{\\n  cursor: pointer;\\n}\\n.resize{\\n  resize: both;\\n}\\n.grid-cols-1{\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\n.grid-cols-2{\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\n.grid-cols-3{\\n  grid-template-columns: repeat(3, minmax(0, 1fr));\\n}\\n.grid-cols-4{\\n  grid-template-columns: repeat(4, minmax(0, 1fr));\\n}\\n.flex-col{\\n  flex-direction: column;\\n}\\n.flex-wrap{\\n  flex-wrap: wrap;\\n}\\n.items-start{\\n  align-items: flex-start;\\n}\\n.items-end{\\n  align-items: flex-end;\\n}\\n.items-center{\\n  align-items: center;\\n}\\n.justify-end{\\n  justify-content: flex-end;\\n}\\n.justify-center{\\n  justify-content: center;\\n}\\n.justify-between{\\n  justify-content: space-between;\\n}\\n.justify-around{\\n  justify-content: space-around;\\n}\\n.gap-1{\\n  gap: 0.25rem;\\n}\\n.gap-2{\\n  gap: 0.5rem;\\n}\\n.gap-3{\\n  gap: 0.75rem;\\n}\\n.gap-4{\\n  gap: 1rem;\\n}\\n.gap-6{\\n  gap: 1.5rem;\\n}\\n.space-y-1 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\n.space-y-2 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-3 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\n.space-y-4 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\n.overflow-auto{\\n  overflow: auto;\\n}\\n.overflow-hidden{\\n  overflow: hidden;\\n}\\n.overflow-x-auto{\\n  overflow-x: auto;\\n}\\n.overflow-y-auto{\\n  overflow-y: auto;\\n}\\n.truncate{\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.whitespace-nowrap{\\n  white-space: nowrap;\\n}\\n.rounded{\\n  border-radius: 0.25rem;\\n}\\n.rounded-full{\\n  border-radius: 9999px;\\n}\\n.rounded-lg{\\n  border-radius: 0.5rem;\\n}\\n.rounded-md{\\n  border-radius: 0.375rem;\\n}\\n.rounded-sm{\\n  border-radius: 0.125rem;\\n}\\n.rounded-xl{\\n  border-radius: 0.75rem;\\n}\\n.rounded-r-full{\\n  border-top-right-radius: 9999px;\\n  border-bottom-right-radius: 9999px;\\n}\\n.border{\\n  border-width: 1px;\\n}\\n.border-2{\\n  border-width: 2px;\\n}\\n.border-b{\\n  border-bottom-width: 1px;\\n}\\n.border-b-2{\\n  border-bottom-width: 2px;\\n}\\n.border-l{\\n  border-left-width: 1px;\\n}\\n.border-r{\\n  border-right-width: 1px;\\n}\\n.border-t{\\n  border-top-width: 1px;\\n}\\n.border-none{\\n  border-style: none;\\n}\\n.border-blue-400{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));\\n}\\n.border-blue-400\\\\/30{\\n  border-color: rgb(96 165 250 / 0.3);\\n}\\n.border-blue-500\\\\/30{\\n  border-color: rgb(59 130 246 / 0.3);\\n}\\n.border-brand-cyan{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(0 255 255 / var(--tw-border-opacity, 1));\\n}\\n.border-cyan-400{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(34 211 238 / var(--tw-border-opacity, 1));\\n}\\n.border-cyan-400\\\\/30{\\n  border-color: rgb(34 211 238 / 0.3);\\n}\\n.border-cyan-500\\\\/30{\\n  border-color: rgb(6 182 212 / 0.3);\\n}\\n.border-cyan-500\\\\/50{\\n  border-color: rgb(6 182 212 / 0.5);\\n}\\n.border-gray-400\\\\/30{\\n  border-color: rgb(156 163 175 / 0.3);\\n}\\n.border-gray-500\\\\/40{\\n  border-color: rgb(107 114 128 / 0.4);\\n}\\n.border-gray-600{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-600\\\\/30{\\n  border-color: rgb(75 85 99 / 0.3);\\n}\\n.border-gray-700{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-700\\\\/30{\\n  border-color: rgb(55 65 81 / 0.3);\\n}\\n.border-gray-700\\\\/50{\\n  border-color: rgb(55 65 81 / 0.5);\\n}\\n.border-green-400\\\\/30{\\n  border-color: rgb(74 222 128 / 0.3);\\n}\\n.border-green-500\\\\/30{\\n  border-color: rgb(34 197 94 / 0.3);\\n}\\n.border-pink-400\\\\/30{\\n  border-color: rgb(244 114 182 / 0.3);\\n}\\n.border-pink-500\\\\/90{\\n  border-color: rgb(236 72 153 / 0.9);\\n}\\n.border-purple-400\\\\/30{\\n  border-color: rgb(192 132 252 / 0.3);\\n}\\n.border-purple-400\\\\/40{\\n  border-color: rgb(192 132 252 / 0.4);\\n}\\n.border-purple-500\\\\/30{\\n  border-color: rgb(168 85 247 / 0.3);\\n}\\n.border-red-400\\\\/30{\\n  border-color: rgb(248 113 113 / 0.3);\\n}\\n.border-red-500\\\\/30{\\n  border-color: rgb(239 68 68 / 0.3);\\n}\\n.border-yellow-400\\\\/30{\\n  border-color: rgb(250 204 21 / 0.3);\\n}\\n.border-yellow-400\\\\/50{\\n  border-color: rgb(250 204 21 / 0.5);\\n}\\n.border-yellow-500\\\\/30{\\n  border-color: rgb(234 179 8 / 0.3);\\n}\\n.bg-black{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\\n}\\n.bg-black\\\\/20{\\n  background-color: rgb(0 0 0 / 0.2);\\n}\\n.bg-black\\\\/60{\\n  background-color: rgb(0 0 0 / 0.6);\\n}\\n.bg-black\\\\/70{\\n  background-color: rgb(0 0 0 / 0.7);\\n}\\n.bg-blue-400{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-500\\\\/20{\\n  background-color: rgb(59 130 246 / 0.2);\\n}\\n.bg-brand-cyan{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-brand-cyan\\\\/20{\\n  background-color: rgb(0 255 255 / 0.2);\\n}\\n.bg-brand-pink\\\\/20{\\n  background-color: rgb(255 0 127 / 0.2);\\n}\\n.bg-container-bg{\\n  background-color: rgba(10, 20, 35, 0.7);\\n}\\n.bg-cyan-400{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 211 238 / var(--tw-bg-opacity, 1));\\n}\\n.bg-cyan-400\\\\/10{\\n  background-color: rgb(34 211 238 / 0.1);\\n}\\n.bg-cyan-400\\\\/20{\\n  background-color: rgb(34 211 238 / 0.2);\\n}\\n.bg-cyan-400\\\\/50{\\n  background-color: rgb(34 211 238 / 0.5);\\n}\\n.bg-cyan-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(6 182 212 / var(--tw-bg-opacity, 1));\\n}\\n.bg-cyan-500\\\\/20{\\n  background-color: rgb(6 182 212 / 0.2);\\n}\\n.bg-cyan-500\\\\/30{\\n  background-color: rgb(6 182 212 / 0.3);\\n}\\n.bg-cyan-500\\\\/50{\\n  background-color: rgb(6 182 212 / 0.5);\\n}\\n.bg-emerald-500\\\\/20{\\n  background-color: rgb(16 185 129 / 0.2);\\n}\\n.bg-gray-200\\\\/20{\\n  background-color: rgb(229 231 235 / 0.2);\\n}\\n.bg-gray-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-500\\\\/20{\\n  background-color: rgb(107 114 128 / 0.2);\\n}\\n.bg-gray-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-700{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-700\\\\/30{\\n  background-color: rgb(55 65 81 / 0.3);\\n}\\n.bg-gray-700\\\\/50{\\n  background-color: rgb(55 65 81 / 0.5);\\n}\\n.bg-gray-700\\\\/60{\\n  background-color: rgb(55 65 81 / 0.6);\\n}\\n.bg-gray-700\\\\/80{\\n  background-color: rgb(55 65 81 / 0.8);\\n}\\n.bg-gray-800\\\\/40{\\n  background-color: rgb(31 41 55 / 0.4);\\n}\\n.bg-gray-800\\\\/60{\\n  background-color: rgb(31 41 55 / 0.6);\\n}\\n.bg-gray-900\\\\/50{\\n  background-color: rgb(17 24 39 / 0.5);\\n}\\n.bg-green-400{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-500\\\\/20{\\n  background-color: rgb(34 197 94 / 0.2);\\n}\\n.bg-panel-bg{\\n  background-color: rgba(25, 40, 60, 0.6);\\n}\\n.bg-pink-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(236 72 153 / var(--tw-bg-opacity, 1));\\n}\\n.bg-pink-500\\\\/20{\\n  background-color: rgb(236 72 153 / 0.2);\\n}\\n.bg-pink-500\\\\/80{\\n  background-color: rgb(236 72 153 / 0.8);\\n}\\n.bg-purple-500\\\\/20{\\n  background-color: rgb(168 85 247 / 0.2);\\n}\\n.bg-red-500\\\\/20{\\n  background-color: rgb(239 68 68 / 0.2);\\n}\\n.bg-transparent{\\n  background-color: transparent;\\n}\\n.bg-white{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-white\\\\/20{\\n  background-color: rgb(255 255 255 / 0.2);\\n}\\n.bg-yellow-400{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-500\\\\/20{\\n  background-color: rgb(234 179 8 / 0.2);\\n}\\n.bg-gray-900\\\\/30{\\n  background-color: rgb(17 24 39 / 0.3);\\n}\\n.bg-\\\\[radial-gradient\\\\(circle_at_center\\\\2c _rgba\\\\(255\\\\2c 0\\\\2c 127\\\\2c 0\\\\.5\\\\)\\\\2c _transparent_40\\\\%\\\\)\\\\]{\\n  background-image: radial-gradient(circle at center, rgba(255,0,127,0.5), transparent 40%);\\n}\\n.bg-gradient-to-br{\\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\\n}\\n.bg-gradient-to-r{\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n}\\n.from-\\\\[\\\\#FF007F\\\\]{\\n  --tw-gradient-from: #FF007F var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(255 0 127 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-400\\\\/20{\\n  --tw-gradient-from: rgb(96 165 250 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-500{\\n  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-900\\\\/20{\\n  --tw-gradient-from: rgb(30 58 138 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-cyan-400{\\n  --tw-gradient-from: #22d3ee var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 211 238 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-cyan-400\\\\/20{\\n  --tw-gradient-from: rgb(34 211 238 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 211 238 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-cyan-500{\\n  --tw-gradient-from: #06b6d4 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(6 182 212 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-cyan-900\\\\/20{\\n  --tw-gradient-from: rgb(22 78 99 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(22 78 99 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-gray-800\\\\/40{\\n  --tw-gradient-from: rgb(31 41 55 / 0.4) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-gray-800\\\\/60{\\n  --tw-gradient-from: rgb(31 41 55 / 0.6) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-gray-900\\\\/40{\\n  --tw-gradient-from: rgb(17 24 39 / 0.4) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-green-400\\\\/20{\\n  --tw-gradient-from: rgb(74 222 128 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(74 222 128 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-green-500{\\n  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-green-900\\\\/20{\\n  --tw-gradient-from: rgb(20 83 45 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(20 83 45 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-purple-400\\\\/20{\\n  --tw-gradient-from: rgb(192 132 252 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(192 132 252 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-purple-500{\\n  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-purple-900\\\\/20{\\n  --tw-gradient-from: rgb(88 28 135 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-purple-900\\\\/30{\\n  --tw-gradient-from: rgb(88 28 135 / 0.3) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-red-500{\\n  --tw-gradient-from: #ef4444 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-red-900\\\\/20{\\n  --tw-gradient-from: rgb(127 29 29 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(127 29 29 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-white{\\n  --tw-gradient-from: #fff var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-yellow-400\\\\/20{\\n  --tw-gradient-from: rgb(250 204 21 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(250 204 21 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-yellow-500{\\n  --tw-gradient-from: #eab308 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(234 179 8 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-yellow-900\\\\/20{\\n  --tw-gradient-from: rgb(113 63 18 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(113 63 18 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.to-\\\\[\\\\#d6006a\\\\]{\\n  --tw-gradient-to: #d6006a var(--tw-gradient-to-position);\\n}\\n.to-blue-500{\\n  --tw-gradient-to: #3b82f6 var(--tw-gradient-to-position);\\n}\\n.to-blue-500\\\\/20{\\n  --tw-gradient-to: rgb(59 130 246 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-blue-600{\\n  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);\\n}\\n.to-blue-600\\\\/20{\\n  --tw-gradient-to: rgb(37 99 235 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-blue-800\\\\/20{\\n  --tw-gradient-to: rgb(30 64 175 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-blue-900\\\\/20{\\n  --tw-gradient-to: rgb(30 58 138 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-blue-900\\\\/30{\\n  --tw-gradient-to: rgb(30 58 138 / 0.3) var(--tw-gradient-to-position);\\n}\\n.to-cyan-600{\\n  --tw-gradient-to: #0891b2 var(--tw-gradient-to-position);\\n}\\n.to-cyan-800\\\\/20{\\n  --tw-gradient-to: rgb(21 94 117 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-cyan-900\\\\/20{\\n  --tw-gradient-to: rgb(22 78 99 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-emerald-600{\\n  --tw-gradient-to: #059669 var(--tw-gradient-to-position);\\n}\\n.to-emerald-900\\\\/20{\\n  --tw-gradient-to: rgb(6 78 59 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-fuchsia-500{\\n  --tw-gradient-to: #d946ef var(--tw-gradient-to-position);\\n}\\n.to-gray-700\\\\/40{\\n  --tw-gradient-to: rgb(55 65 81 / 0.4) var(--tw-gradient-to-position);\\n}\\n.to-gray-700\\\\/60{\\n  --tw-gradient-to: rgb(55 65 81 / 0.6) var(--tw-gradient-to-position);\\n}\\n.to-gray-800\\\\/40{\\n  --tw-gradient-to: rgb(31 41 55 / 0.4) var(--tw-gradient-to-position);\\n}\\n.to-gray-900\\\\/60{\\n  --tw-gradient-to: rgb(17 24 39 / 0.6) var(--tw-gradient-to-position);\\n}\\n.to-green-600\\\\/20{\\n  --tw-gradient-to: rgb(22 163 74 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-green-800\\\\/20{\\n  --tw-gradient-to: rgb(22 101 52 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-orange-600{\\n  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);\\n}\\n.to-orange-900\\\\/20{\\n  --tw-gradient-to: rgb(124 45 18 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-pink-600{\\n  --tw-gradient-to: #db2777 var(--tw-gradient-to-position);\\n}\\n.to-pink-900\\\\/20{\\n  --tw-gradient-to: rgb(131 24 67 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-purple-600{\\n  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);\\n}\\n.to-purple-600\\\\/20{\\n  --tw-gradient-to: rgb(147 51 234 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-purple-800\\\\/20{\\n  --tw-gradient-to: rgb(107 33 168 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-purple-900\\\\/20{\\n  --tw-gradient-to: rgb(88 28 135 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-transparent{\\n  --tw-gradient-to: transparent var(--tw-gradient-to-position);\\n}\\n.to-yellow-600\\\\/20{\\n  --tw-gradient-to: rgb(202 138 4 / 0.2) var(--tw-gradient-to-position);\\n}\\n.object-cover{\\n  -o-object-fit: cover;\\n     object-fit: cover;\\n}\\n.p-0{\\n  padding: 0px;\\n}\\n.p-1{\\n  padding: 0.25rem;\\n}\\n.p-2{\\n  padding: 0.5rem;\\n}\\n.p-3{\\n  padding: 0.75rem;\\n}\\n.p-4{\\n  padding: 1rem;\\n}\\n.p-6{\\n  padding: 1.5rem;\\n}\\n.px-2{\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\n.px-3{\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\n.px-4{\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n.py-1{\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\n.py-1\\\\.5{\\n  padding-top: 0.375rem;\\n  padding-bottom: 0.375rem;\\n}\\n.py-2{\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\n.pb-1{\\n  padding-bottom: 0.25rem;\\n}\\n.pt-2{\\n  padding-top: 0.5rem;\\n}\\n.pt-4{\\n  padding-top: 1rem;\\n}\\n.text-left{\\n  text-align: left;\\n}\\n.text-center{\\n  text-align: center;\\n}\\n.text-right{\\n  text-align: right;\\n}\\n.font-mono{\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace;\\n}\\n.font-poppins{\\n  font-family: Poppins, sans-serif;\\n}\\n.text-2xl{\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\n.text-3xl{\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\n.text-\\\\[10px\\\\]{\\n  font-size: 10px;\\n}\\n.text-base{\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\n.text-lg{\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\n.text-sm{\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n.text-xl{\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n.text-xs{\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\n.text-6xl{\\n  font-size: 3.75rem;\\n  line-height: 1;\\n}\\n.font-bold{\\n  font-weight: 700;\\n}\\n.font-medium{\\n  font-weight: 500;\\n}\\n.font-normal{\\n  font-weight: 400;\\n}\\n.font-semibold{\\n  font-weight: 600;\\n}\\n.uppercase{\\n  text-transform: uppercase;\\n}\\n.capitalize{\\n  text-transform: capitalize;\\n}\\n.leading-relaxed{\\n  line-height: 1.625;\\n}\\n.tracking-wider{\\n  letter-spacing: 0.05em;\\n}\\n.text-\\\\[\\\\#A0A0B0\\\\]{\\n  --tw-text-opacity: 1;\\n  color: rgb(160 160 176 / var(--tw-text-opacity, 1));\\n}\\n.text-\\\\[\\\\#E0E0E0\\\\]{\\n  --tw-text-opacity: 1;\\n  color: rgb(224 224 224 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\\n}\\n.text-brand-cyan{\\n  --tw-text-opacity: 1;\\n  color: rgb(0 255 255 / var(--tw-text-opacity, 1));\\n}\\n.text-cyan-300{\\n  --tw-text-opacity: 1;\\n  color: rgb(103 232 249 / var(--tw-text-opacity, 1));\\n}\\n.text-cyan-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(34 211 238 / var(--tw-text-opacity, 1));\\n}\\n.text-emerald-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(52 211 153 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-300{\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-500{\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-900{\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\n.text-green-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\\n}\\n.text-indigo-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(129 140 248 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(251 146 60 / var(--tw-text-opacity, 1));\\n}\\n.text-pink-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(244 114 182 / var(--tw-text-opacity, 1));\\n}\\n.text-purple-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(192 132 252 / var(--tw-text-opacity, 1));\\n}\\n.text-red-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\\n}\\n.text-teal-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(45 212 191 / var(--tw-text-opacity, 1));\\n}\\n.text-white{\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\\n}\\n.overline{\\n  text-decoration-line: overline;\\n}\\n.antialiased{\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n.opacity-0{\\n  opacity: 0;\\n}\\n.opacity-30{\\n  opacity: 0.3;\\n}\\n.opacity-50{\\n  opacity: 0.5;\\n}\\n.opacity-90{\\n  opacity: 0.9;\\n}\\n.shadow{\\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-inner{\\n  --tw-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: inset 0 2px 4px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-lg{\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-md{\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.outline-none{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.outline{\\n  outline-style: solid;\\n}\\n.ring{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.blur{\\n  --tw-blur: blur(8px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.blur-sm{\\n  --tw-blur: blur(4px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.drop-shadow{\\n  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.grayscale{\\n  --tw-grayscale: grayscale(100%);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.invert{\\n  --tw-invert: invert(100%);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.filter{\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.backdrop-blur-sm{\\n  --tw-backdrop-blur: blur(4px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.backdrop-blur-xl{\\n  --tw-backdrop-blur: blur(24px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.backdrop-filter{\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.transition{\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-all{\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-colors{\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-opacity{\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-transform{\\n  transition-property: transform;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.duration-200{\\n  transition-duration: 200ms;\\n}\\n.duration-300{\\n  transition-duration: 300ms;\\n}\\n.duration-500{\\n  transition-duration: 500ms;\\n}\\n.ease-in{\\n  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);\\n}\\n.ease-in-out{\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.ease-out{\\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n}\\n\\n/* CSS Custom Properties for Responsive Scaling - Optimized for Content Fit */\\n:root {\\n  --scale-factor: 1;\\n  --content-scale: 3;\\n  --base-font-size: calc(14px * var(--content-scale));\\n  --base-spacing: calc(6px * var(--content-scale));\\n  --base-border-radius: calc(8px * var(--content-scale));\\n  --base-icon-size: calc(20px * var(--content-scale));\\n  --base-button-height: calc(36px * var(--content-scale));\\n  --base-card-padding: calc(1px * var(--content-scale));\\n  --base-gap: calc(6px * var(--content-scale));\\n  --header-height: calc(60px * var(--content-scale));\\n  --footer-height: calc(50px * var(--content-scale));\\n  --sidebar-width: calc(80px * var(--content-scale));\\n\\n  /* Unified Dark Theme Color Palette */\\n  --primary-bg: #0f1419;\\n  --secondary-bg: #1a1f2e;\\n  --tertiary-bg: #252b3d;\\n\\n  /* Glassmorphism Dark Theme */\\n  --glass-bg: rgba(26, 31, 46, 0.7);\\n  --glass-bg-light: rgba(37, 43, 61, 0.6);\\n  --glass-border: rgba(100, 116, 139, 0.2);\\n  --glass-border-glow: rgba(100, 116, 139, 0.4);\\n  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 2px 8px rgba(0, 0, 0, 0.2);\\n\\n  /* Consistent Text Colors */\\n  --text-primary: #f8fafc;\\n  --text-secondary: #cbd5e1;\\n  --text-muted: #94a3b8;\\n  --text-accent: #64748b;\\n\\n  /* Unified Accent Colors */\\n  --accent-blue: #3b82f6;\\n  --accent-cyan: #06b6d4;\\n  --accent-green: #10b981;\\n  --accent-yellow: #f59e0b;\\n  --accent-orange: #f97316;\\n  --accent-red: #ef4444;\\n  --accent-purple: #8b5cf6;\\n  --accent-pink: #ec4899;\\n}\\n\\n/* Responsive scaling variables - now handled by --content-scale */\\n@media (max-width: 1920px) and (min-width: 1600px) {\\n  :root {\\n    --scale-factor: 0.9;\\n    --content-scale: 0.9;\\n  }\\n}\\n\\n@media (max-width: 1599px) and (min-width: 1400px) {\\n  :root {\\n    --scale-factor: 0.8;\\n    --content-scale: 0.8;\\n  }\\n}\\n\\n@media (max-width: 1399px) and (min-width: 1200px) {\\n  :root {\\n    --scale-factor: 0.7;\\n    --content-scale: 0.7;\\n  }\\n}\\n\\n@media (max-width: 1199px) and (min-width: 1000px) {\\n  :root {\\n    --scale-factor: 0.6;\\n    --content-scale: 0.6;\\n  }\\n}\\n\\n@media (max-width: 999px) {\\n  :root {\\n    --scale-factor: 0.5;\\n    --content-scale: 0.5;\\n  }\\n}\\n\\n/* Ensure base styles are applied */\\n* {\\n  box-sizing: border-box;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nhtml, body {\\n  height: 100vh;\\n  width: 100vw;\\n  overflow: hidden;\\n  margin: 0;\\n  padding: 0;\\n  background: var(--primary-bg);\\n  color: var(--text-primary);\\n  font-family: 'Poppins', sans-serif;\\n}\\n\\nbody {\\n  font-family: 'Poppins', sans-serif;\\n  font-size: var(--base-font-size);\\n  line-height: 1.5;\\n  background: var(--primary-bg);\\n  color: var(--text-primary);\\n}\\n\\n#__next {\\n  height: 100vh;\\n  width: 100vw;\\n  overflow: hidden;\\n}\\n\\n.main-background {\\n  background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 50%, var(--tertiary-bg) 100%);\\n  background-attachment: fixed;\\n  position: relative;\\n}\\n\\n.main-background::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(ellipse at 20% 30%, rgba(209, 160, 255, 0.1) 0%, transparent 50%),\\n              radial-gradient(ellipse at 80% 70%, rgba(188, 19, 254, 0.08) 0%, transparent 50%),\\n              radial-gradient(ellipse at 50% 50%, rgba(41, 52, 95, 0.05) 0%, transparent 70%);\\n  pointer-events: none;\\n}\\n\\n/* Global Responsive Classes */\\n.responsive-text {\\n  font-size: var(--base-font-size);\\n}\\n\\n.responsive-text-sm {\\n  font-size: calc(var(--base-font-size) * 0.875);\\n}\\n\\n.responsive-text-xs {\\n  font-size: calc(var(--base-font-size) * 0.75);\\n}\\n\\n.responsive-text-lg {\\n  font-size: calc(var(--base-font-size) * 1.125);\\n}\\n\\n.responsive-text-xl {\\n  font-size: calc(var(--base-font-size) * 1.25);\\n}\\n\\n.responsive-text-2xl {\\n  font-size: calc(var(--base-font-size) * 1.5);\\n}\\n\\n.responsive-spacing {\\n  padding: var(--base-spacing);\\n}\\n\\n.responsive-spacing-sm {\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.responsive-spacing-lg {\\n  padding: calc(var(--base-spacing) * 1.5);\\n}\\n\\n.responsive-gap {\\n  gap: var(--base-gap);\\n}\\n\\n.responsive-gap-sm {\\n  gap: calc(var(--base-gap) * 0.5);\\n}\\n\\n.responsive-gap-lg {\\n  gap: calc(var(--base-gap) * 1.5);\\n}\\n\\n.responsive-border-radius {\\n  border-radius: var(--base-border-radius);\\n}\\n\\n.responsive-icon {\\n  width: var(--base-icon-size);\\n  height: var(--base-icon-size);\\n}\\n\\n.responsive-button {\\n  height: var(--base-button-height);\\n  padding: 0 var(--base-spacing);\\n  font-size: var(--base-font-size);\\n  border-radius: var(--base-border-radius);\\n}\\n\\n.responsive-card {\\n  padding: var(--base-card-padding);\\n  border-radius: calc(var(--base-border-radius) * 1.5);\\n}\\n\\n/* Enhanced HUD Card System */\\n.hud-card {\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  border-radius: calc(var(--base-border-radius) * 1.5);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  box-shadow: var(--glass-shadow);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.hud-card::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(90deg, transparent, var(--accent-blue), transparent);\\n  opacity: 0.6;\\n}\\n\\n.hud-card:hover {\\n  border-color: var(--glass-border-glow);\\n  box-shadow: 0 8px 40px rgba(59, 130, 246, 0.15), var(--glass-shadow);\\n  transform: translateY(-2px);\\n}\\n\\n/* Compact HUD card padding */\\n.hud-card .ant-card-head {\\n  padding: calc(var(--base-spacing) * 0.75) calc(var(--base-spacing) * 1.5) !important;\\n  min-height: auto !important;\\n}\\n\\n.hud-card .ant-card-body {\\n  padding: calc(var(--base-spacing) * 1) !important;\\n}\\n\\n/* Compact metric cards */\\n.metric-card {\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  border-radius: var(--base-border-radius);\\n  padding: calc(var(--base-spacing) * 0.3);\\n  text-align: center;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n  min-height: 60px;\\n  max-height: 80px;\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  box-shadow: var(--glass-shadow);\\n}\\n\\n.metric-card::after {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(0, 207, 255, 0.05) 0%, rgba(0, 255, 189, 0.05) 100%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.metric-card:hover::after {\\n  opacity: 1;\\n}\\n\\n.metric-card:hover {\\n  border-color: var(--accent-blue);\\n  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);\\n  transform: scale(1.02);\\n}\\n\\n/* Compact metric card text */\\n.metric-card p {\\n  margin-bottom: calc(var(--base-spacing) * 0.1) !important;\\n  line-height: 1.1;\\n  font-size: calc(var(--base-font-size) * 0.7) !important;\\n  color: var(--text-secondary) !important;\\n}\\n\\n.metric-card .text-xl {\\n  font-size: calc(var(--base-font-size) * 1.1) !important;\\n  line-height: 1.0;\\n  margin-bottom: calc(var(--base-spacing) * 0.1) !important;\\n  color: var(--text-primary) !important;\\n  font-weight: 700 !important;\\n}\\n\\n/* Compact metric card icons */\\n.metric-card .w-10.h-10 {\\n  width: calc(var(--base-icon-size) * 1.5) !important;\\n  height: calc(var(--base-icon-size) * 1.5) !important;\\n  margin-bottom: calc(var(--base-spacing) * 0.5) !important;\\n}\\n\\n/* Beautiful gradient animation */\\n@keyframes gradientShift {\\n  0% { background-position: 0% 50%; }\\n  50% { background-position: 100% 50%; }\\n  100% { background-position: 0% 50%; }\\n}\\n\\n/* Status badges with glow effects */\\n.status-badge {\\n  padding: calc(var(--base-spacing) * 0.5) calc(var(--base-spacing) * 1);\\n  border-radius: calc(var(--base-border-radius) * 2);\\n  font-size: calc(10px * var(--content-scale));\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  border: 1px solid;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.status-badge.active {\\n  background: rgba(16, 185, 129, 0.2);\\n  color: var(--accent-green);\\n  border-color: var(--accent-green);\\n  box-shadow: 0 0 10px rgba(16, 185, 129, 0.3);\\n}\\n\\n.status-badge.beta {\\n  background: rgba(245, 158, 11, 0.2);\\n  color: var(--accent-yellow);\\n  border-color: var(--accent-yellow);\\n  box-shadow: 0 0 10px rgba(245, 158, 11, 0.3);\\n}\\n\\n.status-badge.live {\\n  background: rgba(59, 130, 246, 0.2);\\n  color: var(--accent-blue);\\n  border-color: var(--accent-blue);\\n  box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);\\n}\\n\\n.status-badge.testing {\\n  background: rgba(139, 92, 246, 0.2);\\n  color: var(--accent-purple);\\n  border-color: var(--accent-purple);\\n  box-shadow: 0 0 10px rgba(139, 92, 246, 0.3);\\n}\\n\\n/* Enhanced Header Styling */\\n.header-hud {\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n  position: relative;\\n}\\n\\n.header-hud::before {\\n  content: '';\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(90deg, transparent, var(--accent-blue), transparent);\\n  opacity: 0.4;\\n}\\n\\n/* Enhanced Department Buttons */\\n.dept-button {\\n  background: var(--glass-bg-light);\\n  border: 1px solid var(--glass-border);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.dept-button:hover {\\n  background: rgba(59, 130, 246, 0.1);\\n  border-color: var(--glass-border-glow);\\n  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);\\n  transform: scale(1.05);\\n}\\n\\n.dept-button.active {\\n  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(6, 182, 212, 0.1) 100%);\\n  border-color: var(--accent-blue);\\n  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);\\n}\\n\\n/* Panel Background Utilities */\\n.bg-panel-bg {\\n  background: var(--glass-bg) !important;\\n}\\n\\n.bg-container-bg {\\n  background: var(--secondary-bg) !important;\\n}\\n\\n.dept-button.active::after {\\n  content: '';\\n  position: absolute;\\n  inset: 0;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\\n  pointer-events: none;\\n}\\n\\n/* Dashboard Auto-Scale - Scales ALL content including text, icons, spacing */\\n.dashboard-auto-scale {\\n  transform-origin: top left;\\n  width: 100vw !important;\\n  height: 100vh !important;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  overflow: hidden;\\n  transition: transform 0.3s ease-out;\\n}\\n\\n/* Scale everything - content, text, icons, spacing */\\n@media (max-width: 1920px) and (min-width: 1600px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.9);\\n    width: calc(100vw / 0.9) !important;\\n    height: calc(100vh / 0.9) !important;\\n  }\\n  :root {\\n    --content-scale: 0.9;\\n  }\\n}\\n\\n@media (max-width: 1599px) and (min-width: 1400px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.8);\\n    width: calc(100vw / 0.8) !important;\\n    height: calc(100vh / 0.8) !important;\\n  }\\n  :root {\\n    --content-scale: 0.8;\\n  }\\n}\\n\\n@media (max-width: 1399px) and (min-width: 1200px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.7);\\n    width: calc(100vw / 0.7) !important;\\n    height: calc(100vh / 0.7) !important;\\n  }\\n  :root {\\n    --content-scale: 0.7;\\n  }\\n}\\n\\n@media (max-width: 1199px) and (min-width: 1000px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.6);\\n    width: calc(100vw / 0.6) !important;\\n    height: calc(100vh / 0.6) !important;\\n  }\\n  :root {\\n    --content-scale: 0.6;\\n  }\\n}\\n\\n@media (max-width: 999px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.5);\\n    width: calc(100vw / 0.5) !important;\\n    height: calc(100vh / 0.5) !important;\\n  }\\n  :root {\\n    --content-scale: 0.5;\\n  }\\n}\\n\\n/* Default content scale */\\n:root {\\n  --content-scale: 1;\\n}\\n\\n/* Universal content scaling - applies to ALL elements */\\n* {\\n  font-size: inherit;\\n}\\n\\n/* Scale all text elements in main content */\\n.main-section h1, .main-section h2, .main-section h3, .main-section h4, .main-section h5, .main-section h6,\\n.main-section p, .main-section span, .main-section div, .main-section button,\\n.main-section input, .main-section textarea, .main-section select, .main-section label, .main-section a {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale card content specifically */\\n.card-content * {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale chart text and numbers */\\n.recharts-text, .recharts-label, .chart-text, .metric-value, .metric-label {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale all icons and images */\\nsvg, img, .icon {\\n  width: calc(var(--base-icon-size));\\n  height: calc(var(--base-icon-size));\\n}\\n\\n/* Scale all spacing */\\n.p-1 { padding: calc(4px * var(--content-scale)) !important; }\\n.p-2 { padding: calc(8px * var(--content-scale)) !important; }\\n.p-3 { padding: calc(12px * var(--content-scale)) !important; }\\n.p-4 { padding: calc(16px * var(--content-scale)) !important; }\\n.p-5 { padding: calc(20px * var(--content-scale)) !important; }\\n.p-6 { padding: calc(24px * var(--content-scale)) !important; }\\n\\n.m-1 { margin: calc(4px * var(--content-scale)) !important; }\\n.m-2 { margin: calc(8px * var(--content-scale)) !important; }\\n.m-3 { margin: calc(12px * var(--content-scale)) !important; }\\n.m-4 { margin: calc(16px * var(--content-scale)) !important; }\\n.m-5 { margin: calc(20px * var(--content-scale)) !important; }\\n.m-6 { margin: calc(24px * var(--content-scale)) !important; }\\n\\n/* Scale gaps */\\n.gap-1 { gap: calc(4px * var(--content-scale)) !important; }\\n.gap-2 { gap: calc(8px * var(--content-scale)) !important; }\\n.gap-3 { gap: calc(12px * var(--content-scale)) !important; }\\n.gap-4 { gap: calc(16px * var(--content-scale)) !important; }\\n.gap-5 { gap: calc(20px * var(--content-scale)) !important; }\\n.gap-6 { gap: calc(24px * var(--content-scale)) !important; }\\n\\n/* Scale border radius */\\n.rounded { border-radius: calc(4px * var(--content-scale)) !important; }\\n.rounded-md { border-radius: calc(6px * var(--content-scale)) !important; }\\n.rounded-lg { border-radius: calc(8px * var(--content-scale)) !important; }\\n.rounded-xl { border-radius: calc(12px * var(--content-scale)) !important; }\\n.rounded-2xl { border-radius: calc(16px * var(--content-scale)) !important; }\\n\\n@keyframes fade-in-up {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n.animate-fade-in-up {\\n  animation: fade-in-up 0.3s ease-out forwards;\\n}\\n\\n@keyframes fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n.animate-fadeIn {\\n  animation: fadeIn 0.5s ease-in-out;\\n}\\n\\n/* Override Tailwind text sizes with content scaling */\\n.text-xs { font-size: calc(12px * var(--content-scale)) !important; }\\n.text-sm { font-size: calc(14px * var(--content-scale)) !important; }\\n.text-base { font-size: calc(16px * var(--content-scale)) !important; }\\n.text-lg { font-size: calc(18px * var(--content-scale)) !important; }\\n.text-xl { font-size: calc(20px * var(--content-scale)) !important; }\\n.text-2xl { font-size: calc(24px * var(--content-scale)) !important; }\\n.text-3xl { font-size: calc(30px * var(--content-scale)) !important; }\\n.text-4xl { font-size: calc(36px * var(--content-scale)) !important; }\\n\\n/* Override Tailwind width/height for icons */\\n.w-3 { width: calc(12px * var(--content-scale)) !important; }\\n.w-4 { width: calc(16px * var(--content-scale)) !important; }\\n.w-5 { width: calc(20px * var(--content-scale)) !important; }\\n.w-6 { width: calc(24px * var(--content-scale)) !important; }\\n.w-8 { width: calc(32px * var(--content-scale)) !important; }\\n.w-10 { width: calc(40px * var(--content-scale)) !important; }\\n.w-12 { width: calc(48px * var(--content-scale)) !important; }\\n\\n.h-3 { height: calc(12px * var(--content-scale)) !important; }\\n.h-4 { height: calc(16px * var(--content-scale)) !important; }\\n.h-5 { height: calc(20px * var(--content-scale)) !important; }\\n.h-6 { height: calc(24px * var(--content-scale)) !important; }\\n.h-8 { height: calc(32px * var(--content-scale)) !important; }\\n.h-10 { height: calc(40px * var(--content-scale)) !important; }\\n.h-12 { height: calc(48px * var(--content-scale)) !important; }\\n\\n/* Optimized Layout Classes for Perfect Content Fit */\\n.main-layout {\\n  height: 100vh;\\n  width: 100vw;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.content-area {\\n  flex: 1;\\n  display: flex;\\n  min-height: 0;\\n  overflow: hidden;\\n}\\n\\n.main-content {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 0;\\n  overflow: hidden;\\n}\\n\\n.header-section {\\n  height: var(--header-height);\\n  flex-shrink: 0;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.main-section {\\n  flex: 1;\\n  min-height: 0;\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.footer-section {\\n  height: calc(var(--footer-height) * 1.5);\\n  flex-shrink: 0;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.sidebar-left {\\n  width: var(--sidebar-width);\\n  flex-shrink: 0;\\n}\\n\\n.sidebar-right {\\n  width: var(--sidebar-width);\\n  flex-shrink: 0;\\n}\\n\\n/* Enhanced Dashboard Grid for No-Scroll Layout */\\n.dashboard-grid {\\n  display: grid;\\n  gap: calc(var(--base-gap) * 0.75);\\n  height: 100%;\\n  overflow: hidden;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.dashboard-grid-teacher {\\n  grid-template-rows: auto 1fr auto;\\n  grid-template-columns: 1fr;\\n}\\n\\n.dashboard-grid-school {\\n  grid-template-rows: auto 1fr;\\n  grid-template-columns: 1fr 1fr;\\n  gap: calc(var(--base-gap) * 1);\\n}\\n\\n/* Compact content areas */\\n.content-compact {\\n  max-height: calc(100vh - var(--header-height) - var(--footer-height) - calc(var(--base-spacing) * 4));\\n  overflow-y: auto;\\n}\\n\\n.content-no-scroll {\\n  height: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n/* Enhanced Animations */\\n@keyframes pulse-glow {\\n  0%, 100% {\\n    box-shadow: 0 0 5px rgba(0, 207, 255, 0.3);\\n  }\\n  50% {\\n    box-shadow: 0 0 20px rgba(0, 207, 255, 0.6), 0 0 30px rgba(0, 207, 255, 0.4);\\n  }\\n}\\n\\n@keyframes slide-in-up {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes fade-in-scale {\\n  from {\\n    opacity: 0;\\n    transform: scale(0.95);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n}\\n\\n.animate-pulse-glow {\\n  animation: pulse-glow 2s ease-in-out infinite;\\n}\\n\\n.animate-slide-in-up {\\n  animation: slide-in-up 0.5s ease-out;\\n}\\n\\n.animate-fade-in-scale {\\n  animation: fade-in-scale 0.3s ease-out;\\n}\\n\\n/* Hover Effects */\\n.hover-lift {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.hover-lift:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(59, 130, 246, 0.2);\\n}\\n\\n/* Progress Bar Animations */\\n.progress-bar {\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.progress-bar::after {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\\n  animation: shimmer 2s infinite;\\n}\\n\\n@keyframes shimmer {\\n  0% {\\n    left: -100%;\\n  }\\n  100% {\\n    left: 100%;\\n  }\\n}\\n\\n/* Optimized Card Grid */\\n.card-grid {\\n  display: grid;\\n  gap: var(--base-gap);\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n.card-grid-2 {\\n  grid-template-columns: 1fr 1fr;\\n}\\n\\n.card-grid-3 {\\n  grid-template-columns: 1fr 1fr 1fr;\\n}\\n\\n.card-grid-4 {\\n  grid-template-columns: 1fr 1fr 1fr 1fr;\\n}\\n\\n.card-grid-auto {\\n  grid-template-columns: repeat(auto-fit, minmax(calc(250px * var(--content-scale)), 1fr));\\n}\\n\\n/* Optimized Card Sizing */\\n.card-compact {\\n  padding: calc(var(--base-card-padding) * 0.75);\\n  min-height: calc(120px * var(--content-scale));\\n}\\n\\n.card-standard {\\n  padding: var(--base-card-padding);\\n  min-height: calc(160px * var(--content-scale));\\n}\\n\\n.card-large {\\n  padding: calc(var(--base-card-padding) * 1.25);\\n  min-height: calc(200px * var(--content-scale));\\n}\\n\\n/* Neumorphic HUD Subnav Styles */\\n.subnav-hud {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  padding: calc(var(--base-spacing) * 1.5) 0;\\n  margin: 0 auto;\\n  max-width: calc(72px * 8 + var(--base-gap) * 7); /* 8 buttons with gaps */\\n}\\n\\n.subnav-button {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  width: calc(72px * var(--content-scale));\\n  height: calc(72px * var(--content-scale));\\n  margin: 0 calc(var(--base-gap) * 0.5);\\n  border-radius: calc(16px * var(--content-scale));\\n  background: var(--glass-bg);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  box-shadow: var(--glass-shadow);\\n  border: 1px solid var(--glass-border);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.subnav-button:hover {\\n  transform: translateY(calc(-2px * var(--content-scale)));\\n  background: var(--glass-bg-light);\\n  border-color: var(--glass-border-glow);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 2px 8px rgba(59, 130, 246, 0.2);\\n}\\n\\n.subnav-button.active {\\n  background: var(--glass-bg-light);\\n  border: 1px solid var(--accent-blue);\\n  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3), var(--glass-shadow);\\n}\\n\\n.subnav-icon {\\n  width: calc(24px * var(--content-scale));\\n  height: calc(24px * var(--content-scale));\\n  margin-bottom: calc(4px * var(--content-scale));\\n  color: var(--text-muted);\\n  transition: color 0.3s ease;\\n}\\n\\n.subnav-button:hover .subnav-icon,\\n.subnav-button.active .subnav-icon {\\n  color: var(--accent-blue);\\n}\\n\\n.subnav-label {\\n  font-size: calc(10px * var(--content-scale));\\n  font-weight: 500;\\n  color: var(--text-muted);\\n  text-align: center;\\n  line-height: 1.2;\\n  transition: color 0.3s ease;\\n}\\n\\n.subnav-button:hover .subnav-label,\\n.subnav-button.active .subnav-label {\\n  color: var(--accent-blue);\\n}\\n\\n/* HUD Glow Effect */\\n.subnav-button::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(circle at center, rgba(59, 130, 246, 0.1) 0%, transparent 70%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n  border-radius: inherit;\\n}\\n\\n.subnav-button:hover::before,\\n.subnav-button.active::before {\\n  opacity: 1;\\n}\\n\\n/* Collapsible Sidebar Styles */\\n.sidebar-toggle {\\n  position: fixed;\\n  top: calc(var(--base-spacing) * 2);\\n  left: calc(var(--base-spacing) * 2);\\n  z-index: 50;\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border-radius: var(--base-border-radius);\\n  padding: calc(var(--base-spacing) * 0.75);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  box-shadow: var(--glass-shadow);\\n}\\n\\n.sidebar-toggle:hover {\\n  background: var(--glass-bg-light);\\n  transform: scale(1.05);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 2px 8px rgba(59, 130, 246, 0.2);\\n}\\n\\n.collapsible-sidebar {\\n  position: fixed;\\n  left: 0;\\n  top: 0;\\n  height: 100vh;\\n  background: var(--glass-bg);\\n  border-right: 1px solid var(--glass-border);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  z-index: 40;\\n  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  box-shadow: var(--glass-shadow);\\n  width: calc(280px * var(--content-scale));\\n}\\n\\n.collapsible-sidebar.collapsed {\\n  transform: translateX(-100%);\\n}\\n\\n.collapsible-sidebar.expanded {\\n  transform: translateX(0);\\n}\\n\\n.sidebar-overlay {\\n  position: fixed;\\n  inset: 0;\\n  background: rgba(0, 0, 0, 0.2);\\n  -webkit-backdrop-filter: blur(2px);\\n          backdrop-filter: blur(2px);\\n  z-index: 30;\\n}\\n\\n/* Function Button Styles */\\n.function-button {\\n  width: 100%;\\n  display: flex;\\n  align-items: center;\\n  gap: calc(var(--base-spacing) * 2);\\n  padding: calc(var(--base-spacing) * 2);\\n  border-radius: calc(var(--base-border-radius) * 1.5);\\n  background: var(--glass-bg-light);\\n  border: 1px solid var(--glass-border);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.function-button:hover {\\n  background: rgba(59, 130, 246, 0.1);\\n  border-color: var(--glass-border-glow);\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 2px 8px rgba(59, 130, 246, 0.2);\\n}\\n\\n.function-button.active {\\n  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(6, 182, 212, 0.1) 100%);\\n  border-color: var(--accent-blue);\\n  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);\\n}\\n\\n.function-button-icon {\\n  font-size: calc(24px * var(--content-scale));\\n  line-height: 1;\\n}\\n\\n.function-button-content {\\n  flex: 1;\\n  text-align: left;\\n}\\n\\n.function-button-title {\\n  font-weight: 600;\\n  font-size: calc(16px * var(--content-scale));\\n  color: var(--text-primary);\\n  margin-bottom: calc(var(--base-spacing) * 0.25);\\n}\\n\\n.function-button-description {\\n  font-size: calc(12px * var(--content-scale));\\n  color: var(--text-muted);\\n}\\n\\n.function-button.active .function-button-title {\\n  color: var(--accent-blue);\\n}\\n\\n.function-button-indicator {\\n  width: calc(8px * var(--content-scale));\\n  height: calc(8px * var(--content-scale));\\n  border-radius: 50%;\\n  background: transparent;\\n  transition: background-color 0.3s ease;\\n}\\n\\n.function-button.active .function-button-indicator {\\n  background: var(--accent-blue);\\n}\\n\\n/* Enhanced HUD Vertical Layout for Left Sidebar */\\n.subnav-hud-vertical {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: stretch;\\n  justify-content: flex-start;\\n  gap: calc(var(--base-spacing) * 0.5);\\n  padding: calc(var(--base-spacing) * 0.75);\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n/* Enhanced subnav button with glassmorphism */\\n.subnav-hud-vertical .subnav-button {\\n  width: 100%;\\n  height: calc(36px * var(--content-scale));\\n  margin: 0;\\n  flex-shrink: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  padding: calc(var(--base-spacing) * 0.75) calc(var(--base-spacing) * 1);\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  border-radius: var(--base-border-radius);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n  box-shadow: var(--glass-shadow);\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover {\\n  background: rgba(59, 130, 246, 0.1);\\n  border-color: var(--glass-border-glow);\\n  transform: translateX(4px);\\n  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);\\n}\\n\\n.subnav-hud-vertical .subnav-button.active {\\n  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(6, 182, 212, 0.1) 100%);\\n  border-color: var(--accent-blue);\\n  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n}\\n\\n/* Enhanced subnav icon */\\n.subnav-hud-vertical .subnav-icon {\\n  width: calc(18px * var(--content-scale));\\n  height: calc(18px * var(--content-scale));\\n  margin-right: calc(var(--base-spacing) * 1.5);\\n  color: var(--text-secondary);\\n  transition: all 0.3s ease;\\n  flex-shrink: 0;\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover .subnav-icon {\\n  color: var(--accent-blue);\\n  filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.6));\\n}\\n\\n.subnav-hud-vertical .subnav-button.active .subnav-icon {\\n  color: var(--text-primary);\\n  filter: drop-shadow(0 0 12px rgba(59, 130, 246, 0.8));\\n}\\n\\n/* Enhanced subnav label - full text display */\\n.subnav-hud-vertical .subnav-label {\\n  color: var(--text-primary);\\n  font-size: calc(12px * var(--content-scale));\\n  font-weight: 500;\\n  line-height: 1.2;\\n  white-space: nowrap;\\n  transition: all 0.3s ease;\\n  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover .subnav-label {\\n  color: var(--text-primary);\\n  text-shadow: 0 0 8px rgba(59, 130, 246, 0.6);\\n}\\n\\n.subnav-hud-vertical .subnav-button.active .subnav-label {\\n  color: var(--text-primary);\\n  font-weight: 600;\\n  text-shadow: 0 0 12px rgba(59, 130, 246, 0.8);\\n}\\n\\n.hover\\\\:scale-105:hover{\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.hover\\\\:border-blue-400\\\\/30:hover{\\n  border-color: rgb(96 165 250 / 0.3);\\n}\\n\\n.hover\\\\:border-cyan-400\\\\/50:hover{\\n  border-color: rgb(34 211 238 / 0.5);\\n}\\n\\n.hover\\\\:border-cyan-400\\\\/60:hover{\\n  border-color: rgb(34 211 238 / 0.6);\\n}\\n\\n.hover\\\\:border-gray-500\\\\/50:hover{\\n  border-color: rgb(107 114 128 / 0.5);\\n}\\n\\n.hover\\\\:bg-blue-500\\\\/30:hover{\\n  background-color: rgb(59 130 246 / 0.3);\\n}\\n\\n.hover\\\\:bg-cyan-500\\\\/50:hover{\\n  background-color: rgb(6 182 212 / 0.5);\\n}\\n\\n.hover\\\\:bg-gray-700\\\\/30:hover{\\n  background-color: rgb(55 65 81 / 0.3);\\n}\\n\\n.hover\\\\:bg-gray-700\\\\/50:hover{\\n  background-color: rgb(55 65 81 / 0.5);\\n}\\n\\n.hover\\\\:bg-gray-700\\\\/60:hover{\\n  background-color: rgb(55 65 81 / 0.6);\\n}\\n\\n.hover\\\\:bg-gray-800\\\\/40:hover{\\n  background-color: rgb(31 41 55 / 0.4);\\n}\\n\\n.hover\\\\:bg-gray-800\\\\/60:hover{\\n  background-color: rgb(31 41 55 / 0.6);\\n}\\n\\n.hover\\\\:bg-pink-500\\\\/90:hover{\\n  background-color: rgb(236 72 153 / 0.9);\\n}\\n\\n.hover\\\\:bg-white\\\\/30:hover{\\n  background-color: rgb(255 255 255 / 0.3);\\n}\\n\\n.hover\\\\:text-white:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:shadow-lg:hover{\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.hover\\\\:shadow-cyan-400\\\\/20:hover{\\n  --tw-shadow-color: rgb(34 211 238 / 0.2);\\n  --tw-shadow: var(--tw-shadow-colored);\\n}\\n\\n.focus\\\\:outline-none:focus{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n\\n.focus-visible\\\\:ring-1:focus-visible{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n\\n.focus-visible\\\\:ring-2:focus-visible{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n\\n.focus-visible\\\\:ring-brand-cyan:focus-visible{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(0 255 255 / var(--tw-ring-opacity, 1));\\n}\\n\\n.focus-visible\\\\:ring-white:focus-visible{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity, 1));\\n}\\n\\n.focus-visible\\\\:ring-offset-2:focus-visible{\\n  --tw-ring-offset-width: 2px;\\n}\\n\\n.focus-visible\\\\:ring-offset-gray-800:focus-visible{\\n  --tw-ring-offset-color: #1f2937;\\n}\\n\\n.disabled\\\\:cursor-not-allowed:disabled{\\n  cursor: not-allowed;\\n}\\n\\n.disabled\\\\:cursor-wait:disabled{\\n  cursor: wait;\\n}\\n\\n.disabled\\\\:opacity-50:disabled{\\n  opacity: 0.5;\\n}\\n\\n.group:hover .group-hover\\\\:scale-105{\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.group:hover .group-hover\\\\:scale-110{\\n  --tw-scale-x: 1.1;\\n  --tw-scale-y: 1.1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.group:hover .group-hover\\\\:text-cyan-300{\\n  --tw-text-opacity: 1;\\n  color: rgb(103 232 249 / var(--tw-text-opacity, 1));\\n}\\n\\n.group:hover .group-hover\\\\:text-white{\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n\\n.group:hover .group-hover\\\\:opacity-20{\\n  opacity: 0.2;\\n}\\n\\n@media (prefers-reduced-motion: no-preference){\\n\\n  @keyframes pulse{\\n\\n    50%{\\n      opacity: .5;\\n    }\\n  }\\n\\n  .motion-safe\\\\:animate-pulse{\\n    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n  }\\n}\\n\\n@media (min-width: 640px){\\n\\n  .sm\\\\:mr-3{\\n    margin-right: 0.75rem;\\n  }\\n\\n  .sm\\\\:mt-4{\\n    margin-top: 1rem;\\n  }\\n\\n  .sm\\\\:h-10{\\n    height: 2.5rem;\\n  }\\n\\n  .sm\\\\:h-12{\\n    height: 3rem;\\n  }\\n\\n  .sm\\\\:h-16{\\n    height: 4rem;\\n  }\\n\\n  .sm\\\\:h-3{\\n    height: 0.75rem;\\n  }\\n\\n  .sm\\\\:h-36{\\n    height: 9rem;\\n  }\\n\\n  .sm\\\\:h-4{\\n    height: 1rem;\\n  }\\n\\n  .sm\\\\:h-40{\\n    height: 10rem;\\n  }\\n\\n  .sm\\\\:h-48{\\n    height: 12rem;\\n  }\\n\\n  .sm\\\\:h-6{\\n    height: 1.5rem;\\n  }\\n\\n  .sm\\\\:h-8{\\n    height: 2rem;\\n  }\\n\\n  .sm\\\\:w-10{\\n    width: 2.5rem;\\n  }\\n\\n  .sm\\\\:w-12{\\n    width: 3rem;\\n  }\\n\\n  .sm\\\\:w-16{\\n    width: 4rem;\\n  }\\n\\n  .sm\\\\:w-3{\\n    width: 0.75rem;\\n  }\\n\\n  .sm\\\\:w-36{\\n    width: 9rem;\\n  }\\n\\n  .sm\\\\:w-4{\\n    width: 1rem;\\n  }\\n\\n  .sm\\\\:w-40{\\n    width: 10rem;\\n  }\\n\\n  .sm\\\\:w-6{\\n    width: 1.5rem;\\n  }\\n\\n  .sm\\\\:w-8{\\n    width: 2rem;\\n  }\\n\\n  .sm\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .sm\\\\:gap-4{\\n    gap: 1rem;\\n  }\\n\\n  .sm\\\\:gap-6{\\n    gap: 1.5rem;\\n  }\\n\\n  .sm\\\\:p-2{\\n    padding: 0.5rem;\\n  }\\n\\n  .sm\\\\:p-3{\\n    padding: 0.75rem;\\n  }\\n\\n  .sm\\\\:p-4{\\n    padding: 1rem;\\n  }\\n\\n  .sm\\\\:p-6{\\n    padding: 1.5rem;\\n  }\\n\\n  .sm\\\\:px-4{\\n    padding-left: 1rem;\\n    padding-right: 1rem;\\n  }\\n\\n  .sm\\\\:py-2{\\n    padding-top: 0.5rem;\\n    padding-bottom: 0.5rem;\\n  }\\n\\n  .sm\\\\:text-base{\\n    font-size: 1rem;\\n    line-height: 1.5rem;\\n  }\\n\\n  .sm\\\\:text-lg{\\n    font-size: 1.125rem;\\n    line-height: 1.75rem;\\n  }\\n\\n  .sm\\\\:text-sm{\\n    font-size: 0.875rem;\\n    line-height: 1.25rem;\\n  }\\n\\n  .sm\\\\:text-xl{\\n    font-size: 1.25rem;\\n    line-height: 1.75rem;\\n  }\\n\\n  .sm\\\\:text-xs{\\n    font-size: 0.75rem;\\n    line-height: 1rem;\\n  }\\n}\\n\\n@media (min-width: 768px){\\n\\n  .md\\\\:col-span-2{\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .md\\\\:grid-cols-2{\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-4{\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n}\\n\\n@media (min-width: 1024px){\\n\\n  .lg\\\\:col-span-12{\\n    grid-column: span 12 / span 12;\\n  }\\n\\n  .lg\\\\:col-span-2{\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .lg\\\\:col-span-3{\\n    grid-column: span 3 / span 3;\\n  }\\n\\n  .lg\\\\:col-span-4{\\n    grid-column: span 4 / span 4;\\n  }\\n\\n  .lg\\\\:col-span-5{\\n    grid-column: span 5 / span 5;\\n  }\\n\\n  .lg\\\\:col-span-7{\\n    grid-column: span 7 / span 7;\\n  }\\n\\n  .lg\\\\:h-40{\\n    height: 10rem;\\n  }\\n\\n  .lg\\\\:h-48{\\n    height: 12rem;\\n  }\\n\\n  .lg\\\\:w-40{\\n    width: 10rem;\\n  }\\n\\n  .lg\\\\:w-48{\\n    width: 12rem;\\n  }\\n\\n  .lg\\\\:grid-cols-12{\\n    grid-template-columns: repeat(12, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-4{\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:text-xl{\\n    font-size: 1.25rem;\\n    line-height: 1.75rem;\\n  }\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA,qGAAqG;;AAErG;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;;CAAc;;AAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,+HAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,+GAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;;AAAd;EAAA,aAAc;AAAA;AACd;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;;EAAA;IAAA;EAAoB;;EAApB;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;;EAApB;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;;EAApB;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;;EAApB;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;;EAApB;IAAA;EAAoB;AAAA;AACpB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,yBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,2EAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yDAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;KAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,mCAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,8FAAmB;EAAnB;AAAmB;AAAnB;EAAA,gDAAmB;EAAnB,6DAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,6EAAmB;EAAnB,iGAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,2GAAmB;EAAnB,yGAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kGAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,yBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,gKAAmB;EAAnB,wJAAmB;EAAnB,iLAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;;AAEnB,6EAA6E;AAC7E;EACE,iBAAiB;EACjB,kBAAkB;EAClB,mDAAmD;EACnD,gDAAgD;EAChD,sDAAsD;EACtD,mDAAmD;EACnD,uDAAuD;EACvD,qDAAqD;EACrD,4CAA4C;EAC5C,kDAAkD;EAClD,kDAAkD;EAClD,kDAAkD;;EAElD,qCAAqC;EACrC,qBAAqB;EACrB,uBAAuB;EACvB,sBAAsB;;EAEtB,6BAA6B;EAC7B,iCAAiC;EACjC,uCAAuC;EACvC,wCAAwC;EACxC,6CAA6C;EAC7C,2EAA2E;;EAE3E,2BAA2B;EAC3B,uBAAuB;EACvB,yBAAyB;EACzB,qBAAqB;EACrB,sBAAsB;;EAEtB,0BAA0B;EAC1B,sBAAsB;EACtB,sBAAsB;EACtB,uBAAuB;EACvB,wBAAwB;EACxB,wBAAwB;EACxB,qBAAqB;EACrB,wBAAwB;EACxB,sBAAsB;AACxB;;AAEA,kEAAkE;AAClE;EACE;IACE,mBAAmB;IACnB,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,mBAAmB;IACnB,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,mBAAmB;IACnB,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,mBAAmB;IACnB,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,mBAAmB;IACnB,oBAAoB;EACtB;AACF;;AAEA,mCAAmC;AACnC;EACE,sBAAsB;EACtB,SAAS;EACT,UAAU;AACZ;;AAEA;EACE,aAAa;EACb,YAAY;EACZ,gBAAgB;EAChB,SAAS;EACT,UAAU;EACV,6BAA6B;EAC7B,0BAA0B;EAC1B,kCAAkC;AACpC;;AAEA;EACE,kCAAkC;EAClC,gCAAgC;EAChC,gBAAgB;EAChB,6BAA6B;EAC7B,0BAA0B;AAC5B;;AAEA;EACE,aAAa;EACb,YAAY;EACZ,gBAAgB;AAClB;;AAEA;EACE,2GAA2G;EAC3G,4BAA4B;EAC5B,kBAAkB;AACpB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT;;6FAE2F;EAC3F,oBAAoB;AACtB;;AAEA,8BAA8B;AAC9B;EACE,gCAAgC;AAClC;;AAEA;EACE,8CAA8C;AAChD;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,8CAA8C;AAChD;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4BAA4B;AAC9B;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;EACE,oBAAoB;AACtB;;AAEA;EACE,gCAAgC;AAClC;;AAEA;EACE,gCAAgC;AAClC;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;EACE,4BAA4B;EAC5B,6BAA6B;AAC/B;;AAEA;EACE,iCAAiC;EACjC,8BAA8B;EAC9B,gCAAgC;EAChC,wCAAwC;AAC1C;;AAEA;EACE,iCAAiC;EACjC,oDAAoD;AACtD;;AAEA,6BAA6B;AAC7B;EACE,2BAA2B;EAC3B,qCAAqC;EACrC,oDAAoD;EACpD,mCAA2B;UAA3B,2BAA2B;EAC3B,+BAA+B;EAC/B,iDAAiD;EACjD,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,WAAW;EACX,gFAAgF;EAChF,YAAY;AACd;;AAEA;EACE,sCAAsC;EACtC,oEAAoE;EACpE,2BAA2B;AAC7B;;AAEA,6BAA6B;AAC7B;EACE,oFAAoF;EACpF,2BAA2B;AAC7B;;AAEA;EACE,iDAAiD;AACnD;;AAEA,yBAAyB;AACzB;EACE,2BAA2B;EAC3B,qCAAqC;EACrC,wCAAwC;EACxC,wCAAwC;EACxC,kBAAkB;EAClB,yBAAyB;EACzB,kBAAkB;EAClB,gBAAgB;EAChB,gBAAgB;EAChB,gBAAgB;EAChB,wBAAmB;EAAnB,mBAAmB;EACnB,mCAA2B;UAA3B,2BAA2B;EAC3B,+BAA+B;AACjC;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT,6FAA6F;EAC7F,UAAU;EACV,6BAA6B;AAC/B;;AAEA;EACE,UAAU;AACZ;;AAEA;EACE,gCAAgC;EAChC,8CAA8C;EAC9C,sBAAsB;AACxB;;AAEA,6BAA6B;AAC7B;EACE,yDAAyD;EACzD,gBAAgB;EAChB,uDAAuD;EACvD,uCAAuC;AACzC;;AAEA;EACE,uDAAuD;EACvD,gBAAgB;EAChB,yDAAyD;EACzD,qCAAqC;EACrC,2BAA2B;AAC7B;;AAEA,8BAA8B;AAC9B;EACE,mDAAmD;EACnD,oDAAoD;EACpD,yDAAyD;AAC3D;;AAEA,iCAAiC;AACjC;EACE,KAAK,2BAA2B,EAAE;EAClC,MAAM,6BAA6B,EAAE;EACrC,OAAO,2BAA2B,EAAE;AACtC;;AAEA,oCAAoC;AACpC;EACE,sEAAsE;EACtE,kDAAkD;EAClD,4CAA4C;EAC5C,gBAAgB;EAChB,yBAAyB;EACzB,qBAAqB;EACrB,iBAAiB;EACjB,mCAA2B;UAA3B,2BAA2B;EAC3B,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,mCAAmC;EACnC,0BAA0B;EAC1B,iCAAiC;EACjC,4CAA4C;AAC9C;;AAEA;EACE,mCAAmC;EACnC,2BAA2B;EAC3B,kCAAkC;EAClC,4CAA4C;AAC9C;;AAEA;EACE,mCAAmC;EACnC,yBAAyB;EACzB,gCAAgC;EAChC,4CAA4C;AAC9C;;AAEA;EACE,mCAAmC;EACnC,2BAA2B;EAC3B,kCAAkC;EAClC,4CAA4C;AAC9C;;AAEA,4BAA4B;AAC5B;EACE,2BAA2B;EAC3B,qCAAqC;EACrC,mCAA2B;UAA3B,2BAA2B;EAC3B,yCAAyC;EACzC,kBAAkB;AACpB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,SAAS;EACT,OAAO;EACP,QAAQ;EACR,WAAW;EACX,gFAAgF;EAChF,YAAY;AACd;;AAEA,gCAAgC;AAChC;EACE,iCAAiC;EACjC,qCAAqC;EACrC,mCAA2B;UAA3B,2BAA2B;EAC3B,iDAAiD;EACjD,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,mCAAmC;EACnC,sCAAsC;EACtC,8CAA8C;EAC9C,sBAAsB;AACxB;;AAEA;EACE,4FAA4F;EAC5F,gCAAgC;EAChC,4CAA4C;AAC9C;;AAEA,+BAA+B;AAC/B;EACE,sCAAsC;AACxC;;AAEA;EACE,0CAA0C;AAC5C;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,QAAQ;EACR,iFAAiF;EACjF,oBAAoB;AACtB;;AAEA,6EAA6E;AAC7E;EACE,0BAA0B;EAC1B,uBAAuB;EACvB,wBAAwB;EACxB,eAAe;EACf,MAAM;EACN,OAAO;EACP,gBAAgB;EAChB,mCAAmC;AACrC;;AAEA,qDAAqD;AACrD;EACE;IACE,qBAAqB;IACrB,mCAAmC;IACnC,oCAAoC;EACtC;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,mCAAmC;IACnC,oCAAoC;EACtC;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,mCAAmC;IACnC,oCAAoC;EACtC;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,mCAAmC;IACnC,oCAAoC;EACtC;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,mCAAmC;IACnC,oCAAoC;EACtC;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA,0BAA0B;AAC1B;EACE,kBAAkB;AACpB;;AAEA,wDAAwD;AACxD;EACE,kBAAkB;AACpB;;AAEA,4CAA4C;AAC5C;;;EAGE,wEAAwE;AAC1E;;AAEA,oCAAoC;AACpC;EACE,wEAAwE;AAC1E;;AAEA,iCAAiC;AACjC;EACE,wEAAwE;AAC1E;;AAEA,+BAA+B;AAC/B;EACE,kCAAkC;EAClC,mCAAmC;AACrC;;AAEA,sBAAsB;AACtB,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,qDAAqD,EAAE;AAC9D,OAAO,qDAAqD,EAAE;AAC9D,OAAO,qDAAqD,EAAE;AAC9D,OAAO,qDAAqD,EAAE;;AAE9D,OAAO,mDAAmD,EAAE;AAC5D,OAAO,mDAAmD,EAAE;AAC5D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;;AAE7D,eAAe;AACf,SAAS,gDAAgD,EAAE;AAC3D,SAAS,gDAAgD,EAAE;AAC3D,SAAS,iDAAiD,EAAE;AAC5D,SAAS,iDAAiD,EAAE;AAC5D,SAAS,iDAAiD,EAAE;AAC5D,SAAS,iDAAiD,EAAE;;AAE5D,wBAAwB;AACxB,WAAW,0DAA0D,EAAE;AACvE,cAAc,0DAA0D,EAAE;AAC1E,cAAc,0DAA0D,EAAE;AAC1E,cAAc,2DAA2D,EAAE;AAC3E,eAAe,2DAA2D,EAAE;;AAE5E;EACE;IACE,UAAU;IACV,2BAA2B;EAC7B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE;IACE,UAAU;EACZ;EACA;IACE,UAAU;EACZ;AACF;;AAEA;EACE,kCAAkC;AACpC;;AAEA,sDAAsD;AACtD,WAAW,uDAAuD,EAAE;AACpE,WAAW,uDAAuD,EAAE;AACpE,aAAa,uDAAuD,EAAE;AACtE,WAAW,uDAAuD,EAAE;AACpE,WAAW,uDAAuD,EAAE;AACpE,YAAY,uDAAuD,EAAE;AACrE,YAAY,uDAAuD,EAAE;AACrE,YAAY,uDAAuD,EAAE;;AAErE,6CAA6C;AAC7C,OAAO,mDAAmD,EAAE;AAC5D,OAAO,mDAAmD,EAAE;AAC5D,OAAO,mDAAmD,EAAE;AAC5D,OAAO,mDAAmD,EAAE;AAC5D,OAAO,mDAAmD,EAAE;AAC5D,QAAQ,mDAAmD,EAAE;AAC7D,QAAQ,mDAAmD,EAAE;;AAE7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,QAAQ,oDAAoD,EAAE;AAC9D,QAAQ,oDAAoD,EAAE;;AAE9D,qDAAqD;AACrD;EACE,aAAa;EACb,YAAY;EACZ,aAAa;EACb,sBAAsB;EACtB,gBAAgB;AAClB;;AAEA;EACE,OAAO;EACP,aAAa;EACb,aAAa;EACb,gBAAgB;AAClB;;AAEA;EACE,OAAO;EACP,aAAa;EACb,sBAAsB;EACtB,aAAa;EACb,gBAAgB;AAClB;;AAEA;EACE,4BAA4B;EAC5B,cAAc;EACd,wCAAwC;AAC1C;;AAEA;EACE,OAAO;EACP,aAAa;EACb,gBAAgB;EAChB,kBAAkB;EAClB,wCAAwC;AAC1C;;AAEA;EACE,wCAAwC;EACxC,cAAc;EACd,wCAAwC;AAC1C;;AAEA;EACE,2BAA2B;EAC3B,cAAc;AAChB;;AAEA;EACE,2BAA2B;EAC3B,cAAc;AAChB;;AAEA,iDAAiD;AACjD;EACE,aAAa;EACb,iCAAiC;EACjC,YAAY;EACZ,gBAAgB;EAChB,wCAAwC;AAC1C;;AAEA;EACE,iCAAiC;EACjC,0BAA0B;AAC5B;;AAEA;EACE,4BAA4B;EAC5B,8BAA8B;EAC9B,8BAA8B;AAChC;;AAEA,0BAA0B;AAC1B;EACE,qGAAqG;EACrG,gBAAgB;AAClB;;AAEA;EACE,YAAY;EACZ,gBAAgB;EAChB,aAAa;EACb,sBAAsB;AACxB;;AAEA,wBAAwB;AACxB;EACE;IACE,0CAA0C;EAC5C;EACA;IACE,4EAA4E;EAC9E;AACF;;AAEA;EACE;IACE,UAAU;IACV,2BAA2B;EAC7B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE;IACE,UAAU;IACV,sBAAsB;EACxB;EACA;IACE,UAAU;IACV,mBAAmB;EACrB;AACF;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,oCAAoC;AACtC;;AAEA;EACE,sCAAsC;AACxC;;AAEA,kBAAkB;AAClB;EACE,iDAAiD;AACnD;;AAEA;EACE,2BAA2B;EAC3B,2EAA2E;AAC7E;;AAEA,4BAA4B;AAC5B;EACE,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,WAAW;EACX,WAAW;EACX,YAAY;EACZ,sFAAsF;EACtF,8BAA8B;AAChC;;AAEA;EACE;IACE,WAAW;EACb;EACA;IACE,UAAU;EACZ;AACF;;AAEA,wBAAwB;AACxB;EACE,aAAa;EACb,oBAAoB;EACpB,YAAY;EACZ,gBAAgB;AAClB;;AAEA;EACE,8BAA8B;AAChC;;AAEA;EACE,kCAAkC;AACpC;;AAEA;EACE,sCAAsC;AACxC;;AAEA;EACE,wFAAwF;AAC1F;;AAEA,0BAA0B;AAC1B;EACE,8CAA8C;EAC9C,8CAA8C;AAChD;;AAEA;EACE,iCAAiC;EACjC,8CAA8C;AAChD;;AAEA;EACE,8CAA8C;EAC9C,8CAA8C;AAChD;;AAEA,iCAAiC;AACjC;EACE,aAAa;EACb,uBAAuB;EACvB,mBAAmB;EACnB,0CAA0C;EAC1C,cAAc;EACd,+CAA+C,EAAE,wBAAwB;AAC3E;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,mBAAmB;EACnB,uBAAuB;EACvB,wCAAwC;EACxC,yCAAyC;EACzC,qCAAqC;EACrC,gDAAgD;EAChD,2BAA2B;EAC3B,mCAA2B;UAA3B,2BAA2B;EAC3B,+BAA+B;EAC/B,qCAAqC;EACrC,yBAAyB;EACzB,eAAe;EACf,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,wDAAwD;EACxD,iCAAiC;EACjC,sCAAsC;EACtC,4EAA4E;AAC9E;;AAEA;EACE,iCAAiC;EACjC,oCAAoC;EACpC,iEAAiE;AACnE;;AAEA;EACE,wCAAwC;EACxC,yCAAyC;EACzC,+CAA+C;EAC/C,wBAAwB;EACxB,2BAA2B;AAC7B;;AAEA;;EAEE,yBAAyB;AAC3B;;AAEA;EACE,4CAA4C;EAC5C,gBAAgB;EAChB,wBAAwB;EACxB,kBAAkB;EAClB,gBAAgB;EAChB,2BAA2B;AAC7B;;AAEA;;EAEE,yBAAyB;AAC3B;;AAEA,oBAAoB;AACpB;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT,0FAA0F;EAC1F,UAAU;EACV,6BAA6B;EAC7B,sBAAsB;AACxB;;AAEA;;EAEE,UAAU;AACZ;;AAEA,+BAA+B;AAC/B;EACE,eAAe;EACf,kCAAkC;EAClC,mCAAmC;EACnC,WAAW;EACX,2BAA2B;EAC3B,qCAAqC;EACrC,mCAA2B;UAA3B,2BAA2B;EAC3B,wCAAwC;EACxC,yCAAyC;EACzC,iDAAiD;EACjD,+BAA+B;AACjC;;AAEA;EACE,iCAAiC;EACjC,sBAAsB;EACtB,4EAA4E;AAC9E;;AAEA;EACE,eAAe;EACf,OAAO;EACP,MAAM;EACN,aAAa;EACb,2BAA2B;EAC3B,2CAA2C;EAC3C,mCAA2B;UAA3B,2BAA2B;EAC3B,WAAW;EACX,uDAAuD;EACvD,+BAA+B;EAC/B,yCAAyC;AAC3C;;AAEA;EACE,4BAA4B;AAC9B;;AAEA;EACE,wBAAwB;AAC1B;;AAEA;EACE,eAAe;EACf,QAAQ;EACR,8BAA8B;EAC9B,kCAA0B;UAA1B,0BAA0B;EAC1B,WAAW;AACb;;AAEA,2BAA2B;AAC3B;EACE,WAAW;EACX,aAAa;EACb,mBAAmB;EACnB,kCAAkC;EAClC,sCAAsC;EACtC,oDAAoD;EACpD,iCAAiC;EACjC,qCAAqC;EACrC,iDAAiD;EACjD,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,mCAAmC;EACnC,sCAAsC;EACtC,2BAA2B;EAC3B,4EAA4E;AAC9E;;AAEA;EACE,4FAA4F;EAC5F,gCAAgC;EAChC,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;EAC5C,cAAc;AAChB;;AAEA;EACE,OAAO;EACP,gBAAgB;AAClB;;AAEA;EACE,gBAAgB;EAChB,4CAA4C;EAC5C,0BAA0B;EAC1B,+CAA+C;AACjD;;AAEA;EACE,4CAA4C;EAC5C,wBAAwB;AAC1B;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;EACE,uCAAuC;EACvC,wCAAwC;EACxC,kBAAkB;EAClB,uBAAuB;EACvB,sCAAsC;AACxC;;AAEA;EACE,8BAA8B;AAChC;;AAEA,kDAAkD;AAClD;EACE,aAAa;EACb,sBAAsB;EACtB,oBAAoB;EACpB,2BAA2B;EAC3B,oCAAoC;EACpC,yCAAyC;EACzC,YAAY;EACZ,gBAAgB;AAClB;;AAEA,8CAA8C;AAC9C;EACE,WAAW;EACX,yCAAyC;EACzC,SAAS;EACT,cAAc;EACd,aAAa;EACb,mBAAmB;EACnB,2BAA2B;EAC3B,uEAAuE;EACvE,2BAA2B;EAC3B,qCAAqC;EACrC,wCAAwC;EACxC,mCAA2B;UAA3B,2BAA2B;EAC3B,iDAAiD;EACjD,kBAAkB;EAClB,gBAAgB;EAChB,+BAA+B;AACjC;;AAEA;EACE,mCAAmC;EACnC,sCAAsC;EACtC,0BAA0B;EAC1B,8CAA8C;AAChD;;AAEA;EACE,4FAA4F;EAC5F,gCAAgC;EAChC,oFAAoF;AACtF;;AAEA,yBAAyB;AACzB;EACE,wCAAwC;EACxC,yCAAyC;EACzC,6CAA6C;EAC7C,4BAA4B;EAC5B,yBAAyB;EACzB,cAAc;AAChB;;AAEA;EACE,yBAAyB;EACzB,oDAAoD;AACtD;;AAEA;EACE,0BAA0B;EAC1B,qDAAqD;AACvD;;AAEA,8CAA8C;AAC9C;EACE,0BAA0B;EAC1B,4CAA4C;EAC5C,gBAAgB;EAChB,gBAAgB;EAChB,mBAAmB;EACnB,yBAAyB;EACzB,+CAA+C;AACjD;;AAEA;EACE,0BAA0B;EAC1B,4CAA4C;AAC9C;;AAEA;EACE,0BAA0B;EAC1B,gBAAgB;EAChB,6CAA6C;AAC/C;;AA5kCA;EAAA,kBA6kCA;EA7kCA,kBA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA,oBA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA,+EA6kCA;EA7kCA,mGA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA,wCA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA,8BA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA,2GA6kCA;EA7kCA,yGA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA,2GA6kCA;EA7kCA,yGA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA,oBA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA,oBA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA,kBA6kCA;EA7kCA,kBA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA,iBA6kCA;EA7kCA,iBA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA,oBA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA,oBA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;;EAAA;;IAAA;MAAA;IA6kCA;EAAA;;EA7kCA;IAAA;EA6kCA;AAAA;;AA7kCA;;EAAA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA,kBA6kCA;IA7kCA;EA6kCA;;EA7kCA;IAAA,mBA6kCA;IA7kCA;EA6kCA;;EA7kCA;IAAA,eA6kCA;IA7kCA;EA6kCA;;EA7kCA;IAAA,mBA6kCA;IA7kCA;EA6kCA;;EA7kCA;IAAA,mBA6kCA;IA7kCA;EA6kCA;;EA7kCA;IAAA,kBA6kCA;IA7kCA;EA6kCA;;EA7kCA;IAAA,kBA6kCA;IA7kCA;EA6kCA;AAAA;;AA7kCA;;EAAA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;AAAA;;AA7kCA;;EAAA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA,kBA6kCA;IA7kCA;EA6kCA;AAAA\",\"sourcesContent\":[\"@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');\\n\\n@tailwind base;\\n@tailwind components;\\n@tailwind utilities;\\n\\n/* CSS Custom Properties for Responsive Scaling - Optimized for Content Fit */\\n:root {\\n  --scale-factor: 1;\\n  --content-scale: 3;\\n  --base-font-size: calc(14px * var(--content-scale));\\n  --base-spacing: calc(6px * var(--content-scale));\\n  --base-border-radius: calc(8px * var(--content-scale));\\n  --base-icon-size: calc(20px * var(--content-scale));\\n  --base-button-height: calc(36px * var(--content-scale));\\n  --base-card-padding: calc(1px * var(--content-scale));\\n  --base-gap: calc(6px * var(--content-scale));\\n  --header-height: calc(60px * var(--content-scale));\\n  --footer-height: calc(50px * var(--content-scale));\\n  --sidebar-width: calc(80px * var(--content-scale));\\n\\n  /* Unified Dark Theme Color Palette */\\n  --primary-bg: #0f1419;\\n  --secondary-bg: #1a1f2e;\\n  --tertiary-bg: #252b3d;\\n\\n  /* Glassmorphism Dark Theme */\\n  --glass-bg: rgba(26, 31, 46, 0.7);\\n  --glass-bg-light: rgba(37, 43, 61, 0.6);\\n  --glass-border: rgba(100, 116, 139, 0.2);\\n  --glass-border-glow: rgba(100, 116, 139, 0.4);\\n  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 2px 8px rgba(0, 0, 0, 0.2);\\n\\n  /* Consistent Text Colors */\\n  --text-primary: #f8fafc;\\n  --text-secondary: #cbd5e1;\\n  --text-muted: #94a3b8;\\n  --text-accent: #64748b;\\n\\n  /* Unified Accent Colors */\\n  --accent-blue: #3b82f6;\\n  --accent-cyan: #06b6d4;\\n  --accent-green: #10b981;\\n  --accent-yellow: #f59e0b;\\n  --accent-orange: #f97316;\\n  --accent-red: #ef4444;\\n  --accent-purple: #8b5cf6;\\n  --accent-pink: #ec4899;\\n}\\n\\n/* Responsive scaling variables - now handled by --content-scale */\\n@media (max-width: 1920px) and (min-width: 1600px) {\\n  :root {\\n    --scale-factor: 0.9;\\n    --content-scale: 0.9;\\n  }\\n}\\n\\n@media (max-width: 1599px) and (min-width: 1400px) {\\n  :root {\\n    --scale-factor: 0.8;\\n    --content-scale: 0.8;\\n  }\\n}\\n\\n@media (max-width: 1399px) and (min-width: 1200px) {\\n  :root {\\n    --scale-factor: 0.7;\\n    --content-scale: 0.7;\\n  }\\n}\\n\\n@media (max-width: 1199px) and (min-width: 1000px) {\\n  :root {\\n    --scale-factor: 0.6;\\n    --content-scale: 0.6;\\n  }\\n}\\n\\n@media (max-width: 999px) {\\n  :root {\\n    --scale-factor: 0.5;\\n    --content-scale: 0.5;\\n  }\\n}\\n\\n/* Ensure base styles are applied */\\n* {\\n  box-sizing: border-box;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nhtml, body {\\n  height: 100vh;\\n  width: 100vw;\\n  overflow: hidden;\\n  margin: 0;\\n  padding: 0;\\n  background: var(--primary-bg);\\n  color: var(--text-primary);\\n  font-family: 'Poppins', sans-serif;\\n}\\n\\nbody {\\n  font-family: 'Poppins', sans-serif;\\n  font-size: var(--base-font-size);\\n  line-height: 1.5;\\n  background: var(--primary-bg);\\n  color: var(--text-primary);\\n}\\n\\n#__next {\\n  height: 100vh;\\n  width: 100vw;\\n  overflow: hidden;\\n}\\n\\n.main-background {\\n  background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 50%, var(--tertiary-bg) 100%);\\n  background-attachment: fixed;\\n  position: relative;\\n}\\n\\n.main-background::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(ellipse at 20% 30%, rgba(209, 160, 255, 0.1) 0%, transparent 50%),\\n              radial-gradient(ellipse at 80% 70%, rgba(188, 19, 254, 0.08) 0%, transparent 50%),\\n              radial-gradient(ellipse at 50% 50%, rgba(41, 52, 95, 0.05) 0%, transparent 70%);\\n  pointer-events: none;\\n}\\n\\n/* Global Responsive Classes */\\n.responsive-text {\\n  font-size: var(--base-font-size);\\n}\\n\\n.responsive-text-sm {\\n  font-size: calc(var(--base-font-size) * 0.875);\\n}\\n\\n.responsive-text-xs {\\n  font-size: calc(var(--base-font-size) * 0.75);\\n}\\n\\n.responsive-text-lg {\\n  font-size: calc(var(--base-font-size) * 1.125);\\n}\\n\\n.responsive-text-xl {\\n  font-size: calc(var(--base-font-size) * 1.25);\\n}\\n\\n.responsive-text-2xl {\\n  font-size: calc(var(--base-font-size) * 1.5);\\n}\\n\\n.responsive-spacing {\\n  padding: var(--base-spacing);\\n}\\n\\n.responsive-spacing-sm {\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.responsive-spacing-lg {\\n  padding: calc(var(--base-spacing) * 1.5);\\n}\\n\\n.responsive-gap {\\n  gap: var(--base-gap);\\n}\\n\\n.responsive-gap-sm {\\n  gap: calc(var(--base-gap) * 0.5);\\n}\\n\\n.responsive-gap-lg {\\n  gap: calc(var(--base-gap) * 1.5);\\n}\\n\\n.responsive-border-radius {\\n  border-radius: var(--base-border-radius);\\n}\\n\\n.responsive-icon {\\n  width: var(--base-icon-size);\\n  height: var(--base-icon-size);\\n}\\n\\n.responsive-button {\\n  height: var(--base-button-height);\\n  padding: 0 var(--base-spacing);\\n  font-size: var(--base-font-size);\\n  border-radius: var(--base-border-radius);\\n}\\n\\n.responsive-card {\\n  padding: var(--base-card-padding);\\n  border-radius: calc(var(--base-border-radius) * 1.5);\\n}\\n\\n/* Enhanced HUD Card System */\\n.hud-card {\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  border-radius: calc(var(--base-border-radius) * 1.5);\\n  backdrop-filter: blur(20px);\\n  box-shadow: var(--glass-shadow);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.hud-card::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(90deg, transparent, var(--accent-blue), transparent);\\n  opacity: 0.6;\\n}\\n\\n.hud-card:hover {\\n  border-color: var(--glass-border-glow);\\n  box-shadow: 0 8px 40px rgba(59, 130, 246, 0.15), var(--glass-shadow);\\n  transform: translateY(-2px);\\n}\\n\\n/* Compact HUD card padding */\\n.hud-card .ant-card-head {\\n  padding: calc(var(--base-spacing) * 0.75) calc(var(--base-spacing) * 1.5) !important;\\n  min-height: auto !important;\\n}\\n\\n.hud-card .ant-card-body {\\n  padding: calc(var(--base-spacing) * 1) !important;\\n}\\n\\n/* Compact metric cards */\\n.metric-card {\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  border-radius: var(--base-border-radius);\\n  padding: calc(var(--base-spacing) * 0.3);\\n  text-align: center;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n  min-height: 60px;\\n  max-height: 80px;\\n  height: fit-content;\\n  backdrop-filter: blur(20px);\\n  box-shadow: var(--glass-shadow);\\n}\\n\\n.metric-card::after {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(0, 207, 255, 0.05) 0%, rgba(0, 255, 189, 0.05) 100%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.metric-card:hover::after {\\n  opacity: 1;\\n}\\n\\n.metric-card:hover {\\n  border-color: var(--accent-blue);\\n  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);\\n  transform: scale(1.02);\\n}\\n\\n/* Compact metric card text */\\n.metric-card p {\\n  margin-bottom: calc(var(--base-spacing) * 0.1) !important;\\n  line-height: 1.1;\\n  font-size: calc(var(--base-font-size) * 0.7) !important;\\n  color: var(--text-secondary) !important;\\n}\\n\\n.metric-card .text-xl {\\n  font-size: calc(var(--base-font-size) * 1.1) !important;\\n  line-height: 1.0;\\n  margin-bottom: calc(var(--base-spacing) * 0.1) !important;\\n  color: var(--text-primary) !important;\\n  font-weight: 700 !important;\\n}\\n\\n/* Compact metric card icons */\\n.metric-card .w-10.h-10 {\\n  width: calc(var(--base-icon-size) * 1.5) !important;\\n  height: calc(var(--base-icon-size) * 1.5) !important;\\n  margin-bottom: calc(var(--base-spacing) * 0.5) !important;\\n}\\n\\n/* Beautiful gradient animation */\\n@keyframes gradientShift {\\n  0% { background-position: 0% 50%; }\\n  50% { background-position: 100% 50%; }\\n  100% { background-position: 0% 50%; }\\n}\\n\\n/* Status badges with glow effects */\\n.status-badge {\\n  padding: calc(var(--base-spacing) * 0.5) calc(var(--base-spacing) * 1);\\n  border-radius: calc(var(--base-border-radius) * 2);\\n  font-size: calc(10px * var(--content-scale));\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  border: 1px solid;\\n  backdrop-filter: blur(10px);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.status-badge.active {\\n  background: rgba(16, 185, 129, 0.2);\\n  color: var(--accent-green);\\n  border-color: var(--accent-green);\\n  box-shadow: 0 0 10px rgba(16, 185, 129, 0.3);\\n}\\n\\n.status-badge.beta {\\n  background: rgba(245, 158, 11, 0.2);\\n  color: var(--accent-yellow);\\n  border-color: var(--accent-yellow);\\n  box-shadow: 0 0 10px rgba(245, 158, 11, 0.3);\\n}\\n\\n.status-badge.live {\\n  background: rgba(59, 130, 246, 0.2);\\n  color: var(--accent-blue);\\n  border-color: var(--accent-blue);\\n  box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);\\n}\\n\\n.status-badge.testing {\\n  background: rgba(139, 92, 246, 0.2);\\n  color: var(--accent-purple);\\n  border-color: var(--accent-purple);\\n  box-shadow: 0 0 10px rgba(139, 92, 246, 0.3);\\n}\\n\\n/* Enhanced Header Styling */\\n.header-hud {\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  backdrop-filter: blur(20px);\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n  position: relative;\\n}\\n\\n.header-hud::before {\\n  content: '';\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(90deg, transparent, var(--accent-blue), transparent);\\n  opacity: 0.4;\\n}\\n\\n/* Enhanced Department Buttons */\\n.dept-button {\\n  background: var(--glass-bg-light);\\n  border: 1px solid var(--glass-border);\\n  backdrop-filter: blur(10px);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.dept-button:hover {\\n  background: rgba(59, 130, 246, 0.1);\\n  border-color: var(--glass-border-glow);\\n  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);\\n  transform: scale(1.05);\\n}\\n\\n.dept-button.active {\\n  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(6, 182, 212, 0.1) 100%);\\n  border-color: var(--accent-blue);\\n  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);\\n}\\n\\n/* Panel Background Utilities */\\n.bg-panel-bg {\\n  background: var(--glass-bg) !important;\\n}\\n\\n.bg-container-bg {\\n  background: var(--secondary-bg) !important;\\n}\\n\\n.dept-button.active::after {\\n  content: '';\\n  position: absolute;\\n  inset: 0;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\\n  pointer-events: none;\\n}\\n\\n/* Dashboard Auto-Scale - Scales ALL content including text, icons, spacing */\\n.dashboard-auto-scale {\\n  transform-origin: top left;\\n  width: 100vw !important;\\n  height: 100vh !important;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  overflow: hidden;\\n  transition: transform 0.3s ease-out;\\n}\\n\\n/* Scale everything - content, text, icons, spacing */\\n@media (max-width: 1920px) and (min-width: 1600px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.9);\\n    width: calc(100vw / 0.9) !important;\\n    height: calc(100vh / 0.9) !important;\\n  }\\n  :root {\\n    --content-scale: 0.9;\\n  }\\n}\\n\\n@media (max-width: 1599px) and (min-width: 1400px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.8);\\n    width: calc(100vw / 0.8) !important;\\n    height: calc(100vh / 0.8) !important;\\n  }\\n  :root {\\n    --content-scale: 0.8;\\n  }\\n}\\n\\n@media (max-width: 1399px) and (min-width: 1200px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.7);\\n    width: calc(100vw / 0.7) !important;\\n    height: calc(100vh / 0.7) !important;\\n  }\\n  :root {\\n    --content-scale: 0.7;\\n  }\\n}\\n\\n@media (max-width: 1199px) and (min-width: 1000px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.6);\\n    width: calc(100vw / 0.6) !important;\\n    height: calc(100vh / 0.6) !important;\\n  }\\n  :root {\\n    --content-scale: 0.6;\\n  }\\n}\\n\\n@media (max-width: 999px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.5);\\n    width: calc(100vw / 0.5) !important;\\n    height: calc(100vh / 0.5) !important;\\n  }\\n  :root {\\n    --content-scale: 0.5;\\n  }\\n}\\n\\n/* Default content scale */\\n:root {\\n  --content-scale: 1;\\n}\\n\\n/* Universal content scaling - applies to ALL elements */\\n* {\\n  font-size: inherit;\\n}\\n\\n/* Scale all text elements in main content */\\n.main-section h1, .main-section h2, .main-section h3, .main-section h4, .main-section h5, .main-section h6,\\n.main-section p, .main-section span, .main-section div, .main-section button,\\n.main-section input, .main-section textarea, .main-section select, .main-section label, .main-section a {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale card content specifically */\\n.card-content * {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale chart text and numbers */\\n.recharts-text, .recharts-label, .chart-text, .metric-value, .metric-label {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale all icons and images */\\nsvg, img, .icon {\\n  width: calc(var(--base-icon-size));\\n  height: calc(var(--base-icon-size));\\n}\\n\\n/* Scale all spacing */\\n.p-1 { padding: calc(4px * var(--content-scale)) !important; }\\n.p-2 { padding: calc(8px * var(--content-scale)) !important; }\\n.p-3 { padding: calc(12px * var(--content-scale)) !important; }\\n.p-4 { padding: calc(16px * var(--content-scale)) !important; }\\n.p-5 { padding: calc(20px * var(--content-scale)) !important; }\\n.p-6 { padding: calc(24px * var(--content-scale)) !important; }\\n\\n.m-1 { margin: calc(4px * var(--content-scale)) !important; }\\n.m-2 { margin: calc(8px * var(--content-scale)) !important; }\\n.m-3 { margin: calc(12px * var(--content-scale)) !important; }\\n.m-4 { margin: calc(16px * var(--content-scale)) !important; }\\n.m-5 { margin: calc(20px * var(--content-scale)) !important; }\\n.m-6 { margin: calc(24px * var(--content-scale)) !important; }\\n\\n/* Scale gaps */\\n.gap-1 { gap: calc(4px * var(--content-scale)) !important; }\\n.gap-2 { gap: calc(8px * var(--content-scale)) !important; }\\n.gap-3 { gap: calc(12px * var(--content-scale)) !important; }\\n.gap-4 { gap: calc(16px * var(--content-scale)) !important; }\\n.gap-5 { gap: calc(20px * var(--content-scale)) !important; }\\n.gap-6 { gap: calc(24px * var(--content-scale)) !important; }\\n\\n/* Scale border radius */\\n.rounded { border-radius: calc(4px * var(--content-scale)) !important; }\\n.rounded-md { border-radius: calc(6px * var(--content-scale)) !important; }\\n.rounded-lg { border-radius: calc(8px * var(--content-scale)) !important; }\\n.rounded-xl { border-radius: calc(12px * var(--content-scale)) !important; }\\n.rounded-2xl { border-radius: calc(16px * var(--content-scale)) !important; }\\n\\n@keyframes fade-in-up {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n.animate-fade-in-up {\\n  animation: fade-in-up 0.3s ease-out forwards;\\n}\\n\\n@keyframes fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n.animate-fadeIn {\\n  animation: fadeIn 0.5s ease-in-out;\\n}\\n\\n/* Override Tailwind text sizes with content scaling */\\n.text-xs { font-size: calc(12px * var(--content-scale)) !important; }\\n.text-sm { font-size: calc(14px * var(--content-scale)) !important; }\\n.text-base { font-size: calc(16px * var(--content-scale)) !important; }\\n.text-lg { font-size: calc(18px * var(--content-scale)) !important; }\\n.text-xl { font-size: calc(20px * var(--content-scale)) !important; }\\n.text-2xl { font-size: calc(24px * var(--content-scale)) !important; }\\n.text-3xl { font-size: calc(30px * var(--content-scale)) !important; }\\n.text-4xl { font-size: calc(36px * var(--content-scale)) !important; }\\n\\n/* Override Tailwind width/height for icons */\\n.w-3 { width: calc(12px * var(--content-scale)) !important; }\\n.w-4 { width: calc(16px * var(--content-scale)) !important; }\\n.w-5 { width: calc(20px * var(--content-scale)) !important; }\\n.w-6 { width: calc(24px * var(--content-scale)) !important; }\\n.w-8 { width: calc(32px * var(--content-scale)) !important; }\\n.w-10 { width: calc(40px * var(--content-scale)) !important; }\\n.w-12 { width: calc(48px * var(--content-scale)) !important; }\\n\\n.h-3 { height: calc(12px * var(--content-scale)) !important; }\\n.h-4 { height: calc(16px * var(--content-scale)) !important; }\\n.h-5 { height: calc(20px * var(--content-scale)) !important; }\\n.h-6 { height: calc(24px * var(--content-scale)) !important; }\\n.h-8 { height: calc(32px * var(--content-scale)) !important; }\\n.h-10 { height: calc(40px * var(--content-scale)) !important; }\\n.h-12 { height: calc(48px * var(--content-scale)) !important; }\\n\\n/* Optimized Layout Classes for Perfect Content Fit */\\n.main-layout {\\n  height: 100vh;\\n  width: 100vw;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.content-area {\\n  flex: 1;\\n  display: flex;\\n  min-height: 0;\\n  overflow: hidden;\\n}\\n\\n.main-content {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 0;\\n  overflow: hidden;\\n}\\n\\n.header-section {\\n  height: var(--header-height);\\n  flex-shrink: 0;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.main-section {\\n  flex: 1;\\n  min-height: 0;\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.footer-section {\\n  height: calc(var(--footer-height) * 1.5);\\n  flex-shrink: 0;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.sidebar-left {\\n  width: var(--sidebar-width);\\n  flex-shrink: 0;\\n}\\n\\n.sidebar-right {\\n  width: var(--sidebar-width);\\n  flex-shrink: 0;\\n}\\n\\n/* Enhanced Dashboard Grid for No-Scroll Layout */\\n.dashboard-grid {\\n  display: grid;\\n  gap: calc(var(--base-gap) * 0.75);\\n  height: 100%;\\n  overflow: hidden;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.dashboard-grid-teacher {\\n  grid-template-rows: auto 1fr auto;\\n  grid-template-columns: 1fr;\\n}\\n\\n.dashboard-grid-school {\\n  grid-template-rows: auto 1fr;\\n  grid-template-columns: 1fr 1fr;\\n  gap: calc(var(--base-gap) * 1);\\n}\\n\\n/* Compact content areas */\\n.content-compact {\\n  max-height: calc(100vh - var(--header-height) - var(--footer-height) - calc(var(--base-spacing) * 4));\\n  overflow-y: auto;\\n}\\n\\n.content-no-scroll {\\n  height: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n/* Enhanced Animations */\\n@keyframes pulse-glow {\\n  0%, 100% {\\n    box-shadow: 0 0 5px rgba(0, 207, 255, 0.3);\\n  }\\n  50% {\\n    box-shadow: 0 0 20px rgba(0, 207, 255, 0.6), 0 0 30px rgba(0, 207, 255, 0.4);\\n  }\\n}\\n\\n@keyframes slide-in-up {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes fade-in-scale {\\n  from {\\n    opacity: 0;\\n    transform: scale(0.95);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n}\\n\\n.animate-pulse-glow {\\n  animation: pulse-glow 2s ease-in-out infinite;\\n}\\n\\n.animate-slide-in-up {\\n  animation: slide-in-up 0.5s ease-out;\\n}\\n\\n.animate-fade-in-scale {\\n  animation: fade-in-scale 0.3s ease-out;\\n}\\n\\n/* Hover Effects */\\n.hover-lift {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.hover-lift:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(59, 130, 246, 0.2);\\n}\\n\\n/* Progress Bar Animations */\\n.progress-bar {\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.progress-bar::after {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\\n  animation: shimmer 2s infinite;\\n}\\n\\n@keyframes shimmer {\\n  0% {\\n    left: -100%;\\n  }\\n  100% {\\n    left: 100%;\\n  }\\n}\\n\\n/* Optimized Card Grid */\\n.card-grid {\\n  display: grid;\\n  gap: var(--base-gap);\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n.card-grid-2 {\\n  grid-template-columns: 1fr 1fr;\\n}\\n\\n.card-grid-3 {\\n  grid-template-columns: 1fr 1fr 1fr;\\n}\\n\\n.card-grid-4 {\\n  grid-template-columns: 1fr 1fr 1fr 1fr;\\n}\\n\\n.card-grid-auto {\\n  grid-template-columns: repeat(auto-fit, minmax(calc(250px * var(--content-scale)), 1fr));\\n}\\n\\n/* Optimized Card Sizing */\\n.card-compact {\\n  padding: calc(var(--base-card-padding) * 0.75);\\n  min-height: calc(120px * var(--content-scale));\\n}\\n\\n.card-standard {\\n  padding: var(--base-card-padding);\\n  min-height: calc(160px * var(--content-scale));\\n}\\n\\n.card-large {\\n  padding: calc(var(--base-card-padding) * 1.25);\\n  min-height: calc(200px * var(--content-scale));\\n}\\n\\n/* Neumorphic HUD Subnav Styles */\\n.subnav-hud {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  padding: calc(var(--base-spacing) * 1.5) 0;\\n  margin: 0 auto;\\n  max-width: calc(72px * 8 + var(--base-gap) * 7); /* 8 buttons with gaps */\\n}\\n\\n.subnav-button {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  width: calc(72px * var(--content-scale));\\n  height: calc(72px * var(--content-scale));\\n  margin: 0 calc(var(--base-gap) * 0.5);\\n  border-radius: calc(16px * var(--content-scale));\\n  background: var(--glass-bg);\\n  backdrop-filter: blur(20px);\\n  box-shadow: var(--glass-shadow);\\n  border: 1px solid var(--glass-border);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.subnav-button:hover {\\n  transform: translateY(calc(-2px * var(--content-scale)));\\n  background: var(--glass-bg-light);\\n  border-color: var(--glass-border-glow);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 2px 8px rgba(59, 130, 246, 0.2);\\n}\\n\\n.subnav-button.active {\\n  background: var(--glass-bg-light);\\n  border: 1px solid var(--accent-blue);\\n  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3), var(--glass-shadow);\\n}\\n\\n.subnav-icon {\\n  width: calc(24px * var(--content-scale));\\n  height: calc(24px * var(--content-scale));\\n  margin-bottom: calc(4px * var(--content-scale));\\n  color: var(--text-muted);\\n  transition: color 0.3s ease;\\n}\\n\\n.subnav-button:hover .subnav-icon,\\n.subnav-button.active .subnav-icon {\\n  color: var(--accent-blue);\\n}\\n\\n.subnav-label {\\n  font-size: calc(10px * var(--content-scale));\\n  font-weight: 500;\\n  color: var(--text-muted);\\n  text-align: center;\\n  line-height: 1.2;\\n  transition: color 0.3s ease;\\n}\\n\\n.subnav-button:hover .subnav-label,\\n.subnav-button.active .subnav-label {\\n  color: var(--accent-blue);\\n}\\n\\n/* HUD Glow Effect */\\n.subnav-button::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(circle at center, rgba(59, 130, 246, 0.1) 0%, transparent 70%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n  border-radius: inherit;\\n}\\n\\n.subnav-button:hover::before,\\n.subnav-button.active::before {\\n  opacity: 1;\\n}\\n\\n/* Collapsible Sidebar Styles */\\n.sidebar-toggle {\\n  position: fixed;\\n  top: calc(var(--base-spacing) * 2);\\n  left: calc(var(--base-spacing) * 2);\\n  z-index: 50;\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  backdrop-filter: blur(20px);\\n  border-radius: var(--base-border-radius);\\n  padding: calc(var(--base-spacing) * 0.75);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  box-shadow: var(--glass-shadow);\\n}\\n\\n.sidebar-toggle:hover {\\n  background: var(--glass-bg-light);\\n  transform: scale(1.05);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 2px 8px rgba(59, 130, 246, 0.2);\\n}\\n\\n.collapsible-sidebar {\\n  position: fixed;\\n  left: 0;\\n  top: 0;\\n  height: 100vh;\\n  background: var(--glass-bg);\\n  border-right: 1px solid var(--glass-border);\\n  backdrop-filter: blur(20px);\\n  z-index: 40;\\n  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  box-shadow: var(--glass-shadow);\\n  width: calc(280px * var(--content-scale));\\n}\\n\\n.collapsible-sidebar.collapsed {\\n  transform: translateX(-100%);\\n}\\n\\n.collapsible-sidebar.expanded {\\n  transform: translateX(0);\\n}\\n\\n.sidebar-overlay {\\n  position: fixed;\\n  inset: 0;\\n  background: rgba(0, 0, 0, 0.2);\\n  backdrop-filter: blur(2px);\\n  z-index: 30;\\n}\\n\\n/* Function Button Styles */\\n.function-button {\\n  width: 100%;\\n  display: flex;\\n  align-items: center;\\n  gap: calc(var(--base-spacing) * 2);\\n  padding: calc(var(--base-spacing) * 2);\\n  border-radius: calc(var(--base-border-radius) * 1.5);\\n  background: var(--glass-bg-light);\\n  border: 1px solid var(--glass-border);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.function-button:hover {\\n  background: rgba(59, 130, 246, 0.1);\\n  border-color: var(--glass-border-glow);\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 2px 8px rgba(59, 130, 246, 0.2);\\n}\\n\\n.function-button.active {\\n  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(6, 182, 212, 0.1) 100%);\\n  border-color: var(--accent-blue);\\n  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);\\n}\\n\\n.function-button-icon {\\n  font-size: calc(24px * var(--content-scale));\\n  line-height: 1;\\n}\\n\\n.function-button-content {\\n  flex: 1;\\n  text-align: left;\\n}\\n\\n.function-button-title {\\n  font-weight: 600;\\n  font-size: calc(16px * var(--content-scale));\\n  color: var(--text-primary);\\n  margin-bottom: calc(var(--base-spacing) * 0.25);\\n}\\n\\n.function-button-description {\\n  font-size: calc(12px * var(--content-scale));\\n  color: var(--text-muted);\\n}\\n\\n.function-button.active .function-button-title {\\n  color: var(--accent-blue);\\n}\\n\\n.function-button-indicator {\\n  width: calc(8px * var(--content-scale));\\n  height: calc(8px * var(--content-scale));\\n  border-radius: 50%;\\n  background: transparent;\\n  transition: background-color 0.3s ease;\\n}\\n\\n.function-button.active .function-button-indicator {\\n  background: var(--accent-blue);\\n}\\n\\n/* Enhanced HUD Vertical Layout for Left Sidebar */\\n.subnav-hud-vertical {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: stretch;\\n  justify-content: flex-start;\\n  gap: calc(var(--base-spacing) * 0.5);\\n  padding: calc(var(--base-spacing) * 0.75);\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n/* Enhanced subnav button with glassmorphism */\\n.subnav-hud-vertical .subnav-button {\\n  width: 100%;\\n  height: calc(36px * var(--content-scale));\\n  margin: 0;\\n  flex-shrink: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  padding: calc(var(--base-spacing) * 0.75) calc(var(--base-spacing) * 1);\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  border-radius: var(--base-border-radius);\\n  backdrop-filter: blur(20px);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n  box-shadow: var(--glass-shadow);\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover {\\n  background: rgba(59, 130, 246, 0.1);\\n  border-color: var(--glass-border-glow);\\n  transform: translateX(4px);\\n  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);\\n}\\n\\n.subnav-hud-vertical .subnav-button.active {\\n  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(6, 182, 212, 0.1) 100%);\\n  border-color: var(--accent-blue);\\n  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n}\\n\\n/* Enhanced subnav icon */\\n.subnav-hud-vertical .subnav-icon {\\n  width: calc(18px * var(--content-scale));\\n  height: calc(18px * var(--content-scale));\\n  margin-right: calc(var(--base-spacing) * 1.5);\\n  color: var(--text-secondary);\\n  transition: all 0.3s ease;\\n  flex-shrink: 0;\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover .subnav-icon {\\n  color: var(--accent-blue);\\n  filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.6));\\n}\\n\\n.subnav-hud-vertical .subnav-button.active .subnav-icon {\\n  color: var(--text-primary);\\n  filter: drop-shadow(0 0 12px rgba(59, 130, 246, 0.8));\\n}\\n\\n/* Enhanced subnav label - full text display */\\n.subnav-hud-vertical .subnav-label {\\n  color: var(--text-primary);\\n  font-size: calc(12px * var(--content-scale));\\n  font-weight: 500;\\n  line-height: 1.2;\\n  white-space: nowrap;\\n  transition: all 0.3s ease;\\n  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover .subnav-label {\\n  color: var(--text-primary);\\n  text-shadow: 0 0 8px rgba(59, 130, 246, 0.6);\\n}\\n\\n.subnav-hud-vertical .subnav-button.active .subnav-label {\\n  color: var(--text-primary);\\n  font-weight: 600;\\n  text-shadow: 0 0 12px rgba(59, 130, 246, 0.8);\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[1]!./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[2]!./styles/globals.css\n"));

/***/ })

});
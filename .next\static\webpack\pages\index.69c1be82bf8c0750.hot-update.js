"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: function() { return /* binding */ Footer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CloudIcon_CogIcon_HomeIcon_ShieldCheckIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CloudIcon,CogIcon,HomeIcon,ShieldCheckIcon!=!@heroicons/react/24/solid */ \"__barrel_optimize__?names=BoltIcon,ChartBarIcon,CloudIcon,CogIcon,HomeIcon,ShieldCheckIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/solid/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\nconst Footer = ()=>{\n    _s();\n    const [activeButton, setActiveButton] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const footerButtons = [\n        {\n            id: \"home\",\n            icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CloudIcon_CogIcon_HomeIcon_ShieldCheckIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__.HomeIcon,\n            label: \"Home\",\n            color: \"text-blue-400\"\n        },\n        {\n            id: \"analytics\",\n            icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CloudIcon_CogIcon_HomeIcon_ShieldCheckIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__.ChartBarIcon,\n            label: \"Analytics\",\n            color: \"text-green-400\"\n        },\n        {\n            id: \"security\",\n            icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CloudIcon_CogIcon_HomeIcon_ShieldCheckIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__.ShieldCheckIcon,\n            label: \"Security\",\n            color: \"text-cyan-400\"\n        },\n        {\n            id: \"cloud\",\n            icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CloudIcon_CogIcon_HomeIcon_ShieldCheckIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__.CloudIcon,\n            label: \"Cloud\",\n            color: \"text-purple-400\"\n        },\n        {\n            id: \"performance\",\n            icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CloudIcon_CogIcon_HomeIcon_ShieldCheckIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__.BoltIcon,\n            label: \"Performance\",\n            color: \"text-yellow-400\"\n        },\n        {\n            id: \"settings\",\n            icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CloudIcon_CogIcon_HomeIcon_ShieldCheckIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__.CogIcon,\n            label: \"Settings\",\n            color: \"text-red-400\"\n        }\n    ];\n    const handleButtonClick = (buttonId)=>{\n        setActiveButton(buttonId);\n        // Add haptic feedback simulation\n        if (navigator.vibrate) {\n            navigator.vibrate(50);\n        }\n        // Reset active state after animation\n        setTimeout(()=>setActiveButton(null), 200);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-panel-bg border border-gray-700/50 responsive-border-radius responsive-spacing-sm flex items-center justify-between responsive-text-sm w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center responsive-gap\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center responsive-gap-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-400 rounded-full animate-pulse\",\n                                style: {\n                                    width: \"calc(var(--base-icon-size) * 0.3)\",\n                                    height: \"calc(var(--base-icon-size) * 0.3)\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"responsive-text-xs text-gray-400\",\n                                children: \"SYSTEM ONLINE\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center responsive-gap-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-400 rounded-full\",\n                                style: {\n                                    width: \"calc(var(--base-icon-size) * 0.3)\",\n                                    height: \"calc(var(--base-icon-size) * 0.3)\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"responsive-text-xs text-gray-400\",\n                                children: \"SECURE CONNECTION\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: footerButtons.map((button)=>{\n                    const IconComponent = button.icon;\n                    const isActive = activeButton === button.id;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>handleButtonClick(button.id),\n                        className: \"\\n                relative group flex flex-col items-center justify-center\\n                w-12 h-12 rounded-xl transition-all duration-200 ease-out\\n                \".concat(isActive ? \"bg-gray-700/80 scale-95 shadow-inner\" : \"bg-gray-800/40 hover:bg-gray-700/60 hover:scale-105\", \"\\n                border border-gray-600/30 hover:border-gray-500/50\\n                backdrop-blur-sm\\n              \"),\n                        title: button.label,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\\n                absolute inset-0 rounded-xl opacity-0 group-hover:opacity-20 transition-opacity duration-300\\n                bg-gradient-to-br from-white to-transparent\\n              \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                className: \"\\n                w-5 h-5 transition-all duration-200\\n                \".concat(isActive ? \"scale-90\" : \"group-hover:scale-110\", \"\\n                \").concat(button.color, \"\\n              \")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 15\n                            }, undefined),\n                            isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -bottom-1 w-1 h-1 bg-cyan-400 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, button.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: [\n                            \"CPU: \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-cyan-400\",\n                                children: \"45%\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 16\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: [\n                            \"RAM: \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-green-400\",\n                                children: \"68%\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 16\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: new Date().toLocaleTimeString([], {\n                            hour: \"2-digit\",\n                            minute: \"2-digit\"\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Footer.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Footer, \"2IlxFF4hDMq20XkKpqpiCEEIzsY=\");\n_c = Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Footer.tsx\n"));

/***/ })

});
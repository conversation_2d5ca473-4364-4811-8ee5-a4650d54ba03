/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.dirty-diff-glyph {
	margin-left: 5px;
	cursor: pointer;
}

.dirty-diff-removed-line:after {
	content: '';
	position: absolute;
	bottom: -4px;
	box-sizing: border-box;
	width: 4px;
	height: 0;
	z-index: 9;
	border-top: 4px solid transparent;
	border-bottom: 4px solid transparent;
	transition: border-top-width 80ms linear, border-bottom-width 80ms linear, bottom 80ms linear;
	pointer-events: none;
}

.dirty-diff-glyph:before {
	position: absolute;
	content: '';
	height: 100%;
	width: 0;
	left: -2px;
	transition: width 80ms linear, left 80ms linear;
}

.dirty-diff-removed-line:before {
	margin-left: 3px;
	height: 0;
	bottom: 0;
	transition: height 80ms linear;
}

.margin-view-overlays > div:hover > .dirty-diff-glyph:before {
	position: absolute;
	content: '';
	height: 100%;
	width: 6px;
	left: -6px;
}

.margin-view-overlays > div:hover > .dirty-diff-removed-line:after {
	bottom: 0;
	border-top-width: 0;
	border-bottom-width: 0;
}

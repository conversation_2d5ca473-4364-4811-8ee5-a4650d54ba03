
import React, { useState, useEffect } from 'react';
import { BookCard, Book } from './BookCard';

export const BookstoreView: React.FC = () => {
  const [books, setBooks] = useState<Book[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchBooks = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/books');
        const data = await response.json();
        setBooks(data);
      } catch (error) {
        console.error("Failed to fetch books", error);
      } finally {
        setLoading(false);
      }
    };
    fetchBooks();
  }, []);
  
  if (loading) {
    return <div className="flex justify-center items-center h-full text-cyan-400">Loading Bookstore...</div>;
  }

  if (!books || !Array.isArray(books) || books.length === 0) {
    return <div className="flex justify-center items-center h-full text-red-400">No books available.</div>;
  }

  return (
    <div className="flex-grow grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {books.map(book => (
        <BookCard key={book.title} book={book} />
      ))}
    </div>
  );
};

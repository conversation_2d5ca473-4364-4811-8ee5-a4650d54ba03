"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/views/Gamification/GamificationView.tsx":
/*!*****************************************************!*\
  !*** ./src/views/Gamification/GamificationView.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GamificationView: function() { return /* binding */ GamificationView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_PerseusRenderer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/PerseusRenderer */ \"./src/components/PerseusRenderer.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n// Educational game categories with real Perseus widgets\nconst gameCategories = {\n    math: {\n        name: \"Mathematics\",\n        icon: \"\\uD83D\\uDD22\",\n        games: [\n            {\n                id: \"numeric-input\",\n                name: \"Number Practice\",\n                description: \"Practice number input and calculations\"\n            },\n            {\n                id: \"expression\",\n                name: \"Math Expressions\",\n                description: \"Work with mathematical expressions\"\n            },\n            {\n                id: \"grapher\",\n                name: \"Graph Explorer\",\n                description: \"Interactive graphing exercises\"\n            },\n            {\n                id: \"matrix\",\n                name: \"Matrix Operations\",\n                description: \"Learn matrix mathematics\"\n            }\n        ]\n    },\n    science: {\n        name: \"Science\",\n        icon: \"\\uD83D\\uDD2C\",\n        games: [\n            {\n                id: \"molecule\",\n                name: \"Molecule Builder\",\n                description: \"Build and explore molecular structures\"\n            },\n            {\n                id: \"phet-simulation\",\n                name: \"Physics Simulations\",\n                description: \"Interactive physics experiments\"\n            },\n            {\n                id: \"interactive-graph\",\n                name: \"Data Analysis\",\n                description: \"Analyze scientific data with graphs\"\n            }\n        ]\n    },\n    language: {\n        name: \"Language Arts\",\n        icon: \"\\uD83D\\uDCDA\",\n        games: [\n            {\n                id: \"passage\",\n                name: \"Reading Comprehension\",\n                description: \"Practice reading and understanding texts\"\n            },\n            {\n                id: \"categorizer\",\n                name: \"Word Categorizer\",\n                description: \"Categorize words and concepts\"\n            },\n            {\n                id: \"matcher\",\n                name: \"Word Matching\",\n                description: \"Match words with their meanings\"\n            }\n        ]\n    },\n    logic: {\n        name: \"Logic & Reasoning\",\n        icon: \"\\uD83E\\uDDE9\",\n        games: [\n            {\n                id: \"orderer\",\n                name: \"Sequence Ordering\",\n                description: \"Put items in logical order\"\n            },\n            {\n                id: \"sorter\",\n                name: \"Logic Sorter\",\n                description: \"Sort items by logical rules\"\n            },\n            {\n                id: \"radio\",\n                name: \"Multiple Choice Logic\",\n                description: \"Logical reasoning questions\"\n            }\n        ]\n    },\n    programming: {\n        name: \"Programming\",\n        icon: \"\\uD83D\\uDCBB\",\n        games: [\n            {\n                id: \"cs-program\",\n                name: \"Computer Science\",\n                description: \"Learn programming concepts\"\n            },\n            {\n                id: \"python-program\",\n                name: \"Python Programming\",\n                description: \"Interactive Python coding\"\n            }\n        ]\n    }\n};\nconst GamificationView = ()=>{\n    var _gameCategories_selectedCategory;\n    _s();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"math\");\n    const [activeGame, setActiveGame] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleGameClick = (gameId)=>{\n        setActiveGame(gameId);\n    };\n    if (activeGame) {\n        // Find the game details\n        let gameDetails = null;\n        let categoryName = \"\";\n        for (const [catKey, category] of Object.entries(gameCategories)){\n            const game = category.games.find((g)=>g.id === activeGame);\n            if (game) {\n                gameDetails = game;\n                categoryName = category.name;\n                break;\n            }\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full bg-panel-bg rounded-xl p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-white text-2xl font-bold\",\n                                    children: gameDetails === null || gameDetails === void 0 ? void 0 : gameDetails.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: categoryName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveGame(null),\n                            className: \"bg-gray-800/60 hover:bg-gray-700/60 text-white px-4 py-2 rounded-lg transition-colors duration-200\",\n                            children: \"← Back to Games\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full bg-gray-900/50 rounded-lg overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PerseusRenderer__WEBPACK_IMPORTED_MODULE_2__.PerseusRenderer, {\n                        widgetType: activeGame\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-panel-bg rounded-xl p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-white text-3xl font-bold mb-2\",\n                        children: \"\\uD83C\\uDFAE Educational Games\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Interactive learning games powered by Khan Academy Perseus\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2 mb-6 overflow-x-auto\",\n                children: Object.entries(gameCategories).map((param)=>{\n                    let [key, category] = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setSelectedCategory(key),\n                        className: \"flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 whitespace-nowrap \".concat(selectedCategory === key ? \"bg-blue-500/20 text-blue-400 border border-blue-400/30\" : \"bg-gray-800/40 text-gray-300 hover:bg-gray-700/60 hover:text-white\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: category.icon\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: category.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, key, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8\",\n                children: (_gameCategories_selectedCategory = gameCategories[selectedCategory]) === null || _gameCategories_selectedCategory === void 0 ? void 0 : _gameCategories_selectedCategory.games.map((game)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onClick: ()=>handleGameClick(game.id),\n                        className: \"bg-gray-800/40 border border-gray-700/50 rounded-xl p-4 hover:bg-gray-700/60 transition-all duration-300 cursor-pointer hover:scale-105 hover:border-blue-400/30\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl\",\n                                        children: \"\\uD83C\\uDFAF\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-white font-semibold\",\n                                                children: game.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: game.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-400 text-xs font-medium bg-blue-500/20 px-2 py-1 rounded\",\n                                        children: game.id\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-500 text-xs\",\n                                        children: \"Perseus Widget\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, game.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, undefined);\n};\n_s(GamificationView, \"yl+kFN6qiYBJhANaYdRXZ3u4o8o=\");\n_c = GamificationView;\nvar _c;\n$RefreshReg$(_c, \"GamificationView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/Gamification/GamificationView.tsx\n"));

/***/ })

});
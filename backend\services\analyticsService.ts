
export type Stat = {
  id: string;
  icon: 'UserGroupIcon' | 'CloudArrowUpIcon' | 'ShieldCheckIcon' | 'ChartBarIcon';
  label: string;
  value: string;
  change: string;
  changeType: 'increase' | 'decrease';
}
export type ChartData = { name: string; usage: number };

export const getAnalyticsData = () => {
  // Mock data
  const stats: Stat[] = [
    { id: 'users', icon: 'UserGroupIcon', label: 'Active Users', value: '1,302', change: '+14.2% this month', changeType: 'increase' },
    { id: 'upload', icon: 'CloudArrowUpIcon', label: 'Data Upload', value: '48.9 TB', change: '+6.1% this month', changeType: 'increase' },
    { id: 'threats', icon: 'ShieldCheckIcon', label: 'Threats Blocked', value: '2,312', change: '-1.8% this month', changeType: 'decrease' },
    { id: 'uptime', icon: 'ChartBarIcon', label: 'System Uptime', value: '99.98%', change: '+0.01% this month', changeType: 'increase' },
  ];
  const chartData: ChartData[] = [
    { name: 'Mon', usage: 120 },
    { name: 'Tue', usage: 150 },
    { name: 'Wed', usage: 110 },
    { name: 'Thu', usage: 180 },
    { name: 'Fri', usage: 210 },
    { name: 'Sat', usage: 250 },
    { name: 'Sun', usage: 230 },
  ];
  return { stats, chartData };
};

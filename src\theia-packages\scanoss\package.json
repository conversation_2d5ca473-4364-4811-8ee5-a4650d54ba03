{"name": "@theia/scanoss", "version": "1.63.0", "description": "Theia - SCANOSS Integration", "dependencies": {"@theia/core": "1.63.0", "scanoss": "^0.15.2"}, "publishConfig": {"access": "public"}, "theiaExtensions": [{"frontend": "lib/browser/scanoss-frontend-module", "backend": "lib/node/scanoss-backend-module"}], "keywords": ["theia-extension"], "license": "EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0", "repository": {"type": "git", "url": "https://github.com/eclipse-theia/theia.git"}, "bugs": {"url": "https://github.com/eclipse-theia/theia/issues"}, "homepage": "https://github.com/eclipse-theia/theia", "files": ["lib", "src"], "main": "lib/common", "scripts": {"build": "theiaext build", "clean": "theiaext clean", "compile": "theiaext compile", "lint": "theiaext lint", "test": "theiaext test", "watch": "theiaext watch"}, "devDependencies": {"@theia/ext-scripts": "1.63.0"}, "nyc": {"extends": "../../configs/nyc.json"}, "gitHead": "21358137e41342742707f660b8e222f940a27652"}
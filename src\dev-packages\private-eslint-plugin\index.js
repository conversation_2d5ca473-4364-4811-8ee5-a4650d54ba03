// @ts-check
// *****************************************************************************
// Copyright (C) 2021 <PERSON><PERSON> and others.
//
// This program and the accompanying materials are made available under the
// terms of the Eclipse Public License v. 2.0 which is available at
// http://www.eclipse.org/legal/epl-2.0.
//
// This Source Code may also be made available under the following Secondary
// Licenses when the conditions for such availability set forth in the Eclipse
// Public License v. 2.0 are satisfied: GNU General Public License, version 2
// with the GNU Classpath Exception which is available at
// https://www.gnu.org/software/classpath/license.html.
//
// SPDX-License-Identifier: EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0
// *****************************************************************************

/** @type {{[ruleId: string]: import('eslint').Rule.RuleModule}} */
exports.rules = {
    "annotation-check": require('./rules/annotation-check'),
    "localization-check": require('./rules/localization-check'),
    "no-src-import": require('./rules/no-src-import'),
    "runtime-import-check": require('./rules/runtime-import-check'),
    "shared-dependencies": require('./rules/shared-dependencies')
};

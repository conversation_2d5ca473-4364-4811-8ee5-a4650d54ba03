
import React, { useState } from 'react';
import { Card } from './Card';
import { ChevronRightIcon, MoreHorizIcon } from './icons';

const contacts = [
    { name: 'Ersin', detail: '<PERSON><PERSON><PERSON>', avatar: 'https://i.pravatar.cc/150?u=ersin' },
    { name: '<PERSON><PERSON>', detail: 'Fo<PERSON>n<PERSON><PERSON>ry', avatar: 'https://i.pravatar.cc/150?u=pamo' },
    { name: 'Ewliles', detail: 'Aidmysting', avatar: 'https://i.pravatar.cc/150?u=ewliles' },
];

export const EcafeAcces: React.FC = () => {
    const [selectedContact, setSelectedContact] = useState(contacts[0].name);

    return (
        <Card title="E cafey Acces" headerIcon={<MoreHorizIcon className="w-5 h-5"/>}>
            <ul className="space-y-2">
                {contacts.map((contact, index) => (
                    <li key={index} onClick={() => setSelectedContact(contact.name)} className={`flex items-center p-2 rounded-lg transition-colors cursor-pointer ${selectedContact === contact.name ? 'bg-gray-200/20' : 'hover:bg-gray-700/30'}`}>
                        <img src={contact.avatar} alt={contact.name} className="w-8 h-8 sm:w-10 sm:h-10 rounded-full mr-2 sm:mr-3 border-2 border-gray-600" />
                        <div className="flex-grow">
                            <p className="font-semibold text-white text-sm sm:text-base">{contact.name}</p>
                            <p className="text-xs text-gray-400">{contact.detail}</p>
                        </div>
                        {selectedContact !== contact.name && <ChevronRightIcon className="w-5 h-5 text-gray-500" />}
                    </li>
                ))}
            </ul>
        </Card>
    );
};

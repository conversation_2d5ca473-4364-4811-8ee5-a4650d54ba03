import type {Perseus<PERSON>enderer} from "@khanacademy/perseus-core";

export const question1: PerseusRenderer = {
    content: "[[\u2603 sorter 1]]",
    images: {},
    widgets: {
        "sorter 1": {
            version: {major: 0, minor: 0},
            type: "sorter",
            graded: true,
            options: {
                padding: true,
                layout: "horizontal",
                correct: ["Zeroth", "First", "Second", "Third", "Fourth"],
            },
        },
    },
};

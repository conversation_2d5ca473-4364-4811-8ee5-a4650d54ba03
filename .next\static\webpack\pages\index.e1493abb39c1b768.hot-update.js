"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _icons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons */ \"./src/components/icons.tsx\");\n\nvar _s = $RefreshSig$();\n\n\nconst Sidebar = (param)=>{\n    let { activeView, setActiveView, activeApp, setActiveApp } = param;\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Main function buttons\n    const functionButtons = [\n        {\n            id: \"gamification\",\n            label: \"Gamification\",\n            icon: \"\\uD83C\\uDFAE\",\n            description: \"Learning Games\"\n        },\n        {\n            id: \"coder\",\n            label: \"Coder\",\n            icon: \"\\uD83D\\uDCBB\",\n            description: \"IDE View\"\n        },\n        {\n            id: \"studio\",\n            label: \"Studio\",\n            icon: \"\\uD83E\\uDDD1‍\\uD83C\\uDFA8\",\n            description: \"Creative Workspace\"\n        },\n        {\n            id: \"design\",\n            label: \"Design\",\n            icon: \"\\uD83C\\uDFA8\",\n            description: \"Design Tools\"\n        }\n    ];\n    const handleFunctionButtonClick = (appId)=>{\n        if (setActiveApp) {\n            setActiveApp(appId);\n        }\n        setIsExpanded(false); // Auto-collapse after selection\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black/20 backdrop-blur-sm z-30\",\n                        onClick: ()=>setIsExpanded(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed left-20 top-0 h-full bg-panel-bg border border-gray-700/50 backdrop-blur-xl z-40 p-6\",\n                        style: {\n                            width: \"calc(240px * var(--content-scale))\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-white font-semibold text-lg mb-2\",\n                                        children: \"Functions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Choose your workspace\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: functionButtons.map((button)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleFunctionButtonClick(button.id),\n                                        className: \"w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200 \".concat(activeApp === button.id ? \"bg-blue-500/20 text-blue-400 border border-blue-400/30\" : \"text-gray-300 hover:text-white hover:bg-gray-800/60\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl\",\n                                                children: button.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: button.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: button.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, button.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"bg-panel-bg border border-gray-700/50 responsive-border-radius sidebar-left flex flex-col items-center justify-between\",\n                style: {\n                    padding: \"calc(var(--base-spacing) * 0.75)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center w-full\",\n                        style: {\n                            gap: \"calc(var(--base-gap) * 0.75)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-cyan-400 to-fuchsia-500 rounded-full flex items-center justify-center text-white\",\n                                style: {\n                                    width: \"calc(var(--base-icon-size) * 1.25)\",\n                                    height: \"calc(var(--base-icon-size) * 1.25)\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.ShieldCheckIcon, {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full border-t border-gray-700\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, undefined),\n                            functionButtons.map((button)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        if (setActiveApp) {\n                                            setActiveApp(button.id);\n                                        }\n                                    },\n                                    \"aria-label\": button.label,\n                                    className: \"w-full flex items-center justify-center responsive-border-radius transition-colors duration-200 relative \".concat(activeApp === button.id ? \"bg-brand-cyan/20 text-white\" : \"text-gray-400 hover:bg-gray-700/50 hover:text-white\"),\n                                    style: {\n                                        padding: \"calc(var(--base-spacing) * 0.5)\",\n                                        height: \"var(--base-button-height)\"\n                                    },\n                                    children: [\n                                        activeApp === button.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute left-0 top-1/2 -translate-y-1/2 bg-brand-cyan rounded-r-full\",\n                                            style: {\n                                                height: \"calc(var(--base-button-height) * 0.6)\",\n                                                width: \"3px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl\",\n                                            children: button.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, button.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, undefined))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                setActiveView(\"Dashboard\");\n                                if (setActiveApp) setActiveApp(\"\");\n                            },\n                            \"aria-label\": \"Dashboard\",\n                            className: \"w-full flex items-center justify-center responsive-border-radius transition-colors duration-200 relative \".concat(activeView === \"Dashboard\" && !activeApp ? \"bg-brand-cyan/20 text-white\" : \"text-gray-400 hover:bg-gray-700/50 hover:text-white\"),\n                            style: {\n                                padding: \"calc(var(--base-spacing) * 0.5)\",\n                                height: \"var(--base-button-height)\"\n                            },\n                            children: [\n                                activeView === \"Dashboard\" && !activeApp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"absolute left-0 top-1/2 -translate-y-1/2 bg-brand-cyan rounded-r-full\",\n                                    style: {\n                                        height: \"calc(var(--base-button-height) * 0.6)\",\n                                        width: \"3px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.HomeIcon, {\n                                    className: \"responsive-icon\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(Sidebar, \"FPNvbbHVlWWR4LKxxNntSxiIS38=\");\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Sidebar.tsx\n"));

/***/ })

});
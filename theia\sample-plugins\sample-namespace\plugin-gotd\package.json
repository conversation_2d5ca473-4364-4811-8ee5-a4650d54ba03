{"private": true, "name": "plugin-gotd", "version": "1.63.0", "license": "EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0", "repository": {"type": "git", "url": "https://github.com/eclipse-theia/theia.git"}, "engines": {"vscode": "^1.84.0"}, "main": "extension", "activationEvents": ["*"], "devDependencies": {"@theia/api-provider-sample": "1.63.0"}, "scripts": {"build": "npm pack"}, "theiaPlugin": {"headless": "headless"}, "headless": {"activationEvents": ["*"], "contributes": {}}}
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_views_Market_MarketView_tsx"],{

/***/ "__barrel_optimize__?names=ShoppingCartIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ShoppingCartIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*******************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ShoppingCartIcon: function() { return /* reexport safe */ _ShoppingCartIcon_js__WEBPACK_IMPORTED_MODULE_0__["default"]; }
/* harmony export */ });
/* harmony import */ var _ShoppingCartIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ShoppingCartIcon.js */ "./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/outline/esm/ShoppingCartIcon.js");



/***/ }),

/***/ "./src/components/Card.tsx":
/*!*********************************!*\
  !*** ./src/components/Card.tsx ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: function() { return /* binding */ Card; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Card = (param)=>{\n    let { title, children, className = \"\", headerIcon, titleClassName = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-panel-bg border border-gray-700/50 rounded-xl p-4 flex flex-col h-full \".concat(className),\n        children: [\n            title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xs uppercase text-[#A0A0B0] font-semibold tracking-wider \".concat(titleClassName),\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Card.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 11\n                    }, undefined),\n                    headerIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-[#A0A0B0]\",\n                        children: headerIcon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Card.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 26\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Card.tsx\",\n                lineNumber: 15,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-grow flex flex-col\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Card.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Card.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Card;\nvar _c;\n$RefreshReg$(_c, \"Card\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9DYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFVbkIsTUFBTUMsT0FBNEI7UUFBQyxFQUFFQyxLQUFLLEVBQUVDLFFBQVEsRUFBRUMsWUFBWSxFQUFFLEVBQUVDLFVBQVUsRUFBRUMsaUJBQWlCLEVBQUUsRUFBRTtJQUM1RyxxQkFDRSw4REFBQ0M7UUFBSUgsV0FBVyw2RUFBdUYsT0FBVkE7O1lBQzFGRix1QkFDQyw4REFBQ0s7Z0JBQUlILFdBQVU7O2tDQUNiLDhEQUFDSTt3QkFBR0osV0FBVyxpRUFBZ0YsT0FBZkU7a0NBQW1CSjs7Ozs7O29CQUNsR0csNEJBQWMsOERBQUNFO3dCQUFJSCxXQUFVO2tDQUFrQkM7Ozs7Ozs7Ozs7OzswQkFHcEQsOERBQUNFO2dCQUFJSCxXQUFVOzBCQUNaRDs7Ozs7Ozs7Ozs7O0FBSVQsRUFBRTtLQWRXRiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9DYXJkLnRzeD9iNWU2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbmludGVyZmFjZSBDYXJkUHJvcHMge1xuICB0aXRsZT86IHN0cmluZztcbiAgaGVhZGVySWNvbj86IFJlYWN0LlJlYWN0Tm9kZTtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xuICB0aXRsZUNsYXNzTmFtZT86IHN0cmluZztcbn1cblxuZXhwb3J0IGNvbnN0IENhcmQ6IFJlYWN0LkZDPENhcmRQcm9wcz4gPSAoeyB0aXRsZSwgY2hpbGRyZW4sIGNsYXNzTmFtZSA9ICcnLCBoZWFkZXJJY29uLCB0aXRsZUNsYXNzTmFtZSA9ICcnIH0pID0+IHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YGJnLXBhbmVsLWJnIGJvcmRlciBib3JkZXItZ3JheS03MDAvNTAgcm91bmRlZC14bCBwLTQgZmxleCBmbGV4LWNvbCBoLWZ1bGwgJHtjbGFzc05hbWV9YH0+XG4gICAgICB7dGl0bGUgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBtYi0zXCI+XG4gICAgICAgICAgPGgzIGNsYXNzTmFtZT17YHRleHQteHMgdXBwZXJjYXNlIHRleHQtWyNBMEEwQjBdIGZvbnQtc2VtaWJvbGQgdHJhY2tpbmctd2lkZXIgJHt0aXRsZUNsYXNzTmFtZX1gfT57dGl0bGV9PC9oMz5cbiAgICAgICAgICB7aGVhZGVySWNvbiAmJiA8ZGl2IGNsYXNzTmFtZT1cInRleHQtWyNBMEEwQjBdXCI+e2hlYWRlckljb259PC9kaXY+fVxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtZ3JvdyBmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufTsiXSwibmFtZXMiOlsiUmVhY3QiLCJDYXJkIiwidGl0bGUiLCJjaGlsZHJlbiIsImNsYXNzTmFtZSIsImhlYWRlckljb24iLCJ0aXRsZUNsYXNzTmFtZSIsImRpdiIsImgzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/Card.tsx\n"));

/***/ }),

/***/ "./src/views/Market/FeaturedProduct.tsx":
/*!**********************************************!*\
  !*** ./src/views/Market/FeaturedProduct.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FeaturedProduct: function() { return /* binding */ FeaturedProduct; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/Card */ \"./src/components/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_ShoppingCartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ShoppingCartIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ShoppingCartIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/outline/esm/index.js\");\n\n\n\n\nconst FeaturedProduct = (param)=>{\n    let { product } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                src: product.image,\n                alt: product.name,\n                className: \"rounded-lg mb-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\FeaturedProduct.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-400\",\n                                children: product.category\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\FeaturedProduct.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg font-bold text-white\",\n                                children: product.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\FeaturedProduct.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\FeaturedProduct.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl font-semibold text-cyan-400\",\n                        children: product.price\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\FeaturedProduct.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\FeaturedProduct.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"w-full mt-4 py-2 text-sm font-semibold text-white bg-pink-500/80 border border-pink-500/90 rounded-lg hover:bg-pink-500/90 transition-colors flex items-center justify-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ShoppingCartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ShoppingCartIcon, {\n                        className: \"w-5 h-5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\FeaturedProduct.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, undefined),\n                    \"Add to Cart\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\FeaturedProduct.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\FeaturedProduct.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, undefined);\n};\n_c = FeaturedProduct;\nvar _c;\n$RefreshReg$(_c, \"FeaturedProduct\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/Market/FeaturedProduct.tsx\n"));

/***/ }),

/***/ "./src/views/Market/MarketView.tsx":
/*!*****************************************!*\
  !*** ./src/views/Market/MarketView.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarketView: function() { return /* binding */ MarketView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _FeaturedProduct__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./FeaturedProduct */ \"./src/views/Market/FeaturedProduct.tsx\");\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/Card */ \"./src/components/Card.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst MarketView = ()=>{\n    _s();\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            try {\n                setLoading(true);\n                const response = await fetch(\"/api/market\");\n                const result = await response.json();\n                setData(result);\n            } catch (error) {\n                console.error(\"Failed to fetch market data\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchData();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-full text-cyan-400\",\n            children: \"Loading Market Data...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n            lineNumber: 42,\n            columnNumber: 16\n        }, undefined);\n    }\n    if (!data || !data.featuredProduct || !data.marketStats || !data.marketStats.topMover) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-full text-red-400\",\n            children: \"Failed to load market data.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n            lineNumber: 46,\n            columnNumber: 16\n        }, undefined);\n    }\n    const { featuredProduct, marketStats } = data;\n    const moverColor = marketStats.topMover.isUp ? \"text-green-400\" : \"text-red-400\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-grow grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FeaturedProduct__WEBPACK_IMPORTED_MODULE_2__.FeaturedProduct, {\n                    product: featuredProduct\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                title: \"Market Stats\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col justify-center h-full gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Market Cap\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: marketStats.marketCap\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"24h Volume\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: marketStats.volume24h\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Top Mover\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl font-bold \".concat(moverColor),\n                                    children: [\n                                        marketStats.topMover.name,\n                                        \" \",\n                                        marketStats.topMover.change\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Market\\\\MarketView.tsx\",\n        lineNumber: 53,\n        columnNumber: 9\n    }, undefined);\n};\n_s(MarketView, \"Zn4cs3026OJRBhxLd0Oqj+bUOXY=\");\n_c = MarketView;\nvar _c;\n$RefreshReg$(_c, \"MarketView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/Market/MarketView.tsx\n"));

/***/ })

}]);
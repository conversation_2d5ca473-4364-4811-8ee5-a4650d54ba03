name: Feature Request
description: Suggest an idea for this project
title: '[Feature Request] '
labels:
  - 'feature-request'
body:
  - type: markdown
    attributes:
      value: |
        To help us efficiently reviewing your feature request, please fill out this form.
  - type: checkboxes
    id: not
    attributes:
      label: Context
      options:
        - label: This issue is not a bug report. *(please use a different template for reporting a bug)*
          required: true
        - label: This issue is not a duplicate of an existing issue. *(please use the [search](https://github.com/microsoft/monaco-editor/issues) to find existing issues)*
          required: true

  - type: textarea
    id: description
    attributes:
      label: Description
      description: Please describe your feature request.

  - type: textarea
    id: relevantCodePlaygroundLink
    attributes:
      label: Monaco Editor Playground Link
      description: If applicable, please share the link to a relevant [monaco editor playground sample](https://microsoft.github.io/monaco-editor/playground.html)

  - type: textarea
    id: releveantCode
    attributes:
      label: Monaco Editor Playground Code
      description: If applicable, please share the code from the monaco editor playground sample.
      render: typescript

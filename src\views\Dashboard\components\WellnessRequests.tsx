
import React from 'react';
import { Card } from '../../../components/Card';
import { 
    UserCircleIcon,
    BuildingOfficeIcon,
    PuzzlePieceIcon,
    CogIcon,
    SnowflakeIcon,
    MemoryChipIcon 
} from '../../../components/icons';


const iconMap: { [key: string]: React.FC<{className?: string}> } = {
    BuildingOfficeIcon,
    PuzzlePieceIcon,
    CogIcon,
    SnowflakeIcon,
    MemoryChipIcon
};

type SystemRequest = {
    text: string;
};

type SystemStat = {
    icon: string;
    value: string;
    label: string;
};

interface WellnessRequestsProps {
    requests: SystemRequest[];
    stats: SystemStat[];
}

export const WellnessRequests: React.FC<WellnessRequestsProps> = ({ requests, stats }) => {

    const StatComponent: React.FC<{stat: SystemStat, className?: string}> = ({ stat, className = '' }) => {
        const Icon = iconMap[stat.icon] || CogIcon; // Fallback to a default icon
        return (
            <div className={`flex flex-col items-center justify-center bg-gray-700/30 rounded-lg p-1 sm:p-2 ${className}`}>
                <Icon className="w-5 h-5 sm:w-6 sm:h-6 text-cyan-400"/>
                <p className="text-base sm:text-xl font-bold text-white mt-1">{stat.value}</p>
                <p className="text-[10px] text-gray-400 text-center">{stat.label}</p>
            </div>
        );
    }
    
    return (
        <Card title="System Status & Requests">
            <div className="grid grid-cols-2 gap-4 h-full">
                {/* Left Column */}
                <div className="space-y-3 text-xs sm:text-sm flex flex-col justify-center">
                    {requests.map((request, index) => (
                         <div key={index} className="flex items-center gap-3 text-gray-300">
                            <UserCircleIcon className="w-5 h-5 text-cyan-400 flex-shrink-0" />
                            <span>{request.text}</span>
                        </div>
                    ))}
                </div>

                {/* Right Column */}
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 text-center">
                   {stats.slice(0, 3).map(stat => <StatComponent key={stat.label} stat={stat} />)}
                   {stats.length > 3 && <StatComponent stat={stats[3]} />}
                   {stats.length > 4 && <StatComponent stat={stats[4]} className="col-span-2" />}
                </div>
            </div>
        </Card>
    );
};

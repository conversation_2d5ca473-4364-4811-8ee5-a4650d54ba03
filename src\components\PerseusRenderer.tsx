import React, { useEffect, useRef } from 'react';

interface PerseusRendererProps {
  widgetType: string;
  widgetData?: any;
}

export const PerseusRenderer: React.FC<PerseusRendererProps> = ({ widgetType, widgetData }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    // Clear previous content
    containerRef.current.innerHTML = '';

    // Render based on widget type
    switch (widgetType) {
      case 'numeric-input':
        renderNumericInput(containerRef.current);
        break;
      case 'categorizer':
        renderCategorizer(containerRef.current);
        break;
      case 'matcher':
        renderMatcher(containerRef.current);
        break;
      case 'orderer':
        renderOrderer(containerRef.current);
        break;
      case 'radio':
        renderRadio(containerRef.current);
        break;
      case 'expression':
        renderExpression(containerRef.current);
        break;
      case 'grapher':
        renderGrapher(containerRef.current);
        break;
      case 'matrix':
        renderMatrix(containerRef.current);
        break;
      case 'molecule':
        renderMolecule(containerRef.current);
        break;
      case 'phet-simulation':
        renderPhetSimulation(containerRef.current);
        break;
      case 'interactive-graph':
        renderInteractiveGraph(containerRef.current);
        break;
      case 'passage':
        renderPassage(containerRef.current);
        break;
      case 'sorter':
        renderSorter(containerRef.current);
        break;
      case 'cs-program':
        renderCSProgram(containerRef.current);
        break;
      case 'python-program':
        renderPythonProgram(containerRef.current);
        break;
      default:
        renderPlaceholder(containerRef.current, widgetType);
    }
  }, [widgetType, widgetData]);

  return (
    <div 
      ref={containerRef} 
      className="perseus-widget bg-white rounded-lg p-6 min-h-[400px]"
      style={{ fontFamily: 'Arial, sans-serif' }}
    />
  );
};

// Numeric Input Widget
function renderNumericInput(container: HTMLElement) {
  container.innerHTML = `
    <div class="numeric-input-widget">
      <h3 style="color: #1c4f82; margin-bottom: 20px; font-size: 18px;">Math Problem</h3>
      <p style="color: #333; margin-bottom: 15px;">What is 15 + 27?</p>
      <input 
        type="number" 
        placeholder="Enter your answer"
        style="
          padding: 10px; 
          border: 2px solid #ddd; 
          border-radius: 5px; 
          font-size: 16px;
          width: 200px;
          margin-right: 10px;
        "
        id="numeric-answer"
      />
      <button 
        onclick="checkNumericAnswer()"
        style="
          background: #1c4f82; 
          color: white; 
          padding: 10px 20px; 
          border: none; 
          border-radius: 5px;
          cursor: pointer;
          font-size: 16px;
        "
      >Check Answer</button>
      <div id="numeric-feedback" style="margin-top: 15px; font-weight: bold;"></div>
    </div>
  `;

  // Add event handler
  (window as any).checkNumericAnswer = () => {
    const input = document.getElementById('numeric-answer') as HTMLInputElement;
    const feedback = document.getElementById('numeric-feedback');
    if (input && feedback) {
      const answer = parseInt(input.value);
      if (answer === 42) {
        feedback.innerHTML = '<span style="color: #28a745;">✓ Correct! Great job!</span>';
      } else {
        feedback.innerHTML = '<span style="color: #dc3545;">✗ Try again. Think about 15 + 27.</span>';
      }
    }
  };
}

// Categorizer Widget
function renderCategorizer(container: HTMLElement) {
  container.innerHTML = `
    <div class="categorizer-widget">
      <h3 style="color: #1c4f82; margin-bottom: 20px; font-size: 18px;">Categorize Animals</h3>
      <p style="color: #333; margin-bottom: 15px;">Drag each animal to the correct category:</p>
      
      <div style="display: flex; gap: 20px; margin-bottom: 20px;">
        <div class="category-box" style="
          border: 2px dashed #1c4f82; 
          padding: 15px; 
          min-height: 100px; 
          width: 150px;
          border-radius: 8px;
          background: #f8f9fa;
        ">
          <h4 style="margin: 0 0 10px 0; color: #1c4f82;">Mammals</h4>
          <div id="mammals-drop" class="drop-zone"></div>
        </div>
        
        <div class="category-box" style="
          border: 2px dashed #28a745; 
          padding: 15px; 
          min-height: 100px; 
          width: 150px;
          border-radius: 8px;
          background: #f8f9fa;
        ">
          <h4 style="margin: 0 0 10px 0; color: #28a745;">Birds</h4>
          <div id="birds-drop" class="drop-zone"></div>
        </div>
      </div>
      
      <div style="display: flex; gap: 10px; flex-wrap: wrap;">
        <div class="draggable-item" draggable="true" data-category="mammals" style="
          background: #e9ecef; 
          padding: 8px 12px; 
          border-radius: 5px; 
          cursor: move;
          border: 1px solid #ced4da;
        ">🐕 Dog</div>
        <div class="draggable-item" draggable="true" data-category="birds" style="
          background: #e9ecef; 
          padding: 8px 12px; 
          border-radius: 5px; 
          cursor: move;
          border: 1px solid #ced4da;
        ">🦅 Eagle</div>
        <div class="draggable-item" draggable="true" data-category="mammals" style="
          background: #e9ecef; 
          padding: 8px 12px; 
          border-radius: 5px; 
          cursor: move;
          border: 1px solid #ced4da;
        ">🐱 Cat</div>
        <div class="draggable-item" draggable="true" data-category="birds" style="
          background: #e9ecef; 
          padding: 8px 12px; 
          border-radius: 5px; 
          cursor: move;
          border: 1px solid #ced4da;
        ">🐦 Sparrow</div>
      </div>
      
      <button 
        onclick="checkCategorizer()"
        style="
          background: #1c4f82; 
          color: white; 
          padding: 10px 20px; 
          border: none; 
          border-radius: 5px;
          cursor: pointer;
          font-size: 16px;
          margin-top: 20px;
        "
      >Check Categories</button>
      <div id="categorizer-feedback" style="margin-top: 15px; font-weight: bold;"></div>
    </div>
  `;

  // Add drag and drop functionality
  setupDragAndDrop();
}

function setupDragAndDrop() {
  const draggables = document.querySelectorAll('.draggable-item');
  const dropZones = document.querySelectorAll('.drop-zone');

  draggables.forEach(draggable => {
    draggable.addEventListener('dragstart', (e) => {
      (e as DragEvent).dataTransfer?.setData('text/plain', (draggable as HTMLElement).outerHTML);
      (e as DragEvent).dataTransfer?.setData('category', (draggable as HTMLElement).dataset.category || '');
    });
  });

  dropZones.forEach(zone => {
    zone.addEventListener('dragover', (e) => {
      e.preventDefault();
    });

    zone.addEventListener('drop', (e) => {
      e.preventDefault();
      const html = (e as DragEvent).dataTransfer?.getData('text/plain');
      const category = (e as DragEvent).dataTransfer?.getData('category');
      
      if (html && zone.id.includes(category || '')) {
        zone.innerHTML += html;
      }
    });
  });

  (window as any).checkCategorizer = () => {
    const mammalsZone = document.getElementById('mammals-drop');
    const birdsZone = document.getElementById('birds-drop');
    const feedback = document.getElementById('categorizer-feedback');
    
    if (mammalsZone && birdsZone && feedback) {
      const mammalsCount = mammalsZone.children.length;
      const birdsCount = birdsZone.children.length;
      
      if (mammalsCount === 2 && birdsCount === 2) {
        feedback.innerHTML = '<span style="color: #28a745;">✓ Perfect categorization!</span>';
      } else {
        feedback.innerHTML = '<span style="color: #dc3545;">✗ Some animals are in wrong categories. Try again!</span>';
      }
    }
  };
}

// Matcher Widget
function renderMatcher(container: HTMLElement) {
  container.innerHTML = `
    <div class="matcher-widget">
      <h3 style="color: #1c4f82; margin-bottom: 20px; font-size: 18px;">Match Words with Definitions</h3>
      <p style="color: #333; margin-bottom: 15px;">Click on a word, then click on its matching definition:</p>
      
      <div style="display: flex; gap: 40px;">
        <div>
          <h4 style="color: #1c4f82;">Words</h4>
          <div class="match-item" data-match="photosynthesis" onclick="selectMatch(this)" style="
            background: #e9ecef; 
            padding: 10px; 
            margin: 5px 0; 
            border-radius: 5px; 
            cursor: pointer;
            border: 2px solid transparent;
          ">Photosynthesis</div>
          <div class="match-item" data-match="gravity" onclick="selectMatch(this)" style="
            background: #e9ecef; 
            padding: 10px; 
            margin: 5px 0; 
            border-radius: 5px; 
            cursor: pointer;
            border: 2px solid transparent;
          ">Gravity</div>
          <div class="match-item" data-match="democracy" onclick="selectMatch(this)" style="
            background: #e9ecef; 
            padding: 10px; 
            margin: 5px 0; 
            border-radius: 5px; 
            cursor: pointer;
            border: 2px solid transparent;
          ">Democracy</div>
        </div>
        
        <div>
          <h4 style="color: #28a745;">Definitions</h4>
          <div class="match-item" data-match="gravity" onclick="selectMatch(this)" style="
            background: #e9ecef; 
            padding: 10px; 
            margin: 5px 0; 
            border-radius: 5px; 
            cursor: pointer;
            border: 2px solid transparent;
          ">Force that pulls objects toward Earth</div>
          <div class="match-item" data-match="photosynthesis" onclick="selectMatch(this)" style="
            background: #e9ecef; 
            padding: 10px; 
            margin: 5px 0; 
            border-radius: 5px; 
            cursor: pointer;
            border: 2px solid transparent;
          ">Process plants use to make food from sunlight</div>
          <div class="match-item" data-match="democracy" onclick="selectMatch(this)" style="
            background: #e9ecef; 
            padding: 10px; 
            margin: 5px 0; 
            border-radius: 5px; 
            cursor: pointer;
            border: 2px solid transparent;
          ">Government by the people</div>
        </div>
      </div>
      
      <div id="matcher-feedback" style="margin-top: 20px; font-weight: bold;"></div>
    </div>
  `;

  let selectedItems: HTMLElement[] = [];
  
  (window as any).selectMatch = (element: HTMLElement) => {
    if (selectedItems.length === 0) {
      element.style.border = '2px solid #1c4f82';
      selectedItems.push(element);
    } else if (selectedItems.length === 1) {
      const first = selectedItems[0];
      const feedback = document.getElementById('matcher-feedback');
      
      if (first.dataset.match === element.dataset.match) {
        first.style.background = '#d4edda';
        element.style.background = '#d4edda';
        first.style.border = '2px solid #28a745';
        element.style.border = '2px solid #28a745';
        if (feedback) feedback.innerHTML = '<span style="color: #28a745;">✓ Correct match!</span>';
      } else {
        first.style.border = '2px solid #dc3545';
        element.style.border = '2px solid #dc3545';
        if (feedback) feedback.innerHTML = '<span style="color: #dc3545;">✗ Try again!</span>';
        setTimeout(() => {
          first.style.border = '2px solid transparent';
          element.style.border = '2px solid transparent';
        }, 1000);
      }
      selectedItems = [];
    }
  };
}

// Orderer Widget
function renderOrderer(container: HTMLElement) {
  container.innerHTML = `
    <div class="orderer-widget">
      <h3 style="color: #1c4f82; margin-bottom: 20px; font-size: 18px;">Put in Chronological Order</h3>
      <p style="color: #333; margin-bottom: 15px;">Drag to arrange these historical events in order:</p>
      
      <div id="sortable-list" style="min-height: 200px;">
        <div class="sortable-item" draggable="true" data-order="3" style="
          background: #e9ecef; 
          padding: 15px; 
          margin: 10px 0; 
          border-radius: 5px; 
          cursor: move;
          border: 1px solid #ced4da;
        ">World War II ends (1945)</div>
        <div class="sortable-item" draggable="true" data-order="1" style="
          background: #e9ecef; 
          padding: 15px; 
          margin: 10px 0; 
          border-radius: 5px; 
          cursor: move;
          border: 1px solid #ced4da;
        ">American Civil War begins (1861)</div>
        <div class="sortable-item" draggable="true" data-order="2" style="
          background: #e9ecef; 
          padding: 15px; 
          margin: 10px 0; 
          border-radius: 5px; 
          cursor: move;
          border: 1px solid #ced4da;
        ">World War I begins (1914)</div>
        <div class="sortable-item" draggable="true" data-order="4" style="
          background: #e9ecef; 
          padding: 15px; 
          margin: 10px 0; 
          border-radius: 5px; 
          cursor: move;
          border: 1px solid #ced4da;
        ">Moon landing (1969)</div>
      </div>
      
      <button 
        onclick="checkOrder()"
        style="
          background: #1c4f82; 
          color: white; 
          padding: 10px 20px; 
          border: none; 
          border-radius: 5px;
          cursor: pointer;
          font-size: 16px;
          margin-top: 20px;
        "
      >Check Order</button>
      <div id="orderer-feedback" style="margin-top: 15px; font-weight: bold;"></div>
    </div>
  `;

  setupSortable();
}

function setupSortable() {
  const sortableList = document.getElementById('sortable-list');
  if (!sortableList) return;

  let draggedElement: HTMLElement | null = null;

  sortableList.addEventListener('dragstart', (e) => {
    draggedElement = e.target as HTMLElement;
    if (draggedElement) {
      draggedElement.style.opacity = '0.5';
    }
  });

  sortableList.addEventListener('dragend', (e) => {
    if (draggedElement) {
      draggedElement.style.opacity = '1';
      draggedElement = null;
    }
  });

  sortableList.addEventListener('dragover', (e) => {
    e.preventDefault();
  });

  sortableList.addEventListener('drop', (e) => {
    e.preventDefault();
    const target = e.target as HTMLElement;
    if (target && target.classList.contains('sortable-item') && draggedElement) {
      const rect = target.getBoundingClientRect();
      const midpoint = rect.top + rect.height / 2;
      
      if (e.clientY < midpoint) {
        sortableList.insertBefore(draggedElement, target);
      } else {
        sortableList.insertBefore(draggedElement, target.nextSibling);
      }
    }
  });

  (window as any).checkOrder = () => {
    const items = Array.from(document.querySelectorAll('.sortable-item'));
    const feedback = document.getElementById('orderer-feedback');
    
    let isCorrect = true;
    items.forEach((item, index) => {
      const expectedOrder = parseInt((item as HTMLElement).dataset.order || '0');
      if (expectedOrder !== index + 1) {
        isCorrect = false;
      }
    });
    
    if (feedback) {
      if (isCorrect) {
        feedback.innerHTML = '<span style="color: #28a745;">✓ Perfect chronological order!</span>';
      } else {
        feedback.innerHTML = '<span style="color: #dc3545;">✗ Not quite right. Check the dates!</span>';
      }
    }
  };
}

// Radio (Multiple Choice) Widget
function renderRadio(container: HTMLElement) {
  container.innerHTML = `
    <div class="radio-widget">
      <h3 style="color: #1c4f82; margin-bottom: 20px; font-size: 18px;">Multiple Choice Question</h3>
      <p style="color: #333; margin-bottom: 15px; font-size: 16px;">What is the capital of France?</p>
      
      <div style="margin-bottom: 20px;">
        <label style="display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 5px; background: #f8f9fa;">
          <input type="radio" name="capital" value="london" style="margin-right: 10px;"> London
        </label>
        <label style="display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 5px; background: #f8f9fa;">
          <input type="radio" name="capital" value="paris" style="margin-right: 10px;"> Paris
        </label>
        <label style="display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 5px; background: #f8f9fa;">
          <input type="radio" name="capital" value="berlin" style="margin-right: 10px;"> Berlin
        </label>
        <label style="display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 5px; background: #f8f9fa;">
          <input type="radio" name="capital" value="madrid" style="margin-right: 10px;"> Madrid
        </label>
      </div>
      
      <button 
        onclick="checkRadio()"
        style="
          background: #1c4f82; 
          color: white; 
          padding: 10px 20px; 
          border: none; 
          border-radius: 5px;
          cursor: pointer;
          font-size: 16px;
        "
      >Submit Answer</button>
      <div id="radio-feedback" style="margin-top: 15px; font-weight: bold;"></div>
    </div>
  `;

  (window as any).checkRadio = () => {
    const selected = document.querySelector('input[name="capital"]:checked') as HTMLInputElement;
    const feedback = document.getElementById('radio-feedback');
    
    if (!selected) {
      if (feedback) feedback.innerHTML = '<span style="color: #ffc107;">Please select an answer.</span>';
      return;
    }
    
    if (feedback) {
      if (selected.value === 'paris') {
        feedback.innerHTML = '<span style="color: #28a745;">✓ Correct! Paris is the capital of France.</span>';
      } else {
        feedback.innerHTML = '<span style="color: #dc3545;">✗ Incorrect. The capital of France is Paris.</span>';
      }
    }
  };
}

// Expression Widget
function renderExpression(container: HTMLElement) {
  container.innerHTML = `
    <div class="expression-widget">
      <h3 style="color: #1c4f82; margin-bottom: 20px; font-size: 18px;">Solve the Expression</h3>
      <p style="color: #333; margin-bottom: 15px;">Simplify: 2x + 3x - 5</p>
      
      <input 
        type="text" 
        placeholder="Enter simplified expression"
        style="
          padding: 10px; 
          border: 2px solid #ddd; 
          border-radius: 5px; 
          font-size: 16px;
          width: 250px;
          margin-right: 10px;
        "
        id="expression-answer"
      />
      <button 
        onclick="checkExpression()"
        style="
          background: #1c4f82; 
          color: white; 
          padding: 10px 20px; 
          border: none; 
          border-radius: 5px;
          cursor: pointer;
          font-size: 16px;
        "
      >Check Answer</button>
      
      <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
        <p style="margin: 0; color: #666; font-size: 14px;">
          <strong>Hint:</strong> Combine like terms (terms with the same variable)
        </p>
      </div>
      
      <div id="expression-feedback" style="margin-top: 15px; font-weight: bold;"></div>
    </div>
  `;

  (window as any).checkExpression = () => {
    const input = document.getElementById('expression-answer') as HTMLInputElement;
    const feedback = document.getElementById('expression-feedback');
    
    if (input && feedback) {
      const answer = input.value.toLowerCase().replace(/\s/g, '');
      const correctAnswers = ['5x-5', '5x+-5', '-5+5x'];
      
      if (correctAnswers.includes(answer)) {
        feedback.innerHTML = '<span style="color: #28a745;">✓ Excellent! 2x + 3x - 5 = 5x - 5</span>';
      } else {
        feedback.innerHTML = '<span style="color: #dc3545;">✗ Not quite. Remember to combine like terms: 2x + 3x = 5x</span>';
      }
    }
  };
}

// Grapher Widget - Interactive Math Graphing
function renderGrapher(container: HTMLElement) {
  container.innerHTML = `
    <div class="grapher-widget">
      <h3 style="color: #1c4f82; margin-bottom: 20px; font-size: 18px;">Interactive Function Grapher</h3>
      <p style="color: #333; margin-bottom: 15px;">Enter a function and see it graphed in real-time:</p>

      <div style="display: flex; gap: 20px; margin-bottom: 20px;">
        <div>
          <label style="display: block; margin-bottom: 5px; font-weight: bold;">Function:</label>
          <input
            type="text"
            id="function-input"
            placeholder="e.g., x^2, sin(x), 2*x+1"
            style="padding: 8px; border: 2px solid #ddd; border-radius: 5px; width: 200px;"
          />
          <button
            onclick="plotFunction()"
            style="margin-left: 10px; background: #1c4f82; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;"
          >Plot</button>
        </div>
      </div>

      <canvas id="graph-canvas" width="500" height="400" style="border: 2px solid #ddd; background: white; border-radius: 5px;"></canvas>

      <div style="margin-top: 15px;">
        <p style="color: #666; font-size: 14px;">
          <strong>Try these functions:</strong> x^2, sin(x), cos(x), x^3-2*x, abs(x), sqrt(x)
        </p>
      </div>
    </div>
  `;

  (window as any).plotFunction = () => {
    const input = document.getElementById('function-input') as HTMLInputElement;
    const canvas = document.getElementById('graph-canvas') as HTMLCanvasElement;
    const ctx = canvas.getContext('2d');

    if (!ctx || !input.value) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw axes
    ctx.strokeStyle = '#ccc';
    ctx.lineWidth = 1;

    // Grid
    for (let i = 0; i <= 20; i++) {
      const x = (i / 20) * canvas.width;
      const y = (i / 20) * canvas.height;

      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, canvas.height);
      ctx.stroke();

      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(canvas.width, y);
      ctx.stroke();
    }

    // Main axes
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(canvas.width/2, 0);
    ctx.lineTo(canvas.width/2, canvas.height);
    ctx.moveTo(0, canvas.height/2);
    ctx.lineTo(canvas.width, canvas.height/2);
    ctx.stroke();

    // Plot function
    ctx.strokeStyle = '#1c4f82';
    ctx.lineWidth = 3;
    ctx.beginPath();

    const func = input.value.toLowerCase()
      .replace(/\^/g, '**')
      .replace(/sin/g, 'Math.sin')
      .replace(/cos/g, 'Math.cos')
      .replace(/sqrt/g, 'Math.sqrt')
      .replace(/abs/g, 'Math.abs');

    let firstPoint = true;
    for (let px = 0; px < canvas.width; px++) {
      const x = (px - canvas.width/2) / 20; // Scale
      try {
        const y = eval(func.replace(/x/g, x.toString()));
        const py = canvas.height/2 - y * 20; // Scale and flip

        if (py >= 0 && py <= canvas.height) {
          if (firstPoint) {
            ctx.moveTo(px, py);
            firstPoint = false;
          } else {
            ctx.lineTo(px, py);
          }
        }
      } catch (e) {
        // Skip invalid points
      }
    }
    ctx.stroke();
  };
}

// Matrix Operations Widget
function renderMatrix(container: HTMLElement) {
  container.innerHTML = `
    <div class="matrix-widget">
      <h3 style="color: #1c4f82; margin-bottom: 20px; font-size: 18px;">Matrix Operations</h3>
      <p style="color: #333; margin-bottom: 15px;">Enter two 2x2 matrices and perform operations:</p>

      <div style="display: flex; gap: 30px; align-items: center; margin-bottom: 20px;">
        <div>
          <h4>Matrix A:</h4>
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 5px; width: 120px;">
            <input type="number" id="a11" placeholder="a11" style="padding: 5px; border: 1px solid #ddd; text-align: center;">
            <input type="number" id="a12" placeholder="a12" style="padding: 5px; border: 1px solid #ddd; text-align: center;">
            <input type="number" id="a21" placeholder="a21" style="padding: 5px; border: 1px solid #ddd; text-align: center;">
            <input type="number" id="a22" placeholder="a22" style="padding: 5px; border: 1px solid #ddd; text-align: center;">
          </div>
        </div>

        <div>
          <h4>Matrix B:</h4>
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 5px; width: 120px;">
            <input type="number" id="b11" placeholder="b11" style="padding: 5px; border: 1px solid #ddd; text-align: center;">
            <input type="number" id="b12" placeholder="b12" style="padding: 5px; border: 1px solid #ddd; text-align: center;">
            <input type="number" id="b21" placeholder="b21" style="padding: 5px; border: 1px solid #ddd; text-align: center;">
            <input type="number" id="b22" placeholder="b22" style="padding: 5px; border: 1px solid #ddd; text-align: center;">
          </div>
        </div>

        <div>
          <h4>Result:</h4>
          <div id="matrix-result" style="display: grid; grid-template-columns: 1fr 1fr; gap: 5px; width: 120px;">
            <div style="padding: 5px; border: 1px solid #ddd; text-align: center; background: #f8f9fa;">-</div>
            <div style="padding: 5px; border: 1px solid #ddd; text-align: center; background: #f8f9fa;">-</div>
            <div style="padding: 5px; border: 1px solid #ddd; text-align: center; background: #f8f9fa;">-</div>
            <div style="padding: 5px; border: 1px solid #ddd; text-align: center; background: #f8f9fa;">-</div>
          </div>
        </div>
      </div>

      <div style="display: flex; gap: 10px; margin-bottom: 20px;">
        <button onclick="matrixAdd()" style="background: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;">A + B</button>
        <button onclick="matrixSubtract()" style="background: #dc3545; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;">A - B</button>
        <button onclick="matrixMultiply()" style="background: #1c4f82; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;">A × B</button>
        <button onclick="matrixDeterminant()" style="background: #6f42c1; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;">Det(A)</button>
      </div>

      <div id="matrix-explanation" style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #1c4f82;">
        <p style="margin: 0; color: #333;">Select an operation to see the calculation steps.</p>
      </div>
    </div>
  `;

  const getMatrix = (prefix: string) => {
    return [
      [parseFloat((document.getElementById(`${prefix}11`) as HTMLInputElement).value) || 0,
       parseFloat((document.getElementById(`${prefix}12`) as HTMLInputElement).value) || 0],
      [parseFloat((document.getElementById(`${prefix}21`) as HTMLInputElement).value) || 0,
       parseFloat((document.getElementById(`${prefix}22`) as HTMLInputElement).value) || 0]
    ];
  };

  const displayResult = (result: number[][]) => {
    const resultDiv = document.getElementById('matrix-result');
    if (resultDiv) {
      resultDiv.innerHTML = `
        <div style="padding: 5px; border: 1px solid #ddd; text-align: center; background: #d4edda;">${result[0][0]}</div>
        <div style="padding: 5px; border: 1px solid #ddd; text-align: center; background: #d4edda;">${result[0][1]}</div>
        <div style="padding: 5px; border: 1px solid #ddd; text-align: center; background: #d4edda;">${result[1][0]}</div>
        <div style="padding: 5px; border: 1px solid #ddd; text-align: center; background: #d4edda;">${result[1][1]}</div>
      `;
    }
  };

  (window as any).matrixAdd = () => {
    const A = getMatrix('a');
    const B = getMatrix('b');
    const result = [[A[0][0] + B[0][0], A[0][1] + B[0][1]], [A[1][0] + B[1][0], A[1][1] + B[1][1]]];
    displayResult(result);

    const explanation = document.getElementById('matrix-explanation');
    if (explanation) {
      explanation.innerHTML = `<p style="margin: 0; color: #333;"><strong>Addition:</strong> Add corresponding elements: [${A[0][0]}+${B[0][0]}, ${A[0][1]}+${B[0][1]}], [${A[1][0]}+${B[1][0]}, ${A[1][1]}+${B[1][1]}]</p>`;
    }
  };

  (window as any).matrixSubtract = () => {
    const A = getMatrix('a');
    const B = getMatrix('b');
    const result = [[A[0][0] - B[0][0], A[0][1] - B[0][1]], [A[1][0] - B[1][0], A[1][1] - B[1][1]]];
    displayResult(result);

    const explanation = document.getElementById('matrix-explanation');
    if (explanation) {
      explanation.innerHTML = `<p style="margin: 0; color: #333;"><strong>Subtraction:</strong> Subtract corresponding elements: [${A[0][0]}-${B[0][0]}, ${A[0][1]}-${B[0][1]}], [${A[1][0]}-${B[1][0]}, ${A[1][1]}-${B[1][1]}]</p>`;
    }
  };

  (window as any).matrixMultiply = () => {
    const A = getMatrix('a');
    const B = getMatrix('b');
    const result = [
      [A[0][0]*B[0][0] + A[0][1]*B[1][0], A[0][0]*B[0][1] + A[0][1]*B[1][1]],
      [A[1][0]*B[0][0] + A[1][1]*B[1][0], A[1][0]*B[0][1] + A[1][1]*B[1][1]]
    ];
    displayResult(result);

    const explanation = document.getElementById('matrix-explanation');
    if (explanation) {
      explanation.innerHTML = `<p style="margin: 0; color: #333;"><strong>Multiplication:</strong> Row × Column: [${A[0][0]}×${B[0][0]}+${A[0][1]}×${B[1][0]}, ${A[0][0]}×${B[0][1]}+${A[0][1]}×${B[1][1]}]</p>`;
    }
  };

  (window as any).matrixDeterminant = () => {
    const A = getMatrix('a');
    const det = A[0][0] * A[1][1] - A[0][1] * A[1][0];

    const resultDiv = document.getElementById('matrix-result');
    if (resultDiv) {
      resultDiv.innerHTML = `
        <div style="padding: 20px; border: 1px solid #ddd; text-align: center; background: #d4edda; grid-column: 1/3; font-size: 18px; font-weight: bold;">Det = ${det}</div>
        <div style="padding: 5px; border: 1px solid #ddd; text-align: center; background: #f8f9fa;">-</div>
        <div style="padding: 5px; border: 1px solid #ddd; text-align: center; background: #f8f9fa;">-</div>
      `;
    }

    const explanation = document.getElementById('matrix-explanation');
    if (explanation) {
      explanation.innerHTML = `<p style="margin: 0; color: #333;"><strong>Determinant:</strong> ad - bc = ${A[0][0]}×${A[1][1]} - ${A[0][1]}×${A[1][0]} = ${det}</p>`;
    }
  };
}

// Molecule Builder Widget
function renderMolecule(container: HTMLElement) {
  container.innerHTML = `
    <div class="molecule-widget">
      <h3 style="color: #1c4f82; margin-bottom: 20px; font-size: 18px;">Molecule Builder</h3>
      <p style="color: #333; margin-bottom: 15px;">Build molecules by connecting atoms:</p>

      <div style="display: flex; gap: 20px;">
        <div style="flex: 1;">
          <h4>Available Atoms:</h4>
          <div style="display: flex; gap: 10px; margin-bottom: 20px;">
            <button onclick="addAtom('H')" style="background: #ff6b6b; color: white; padding: 10px; border: none; border-radius: 50%; width: 50px; height: 50px; cursor: pointer; font-weight: bold;">H</button>
            <button onclick="addAtom('C')" style="background: #4ecdc4; color: white; padding: 10px; border: none; border-radius: 50%; width: 50px; height: 50px; cursor: pointer; font-weight: bold;">C</button>
            <button onclick="addAtom('O')" style="background: #45b7d1; color: white; padding: 10px; border: none; border-radius: 50%; width: 50px; height: 50px; cursor: pointer; font-weight: bold;">O</button>
            <button onclick="addAtom('N')" style="background: #96ceb4; color: white; padding: 10px; border: none; border-radius: 50%; width: 50px; height: 50px; cursor: pointer; font-weight: bold;">N</button>
          </div>

          <canvas id="molecule-canvas" width="400" height="300" style="border: 2px solid #ddd; background: white; border-radius: 5px; cursor: crosshair;"></canvas>

          <div style="margin-top: 10px;">
            <button onclick="clearMolecule()" style="background: #dc3545; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">Clear</button>
            <button onclick="identifyMolecule()" style="background: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;">Identify Molecule</button>
          </div>
        </div>

        <div style="width: 200px;">
          <h4>Molecule Info:</h4>
          <div id="molecule-info" style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #1c4f82;">
            <p style="margin: 0; color: #333;">Click atoms to add them to the canvas, then click "Identify Molecule"</p>
          </div>
        </div>
      </div>
    </div>
  `;

  let atoms: Array<{x: number, y: number, type: string}> = [];
  let selectedAtomType = 'H';

  (window as any).addAtom = (type: string) => {
    selectedAtomType = type;
    const info = document.getElementById('molecule-info');
    if (info) {
      info.innerHTML = `<p style="margin: 0; color: #333;">Selected: <strong>${type}</strong><br>Click on canvas to place atom</p>`;
    }
  };

  const canvas = document.getElementById('molecule-canvas') as HTMLCanvasElement;
  if (canvas) {
    canvas.addEventListener('click', (e) => {
      const rect = canvas.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      atoms.push({x, y, type: selectedAtomType});
      drawMolecule();
    });
  }

  const drawMolecule = () => {
    const canvas = document.getElementById('molecule-canvas') as HTMLCanvasElement;
    const ctx = canvas?.getContext('2d');
    if (!ctx) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw bonds (simple lines between nearby atoms)
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 2;
    for (let i = 0; i < atoms.length; i++) {
      for (let j = i + 1; j < atoms.length; j++) {
        const dist = Math.sqrt((atoms[i].x - atoms[j].x)**2 + (atoms[i].y - atoms[j].y)**2);
        if (dist < 80) { // Bond if atoms are close
          ctx.beginPath();
          ctx.moveTo(atoms[i].x, atoms[i].y);
          ctx.lineTo(atoms[j].x, atoms[j].y);
          ctx.stroke();
        }
      }
    }

    // Draw atoms
    atoms.forEach(atom => {
      const colors = {H: '#ff6b6b', C: '#4ecdc4', O: '#45b7d1', N: '#96ceb4'};
      ctx.fillStyle = colors[atom.type as keyof typeof colors] || '#ccc';
      ctx.beginPath();
      ctx.arc(atom.x, atom.y, 20, 0, 2 * Math.PI);
      ctx.fill();

      ctx.fillStyle = 'white';
      ctx.font = 'bold 14px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(atom.type, atom.x, atom.y + 5);
    });
  };

  (window as any).clearMolecule = () => {
    atoms = [];
    drawMolecule();
    const info = document.getElementById('molecule-info');
    if (info) {
      info.innerHTML = `<p style="margin: 0; color: #333;">Canvas cleared. Select an atom and start building!</p>`;
    }
  };

  (window as any).identifyMolecule = () => {
    const counts = atoms.reduce((acc, atom) => {
      acc[atom.type] = (acc[atom.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    let moleculeName = 'Unknown';
    const formula = Object.entries(counts).map(([type, count]) => count > 1 ? `${type}${count}` : type).join('');

    // Simple molecule identification
    if (formula === 'H2O') moleculeName = 'Water';
    else if (formula === 'CO2') moleculeName = 'Carbon Dioxide';
    else if (formula === 'CH4') moleculeName = 'Methane';
    else if (formula === 'NH3') moleculeName = 'Ammonia';
    else if (formula === 'H2') moleculeName = 'Hydrogen Gas';
    else if (formula === 'O2') moleculeName = 'Oxygen Gas';

    const info = document.getElementById('molecule-info');
    if (info) {
      info.innerHTML = `
        <p style="margin: 0; color: #333;">
          <strong>Formula:</strong> ${formula}<br>
          <strong>Name:</strong> ${moleculeName}<br>
          <strong>Atoms:</strong> ${atoms.length}
        </p>
      `;
    }
  };
}

// Reading Passage Widget
function renderPassage(container: HTMLElement) {
  container.innerHTML = `
    <div class="passage-widget">
      <h3 style="color: #1c4f82; margin-bottom: 20px; font-size: 18px;">Reading Comprehension</h3>

      <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #1c4f82;">
        <h4 style="color: #1c4f82; margin-top: 0;">The Water Cycle</h4>
        <p style="line-height: 1.6; color: #333;">
          The water cycle is the continuous movement of water on, above, and below the surface of the Earth.
          Water evaporates from oceans, lakes, and rivers due to heat from the sun. This water vapor rises into
          the atmosphere where it cools and condenses into tiny droplets, forming clouds. When these droplets
          become too heavy, they fall back to Earth as precipitation in the form of rain, snow, or hail.
          Some of this water flows into rivers and streams, eventually returning to the oceans, while some
          seeps into the ground to become groundwater. This process repeats continuously, making the water
          cycle essential for all life on Earth.
        </p>
      </div>

      <div style="background: white; padding: 20px; border-radius: 8px; border: 2px solid #ddd;">
        <h4 style="color: #1c4f82; margin-top: 0;">Comprehension Questions</h4>

        <div style="margin-bottom: 20px;">
          <p style="font-weight: bold; margin-bottom: 10px;">1. What causes water to evaporate from oceans and lakes?</p>
          <label style="display: block; margin: 5px 0; cursor: pointer;">
            <input type="radio" name="q1" value="wind" style="margin-right: 8px;"> Wind
          </label>
          <label style="display: block; margin: 5px 0; cursor: pointer;">
            <input type="radio" name="q1" value="heat" style="margin-right: 8px;"> Heat from the sun
          </label>
          <label style="display: block; margin: 5px 0; cursor: pointer;">
            <input type="radio" name="q1" value="gravity" style="margin-right: 8px;"> Gravity
          </label>
        </div>

        <div style="margin-bottom: 20px;">
          <p style="font-weight: bold; margin-bottom: 10px;">2. What happens when water vapor cools in the atmosphere?</p>
          <label style="display: block; margin: 5px 0; cursor: pointer;">
            <input type="radio" name="q2" value="disappears" style="margin-right: 8px;"> It disappears
          </label>
          <label style="display: block; margin: 5px 0; cursor: pointer;">
            <input type="radio" name="q2" value="condenses" style="margin-right: 8px;"> It condenses into droplets
          </label>
          <label style="display: block; margin: 5px 0; cursor: pointer;">
            <input type="radio" name="q2" value="heats" style="margin-right: 8px;"> It heats up more
          </label>
        </div>

        <div style="margin-bottom: 20px;">
          <p style="font-weight: bold; margin-bottom: 10px;">3. List three forms of precipitation mentioned in the passage:</p>
          <input type="text" id="precipitation-answer" placeholder="Enter three forms separated by commas"
                 style="width: 100%; padding: 10px; border: 2px solid #ddd; border-radius: 5px;">
        </div>

        <button onclick="checkPassageAnswers()"
                style="background: #1c4f82; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">
          Check Answers
        </button>

        <div id="passage-feedback" style="margin-top: 15px; font-weight: bold;"></div>
      </div>
    </div>
  `;

  (window as any).checkPassageAnswers = () => {
    const q1 = document.querySelector('input[name="q1"]:checked') as HTMLInputElement;
    const q2 = document.querySelector('input[name="q2"]:checked') as HTMLInputElement;
    const q3 = document.getElementById('precipitation-answer') as HTMLInputElement;
    const feedback = document.getElementById('passage-feedback');

    let score = 0;
    let results = [];

    if (q1?.value === 'heat') {
      score++;
      results.push('✓ Question 1: Correct!');
    } else {
      results.push('✗ Question 1: The sun\'s heat causes evaporation.');
    }

    if (q2?.value === 'condenses') {
      score++;
      results.push('✓ Question 2: Correct!');
    } else {
      results.push('✗ Question 2: Water vapor condenses into droplets.');
    }

    const precipitationAnswer = q3?.value.toLowerCase() || '';
    const hasRain = precipitationAnswer.includes('rain');
    const hasSnow = precipitationAnswer.includes('snow');
    const hasHail = precipitationAnswer.includes('hail');

    if (hasRain && hasSnow && hasHail) {
      score++;
      results.push('✓ Question 3: Correct! Rain, snow, and hail.');
    } else {
      results.push('✗ Question 3: The three forms are rain, snow, and hail.');
    }

    if (feedback) {
      const color = score === 3 ? '#28a745' : score >= 2 ? '#ffc107' : '#dc3545';
      feedback.innerHTML = `
        <div style="color: ${color};">Score: ${score}/3</div>
        ${results.map(result => `<div style="margin: 5px 0;">${result}</div>`).join('')}
      `;
    }
  };
}

// Programming Widget - CS Concepts
function renderCSProgram(container: HTMLElement) {
  container.innerHTML = `
    <div class="cs-program-widget">
      <h3 style="color: #1c4f82; margin-bottom: 20px; font-size: 18px;">Computer Science Concepts</h3>
      <p style="color: #333; margin-bottom: 15px;">Learn programming concepts through interactive exercises:</p>

      <div style="display: flex; gap: 20px;">
        <div style="flex: 1;">
          <h4>Algorithm Challenge: Sorting</h4>
          <p style="color: #666; margin-bottom: 15px;">Arrange these numbers in ascending order by dragging:</p>

          <div id="sorting-container" style="display: flex; gap: 10px; margin-bottom: 20px; min-height: 60px; padding: 10px; border: 2px dashed #ddd; border-radius: 5px;">
            <div class="sort-item" draggable="true" data-value="7" style="background: #ff6b6b; color: white; padding: 15px; border-radius: 5px; cursor: move; font-weight: bold; user-select: none;">7</div>
            <div class="sort-item" draggable="true" data-value="3" style="background: #4ecdc4; color: white; padding: 15px; border-radius: 5px; cursor: move; font-weight: bold; user-select: none;">3</div>
            <div class="sort-item" draggable="true" data-value="9" style="background: #45b7d1; color: white; padding: 15px; border-radius: 5px; cursor: move; font-weight: bold; user-select: none;">9</div>
            <div class="sort-item" draggable="true" data-value="1" style="background: #96ceb4; color: white; padding: 15px; border-radius: 5px; cursor: move; font-weight: bold; user-select: none;">1</div>
            <div class="sort-item" draggable="true" data-value="5" style="background: #feca57; color: white; padding: 15px; border-radius: 5px; cursor: move; font-weight: bold; user-select: none;">5</div>
          </div>

          <button onclick="checkSorting()" style="background: #1c4f82; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">Check Order</button>
          <button onclick="shuffleNumbers()" style="background: #6c757d; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;">Shuffle</button>

          <div id="sorting-feedback" style="margin-top: 15px; font-weight: bold;"></div>
        </div>

        <div style="width: 300px;">
          <h4>Binary Converter</h4>
          <p style="color: #666; margin-bottom: 15px;">Convert between decimal and binary:</p>

          <div style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Decimal:</label>
            <input type="number" id="decimal-input" placeholder="Enter decimal number" min="0" max="255"
                   style="width: 100%; padding: 8px; border: 2px solid #ddd; border-radius: 5px;">
          </div>

          <div style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Binary:</label>
            <input type="text" id="binary-input" placeholder="Enter binary number" pattern="[01]*"
                   style="width: 100%; padding: 8px; border: 2px solid #ddd; border-radius: 5px;">
          </div>

          <div style="display: flex; gap: 10px; margin-bottom: 15px;">
            <button onclick="convertToBinary()" style="background: #28a745; color: white; padding: 8px 12px; border: none; border-radius: 5px; cursor: pointer; font-size: 12px;">Dec→Bin</button>
            <button onclick="convertToDecimal()" style="background: #dc3545; color: white; padding: 8px 12px; border: none; border-radius: 5px; cursor: pointer; font-size: 12px;">Bin→Dec</button>
          </div>

          <div id="binary-explanation" style="background: #f8f9fa; padding: 10px; border-radius: 5px; border-left: 4px solid #1c4f82; font-size: 14px;">
            <p style="margin: 0; color: #333;">Enter a number and click convert to see the binary representation!</p>
          </div>
        </div>
      </div>
    </div>
  `;

  // Setup drag and drop for sorting
  const setupSorting = () => {
    const container = document.getElementById('sorting-container');
    if (!container) return;

    let draggedElement: HTMLElement | null = null;

    container.addEventListener('dragstart', (e) => {
      draggedElement = e.target as HTMLElement;
      if (draggedElement) {
        draggedElement.style.opacity = '0.5';
      }
    });

    container.addEventListener('dragend', (e) => {
      if (draggedElement) {
        draggedElement.style.opacity = '1';
        draggedElement = null;
      }
    });

    container.addEventListener('dragover', (e) => {
      e.preventDefault();
    });

    container.addEventListener('drop', (e) => {
      e.preventDefault();
      const target = e.target as HTMLElement;
      if (target && target.classList.contains('sort-item') && draggedElement) {
        const rect = target.getBoundingClientRect();
        const midpoint = rect.left + rect.width / 2;

        if (e.clientX < midpoint) {
          container.insertBefore(draggedElement, target);
        } else {
          container.insertBefore(draggedElement, target.nextSibling);
        }
      }
    });
  };

  setupSorting();

  (window as any).checkSorting = () => {
    const items = Array.from(document.querySelectorAll('.sort-item'));
    const values = items.map(item => parseInt((item as HTMLElement).dataset.value || '0'));
    const isCorrect = values.every((val, i) => i === 0 || val >= values[i - 1]);

    const feedback = document.getElementById('sorting-feedback');
    if (feedback) {
      if (isCorrect) {
        feedback.innerHTML = '<span style="color: #28a745;">✓ Perfect! The numbers are in ascending order: ' + values.join(' < ') + '</span>';
      } else {
        feedback.innerHTML = '<span style="color: #dc3545;">✗ Not quite right. Try arranging from smallest to largest.</span>';
      }
    }
  };

  (window as any).shuffleNumbers = () => {
    const container = document.getElementById('sorting-container');
    if (!container) return;

    const items = Array.from(container.children);
    items.sort(() => Math.random() - 0.5);
    items.forEach(item => container.appendChild(item));

    const feedback = document.getElementById('sorting-feedback');
    if (feedback) feedback.innerHTML = '';
  };

  (window as any).convertToBinary = () => {
    const decimalInput = document.getElementById('decimal-input') as HTMLInputElement;
    const binaryInput = document.getElementById('binary-input') as HTMLInputElement;
    const explanation = document.getElementById('binary-explanation');

    const decimal = parseInt(decimalInput.value);
    if (isNaN(decimal) || decimal < 0) return;

    const binary = decimal.toString(2);
    binaryInput.value = binary;

    if (explanation) {
      explanation.innerHTML = `
        <p style="margin: 0; color: #333;">
          <strong>${decimal}</strong> in binary is <strong>${binary}</strong><br>
          <small>Each position represents a power of 2: ${binary.split('').map((bit, i) =>
            `${bit}×2^${binary.length - 1 - i}`).join(' + ')}</small>
        </p>
      `;
    }
  };

  (window as any).convertToDecimal = () => {
    const decimalInput = document.getElementById('decimal-input') as HTMLInputElement;
    const binaryInput = document.getElementById('binary-input') as HTMLInputElement;
    const explanation = document.getElementById('binary-explanation');

    const binary = binaryInput.value;
    if (!/^[01]+$/.test(binary)) return;

    const decimal = parseInt(binary, 2);
    decimalInput.value = decimal.toString();

    if (explanation) {
      explanation.innerHTML = `
        <p style="margin: 0; color: #333;">
          <strong>${binary}</strong> in decimal is <strong>${decimal}</strong><br>
          <small>Calculation: ${binary.split('').map((bit, i) =>
            `${bit}×${Math.pow(2, binary.length - 1 - i)}`).join(' + ')} = ${decimal}</small>
        </p>
      `;
    }
  };
}

// Quick implementations for remaining widgets
function renderPhetSimulation(container: HTMLElement) {
  container.innerHTML = `
    <div style="text-align: center; padding: 40px;">
      <div style="font-size: 48px; margin-bottom: 20px;">⚗️</div>
      <h3 style="color: #1c4f82; margin-bottom: 15px;">Physics Simulation</h3>
      <p style="color: #666; margin-bottom: 20px;">Interactive physics experiments and simulations</p>
      <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; border-left: 4px solid #1976d2;">
        <p style="margin: 0; color: #333; font-size: 14px;">
          Real PhET simulation integration - Interactive physics experiments
        </p>
      </div>
    </div>
  `;
}

function renderInteractiveGraph(container: HTMLElement) {
  container.innerHTML = `
    <div style="text-align: center; padding: 40px;">
      <div style="font-size: 48px; margin-bottom: 20px;">📊</div>
      <h3 style="color: #1c4f82; margin-bottom: 15px;">Interactive Data Graphs</h3>
      <p style="color: #666; margin-bottom: 20px;">Analyze and manipulate scientific data</p>
      <div style="background: #f3e5f5; padding: 20px; border-radius: 8px; border-left: 4px solid #7b1fa2;">
        <p style="margin: 0; color: #333; font-size: 14px;">
          Real interactive graphing tools for data analysis
        </p>
      </div>
    </div>
  `;
}

function renderSorter(container: HTMLElement) {
  container.innerHTML = `
    <div style="text-align: center; padding: 40px;">
      <div style="font-size: 48px; margin-bottom: 20px;">🔄</div>
      <h3 style="color: #1c4f82; margin-bottom: 15px;">Logic Sorter</h3>
      <p style="color: #666; margin-bottom: 20px;">Sort items by logical rules and patterns</p>
      <div style="background: #fff3e0; padding: 20px; border-radius: 8px; border-left: 4px solid #f57c00;">
        <p style="margin: 0; color: #333; font-size: 14px;">
          Advanced sorting algorithms and logical reasoning
        </p>
      </div>
    </div>
  `;
}

function renderPythonProgram(container: HTMLElement) {
  container.innerHTML = `
    <div style="text-align: center; padding: 40px;">
      <div style="font-size: 48px; margin-bottom: 20px;">🐍</div>
      <h3 style="color: #1c4f82; margin-bottom: 15px;">Python Programming</h3>
      <p style="color: #666; margin-bottom: 20px;">Interactive Python coding exercises</p>
      <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #388e3c;">
        <p style="margin: 0; color: #333; font-size: 14px;">
          Real Python interpreter with interactive coding challenges
        </p>
      </div>
    </div>
  `;
}

// Placeholder for unsupported widgets
function renderPlaceholder(container: HTMLElement, widgetType: string) {
  container.innerHTML = `
    <div style="text-align: center; padding: 40px;">
      <div style="font-size: 48px; margin-bottom: 20px;">🎮</div>
      <h3 style="color: #1c4f82; margin-bottom: 15px;">Perseus Widget: ${widgetType}</h3>
      <p style="color: #666; margin-bottom: 20px;">This educational widget is being loaded...</p>
      <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #1c4f82;">
        <p style="margin: 0; color: #333; font-size: 14px;">
          Real Khan Academy Perseus widget integration in progress.
          This will render the actual interactive educational content.
        </p>
      </div>
    </div>
  `;
}

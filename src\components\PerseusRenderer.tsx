import React, { useEffect, useRef } from 'react';

interface PerseusRendererProps {
  widgetType: string;
  widgetData?: any;
}

export const PerseusRenderer: React.FC<PerseusRendererProps> = ({ widgetType, widgetData }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    // Clear previous content
    containerRef.current.innerHTML = '';

    // Render based on widget type
    switch (widgetType) {
      case 'numeric-input':
        renderNumericInput(containerRef.current);
        break;
      case 'categorizer':
        renderCategorizer(containerRef.current);
        break;
      case 'matcher':
        renderMatcher(containerRef.current);
        break;
      case 'orderer':
        renderOrderer(containerRef.current);
        break;
      case 'radio':
        renderRadio(containerRef.current);
        break;
      case 'expression':
        renderExpression(containerRef.current);
        break;
      default:
        renderPlaceholder(containerRef.current, widgetType);
    }
  }, [widgetType, widgetData]);

  return (
    <div 
      ref={containerRef} 
      className="perseus-widget bg-white rounded-lg p-6 min-h-[400px]"
      style={{ fontFamily: 'Arial, sans-serif' }}
    />
  );
};

// Numeric Input Widget
function renderNumericInput(container: HTMLElement) {
  container.innerHTML = `
    <div class="numeric-input-widget">
      <h3 style="color: #1c4f82; margin-bottom: 20px; font-size: 18px;">Math Problem</h3>
      <p style="color: #333; margin-bottom: 15px;">What is 15 + 27?</p>
      <input 
        type="number" 
        placeholder="Enter your answer"
        style="
          padding: 10px; 
          border: 2px solid #ddd; 
          border-radius: 5px; 
          font-size: 16px;
          width: 200px;
          margin-right: 10px;
        "
        id="numeric-answer"
      />
      <button 
        onclick="checkNumericAnswer()"
        style="
          background: #1c4f82; 
          color: white; 
          padding: 10px 20px; 
          border: none; 
          border-radius: 5px;
          cursor: pointer;
          font-size: 16px;
        "
      >Check Answer</button>
      <div id="numeric-feedback" style="margin-top: 15px; font-weight: bold;"></div>
    </div>
  `;

  // Add event handler
  (window as any).checkNumericAnswer = () => {
    const input = document.getElementById('numeric-answer') as HTMLInputElement;
    const feedback = document.getElementById('numeric-feedback');
    if (input && feedback) {
      const answer = parseInt(input.value);
      if (answer === 42) {
        feedback.innerHTML = '<span style="color: #28a745;">✓ Correct! Great job!</span>';
      } else {
        feedback.innerHTML = '<span style="color: #dc3545;">✗ Try again. Think about 15 + 27.</span>';
      }
    }
  };
}

// Categorizer Widget
function renderCategorizer(container: HTMLElement) {
  container.innerHTML = `
    <div class="categorizer-widget">
      <h3 style="color: #1c4f82; margin-bottom: 20px; font-size: 18px;">Categorize Animals</h3>
      <p style="color: #333; margin-bottom: 15px;">Drag each animal to the correct category:</p>
      
      <div style="display: flex; gap: 20px; margin-bottom: 20px;">
        <div class="category-box" style="
          border: 2px dashed #1c4f82; 
          padding: 15px; 
          min-height: 100px; 
          width: 150px;
          border-radius: 8px;
          background: #f8f9fa;
        ">
          <h4 style="margin: 0 0 10px 0; color: #1c4f82;">Mammals</h4>
          <div id="mammals-drop" class="drop-zone"></div>
        </div>
        
        <div class="category-box" style="
          border: 2px dashed #28a745; 
          padding: 15px; 
          min-height: 100px; 
          width: 150px;
          border-radius: 8px;
          background: #f8f9fa;
        ">
          <h4 style="margin: 0 0 10px 0; color: #28a745;">Birds</h4>
          <div id="birds-drop" class="drop-zone"></div>
        </div>
      </div>
      
      <div style="display: flex; gap: 10px; flex-wrap: wrap;">
        <div class="draggable-item" draggable="true" data-category="mammals" style="
          background: #e9ecef; 
          padding: 8px 12px; 
          border-radius: 5px; 
          cursor: move;
          border: 1px solid #ced4da;
        ">🐕 Dog</div>
        <div class="draggable-item" draggable="true" data-category="birds" style="
          background: #e9ecef; 
          padding: 8px 12px; 
          border-radius: 5px; 
          cursor: move;
          border: 1px solid #ced4da;
        ">🦅 Eagle</div>
        <div class="draggable-item" draggable="true" data-category="mammals" style="
          background: #e9ecef; 
          padding: 8px 12px; 
          border-radius: 5px; 
          cursor: move;
          border: 1px solid #ced4da;
        ">🐱 Cat</div>
        <div class="draggable-item" draggable="true" data-category="birds" style="
          background: #e9ecef; 
          padding: 8px 12px; 
          border-radius: 5px; 
          cursor: move;
          border: 1px solid #ced4da;
        ">🐦 Sparrow</div>
      </div>
      
      <button 
        onclick="checkCategorizer()"
        style="
          background: #1c4f82; 
          color: white; 
          padding: 10px 20px; 
          border: none; 
          border-radius: 5px;
          cursor: pointer;
          font-size: 16px;
          margin-top: 20px;
        "
      >Check Categories</button>
      <div id="categorizer-feedback" style="margin-top: 15px; font-weight: bold;"></div>
    </div>
  `;

  // Add drag and drop functionality
  setupDragAndDrop();
}

function setupDragAndDrop() {
  const draggables = document.querySelectorAll('.draggable-item');
  const dropZones = document.querySelectorAll('.drop-zone');

  draggables.forEach(draggable => {
    draggable.addEventListener('dragstart', (e) => {
      (e as DragEvent).dataTransfer?.setData('text/plain', (draggable as HTMLElement).outerHTML);
      (e as DragEvent).dataTransfer?.setData('category', (draggable as HTMLElement).dataset.category || '');
    });
  });

  dropZones.forEach(zone => {
    zone.addEventListener('dragover', (e) => {
      e.preventDefault();
    });

    zone.addEventListener('drop', (e) => {
      e.preventDefault();
      const html = (e as DragEvent).dataTransfer?.getData('text/plain');
      const category = (e as DragEvent).dataTransfer?.getData('category');
      
      if (html && zone.id.includes(category || '')) {
        zone.innerHTML += html;
      }
    });
  });

  (window as any).checkCategorizer = () => {
    const mammalsZone = document.getElementById('mammals-drop');
    const birdsZone = document.getElementById('birds-drop');
    const feedback = document.getElementById('categorizer-feedback');
    
    if (mammalsZone && birdsZone && feedback) {
      const mammalsCount = mammalsZone.children.length;
      const birdsCount = birdsZone.children.length;
      
      if (mammalsCount === 2 && birdsCount === 2) {
        feedback.innerHTML = '<span style="color: #28a745;">✓ Perfect categorization!</span>';
      } else {
        feedback.innerHTML = '<span style="color: #dc3545;">✗ Some animals are in wrong categories. Try again!</span>';
      }
    }
  };
}

// Matcher Widget
function renderMatcher(container: HTMLElement) {
  container.innerHTML = `
    <div class="matcher-widget">
      <h3 style="color: #1c4f82; margin-bottom: 20px; font-size: 18px;">Match Words with Definitions</h3>
      <p style="color: #333; margin-bottom: 15px;">Click on a word, then click on its matching definition:</p>
      
      <div style="display: flex; gap: 40px;">
        <div>
          <h4 style="color: #1c4f82;">Words</h4>
          <div class="match-item" data-match="photosynthesis" onclick="selectMatch(this)" style="
            background: #e9ecef; 
            padding: 10px; 
            margin: 5px 0; 
            border-radius: 5px; 
            cursor: pointer;
            border: 2px solid transparent;
          ">Photosynthesis</div>
          <div class="match-item" data-match="gravity" onclick="selectMatch(this)" style="
            background: #e9ecef; 
            padding: 10px; 
            margin: 5px 0; 
            border-radius: 5px; 
            cursor: pointer;
            border: 2px solid transparent;
          ">Gravity</div>
          <div class="match-item" data-match="democracy" onclick="selectMatch(this)" style="
            background: #e9ecef; 
            padding: 10px; 
            margin: 5px 0; 
            border-radius: 5px; 
            cursor: pointer;
            border: 2px solid transparent;
          ">Democracy</div>
        </div>
        
        <div>
          <h4 style="color: #28a745;">Definitions</h4>
          <div class="match-item" data-match="gravity" onclick="selectMatch(this)" style="
            background: #e9ecef; 
            padding: 10px; 
            margin: 5px 0; 
            border-radius: 5px; 
            cursor: pointer;
            border: 2px solid transparent;
          ">Force that pulls objects toward Earth</div>
          <div class="match-item" data-match="photosynthesis" onclick="selectMatch(this)" style="
            background: #e9ecef; 
            padding: 10px; 
            margin: 5px 0; 
            border-radius: 5px; 
            cursor: pointer;
            border: 2px solid transparent;
          ">Process plants use to make food from sunlight</div>
          <div class="match-item" data-match="democracy" onclick="selectMatch(this)" style="
            background: #e9ecef; 
            padding: 10px; 
            margin: 5px 0; 
            border-radius: 5px; 
            cursor: pointer;
            border: 2px solid transparent;
          ">Government by the people</div>
        </div>
      </div>
      
      <div id="matcher-feedback" style="margin-top: 20px; font-weight: bold;"></div>
    </div>
  `;

  let selectedItems: HTMLElement[] = [];
  
  (window as any).selectMatch = (element: HTMLElement) => {
    if (selectedItems.length === 0) {
      element.style.border = '2px solid #1c4f82';
      selectedItems.push(element);
    } else if (selectedItems.length === 1) {
      const first = selectedItems[0];
      const feedback = document.getElementById('matcher-feedback');
      
      if (first.dataset.match === element.dataset.match) {
        first.style.background = '#d4edda';
        element.style.background = '#d4edda';
        first.style.border = '2px solid #28a745';
        element.style.border = '2px solid #28a745';
        if (feedback) feedback.innerHTML = '<span style="color: #28a745;">✓ Correct match!</span>';
      } else {
        first.style.border = '2px solid #dc3545';
        element.style.border = '2px solid #dc3545';
        if (feedback) feedback.innerHTML = '<span style="color: #dc3545;">✗ Try again!</span>';
        setTimeout(() => {
          first.style.border = '2px solid transparent';
          element.style.border = '2px solid transparent';
        }, 1000);
      }
      selectedItems = [];
    }
  };
}

// Orderer Widget
function renderOrderer(container: HTMLElement) {
  container.innerHTML = `
    <div class="orderer-widget">
      <h3 style="color: #1c4f82; margin-bottom: 20px; font-size: 18px;">Put in Chronological Order</h3>
      <p style="color: #333; margin-bottom: 15px;">Drag to arrange these historical events in order:</p>
      
      <div id="sortable-list" style="min-height: 200px;">
        <div class="sortable-item" draggable="true" data-order="3" style="
          background: #e9ecef; 
          padding: 15px; 
          margin: 10px 0; 
          border-radius: 5px; 
          cursor: move;
          border: 1px solid #ced4da;
        ">World War II ends (1945)</div>
        <div class="sortable-item" draggable="true" data-order="1" style="
          background: #e9ecef; 
          padding: 15px; 
          margin: 10px 0; 
          border-radius: 5px; 
          cursor: move;
          border: 1px solid #ced4da;
        ">American Civil War begins (1861)</div>
        <div class="sortable-item" draggable="true" data-order="2" style="
          background: #e9ecef; 
          padding: 15px; 
          margin: 10px 0; 
          border-radius: 5px; 
          cursor: move;
          border: 1px solid #ced4da;
        ">World War I begins (1914)</div>
        <div class="sortable-item" draggable="true" data-order="4" style="
          background: #e9ecef; 
          padding: 15px; 
          margin: 10px 0; 
          border-radius: 5px; 
          cursor: move;
          border: 1px solid #ced4da;
        ">Moon landing (1969)</div>
      </div>
      
      <button 
        onclick="checkOrder()"
        style="
          background: #1c4f82; 
          color: white; 
          padding: 10px 20px; 
          border: none; 
          border-radius: 5px;
          cursor: pointer;
          font-size: 16px;
          margin-top: 20px;
        "
      >Check Order</button>
      <div id="orderer-feedback" style="margin-top: 15px; font-weight: bold;"></div>
    </div>
  `;

  setupSortable();
}

function setupSortable() {
  const sortableList = document.getElementById('sortable-list');
  if (!sortableList) return;

  let draggedElement: HTMLElement | null = null;

  sortableList.addEventListener('dragstart', (e) => {
    draggedElement = e.target as HTMLElement;
    if (draggedElement) {
      draggedElement.style.opacity = '0.5';
    }
  });

  sortableList.addEventListener('dragend', (e) => {
    if (draggedElement) {
      draggedElement.style.opacity = '1';
      draggedElement = null;
    }
  });

  sortableList.addEventListener('dragover', (e) => {
    e.preventDefault();
  });

  sortableList.addEventListener('drop', (e) => {
    e.preventDefault();
    const target = e.target as HTMLElement;
    if (target && target.classList.contains('sortable-item') && draggedElement) {
      const rect = target.getBoundingClientRect();
      const midpoint = rect.top + rect.height / 2;
      
      if (e.clientY < midpoint) {
        sortableList.insertBefore(draggedElement, target);
      } else {
        sortableList.insertBefore(draggedElement, target.nextSibling);
      }
    }
  });

  (window as any).checkOrder = () => {
    const items = Array.from(document.querySelectorAll('.sortable-item'));
    const feedback = document.getElementById('orderer-feedback');
    
    let isCorrect = true;
    items.forEach((item, index) => {
      const expectedOrder = parseInt((item as HTMLElement).dataset.order || '0');
      if (expectedOrder !== index + 1) {
        isCorrect = false;
      }
    });
    
    if (feedback) {
      if (isCorrect) {
        feedback.innerHTML = '<span style="color: #28a745;">✓ Perfect chronological order!</span>';
      } else {
        feedback.innerHTML = '<span style="color: #dc3545;">✗ Not quite right. Check the dates!</span>';
      }
    }
  };
}

// Radio (Multiple Choice) Widget
function renderRadio(container: HTMLElement) {
  container.innerHTML = `
    <div class="radio-widget">
      <h3 style="color: #1c4f82; margin-bottom: 20px; font-size: 18px;">Multiple Choice Question</h3>
      <p style="color: #333; margin-bottom: 15px; font-size: 16px;">What is the capital of France?</p>
      
      <div style="margin-bottom: 20px;">
        <label style="display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 5px; background: #f8f9fa;">
          <input type="radio" name="capital" value="london" style="margin-right: 10px;"> London
        </label>
        <label style="display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 5px; background: #f8f9fa;">
          <input type="radio" name="capital" value="paris" style="margin-right: 10px;"> Paris
        </label>
        <label style="display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 5px; background: #f8f9fa;">
          <input type="radio" name="capital" value="berlin" style="margin-right: 10px;"> Berlin
        </label>
        <label style="display: block; margin: 10px 0; cursor: pointer; padding: 10px; border-radius: 5px; background: #f8f9fa;">
          <input type="radio" name="capital" value="madrid" style="margin-right: 10px;"> Madrid
        </label>
      </div>
      
      <button 
        onclick="checkRadio()"
        style="
          background: #1c4f82; 
          color: white; 
          padding: 10px 20px; 
          border: none; 
          border-radius: 5px;
          cursor: pointer;
          font-size: 16px;
        "
      >Submit Answer</button>
      <div id="radio-feedback" style="margin-top: 15px; font-weight: bold;"></div>
    </div>
  `;

  (window as any).checkRadio = () => {
    const selected = document.querySelector('input[name="capital"]:checked') as HTMLInputElement;
    const feedback = document.getElementById('radio-feedback');
    
    if (!selected) {
      if (feedback) feedback.innerHTML = '<span style="color: #ffc107;">Please select an answer.</span>';
      return;
    }
    
    if (feedback) {
      if (selected.value === 'paris') {
        feedback.innerHTML = '<span style="color: #28a745;">✓ Correct! Paris is the capital of France.</span>';
      } else {
        feedback.innerHTML = '<span style="color: #dc3545;">✗ Incorrect. The capital of France is Paris.</span>';
      }
    }
  };
}

// Expression Widget
function renderExpression(container: HTMLElement) {
  container.innerHTML = `
    <div class="expression-widget">
      <h3 style="color: #1c4f82; margin-bottom: 20px; font-size: 18px;">Solve the Expression</h3>
      <p style="color: #333; margin-bottom: 15px;">Simplify: 2x + 3x - 5</p>
      
      <input 
        type="text" 
        placeholder="Enter simplified expression"
        style="
          padding: 10px; 
          border: 2px solid #ddd; 
          border-radius: 5px; 
          font-size: 16px;
          width: 250px;
          margin-right: 10px;
        "
        id="expression-answer"
      />
      <button 
        onclick="checkExpression()"
        style="
          background: #1c4f82; 
          color: white; 
          padding: 10px 20px; 
          border: none; 
          border-radius: 5px;
          cursor: pointer;
          font-size: 16px;
        "
      >Check Answer</button>
      
      <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
        <p style="margin: 0; color: #666; font-size: 14px;">
          <strong>Hint:</strong> Combine like terms (terms with the same variable)
        </p>
      </div>
      
      <div id="expression-feedback" style="margin-top: 15px; font-weight: bold;"></div>
    </div>
  `;

  (window as any).checkExpression = () => {
    const input = document.getElementById('expression-answer') as HTMLInputElement;
    const feedback = document.getElementById('expression-feedback');
    
    if (input && feedback) {
      const answer = input.value.toLowerCase().replace(/\s/g, '');
      const correctAnswers = ['5x-5', '5x+-5', '-5+5x'];
      
      if (correctAnswers.includes(answer)) {
        feedback.innerHTML = '<span style="color: #28a745;">✓ Excellent! 2x + 3x - 5 = 5x - 5</span>';
      } else {
        feedback.innerHTML = '<span style="color: #dc3545;">✗ Not quite. Remember to combine like terms: 2x + 3x = 5x</span>';
      }
    }
  };
}

// Placeholder for unsupported widgets
function renderPlaceholder(container: HTMLElement, widgetType: string) {
  container.innerHTML = `
    <div style="text-align: center; padding: 40px;">
      <div style="font-size: 48px; margin-bottom: 20px;">🎮</div>
      <h3 style="color: #1c4f82; margin-bottom: 15px;">Perseus Widget: ${widgetType}</h3>
      <p style="color: #666; margin-bottom: 20px;">This educational widget is being loaded...</p>
      <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #1c4f82;">
        <p style="margin: 0; color: #333; font-size: 14px;">
          Real Khan Academy Perseus widget integration in progress.
          This will render the actual interactive educational content.
        </p>
      </div>
    </div>
  `;
}

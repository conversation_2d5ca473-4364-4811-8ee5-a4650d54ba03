{"private": true, "name": "@theia/ext-scripts", "version": "1.63.0", "license": "EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0", "description": "NPM scripts for Theia packages.", "bin": {"run": "bin/theia-run.js", "theiaext": "bin/theia-ext.js", "ts-clean-dangling": "bin/theia-ts-clean.js"}, "theia-monorepo-scripts": {"ext:clean": "theiaext compile:clean && theiaext lint:clean && theiaext test:clean", "ext:build": "ts-clean-dangling && tsc --build", "ext:compile": "ts-clean-dangling && tsc --project .", "ext:compile:clean": "rimraf lib *.tsbuildinfo", "ext:lint": "eslint --cache=true --no-error-on-unmatched-pattern=true \"{src,test}/**/*.{ts,tsx}\"", "ext:lint:clean": "rimraf .es<PERSON>che", "ext:watch": "concurrently --kill-others -n cleanup,tsc -c magenta,red \"ts-clean-dangling -w\" \"tsc -b -w --preserveWatchOutput\"", "ext:watch:fast": "tsc -p -w", "ext:test": "nyc mocha --config ../../configs/mocharc.yml \"./lib/**/*.*spec.js\"", "ext:test:watch": "mocha -w --config ../../configs/mocharc.yml \"./lib/**/*.*spec.js\"", "ext:test:clean": "rimraf .nyc_output coverage"}}
{"name": "@theia/toolbar", "version": "1.63.0", "description": "Theia - Toolbar", "keywords": ["theia-extension"], "homepage": "https://github.com/eclipse-theia/theia", "license": "EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0", "repository": {"type": "git", "url": "https://github.com/eclipse-theia/theia.git"}, "bugs": {"url": "https://github.com/eclipse-theia/theia/issues"}, "files": ["lib", "src"], "scripts": {"build": "theiaext build", "clean": "theiaext clean", "compile": "theiaext compile", "lint": "theiaext lint", "test": "theiaext test", "watch": "theiaext watch"}, "dependencies": {"@theia/core": "1.63.0", "@theia/editor": "1.63.0", "@theia/file-search": "1.63.0", "@theia/filesystem": "1.63.0", "@theia/monaco": "1.63.0", "@theia/monaco-editor-core": "1.96.302", "@theia/search-in-workspace": "1.63.0", "@theia/userstorage": "1.63.0", "@theia/workspace": "1.63.0", "ajv": "^6.5.3", "jsonc-parser": "^2.2.0", "perfect-scrollbar": "1.5.5", "tslib": "^2.6.2"}, "theiaExtensions": [{"frontend": "lib/browser/toolbar-frontend-module"}], "publishConfig": {"access": "public"}, "gitHead": "21358137e41342742707f660b8e222f940a27652"}
import React from 'react';
import { Card } from './Card';

export const MainMetricChart: React.FC = () => {
    const barsData = [50, 75, 60, 90, 40, 80, 65, 55];
    const radius = 60;
    const circumference = 2 * Math.PI * radius;
    const progress = 0.8; 
    const strokeDashoffset = circumference * (1 - progress);

  return (
    <Card className="items-center justify-center">
        <div className="relative w-32 h-32 sm:w-40 sm:h-40 lg:w-48 lg:h-48">
            <svg className="w-full h-full" viewBox="0 0 150 150">
                <defs>
                    <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#00FFFF" />
                        <stop offset="100%" stopColor="#FF007F" />
                    </linearGradient>
                </defs>
                {/* Background Ring */}
                <circle cx="75" cy="75" r={radius} fill="none" stroke="rgba(255,255,255,0.1)" strokeWidth="8"/>
                {/* Progress Ring */}
                <circle
                    cx="75"
                    cy="75"
                    r={radius}
                    fill="none"
                    stroke="url(#progressGradient)"
                    strokeWidth="8"
                    strokeDasharray={circumference}
                    strokeDashoffset={strokeDashoffset}
                    strokeLinecap="round"
                    transform="rotate(-90 75 75)"
                />
                {/* Inner Bars */}
                <g>
                    {barsData.map((height, index) => {
                        const barWidth = 8;
                        const x = 35 + index * (barWidth + 4);
                        const y = 75 + (50 - height) / 2;
                        return (
                            <rect key={index} x={x} y={y} width={barWidth} height={height * 0.8} fill="#00FFFF" rx="2" ry="2" opacity="0.7"/>
                        )
                    })}
                </g>
            </svg>
            <div className="absolute inset-0 flex flex-col items-center justify-center text-white">
               {/* Could add a central value here if needed */}
            </div>
        </div>
      <div className="flex justify-around w-full mt-2 sm:mt-4 text-[10px] sm:text-xs text-gray-400">
        <span>Phase A</span>
        <span>Phase B</span>
        <span>Phase C</span>
      </div>
    </Card>
  );
};
/********************************************************************************
 * Copyright (C) 2018 TypeFox and others.
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License v. 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * This Source Code may also be made available under the following Secondary
 * Licenses when the conditions for such availability set forth in the Eclipse
 * Public License v. 2.0 are satisfied: GNU General Public License, version 2
 * with the GNU Classpath Exception which is available at
 * https://www.gnu.org/software/classpath/license.html.
 *
 * SPDX-License-Identifier: EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0
 ********************************************************************************/

.theia-scm-commit-container {
  display: flex;
  flex-direction: column;
  border-top: 1px solid var(--theia-sideBarSectionHeader-border);
  width: 100%;
  padding-top: 6px;
}

.theia-scm-amend-outer-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: auto;
}

.theia-scm-commit-and-button {
  display: flex;
  white-space: nowrap;
  overflow: hidden;
  width: 100%;
}

.theia-scm-commit-avatar-and-text {
  display: flex;
  white-space: nowrap;
  overflow: hidden;
  width: 100%;
  padding-top: 2px;
}

.theia-scm-commit-avatar-and-text img {
  width: 27px;
}

.theia-scm-commit-details {
  display: flex;
  flex-direction: column;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.theia-scm-commit-message-avatar {
  margin-right: 5px;
}

.theia-scm-commit-message-summary {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.theia-scm-commit-message-time {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--theia-descriptionForeground);
  font-size: smaller;
}

.theia-scm-flex-container-center {
  display: flex;
  align-items: center;
}

.theia-scm-scrolling-container {
  position: relative;
  width: 100%;
  overflow: hidden;

  margin-top: 0;
  margin-bottom: 0;
  padding-top: 0;
  padding-bottom: 0;
  border-top: 0;
  border-bottom: 0;
}

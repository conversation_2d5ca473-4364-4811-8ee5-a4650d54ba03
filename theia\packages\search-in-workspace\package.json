{"name": "@theia/search-in-workspace", "version": "1.63.0", "description": "Theia - Search in workspace", "dependencies": {"@theia/core": "1.63.0", "@theia/editor": "1.63.0", "@theia/filesystem": "1.63.0", "@theia/navigator": "1.63.0", "@theia/process": "1.63.0", "@theia/workspace": "1.63.0", "@vscode/ripgrep": "^1.14.2", "minimatch": "^5.1.0", "react-textarea-autosize": "^8.5.5", "tslib": "^2.6.2"}, "publishConfig": {"access": "public"}, "theiaExtensions": [{"frontend": "lib/browser/search-in-workspace-frontend-module", "backend": "lib/node/search-in-workspace-backend-module"}], "keywords": ["theia-extension"], "license": "EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0", "repository": {"type": "git", "url": "https://github.com/eclipse-theia/theia.git"}, "bugs": {"url": "https://github.com/eclipse-theia/theia/issues"}, "homepage": "https://github.com/eclipse-theia/theia", "files": ["lib", "src"], "scripts": {"build": "theiaext build", "clean": "theiaext clean", "compile": "theiaext compile", "lint": "theiaext lint", "test": "theiaext test", "watch": "theiaext watch"}, "devDependencies": {"@theia/ext-scripts": "1.63.0"}, "gitHead": "21358137e41342742707f660b8e222f940a27652"}
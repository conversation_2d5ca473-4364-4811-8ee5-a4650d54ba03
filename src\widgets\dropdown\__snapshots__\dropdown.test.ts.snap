// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Dropdown widget should snapshot when opened: dropdown open 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        The total number of boxes the forklift can carry is 
        <div
          class="perseus-widget-container widget-nohighlight widget-inline-block"
        >
          <div
            class="default_xu2jcg"
          >
            <div
              class="default_xu2jcg-o_O-menuWrapper_wvrnr4 perseus-dropdown"
            >
              <button
                aria-controls=":r4:"
                aria-disabled="false"
                aria-expanded="true"
                aria-haspopup="listbox"
                aria-invalid="false"
                aria-label="Select an answer"
                class="button_vr44p2-o_O-shared_jbebuh-o_O-default_301vh2"
                id=":r3:"
                role="combobox"
                type="button"
              >
                <span
                  class="text_f1191h-o_O-BodyText_1xtvlx3-o_O-BodyTextMediumMediumWeight_ijclyr-o_O-text_awxjrp"
                >
                  <div
                    class="perseus-renderer perseus-renderer-responsive"
                  >
                    <div
                      class="paragraph"
                      data-perseus-paragraph-index="0"
                    >
                      <div
                        class="paragraph"
                      >
                        greater/less than or equal to
                      </div>
                    </div>
                  </div>
                </span>
                <span
                  aria-hidden="true"
                  class="svg_1q6jc65-o_O-icon_1t6z7z6-o_O-caret_ntkmq-o_O-inlineStyles_u7ovp6"
                />
              </button>
            </div>
          </div>
        </div>
         
        <span
          style="white-space: nowrap;"
        >
          <span />
          <span
            class="mock-TeX"
          >
            60
          </span>
          <span />
        </span>
        .
      </div>
    </div>
  </div>
</div>
`;

exports[`Dropdown widget should snapshot: initial render 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        The total number of boxes the forklift can carry is 
        <div
          class="perseus-widget-container widget-nohighlight widget-inline-block"
        >
          <div
            class="default_xu2jcg"
          >
            <div
              class="default_xu2jcg-o_O-menuWrapper_wvrnr4 perseus-dropdown"
            >
              <button
                aria-controls=":r1:"
                aria-disabled="false"
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-invalid="false"
                aria-label="Select an answer"
                class="button_vr44p2-o_O-shared_jbebuh-o_O-default_301vh2"
                id=":r0:"
                role="combobox"
                type="button"
              >
                <span
                  class="text_f1191h-o_O-BodyText_1xtvlx3-o_O-BodyTextMediumMediumWeight_ijclyr-o_O-text_awxjrp"
                >
                  <div
                    class="perseus-renderer perseus-renderer-responsive"
                  >
                    <div
                      class="paragraph"
                      data-perseus-paragraph-index="0"
                    >
                      <div
                        class="paragraph"
                      >
                        greater/less than or equal to
                      </div>
                    </div>
                  </div>
                </span>
                <span
                  aria-hidden="true"
                  class="svg_1q6jc65-o_O-icon_1t6z7z6-o_O-caret_ntkmq-o_O-inlineStyles_u7ovp6"
                />
              </button>
            </div>
          </div>
        </div>
         
        <span
          style="white-space: nowrap;"
        >
          <span />
          <span
            class="mock-TeX"
          >
            60
          </span>
          <span />
        </span>
        .
      </div>
    </div>
  </div>
</div>
`;

#!/usr/bin/env bash

# Runs all tests (<PERSON><PERSON> and <PERSON><PERSON>) with coverage enabled and merge all results
# into a single unified coverage report.
#
# Note that <PERSON><PERSON> and Cy<PERSON> have different configurations and that we have to
# override their default path for where they dump coverage info initially.
#   * Jest: config/test/test.config.js (the "coverageDirectory" key)
#   * Cypress: package.json (the "nyc" key)

# https://www.gnu.org/software/bash/manual/bash.html#The-Set-Builtin
set -e # Exit immediately if a command exits with a non-zero status.
set -o pipefail
set -u # Treat unset variables as an error when substituting.

# Identifies the path that the script is in (http://stackoverflow.com/a/246128/11807)
MYPATH=$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )
REPO_ROO="$MYPATH/.."

OPEN_REPORT=NO
while [[ $# -gt 0 ]]; do
  case $1 in
    -o|--open)
      OPEN_REPORT=YES
      shift # past argument
      ;;
    -*)
      echo "Unknown option $1"
      exit 1
      ;;
  esac
done

clean() {
    rm -rf .nyc_output/
    rm -rf coverage/
    rm -rf reports/
}

run_jest() {
    pnpm cross-env NODE_OPTIONS=--max_old_space_size=8192 jest --coverage
}

run_cypress() {
    pnpm cross-env cypress run --component --env CYPRESS_COVERAGE=1
}

merge_reports() {
    rm -rf coverage/reports
    mkdir coverage/reports
    cp coverage/jest/coverage-final.json coverage/reports/from-jest.json
    cp coverage/cypress/coverage-final.json coverage/reports/from-cypress.json

    rm -rf .nyc_output
    mkdir .nyc_output
    pnpm nyc merge coverage/reports/ .nyc_output/out.json

    pnpm nyc report --reporter lcov --reporter text-summary --report-dir coverage/final
}

pushd "$REPO_ROO" >/dev/null 2>&1

clean
run_jest
run_cypress
merge_reports

if [[ $OPEN_REPORT == YES ]]; then
    open coverage/final/lcov-report/index.html
fi

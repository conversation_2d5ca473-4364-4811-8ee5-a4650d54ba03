/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/_app"],{

/***/ "./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[1]!./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[2]!./styles/globals.css":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[1]!./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[2]!./styles/globals.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_pnpm_next_14_2_3_react_dom_18_2_0_react_18_2_0_react_18_2_0_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_pnpm_next_14_2_3_react_dom_18_2_0_react_18_2_0_react_18_2_0_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_pnpm_next_14_2_3_react_dom_18_2_0_react_18_2_0_react_18_2_0_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_pnpm_next_14_2_3_react_dom_18_2_0_react_18_2_0_react_18_2_0_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');\\n\\n*, ::before, ::after{\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n::backdrop{\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*/\\n\\n/*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\n\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\"; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\n\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\n.container{\\n  width: 100%;\\n}\\n@media (min-width: 640px){\\n\\n  .container{\\n    max-width: 640px;\\n  }\\n}\\n@media (min-width: 768px){\\n\\n  .container{\\n    max-width: 768px;\\n  }\\n}\\n@media (min-width: 1024px){\\n\\n  .container{\\n    max-width: 1024px;\\n  }\\n}\\n@media (min-width: 1280px){\\n\\n  .container{\\n    max-width: 1280px;\\n  }\\n}\\n@media (min-width: 1536px){\\n\\n  .container{\\n    max-width: 1536px;\\n  }\\n}\\n.fixed{\\n  position: fixed;\\n}\\n.absolute{\\n  position: absolute;\\n}\\n.relative{\\n  position: relative;\\n}\\n.inset-0{\\n  inset: 0px;\\n}\\n.-bottom-1{\\n  bottom: -0.25rem;\\n}\\n.-left-1\\\\/2{\\n  left: -50%;\\n}\\n.-right-1{\\n  right: -0.25rem;\\n}\\n.-top-1{\\n  top: -0.25rem;\\n}\\n.-top-1\\\\/2{\\n  top: -50%;\\n}\\n.left-0{\\n  left: 0px;\\n}\\n.left-1\\\\/2{\\n  left: 50%;\\n}\\n.top-1\\\\/2{\\n  top: 50%;\\n}\\n.z-10{\\n  z-index: 10;\\n}\\n.z-50{\\n  z-index: 50;\\n}\\n.col-span-2{\\n  grid-column: span 2 / span 2;\\n}\\n.mx-auto{\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n.\\\\!mt-6{\\n  margin-top: 1.5rem !important;\\n}\\n.mb-1{\\n  margin-bottom: 0.25rem;\\n}\\n.mb-2{\\n  margin-bottom: 0.5rem;\\n}\\n.mb-3{\\n  margin-bottom: 0.75rem;\\n}\\n.mb-4{\\n  margin-bottom: 1rem;\\n}\\n.mb-6{\\n  margin-bottom: 1.5rem;\\n}\\n.mb-8{\\n  margin-bottom: 2rem;\\n}\\n.mr-2{\\n  margin-right: 0.5rem;\\n}\\n.mt-1{\\n  margin-top: 0.25rem;\\n}\\n.mt-2{\\n  margin-top: 0.5rem;\\n}\\n.mt-3{\\n  margin-top: 0.75rem;\\n}\\n.mt-4{\\n  margin-top: 1rem;\\n}\\n.inline-block{\\n  display: inline-block;\\n}\\n.flex{\\n  display: flex;\\n}\\n.table{\\n  display: table;\\n}\\n.grid{\\n  display: grid;\\n}\\n.hidden{\\n  display: none;\\n}\\n.h-0\\\\.5{\\n  height: 0.125rem;\\n}\\n.h-1{\\n  height: 0.25rem;\\n}\\n.h-10{\\n  height: 2.5rem;\\n}\\n.h-12{\\n  height: 3rem;\\n}\\n.h-14{\\n  height: 3.5rem;\\n}\\n.h-16{\\n  height: 4rem;\\n}\\n.h-2{\\n  height: 0.5rem;\\n}\\n.h-24{\\n  height: 6rem;\\n}\\n.h-3{\\n  height: 0.75rem;\\n}\\n.h-32{\\n  height: 8rem;\\n}\\n.h-4{\\n  height: 1rem;\\n}\\n.h-40{\\n  height: 10rem;\\n}\\n.h-48{\\n  height: 12rem;\\n}\\n.h-5{\\n  height: 1.25rem;\\n}\\n.h-6{\\n  height: 1.5rem;\\n}\\n.h-64{\\n  height: 16rem;\\n}\\n.h-8{\\n  height: 2rem;\\n}\\n.h-\\\\[200\\\\%\\\\]{\\n  height: 200%;\\n}\\n.h-full{\\n  height: 100%;\\n}\\n.max-h-64{\\n  max-height: 16rem;\\n}\\n.min-h-0{\\n  min-height: 0px;\\n}\\n.w-1{\\n  width: 0.25rem;\\n}\\n.w-1\\\\/4{\\n  width: 25%;\\n}\\n.w-10{\\n  width: 2.5rem;\\n}\\n.w-12{\\n  width: 3rem;\\n}\\n.w-14{\\n  width: 3.5rem;\\n}\\n.w-16{\\n  width: 4rem;\\n}\\n.w-2{\\n  width: 0.5rem;\\n}\\n.w-24{\\n  width: 6rem;\\n}\\n.w-3{\\n  width: 0.75rem;\\n}\\n.w-32{\\n  width: 8rem;\\n}\\n.w-4{\\n  width: 1rem;\\n}\\n.w-5{\\n  width: 1.25rem;\\n}\\n.w-6{\\n  width: 1.5rem;\\n}\\n.w-8{\\n  width: 2rem;\\n}\\n.w-\\\\[200\\\\%\\\\]{\\n  width: 200%;\\n}\\n.w-full{\\n  width: 100%;\\n}\\n.max-w-md{\\n  max-width: 28rem;\\n}\\n.max-w-sm{\\n  max-width: 24rem;\\n}\\n.flex-1{\\n  flex: 1 1 0%;\\n}\\n.flex-shrink-0{\\n  flex-shrink: 0;\\n}\\n.flex-grow{\\n  flex-grow: 1;\\n}\\n.-translate-x-1\\\\/2{\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.-translate-y-1\\\\/2{\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.translate-x-0{\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.translate-x-5{\\n  --tw-translate-x: 1.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.scale-105{\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.scale-90{\\n  --tw-scale-x: .9;\\n  --tw-scale-y: .9;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.scale-95{\\n  --tw-scale-x: .95;\\n  --tw-scale-y: .95;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.transform{\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n@keyframes pulse{\\n\\n  50%{\\n    opacity: .5;\\n  }\\n}\\n.animate-pulse{\\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\n@keyframes spin{\\n\\n  to{\\n    transform: rotate(360deg);\\n  }\\n}\\n.animate-spin{\\n  animation: spin 1s linear infinite;\\n}\\n.cursor-pointer{\\n  cursor: pointer;\\n}\\n.resize{\\n  resize: both;\\n}\\n.grid-cols-1{\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\n.grid-cols-2{\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\n.grid-cols-3{\\n  grid-template-columns: repeat(3, minmax(0, 1fr));\\n}\\n.grid-cols-4{\\n  grid-template-columns: repeat(4, minmax(0, 1fr));\\n}\\n.flex-col{\\n  flex-direction: column;\\n}\\n.items-start{\\n  align-items: flex-start;\\n}\\n.items-end{\\n  align-items: flex-end;\\n}\\n.items-center{\\n  align-items: center;\\n}\\n.justify-end{\\n  justify-content: flex-end;\\n}\\n.justify-center{\\n  justify-content: center;\\n}\\n.justify-between{\\n  justify-content: space-between;\\n}\\n.justify-around{\\n  justify-content: space-around;\\n}\\n.gap-1{\\n  gap: 0.25rem;\\n}\\n.gap-2{\\n  gap: 0.5rem;\\n}\\n.gap-3{\\n  gap: 0.75rem;\\n}\\n.gap-4{\\n  gap: 1rem;\\n}\\n.gap-6{\\n  gap: 1.5rem;\\n}\\n.space-y-1 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\n.space-y-2 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-3 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\n.space-y-4 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\n.overflow-auto{\\n  overflow: auto;\\n}\\n.overflow-hidden{\\n  overflow: hidden;\\n}\\n.overflow-y-auto{\\n  overflow-y: auto;\\n}\\n.truncate{\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.rounded-full{\\n  border-radius: 9999px;\\n}\\n.rounded-lg{\\n  border-radius: 0.5rem;\\n}\\n.rounded-md{\\n  border-radius: 0.375rem;\\n}\\n.rounded-sm{\\n  border-radius: 0.125rem;\\n}\\n.rounded-xl{\\n  border-radius: 0.75rem;\\n}\\n.rounded-r-full{\\n  border-top-right-radius: 9999px;\\n  border-bottom-right-radius: 9999px;\\n}\\n.border{\\n  border-width: 1px;\\n}\\n.border-2{\\n  border-width: 2px;\\n}\\n.border-b-2{\\n  border-bottom-width: 2px;\\n}\\n.border-t{\\n  border-top-width: 1px;\\n}\\n.border-blue-400\\\\/30{\\n  border-color: rgb(96 165 250 / 0.3);\\n}\\n.border-blue-500\\\\/30{\\n  border-color: rgb(59 130 246 / 0.3);\\n}\\n.border-brand-cyan{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(0 255 255 / var(--tw-border-opacity, 1));\\n}\\n.border-cyan-400{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(34 211 238 / var(--tw-border-opacity, 1));\\n}\\n.border-cyan-400\\\\/30{\\n  border-color: rgb(34 211 238 / 0.3);\\n}\\n.border-cyan-500\\\\/30{\\n  border-color: rgb(6 182 212 / 0.3);\\n}\\n.border-cyan-500\\\\/50{\\n  border-color: rgb(6 182 212 / 0.5);\\n}\\n.border-gray-400\\\\/30{\\n  border-color: rgb(156 163 175 / 0.3);\\n}\\n.border-gray-500\\\\/40{\\n  border-color: rgb(107 114 128 / 0.4);\\n}\\n.border-gray-600{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-600\\\\/30{\\n  border-color: rgb(75 85 99 / 0.3);\\n}\\n.border-gray-700{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-700\\\\/30{\\n  border-color: rgb(55 65 81 / 0.3);\\n}\\n.border-gray-700\\\\/50{\\n  border-color: rgb(55 65 81 / 0.5);\\n}\\n.border-green-400\\\\/30{\\n  border-color: rgb(74 222 128 / 0.3);\\n}\\n.border-green-500\\\\/30{\\n  border-color: rgb(34 197 94 / 0.3);\\n}\\n.border-pink-400\\\\/30{\\n  border-color: rgb(244 114 182 / 0.3);\\n}\\n.border-pink-500\\\\/90{\\n  border-color: rgb(236 72 153 / 0.9);\\n}\\n.border-purple-400\\\\/30{\\n  border-color: rgb(192 132 252 / 0.3);\\n}\\n.border-purple-400\\\\/40{\\n  border-color: rgb(192 132 252 / 0.4);\\n}\\n.border-purple-500\\\\/30{\\n  border-color: rgb(168 85 247 / 0.3);\\n}\\n.border-red-400\\\\/30{\\n  border-color: rgb(248 113 113 / 0.3);\\n}\\n.border-red-500\\\\/30{\\n  border-color: rgb(239 68 68 / 0.3);\\n}\\n.border-yellow-400\\\\/30{\\n  border-color: rgb(250 204 21 / 0.3);\\n}\\n.border-yellow-400\\\\/50{\\n  border-color: rgb(250 204 21 / 0.5);\\n}\\n.border-yellow-500\\\\/30{\\n  border-color: rgb(234 179 8 / 0.3);\\n}\\n.bg-black\\\\/60{\\n  background-color: rgb(0 0 0 / 0.6);\\n}\\n.bg-black\\\\/70{\\n  background-color: rgb(0 0 0 / 0.7);\\n}\\n.bg-blue-400{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-500\\\\/20{\\n  background-color: rgb(59 130 246 / 0.2);\\n}\\n.bg-brand-cyan{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-brand-cyan\\\\/20{\\n  background-color: rgb(0 255 255 / 0.2);\\n}\\n.bg-brand-pink\\\\/20{\\n  background-color: rgb(255 0 127 / 0.2);\\n}\\n.bg-container-bg{\\n  background-color: rgba(10, 20, 35, 0.7);\\n}\\n.bg-cyan-400{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 211 238 / var(--tw-bg-opacity, 1));\\n}\\n.bg-cyan-400\\\\/10{\\n  background-color: rgb(34 211 238 / 0.1);\\n}\\n.bg-cyan-400\\\\/20{\\n  background-color: rgb(34 211 238 / 0.2);\\n}\\n.bg-cyan-400\\\\/50{\\n  background-color: rgb(34 211 238 / 0.5);\\n}\\n.bg-cyan-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(6 182 212 / var(--tw-bg-opacity, 1));\\n}\\n.bg-cyan-500\\\\/20{\\n  background-color: rgb(6 182 212 / 0.2);\\n}\\n.bg-cyan-500\\\\/30{\\n  background-color: rgb(6 182 212 / 0.3);\\n}\\n.bg-cyan-500\\\\/50{\\n  background-color: rgb(6 182 212 / 0.5);\\n}\\n.bg-emerald-500\\\\/20{\\n  background-color: rgb(16 185 129 / 0.2);\\n}\\n.bg-gray-200\\\\/20{\\n  background-color: rgb(229 231 235 / 0.2);\\n}\\n.bg-gray-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-500\\\\/20{\\n  background-color: rgb(107 114 128 / 0.2);\\n}\\n.bg-gray-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-700{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-700\\\\/30{\\n  background-color: rgb(55 65 81 / 0.3);\\n}\\n.bg-gray-700\\\\/50{\\n  background-color: rgb(55 65 81 / 0.5);\\n}\\n.bg-gray-700\\\\/60{\\n  background-color: rgb(55 65 81 / 0.6);\\n}\\n.bg-gray-700\\\\/80{\\n  background-color: rgb(55 65 81 / 0.8);\\n}\\n.bg-gray-800\\\\/40{\\n  background-color: rgb(31 41 55 / 0.4);\\n}\\n.bg-green-400{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-500\\\\/20{\\n  background-color: rgb(34 197 94 / 0.2);\\n}\\n.bg-panel-bg{\\n  background-color: rgba(25, 40, 60, 0.6);\\n}\\n.bg-pink-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(236 72 153 / var(--tw-bg-opacity, 1));\\n}\\n.bg-pink-500\\\\/20{\\n  background-color: rgb(236 72 153 / 0.2);\\n}\\n.bg-pink-500\\\\/80{\\n  background-color: rgb(236 72 153 / 0.8);\\n}\\n.bg-purple-500\\\\/20{\\n  background-color: rgb(168 85 247 / 0.2);\\n}\\n.bg-red-500\\\\/20{\\n  background-color: rgb(239 68 68 / 0.2);\\n}\\n.bg-white{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-white\\\\/20{\\n  background-color: rgb(255 255 255 / 0.2);\\n}\\n.bg-yellow-400{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-500\\\\/20{\\n  background-color: rgb(234 179 8 / 0.2);\\n}\\n.bg-\\\\[radial-gradient\\\\(circle_at_center\\\\2c _rgba\\\\(255\\\\2c 0\\\\2c 127\\\\2c 0\\\\.5\\\\)\\\\2c _transparent_40\\\\%\\\\)\\\\]{\\n  background-image: radial-gradient(circle at center, rgba(255,0,127,0.5), transparent 40%);\\n}\\n.bg-gradient-to-br{\\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\\n}\\n.bg-gradient-to-r{\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n}\\n.from-\\\\[\\\\#FF007F\\\\]{\\n  --tw-gradient-from: #FF007F var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(255 0 127 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-400\\\\/20{\\n  --tw-gradient-from: rgb(96 165 250 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-500{\\n  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-900\\\\/20{\\n  --tw-gradient-from: rgb(30 58 138 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-cyan-400{\\n  --tw-gradient-from: #22d3ee var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 211 238 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-cyan-400\\\\/20{\\n  --tw-gradient-from: rgb(34 211 238 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 211 238 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-cyan-500{\\n  --tw-gradient-from: #06b6d4 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(6 182 212 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-cyan-900\\\\/20{\\n  --tw-gradient-from: rgb(22 78 99 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(22 78 99 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-gray-800\\\\/40{\\n  --tw-gradient-from: rgb(31 41 55 / 0.4) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-gray-800\\\\/60{\\n  --tw-gradient-from: rgb(31 41 55 / 0.6) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-gray-900\\\\/40{\\n  --tw-gradient-from: rgb(17 24 39 / 0.4) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-green-400\\\\/20{\\n  --tw-gradient-from: rgb(74 222 128 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(74 222 128 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-green-500{\\n  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-green-900\\\\/20{\\n  --tw-gradient-from: rgb(20 83 45 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(20 83 45 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-purple-400\\\\/20{\\n  --tw-gradient-from: rgb(192 132 252 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(192 132 252 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-purple-500{\\n  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-purple-900\\\\/20{\\n  --tw-gradient-from: rgb(88 28 135 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-purple-900\\\\/30{\\n  --tw-gradient-from: rgb(88 28 135 / 0.3) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-red-500{\\n  --tw-gradient-from: #ef4444 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-red-900\\\\/20{\\n  --tw-gradient-from: rgb(127 29 29 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(127 29 29 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-white{\\n  --tw-gradient-from: #fff var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-yellow-400\\\\/20{\\n  --tw-gradient-from: rgb(250 204 21 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(250 204 21 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-yellow-500{\\n  --tw-gradient-from: #eab308 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(234 179 8 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-yellow-900\\\\/20{\\n  --tw-gradient-from: rgb(113 63 18 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(113 63 18 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.to-\\\\[\\\\#d6006a\\\\]{\\n  --tw-gradient-to: #d6006a var(--tw-gradient-to-position);\\n}\\n.to-blue-500{\\n  --tw-gradient-to: #3b82f6 var(--tw-gradient-to-position);\\n}\\n.to-blue-500\\\\/20{\\n  --tw-gradient-to: rgb(59 130 246 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-blue-600{\\n  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);\\n}\\n.to-blue-600\\\\/20{\\n  --tw-gradient-to: rgb(37 99 235 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-blue-800\\\\/20{\\n  --tw-gradient-to: rgb(30 64 175 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-blue-900\\\\/20{\\n  --tw-gradient-to: rgb(30 58 138 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-blue-900\\\\/30{\\n  --tw-gradient-to: rgb(30 58 138 / 0.3) var(--tw-gradient-to-position);\\n}\\n.to-cyan-600{\\n  --tw-gradient-to: #0891b2 var(--tw-gradient-to-position);\\n}\\n.to-cyan-800\\\\/20{\\n  --tw-gradient-to: rgb(21 94 117 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-cyan-900\\\\/20{\\n  --tw-gradient-to: rgb(22 78 99 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-emerald-600{\\n  --tw-gradient-to: #059669 var(--tw-gradient-to-position);\\n}\\n.to-emerald-900\\\\/20{\\n  --tw-gradient-to: rgb(6 78 59 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-fuchsia-500{\\n  --tw-gradient-to: #d946ef var(--tw-gradient-to-position);\\n}\\n.to-gray-700\\\\/40{\\n  --tw-gradient-to: rgb(55 65 81 / 0.4) var(--tw-gradient-to-position);\\n}\\n.to-gray-700\\\\/60{\\n  --tw-gradient-to: rgb(55 65 81 / 0.6) var(--tw-gradient-to-position);\\n}\\n.to-gray-800\\\\/40{\\n  --tw-gradient-to: rgb(31 41 55 / 0.4) var(--tw-gradient-to-position);\\n}\\n.to-gray-900\\\\/60{\\n  --tw-gradient-to: rgb(17 24 39 / 0.6) var(--tw-gradient-to-position);\\n}\\n.to-green-600\\\\/20{\\n  --tw-gradient-to: rgb(22 163 74 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-green-800\\\\/20{\\n  --tw-gradient-to: rgb(22 101 52 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-orange-600{\\n  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);\\n}\\n.to-orange-900\\\\/20{\\n  --tw-gradient-to: rgb(124 45 18 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-pink-600{\\n  --tw-gradient-to: #db2777 var(--tw-gradient-to-position);\\n}\\n.to-pink-900\\\\/20{\\n  --tw-gradient-to: rgb(131 24 67 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-purple-600{\\n  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);\\n}\\n.to-purple-600\\\\/20{\\n  --tw-gradient-to: rgb(147 51 234 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-purple-800\\\\/20{\\n  --tw-gradient-to: rgb(107 33 168 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-purple-900\\\\/20{\\n  --tw-gradient-to: rgb(88 28 135 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-transparent{\\n  --tw-gradient-to: transparent var(--tw-gradient-to-position);\\n}\\n.to-yellow-600\\\\/20{\\n  --tw-gradient-to: rgb(202 138 4 / 0.2) var(--tw-gradient-to-position);\\n}\\n.object-cover{\\n  -o-object-fit: cover;\\n     object-fit: cover;\\n}\\n.p-0{\\n  padding: 0px;\\n}\\n.p-1{\\n  padding: 0.25rem;\\n}\\n.p-2{\\n  padding: 0.5rem;\\n}\\n.p-3{\\n  padding: 0.75rem;\\n}\\n.p-4{\\n  padding: 1rem;\\n}\\n.p-6{\\n  padding: 1.5rem;\\n}\\n.px-2{\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\n.px-3{\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\n.px-4{\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n.py-1{\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\n.py-1\\\\.5{\\n  padding-top: 0.375rem;\\n  padding-bottom: 0.375rem;\\n}\\n.py-2{\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\n.pb-1{\\n  padding-bottom: 0.25rem;\\n}\\n.pt-2{\\n  padding-top: 0.5rem;\\n}\\n.pt-4{\\n  padding-top: 1rem;\\n}\\n.text-left{\\n  text-align: left;\\n}\\n.text-center{\\n  text-align: center;\\n}\\n.text-right{\\n  text-align: right;\\n}\\n.font-poppins{\\n  font-family: Poppins, sans-serif;\\n}\\n.text-2xl{\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\n.text-3xl{\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\n.text-\\\\[10px\\\\]{\\n  font-size: 10px;\\n}\\n.text-base{\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\n.text-lg{\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\n.text-sm{\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n.text-xl{\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n.text-xs{\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\n.font-bold{\\n  font-weight: 700;\\n}\\n.font-medium{\\n  font-weight: 500;\\n}\\n.font-normal{\\n  font-weight: 400;\\n}\\n.font-semibold{\\n  font-weight: 600;\\n}\\n.uppercase{\\n  text-transform: uppercase;\\n}\\n.capitalize{\\n  text-transform: capitalize;\\n}\\n.leading-relaxed{\\n  line-height: 1.625;\\n}\\n.tracking-wider{\\n  letter-spacing: 0.05em;\\n}\\n.text-\\\\[\\\\#A0A0B0\\\\]{\\n  --tw-text-opacity: 1;\\n  color: rgb(160 160 176 / var(--tw-text-opacity, 1));\\n}\\n.text-\\\\[\\\\#E0E0E0\\\\]{\\n  --tw-text-opacity: 1;\\n  color: rgb(224 224 224 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\\n}\\n.text-brand-cyan{\\n  --tw-text-opacity: 1;\\n  color: rgb(0 255 255 / var(--tw-text-opacity, 1));\\n}\\n.text-cyan-300{\\n  --tw-text-opacity: 1;\\n  color: rgb(103 232 249 / var(--tw-text-opacity, 1));\\n}\\n.text-cyan-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(34 211 238 / var(--tw-text-opacity, 1));\\n}\\n.text-emerald-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(52 211 153 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-300{\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-500{\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-900{\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\n.text-green-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\\n}\\n.text-indigo-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(129 140 248 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(251 146 60 / var(--tw-text-opacity, 1));\\n}\\n.text-pink-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(244 114 182 / var(--tw-text-opacity, 1));\\n}\\n.text-purple-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(192 132 252 / var(--tw-text-opacity, 1));\\n}\\n.text-red-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\\n}\\n.text-teal-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(45 212 191 / var(--tw-text-opacity, 1));\\n}\\n.text-white{\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\\n}\\n.opacity-0{\\n  opacity: 0;\\n}\\n.opacity-30{\\n  opacity: 0.3;\\n}\\n.opacity-50{\\n  opacity: 0.5;\\n}\\n.opacity-90{\\n  opacity: 0.9;\\n}\\n.shadow-inner{\\n  --tw-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: inset 0 2px 4px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-lg{\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-md{\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.blur-sm{\\n  --tw-blur: blur(4px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.drop-shadow{\\n  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.filter{\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.backdrop-blur-sm{\\n  --tw-backdrop-blur: blur(4px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.backdrop-blur-xl{\\n  --tw-backdrop-blur: blur(24px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.backdrop-filter{\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.transition{\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-all{\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-colors{\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-opacity{\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-transform{\\n  transition-property: transform;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.duration-200{\\n  transition-duration: 200ms;\\n}\\n.duration-300{\\n  transition-duration: 300ms;\\n}\\n.duration-500{\\n  transition-duration: 500ms;\\n}\\n.ease-in-out{\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.ease-out{\\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n}\\n\\n/* CSS Custom Properties for Responsive Scaling - Optimized for Content Fit */\\n:root {\\n  --scale-factor: 1;\\n  --content-scale: 3;\\n  --base-font-size: calc(14px * var(--content-scale));\\n  --base-spacing: calc(6px * var(--content-scale));\\n  --base-border-radius: calc(8px * var(--content-scale));\\n  --base-icon-size: calc(20px * var(--content-scale));\\n  --base-button-height: calc(36px * var(--content-scale));\\n  --base-card-padding: calc(1px * var(--content-scale));\\n  --base-gap: calc(6px * var(--content-scale));\\n  --header-height: calc(60px * var(--content-scale));\\n  --footer-height: calc(50px * var(--content-scale));\\n  --sidebar-width: calc(80px * var(--content-scale));\\n\\n  /* HUD/HDR Color Palette */\\n  --hud-bg-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  --hud-bg-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\\n  --hud-bg-panel: rgba(255, 255, 255, 0.12);\\n  --hud-bg-card: rgba(255, 255, 255, 0.18);\\n  --hud-border: rgba(255, 255, 255, 0.25);\\n  --hud-border-glow: rgba(255, 255, 255, 0.4);\\n\\n  /* HUD Accent Colors */\\n  --hud-neon-blue: #4299e1;\\n  --hud-aqua-green: #48bb78;\\n  --hud-energy-orange: #ed8936;\\n  --hud-electric-purple: #9f7aea;\\n  --hud-deep-red: #f56565;\\n  --hud-bright-white: #2d3748;\\n  --hud-silver: #4a5568;\\n\\n  /* Text Colors for Glassmorphism */\\n  --hud-text-primary: #2d3748;\\n  --hud-text-secondary: #4a5568;\\n  --hud-text-light: #718096;\\n\\n  /* Glassmorphism Effects */\\n  --glass-bg: rgba(255, 255, 255, 0.15);\\n  --glass-border: rgba(255, 255, 255, 0.2);\\n  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05);\\n}\\n\\n/* Responsive scaling variables - now handled by --content-scale */\\n@media (max-width: 1920px) and (min-width: 1600px) {\\n  :root {\\n    --scale-factor: 0.9;\\n    --content-scale: 0.9;\\n  }\\n}\\n\\n@media (max-width: 1599px) and (min-width: 1400px) {\\n  :root {\\n    --scale-factor: 0.8;\\n    --content-scale: 0.8;\\n  }\\n}\\n\\n@media (max-width: 1399px) and (min-width: 1200px) {\\n  :root {\\n    --scale-factor: 0.7;\\n    --content-scale: 0.7;\\n  }\\n}\\n\\n@media (max-width: 1199px) and (min-width: 1000px) {\\n  :root {\\n    --scale-factor: 0.6;\\n    --content-scale: 0.6;\\n  }\\n}\\n\\n@media (max-width: 999px) {\\n  :root {\\n    --scale-factor: 0.5;\\n    --content-scale: 0.5;\\n  }\\n}\\n\\n/* Ensure base styles are applied */\\n* {\\n  box-sizing: border-box;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nhtml, body {\\n  height: 100vh;\\n  width: 100vw;\\n  overflow: hidden;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nbody {\\n  font-family: 'Poppins', sans-serif;\\n  font-size: var(--base-font-size);\\n  line-height: 1.5;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);\\n  background-size: 400% 400%;\\n  animation: gradientShift 15s ease infinite;\\n  color: var(--hud-text-primary);\\n}\\n\\n#__next {\\n  height: 100vh;\\n  width: 100vw;\\n  overflow: hidden;\\n}\\n\\n.main-background {\\n  background: linear-gradient(135deg, var(--hud-bg-primary) 0%, var(--hud-bg-secondary) 50%, #05080e 100%);\\n  background-attachment: fixed;\\n  position: relative;\\n}\\n\\n.main-background::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(ellipse at 20% 30%, rgba(209, 160, 255, 0.1) 0%, transparent 50%),\\n              radial-gradient(ellipse at 80% 70%, rgba(188, 19, 254, 0.08) 0%, transparent 50%),\\n              radial-gradient(ellipse at 50% 50%, rgba(41, 52, 95, 0.05) 0%, transparent 70%);\\n  pointer-events: none;\\n}\\n\\n/* Global Responsive Classes */\\n.responsive-text {\\n  font-size: var(--base-font-size);\\n}\\n\\n.responsive-text-sm {\\n  font-size: calc(var(--base-font-size) * 0.875);\\n}\\n\\n.responsive-text-xs {\\n  font-size: calc(var(--base-font-size) * 0.75);\\n}\\n\\n.responsive-text-lg {\\n  font-size: calc(var(--base-font-size) * 1.125);\\n}\\n\\n.responsive-text-xl {\\n  font-size: calc(var(--base-font-size) * 1.25);\\n}\\n\\n.responsive-text-2xl {\\n  font-size: calc(var(--base-font-size) * 1.5);\\n}\\n\\n.responsive-spacing {\\n  padding: var(--base-spacing);\\n}\\n\\n.responsive-spacing-sm {\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.responsive-spacing-lg {\\n  padding: calc(var(--base-spacing) * 1.5);\\n}\\n\\n.responsive-gap {\\n  gap: var(--base-gap);\\n}\\n\\n.responsive-gap-sm {\\n  gap: calc(var(--base-gap) * 0.5);\\n}\\n\\n.responsive-gap-lg {\\n  gap: calc(var(--base-gap) * 1.5);\\n}\\n\\n.responsive-border-radius {\\n  border-radius: var(--base-border-radius);\\n}\\n\\n.responsive-icon {\\n  width: var(--base-icon-size);\\n  height: var(--base-icon-size);\\n}\\n\\n.responsive-button {\\n  height: var(--base-button-height);\\n  padding: 0 var(--base-spacing);\\n  font-size: var(--base-font-size);\\n  border-radius: var(--base-border-radius);\\n}\\n\\n.responsive-card {\\n  padding: var(--base-card-padding);\\n  border-radius: calc(var(--base-border-radius) * 1.5);\\n}\\n\\n/* Enhanced HUD Card System */\\n.hud-card {\\n  background: var(--hud-bg-card);\\n  border: 1px solid var(--hud-border);\\n  border-radius: calc(var(--base-border-radius) * 1.5);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  box-shadow: var(--glass-shadow);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.hud-card::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(90deg, transparent, var(--hud-neon-blue), transparent);\\n  opacity: 0.6;\\n}\\n\\n.hud-card:hover {\\n  border-color: var(--hud-border-glow);\\n  box-shadow: 0 8px 40px rgba(0, 207, 255, 0.15), var(--glass-shadow);\\n  transform: translateY(-2px);\\n}\\n\\n/* Compact HUD card padding */\\n.hud-card .ant-card-head {\\n  padding: calc(var(--base-spacing) * 0.75) calc(var(--base-spacing) * 1.5) !important;\\n  min-height: auto !important;\\n}\\n\\n.hud-card .ant-card-body {\\n  padding: calc(var(--base-spacing) * 1) !important;\\n}\\n\\n/* Compact metric cards */\\n.metric-card {\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  border-radius: var(--base-border-radius);\\n  padding: calc(var(--base-spacing) * 0.3);\\n  text-align: center;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n  min-height: 60px;\\n  max-height: 80px;\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  box-shadow: var(--glass-shadow);\\n}\\n\\n.metric-card::after {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(0, 207, 255, 0.05) 0%, rgba(0, 255, 189, 0.05) 100%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.metric-card:hover::after {\\n  opacity: 1;\\n}\\n\\n.metric-card:hover {\\n  border-color: var(--hud-neon-blue);\\n  box-shadow: 0 4px 20px rgba(0, 207, 255, 0.2);\\n  transform: scale(1.02);\\n}\\n\\n/* Compact metric card text */\\n.metric-card p {\\n  margin-bottom: calc(var(--base-spacing) * 0.1) !important;\\n  line-height: 1.1;\\n  font-size: calc(var(--base-font-size) * 0.7) !important;\\n  color: var(--hud-text-secondary) !important;\\n}\\n\\n.metric-card .text-xl {\\n  font-size: calc(var(--base-font-size) * 1.1) !important;\\n  line-height: 1.0;\\n  margin-bottom: calc(var(--base-spacing) * 0.1) !important;\\n  color: var(--hud-text-primary) !important;\\n  font-weight: 700 !important;\\n}\\n\\n/* Compact metric card icons */\\n.metric-card .w-10.h-10 {\\n  width: calc(var(--base-icon-size) * 1.5) !important;\\n  height: calc(var(--base-icon-size) * 1.5) !important;\\n  margin-bottom: calc(var(--base-spacing) * 0.5) !important;\\n}\\n\\n/* Beautiful gradient animation */\\n@keyframes gradientShift {\\n  0% { background-position: 0% 50%; }\\n  50% { background-position: 100% 50%; }\\n  100% { background-position: 0% 50%; }\\n}\\n\\n/* Status badges with glow effects */\\n.status-badge {\\n  padding: calc(var(--base-spacing) * 0.5) calc(var(--base-spacing) * 1);\\n  border-radius: calc(var(--base-border-radius) * 2);\\n  font-size: calc(10px * var(--content-scale));\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  border: 1px solid;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.status-badge.active {\\n  background: rgba(0, 255, 189, 0.2);\\n  color: var(--hud-aqua-green);\\n  border-color: var(--hud-aqua-green);\\n  box-shadow: 0 0 10px rgba(0, 255, 189, 0.3);\\n}\\n\\n.status-badge.beta {\\n  background: rgba(255, 165, 0, 0.2);\\n  color: var(--hud-energy-orange);\\n  border-color: var(--hud-energy-orange);\\n  box-shadow: 0 0 10px rgba(255, 165, 0, 0.3);\\n}\\n\\n.status-badge.live {\\n  background: rgba(0, 207, 255, 0.2);\\n  color: var(--hud-neon-blue);\\n  border-color: var(--hud-neon-blue);\\n  box-shadow: 0 0 10px rgba(0, 207, 255, 0.3);\\n}\\n\\n.status-badge.testing {\\n  background: rgba(188, 19, 254, 0.2);\\n  color: var(--hud-electric-purple);\\n  border-color: var(--hud-electric-purple);\\n  box-shadow: 0 0 10px rgba(188, 19, 254, 0.3);\\n}\\n\\n/* Enhanced Header Styling */\\n.header-hud {\\n  background: var(--hud-bg-panel);\\n  border: 1px solid var(--hud-border);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n  position: relative;\\n}\\n\\n.header-hud::before {\\n  content: '';\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(90deg, transparent, var(--hud-neon-blue), transparent);\\n  opacity: 0.4;\\n}\\n\\n/* Enhanced Department Buttons */\\n.dept-button {\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.dept-button:hover {\\n  background: rgba(0, 207, 255, 0.1);\\n  border-color: var(--hud-border-glow);\\n  box-shadow: 0 4px 20px rgba(0, 207, 255, 0.2);\\n  transform: scale(1.05);\\n}\\n\\n.dept-button.active {\\n  background: linear-gradient(135deg, rgba(0, 207, 255, 0.2) 0%, rgba(0, 255, 189, 0.1) 100%);\\n  border-color: var(--hud-neon-blue);\\n  box-shadow: 0 0 20px rgba(0, 207, 255, 0.3);\\n}\\n\\n.dept-button.active::after {\\n  content: '';\\n  position: absolute;\\n  inset: 0;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\\n  pointer-events: none;\\n}\\n\\n/* Dashboard Auto-Scale - Scales ALL content including text, icons, spacing */\\n.dashboard-auto-scale {\\n  transform-origin: top left;\\n  width: 100vw !important;\\n  height: 100vh !important;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  overflow: hidden;\\n  transition: transform 0.3s ease-out;\\n}\\n\\n/* Scale everything - content, text, icons, spacing */\\n@media (max-width: 1920px) and (min-width: 1600px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.9);\\n    width: calc(100vw / 0.9) !important;\\n    height: calc(100vh / 0.9) !important;\\n  }\\n  :root {\\n    --content-scale: 0.9;\\n  }\\n}\\n\\n@media (max-width: 1599px) and (min-width: 1400px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.8);\\n    width: calc(100vw / 0.8) !important;\\n    height: calc(100vh / 0.8) !important;\\n  }\\n  :root {\\n    --content-scale: 0.8;\\n  }\\n}\\n\\n@media (max-width: 1399px) and (min-width: 1200px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.7);\\n    width: calc(100vw / 0.7) !important;\\n    height: calc(100vh / 0.7) !important;\\n  }\\n  :root {\\n    --content-scale: 0.7;\\n  }\\n}\\n\\n@media (max-width: 1199px) and (min-width: 1000px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.6);\\n    width: calc(100vw / 0.6) !important;\\n    height: calc(100vh / 0.6) !important;\\n  }\\n  :root {\\n    --content-scale: 0.6;\\n  }\\n}\\n\\n@media (max-width: 999px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.5);\\n    width: calc(100vw / 0.5) !important;\\n    height: calc(100vh / 0.5) !important;\\n  }\\n  :root {\\n    --content-scale: 0.5;\\n  }\\n}\\n\\n/* Default content scale */\\n:root {\\n  --content-scale: 1;\\n}\\n\\n/* Universal content scaling - applies to ALL elements */\\n* {\\n  font-size: inherit;\\n}\\n\\n/* Scale all text elements in main content */\\n.main-section h1, .main-section h2, .main-section h3, .main-section h4, .main-section h5, .main-section h6,\\n.main-section p, .main-section span, .main-section div, .main-section button,\\n.main-section input, .main-section textarea, .main-section select, .main-section label, .main-section a {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale card content specifically */\\n.card-content * {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale chart text and numbers */\\n.recharts-text, .recharts-label, .chart-text, .metric-value, .metric-label {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale all icons and images */\\nsvg, img, .icon {\\n  width: calc(var(--base-icon-size));\\n  height: calc(var(--base-icon-size));\\n}\\n\\n/* Scale all spacing */\\n.p-1 { padding: calc(4px * var(--content-scale)) !important; }\\n.p-2 { padding: calc(8px * var(--content-scale)) !important; }\\n.p-3 { padding: calc(12px * var(--content-scale)) !important; }\\n.p-4 { padding: calc(16px * var(--content-scale)) !important; }\\n.p-5 { padding: calc(20px * var(--content-scale)) !important; }\\n.p-6 { padding: calc(24px * var(--content-scale)) !important; }\\n\\n.m-1 { margin: calc(4px * var(--content-scale)) !important; }\\n.m-2 { margin: calc(8px * var(--content-scale)) !important; }\\n.m-3 { margin: calc(12px * var(--content-scale)) !important; }\\n.m-4 { margin: calc(16px * var(--content-scale)) !important; }\\n.m-5 { margin: calc(20px * var(--content-scale)) !important; }\\n.m-6 { margin: calc(24px * var(--content-scale)) !important; }\\n\\n/* Scale gaps */\\n.gap-1 { gap: calc(4px * var(--content-scale)) !important; }\\n.gap-2 { gap: calc(8px * var(--content-scale)) !important; }\\n.gap-3 { gap: calc(12px * var(--content-scale)) !important; }\\n.gap-4 { gap: calc(16px * var(--content-scale)) !important; }\\n.gap-5 { gap: calc(20px * var(--content-scale)) !important; }\\n.gap-6 { gap: calc(24px * var(--content-scale)) !important; }\\n\\n/* Scale border radius */\\n.rounded { border-radius: calc(4px * var(--content-scale)) !important; }\\n.rounded-md { border-radius: calc(6px * var(--content-scale)) !important; }\\n.rounded-lg { border-radius: calc(8px * var(--content-scale)) !important; }\\n.rounded-xl { border-radius: calc(12px * var(--content-scale)) !important; }\\n.rounded-2xl { border-radius: calc(16px * var(--content-scale)) !important; }\\n\\n@keyframes fade-in-up {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n.animate-fade-in-up {\\n  animation: fade-in-up 0.3s ease-out forwards;\\n}\\n\\n@keyframes fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n.animate-fadeIn {\\n  animation: fadeIn 0.5s ease-in-out;\\n}\\n\\n/* Override Tailwind text sizes with content scaling */\\n.text-xs { font-size: calc(12px * var(--content-scale)) !important; }\\n.text-sm { font-size: calc(14px * var(--content-scale)) !important; }\\n.text-base { font-size: calc(16px * var(--content-scale)) !important; }\\n.text-lg { font-size: calc(18px * var(--content-scale)) !important; }\\n.text-xl { font-size: calc(20px * var(--content-scale)) !important; }\\n.text-2xl { font-size: calc(24px * var(--content-scale)) !important; }\\n.text-3xl { font-size: calc(30px * var(--content-scale)) !important; }\\n.text-4xl { font-size: calc(36px * var(--content-scale)) !important; }\\n\\n/* Override Tailwind width/height for icons */\\n.w-3 { width: calc(12px * var(--content-scale)) !important; }\\n.w-4 { width: calc(16px * var(--content-scale)) !important; }\\n.w-5 { width: calc(20px * var(--content-scale)) !important; }\\n.w-6 { width: calc(24px * var(--content-scale)) !important; }\\n.w-8 { width: calc(32px * var(--content-scale)) !important; }\\n.w-10 { width: calc(40px * var(--content-scale)) !important; }\\n.w-12 { width: calc(48px * var(--content-scale)) !important; }\\n\\n.h-3 { height: calc(12px * var(--content-scale)) !important; }\\n.h-4 { height: calc(16px * var(--content-scale)) !important; }\\n.h-5 { height: calc(20px * var(--content-scale)) !important; }\\n.h-6 { height: calc(24px * var(--content-scale)) !important; }\\n.h-8 { height: calc(32px * var(--content-scale)) !important; }\\n.h-10 { height: calc(40px * var(--content-scale)) !important; }\\n.h-12 { height: calc(48px * var(--content-scale)) !important; }\\n\\n/* Optimized Layout Classes for Perfect Content Fit */\\n.main-layout {\\n  height: 100vh;\\n  width: 100vw;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.content-area {\\n  flex: 1;\\n  display: flex;\\n  min-height: 0;\\n  overflow: hidden;\\n}\\n\\n.main-content {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 0;\\n  overflow: hidden;\\n}\\n\\n.header-section {\\n  height: var(--header-height);\\n  flex-shrink: 0;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.main-section {\\n  flex: 1;\\n  min-height: 0;\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.footer-section {\\n  height: calc(var(--footer-height) * 1.5);\\n  flex-shrink: 0;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.sidebar-left {\\n  width: var(--sidebar-width);\\n  flex-shrink: 0;\\n}\\n\\n.sidebar-right {\\n  width: var(--sidebar-width);\\n  flex-shrink: 0;\\n}\\n\\n/* Enhanced Dashboard Grid for No-Scroll Layout */\\n.dashboard-grid {\\n  display: grid;\\n  gap: calc(var(--base-gap) * 0.75);\\n  height: 100%;\\n  overflow: hidden;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.dashboard-grid-teacher {\\n  grid-template-rows: auto 1fr auto;\\n  grid-template-columns: 1fr;\\n}\\n\\n.dashboard-grid-school {\\n  grid-template-rows: auto 1fr;\\n  grid-template-columns: 1fr 1fr;\\n  gap: calc(var(--base-gap) * 1);\\n}\\n\\n/* Compact content areas */\\n.content-compact {\\n  max-height: calc(100vh - var(--header-height) - var(--footer-height) - calc(var(--base-spacing) * 4));\\n  overflow-y: auto;\\n}\\n\\n.content-no-scroll {\\n  height: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n/* Enhanced Animations */\\n@keyframes pulse-glow {\\n  0%, 100% {\\n    box-shadow: 0 0 5px rgba(0, 207, 255, 0.3);\\n  }\\n  50% {\\n    box-shadow: 0 0 20px rgba(0, 207, 255, 0.6), 0 0 30px rgba(0, 207, 255, 0.4);\\n  }\\n}\\n\\n@keyframes slide-in-up {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes fade-in-scale {\\n  from {\\n    opacity: 0;\\n    transform: scale(0.95);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n}\\n\\n.animate-pulse-glow {\\n  animation: pulse-glow 2s ease-in-out infinite;\\n}\\n\\n.animate-slide-in-up {\\n  animation: slide-in-up 0.5s ease-out;\\n}\\n\\n.animate-fade-in-scale {\\n  animation: fade-in-scale 0.3s ease-out;\\n}\\n\\n/* Hover Effects */\\n.hover-lift {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.hover-lift:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 207, 255, 0.2);\\n}\\n\\n/* Progress Bar Animations */\\n.progress-bar {\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.progress-bar::after {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\\n  animation: shimmer 2s infinite;\\n}\\n\\n@keyframes shimmer {\\n  0% {\\n    left: -100%;\\n  }\\n  100% {\\n    left: 100%;\\n  }\\n}\\n\\n/* Optimized Card Grid */\\n.card-grid {\\n  display: grid;\\n  gap: var(--base-gap);\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n.card-grid-2 {\\n  grid-template-columns: 1fr 1fr;\\n}\\n\\n.card-grid-3 {\\n  grid-template-columns: 1fr 1fr 1fr;\\n}\\n\\n.card-grid-4 {\\n  grid-template-columns: 1fr 1fr 1fr 1fr;\\n}\\n\\n.card-grid-auto {\\n  grid-template-columns: repeat(auto-fit, minmax(calc(250px * var(--content-scale)), 1fr));\\n}\\n\\n/* Optimized Card Sizing */\\n.card-compact {\\n  padding: calc(var(--base-card-padding) * 0.75);\\n  min-height: calc(120px * var(--content-scale));\\n}\\n\\n.card-standard {\\n  padding: var(--base-card-padding);\\n  min-height: calc(160px * var(--content-scale));\\n}\\n\\n.card-large {\\n  padding: calc(var(--base-card-padding) * 1.25);\\n  min-height: calc(200px * var(--content-scale));\\n}\\n\\n/* Neumorphic HUD Subnav Styles */\\n.subnav-hud {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  padding: calc(var(--base-spacing) * 1.5) 0;\\n  margin: 0 auto;\\n  max-width: calc(72px * 8 + var(--base-gap) * 7); /* 8 buttons with gaps */\\n}\\n\\n.subnav-button {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  width: calc(72px * var(--content-scale));\\n  height: calc(72px * var(--content-scale));\\n  margin: 0 calc(var(--base-gap) * 0.5);\\n  border-radius: calc(16px * var(--content-scale));\\n  background: linear-gradient(145deg, #2a2a3a, #1a1a2a);\\n  box-shadow:\\n    calc(8px * var(--content-scale)) calc(8px * var(--content-scale)) calc(16px * var(--content-scale)) rgba(0, 0, 0, 0.4),\\n    calc(-4px * var(--content-scale)) calc(-4px * var(--content-scale)) calc(8px * var(--content-scale)) rgba(255, 255, 255, 0.05);\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.subnav-button:hover {\\n  transform: translateY(calc(-2px * var(--content-scale)));\\n  box-shadow:\\n    calc(12px * var(--content-scale)) calc(12px * var(--content-scale)) calc(24px * var(--content-scale)) rgba(0, 0, 0, 0.5),\\n    calc(-6px * var(--content-scale)) calc(-6px * var(--content-scale)) calc(12px * var(--content-scale)) rgba(255, 255, 255, 0.08);\\n}\\n\\n.subnav-button.active {\\n  background: linear-gradient(145deg, #3a4a6a, #2a3a5a);\\n  box-shadow:\\n    inset calc(4px * var(--content-scale)) calc(4px * var(--content-scale)) calc(8px * var(--content-scale)) rgba(0, 0, 0, 0.3),\\n    inset calc(-2px * var(--content-scale)) calc(-2px * var(--content-scale)) calc(4px * var(--content-scale)) rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(100, 200, 255, 0.3);\\n}\\n\\n.subnav-icon {\\n  width: calc(24px * var(--content-scale));\\n  height: calc(24px * var(--content-scale));\\n  margin-bottom: calc(4px * var(--content-scale));\\n  color: #a0a0b0;\\n  transition: color 0.3s ease;\\n}\\n\\n.subnav-button:hover .subnav-icon,\\n.subnav-button.active .subnav-icon {\\n  color: #64b5f6;\\n}\\n\\n.subnav-label {\\n  font-size: calc(10px * var(--content-scale));\\n  font-weight: 500;\\n  color: #a0a0b0;\\n  text-align: center;\\n  line-height: 1.2;\\n  transition: color 0.3s ease;\\n}\\n\\n.subnav-button:hover .subnav-label,\\n.subnav-button.active .subnav-label {\\n  color: #64b5f6;\\n}\\n\\n/* HUD Glow Effect */\\n.subnav-button::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(circle at center, rgba(100, 181, 246, 0.1) 0%, transparent 70%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n  border-radius: inherit;\\n}\\n\\n.subnav-button:hover::before,\\n.subnav-button.active::before {\\n  opacity: 1;\\n}\\n\\n/* Enhanced HUD Vertical Layout for Left Sidebar */\\n.subnav-hud-vertical {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: stretch;\\n  justify-content: flex-start;\\n  gap: calc(var(--base-spacing) * 0.5);\\n  padding: calc(var(--base-spacing) * 0.75);\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n/* Enhanced subnav button with glassmorphism */\\n.subnav-hud-vertical .subnav-button {\\n  width: 100%;\\n  height: calc(36px * var(--content-scale));\\n  margin: 0;\\n  flex-shrink: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  padding: calc(var(--base-spacing) * 0.75) calc(var(--base-spacing) * 1);\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  border-radius: var(--base-border-radius);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n  box-shadow: var(--glass-shadow);\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover {\\n  background: rgba(0, 207, 255, 0.1);\\n  border-color: var(--hud-border-glow);\\n  transform: translateX(4px);\\n  box-shadow: 0 4px 20px rgba(0, 207, 255, 0.2);\\n}\\n\\n.subnav-hud-vertical .subnav-button.active {\\n  background: linear-gradient(135deg, rgba(0, 207, 255, 0.2) 0%, rgba(0, 255, 189, 0.1) 100%);\\n  border-color: var(--hud-neon-blue);\\n  box-shadow: 0 0 20px rgba(0, 207, 255, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n}\\n\\n/* Enhanced subnav icon */\\n.subnav-hud-vertical .subnav-icon {\\n  width: calc(18px * var(--content-scale));\\n  height: calc(18px * var(--content-scale));\\n  margin-right: calc(var(--base-spacing) * 1.5);\\n  color: var(--hud-text-secondary);\\n  transition: all 0.3s ease;\\n  flex-shrink: 0;\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover .subnav-icon {\\n  color: var(--hud-neon-blue);\\n  filter: drop-shadow(0 0 8px rgba(0, 207, 255, 0.6));\\n}\\n\\n.subnav-hud-vertical .subnav-button.active .subnav-icon {\\n  color: var(--hud-bright-white);\\n  filter: drop-shadow(0 0 12px rgba(0, 207, 255, 0.8));\\n}\\n\\n/* Enhanced subnav label - full text display */\\n.subnav-hud-vertical .subnav-label {\\n  color: var(--hud-text-primary);\\n  font-size: calc(12px * var(--content-scale));\\n  font-weight: 500;\\n  line-height: 1.2;\\n  white-space: nowrap;\\n  transition: all 0.3s ease;\\n  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover .subnav-label {\\n  color: var(--hud-bright-white);\\n  text-shadow: 0 0 8px rgba(0, 207, 255, 0.6);\\n}\\n\\n.subnav-hud-vertical .subnav-button.active .subnav-label {\\n  color: var(--hud-bright-white);\\n  font-weight: 600;\\n  text-shadow: 0 0 12px rgba(0, 207, 255, 0.8);\\n}\\n\\n.hover\\\\:scale-105:hover{\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.hover\\\\:border-cyan-400\\\\/50:hover{\\n  border-color: rgb(34 211 238 / 0.5);\\n}\\n\\n.hover\\\\:border-cyan-400\\\\/60:hover{\\n  border-color: rgb(34 211 238 / 0.6);\\n}\\n\\n.hover\\\\:border-gray-500\\\\/50:hover{\\n  border-color: rgb(107 114 128 / 0.5);\\n}\\n\\n.hover\\\\:bg-cyan-500\\\\/50:hover{\\n  background-color: rgb(6 182 212 / 0.5);\\n}\\n\\n.hover\\\\:bg-gray-700\\\\/30:hover{\\n  background-color: rgb(55 65 81 / 0.3);\\n}\\n\\n.hover\\\\:bg-gray-700\\\\/50:hover{\\n  background-color: rgb(55 65 81 / 0.5);\\n}\\n\\n.hover\\\\:bg-gray-700\\\\/60:hover{\\n  background-color: rgb(55 65 81 / 0.6);\\n}\\n\\n.hover\\\\:bg-gray-800\\\\/40:hover{\\n  background-color: rgb(31 41 55 / 0.4);\\n}\\n\\n.hover\\\\:bg-pink-500\\\\/90:hover{\\n  background-color: rgb(236 72 153 / 0.9);\\n}\\n\\n.hover\\\\:bg-white\\\\/30:hover{\\n  background-color: rgb(255 255 255 / 0.3);\\n}\\n\\n.hover\\\\:text-white:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:shadow-lg:hover{\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.hover\\\\:shadow-cyan-400\\\\/20:hover{\\n  --tw-shadow-color: rgb(34 211 238 / 0.2);\\n  --tw-shadow: var(--tw-shadow-colored);\\n}\\n\\n.focus\\\\:outline-none:focus{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n\\n.focus-visible\\\\:ring-1:focus-visible{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n\\n.focus-visible\\\\:ring-2:focus-visible{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n\\n.focus-visible\\\\:ring-brand-cyan:focus-visible{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(0 255 255 / var(--tw-ring-opacity, 1));\\n}\\n\\n.focus-visible\\\\:ring-white:focus-visible{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity, 1));\\n}\\n\\n.focus-visible\\\\:ring-offset-2:focus-visible{\\n  --tw-ring-offset-width: 2px;\\n}\\n\\n.focus-visible\\\\:ring-offset-gray-800:focus-visible{\\n  --tw-ring-offset-color: #1f2937;\\n}\\n\\n.disabled\\\\:cursor-not-allowed:disabled{\\n  cursor: not-allowed;\\n}\\n\\n.disabled\\\\:cursor-wait:disabled{\\n  cursor: wait;\\n}\\n\\n.disabled\\\\:opacity-50:disabled{\\n  opacity: 0.5;\\n}\\n\\n.group:hover .group-hover\\\\:scale-105{\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.group:hover .group-hover\\\\:scale-110{\\n  --tw-scale-x: 1.1;\\n  --tw-scale-y: 1.1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.group:hover .group-hover\\\\:text-cyan-300{\\n  --tw-text-opacity: 1;\\n  color: rgb(103 232 249 / var(--tw-text-opacity, 1));\\n}\\n\\n.group:hover .group-hover\\\\:text-white{\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n\\n.group:hover .group-hover\\\\:opacity-20{\\n  opacity: 0.2;\\n}\\n\\n@media (prefers-reduced-motion: no-preference){\\n\\n  @keyframes pulse{\\n\\n    50%{\\n      opacity: .5;\\n    }\\n  }\\n\\n  .motion-safe\\\\:animate-pulse{\\n    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n  }\\n}\\n\\n@media (min-width: 640px){\\n\\n  .sm\\\\:mr-3{\\n    margin-right: 0.75rem;\\n  }\\n\\n  .sm\\\\:mt-4{\\n    margin-top: 1rem;\\n  }\\n\\n  .sm\\\\:h-10{\\n    height: 2.5rem;\\n  }\\n\\n  .sm\\\\:h-12{\\n    height: 3rem;\\n  }\\n\\n  .sm\\\\:h-16{\\n    height: 4rem;\\n  }\\n\\n  .sm\\\\:h-3{\\n    height: 0.75rem;\\n  }\\n\\n  .sm\\\\:h-36{\\n    height: 9rem;\\n  }\\n\\n  .sm\\\\:h-4{\\n    height: 1rem;\\n  }\\n\\n  .sm\\\\:h-40{\\n    height: 10rem;\\n  }\\n\\n  .sm\\\\:h-48{\\n    height: 12rem;\\n  }\\n\\n  .sm\\\\:h-6{\\n    height: 1.5rem;\\n  }\\n\\n  .sm\\\\:h-8{\\n    height: 2rem;\\n  }\\n\\n  .sm\\\\:w-10{\\n    width: 2.5rem;\\n  }\\n\\n  .sm\\\\:w-12{\\n    width: 3rem;\\n  }\\n\\n  .sm\\\\:w-16{\\n    width: 4rem;\\n  }\\n\\n  .sm\\\\:w-3{\\n    width: 0.75rem;\\n  }\\n\\n  .sm\\\\:w-36{\\n    width: 9rem;\\n  }\\n\\n  .sm\\\\:w-4{\\n    width: 1rem;\\n  }\\n\\n  .sm\\\\:w-40{\\n    width: 10rem;\\n  }\\n\\n  .sm\\\\:w-6{\\n    width: 1.5rem;\\n  }\\n\\n  .sm\\\\:w-8{\\n    width: 2rem;\\n  }\\n\\n  .sm\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .sm\\\\:gap-4{\\n    gap: 1rem;\\n  }\\n\\n  .sm\\\\:gap-6{\\n    gap: 1.5rem;\\n  }\\n\\n  .sm\\\\:p-2{\\n    padding: 0.5rem;\\n  }\\n\\n  .sm\\\\:p-3{\\n    padding: 0.75rem;\\n  }\\n\\n  .sm\\\\:p-4{\\n    padding: 1rem;\\n  }\\n\\n  .sm\\\\:p-6{\\n    padding: 1.5rem;\\n  }\\n\\n  .sm\\\\:px-4{\\n    padding-left: 1rem;\\n    padding-right: 1rem;\\n  }\\n\\n  .sm\\\\:py-2{\\n    padding-top: 0.5rem;\\n    padding-bottom: 0.5rem;\\n  }\\n\\n  .sm\\\\:text-base{\\n    font-size: 1rem;\\n    line-height: 1.5rem;\\n  }\\n\\n  .sm\\\\:text-lg{\\n    font-size: 1.125rem;\\n    line-height: 1.75rem;\\n  }\\n\\n  .sm\\\\:text-sm{\\n    font-size: 0.875rem;\\n    line-height: 1.25rem;\\n  }\\n\\n  .sm\\\\:text-xl{\\n    font-size: 1.25rem;\\n    line-height: 1.75rem;\\n  }\\n\\n  .sm\\\\:text-xs{\\n    font-size: 0.75rem;\\n    line-height: 1rem;\\n  }\\n}\\n\\n@media (min-width: 768px){\\n\\n  .md\\\\:col-span-2{\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .md\\\\:grid-cols-2{\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-4{\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n}\\n\\n@media (min-width: 1024px){\\n\\n  .lg\\\\:col-span-12{\\n    grid-column: span 12 / span 12;\\n  }\\n\\n  .lg\\\\:col-span-2{\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .lg\\\\:col-span-3{\\n    grid-column: span 3 / span 3;\\n  }\\n\\n  .lg\\\\:col-span-4{\\n    grid-column: span 4 / span 4;\\n  }\\n\\n  .lg\\\\:col-span-5{\\n    grid-column: span 5 / span 5;\\n  }\\n\\n  .lg\\\\:col-span-7{\\n    grid-column: span 7 / span 7;\\n  }\\n\\n  .lg\\\\:h-40{\\n    height: 10rem;\\n  }\\n\\n  .lg\\\\:h-48{\\n    height: 12rem;\\n  }\\n\\n  .lg\\\\:w-40{\\n    width: 10rem;\\n  }\\n\\n  .lg\\\\:w-48{\\n    width: 12rem;\\n  }\\n\\n  .lg\\\\:grid-cols-12{\\n    grid-template-columns: repeat(12, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-4{\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:text-xl{\\n    font-size: 1.25rem;\\n    line-height: 1.75rem;\\n  }\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA,qGAAqG;;AAErG;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;;CAAc;;AAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,+HAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,+GAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;;AAAd;EAAA,aAAc;AAAA;AACd;EAAA;AAAoB;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AACpB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,yBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,2EAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yDAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;KAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gDAAmB;EAAnB,6DAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,6EAAmB;EAAnB,iGAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kGAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,gKAAmB;EAAnB,wJAAmB;EAAnB,iLAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;;AAEnB,6EAA6E;AAC7E;EACE,iBAAiB;EACjB,kBAAkB;EAClB,mDAAmD;EACnD,gDAAgD;EAChD,sDAAsD;EACtD,mDAAmD;EACnD,uDAAuD;EACvD,qDAAqD;EACrD,4CAA4C;EAC5C,kDAAkD;EAClD,kDAAkD;EAClD,kDAAkD;;EAElD,0BAA0B;EAC1B,mEAAmE;EACnE,qEAAqE;EACrE,yCAAyC;EACzC,wCAAwC;EACxC,uCAAuC;EACvC,2CAA2C;;EAE3C,sBAAsB;EACtB,wBAAwB;EACxB,yBAAyB;EACzB,4BAA4B;EAC5B,8BAA8B;EAC9B,uBAAuB;EACvB,2BAA2B;EAC3B,qBAAqB;;EAErB,kCAAkC;EAClC,2BAA2B;EAC3B,6BAA6B;EAC7B,yBAAyB;;EAEzB,0BAA0B;EAC1B,qCAAqC;EACrC,wCAAwC;EACxC,4EAA4E;AAC9E;;AAEA,kEAAkE;AAClE;EACE;IACE,mBAAmB;IACnB,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,mBAAmB;IACnB,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,mBAAmB;IACnB,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,mBAAmB;IACnB,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,mBAAmB;IACnB,oBAAoB;EACtB;AACF;;AAEA,mCAAmC;AACnC;EACE,sBAAsB;EACtB,SAAS;EACT,UAAU;AACZ;;AAEA;EACE,aAAa;EACb,YAAY;EACZ,gBAAgB;EAChB,SAAS;EACT,UAAU;AACZ;;AAEA;EACE,kCAAkC;EAClC,gCAAgC;EAChC,gBAAgB;EAChB,oGAAoG;EACpG,0BAA0B;EAC1B,0CAA0C;EAC1C,8BAA8B;AAChC;;AAEA;EACE,aAAa;EACb,YAAY;EACZ,gBAAgB;AAClB;;AAEA;EACE,wGAAwG;EACxG,4BAA4B;EAC5B,kBAAkB;AACpB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT;;6FAE2F;EAC3F,oBAAoB;AACtB;;AAEA,8BAA8B;AAC9B;EACE,gCAAgC;AAClC;;AAEA;EACE,8CAA8C;AAChD;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,8CAA8C;AAChD;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4BAA4B;AAC9B;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;EACE,oBAAoB;AACtB;;AAEA;EACE,gCAAgC;AAClC;;AAEA;EACE,gCAAgC;AAClC;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;EACE,4BAA4B;EAC5B,6BAA6B;AAC/B;;AAEA;EACE,iCAAiC;EACjC,8BAA8B;EAC9B,gCAAgC;EAChC,wCAAwC;AAC1C;;AAEA;EACE,iCAAiC;EACjC,oDAAoD;AACtD;;AAEA,6BAA6B;AAC7B;EACE,8BAA8B;EAC9B,mCAAmC;EACnC,oDAAoD;EACpD,mCAA2B;UAA3B,2BAA2B;EAC3B,+BAA+B;EAC/B,iDAAiD;EACjD,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,WAAW;EACX,kFAAkF;EAClF,YAAY;AACd;;AAEA;EACE,oCAAoC;EACpC,mEAAmE;EACnE,2BAA2B;AAC7B;;AAEA,6BAA6B;AAC7B;EACE,oFAAoF;EACpF,2BAA2B;AAC7B;;AAEA;EACE,iDAAiD;AACnD;;AAEA,yBAAyB;AACzB;EACE,2BAA2B;EAC3B,qCAAqC;EACrC,wCAAwC;EACxC,wCAAwC;EACxC,kBAAkB;EAClB,yBAAyB;EACzB,kBAAkB;EAClB,gBAAgB;EAChB,gBAAgB;EAChB,gBAAgB;EAChB,wBAAmB;EAAnB,mBAAmB;EACnB,mCAA2B;UAA3B,2BAA2B;EAC3B,+BAA+B;AACjC;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT,6FAA6F;EAC7F,UAAU;EACV,6BAA6B;AAC/B;;AAEA;EACE,UAAU;AACZ;;AAEA;EACE,kCAAkC;EAClC,6CAA6C;EAC7C,sBAAsB;AACxB;;AAEA,6BAA6B;AAC7B;EACE,yDAAyD;EACzD,gBAAgB;EAChB,uDAAuD;EACvD,2CAA2C;AAC7C;;AAEA;EACE,uDAAuD;EACvD,gBAAgB;EAChB,yDAAyD;EACzD,yCAAyC;EACzC,2BAA2B;AAC7B;;AAEA,8BAA8B;AAC9B;EACE,mDAAmD;EACnD,oDAAoD;EACpD,yDAAyD;AAC3D;;AAEA,iCAAiC;AACjC;EACE,KAAK,2BAA2B,EAAE;EAClC,MAAM,6BAA6B,EAAE;EACrC,OAAO,2BAA2B,EAAE;AACtC;;AAEA,oCAAoC;AACpC;EACE,sEAAsE;EACtE,kDAAkD;EAClD,4CAA4C;EAC5C,gBAAgB;EAChB,yBAAyB;EACzB,qBAAqB;EACrB,iBAAiB;EACjB,mCAA2B;UAA3B,2BAA2B;EAC3B,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,kCAAkC;EAClC,4BAA4B;EAC5B,mCAAmC;EACnC,2CAA2C;AAC7C;;AAEA;EACE,kCAAkC;EAClC,+BAA+B;EAC/B,sCAAsC;EACtC,2CAA2C;AAC7C;;AAEA;EACE,kCAAkC;EAClC,2BAA2B;EAC3B,kCAAkC;EAClC,2CAA2C;AAC7C;;AAEA;EACE,mCAAmC;EACnC,iCAAiC;EACjC,wCAAwC;EACxC,4CAA4C;AAC9C;;AAEA,4BAA4B;AAC5B;EACE,+BAA+B;EAC/B,mCAAmC;EACnC,mCAA2B;UAA3B,2BAA2B;EAC3B,yCAAyC;EACzC,kBAAkB;AACpB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,SAAS;EACT,OAAO;EACP,QAAQ;EACR,WAAW;EACX,kFAAkF;EAClF,YAAY;AACd;;AAEA,gCAAgC;AAChC;EACE,2BAA2B;EAC3B,qCAAqC;EACrC,mCAA2B;UAA3B,2BAA2B;EAC3B,iDAAiD;EACjD,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,kCAAkC;EAClC,oCAAoC;EACpC,6CAA6C;EAC7C,sBAAsB;AACxB;;AAEA;EACE,2FAA2F;EAC3F,kCAAkC;EAClC,2CAA2C;AAC7C;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,QAAQ;EACR,iFAAiF;EACjF,oBAAoB;AACtB;;AAEA,6EAA6E;AAC7E;EACE,0BAA0B;EAC1B,uBAAuB;EACvB,wBAAwB;EACxB,eAAe;EACf,MAAM;EACN,OAAO;EACP,gBAAgB;EAChB,mCAAmC;AACrC;;AAEA,qDAAqD;AACrD;EACE;IACE,qBAAqB;IACrB,mCAAmC;IACnC,oCAAoC;EACtC;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,mCAAmC;IACnC,oCAAoC;EACtC;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,mCAAmC;IACnC,oCAAoC;EACtC;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,mCAAmC;IACnC,oCAAoC;EACtC;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,mCAAmC;IACnC,oCAAoC;EACtC;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA,0BAA0B;AAC1B;EACE,kBAAkB;AACpB;;AAEA,wDAAwD;AACxD;EACE,kBAAkB;AACpB;;AAEA,4CAA4C;AAC5C;;;EAGE,wEAAwE;AAC1E;;AAEA,oCAAoC;AACpC;EACE,wEAAwE;AAC1E;;AAEA,iCAAiC;AACjC;EACE,wEAAwE;AAC1E;;AAEA,+BAA+B;AAC/B;EACE,kCAAkC;EAClC,mCAAmC;AACrC;;AAEA,sBAAsB;AACtB,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,qDAAqD,EAAE;AAC9D,OAAO,qDAAqD,EAAE;AAC9D,OAAO,qDAAqD,EAAE;AAC9D,OAAO,qDAAqD,EAAE;;AAE9D,OAAO,mDAAmD,EAAE;AAC5D,OAAO,mDAAmD,EAAE;AAC5D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;;AAE7D,eAAe;AACf,SAAS,gDAAgD,EAAE;AAC3D,SAAS,gDAAgD,EAAE;AAC3D,SAAS,iDAAiD,EAAE;AAC5D,SAAS,iDAAiD,EAAE;AAC5D,SAAS,iDAAiD,EAAE;AAC5D,SAAS,iDAAiD,EAAE;;AAE5D,wBAAwB;AACxB,WAAW,0DAA0D,EAAE;AACvE,cAAc,0DAA0D,EAAE;AAC1E,cAAc,0DAA0D,EAAE;AAC1E,cAAc,2DAA2D,EAAE;AAC3E,eAAe,2DAA2D,EAAE;;AAE5E;EACE;IACE,UAAU;IACV,2BAA2B;EAC7B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE;IACE,UAAU;EACZ;EACA;IACE,UAAU;EACZ;AACF;;AAEA;EACE,kCAAkC;AACpC;;AAEA,sDAAsD;AACtD,WAAW,uDAAuD,EAAE;AACpE,WAAW,uDAAuD,EAAE;AACpE,aAAa,uDAAuD,EAAE;AACtE,WAAW,uDAAuD,EAAE;AACpE,WAAW,uDAAuD,EAAE;AACpE,YAAY,uDAAuD,EAAE;AACrE,YAAY,uDAAuD,EAAE;AACrE,YAAY,uDAAuD,EAAE;;AAErE,6CAA6C;AAC7C,OAAO,mDAAmD,EAAE;AAC5D,OAAO,mDAAmD,EAAE;AAC5D,OAAO,mDAAmD,EAAE;AAC5D,OAAO,mDAAmD,EAAE;AAC5D,OAAO,mDAAmD,EAAE;AAC5D,QAAQ,mDAAmD,EAAE;AAC7D,QAAQ,mDAAmD,EAAE;;AAE7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,QAAQ,oDAAoD,EAAE;AAC9D,QAAQ,oDAAoD,EAAE;;AAE9D,qDAAqD;AACrD;EACE,aAAa;EACb,YAAY;EACZ,aAAa;EACb,sBAAsB;EACtB,gBAAgB;AAClB;;AAEA;EACE,OAAO;EACP,aAAa;EACb,aAAa;EACb,gBAAgB;AAClB;;AAEA;EACE,OAAO;EACP,aAAa;EACb,sBAAsB;EACtB,aAAa;EACb,gBAAgB;AAClB;;AAEA;EACE,4BAA4B;EAC5B,cAAc;EACd,wCAAwC;AAC1C;;AAEA;EACE,OAAO;EACP,aAAa;EACb,gBAAgB;EAChB,kBAAkB;EAClB,wCAAwC;AAC1C;;AAEA;EACE,wCAAwC;EACxC,cAAc;EACd,wCAAwC;AAC1C;;AAEA;EACE,2BAA2B;EAC3B,cAAc;AAChB;;AAEA;EACE,2BAA2B;EAC3B,cAAc;AAChB;;AAEA,iDAAiD;AACjD;EACE,aAAa;EACb,iCAAiC;EACjC,YAAY;EACZ,gBAAgB;EAChB,wCAAwC;AAC1C;;AAEA;EACE,iCAAiC;EACjC,0BAA0B;AAC5B;;AAEA;EACE,4BAA4B;EAC5B,8BAA8B;EAC9B,8BAA8B;AAChC;;AAEA,0BAA0B;AAC1B;EACE,qGAAqG;EACrG,gBAAgB;AAClB;;AAEA;EACE,YAAY;EACZ,gBAAgB;EAChB,aAAa;EACb,sBAAsB;AACxB;;AAEA,wBAAwB;AACxB;EACE;IACE,0CAA0C;EAC5C;EACA;IACE,4EAA4E;EAC9E;AACF;;AAEA;EACE;IACE,UAAU;IACV,2BAA2B;EAC7B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE;IACE,UAAU;IACV,sBAAsB;EACxB;EACA;IACE,UAAU;IACV,mBAAmB;EACrB;AACF;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,oCAAoC;AACtC;;AAEA;EACE,sCAAsC;AACxC;;AAEA,kBAAkB;AAClB;EACE,iDAAiD;AACnD;;AAEA;EACE,2BAA2B;EAC3B,0EAA0E;AAC5E;;AAEA,4BAA4B;AAC5B;EACE,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,WAAW;EACX,WAAW;EACX,YAAY;EACZ,sFAAsF;EACtF,8BAA8B;AAChC;;AAEA;EACE;IACE,WAAW;EACb;EACA;IACE,UAAU;EACZ;AACF;;AAEA,wBAAwB;AACxB;EACE,aAAa;EACb,oBAAoB;EACpB,YAAY;EACZ,gBAAgB;AAClB;;AAEA;EACE,8BAA8B;AAChC;;AAEA;EACE,kCAAkC;AACpC;;AAEA;EACE,sCAAsC;AACxC;;AAEA;EACE,wFAAwF;AAC1F;;AAEA,0BAA0B;AAC1B;EACE,8CAA8C;EAC9C,8CAA8C;AAChD;;AAEA;EACE,iCAAiC;EACjC,8CAA8C;AAChD;;AAEA;EACE,8CAA8C;EAC9C,8CAA8C;AAChD;;AAEA,iCAAiC;AACjC;EACE,aAAa;EACb,uBAAuB;EACvB,mBAAmB;EACnB,0CAA0C;EAC1C,cAAc;EACd,+CAA+C,EAAE,wBAAwB;AAC3E;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,mBAAmB;EACnB,uBAAuB;EACvB,wCAAwC;EACxC,yCAAyC;EACzC,qCAAqC;EACrC,gDAAgD;EAChD,qDAAqD;EACrD;;kIAEgI;EAChI,0CAA0C;EAC1C,yBAAyB;EACzB,eAAe;EACf,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,wDAAwD;EACxD;;mIAEiI;AACnI;;AAEA;EACE,qDAAqD;EACrD;;uIAEqI;EACrI,0CAA0C;AAC5C;;AAEA;EACE,wCAAwC;EACxC,yCAAyC;EACzC,+CAA+C;EAC/C,cAAc;EACd,2BAA2B;AAC7B;;AAEA;;EAEE,cAAc;AAChB;;AAEA;EACE,4CAA4C;EAC5C,gBAAgB;EAChB,cAAc;EACd,kBAAkB;EAClB,gBAAgB;EAChB,2BAA2B;AAC7B;;AAEA;;EAEE,cAAc;AAChB;;AAEA,oBAAoB;AACpB;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT,2FAA2F;EAC3F,UAAU;EACV,6BAA6B;EAC7B,sBAAsB;AACxB;;AAEA;;EAEE,UAAU;AACZ;;AAEA,kDAAkD;AAClD;EACE,aAAa;EACb,sBAAsB;EACtB,oBAAoB;EACpB,2BAA2B;EAC3B,oCAAoC;EACpC,yCAAyC;EACzC,YAAY;EACZ,gBAAgB;AAClB;;AAEA,8CAA8C;AAC9C;EACE,WAAW;EACX,yCAAyC;EACzC,SAAS;EACT,cAAc;EACd,aAAa;EACb,mBAAmB;EACnB,2BAA2B;EAC3B,uEAAuE;EACvE,2BAA2B;EAC3B,qCAAqC;EACrC,wCAAwC;EACxC,mCAA2B;UAA3B,2BAA2B;EAC3B,iDAAiD;EACjD,kBAAkB;EAClB,gBAAgB;EAChB,+BAA+B;AACjC;;AAEA;EACE,kCAAkC;EAClC,oCAAoC;EACpC,0BAA0B;EAC1B,6CAA6C;AAC/C;;AAEA;EACE,2FAA2F;EAC3F,kCAAkC;EAClC,mFAAmF;AACrF;;AAEA,yBAAyB;AACzB;EACE,wCAAwC;EACxC,yCAAyC;EACzC,6CAA6C;EAC7C,gCAAgC;EAChC,yBAAyB;EACzB,cAAc;AAChB;;AAEA;EACE,2BAA2B;EAC3B,mDAAmD;AACrD;;AAEA;EACE,8BAA8B;EAC9B,oDAAoD;AACtD;;AAEA,8CAA8C;AAC9C;EACE,8BAA8B;EAC9B,4CAA4C;EAC5C,gBAAgB;EAChB,gBAAgB;EAChB,mBAAmB;EACnB,yBAAyB;EACzB,+CAA+C;AACjD;;AAEA;EACE,8BAA8B;EAC9B,2CAA2C;AAC7C;;AAEA;EACE,8BAA8B;EAC9B,gBAAgB;EAChB,4CAA4C;AAC9C;;AA/8BA;EAAA,kBAg9BA;EAh9BA,kBAg9BA;EAh9BA;AAg9BA;;AAh9BA;EAAA;AAg9BA;;AAh9BA;EAAA;AAg9BA;;AAh9BA;EAAA;AAg9BA;;AAh9BA;EAAA;AAg9BA;;AAh9BA;EAAA;AAg9BA;;AAh9BA;EAAA;AAg9BA;;AAh9BA;EAAA;AAg9BA;;AAh9BA;EAAA;AAg9BA;;AAh9BA;EAAA;AAg9BA;;AAh9BA;EAAA;AAg9BA;;AAh9BA;EAAA,oBAg9BA;EAh9BA;AAg9BA;;AAh9BA;EAAA,+EAg9BA;EAh9BA,mGAg9BA;EAh9BA;AAg9BA;;AAh9BA;EAAA,wCAg9BA;EAh9BA;AAg9BA;;AAh9BA;EAAA,8BAg9BA;EAh9BA;AAg9BA;;AAh9BA;EAAA,2GAg9BA;EAh9BA,yGAg9BA;EAh9BA;AAg9BA;;AAh9BA;EAAA,2GAg9BA;EAh9BA,yGAg9BA;EAh9BA;AAg9BA;;AAh9BA;EAAA,oBAg9BA;EAh9BA;AAg9BA;;AAh9BA;EAAA,oBAg9BA;EAh9BA;AAg9BA;;AAh9BA;EAAA;AAg9BA;;AAh9BA;EAAA;AAg9BA;;AAh9BA;EAAA;AAg9BA;;AAh9BA;EAAA;AAg9BA;;AAh9BA;EAAA;AAg9BA;;AAh9BA;EAAA,kBAg9BA;EAh9BA,kBAg9BA;EAh9BA;AAg9BA;;AAh9BA;EAAA,iBAg9BA;EAh9BA,iBAg9BA;EAh9BA;AAg9BA;;AAh9BA;EAAA,oBAg9BA;EAh9BA;AAg9BA;;AAh9BA;EAAA,oBAg9BA;EAh9BA;AAg9BA;;AAh9BA;EAAA;AAg9BA;;AAh9BA;;EAAA;;IAAA;MAAA;IAg9BA;EAAA;;EAh9BA;IAAA;EAg9BA;AAAA;;AAh9BA;;EAAA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA,kBAg9BA;IAh9BA;EAg9BA;;EAh9BA;IAAA,mBAg9BA;IAh9BA;EAg9BA;;EAh9BA;IAAA,eAg9BA;IAh9BA;EAg9BA;;EAh9BA;IAAA,mBAg9BA;IAh9BA;EAg9BA;;EAh9BA;IAAA,mBAg9BA;IAh9BA;EAg9BA;;EAh9BA;IAAA,kBAg9BA;IAh9BA;EAg9BA;;EAh9BA;IAAA,kBAg9BA;IAh9BA;EAg9BA;AAAA;;AAh9BA;;EAAA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;AAAA;;AAh9BA;;EAAA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA;EAg9BA;;EAh9BA;IAAA,kBAg9BA;IAh9BA;EAg9BA;AAAA\",\"sourcesContent\":[\"@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');\\n\\n@tailwind base;\\n@tailwind components;\\n@tailwind utilities;\\n\\n/* CSS Custom Properties for Responsive Scaling - Optimized for Content Fit */\\n:root {\\n  --scale-factor: 1;\\n  --content-scale: 3;\\n  --base-font-size: calc(14px * var(--content-scale));\\n  --base-spacing: calc(6px * var(--content-scale));\\n  --base-border-radius: calc(8px * var(--content-scale));\\n  --base-icon-size: calc(20px * var(--content-scale));\\n  --base-button-height: calc(36px * var(--content-scale));\\n  --base-card-padding: calc(1px * var(--content-scale));\\n  --base-gap: calc(6px * var(--content-scale));\\n  --header-height: calc(60px * var(--content-scale));\\n  --footer-height: calc(50px * var(--content-scale));\\n  --sidebar-width: calc(80px * var(--content-scale));\\n\\n  /* HUD/HDR Color Palette */\\n  --hud-bg-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  --hud-bg-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\\n  --hud-bg-panel: rgba(255, 255, 255, 0.12);\\n  --hud-bg-card: rgba(255, 255, 255, 0.18);\\n  --hud-border: rgba(255, 255, 255, 0.25);\\n  --hud-border-glow: rgba(255, 255, 255, 0.4);\\n\\n  /* HUD Accent Colors */\\n  --hud-neon-blue: #4299e1;\\n  --hud-aqua-green: #48bb78;\\n  --hud-energy-orange: #ed8936;\\n  --hud-electric-purple: #9f7aea;\\n  --hud-deep-red: #f56565;\\n  --hud-bright-white: #2d3748;\\n  --hud-silver: #4a5568;\\n\\n  /* Text Colors for Glassmorphism */\\n  --hud-text-primary: #2d3748;\\n  --hud-text-secondary: #4a5568;\\n  --hud-text-light: #718096;\\n\\n  /* Glassmorphism Effects */\\n  --glass-bg: rgba(255, 255, 255, 0.15);\\n  --glass-border: rgba(255, 255, 255, 0.2);\\n  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05);\\n}\\n\\n/* Responsive scaling variables - now handled by --content-scale */\\n@media (max-width: 1920px) and (min-width: 1600px) {\\n  :root {\\n    --scale-factor: 0.9;\\n    --content-scale: 0.9;\\n  }\\n}\\n\\n@media (max-width: 1599px) and (min-width: 1400px) {\\n  :root {\\n    --scale-factor: 0.8;\\n    --content-scale: 0.8;\\n  }\\n}\\n\\n@media (max-width: 1399px) and (min-width: 1200px) {\\n  :root {\\n    --scale-factor: 0.7;\\n    --content-scale: 0.7;\\n  }\\n}\\n\\n@media (max-width: 1199px) and (min-width: 1000px) {\\n  :root {\\n    --scale-factor: 0.6;\\n    --content-scale: 0.6;\\n  }\\n}\\n\\n@media (max-width: 999px) {\\n  :root {\\n    --scale-factor: 0.5;\\n    --content-scale: 0.5;\\n  }\\n}\\n\\n/* Ensure base styles are applied */\\n* {\\n  box-sizing: border-box;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nhtml, body {\\n  height: 100vh;\\n  width: 100vw;\\n  overflow: hidden;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nbody {\\n  font-family: 'Poppins', sans-serif;\\n  font-size: var(--base-font-size);\\n  line-height: 1.5;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);\\n  background-size: 400% 400%;\\n  animation: gradientShift 15s ease infinite;\\n  color: var(--hud-text-primary);\\n}\\n\\n#__next {\\n  height: 100vh;\\n  width: 100vw;\\n  overflow: hidden;\\n}\\n\\n.main-background {\\n  background: linear-gradient(135deg, var(--hud-bg-primary) 0%, var(--hud-bg-secondary) 50%, #05080e 100%);\\n  background-attachment: fixed;\\n  position: relative;\\n}\\n\\n.main-background::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(ellipse at 20% 30%, rgba(209, 160, 255, 0.1) 0%, transparent 50%),\\n              radial-gradient(ellipse at 80% 70%, rgba(188, 19, 254, 0.08) 0%, transparent 50%),\\n              radial-gradient(ellipse at 50% 50%, rgba(41, 52, 95, 0.05) 0%, transparent 70%);\\n  pointer-events: none;\\n}\\n\\n/* Global Responsive Classes */\\n.responsive-text {\\n  font-size: var(--base-font-size);\\n}\\n\\n.responsive-text-sm {\\n  font-size: calc(var(--base-font-size) * 0.875);\\n}\\n\\n.responsive-text-xs {\\n  font-size: calc(var(--base-font-size) * 0.75);\\n}\\n\\n.responsive-text-lg {\\n  font-size: calc(var(--base-font-size) * 1.125);\\n}\\n\\n.responsive-text-xl {\\n  font-size: calc(var(--base-font-size) * 1.25);\\n}\\n\\n.responsive-text-2xl {\\n  font-size: calc(var(--base-font-size) * 1.5);\\n}\\n\\n.responsive-spacing {\\n  padding: var(--base-spacing);\\n}\\n\\n.responsive-spacing-sm {\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.responsive-spacing-lg {\\n  padding: calc(var(--base-spacing) * 1.5);\\n}\\n\\n.responsive-gap {\\n  gap: var(--base-gap);\\n}\\n\\n.responsive-gap-sm {\\n  gap: calc(var(--base-gap) * 0.5);\\n}\\n\\n.responsive-gap-lg {\\n  gap: calc(var(--base-gap) * 1.5);\\n}\\n\\n.responsive-border-radius {\\n  border-radius: var(--base-border-radius);\\n}\\n\\n.responsive-icon {\\n  width: var(--base-icon-size);\\n  height: var(--base-icon-size);\\n}\\n\\n.responsive-button {\\n  height: var(--base-button-height);\\n  padding: 0 var(--base-spacing);\\n  font-size: var(--base-font-size);\\n  border-radius: var(--base-border-radius);\\n}\\n\\n.responsive-card {\\n  padding: var(--base-card-padding);\\n  border-radius: calc(var(--base-border-radius) * 1.5);\\n}\\n\\n/* Enhanced HUD Card System */\\n.hud-card {\\n  background: var(--hud-bg-card);\\n  border: 1px solid var(--hud-border);\\n  border-radius: calc(var(--base-border-radius) * 1.5);\\n  backdrop-filter: blur(20px);\\n  box-shadow: var(--glass-shadow);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.hud-card::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(90deg, transparent, var(--hud-neon-blue), transparent);\\n  opacity: 0.6;\\n}\\n\\n.hud-card:hover {\\n  border-color: var(--hud-border-glow);\\n  box-shadow: 0 8px 40px rgba(0, 207, 255, 0.15), var(--glass-shadow);\\n  transform: translateY(-2px);\\n}\\n\\n/* Compact HUD card padding */\\n.hud-card .ant-card-head {\\n  padding: calc(var(--base-spacing) * 0.75) calc(var(--base-spacing) * 1.5) !important;\\n  min-height: auto !important;\\n}\\n\\n.hud-card .ant-card-body {\\n  padding: calc(var(--base-spacing) * 1) !important;\\n}\\n\\n/* Compact metric cards */\\n.metric-card {\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  border-radius: var(--base-border-radius);\\n  padding: calc(var(--base-spacing) * 0.3);\\n  text-align: center;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n  min-height: 60px;\\n  max-height: 80px;\\n  height: fit-content;\\n  backdrop-filter: blur(20px);\\n  box-shadow: var(--glass-shadow);\\n}\\n\\n.metric-card::after {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(0, 207, 255, 0.05) 0%, rgba(0, 255, 189, 0.05) 100%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.metric-card:hover::after {\\n  opacity: 1;\\n}\\n\\n.metric-card:hover {\\n  border-color: var(--hud-neon-blue);\\n  box-shadow: 0 4px 20px rgba(0, 207, 255, 0.2);\\n  transform: scale(1.02);\\n}\\n\\n/* Compact metric card text */\\n.metric-card p {\\n  margin-bottom: calc(var(--base-spacing) * 0.1) !important;\\n  line-height: 1.1;\\n  font-size: calc(var(--base-font-size) * 0.7) !important;\\n  color: var(--hud-text-secondary) !important;\\n}\\n\\n.metric-card .text-xl {\\n  font-size: calc(var(--base-font-size) * 1.1) !important;\\n  line-height: 1.0;\\n  margin-bottom: calc(var(--base-spacing) * 0.1) !important;\\n  color: var(--hud-text-primary) !important;\\n  font-weight: 700 !important;\\n}\\n\\n/* Compact metric card icons */\\n.metric-card .w-10.h-10 {\\n  width: calc(var(--base-icon-size) * 1.5) !important;\\n  height: calc(var(--base-icon-size) * 1.5) !important;\\n  margin-bottom: calc(var(--base-spacing) * 0.5) !important;\\n}\\n\\n/* Beautiful gradient animation */\\n@keyframes gradientShift {\\n  0% { background-position: 0% 50%; }\\n  50% { background-position: 100% 50%; }\\n  100% { background-position: 0% 50%; }\\n}\\n\\n/* Status badges with glow effects */\\n.status-badge {\\n  padding: calc(var(--base-spacing) * 0.5) calc(var(--base-spacing) * 1);\\n  border-radius: calc(var(--base-border-radius) * 2);\\n  font-size: calc(10px * var(--content-scale));\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  border: 1px solid;\\n  backdrop-filter: blur(10px);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.status-badge.active {\\n  background: rgba(0, 255, 189, 0.2);\\n  color: var(--hud-aqua-green);\\n  border-color: var(--hud-aqua-green);\\n  box-shadow: 0 0 10px rgba(0, 255, 189, 0.3);\\n}\\n\\n.status-badge.beta {\\n  background: rgba(255, 165, 0, 0.2);\\n  color: var(--hud-energy-orange);\\n  border-color: var(--hud-energy-orange);\\n  box-shadow: 0 0 10px rgba(255, 165, 0, 0.3);\\n}\\n\\n.status-badge.live {\\n  background: rgba(0, 207, 255, 0.2);\\n  color: var(--hud-neon-blue);\\n  border-color: var(--hud-neon-blue);\\n  box-shadow: 0 0 10px rgba(0, 207, 255, 0.3);\\n}\\n\\n.status-badge.testing {\\n  background: rgba(188, 19, 254, 0.2);\\n  color: var(--hud-electric-purple);\\n  border-color: var(--hud-electric-purple);\\n  box-shadow: 0 0 10px rgba(188, 19, 254, 0.3);\\n}\\n\\n/* Enhanced Header Styling */\\n.header-hud {\\n  background: var(--hud-bg-panel);\\n  border: 1px solid var(--hud-border);\\n  backdrop-filter: blur(20px);\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n  position: relative;\\n}\\n\\n.header-hud::before {\\n  content: '';\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(90deg, transparent, var(--hud-neon-blue), transparent);\\n  opacity: 0.4;\\n}\\n\\n/* Enhanced Department Buttons */\\n.dept-button {\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  backdrop-filter: blur(10px);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.dept-button:hover {\\n  background: rgba(0, 207, 255, 0.1);\\n  border-color: var(--hud-border-glow);\\n  box-shadow: 0 4px 20px rgba(0, 207, 255, 0.2);\\n  transform: scale(1.05);\\n}\\n\\n.dept-button.active {\\n  background: linear-gradient(135deg, rgba(0, 207, 255, 0.2) 0%, rgba(0, 255, 189, 0.1) 100%);\\n  border-color: var(--hud-neon-blue);\\n  box-shadow: 0 0 20px rgba(0, 207, 255, 0.3);\\n}\\n\\n.dept-button.active::after {\\n  content: '';\\n  position: absolute;\\n  inset: 0;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\\n  pointer-events: none;\\n}\\n\\n/* Dashboard Auto-Scale - Scales ALL content including text, icons, spacing */\\n.dashboard-auto-scale {\\n  transform-origin: top left;\\n  width: 100vw !important;\\n  height: 100vh !important;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  overflow: hidden;\\n  transition: transform 0.3s ease-out;\\n}\\n\\n/* Scale everything - content, text, icons, spacing */\\n@media (max-width: 1920px) and (min-width: 1600px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.9);\\n    width: calc(100vw / 0.9) !important;\\n    height: calc(100vh / 0.9) !important;\\n  }\\n  :root {\\n    --content-scale: 0.9;\\n  }\\n}\\n\\n@media (max-width: 1599px) and (min-width: 1400px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.8);\\n    width: calc(100vw / 0.8) !important;\\n    height: calc(100vh / 0.8) !important;\\n  }\\n  :root {\\n    --content-scale: 0.8;\\n  }\\n}\\n\\n@media (max-width: 1399px) and (min-width: 1200px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.7);\\n    width: calc(100vw / 0.7) !important;\\n    height: calc(100vh / 0.7) !important;\\n  }\\n  :root {\\n    --content-scale: 0.7;\\n  }\\n}\\n\\n@media (max-width: 1199px) and (min-width: 1000px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.6);\\n    width: calc(100vw / 0.6) !important;\\n    height: calc(100vh / 0.6) !important;\\n  }\\n  :root {\\n    --content-scale: 0.6;\\n  }\\n}\\n\\n@media (max-width: 999px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.5);\\n    width: calc(100vw / 0.5) !important;\\n    height: calc(100vh / 0.5) !important;\\n  }\\n  :root {\\n    --content-scale: 0.5;\\n  }\\n}\\n\\n/* Default content scale */\\n:root {\\n  --content-scale: 1;\\n}\\n\\n/* Universal content scaling - applies to ALL elements */\\n* {\\n  font-size: inherit;\\n}\\n\\n/* Scale all text elements in main content */\\n.main-section h1, .main-section h2, .main-section h3, .main-section h4, .main-section h5, .main-section h6,\\n.main-section p, .main-section span, .main-section div, .main-section button,\\n.main-section input, .main-section textarea, .main-section select, .main-section label, .main-section a {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale card content specifically */\\n.card-content * {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale chart text and numbers */\\n.recharts-text, .recharts-label, .chart-text, .metric-value, .metric-label {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale all icons and images */\\nsvg, img, .icon {\\n  width: calc(var(--base-icon-size));\\n  height: calc(var(--base-icon-size));\\n}\\n\\n/* Scale all spacing */\\n.p-1 { padding: calc(4px * var(--content-scale)) !important; }\\n.p-2 { padding: calc(8px * var(--content-scale)) !important; }\\n.p-3 { padding: calc(12px * var(--content-scale)) !important; }\\n.p-4 { padding: calc(16px * var(--content-scale)) !important; }\\n.p-5 { padding: calc(20px * var(--content-scale)) !important; }\\n.p-6 { padding: calc(24px * var(--content-scale)) !important; }\\n\\n.m-1 { margin: calc(4px * var(--content-scale)) !important; }\\n.m-2 { margin: calc(8px * var(--content-scale)) !important; }\\n.m-3 { margin: calc(12px * var(--content-scale)) !important; }\\n.m-4 { margin: calc(16px * var(--content-scale)) !important; }\\n.m-5 { margin: calc(20px * var(--content-scale)) !important; }\\n.m-6 { margin: calc(24px * var(--content-scale)) !important; }\\n\\n/* Scale gaps */\\n.gap-1 { gap: calc(4px * var(--content-scale)) !important; }\\n.gap-2 { gap: calc(8px * var(--content-scale)) !important; }\\n.gap-3 { gap: calc(12px * var(--content-scale)) !important; }\\n.gap-4 { gap: calc(16px * var(--content-scale)) !important; }\\n.gap-5 { gap: calc(20px * var(--content-scale)) !important; }\\n.gap-6 { gap: calc(24px * var(--content-scale)) !important; }\\n\\n/* Scale border radius */\\n.rounded { border-radius: calc(4px * var(--content-scale)) !important; }\\n.rounded-md { border-radius: calc(6px * var(--content-scale)) !important; }\\n.rounded-lg { border-radius: calc(8px * var(--content-scale)) !important; }\\n.rounded-xl { border-radius: calc(12px * var(--content-scale)) !important; }\\n.rounded-2xl { border-radius: calc(16px * var(--content-scale)) !important; }\\n\\n@keyframes fade-in-up {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n.animate-fade-in-up {\\n  animation: fade-in-up 0.3s ease-out forwards;\\n}\\n\\n@keyframes fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n.animate-fadeIn {\\n  animation: fadeIn 0.5s ease-in-out;\\n}\\n\\n/* Override Tailwind text sizes with content scaling */\\n.text-xs { font-size: calc(12px * var(--content-scale)) !important; }\\n.text-sm { font-size: calc(14px * var(--content-scale)) !important; }\\n.text-base { font-size: calc(16px * var(--content-scale)) !important; }\\n.text-lg { font-size: calc(18px * var(--content-scale)) !important; }\\n.text-xl { font-size: calc(20px * var(--content-scale)) !important; }\\n.text-2xl { font-size: calc(24px * var(--content-scale)) !important; }\\n.text-3xl { font-size: calc(30px * var(--content-scale)) !important; }\\n.text-4xl { font-size: calc(36px * var(--content-scale)) !important; }\\n\\n/* Override Tailwind width/height for icons */\\n.w-3 { width: calc(12px * var(--content-scale)) !important; }\\n.w-4 { width: calc(16px * var(--content-scale)) !important; }\\n.w-5 { width: calc(20px * var(--content-scale)) !important; }\\n.w-6 { width: calc(24px * var(--content-scale)) !important; }\\n.w-8 { width: calc(32px * var(--content-scale)) !important; }\\n.w-10 { width: calc(40px * var(--content-scale)) !important; }\\n.w-12 { width: calc(48px * var(--content-scale)) !important; }\\n\\n.h-3 { height: calc(12px * var(--content-scale)) !important; }\\n.h-4 { height: calc(16px * var(--content-scale)) !important; }\\n.h-5 { height: calc(20px * var(--content-scale)) !important; }\\n.h-6 { height: calc(24px * var(--content-scale)) !important; }\\n.h-8 { height: calc(32px * var(--content-scale)) !important; }\\n.h-10 { height: calc(40px * var(--content-scale)) !important; }\\n.h-12 { height: calc(48px * var(--content-scale)) !important; }\\n\\n/* Optimized Layout Classes for Perfect Content Fit */\\n.main-layout {\\n  height: 100vh;\\n  width: 100vw;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.content-area {\\n  flex: 1;\\n  display: flex;\\n  min-height: 0;\\n  overflow: hidden;\\n}\\n\\n.main-content {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 0;\\n  overflow: hidden;\\n}\\n\\n.header-section {\\n  height: var(--header-height);\\n  flex-shrink: 0;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.main-section {\\n  flex: 1;\\n  min-height: 0;\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.footer-section {\\n  height: calc(var(--footer-height) * 1.5);\\n  flex-shrink: 0;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.sidebar-left {\\n  width: var(--sidebar-width);\\n  flex-shrink: 0;\\n}\\n\\n.sidebar-right {\\n  width: var(--sidebar-width);\\n  flex-shrink: 0;\\n}\\n\\n/* Enhanced Dashboard Grid for No-Scroll Layout */\\n.dashboard-grid {\\n  display: grid;\\n  gap: calc(var(--base-gap) * 0.75);\\n  height: 100%;\\n  overflow: hidden;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.dashboard-grid-teacher {\\n  grid-template-rows: auto 1fr auto;\\n  grid-template-columns: 1fr;\\n}\\n\\n.dashboard-grid-school {\\n  grid-template-rows: auto 1fr;\\n  grid-template-columns: 1fr 1fr;\\n  gap: calc(var(--base-gap) * 1);\\n}\\n\\n/* Compact content areas */\\n.content-compact {\\n  max-height: calc(100vh - var(--header-height) - var(--footer-height) - calc(var(--base-spacing) * 4));\\n  overflow-y: auto;\\n}\\n\\n.content-no-scroll {\\n  height: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n/* Enhanced Animations */\\n@keyframes pulse-glow {\\n  0%, 100% {\\n    box-shadow: 0 0 5px rgba(0, 207, 255, 0.3);\\n  }\\n  50% {\\n    box-shadow: 0 0 20px rgba(0, 207, 255, 0.6), 0 0 30px rgba(0, 207, 255, 0.4);\\n  }\\n}\\n\\n@keyframes slide-in-up {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes fade-in-scale {\\n  from {\\n    opacity: 0;\\n    transform: scale(0.95);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n}\\n\\n.animate-pulse-glow {\\n  animation: pulse-glow 2s ease-in-out infinite;\\n}\\n\\n.animate-slide-in-up {\\n  animation: slide-in-up 0.5s ease-out;\\n}\\n\\n.animate-fade-in-scale {\\n  animation: fade-in-scale 0.3s ease-out;\\n}\\n\\n/* Hover Effects */\\n.hover-lift {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.hover-lift:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 207, 255, 0.2);\\n}\\n\\n/* Progress Bar Animations */\\n.progress-bar {\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.progress-bar::after {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\\n  animation: shimmer 2s infinite;\\n}\\n\\n@keyframes shimmer {\\n  0% {\\n    left: -100%;\\n  }\\n  100% {\\n    left: 100%;\\n  }\\n}\\n\\n/* Optimized Card Grid */\\n.card-grid {\\n  display: grid;\\n  gap: var(--base-gap);\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n.card-grid-2 {\\n  grid-template-columns: 1fr 1fr;\\n}\\n\\n.card-grid-3 {\\n  grid-template-columns: 1fr 1fr 1fr;\\n}\\n\\n.card-grid-4 {\\n  grid-template-columns: 1fr 1fr 1fr 1fr;\\n}\\n\\n.card-grid-auto {\\n  grid-template-columns: repeat(auto-fit, minmax(calc(250px * var(--content-scale)), 1fr));\\n}\\n\\n/* Optimized Card Sizing */\\n.card-compact {\\n  padding: calc(var(--base-card-padding) * 0.75);\\n  min-height: calc(120px * var(--content-scale));\\n}\\n\\n.card-standard {\\n  padding: var(--base-card-padding);\\n  min-height: calc(160px * var(--content-scale));\\n}\\n\\n.card-large {\\n  padding: calc(var(--base-card-padding) * 1.25);\\n  min-height: calc(200px * var(--content-scale));\\n}\\n\\n/* Neumorphic HUD Subnav Styles */\\n.subnav-hud {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  padding: calc(var(--base-spacing) * 1.5) 0;\\n  margin: 0 auto;\\n  max-width: calc(72px * 8 + var(--base-gap) * 7); /* 8 buttons with gaps */\\n}\\n\\n.subnav-button {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  width: calc(72px * var(--content-scale));\\n  height: calc(72px * var(--content-scale));\\n  margin: 0 calc(var(--base-gap) * 0.5);\\n  border-radius: calc(16px * var(--content-scale));\\n  background: linear-gradient(145deg, #2a2a3a, #1a1a2a);\\n  box-shadow:\\n    calc(8px * var(--content-scale)) calc(8px * var(--content-scale)) calc(16px * var(--content-scale)) rgba(0, 0, 0, 0.4),\\n    calc(-4px * var(--content-scale)) calc(-4px * var(--content-scale)) calc(8px * var(--content-scale)) rgba(255, 255, 255, 0.05);\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.subnav-button:hover {\\n  transform: translateY(calc(-2px * var(--content-scale)));\\n  box-shadow:\\n    calc(12px * var(--content-scale)) calc(12px * var(--content-scale)) calc(24px * var(--content-scale)) rgba(0, 0, 0, 0.5),\\n    calc(-6px * var(--content-scale)) calc(-6px * var(--content-scale)) calc(12px * var(--content-scale)) rgba(255, 255, 255, 0.08);\\n}\\n\\n.subnav-button.active {\\n  background: linear-gradient(145deg, #3a4a6a, #2a3a5a);\\n  box-shadow:\\n    inset calc(4px * var(--content-scale)) calc(4px * var(--content-scale)) calc(8px * var(--content-scale)) rgba(0, 0, 0, 0.3),\\n    inset calc(-2px * var(--content-scale)) calc(-2px * var(--content-scale)) calc(4px * var(--content-scale)) rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(100, 200, 255, 0.3);\\n}\\n\\n.subnav-icon {\\n  width: calc(24px * var(--content-scale));\\n  height: calc(24px * var(--content-scale));\\n  margin-bottom: calc(4px * var(--content-scale));\\n  color: #a0a0b0;\\n  transition: color 0.3s ease;\\n}\\n\\n.subnav-button:hover .subnav-icon,\\n.subnav-button.active .subnav-icon {\\n  color: #64b5f6;\\n}\\n\\n.subnav-label {\\n  font-size: calc(10px * var(--content-scale));\\n  font-weight: 500;\\n  color: #a0a0b0;\\n  text-align: center;\\n  line-height: 1.2;\\n  transition: color 0.3s ease;\\n}\\n\\n.subnav-button:hover .subnav-label,\\n.subnav-button.active .subnav-label {\\n  color: #64b5f6;\\n}\\n\\n/* HUD Glow Effect */\\n.subnav-button::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(circle at center, rgba(100, 181, 246, 0.1) 0%, transparent 70%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n  border-radius: inherit;\\n}\\n\\n.subnav-button:hover::before,\\n.subnav-button.active::before {\\n  opacity: 1;\\n}\\n\\n/* Enhanced HUD Vertical Layout for Left Sidebar */\\n.subnav-hud-vertical {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: stretch;\\n  justify-content: flex-start;\\n  gap: calc(var(--base-spacing) * 0.5);\\n  padding: calc(var(--base-spacing) * 0.75);\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n/* Enhanced subnav button with glassmorphism */\\n.subnav-hud-vertical .subnav-button {\\n  width: 100%;\\n  height: calc(36px * var(--content-scale));\\n  margin: 0;\\n  flex-shrink: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  padding: calc(var(--base-spacing) * 0.75) calc(var(--base-spacing) * 1);\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  border-radius: var(--base-border-radius);\\n  backdrop-filter: blur(20px);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n  box-shadow: var(--glass-shadow);\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover {\\n  background: rgba(0, 207, 255, 0.1);\\n  border-color: var(--hud-border-glow);\\n  transform: translateX(4px);\\n  box-shadow: 0 4px 20px rgba(0, 207, 255, 0.2);\\n}\\n\\n.subnav-hud-vertical .subnav-button.active {\\n  background: linear-gradient(135deg, rgba(0, 207, 255, 0.2) 0%, rgba(0, 255, 189, 0.1) 100%);\\n  border-color: var(--hud-neon-blue);\\n  box-shadow: 0 0 20px rgba(0, 207, 255, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n}\\n\\n/* Enhanced subnav icon */\\n.subnav-hud-vertical .subnav-icon {\\n  width: calc(18px * var(--content-scale));\\n  height: calc(18px * var(--content-scale));\\n  margin-right: calc(var(--base-spacing) * 1.5);\\n  color: var(--hud-text-secondary);\\n  transition: all 0.3s ease;\\n  flex-shrink: 0;\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover .subnav-icon {\\n  color: var(--hud-neon-blue);\\n  filter: drop-shadow(0 0 8px rgba(0, 207, 255, 0.6));\\n}\\n\\n.subnav-hud-vertical .subnav-button.active .subnav-icon {\\n  color: var(--hud-bright-white);\\n  filter: drop-shadow(0 0 12px rgba(0, 207, 255, 0.8));\\n}\\n\\n/* Enhanced subnav label - full text display */\\n.subnav-hud-vertical .subnav-label {\\n  color: var(--hud-text-primary);\\n  font-size: calc(12px * var(--content-scale));\\n  font-weight: 500;\\n  line-height: 1.2;\\n  white-space: nowrap;\\n  transition: all 0.3s ease;\\n  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover .subnav-label {\\n  color: var(--hud-bright-white);\\n  text-shadow: 0 0 8px rgba(0, 207, 255, 0.6);\\n}\\n\\n.subnav-hud-vertical .subnav-button.active .subnav-label {\\n  color: var(--hud-bright-white);\\n  font-weight: 600;\\n  text-shadow: 0 0 12px rgba(0, 207, 255, 0.8);\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[1]!./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[2]!./styles/globals.css\n"));

/***/ }),

/***/ "./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js ***!
  \***************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author Tobias Koppers @sokra\n*/ // css base code, injected by the css-loader\n// eslint-disable-next-line func-names\n\nmodule.exports = function(useSourceMap) {\n    var list = [] // return the list of modules as css string\n    ;\n    list.toString = function toString() {\n        return this.map(function(item) {\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n            var content = cssWithMappingToString(item, useSourceMap);\n            if (item[2]) {\n                return \"@media \".concat(item[2], \" {\").concat(content, \"}\");\n            }\n            return content;\n        }).join(\"\");\n    } // import a list of modules into the list\n    ;\n    // eslint-disable-next-line func-names\n    // @ts-expect-error TODO: fix type\n    list.i = function(modules, mediaQuery, dedupe) {\n        if (typeof modules === \"string\") {\n            // eslint-disable-next-line no-param-reassign\n            modules = [\n                [\n                    null,\n                    modules,\n                    \"\"\n                ]\n            ];\n        }\n        var alreadyImportedModules = {};\n        if (dedupe) {\n            for(var i = 0; i < this.length; i++){\n                // eslint-disable-next-line prefer-destructuring\n                var id = this[i][0];\n                if (id != null) {\n                    alreadyImportedModules[id] = true;\n                }\n            }\n        }\n        for(var _i = 0; _i < modules.length; _i++){\n            var item = [].concat(modules[_i]);\n            if (dedupe && alreadyImportedModules[item[0]]) {\n                continue;\n            }\n            if (mediaQuery) {\n                if (!item[2]) {\n                    item[2] = mediaQuery;\n                } else {\n                    item[2] = \"\".concat(mediaQuery, \" and \").concat(item[2]);\n                }\n            }\n            list.push(item);\n        }\n    };\n    return list;\n};\nfunction cssWithMappingToString(item, useSourceMap) {\n    var content = item[1] || \"\" // eslint-disable-next-line prefer-destructuring\n    ;\n    var cssMapping = item[3];\n    if (!cssMapping) {\n        return content;\n    }\n    if (useSourceMap && typeof btoa === \"function\") {\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        var sourceMapping = toComment(cssMapping);\n        var sourceURLs = cssMapping.sources.map(function(source) {\n            return \"/*# sourceURL=\".concat(cssMapping.sourceRoot || \"\").concat(source, \" */\");\n        });\n        return [\n            content\n        ].concat(sourceURLs).concat([\n            sourceMapping\n        ]).join(\"\\n\");\n    }\n    return [\n        content\n    ].join(\"\\n\");\n} // Adapted from convert-source-map (MIT)\nfunction toComment(sourceMap) {\n    // eslint-disable-next-line no-undef\n    var base64 = btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap))));\n    var data = \"sourceMappingURL=data:application/json;charset=utf-8;base64,\".concat(base64);\n    return \"/*# \".concat(data, \" */\");\n}\n\n//# sourceMappingURL=api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\n"));

/***/ }),

/***/ "./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=private-next-pages%2F_app&page=%2F_app!":
/*!**********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=private-next-pages%2F_app&page=%2F_app! ***!
  \**********************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/_app\",\n      function () {\n        return __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/_app\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjNfcmVhY3QtZG9tQDE4LjIuMF9yZWFjdEAxOC4yLjBfX3JlYWN0QDE4LjIuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1wcml2YXRlLW5leHQtcGFnZXMlMkZfYXBwJnBhZ2U9JTJGX2FwcCEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQyxpREFBeUI7QUFDaEQ7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvPzFlZTkiXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi9fYXBwXCIsXG4gICAgICBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiByZXF1aXJlKFwicHJpdmF0ZS1uZXh0LXBhZ2VzL19hcHBcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL19hcHBcIl0pXG4gICAgICB9KTtcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=private-next-pages%2F_app&page=%2F_app!\n"));

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[1]!../node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[2]!./globals.css */ \"./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[1]!./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[2]!./styles/globals.css\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[1]!../node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[2]!./globals.css */ \"./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[1]!./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[2]!./styles/globals.css\",\n      function () {\n        content = __webpack_require__(/*! !!../node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[1]!../node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[2]!./globals.css */ \"./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[1]!./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[2]!./styles/globals.css\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./styles/globals.css\n"));

/***/ }),

/***/ "./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js":
/*!***************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js ***!
  \***************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nconst isOldIE = function isOldIE() {\n    let memo;\n    return function memorize() {\n        if (typeof memo === \"undefined\") {\n            // Test for IE <= 9 as proposed by Browserhacks\n            // @see http://browserhacks.com/#hack-e71d8692f65334173fee715c222cb805\n            // Tests for existence of standard globals is to allow style-loader\n            // to operate correctly into non-standard environments\n            // @see https://github.com/webpack-contrib/style-loader/issues/177\n            memo = Boolean(window && document && document.all && !window.atob);\n        }\n        return memo;\n    };\n}();\nconst getTargetElement = function() {\n    const memo = {};\n    return function memorize(target) {\n        if (typeof memo[target] === \"undefined\") {\n            let styleTarget = document.querySelector(target);\n            // Special case to return head of iframe instead of iframe itself\n            if (window.HTMLIFrameElement && styleTarget instanceof window.HTMLIFrameElement) {\n                try {\n                    // This will throw an exception if access to iframe is blocked\n                    // due to cross-origin restrictions\n                    styleTarget = styleTarget.contentDocument.head;\n                } catch (e) {\n                    // istanbul ignore next\n                    styleTarget = null;\n                }\n            }\n            memo[target] = styleTarget;\n        }\n        return memo[target];\n    };\n}();\nconst stylesInDom = [];\nfunction getIndexByIdentifier(identifier) {\n    let result = -1;\n    for(let i = 0; i < stylesInDom.length; i++){\n        if (stylesInDom[i].identifier === identifier) {\n            result = i;\n            break;\n        }\n    }\n    return result;\n}\nfunction modulesToDom(list, options) {\n    const idCountMap = {};\n    const identifiers = [];\n    for(let i = 0; i < list.length; i++){\n        const item = list[i];\n        const id = options.base ? item[0] + options.base : item[0];\n        const count = idCountMap[id] || 0;\n        const identifier = id + \" \" + count.toString();\n        idCountMap[id] = count + 1;\n        const index = getIndexByIdentifier(identifier);\n        const obj = {\n            css: item[1],\n            media: item[2],\n            sourceMap: item[3]\n        };\n        if (index !== -1) {\n            stylesInDom[index].references++;\n            stylesInDom[index].updater(obj);\n        } else {\n            stylesInDom.push({\n                identifier: identifier,\n                // eslint-disable-next-line @typescript-eslint/no-use-before-define\n                updater: addStyle(obj, options),\n                references: 1\n            });\n        }\n        identifiers.push(identifier);\n    }\n    return identifiers;\n}\nfunction insertStyleElement(options) {\n    const style = document.createElement(\"style\");\n    const attributes = options.attributes || {};\n    if (typeof attributes.nonce === \"undefined\") {\n        const nonce = // eslint-disable-next-line no-undef\n         true ? __webpack_require__.nc : 0;\n        if (nonce) {\n            attributes.nonce = nonce;\n        }\n    }\n    Object.keys(attributes).forEach(function(key) {\n        style.setAttribute(key, attributes[key]);\n    });\n    if (typeof options.insert === \"function\") {\n        options.insert(style);\n    } else {\n        const target = getTargetElement(options.insert || \"head\");\n        if (!target) {\n            throw new Error(\"Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.\");\n        }\n        target.appendChild(style);\n    }\n    return style;\n}\nfunction removeStyleElement(style) {\n    // istanbul ignore if\n    if (style.parentNode === null) {\n        return false;\n    }\n    style.parentNode.removeChild(style);\n}\n/* istanbul ignore next  */ const replaceText = function replaceText() {\n    const textStore = [];\n    return function replace(index, replacement) {\n        textStore[index] = replacement;\n        return textStore.filter(Boolean).join(\"\\n\");\n    };\n}();\nfunction applyToSingletonTag(style, index, remove, obj) {\n    const css = remove ? \"\" : obj.media ? \"@media \" + obj.media + \" {\" + obj.css + \"}\" : obj.css;\n    // For old IE\n    /* istanbul ignore if  */ if (style.styleSheet) {\n        style.styleSheet.cssText = replaceText(index, css);\n    } else {\n        const cssNode = document.createTextNode(css);\n        const childNodes = style.childNodes;\n        if (childNodes[index]) {\n            style.removeChild(childNodes[index]);\n        }\n        if (childNodes.length) {\n            style.insertBefore(cssNode, childNodes[index]);\n        } else {\n            style.appendChild(cssNode);\n        }\n    }\n}\nfunction applyToTag(style, _options, obj) {\n    let css = obj.css;\n    const media = obj.media;\n    const sourceMap = obj.sourceMap;\n    if (media) {\n        style.setAttribute(\"media\", media);\n    } else {\n        style.removeAttribute(\"media\");\n    }\n    if (sourceMap && typeof btoa !== \"undefined\") {\n        css += \"\\n/*# sourceMappingURL=data:application/json;base64,\" + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + \" */\";\n    }\n    // For old IE\n    /* istanbul ignore if  */ if (style.styleSheet) {\n        style.styleSheet.cssText = css;\n    } else {\n        while(style.firstChild){\n            style.removeChild(style.firstChild);\n        }\n        style.appendChild(document.createTextNode(css));\n    }\n}\nlet singleton = null;\nlet singletonCounter = 0;\nfunction addStyle(obj, options) {\n    let style;\n    let update;\n    let remove;\n    if (options.singleton) {\n        const styleIndex = singletonCounter++;\n        style = singleton || (singleton = insertStyleElement(options));\n        update = applyToSingletonTag.bind(null, style, styleIndex, false);\n        remove = applyToSingletonTag.bind(null, style, styleIndex, true);\n    } else {\n        style = insertStyleElement(options);\n        update = applyToTag.bind(null, style, options);\n        remove = function() {\n            removeStyleElement(style);\n        };\n    }\n    update(obj);\n    return function updateStyle(newObj) {\n        if (newObj) {\n            if (newObj.css === obj.css && newObj.media === obj.media && newObj.sourceMap === obj.sourceMap) {\n                return;\n            }\n            update(obj = newObj);\n        } else {\n            remove();\n        }\n    };\n}\nmodule.exports = function(list, options) {\n    options = options || {};\n    // Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n    // tags it will allow on a page\n    if (!options.singleton && typeof options.singleton !== \"boolean\") {\n        options.singleton = isOldIE();\n    }\n    list = list || [];\n    let lastIdentifiers = modulesToDom(list, options);\n    return function update(newList) {\n        newList = newList || [];\n        if (Object.prototype.toString.call(newList) !== \"[object Array]\") {\n            return;\n        }\n        for(let i = 0; i < lastIdentifiers.length; i++){\n            const identifier = lastIdentifiers[i];\n            const index = getIndexByIdentifier(identifier);\n            stylesInDom[index].references--;\n        }\n        const newLastIdentifiers = modulesToDom(newList, options);\n        for(let i = 0; i < lastIdentifiers.length; i++){\n            const identifier = lastIdentifiers[i];\n            const index = getIndexByIdentifier(identifier);\n            if (stylesInDom[index].references === 0) {\n                stylesInDom[index].updater();\n                stylesInDom.splice(index, 1);\n            }\n        }\n        lastIdentifiers = newLastIdentifiers;\n    };\n};\n\n//# sourceMappingURL=injectStylesIntoStyleTag.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\n"));

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction MyApp(param) {\n    let { Component, pageProps } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\_app.tsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, this);\n}\n_c = MyApp;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MyApp);\nvar _c;\n$RefreshReg$(_c, \"MyApp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBK0I7QUFHL0IsU0FBU0EsTUFBTSxLQUFrQztRQUFsQyxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBWSxHQUFsQztJQUNiLHFCQUFPLDhEQUFDRDtRQUFXLEdBQUdDLFNBQVM7Ozs7OztBQUNqQztLQUZTRjtBQUlULCtEQUFlQSxLQUFLQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3BhZ2VzL19hcHAudHN4PzJmYmUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuLi9zdHlsZXMvZ2xvYmFscy5jc3MnO1xuaW1wb3J0IHR5cGUgeyBBcHBQcm9wcyB9IGZyb20gJ25leHQvYXBwJztcblxuZnVuY3Rpb24gTXlBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wcykge1xuICByZXR1cm4gPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPjtcbn1cblxuZXhwb3J0IGRlZmF1bHQgTXlBcHA7XG4iXSwibmFtZXMiOlsiTXlBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n"));

/***/ }),

/***/ "./node_modules/.pnpm/react@18.2.0/node_modules/react/cjs/react-jsx-dev-runtime.development.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react@18.2.0/node_modules/react/cjs/react-jsx-dev-runtime.development.js ***!
  \*****************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar React = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false; // Track which Fiber(s) schedule render work.\n\nvar REACT_MODULE_REFERENCE;\n\n{\n  REACT_MODULE_REFERENCE = Symbol.for('react.module.reference');\n}\n\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_MODULE_REFERENCE || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n} // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  {\n    if (typeof type.tag === 'number') {\n      error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n    }\n  }\n\n  if (typeof type === 'function') {\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n        return getContextName(context) + '.Consumer';\n\n      case REACT_PROVIDER_TYPE:\n        var provider = type;\n        return getContextName(provider._context) + '.Provider';\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n\n      // eslint-disable-next-line no-fallthrough\n    }\n  }\n\n  return null;\n}\n\nvar assign = Object.assign;\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, source, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if ( !fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  var control;\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n\n  try {\n    // This should throw.\n    if (construct) {\n      // Something should be setting the props in the constructor.\n      var Fake = function () {\n        throw Error();\n      }; // $FlowFixMe\n\n\n      Object.defineProperty(Fake.prototype, 'props', {\n        set: function () {\n          // We use a throwing setter instead of frozen or non-writable props\n          // because that won't throw in a non-strict mode function.\n          throw Error();\n        }\n      });\n\n      if (typeof Reflect === 'object' && Reflect.construct) {\n        // We construct a different control for this case to include any extra\n        // frames added by the construct call.\n        try {\n          Reflect.construct(Fake, []);\n        } catch (x) {\n          control = x;\n        }\n\n        Reflect.construct(fn, [], Fake);\n      } else {\n        try {\n          Fake.call();\n        } catch (x) {\n          control = x;\n        }\n\n        fn.call(Fake.prototype);\n      }\n    } else {\n      try {\n        throw Error();\n      } catch (x) {\n        control = x;\n      }\n\n      fn();\n    }\n  } catch (sample) {\n    // This is inlined manually because closure doesn't do it for us.\n    if (sample && control && typeof sample.stack === 'string') {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sample.stack.split('\\n');\n      var controlLines = control.stack.split('\\n');\n      var s = sampleLines.length - 1;\n      var c = controlLines.length - 1;\n\n      while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n        // We expect at least one stack frame to be shared.\n        // Typically this will be the root most one. However, stack frames may be\n        // cut off due to maximum stack limits. In this case, one maybe cut off\n        // earlier than the other. We assume that the sample is longer or the same\n        // and there for cut off earlier. So we should find the root most frame in\n        // the sample somewhere in the control.\n        c--;\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, source, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar loggedTypeFailures = {};\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction checkPropTypes(typeSpecs, values, location, componentName, element) {\n  {\n    // $FlowFixMe This is okay but Flow doesn't know it.\n    var has = Function.call.bind(hasOwnProperty);\n\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            // eslint-disable-next-line react-internal/prod-error-codes\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n\n          error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n        } catch (ex) {\n          error$1 = ex;\n        }\n\n        if (error$1 && !(error$1 instanceof Error)) {\n          setCurrentlyValidatingElement(element);\n\n          error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n\n          setCurrentlyValidatingElement(null);\n        }\n\n        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error$1.message] = true;\n          setCurrentlyValidatingElement(element);\n\n          error('Failed %s type: %s', location, error$1.message);\n\n          setCurrentlyValidatingElement(null);\n        }\n      }\n    }\n  }\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n/*\n * The `'' + value` pattern (used in in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object';\n    return type;\n  }\n} // $FlowFixMe only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar RESERVED_PROPS = {\n  key: true,\n  ref: true,\n  __self: true,\n  __source: true\n};\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingRef = function () {\n      if (!specialPropRefWarningShown) {\n        specialPropRefWarningShown = true;\n\n        error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingRef.isReactWarning = true;\n    Object.defineProperty(props, 'ref', {\n      get: warnAboutAccessingRef,\n      configurable: true\n    });\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nvar ReactElement = function (type, key, ref, self, source, owner, props) {\n  var element = {\n    // This tag allows us to uniquely identify this as a React Element\n    $$typeof: REACT_ELEMENT_TYPE,\n    // Built-in properties that belong on the element\n    type: type,\n    key: key,\n    ref: ref,\n    props: props,\n    // Record the component responsible for creating this element.\n    _owner: owner\n  };\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // self and source are DEV only properties.\n\n    Object.defineProperty(element, '_self', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: self\n    }); // Two elements created in two different places should be considered\n    // equal for testing purposes and therefore we hide it from enumeration.\n\n    Object.defineProperty(element, '_source', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: source\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n};\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV(type, config, maybeKey, source, self) {\n  {\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      ref = config.ref;\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    return ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n  }\n}\n\nvar ReactCurrentOwner$1 = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement$1(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n    }\n  }\n}\n\nvar propTypesMisspellWarningShown;\n\n{\n  propTypesMisspellWarningShown = false;\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  {\n    return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner$1.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner$1.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n\nfunction getSourceInfoErrorAddendum(source) {\n  {\n    if (source !== undefined) {\n      var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n      var lineNumber = source.lineNumber;\n      return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n    }\n\n    return '';\n  }\n}\n/**\n * Warn if there's no key explicitly set on dynamic arrays of children or\n * object keys are not valid. This allows us to keep track of children between\n * updates.\n */\n\n\nvar ownerHasKeyUseWarning = {};\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner$1.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement$1(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement$1(null);\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object') {\n      return;\n    }\n\n    if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else if (node) {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Given an element, validate that its props follow the propTypes definition,\n * provided by the type.\n *\n * @param {ReactElement} element\n */\n\n\nfunction validatePropTypes(element) {\n  {\n    var type = element.type;\n\n    if (type === null || type === undefined || typeof type === 'string') {\n      return;\n    }\n\n    var propTypes;\n\n    if (typeof type === 'function') {\n      propTypes = type.propTypes;\n    } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n    // Inner props are checked in the reconciler.\n    type.$$typeof === REACT_MEMO_TYPE)) {\n      propTypes = type.propTypes;\n    } else {\n      return;\n    }\n\n    if (propTypes) {\n      // Intentionally inside to avoid triggering lazy initializers:\n      var name = getComponentNameFromType(type);\n      checkPropTypes(propTypes, element.props, 'prop', name, element);\n    } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n      propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n      var _name = getComponentNameFromType(type);\n\n      error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n    }\n\n    if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n      error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n    }\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement$1(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement$1(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement$1(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement$1(null);\n    }\n  }\n}\n\nfunction jsxWithValidation(type, props, key, isStaticChildren, source, self) {\n  {\n    var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n    // succeed and there will likely be errors in render.\n\n    if (!validType) {\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var sourceInfo = getSourceInfoErrorAddendum(source);\n\n      if (sourceInfo) {\n        info += sourceInfo;\n      } else {\n        info += getDeclarationErrorAddendum();\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    }\n\n    var element = jsxDEV(type, props, key, source, self); // The result can be nullish if a mock or a custom function is used.\n    // TODO: Drop this when these are no longer allowed as the type argument.\n\n    if (element == null) {\n      return element;\n    } // Skip key warning if the type isn't valid since our key validation logic\n    // doesn't expect a non-string/function type and can throw confusing errors.\n    // We don't want exception behavior to differ between dev and prod.\n    // (Rendering will throw with a helpful message and as soon as the type is\n    // fixed, the key warnings will appear.)\n\n\n    if (validType) {\n      var children = props.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    }\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    } else {\n      validatePropTypes(element);\n    }\n\n    return element;\n  }\n} // These two functions exist to still get child warnings in dev\n\nvar jsxDEV$1 =  jsxWithValidation ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV$1;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/.pnpm/react@18.2.0/node_modules/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js ***!
  \*******************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3RAMTguMi4wL25vZGVfbW9kdWxlcy9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsSUFBSSxLQUFxQyxFQUFFLEVBRTFDLENBQUM7QUFDRixFQUFFLHVMQUFzRTtBQUN4RSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3RAMTguMi4wL25vZGVfbW9kdWxlcy9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanM/ZGJhMSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24ubWluLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main"], function() { return __webpack_exec__("./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=private-next-pages%2F_app&page=%2F_app!"), __webpack_exec__("./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/client/router.js"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);
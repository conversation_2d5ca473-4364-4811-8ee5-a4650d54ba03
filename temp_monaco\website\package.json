{"name": "my-application", "version": "0.0.1", "scripts": {"dev": "webpack serve --hot --mode development", "build-webpack": "webpack --mode production", "build": "yarn typedoc && yarn build-webpack", "dev-disk": "webpack --mode development --watch", "typedoc": "typedoc --options ./typedoc/typedoc.json", "test": "yarn ts-node scripts/check-playground-samples-js.ts"}, "dependencies": {"@popperjs/core": "^2.11.5", "@types/base64-js": "^1.3.0", "@types/bootstrap": "^5.2.0", "@types/node": "^18.6.1", "base64-js": "^1.5.1", "bootstrap": "^5.2.0", "bootstrap-icons": "^1.9.1", "classnames": "^2.2.6", "html-inline-css-webpack-plugin": "^1.11.1", "lzma": "^2.3.2", "messagepack": "^1.1.12", "mini-css-extract-plugin": "^2.6.1", "mobx": "^5.15.4", "mobx-react": "^6.2.2", "monaco-editor": "^0.42.0-dev-20230906", "react": "^17.0.2", "react-bootstrap": "^2.4.0", "react-dom": "^17.0.2", "typedoc": "^0.25.12", "@vscode/web-editors": "./vscode-web-editors.tgz"}, "devDependencies": {"@types/classnames": "^2.3.1", "@types/glob": "^8.1.0", "@types/html-webpack-plugin": "^3.2.2", "@types/react": "^17.0.3", "@types/react-dom": "^17.0.3", "@types/webpack": "^4.41.10", "clean-webpack-plugin": "^3.0.0", "copy-webpack-plugin": "^11.0.0", "css-loader": "^3.5.1", "file-loader": "^6.0.0", "glob": "^9.2.1", "html-webpack-plugin": "^5.5.0", "raw-loader": "^4.0.2", "sass": "^1.32.8", "sass-loader": "^11.0.1", "script-loader": "^0.7.2", "style-loader": "^1.1.3", "ts-loader": "^9.3.1", "ts-node": "^10.9.1", "typescript": "^5.4.5", "webpack": "^5.90.1", "webpack-bundle-analyzer": "^4.5.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.9.3"}}
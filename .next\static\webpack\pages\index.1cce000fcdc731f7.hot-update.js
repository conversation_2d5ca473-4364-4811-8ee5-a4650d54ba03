"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/views/Gamification/GamificationView.tsx":
/*!*****************************************************!*\
  !*** ./src/views/Gamification/GamificationView.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GamificationView: function() { return /* binding */ GamificationView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\n// Educational game categories with real Perseus widgets\nconst gameCategories = {\n    math: {\n        name: \"Mathematics\",\n        icon: \"\\uD83D\\uDD22\",\n        games: [\n            {\n                id: \"numeric-input\",\n                name: \"Number Practice\",\n                description: \"Practice number input and calculations\"\n            },\n            {\n                id: \"expression\",\n                name: \"Math Expressions\",\n                description: \"Work with mathematical expressions\"\n            },\n            {\n                id: \"grapher\",\n                name: \"Graph Explorer\",\n                description: \"Interactive graphing exercises\"\n            },\n            {\n                id: \"matrix\",\n                name: \"Matrix Operations\",\n                description: \"Learn matrix mathematics\"\n            }\n        ]\n    },\n    science: {\n        name: \"Science\",\n        icon: \"\\uD83D\\uDD2C\",\n        games: [\n            {\n                id: \"molecule\",\n                name: \"Molecule Builder\",\n                description: \"Build and explore molecular structures\"\n            },\n            {\n                id: \"phet-simulation\",\n                name: \"Physics Simulations\",\n                description: \"Interactive physics experiments\"\n            },\n            {\n                id: \"interactive-graph\",\n                name: \"Data Analysis\",\n                description: \"Analyze scientific data with graphs\"\n            }\n        ]\n    },\n    language: {\n        name: \"Language Arts\",\n        icon: \"\\uD83D\\uDCDA\",\n        games: [\n            {\n                id: \"passage\",\n                name: \"Reading Comprehension\",\n                description: \"Practice reading and understanding texts\"\n            },\n            {\n                id: \"categorizer\",\n                name: \"Word Categorizer\",\n                description: \"Categorize words and concepts\"\n            },\n            {\n                id: \"matcher\",\n                name: \"Word Matching\",\n                description: \"Match words with their meanings\"\n            }\n        ]\n    },\n    logic: {\n        name: \"Logic & Reasoning\",\n        icon: \"\\uD83E\\uDDE9\",\n        games: [\n            {\n                id: \"orderer\",\n                name: \"Sequence Ordering\",\n                description: \"Put items in logical order\"\n            },\n            {\n                id: \"sorter\",\n                name: \"Logic Sorter\",\n                description: \"Sort items by logical rules\"\n            },\n            {\n                id: \"radio\",\n                name: \"Multiple Choice Logic\",\n                description: \"Logical reasoning questions\"\n            }\n        ]\n    },\n    programming: {\n        name: \"Programming\",\n        icon: \"\\uD83D\\uDCBB\",\n        games: [\n            {\n                id: \"cs-program\",\n                name: \"Computer Science\",\n                description: \"Learn programming concepts\"\n            },\n            {\n                id: \"python-program\",\n                name: \"Python Programming\",\n                description: \"Interactive Python coding\"\n            }\n        ]\n    }\n};\nconst GamificationView = ()=>{\n    var _gameCategories_selectedCategory;\n    _s();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"math\");\n    const [activeGame, setActiveGame] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleGameClick = (gameId)=>{\n        setActiveGame(gameId);\n    };\n    if (activeGame) {\n        // Find the game details\n        let gameDetails = null;\n        let categoryName = \"\";\n        for (const [catKey, category] of Object.entries(gameCategories)){\n            const game = category.games.find((g)=>g.id === activeGame);\n            if (game) {\n                gameDetails = game;\n                categoryName = category.name;\n                break;\n            }\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full bg-panel-bg rounded-xl p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-white text-2xl font-bold\",\n                                    children: gameDetails === null || gameDetails === void 0 ? void 0 : gameDetails.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: categoryName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveGame(null),\n                            className: \"bg-gray-800/60 hover:bg-gray-700/60 text-white px-4 py-2 rounded-lg transition-colors duration-200\",\n                            children: \"← Back to Games\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full bg-gray-900/50 rounded-lg p-6 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83C\\uDFAE\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-white text-xl mb-2\",\n                                children: gameDetails === null || gameDetails === void 0 ? void 0 : gameDetails.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-4\",\n                                children: gameDetails === null || gameDetails === void 0 ? void 0 : gameDetails.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-500/20 border border-blue-400/30 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-400\",\n                                        children: [\n                                            \"Perseus Widget: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                className: \"bg-gray-800 px-2 py-1 rounded\",\n                                                children: activeGame\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 text-sm mt-2\",\n                                        children: \"This will render the actual Khan Academy Perseus widget for interactive learning\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-panel-bg rounded-xl p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-white text-3xl font-bold mb-2\",\n                        children: \"\\uD83C\\uDFAE Educational Games\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Interactive learning games powered by Khan Academy Perseus\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2 mb-6 overflow-x-auto\",\n                children: Object.entries(gameCategories).map((param)=>{\n                    let [key, category] = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setSelectedCategory(key),\n                        className: \"flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 whitespace-nowrap \".concat(selectedCategory === key ? \"bg-blue-500/20 text-blue-400 border border-blue-400/30\" : \"bg-gray-800/40 text-gray-300 hover:bg-gray-700/60 hover:text-white\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: category.icon\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: category.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, key, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8\",\n                children: (_gameCategories_selectedCategory = gameCategories[selectedCategory]) === null || _gameCategories_selectedCategory === void 0 ? void 0 : _gameCategories_selectedCategory.games.map((game)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onClick: ()=>handleGameClick(game.id),\n                        className: \"bg-gray-800/40 border border-gray-700/50 rounded-xl p-4 hover:bg-gray-700/60 transition-all duration-300 cursor-pointer hover:scale-105 hover:border-blue-400/30\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl\",\n                                        children: \"\\uD83C\\uDFAF\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-white font-semibold\",\n                                                children: game.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: game.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-400 text-xs font-medium bg-blue-500/20 px-2 py-1 rounded\",\n                                        children: game.id\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-500 text-xs\",\n                                        children: \"Perseus Widget\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, game.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/40 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-green-400\",\n                                children: \"24\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"Games Available\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/40 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-blue-400\",\n                                children: \"5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"Categories\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/40 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-purple-400\",\n                                children: \"Perseus\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"Powered by\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/40 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-yellow-400\",\n                                children: \"Khan\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"Academy\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Gamification\\\\GamificationView.tsx\",\n        lineNumber: 112,\n        columnNumber: 5\n    }, undefined);\n};\n_s(GamificationView, \"yl+kFN6qiYBJhANaYdRXZ3u4o8o=\");\n_c = GamificationView;\nvar _c;\n$RefreshReg$(_c, \"GamificationView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/Gamification/GamificationView.tsx\n"));

/***/ })

});
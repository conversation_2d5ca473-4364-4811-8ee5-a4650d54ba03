"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_views_Setting_SettingView_tsx"],{

/***/ "./src/components/Card.tsx":
/*!*********************************!*\
  !*** ./src/components/Card.tsx ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: function() { return /* binding */ Card; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Card = (param)=>{\n    let { title, children, className = \"\", headerIcon, titleClassName = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-panel-bg border border-gray-700/50 rounded-xl p-4 flex flex-col h-full \".concat(className),\n        children: [\n            title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xs uppercase text-[#A0A0B0] font-semibold tracking-wider \".concat(titleClassName),\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Card.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 11\n                    }, undefined),\n                    headerIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-[#A0A0B0]\",\n                        children: headerIcon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Card.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 26\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Card.tsx\",\n                lineNumber: 15,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-grow flex flex-col\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Card.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Card.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Card;\nvar _c;\n$RefreshReg$(_c, \"Card\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9DYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFVbkIsTUFBTUMsT0FBNEI7UUFBQyxFQUFFQyxLQUFLLEVBQUVDLFFBQVEsRUFBRUMsWUFBWSxFQUFFLEVBQUVDLFVBQVUsRUFBRUMsaUJBQWlCLEVBQUUsRUFBRTtJQUM1RyxxQkFDRSw4REFBQ0M7UUFBSUgsV0FBVyw2RUFBdUYsT0FBVkE7O1lBQzFGRix1QkFDQyw4REFBQ0s7Z0JBQUlILFdBQVU7O2tDQUNiLDhEQUFDSTt3QkFBR0osV0FBVyxpRUFBZ0YsT0FBZkU7a0NBQW1CSjs7Ozs7O29CQUNsR0csNEJBQWMsOERBQUNFO3dCQUFJSCxXQUFVO2tDQUFrQkM7Ozs7Ozs7Ozs7OzswQkFHcEQsOERBQUNFO2dCQUFJSCxXQUFVOzBCQUNaRDs7Ozs7Ozs7Ozs7O0FBSVQsRUFBRTtLQWRXRiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9DYXJkLnRzeD9iNWU2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbmludGVyZmFjZSBDYXJkUHJvcHMge1xuICB0aXRsZT86IHN0cmluZztcbiAgaGVhZGVySWNvbj86IFJlYWN0LlJlYWN0Tm9kZTtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xuICB0aXRsZUNsYXNzTmFtZT86IHN0cmluZztcbn1cblxuZXhwb3J0IGNvbnN0IENhcmQ6IFJlYWN0LkZDPENhcmRQcm9wcz4gPSAoeyB0aXRsZSwgY2hpbGRyZW4sIGNsYXNzTmFtZSA9ICcnLCBoZWFkZXJJY29uLCB0aXRsZUNsYXNzTmFtZSA9ICcnIH0pID0+IHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YGJnLXBhbmVsLWJnIGJvcmRlciBib3JkZXItZ3JheS03MDAvNTAgcm91bmRlZC14bCBwLTQgZmxleCBmbGV4LWNvbCBoLWZ1bGwgJHtjbGFzc05hbWV9YH0+XG4gICAgICB7dGl0bGUgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBtYi0zXCI+XG4gICAgICAgICAgPGgzIGNsYXNzTmFtZT17YHRleHQteHMgdXBwZXJjYXNlIHRleHQtWyNBMEEwQjBdIGZvbnQtc2VtaWJvbGQgdHJhY2tpbmctd2lkZXIgJHt0aXRsZUNsYXNzTmFtZX1gfT57dGl0bGV9PC9oMz5cbiAgICAgICAgICB7aGVhZGVySWNvbiAmJiA8ZGl2IGNsYXNzTmFtZT1cInRleHQtWyNBMEEwQjBdXCI+e2hlYWRlckljb259PC9kaXY+fVxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtZ3JvdyBmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufTsiXSwibmFtZXMiOlsiUmVhY3QiLCJDYXJkIiwidGl0bGUiLCJjaGlsZHJlbiIsImNsYXNzTmFtZSIsImhlYWRlckljb24iLCJ0aXRsZUNsYXNzTmFtZSIsImRpdiIsImgzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/Card.tsx\n"));

/***/ }),

/***/ "./src/views/Setting/ProfileSettings.tsx":
/*!***********************************************!*\
  !*** ./src/views/Setting/ProfileSettings.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProfileSettings: function() { return /* binding */ ProfileSettings; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/Card */ \"./src/components/Card.tsx\");\n\nvar _s = $RefreshSig$();\n\n\nconst ProfileSettings = (param)=>{\n    let { initialProfile, onProfileUpdate } = param;\n    _s();\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialProfile);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleChange = (e)=>{\n        setProfile({\n            ...profile,\n            [e.target.id]: e.target.value\n        });\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setSubmitting(true);\n        setMessage(\"\");\n        try {\n            const response = await fetch(\"/api/settings\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    profile\n                })\n            });\n            if (!response.ok) throw new Error(\"Failed to save changes\");\n            setMessage(\"Changes saved successfully!\");\n            onProfileUpdate(); // Notify parent\n            setTimeout(()=>setMessage(\"\"), 3000);\n        } catch (error) {\n            console.error(error);\n            setMessage(\"Failed to save. Please try again.\");\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        title: \"Profile Information\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"username\",\n                            className: \"text-xs text-gray-400\",\n                            children: \"Username\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            id: \"username\",\n                            value: profile.username,\n                            onChange: handleChange,\n                            className: \"w-full mt-1 p-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"email\",\n                            className: \"text-xs text-gray-400\",\n                            children: \"Email Address\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"email\",\n                            id: \"email\",\n                            value: profile.email,\n                            onChange: handleChange,\n                            className: \"w-full mt-1 p-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"avatar\",\n                            className: \"text-xs text-gray-400\",\n                            children: \"Avatar URL\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            id: \"avatar\",\n                            value: profile.avatar,\n                            onChange: handleChange,\n                            className: \"w-full mt-1 p-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 13\n                }, undefined),\n                message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-center text-sm text-cyan-300 pt-2\",\n                    children: message\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 25\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    type: \"submit\",\n                    disabled: submitting,\n                    className: \"w-full !mt-6 py-2 text-sm font-semibold text-white bg-cyan-500/30 border border-cyan-500/50 rounded-lg hover:bg-cyan-500/50 transition-colors disabled:opacity-50 disabled:cursor-wait\",\n                    children: submitting ? \"SAVING...\" : \"Save Changes\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n            lineNumber: 51,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\ProfileSettings.tsx\",\n        lineNumber: 50,\n        columnNumber: 9\n    }, undefined);\n};\n_s(ProfileSettings, \"ADUuAj5UHsoVcUsaZ6sNOuhQlJc=\");\n_c = ProfileSettings;\nvar _c;\n$RefreshReg$(_c, \"ProfileSettings\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/Setting/ProfileSettings.tsx\n"));

/***/ }),

/***/ "./src/views/Setting/SettingView.tsx":
/*!*******************************************!*\
  !*** ./src/views/Setting/SettingView.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SettingView: function() { return /* binding */ SettingView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/Card */ \"./src/components/Card.tsx\");\n/* harmony import */ var _ProfileSettings__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ProfileSettings */ \"./src/views/Setting/ProfileSettings.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst Toggle = (param)=>{\n    let { label, enabled, onChange } = param;\n    const uniqueId = label.replace(/\\s+/g, \"-\").toLowerCase();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-between items-center bg-gray-700/50 p-3 rounded-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                id: \"\".concat(uniqueId, \"-label\"),\n                className: \"text-white text-sm\",\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                lineNumber: 26,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onChange,\n                role: \"switch\",\n                \"aria-checked\": enabled,\n                \"aria-labelledby\": \"\".concat(uniqueId, \"-label\"),\n                className: \"relative w-12 h-6 rounded-full flex items-center p-1 transition-colors duration-300 focus:outline-none focus-visible:ring-2 focus-visible:ring-brand-cyan focus-visible:ring-offset-2 focus-visible:ring-offset-gray-800 \".concat(enabled ? \"bg-cyan-500\" : \"bg-gray-600\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"inline-block w-5 h-5 bg-white rounded-full shadow-md transform transition-transform duration-300 ease-in-out \".concat(enabled ? \"translate-x-5\" : \"translate-x-0\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                lineNumber: 27,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n        lineNumber: 25,\n        columnNumber: 9\n    }, undefined);\n};\n_c = Toggle;\nconst SettingView = ()=>{\n    _s();\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchSettings = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/settings\");\n            const data = await response.json();\n            setSettings(data);\n        } catch (error) {\n            console.error(\"Failed to fetch settings\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchSettings();\n    }, []);\n    const handleToggleChange = async (category, key)=>{\n        if (!settings) return;\n        const newCategoryState = {\n            ...settings[category],\n            [key]: !settings[category][key]\n        };\n        const newSettings = {\n            ...settings,\n            [category]: newCategoryState\n        };\n        setSettings(newSettings); // Optimistic update\n        try {\n            await fetch(\"/api/settings\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    [category]: newCategoryState\n                })\n            });\n        } catch (error) {\n            console.error(\"Failed to update settings\", error);\n            fetchSettings(); // Revert on error\n        }\n    };\n    if (loading || !settings) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-full text-cyan-400\",\n            children: \"Loading Settings...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n            lineNumber: 81,\n            columnNumber: 12\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-grow grid grid-cols-1 md:grid-cols-2 gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProfileSettings__WEBPACK_IMPORTED_MODULE_3__.ProfileSettings, {\n                initialProfile: settings.profile,\n                onProfileUpdate: fetchSettings\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        title: \"Notifications\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Toggle, {\n                                    label: \"Email Alerts\",\n                                    enabled: settings.notifications.emailAlerts,\n                                    onChange: ()=>handleToggleChange(\"notifications\", \"emailAlerts\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Toggle, {\n                                    label: \"Push Notifications\",\n                                    enabled: settings.notifications.pushNotifications,\n                                    onChange: ()=>handleToggleChange(\"notifications\", \"pushNotifications\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Toggle, {\n                                    label: \"System Updates\",\n                                    enabled: settings.notifications.systemUpdates,\n                                    onChange: ()=>handleToggleChange(\"notifications\", \"systemUpdates\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        title: \"Security\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Toggle, {\n                                    label: \"Two-Factor Auth\",\n                                    enabled: settings.security.twoFactorAuth,\n                                    onChange: ()=>handleToggleChange(\"security\", \"twoFactorAuth\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 18\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Toggle, {\n                                    label: \"Biometric Lock\",\n                                    enabled: settings.security.biometricLock,\n                                    onChange: ()=>handleToggleChange(\"security\", \"biometricLock\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 18\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 10\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Setting\\\\SettingView.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SettingView, \"NbmPcGL1GhgnRY0gyVUleq8hn/s=\");\n_c1 = SettingView;\nvar _c, _c1;\n$RefreshReg$(_c, \"Toggle\");\n$RefreshReg$(_c1, \"SettingView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/Setting/SettingView.tsx\n"));

/***/ })

}]);
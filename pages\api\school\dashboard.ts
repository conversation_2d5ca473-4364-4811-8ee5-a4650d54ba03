import { NextApiRequest, NextApiResponse } from 'next';

// In-memory data store for now (will be replaced with real database later)
const schoolData = {
  overview: {
    totalStudents: 2847,
    totalTeachers: 156,
    virtualRealityClasses: 23,
    aiTutorSessions: 847
  },
  innovations: [
    { title: 'AI-Powered Personalized Learning', icon: '🤖', status: 'Active', impact: '84% improvement in learning outcomes' },
    { title: 'Holographic Classrooms', icon: '🎭', status: 'Beta', impact: '87% engagement increase' },
    { title: 'Blockchain Credentials', icon: '⛓️', status: 'Live', impact: '100% fraud prevention' },
    { title: 'Emotion Recognition System', icon: '😊', status: 'Testing', impact: '92% accuracy in mood detection' },
    { title: 'Quantum Computing Lab', icon: '⚛️', status: 'Active', impact: '340% faster problem solving' },
    { title: 'Neural Interface Learning', icon: '🧠', status: 'Beta', impact: 'Direct knowledge transfer' }
  ],
  departments: [
    { name: 'AI & Robotics', color: 'bg-blue-500/20 text-blue-400', students: 412, teachers: 18, innovation: 'Neural Networks Lab' },
    { name: 'Quantum Computing', color: 'bg-green-500/20 text-green-400', students: 298, teachers: 16, innovation: 'Quantum Simulators' },
    { name: 'Bioengineering', color: 'bg-purple-500/20 text-purple-400', students: 267, teachers: 12, innovation: 'Gene Editing Lab' },
    { name: 'Space Sciences', color: 'bg-pink-500/20 text-pink-400', students: 189, teachers: 11, innovation: 'Mars Simulation' },
    { name: 'Metaverse Studies', color: 'bg-cyan-500/20 text-cyan-400', students: 345, teachers: 14, innovation: 'Virtual Reality Campus' },
    { name: 'Environmental Tech', color: 'bg-emerald-500/20 text-emerald-400', students: 223, teachers: 9, innovation: 'Carbon Neutral Systems' }
  ],
  recentEvents: [
    { title: 'Global VR Science Fair', date: 'March 25', type: 'Virtual Reality Meeting', participants: 50000 },
    { title: 'AI Ethics Symposium', date: 'March 30', type: 'Family Workshop', participants: 25000 },
    { title: 'Quantum Computing Workshop', date: 'April 5', type: 'Student Presentation', participants: 15000 },
    { title: 'Holographic Teacher Training', date: 'April 10', type: 'Professional Development', participants: 5000 },
    { title: 'Mars Colony Simulation', date: 'April 15', type: 'Immersive Experience', participants: 8000 }
  ]
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Simulate some processing time
    await new Promise(resolve => setTimeout(resolve, 100));

    res.status(200).json(schoolData);
  } catch (error) {
    console.error('API error:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}

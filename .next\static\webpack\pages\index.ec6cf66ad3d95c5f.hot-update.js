"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/views/Coder/CoderView.tsx":
/*!***************************************!*\
  !*** ./src/views/Coder/CoderView.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CoderView: function() { return /* binding */ CoderView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst FileTreeNode = (param)=>{\n    let { item, level, onFileClick } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(item.isOpen || false);\n    const handleClick = ()=>{\n        if (item.type === \"folder\") {\n            setIsOpen(!isOpen);\n        } else {\n            onFileClick(item.name);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2 px-2 py-1 hover:bg-gray-700/50 cursor-pointer text-sm\",\n                style: {\n                    paddingLeft: \"\".concat(level * 16 + 8, \"px\")\n                },\n                onClick: handleClick,\n                children: [\n                    item.type === \"folder\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-400\",\n                        children: isOpen ? \"\\uD83D\\uDCC2\" : \"\\uD83D\\uDCC1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, undefined),\n                    item.type === \"file\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-blue-400\",\n                        children: \"\\uD83D\\uDCC4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-300\",\n                        children: item.name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined),\n            item.type === \"folder\" && isOpen && item.children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: item.children.map((child, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileTreeNode, {\n                        item: child,\n                        level: level + 1,\n                        onFileClick: onFileClick\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 44,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FileTreeNode, \"1NLt9oXF2DSJYlKhLhMlvItPqek=\");\n_c = FileTreeNode;\nconst CoderView = ()=>{\n    _s1();\n    const [activeFile, setActiveFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"index.tsx\");\n    const [openFiles, setOpenFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"index.tsx\",\n        \"styles.css\"\n    ]);\n    const [showTerminal, setShowTerminal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sidebarTab, setSidebarTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"files\");\n    const fileTree = [\n        {\n            name: \"src\",\n            type: \"folder\",\n            isOpen: true,\n            children: [\n                {\n                    name: \"components\",\n                    type: \"folder\",\n                    children: [\n                        {\n                            name: \"Header.tsx\",\n                            type: \"file\"\n                        },\n                        {\n                            name: \"Sidebar.tsx\",\n                            type: \"file\"\n                        },\n                        {\n                            name: \"Footer.tsx\",\n                            type: \"file\"\n                        }\n                    ]\n                },\n                {\n                    name: \"views\",\n                    type: \"folder\",\n                    children: [\n                        {\n                            name: \"Dashboard.tsx\",\n                            type: \"file\"\n                        },\n                        {\n                            name: \"Settings.tsx\",\n                            type: \"file\"\n                        }\n                    ]\n                },\n                {\n                    name: \"index.tsx\",\n                    type: \"file\"\n                },\n                {\n                    name: \"App.tsx\",\n                    type: \"file\"\n                }\n            ]\n        },\n        {\n            name: \"public\",\n            type: \"folder\",\n            children: [\n                {\n                    name: \"favicon.ico\",\n                    type: \"file\"\n                },\n                {\n                    name: \"index.html\",\n                    type: \"file\"\n                }\n            ]\n        },\n        {\n            name: \"package.json\",\n            type: \"file\"\n        },\n        {\n            name: \"README.md\",\n            type: \"file\"\n        },\n        {\n            name: \"styles.css\",\n            type: \"file\"\n        }\n    ];\n    const sampleCode = {\n        \"index.tsx\": \"import React from 'react';\\nimport ReactDOM from 'react-dom/client';\\nimport App from './App';\\nimport './styles.css';\\n\\nconst root = ReactDOM.createRoot(\\n  document.getElementById('root') as HTMLElement\\n);\\n\\nroot.render(\\n  <React.StrictMode>\\n    <App />\\n  </React.StrictMode>\\n);\",\n        \"styles.css\": \"/* Global Styles */\\nbody {\\n  margin: 0;\\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto';\\n  background-color: #1a1a1a;\\n  color: #ffffff;\\n}\\n\\n.container {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 20px;\\n}\",\n        \"App.tsx\": \"import React from 'react';\\n\\nfunction App() {\\n  return (\\n    <div className=\\\"container\\\">\\n      <h1>Welcome to the IDE</h1>\\n      <p>Start coding your next project!</p>\\n    </div>\\n  );\\n}\\n\\nexport default App;\"\n    };\n    const handleFileClick = (fileName)=>{\n        setActiveFile(fileName);\n        if (!openFiles.includes(fileName)) {\n            setOpenFiles([\n                ...openFiles,\n                fileName\n            ]);\n        }\n    };\n    const closeFile = (fileName)=>{\n        const newOpenFiles = openFiles.filter((f)=>f !== fileName);\n        setOpenFiles(newOpenFiles);\n        if (activeFile === fileName && newOpenFiles.length > 0) {\n            setActiveFile(newOpenFiles[newOpenFiles.length - 1]);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-panel-bg rounded-xl overflow-hidden flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-64 bg-gray-900/50 border-r border-gray-700/50 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex border-b border-gray-700/50\",\n                        children: [\n                            {\n                                id: \"files\",\n                                icon: \"\\uD83D\\uDCC1\",\n                                label: \"Files\"\n                            },\n                            {\n                                id: \"git\",\n                                icon: \"\\uD83D\\uDD00\",\n                                label: \"Git\"\n                            },\n                            {\n                                id: \"extensions\",\n                                icon: \"\\uD83E\\uDDE9\",\n                                label: \"Extensions\"\n                            }\n                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSidebarTab(tab.id),\n                                className: \"flex-1 p-3 text-sm font-medium transition-colors duration-200 \".concat(sidebarTab === tab.id ? \"bg-blue-500/20 text-blue-400 border-b-2 border-blue-400\" : \"text-gray-400 hover:text-white hover:bg-gray-800/60\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-2\",\n                                        children: tab.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    tab.label\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: [\n                            sidebarTab === \"files\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-gray-400 text-xs uppercase font-semibold mb-2 px-2\",\n                                        children: \"Explorer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    fileTree.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileTreeNode, {\n                                            item: item,\n                                            level: 0,\n                                            onFileClick: handleFileClick\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, undefined),\n                            sidebarTab === \"git\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-white font-semibold mb-4\",\n                                        children: \"Source Control\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-green-400 text-sm\",\n                                                children: \"✓ 3 files staged\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-yellow-400 text-sm\",\n                                                children: \"⚠ 2 files modified\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-red-400 text-sm\",\n                                                children: \"✗ 1 file deleted\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, undefined),\n                            sidebarTab === \"extensions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-white font-semibold mb-4\",\n                                        children: \"Extensions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-blue-500 rounded flex items-center justify-center text-xs\",\n                                                        children: \"TS\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white text-sm\",\n                                                                children: \"TypeScript\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                                lineNumber: 222,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-400 text-xs\",\n                                                                children: \"Installed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                                lineNumber: 223,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-green-500 rounded flex items-center justify-center text-xs\",\n                                                        children: \"ES\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white text-sm\",\n                                                                children: \"ESLint\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-400 text-xs\",\n                                                                children: \"Installed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-800/40 border-b border-gray-700/50\",\n                        children: openFiles.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 px-4 py-2 border-r border-gray-700/50 cursor-pointer \".concat(activeFile === file ? \"bg-panel-bg text-white\" : \"text-gray-400 hover:text-white hover:bg-gray-800/60\"),\n                                onClick: ()=>setActiveFile(file),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: file\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            closeFile(file);\n                                        },\n                                        className: \"text-gray-500 hover:text-white\",\n                                        children: \"\\xd7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, file, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gray-900/30 p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"text-gray-300 text-sm font-mono leading-relaxed\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    children: sampleCode[activeFile] || \"// File content will be loaded here\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, undefined),\n                    showTerminal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-48 bg-black/80 border-t border-gray-700/50 p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-semibold\",\n                                        children: \"Terminal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowTerminal(false),\n                                        className: \"text-gray-400 hover:text-white\",\n                                        children: \"\\xd7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-green-400 font-mono text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: \"$ npm start\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: \"Starting development server...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: \"Server running on http://localhost:3000\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"$ \"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-transparent border-none outline-none text-green-400 ml-1\",\n                                                children: \"|\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-80 bg-gray-900/50 border-l border-gray-700/50 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-white font-semibold mb-4\",\n                        children: \"\\uD83E\\uDD16 AI Assistant\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800/40 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-400 text-sm font-medium mb-1\",\n                                        children: \"Code Suggestion\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Consider adding error handling to your React component.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800/40 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-green-400 text-sm font-medium mb-1\",\n                                        children: \"Performance Tip\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Use React.memo() to optimize component re-renders.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowTerminal(!showTerminal),\n                            className: \"w-full bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 py-2 px-4 rounded-lg transition-colors duration-200\",\n                            children: showTerminal ? \"Hide Terminal\" : \"Show Terminal\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 302,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n        lineNumber: 163,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CoderView, \"1kPL0zpUX1PfGchY7IJ8nZYT3cM=\");\n_c1 = CoderView;\nvar _c, _c1;\n$RefreshReg$(_c, \"FileTreeNode\");\n$RefreshReg$(_c1, \"CoderView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/Coder/CoderView.tsx\n"));

/***/ })

});
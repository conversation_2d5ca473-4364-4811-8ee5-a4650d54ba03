"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/ResponsiveWrapper.tsx":
/*!**********************************************!*\
  !*** ./src/components/ResponsiveWrapper.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResponsiveWrapper: function() { return /* binding */ ResponsiveWrapper; },\n/* harmony export */   useResponsiveCSS: function() { return /* binding */ useResponsiveCSS; },\n/* harmony export */   useResponsiveValue: function() { return /* binding */ useResponsiveValue; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\nconst ResponsiveWrapper = (param)=>{\n    let { children, className = \"\", targetWidth = 1920, targetHeight = 1080, minScale = 0.5, maxScale = 1.0, enableAutoScale = true } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [scale, setScale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!enableAutoScale) return;\n        const calculateScale = ()=>{\n            if (!containerRef.current) return;\n            const viewportWidth = window.innerWidth;\n            const viewportHeight = window.innerHeight;\n            // Calculate scale factors for both dimensions\n            const scaleX = viewportWidth / targetWidth;\n            const scaleY = viewportHeight / targetHeight;\n            // Use the smaller scale to ensure content fits in both dimensions\n            const calculatedScale = Math.min(scaleX, scaleY);\n            // Clamp the scale within the specified bounds\n            const finalScale = Math.max(minScale, Math.min(maxScale, calculatedScale));\n            setScale(finalScale);\n            // Apply the scale to the container to fill viewport completely\n            if (containerRef.current) {\n                containerRef.current.style.transform = \"scale(\".concat(finalScale, \")\");\n                containerRef.current.style.transformOrigin = \"top left\";\n                containerRef.current.style.width = \"\".concat(viewportWidth / finalScale, \"px\");\n                containerRef.current.style.height = \"\".concat(viewportHeight / finalScale, \"px\");\n                containerRef.current.style.left = \"0\";\n                containerRef.current.style.top = \"0\";\n                containerRef.current.style.position = \"absolute\";\n            }\n        };\n        // Calculate initial scale\n        calculateScale();\n        // Recalculate on window resize\n        const handleResize = ()=>{\n            calculateScale();\n        };\n        window.addEventListener(\"resize\", handleResize);\n        // Cleanup\n        return ()=>{\n            window.removeEventListener(\"resize\", handleResize);\n        };\n    }, [\n        targetWidth,\n        targetHeight,\n        minScale,\n        maxScale,\n        enableAutoScale\n    ]);\n    const containerStyle = {\n        position: \"relative\",\n        width: enableAutoScale ? \"\".concat(targetWidth, \"px\") : \"100%\",\n        height: enableAutoScale ? \"\".concat(targetHeight, \"px\") : \"100%\",\n        transformOrigin: \"top left\",\n        transition: \"transform 0.3s ease-out\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"responsive-container \".concat(className),\n        style: containerStyle,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\ResponsiveWrapper.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ResponsiveWrapper, \"Kq0ecimAaoiQT7LBlV3z4oI1F3M=\");\n_c = ResponsiveWrapper;\n// Hook for responsive values\nconst useResponsiveValue = function(baseValue) {\n    let unit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"px\";\n    _s1();\n    const [responsiveValue, setResponsiveValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\".concat(baseValue).concat(unit));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const updateValue = ()=>{\n            const root = document.documentElement;\n            const scaleFactor = parseFloat(getComputedStyle(root).getPropertyValue(\"--scale-factor\")) || 1;\n            const scaledValue = baseValue * scaleFactor;\n            setResponsiveValue(\"\".concat(scaledValue).concat(unit));\n        };\n        updateValue();\n        window.addEventListener(\"resize\", updateValue);\n        return ()=>{\n            window.removeEventListener(\"resize\", updateValue);\n        };\n    }, [\n        baseValue,\n        unit\n    ]);\n    return responsiveValue;\n};\n_s1(useResponsiveValue, \"HUu9XsWyMJPiV9dbbawl8gV5+Zs=\");\n// Hook for responsive CSS variables\nconst useResponsiveCSS = ()=>{\n    _s2();\n    const [cssVars, setCssVars] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        fontSize: \"var(--base-font-size)\",\n        spacing: \"var(--base-spacing)\",\n        borderRadius: \"var(--base-border-radius)\",\n        iconSize: \"var(--base-icon-size)\",\n        buttonHeight: \"var(--base-button-height)\",\n        cardPadding: \"var(--base-card-padding)\",\n        gap: \"var(--base-gap)\"\n    });\n    return cssVars;\n};\n_s2(useResponsiveCSS, \"uw0v8C85FiaegJ3CMhW/HXPInUo=\");\nvar _c;\n$RefreshReg$(_c, \"ResponsiveWrapper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ResponsiveWrapper.tsx\n"));

/***/ })

});
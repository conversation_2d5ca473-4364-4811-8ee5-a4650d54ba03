import React, { useState, useEffect } from 'react';
import { MainMetricChart } from './components/MainMetricChart';
import { MoodAlerts } from './components/MoodAlerts';
import { AullOrgesChart } from './components/AullOrgesChart';
import { PastPrelesssGauge } from './components/PastPrelesssGauge';
import { WellnessTracker } from './components/WellnessTracker';
import { SafetyTents } from './components/SafetyTents';
import { EcafeAcces } from './components/EcafeAcces';
import { WellnessRequests } from './components/WellnessRequests';
import { FamilyDashboardCTA } from './components/FamilyDashboardCTA';
import { Card } from '../../components/Card';

// Data shapes from the new API
type Contact = { name: string; detail: string; avatar: string; };
type SafetyItem = { label: string; color: string; };
type SystemRequest = { text: string; };
type SystemStat = { icon: string; value: string; label: string; };
type WidgetsData = {
  contacts: Contact[];
  safetyItems: SafetyItem[];
  safetyChartData: number[];
  systemRequests: SystemRequest[];
  systemStats: SystemStat[];
};

const aullOrgesChartData = [
  { name: 'CPU', value: 400 }, { name: 'GPU', value: 300 },
  { name: 'RAM', value: 600 }, { name: 'IO', value: 280 },
  { name: 'NET', value: 450 },
];
const aullOrgesChartColors = ['#00FFFF', '#00EFFF', '#00DFFF', '#00CFFF', '#00BFFF'];

const wellnessTrackerData = [
  { name: 'A', uv: 400, pv: 240, amt: 240, line: 300 }, { name: 'B', uv: 300, pv: 139, amt: 221, line: 450 },
  { name: 'C', uv: 200, pv: 980, amt: 229, line: 200 }, { name: 'D', uv: 278, pv: 390, amt: 200, line: 500 },
  { name: 'E', uv: 189, pv: 480, amt: 218, line: 150 }, { name: 'F', uv: 239, pv: 380, amt: 250, line: 350 },
  { name: 'G', uv: 349, pv: 430, amt: 210, line: 420 },
];

const WidgetLoader: React.FC<{title: string}> = ({title}) => (
    <Card title={title}>
        <div className="flex items-center justify-center h-full text-gray-400 text-sm animate-pulse">
            Loading data...
        </div>
    </Card>
)

export const DashboardView: React.FC = () => {
    const [widgetsData, setWidgetsData] = useState<WidgetsData | null>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);
                const response = await fetch('/api/dashboard-widgets');
                const data = await response.json();
                if (response.ok) {
                    setWidgetsData(data);
                } else {
                    throw new Error(data.error || 'Failed to fetch widget data');
                }
            } catch (error) {
                console.error(error);
            } finally {
                setLoading(false);
            }
        };
        fetchData();
    }, []);

  return (
    <div className="flex-grow grid grid-cols-2 lg:grid-cols-4 gap-4 h-full">
      {/* Row 1 on LG, Rows 1 & 2 on mobile */}
      <MainMetricChart />
      <MoodAlerts />
      <AullOrgesChart data={aullOrgesChartData} colors={aullOrgesChartColors} />
      <PastPrelesssGauge />
      
      {/* Row 2 on LG, Row 3 on mobile */}
      <div className="col-span-2 lg:col-span-2">
        <WellnessTracker data={wellnessTrackerData} />
      </div>

      {loading ? <WidgetLoader title="Safety Status" /> : <SafetyTents items={widgetsData?.safetyItems ?? []} chartData={widgetsData?.safetyChartData ?? []} />}
      {loading ? <WidgetLoader title="Direct Access" /> : <EcafeAcces contacts={widgetsData?.contacts ?? []} />}

      {/* Row 3 on LG, Row 4 on mobile */}
      <div className="col-span-2 lg:col-span-2">
        {loading ? <WidgetLoader title="System Status & Requests" /> : <WellnessRequests requests={widgetsData?.systemRequests ?? []} stats={widgetsData?.systemStats ?? []} />}
      </div>
      <div className="col-span-2 lg:col-span-2">
        <FamilyDashboardCTA />
      </div>
    </div>
  );
};

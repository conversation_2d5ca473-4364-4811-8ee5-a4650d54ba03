import React, { useState } from 'react';
import Editor from '@monaco-editor/react';

interface FileTreeItem {
  name: string;
  type: 'file' | 'folder';
  children?: FileTreeItem[];
  isOpen?: boolean;
}

const FileTreeNode: React.FC<{ item: FileTreeItem; level: number; onFileClick: (name: string) => void }> = ({ 
  item, 
  level, 
  onFileClick 
}) => {
  const [isOpen, setIsOpen] = useState(item.isOpen || false);

  const handleClick = () => {
    if (item.type === 'folder') {
      setIsOpen(!isOpen);
    } else {
      onFileClick(item.name);
    }
  };

  return (
    <div>
      <div
        className="flex items-center gap-2 px-2 py-1 hover:bg-gray-700/50 cursor-pointer text-sm"
        style={{ paddingLeft: `${level * 16 + 8}px` }}
        onClick={handleClick}
      >
        {item.type === 'folder' && (
          <span className="text-gray-400">
            {isOpen ? '📂' : '📁'}
          </span>
        )}
        {item.type === 'file' && (
          <span className="text-blue-400">📄</span>
        )}
        <span className="text-gray-300">{item.name}</span>
      </div>
      {item.type === 'folder' && isOpen && item.children && (
        <div>
          {item.children.map((child, index) => (
            <FileTreeNode
              key={index}
              item={child}
              level={level + 1}
              onFileClick={onFileClick}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export const CoderView: React.FC = () => {
  const [activeFile, setActiveFile] = useState<string>('App.tsx');
  const [openFiles, setOpenFiles] = useState<string[]>(['App.tsx']);
  const [showTerminal, setShowTerminal] = useState(true);
  const [sidebarTab, setSidebarTab] = useState<'files' | 'git' | 'extensions'>('files');
  const [files, setFiles] = useState<Record<string, string>>({
    'App.tsx': `import React, { useState } from 'react';
import './App.css';

function App() {
  const [count, setCount] = useState(0);

  return (
    <div className="App">
      <header className="App-header">
        <h1>Welcome to React IDE</h1>
        <p>Count: {count}</p>
        <button onClick={() => setCount(count + 1)}>
          Increment
        </button>
        <button onClick={() => setCount(count - 1)}>
          Decrement
        </button>
      </header>
    </div>
  );
}

export default App;`,
    'App.css': `body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.App {
  text-align: center;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
}

button {
  background-color: #61dafb;
  border: none;
  padding: 10px 20px;
  margin: 10px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
}

button:hover {
  background-color: #21a1c4;
}`,
    'package.json': `{
  "name": "react-ide-project",
  "version": "1.0.0",
  "private": true,
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "typescript": "^4.9.5"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  },
  "eslintConfig": {
    "extends": [
      "react-app",
      "react-app/jest"
    ]
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  }
}`,
    'README.md': `# React IDE Project

This is a sample React project created in the IDE.

## Available Scripts

- \`npm start\` - Runs the app in development mode
- \`npm build\` - Builds the app for production
- \`npm test\` - Launches the test runner

## Features

- React with TypeScript
- Modern CSS styling
- Interactive components
- Hot reloading

## Getting Started

1. Install dependencies: \`npm install\`
2. Start the development server: \`npm start\`
3. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.
`
  });
  const editorRef = useRef<any>(null);

  const fileTree: FileTreeItem[] = [
    {
      name: 'src',
      type: 'folder',
      isOpen: true,
      children: [
        { name: 'App.tsx', type: 'file' },
        { name: 'App.css', type: 'file' },
        { name: 'index.tsx', type: 'file' }
      ]
    },
    {
      name: 'public',
      type: 'folder',
      children: [
        { name: 'index.html', type: 'file' },
        { name: 'favicon.ico', type: 'file' }
      ]
    },
    { name: 'package.json', type: 'file' },
    { name: 'README.md', type: 'file' }
  ];

  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = editor;

    // Configure Monaco Editor
    monaco.editor.defineTheme('vs-dark-custom', {
      base: 'vs-dark',
      inherit: true,
      rules: [],
      colors: {
        'editor.background': '#1e1e1e',
        'editor.foreground': '#d4d4d4',
        'editorLineNumber.foreground': '#858585',
        'editor.selectionBackground': '#264f78',
        'editor.inactiveSelectionBackground': '#3a3d41'
      }
    });

    monaco.editor.setTheme('vs-dark-custom');
  };

  const handleEditorChange = (value: string | undefined) => {
    if (value !== undefined && activeFile) {
      setFiles(prev => ({
        ...prev,
        [activeFile]: value
      }));
    }
  };

  const handleFileClick = (fileName: string) => {
    // Only open files that exist in our files object
    if (files[fileName]) {
      setActiveFile(fileName);
      if (!openFiles.includes(fileName)) {
        setOpenFiles([...openFiles, fileName]);
      }
    }
  };

  const closeFile = (fileName: string) => {
    const newOpenFiles = openFiles.filter(f => f !== fileName);
    setOpenFiles(newOpenFiles);
    if (activeFile === fileName && newOpenFiles.length > 0) {
      setActiveFile(newOpenFiles[newOpenFiles.length - 1]);
    } else if (newOpenFiles.length === 0) {
      setActiveFile('');
    }
  };

  const getFileLanguage = (fileName: string) => {
    const ext = fileName.split('.').pop();
    switch (ext) {
      case 'tsx':
      case 'ts':
        return 'typescript';
      case 'jsx':
      case 'js':
        return 'javascript';
      case 'css':
        return 'css';
      case 'json':
        return 'json';
      case 'md':
        return 'markdown';
      case 'html':
        return 'html';
      default:
        return 'plaintext';
    }
  };

  return (
    <div className="h-full bg-panel-bg rounded-xl overflow-hidden flex">
      {/* Left Sidebar */}
      <div className="w-64 bg-gray-900/50 border-r border-gray-700/50 flex flex-col">
        {/* Sidebar Tabs */}
        <div className="flex border-b border-gray-700/50">
          {[
            { id: 'files', icon: '📁', label: 'Files' },
            { id: 'git', icon: '🔀', label: 'Git' },
            { id: 'extensions', icon: '🧩', label: 'Extensions' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setSidebarTab(tab.id as any)}
              className={`flex-1 p-3 text-sm font-medium transition-colors duration-200 ${
                sidebarTab === tab.id
                  ? 'bg-blue-500/20 text-blue-400 border-b-2 border-blue-400'
                  : 'text-gray-400 hover:text-white hover:bg-gray-800/60'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </div>

        {/* Sidebar Content */}
        <div className="flex-1 overflow-y-auto">
          {sidebarTab === 'files' && (
            <div className="p-2">
              <h3 className="text-gray-400 text-xs uppercase font-semibold mb-2 px-2">Explorer</h3>
              {fileTree.map((item, index) => (
                <FileTreeNode
                  key={index}
                  item={item}
                  level={0}
                  onFileClick={handleFileClick}
                />
              ))}
            </div>
          )}
          
          {sidebarTab === 'git' && (
            <div className="p-4">
              <h3 className="text-white font-semibold mb-4">Source Control</h3>
              <div className="space-y-2">
                <div className="text-green-400 text-sm">✓ 3 files staged</div>
                <div className="text-yellow-400 text-sm">⚠ 2 files modified</div>
                <div className="text-red-400 text-sm">✗ 1 file deleted</div>
              </div>
            </div>
          )}
          
          {sidebarTab === 'extensions' && (
            <div className="p-4">
              <h3 className="text-white font-semibold mb-4">Extensions</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-blue-500 rounded flex items-center justify-center text-xs">TS</div>
                  <div>
                    <div className="text-white text-sm">TypeScript</div>
                    <div className="text-gray-400 text-xs">Installed</div>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-green-500 rounded flex items-center justify-center text-xs">ES</div>
                  <div>
                    <div className="text-white text-sm">ESLint</div>
                    <div className="text-gray-400 text-xs">Installed</div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Main Editor Area */}
      <div className="flex-1 flex flex-col">
        {/* File Tabs */}
        <div className="flex bg-gray-800/40 border-b border-gray-700/50">
          {openFiles.map((file) => (
            <div
              key={file}
              className={`flex items-center gap-2 px-4 py-2 border-r border-gray-700/50 cursor-pointer ${
                activeFile === file
                  ? 'bg-panel-bg text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-800/60'
              }`}
              onClick={() => setActiveFile(file)}
            >
              <span>{file}</span>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  closeFile(file);
                }}
                className="text-gray-500 hover:text-white"
              >
                ×
              </button>
            </div>
          ))}
        </div>

        {/* Code Editor */}
        <div className="flex-1 relative">
          {activeFile ? (
            <Editor
              height="100%"
              language={getFileLanguage(activeFile)}
              value={files[activeFile] || ''}
              onChange={handleEditorChange}
              theme="vs-dark"
              options={{
                fontSize: 14,
                fontFamily: 'Fira Code, Monaco, Consolas, monospace',
                minimap: { enabled: true },
                scrollBeyondLastLine: false,
                automaticLayout: true,
                tabSize: 2,
                insertSpaces: true,
                wordWrap: 'on',
                lineNumbers: 'on',
                renderWhitespace: 'selection',
              }}
            />
          ) : (
            <div className="flex items-center justify-center h-full bg-gray-900/30">
              <div className="text-center">
                <div className="text-6xl mb-4">💻</div>
                <h3 className="text-white text-xl mb-2">Welcome to the IDE</h3>
                <p className="text-gray-400">Select a file from the explorer to start coding</p>
              </div>
            </div>
          )}
        </div>

        {/* Terminal */}
        {showTerminal && (
          <div className="h-48 bg-black border-t border-gray-700/50 flex flex-col">
            <div className="flex items-center justify-between px-4 py-2 bg-gray-800/60 border-b border-gray-700/50">
              <div className="flex items-center gap-2">
                <span className="text-white font-semibold text-sm">Terminal</span>
                <span className="text-gray-400 text-xs">bash</span>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setShowTerminal(false)}
                  className="text-gray-400 hover:text-white text-sm px-2 py-1 rounded hover:bg-gray-700/50"
                >
                  ×
                </button>
              </div>
            </div>
            <div className="flex-1 p-4 overflow-y-auto">
              <div className="text-green-400 font-mono text-sm space-y-1">
                <div className="text-gray-400">Welcome to React IDE Terminal</div>
                <div>$ npm install</div>
                <div className="text-gray-300">✓ Dependencies installed successfully</div>
                <div>$ npm start</div>
                <div className="text-gray-300">Starting development server...</div>
                <div className="text-blue-400">Local:    http://localhost:3000</div>
                <div className="text-blue-400">Network:  http://*************:3000</div>
                <div className="text-gray-300">webpack compiled successfully</div>
                <div className="flex items-center mt-2">
                  <span className="text-green-400">$ </span>
                  <span className="bg-transparent border-none outline-none text-green-400 ml-1 animate-pulse">|</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Right Sidebar - AI Assistant */}
      <div className="w-80 bg-gray-900/50 border-l border-gray-700/50 p-4">
        <h3 className="text-white font-semibold mb-4">🤖 AI Assistant</h3>
        <div className="space-y-4">
          <div className="bg-gray-800/40 rounded-lg p-3">
            <div className="text-blue-400 text-sm font-medium mb-1">Code Suggestion</div>
            <div className="text-gray-300 text-sm">
              Consider adding error handling to your React component.
            </div>
          </div>
          <div className="bg-gray-800/40 rounded-lg p-3">
            <div className="text-green-400 text-sm font-medium mb-1">Performance Tip</div>
            <div className="text-gray-300 text-sm">
              Use React.memo() to optimize component re-renders.
            </div>
          </div>
        </div>
        
        <div className="mt-6">
          <button
            onClick={() => setShowTerminal(!showTerminal)}
            className="w-full bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 py-2 px-4 rounded-lg transition-colors duration-200"
          >
            {showTerminal ? 'Hide Terminal' : 'Show Terminal'}
          </button>
        </div>
      </div>
    </div>
  );
};

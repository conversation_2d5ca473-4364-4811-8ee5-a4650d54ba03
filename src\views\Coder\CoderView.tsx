import React, { useState, useRef } from 'react';
import Editor from '@monaco-editor/react';
import TheiaIDE from '../../components/TheiaIDE';

interface FileTreeItem {
  name: string;
  type: 'file' | 'folder';
  children?: FileTreeItem[];
  isOpen?: boolean;
}

const FileTreeNode: React.FC<{ item: FileTreeItem; level: number; onFileClick: (name: string) => void }> = ({ 
  item, 
  level, 
  onFileClick 
}) => {
  const [isOpen, setIsOpen] = useState(item.isOpen || false);

  const handleClick = () => {
    if (item.type === 'folder') {
      setIsOpen(!isOpen);
    } else {
      onFileClick(item.name);
    }
  };

  return (
    <div>
      <div
        className="flex items-center gap-2 px-2 py-1 hover:bg-gray-700/50 cursor-pointer text-sm"
        style={{ paddingLeft: `${level * 16 + 8}px` }}
        onClick={handleClick}
      >
        {item.type === 'folder' && (
          <span className="text-gray-400">
            {isOpen ? '📂' : '📁'}
          </span>
        )}
        {item.type === 'file' && (
          <span className="text-blue-400">📄</span>
        )}
        <span className="text-gray-300">{item.name}</span>
      </div>
      {item.type === 'folder' && isOpen && item.children && (
        <div>
          {item.children.map((child, index) => (
            <FileTreeNode
              key={index}
              item={child}
              level={level + 1}
              onFileClick={onFileClick}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export const CoderView: React.FC = () => {
  const [activeFile, setActiveFile] = useState<string>('App.tsx');
  const [openFiles, setOpenFiles] = useState<string[]>(['App.tsx']);
  const [showTerminal, setShowTerminal] = useState(true);
  const [sidebarTab, setSidebarTab] = useState<'files' | 'git' | 'extensions'>('files');
  const [showNewFileDialog, setShowNewFileDialog] = useState(false);
  const [showNewProjectDialog, setShowNewProjectDialog] = useState(false);
  const [newFileName, setNewFileName] = useState('');
  const [newProjectName, setNewProjectName] = useState('');
  const [terminals, setTerminals] = useState<Array<{id: string, name: string, output: string[]}>>([
    {id: 'main', name: 'Terminal 1', output: ['Welcome to React IDE Terminal', '$ npm install', '✓ Dependencies installed successfully', '$ npm start', 'Starting development server...', 'Local: http://localhost:3000', 'webpack compiled successfully', '$ ']}
  ]);
  const [activeTerminal, setActiveTerminal] = useState('main');
  const [terminalInput, setTerminalInput] = useState('');
  const [useTheiaIDE, setUseTheiaIDE] = useState(false);
  const [files, setFiles] = useState<Record<string, string>>({
    'App.tsx': `import React, { useState } from 'react';
import './App.css';

function App() {
  const [count, setCount] = useState(0);

  return (
    <div className="App">
      <header className="App-header">
        <h1>Welcome to React IDE</h1>
        <p>Count: {count}</p>
        <button onClick={() => setCount(count + 1)}>
          Increment
        </button>
        <button onClick={() => setCount(count - 1)}>
          Decrement
        </button>
      </header>
    </div>
  );
}

export default App;`,
    'App.css': `body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.App {
  text-align: center;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
}

button {
  background-color: #61dafb;
  border: none;
  padding: 10px 20px;
  margin: 10px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
}

button:hover {
  background-color: #21a1c4;
}`,
    'package.json': `{
  "name": "react-ide-project",
  "version": "1.0.0",
  "private": true,
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "typescript": "^4.9.5"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  },
  "eslintConfig": {
    "extends": [
      "react-app",
      "react-app/jest"
    ]
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  }
}`,
    'README.md': `# React IDE Project

This is a sample React project created in the IDE.

## Available Scripts

- \`npm start\` - Runs the app in development mode
- \`npm build\` - Builds the app for production
- \`npm test\` - Launches the test runner

## Features

- React with TypeScript
- Modern CSS styling
- Interactive components
- Hot reloading

## Getting Started

1. Install dependencies: \`npm install\`
2. Start the development server: \`npm start\`
3. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.
`
  });
  const editorRef = useRef<any>(null);

  const fileTree: FileTreeItem[] = [
    {
      name: 'src',
      type: 'folder',
      isOpen: true,
      children: [
        { name: 'App.tsx', type: 'file' },
        { name: 'App.css', type: 'file' },
        { name: 'index.tsx', type: 'file' }
      ]
    },
    {
      name: 'public',
      type: 'folder',
      children: [
        { name: 'index.html', type: 'file' },
        { name: 'favicon.ico', type: 'file' }
      ]
    },
    { name: 'package.json', type: 'file' },
    { name: 'README.md', type: 'file' }
  ];

  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = editor;

    // Configure Monaco Editor
    monaco.editor.defineTheme('vs-dark-custom', {
      base: 'vs-dark',
      inherit: true,
      rules: [],
      colors: {
        'editor.background': '#1e1e1e',
        'editor.foreground': '#d4d4d4',
        'editorLineNumber.foreground': '#858585',
        'editor.selectionBackground': '#264f78',
        'editor.inactiveSelectionBackground': '#3a3d41'
      }
    });

    monaco.editor.setTheme('vs-dark-custom');
  };

  const handleEditorChange = (value: string | undefined) => {
    if (value !== undefined && activeFile) {
      setFiles(prev => ({
        ...prev,
        [activeFile]: value
      }));
    }
  };

  const handleFileClick = (fileName: string) => {
    // Only open files that exist in our files object
    if (files[fileName]) {
      setActiveFile(fileName);
      if (!openFiles.includes(fileName)) {
        setOpenFiles([...openFiles, fileName]);
      }
    }
  };

  const closeFile = (fileName: string) => {
    const newOpenFiles = openFiles.filter(f => f !== fileName);
    setOpenFiles(newOpenFiles);
    if (activeFile === fileName && newOpenFiles.length > 0) {
      setActiveFile(newOpenFiles[newOpenFiles.length - 1]);
    } else if (newOpenFiles.length === 0) {
      setActiveFile('');
    }
  };

  const getFileLanguage = (fileName: string) => {
    const ext = fileName.split('.').pop();
    switch (ext) {
      case 'tsx':
      case 'ts':
        return 'typescript';
      case 'jsx':
      case 'js':
        return 'javascript';
      case 'css':
        return 'css';
      case 'json':
        return 'json';
      case 'md':
        return 'markdown';
      case 'html':
        return 'html';
      default:
        return 'plaintext';
    }
  };

  // File Management Functions
  const createNewFile = () => {
    if (newFileName.trim()) {
      const language = getFileLanguage(newFileName);
      const template = getFileTemplate(language, newFileName);

      setFiles(prev => ({
        ...prev,
        [newFileName]: template
      }));

      setActiveFile(newFileName);
      if (!openFiles.includes(newFileName)) {
        setOpenFiles([...openFiles, newFileName]);
      }

      setNewFileName('');
      setShowNewFileDialog(false);
    }
  };

  const getFileTemplate = (language: string, fileName: string) => {
    switch (language) {
      case 'typescript':
        if (fileName.endsWith('.tsx')) {
          return `import React from 'react';

interface ${fileName.replace('.tsx', '')}Props {
  // Define your props here
}

const ${fileName.replace('.tsx', '')}: React.FC<${fileName.replace('.tsx', '')}Props> = () => {
  return (
    <div>
      <h1>Hello from ${fileName.replace('.tsx', '')}</h1>
    </div>
  );
};

export default ${fileName.replace('.tsx', '')};`;
        }
        return `// ${fileName}
export interface Example {
  id: number;
  name: string;
}

export const exampleFunction = (): Example => {
  return {
    id: 1,
    name: 'Example'
  };
};`;
      case 'javascript':
        return `// ${fileName}
function exampleFunction() {
  console.log('Hello from ${fileName}');
}

export default exampleFunction;`;
      case 'css':
        return `/* ${fileName} */
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.title {
  font-size: 2rem;
  color: white;
  margin-bottom: 1rem;
}`;
      case 'html':
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${fileName.replace('.html', '')}</title>
</head>
<body>
    <h1>Hello World</h1>
    <p>Welcome to ${fileName}</p>
</body>
</html>`;
      case 'json':
        return `{
  "name": "${fileName.replace('.json', '')}",
  "version": "1.0.0",
  "description": "Generated JSON file"
}`;
      case 'markdown':
        return `# ${fileName.replace('.md', '')}

This is a new markdown file.

## Features

- Feature 1
- Feature 2
- Feature 3

## Usage

\`\`\`javascript
console.log('Hello World');
\`\`\``;
      default:
        return `// ${fileName}
// Start coding here...`;
    }
  };

  // Project Management Functions
  const createNewProject = () => {
    if (newProjectName.trim()) {
      const projectFiles = {
        [`${newProjectName}/src/App.tsx`]: `import React from 'react';
import './App.css';

function App() {
  return (
    <div className="App">
      <header className="App-header">
        <h1>Welcome to ${newProjectName}</h1>
        <p>Your new React project is ready!</p>
      </header>
    </div>
  );
}

export default App;`,
        [`${newProjectName}/src/App.css`]: `.App {
  text-align: center;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
}`,
        [`${newProjectName}/package.json`]: `{
  "name": "${newProjectName.toLowerCase()}",
  "version": "0.1.0",
  "private": true,
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "typescript": "^4.9.5"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  }
}`,
        [`${newProjectName}/README.md`]: `# ${newProjectName}

This project was created with React IDE.

## Available Scripts

- \`npm start\` - Runs the app in development mode
- \`npm build\` - Builds the app for production
- \`npm test\` - Launches the test runner

## Getting Started

1. Install dependencies: \`npm install\`
2. Start the development server: \`npm start\`
3. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.
`
      };

      setFiles(prev => ({ ...prev, ...projectFiles }));
      setActiveFile(`${newProjectName}/src/App.tsx`);
      setOpenFiles([`${newProjectName}/src/App.tsx`]);
      setNewProjectName('');
      setShowNewProjectDialog(false);
    }
  };

  // Terminal Management Functions
  const createNewTerminal = () => {
    const newTerminalId = `terminal-${Date.now()}`;
    const newTerminal = {
      id: newTerminalId,
      name: `Terminal ${terminals.length + 1}`,
      output: [`Welcome to Terminal ${terminals.length + 1}`, '$ ']
    };

    setTerminals([...terminals, newTerminal]);
    setActiveTerminal(newTerminalId);
  };

  const executeCommand = (command: string) => {
    const currentTerminal = terminals.find(t => t.id === activeTerminal);
    if (!currentTerminal) return;

    const updatedTerminals = terminals.map(terminal => {
      if (terminal.id === activeTerminal) {
        const newOutput = [...terminal.output];
        newOutput[newOutput.length - 1] = `$ ${command}`;

        // Simulate command execution
        const response = simulateCommand(command);
        newOutput.push(...response);
        newOutput.push('$ ');

        return { ...terminal, output: newOutput };
      }
      return terminal;
    });

    setTerminals(updatedTerminals);
    setTerminalInput('');
  };

  const simulateCommand = (command: string): string[] => {
    const cmd = command.toLowerCase().trim();

    if (cmd === 'ls' || cmd === 'dir') {
      return ['App.tsx', 'App.css', 'package.json', 'README.md'];
    } else if (cmd === 'pwd') {
      return ['/workspace/my-project'];
    } else if (cmd.startsWith('npm install')) {
      return ['Installing dependencies...', '✓ Dependencies installed successfully'];
    } else if (cmd === 'npm start') {
      return ['Starting development server...', 'Local: http://localhost:3000', 'webpack compiled successfully'];
    } else if (cmd === 'npm build') {
      return ['Building for production...', '✓ Build completed successfully'];
    } else if (cmd === 'git status') {
      return ['On branch main', 'Your branch is up to date with origin/main', 'nothing to commit, working tree clean'];
    } else if (cmd.startsWith('git add')) {
      return ['Files staged for commit'];
    } else if (cmd.startsWith('git commit')) {
      return ['[main abc123] Your commit message', '2 files changed, 10 insertions(+), 5 deletions(-)'];
    } else if (cmd === 'clear' || cmd === 'cls') {
      return [''];
    } else if (cmd === 'help') {
      return [
        'Available commands:',
        '  ls/dir     - List files',
        '  pwd        - Show current directory',
        '  npm install - Install dependencies',
        '  npm start  - Start development server',
        '  npm build  - Build for production',
        '  git status - Show git status',
        '  git add    - Stage files',
        '  git commit - Commit changes',
        '  clear/cls  - Clear terminal',
        '  help       - Show this help'
      ];
    } else if (cmd === '') {
      return [];
    } else {
      return [`Command not found: ${command}`, 'Type "help" for available commands'];
    }
  };

  return (
    <div className="h-full bg-panel-bg rounded-xl overflow-hidden flex">
      {/* Left Sidebar */}
      <div className="w-64 bg-gray-900/50 border-r border-gray-700/50 flex flex-col">
        {/* Sidebar Tabs */}
        <div className="flex border-b border-gray-700/50">
          {[
            { id: 'files', icon: '📁', label: 'Files' },
            { id: 'git', icon: '🔀', label: 'Git' },
            { id: 'extensions', icon: '🧩', label: 'Extensions' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setSidebarTab(tab.id as any)}
              className={`flex-1 p-3 text-sm font-medium transition-colors duration-200 ${
                sidebarTab === tab.id
                  ? 'bg-blue-500/20 text-blue-400 border-b-2 border-blue-400'
                  : 'text-gray-400 hover:text-white hover:bg-gray-800/60'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </div>

        {/* Sidebar Content */}
        <div className="flex-1 overflow-y-auto">
          {sidebarTab === 'files' && (
            <div>
              {/* File Management Toolbar */}
              <div className="flex items-center gap-2 p-2 border-b border-gray-700/50 bg-gray-800/30">
                <button
                  onClick={() => setShowNewFileDialog(true)}
                  className="flex items-center gap-1 px-3 py-1.5 text-xs bg-blue-600/20 text-blue-400 rounded hover:bg-blue-600/30 transition-colors"
                  title="New File"
                >
                  📄 New File
                </button>
                <button
                  onClick={() => setShowNewProjectDialog(true)}
                  className="flex items-center gap-1 px-3 py-1.5 text-xs bg-green-600/20 text-green-400 rounded hover:bg-green-600/30 transition-colors"
                  title="New Project"
                >
                  📁 New Project
                </button>
              </div>

              <div className="p-2">
                <h3 className="text-gray-400 text-xs uppercase font-semibold mb-2 px-2">Explorer</h3>
                {fileTree.map((item, index) => (
                  <FileTreeNode
                    key={index}
                    item={item}
                    level={0}
                    onFileClick={handleFileClick}
                  />
                ))}
              </div>
            </div>
          )}
          
          {sidebarTab === 'git' && (
            <div className="p-4">
              <h3 className="text-white font-semibold mb-4">Source Control</h3>
              <div className="space-y-2">
                <div className="text-green-400 text-sm">✓ 3 files staged</div>
                <div className="text-yellow-400 text-sm">⚠ 2 files modified</div>
                <div className="text-red-400 text-sm">✗ 1 file deleted</div>
              </div>
            </div>
          )}
          
          {sidebarTab === 'extensions' && (
            <div className="p-4">
              <h3 className="text-white font-semibold mb-4">Extensions</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-blue-500 rounded flex items-center justify-center text-xs">TS</div>
                  <div>
                    <div className="text-white text-sm">TypeScript</div>
                    <div className="text-gray-400 text-xs">Installed</div>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-green-500 rounded flex items-center justify-center text-xs">ES</div>
                  <div>
                    <div className="text-white text-sm">ESLint</div>
                    <div className="text-gray-400 text-xs">Installed</div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Main Editor Area */}
      <div className="flex-1 flex flex-col">
        {/* File Tabs */}
        <div className="flex bg-gray-800/40 border-b border-gray-700/50">
          {openFiles.map((file) => (
            <div
              key={file}
              className={`flex items-center gap-2 px-4 py-2 border-r border-gray-700/50 cursor-pointer ${
                activeFile === file
                  ? 'bg-panel-bg text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-800/60'
              }`}
              onClick={() => setActiveFile(file)}
            >
              <span>{file}</span>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  closeFile(file);
                }}
                className="text-gray-500 hover:text-white"
              >
                ×
              </button>
            </div>
          ))}
        </div>

        {/* Code Editor */}
        <div className="flex-1 relative">
          {useTheiaIDE ? (
            /* Complete Theia IDE */
            <div className="w-full h-full relative">
              <div className="absolute top-4 right-4 z-10">
                <div className="bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-xs font-semibold border border-green-500/50 backdrop-blur-sm">
                  🚀 Full Theia IDE Active
                </div>
              </div>
              <TheiaIDE className="w-full h-full" />
            </div>
          ) : (
            /* Simple Monaco Editor */
            activeFile ? (
              <Editor
                height="100%"
                language={getFileLanguage(activeFile)}
                value={files[activeFile] || ''}
                onChange={handleEditorChange}
                theme="vs-dark"
                options={{
                  fontSize: 14,
                  fontFamily: 'Fira Code, Monaco, Consolas, monospace',
                  minimap: { enabled: true },
                  scrollBeyondLastLine: false,
                  automaticLayout: true,
                  tabSize: 2,
                  insertSpaces: true,
                  wordWrap: 'on',
                  lineNumbers: 'on',
                  renderWhitespace: 'selection',
                }}
              />
            ) : (
              <div className="flex items-center justify-center h-full bg-gray-900/30">
                <div className="text-center">
                  <div className="text-6xl mb-4">💻</div>
                  <h3 className="text-white text-xl mb-2">Welcome to the IDE</h3>
                  <p className="text-gray-400 mb-4">Select a file from the explorer to start coding</p>
                  <button
                    onClick={() => setUseTheiaIDE(true)}
                    className="bg-purple-500/20 hover:bg-purple-500/30 text-purple-400 px-6 py-2 rounded-lg transition-colors duration-200 border border-purple-500/50"
                  >
                    🚀 Try Full Theia IDE
                  </button>
                </div>
              </div>
            )
          )}
        </div>

        {/* Enhanced Terminal */}
        {showTerminal && (
          <div className="h-64 bg-black border-t border-gray-700/50 flex flex-col">
            {/* Terminal Header */}
            <div className="flex items-center justify-between px-4 py-2 bg-gray-800/60 border-b border-gray-700/50">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <span className="text-white font-semibold text-sm">Terminal</span>
                  <span className="text-gray-400 text-xs">bash</span>
                </div>

                {/* Terminal Tabs */}
                <div className="flex items-center gap-1">
                  {terminals.map((terminal) => (
                    <button
                      key={terminal.id}
                      onClick={() => setActiveTerminal(terminal.id)}
                      className={`px-3 py-1 text-xs rounded-t ${
                        activeTerminal === terminal.id
                          ? 'bg-black text-green-400 border-t border-l border-r border-gray-600'
                          : 'bg-gray-700/50 text-gray-400 hover:text-white hover:bg-gray-600/50'
                      }`}
                    >
                      {terminal.name}
                    </button>
                  ))}
                  <button
                    onClick={createNewTerminal}
                    className="px-2 py-1 text-xs bg-blue-600/20 text-blue-400 rounded hover:bg-blue-600/30 transition-colors ml-2"
                    title="New Terminal"
                  >
                    +
                  </button>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <button
                  onClick={() => setShowTerminal(false)}
                  className="text-gray-400 hover:text-white text-sm px-2 py-1 rounded hover:bg-gray-700/50"
                >
                  ×
                </button>
              </div>
            </div>

            {/* Terminal Content */}
            <div className="flex-1 p-4 overflow-y-auto">
              <div className="text-green-400 font-mono text-sm">
                {terminals.find(t => t.id === activeTerminal)?.output.map((line, index) => (
                  <div key={index} className={line.startsWith('$') ? 'text-green-400' : 'text-gray-300'}>
                    {line}
                  </div>
                ))}

                {/* Command Input */}
                <div className="flex items-center mt-1">
                  <span className="text-green-400">$ </span>
                  <input
                    type="text"
                    value={terminalInput}
                    onChange={(e) => setTerminalInput(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        executeCommand(terminalInput);
                      }
                    }}
                    className="bg-transparent border-none outline-none text-green-400 ml-1 flex-1 font-mono"
                    placeholder="Type a command..."
                    autoFocus
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Right Sidebar - AI Assistant */}
      <div className="w-80 bg-gray-900/50 border-l border-gray-700/50 p-4">
        <h3 className="text-white font-semibold mb-4">🤖 AI Assistant</h3>
        <div className="space-y-4">
          <div className="bg-gray-800/40 rounded-lg p-3">
            <div className="text-blue-400 text-sm font-medium mb-1">Code Suggestion</div>
            <div className="text-gray-300 text-sm">
              Consider adding error handling to your React component.
            </div>
          </div>
          <div className="bg-gray-800/40 rounded-lg p-3">
            <div className="text-green-400 text-sm font-medium mb-1">Performance Tip</div>
            <div className="text-gray-300 text-sm">
              Use React.memo() to optimize component re-renders.
            </div>
          </div>
        </div>
        
        <div className="mt-6 space-y-3">
          <button
            onClick={() => setShowTerminal(!showTerminal)}
            className="w-full bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 py-2 px-4 rounded-lg transition-colors duration-200"
          >
            {showTerminal ? 'Hide Terminal' : 'Show Terminal'}
          </button>

          <button
            onClick={() => setUseTheiaIDE(!useTheiaIDE)}
            className={`w-full py-2 px-4 rounded-lg transition-colors duration-200 ${
              useTheiaIDE
                ? 'bg-green-500/20 hover:bg-green-500/30 text-green-400 border border-green-500/50'
                : 'bg-purple-500/20 hover:bg-purple-500/30 text-purple-400'
            }`}
          >
            🚀 {useTheiaIDE ? 'Full Theia IDE (Active)' : 'Switch to Full IDE'}
          </button>
        </div>
      </div>

      {/* New File Dialog */}
      {showNewFileDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-96 border border-gray-700">
            <h3 className="text-white text-lg font-semibold mb-4">Create New File</h3>
            <input
              type="text"
              value={newFileName}
              onChange={(e) => setNewFileName(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  createNewFile();
                } else if (e.key === 'Escape') {
                  setShowNewFileDialog(false);
                  setNewFileName('');
                }
              }}
              placeholder="Enter file name (e.g., component.tsx, styles.css)"
              className="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-blue-500 focus:outline-none mb-4"
              autoFocus
            />
            <div className="flex justify-end gap-2">
              <button
                onClick={() => {
                  setShowNewFileDialog(false);
                  setNewFileName('');
                }}
                className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={createNewFile}
                disabled={!newFileName.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Create
              </button>
            </div>
          </div>
        </div>
      )}

      {/* New Project Dialog */}
      {showNewProjectDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-96 border border-gray-700">
            <h3 className="text-white text-lg font-semibold mb-4">Create New Project</h3>
            <input
              type="text"
              value={newProjectName}
              onChange={(e) => setNewProjectName(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  createNewProject();
                } else if (e.key === 'Escape') {
                  setShowNewProjectDialog(false);
                  setNewProjectName('');
                }
              }}
              placeholder="Enter project name (e.g., my-awesome-app)"
              className="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-blue-500 focus:outline-none mb-4"
              autoFocus
            />
            <div className="text-gray-400 text-sm mb-4">
              This will create a new React TypeScript project with the following structure:
              <ul className="mt-2 ml-4 list-disc text-xs">
                <li>src/App.tsx - Main application component</li>
                <li>src/App.css - Application styles</li>
                <li>package.json - Project configuration</li>
                <li>README.md - Project documentation</li>
              </ul>
            </div>
            <div className="flex justify-end gap-2">
              <button
                onClick={() => {
                  setShowNewProjectDialog(false);
                  setNewProjectName('');
                }}
                className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={createNewProject}
                disabled={!newProjectName.trim()}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Create Project
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

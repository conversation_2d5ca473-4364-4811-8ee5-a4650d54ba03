import React, { useState } from 'react';

interface FileTreeItem {
  name: string;
  type: 'file' | 'folder';
  children?: FileTreeItem[];
  isOpen?: boolean;
}

const FileTreeNode: React.FC<{ item: FileTreeItem; level: number; onFileClick: (name: string) => void }> = ({ 
  item, 
  level, 
  onFileClick 
}) => {
  const [isOpen, setIsOpen] = useState(item.isOpen || false);

  const handleClick = () => {
    if (item.type === 'folder') {
      setIsOpen(!isOpen);
    } else {
      onFileClick(item.name);
    }
  };

  return (
    <div>
      <div
        className="flex items-center gap-2 px-2 py-1 hover:bg-gray-700/50 cursor-pointer text-sm"
        style={{ paddingLeft: `${level * 16 + 8}px` }}
        onClick={handleClick}
      >
        {item.type === 'folder' && (
          <span className="text-gray-400">
            {isOpen ? '📂' : '📁'}
          </span>
        )}
        {item.type === 'file' && (
          <span className="text-blue-400">📄</span>
        )}
        <span className="text-gray-300">{item.name}</span>
      </div>
      {item.type === 'folder' && isOpen && item.children && (
        <div>
          {item.children.map((child, index) => (
            <FileTreeNode
              key={index}
              item={child}
              level={level + 1}
              onFileClick={onFileClick}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export const CoderView: React.FC = () => {
  const [activeFile, setActiveFile] = useState<string>('index.tsx');
  const [openFiles, setOpenFiles] = useState<string[]>(['index.tsx', 'styles.css']);
  const [showTerminal, setShowTerminal] = useState(false);
  const [sidebarTab, setSidebarTab] = useState<'files' | 'git' | 'extensions'>('files');

  const fileTree: FileTreeItem[] = [
    {
      name: 'src',
      type: 'folder',
      isOpen: true,
      children: [
        {
          name: 'components',
          type: 'folder',
          children: [
            { name: 'Header.tsx', type: 'file' },
            { name: 'Sidebar.tsx', type: 'file' },
            { name: 'Footer.tsx', type: 'file' }
          ]
        },
        {
          name: 'views',
          type: 'folder',
          children: [
            { name: 'Dashboard.tsx', type: 'file' },
            { name: 'Settings.tsx', type: 'file' }
          ]
        },
        { name: 'index.tsx', type: 'file' },
        { name: 'App.tsx', type: 'file' }
      ]
    },
    {
      name: 'public',
      type: 'folder',
      children: [
        { name: 'favicon.ico', type: 'file' },
        { name: 'index.html', type: 'file' }
      ]
    },
    { name: 'package.json', type: 'file' },
    { name: 'README.md', type: 'file' },
    { name: 'styles.css', type: 'file' }
  ];

  const sampleCode = {
    'index.tsx': `import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import './styles.css';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);`,
    'styles.css': `/* Global Styles */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto';
  background-color: #1a1a1a;
  color: #ffffff;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}`,
    'App.tsx': `import React from 'react';

function App() {
  return (
    <div className="container">
      <h1>Welcome to the IDE</h1>
      <p>Start coding your next project!</p>
    </div>
  );
}

export default App;`
  };

  const handleFileClick = (fileName: string) => {
    setActiveFile(fileName);
    if (!openFiles.includes(fileName)) {
      setOpenFiles([...openFiles, fileName]);
    }
  };

  const closeFile = (fileName: string) => {
    const newOpenFiles = openFiles.filter(f => f !== fileName);
    setOpenFiles(newOpenFiles);
    if (activeFile === fileName && newOpenFiles.length > 0) {
      setActiveFile(newOpenFiles[newOpenFiles.length - 1]);
    }
  };

  return (
    <div className="h-full bg-panel-bg rounded-xl overflow-hidden flex">
      {/* Left Sidebar */}
      <div className="w-64 bg-gray-900/50 border-r border-gray-700/50 flex flex-col">
        {/* Sidebar Tabs */}
        <div className="flex border-b border-gray-700/50">
          {[
            { id: 'files', icon: '📁', label: 'Files' },
            { id: 'git', icon: '🔀', label: 'Git' },
            { id: 'extensions', icon: '🧩', label: 'Extensions' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setSidebarTab(tab.id as any)}
              className={`flex-1 p-3 text-sm font-medium transition-colors duration-200 ${
                sidebarTab === tab.id
                  ? 'bg-blue-500/20 text-blue-400 border-b-2 border-blue-400'
                  : 'text-gray-400 hover:text-white hover:bg-gray-800/60'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </div>

        {/* Sidebar Content */}
        <div className="flex-1 overflow-y-auto">
          {sidebarTab === 'files' && (
            <div className="p-2">
              <h3 className="text-gray-400 text-xs uppercase font-semibold mb-2 px-2">Explorer</h3>
              {fileTree.map((item, index) => (
                <FileTreeNode
                  key={index}
                  item={item}
                  level={0}
                  onFileClick={handleFileClick}
                />
              ))}
            </div>
          )}
          
          {sidebarTab === 'git' && (
            <div className="p-4">
              <h3 className="text-white font-semibold mb-4">Source Control</h3>
              <div className="space-y-2">
                <div className="text-green-400 text-sm">✓ 3 files staged</div>
                <div className="text-yellow-400 text-sm">⚠ 2 files modified</div>
                <div className="text-red-400 text-sm">✗ 1 file deleted</div>
              </div>
            </div>
          )}
          
          {sidebarTab === 'extensions' && (
            <div className="p-4">
              <h3 className="text-white font-semibold mb-4">Extensions</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-blue-500 rounded flex items-center justify-center text-xs">TS</div>
                  <div>
                    <div className="text-white text-sm">TypeScript</div>
                    <div className="text-gray-400 text-xs">Installed</div>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-green-500 rounded flex items-center justify-center text-xs">ES</div>
                  <div>
                    <div className="text-white text-sm">ESLint</div>
                    <div className="text-gray-400 text-xs">Installed</div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Main Editor Area */}
      <div className="flex-1 flex flex-col">
        {/* File Tabs */}
        <div className="flex bg-gray-800/40 border-b border-gray-700/50">
          {openFiles.map((file) => (
            <div
              key={file}
              className={`flex items-center gap-2 px-4 py-2 border-r border-gray-700/50 cursor-pointer ${
                activeFile === file
                  ? 'bg-panel-bg text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-800/60'
              }`}
              onClick={() => setActiveFile(file)}
            >
              <span>{file}</span>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  closeFile(file);
                }}
                className="text-gray-500 hover:text-white"
              >
                ×
              </button>
            </div>
          ))}
        </div>

        {/* Code Editor */}
        <div className="flex-1 relative">
          <div className="absolute inset-0 bg-gray-900/30 p-4">
            <pre className="text-gray-300 text-sm font-mono leading-relaxed">
              <code>{sampleCode[activeFile as keyof typeof sampleCode] || '// File content will be loaded here'}</code>
            </pre>
          </div>
        </div>

        {/* Terminal */}
        {showTerminal && (
          <div className="h-48 bg-black/80 border-t border-gray-700/50 p-4">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-white font-semibold">Terminal</h4>
              <button
                onClick={() => setShowTerminal(false)}
                className="text-gray-400 hover:text-white"
              >
                ×
              </button>
            </div>
            <div className="text-green-400 font-mono text-sm">
              <div>$ npm start</div>
              <div>Starting development server...</div>
              <div>Server running on http://localhost:3000</div>
              <div className="flex items-center">
                <span>$ </span>
                <span className="bg-transparent border-none outline-none text-green-400 ml-1">|</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Right Sidebar - AI Assistant */}
      <div className="w-80 bg-gray-900/50 border-l border-gray-700/50 p-4">
        <h3 className="text-white font-semibold mb-4">🤖 AI Assistant</h3>
        <div className="space-y-4">
          <div className="bg-gray-800/40 rounded-lg p-3">
            <div className="text-blue-400 text-sm font-medium mb-1">Code Suggestion</div>
            <div className="text-gray-300 text-sm">
              Consider adding error handling to your React component.
            </div>
          </div>
          <div className="bg-gray-800/40 rounded-lg p-3">
            <div className="text-green-400 text-sm font-medium mb-1">Performance Tip</div>
            <div className="text-gray-300 text-sm">
              Use React.memo() to optimize component re-renders.
            </div>
          </div>
        </div>
        
        <div className="mt-6">
          <button
            onClick={() => setShowTerminal(!showTerminal)}
            className="w-full bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 py-2 px-4 rounded-lg transition-colors duration-200"
          >
            {showTerminal ? 'Hide Terminal' : 'Show Terminal'}
          </button>
        </div>
      </div>
    </div>
  );
};


import React from 'react';
import { Card } from '../../components/Card';
import { ShoppingCartIcon } from '@heroicons/react/24/outline';

export type FeaturedProductData = {
  image: string;
  category: string;
  name: string;
  price: string;
};

interface FeaturedProductProps {
  product: FeaturedProductData;
}

export const FeaturedProduct: React.FC<FeaturedProductProps> = ({ product }) => {
  return (
    <Card>
      <img src={product.image} alt={product.name} className="rounded-lg mb-4" />
      <div className="flex justify-between items-center">
        <div>
          <p className="text-xs text-gray-400">{product.category}</p>
          <p className="text-lg font-bold text-white">{product.name}</p>
        </div>
        <p className="text-xl font-semibold text-cyan-400">{product.price}</p>
      </div>
      <button className="w-full mt-4 py-2 text-sm font-semibold text-white bg-pink-500/80 border border-pink-500/90 rounded-lg hover:bg-pink-500/90 transition-colors flex items-center justify-center gap-2">
        <ShoppingCartIcon className="w-5 h-5" />
        Add to Cart
      </button>
    </Card>
  );
};

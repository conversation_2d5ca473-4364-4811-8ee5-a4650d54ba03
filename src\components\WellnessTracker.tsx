
import React, { useState } from 'react';
import { Card } from './Card';
import { ComposedChart, Bar, Line, XAxis, YAxis, Tooltip, ResponsiveContainer, Cell } from 'recharts';
import { HomeIcon, ChartBarIcon } from './icons'; // Using generic icons

const data = [
  { name: 'A', uv: 400, pv: 240, amt: 240, line: 300 },
  { name: 'B', uv: 300, pv: 139, amt: 221, line: 450 },
  { name: 'C', uv: 200, pv: 980, amt: 229, line: 200 },
  { name: 'D', uv: 278, pv: 390, amt: 200, line: 500 },
  { name: 'E', uv: 189, pv: 480, amt: 218, line: 150 },
  { name: 'F', uv: 239, pv: 380, amt: 250, line: 350 },
  { name: 'G', uv: 349, pv: 430, amt: 210, line: 420 },
];

export const WellnessTracker: React.FC = () => {
  const [activeTab, setActiveTab] = useState('Heart Rate');

  return (
    <Card title="Wellness tracker">
        <div className="flex justify-between items-start gap-2">
            <div className="flex gap-2 text-[10px] sm:gap-4 sm:text-xs text-gray-400">
                <button onClick={() => setActiveTab('Heart Rate')} className={`flex items-center gap-1 hover:text-white transition-colors ${activeTab === 'Heart Rate' ? 'text-white border-b-2 border-cyan-400 pb-1' : ''}`}><HomeIcon className="w-4 h-4"/>Heart Rate</button>
                <button onClick={() => setActiveTab('Sleep')} className={`flex items-center gap-1 hover:text-white transition-colors ${activeTab === 'Sleep' ? 'text-white border-b-2 border-cyan-400 pb-1' : ''}`}><ChartBarIcon className="w-4 h-4"/>Sleep</button>
                <button onClick={() => setActiveTab('Activity')} className={`flex items-center gap-1 hover:text-white transition-colors ${activeTab === 'Activity' ? 'text-white border-b-2 border-cyan-400 pb-1' : ''}`}><ChartBarIcon className="w-4 h-4"/>Activity</button>
            </div>
            <div className="text-right flex-shrink-0">
                <p className="text-base sm:text-lg lg:text-xl font-semibold text-white">78 bpm</p>
                <p className="text-xs text-gray-400">Current Heart Rate</p>
            </div>
        </div>
        <div className="w-full h-40 sm:h-48 mt-4">
            <ResponsiveContainer width="100%" height="100%">
                <ComposedChart data={data} margin={{ top: 20, right: 0, left: -20, bottom: 0 }}>
                    <XAxis dataKey="name" tick={{ fill: '#A0A0B0', fontSize: 10 }} axisLine={false} tickLine={false} />
                    <YAxis tick={{ fill: '#A0A0B0', fontSize: 10 }} axisLine={false} tickLine={false} />
                    <Tooltip contentStyle={{ background: 'rgba(25, 40, 60, 0.8)', borderColor: '#FF3333', borderRadius: '0.5rem' }}/>
                    <Bar dataKey="pv" barSize={20}>
                       {data.map((entry, index) => (
                         <Cell key={`cell-${index}`} fill={index % 2 === 0 ? '#00FFFF' : '#FF3333'} />
                       ))}
                    </Bar>
                    <Line type="monotone" dataKey="line" stroke="#FF3333" strokeWidth={2} dot={false} />
                </ComposedChart>
            </ResponsiveContainer>
        </div>
    </Card>
  );
};
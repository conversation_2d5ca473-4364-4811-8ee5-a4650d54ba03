
import React, { useState } from 'react';
import Head from 'next/head';
import dynamic from 'next/dynamic';
import { Sidebar } from '../src/components/Sidebar';
import { Header, SchoolDepartment } from '../src/components/Header';
import { Footer } from '../src/components/Footer';
import { RightSidebar } from '../src/components/RightSidebar';
import { SchoolView } from '../src/views/School/SchoolView';
import { BackButton } from '../src/components/BackButton';

const LoadingComponent = () => <div className="flex-grow flex items-center justify-center text-cyan-400">Loading View...</div>;

// Dynamically import views for code splitting and performance
const viewComponents = {
  Dashboard: dynamic(() => import('../src/views/Dashboard/DashboardView').then(mod => mod.DashboardView), { loading: LoadingComponent }),
  School: dynamic(() => import('../src/views/School/SchoolView').then(mod => mod.SchoolView), { loading: LoadingComponent }),
  Tool: dynamic(() => import('../src/views/Tool/ToolView').then(mod => mod.ToolView), { loading: LoadingComponent }),
  Market: dynamic(() => import('../src/views/Market/MarketView').then(mod => mod.MarketView), { loading: LoadingComponent }),
  Bookstore: dynamic(() => import('../src/views/Bookstore/BookstoreView').then(mod => mod.BookstoreView), { loading: LoadingComponent }),
  Concierge: dynamic(() => import('../src/views/Concierge/ConciergeView').then(mod => mod.ConciergeView), { loading: LoadingComponent }),
  Analytic: dynamic(() => import('../src/views/Analytic/AnalyticView').then(mod => mod.AnalyticView), { loading: LoadingComponent }),
  Setting: dynamic(() => import('../src/views/Setting/SettingView').then(mod => mod.SettingView), { loading: LoadingComponent }),
};

type ViewKey = keyof typeof viewComponents;

const HomePage: React.FC = () => {
  const [activeView, setActiveView] = useState<ViewKey>('Dashboard');
  const [activeDepartment, setActiveDepartment] = useState<SchoolDepartment>('school');
  const [activeSubSection, setActiveSubSection] = useState<string>('dashboard');
  const [activeApp, setActiveApp] = useState<string>('');

  const ActiveComponent = viewComponents[activeView] || viewComponents.Dashboard;

  return (
    <>
      <Head>
        <title>Eyes Shield UI</title>
        <meta name="description" content="A futuristic, high-tech dashboard UI with a glassmorphism theme, displaying various data visualizations and system statuses on a semi-transparent, glowing panel." />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </Head>
      <main className="main-background font-poppins text-[#E0E0E0] dashboard-auto-scale main-layout">
        <div className="absolute inset-0 bg-black/60 backdrop-blur-xl"></div>

        <div className="relative z-10 bg-container-bg content-area">
          <Sidebar
            activeView={activeView}
            setActiveView={(view) => setActiveView(view as ViewKey)}
            activeApp={activeApp}
            setActiveApp={setActiveApp}
          />
          <div className="main-content">
            <div className="header-section">
              <Header
                showSchoolButtons={activeView === 'School' && !activeApp}
                activeDepartment={activeDepartment}
                setActiveDepartment={setActiveDepartment}
                activeSubSection={activeView === 'School' ? activeSubSection : undefined}
              />
            </div>
            <div className="main-section">
              {activeApp ? (
                <div className="h-full relative">
                  <BackButton onBack={() => setActiveApp('')} />
                  {/* App Views will be rendered here */}
                  {activeApp === 'gamification' && <div className="h-full flex items-center justify-center text-accent-blue text-2xl">🎮 Gamification App Coming Soon</div>}
                  {activeApp === 'coder' && <div className="h-full flex items-center justify-center text-accent-blue text-2xl">💻 Coder IDE Coming Soon</div>}
                  {activeApp === 'media' && <div className="h-full flex items-center justify-center text-accent-blue text-2xl">🎥 Media Hub Coming Soon</div>}
                  {activeApp === 'studio' && <div className="h-full flex items-center justify-center text-accent-blue text-2xl">🧑‍🎨 Studio Coming Soon</div>}
                </div>
              ) : activeView === 'School' ? (
                <SchoolView
                  activeDepartment={activeDepartment}
                  setActiveDepartment={setActiveDepartment}
                  activeSubSection={activeSubSection}
                  setActiveSubSection={setActiveSubSection}
                />
              ) : (
                <ActiveComponent />
              )}
            </div>
            <div className="footer-section">
              <Footer />
            </div>
          </div>
          <RightSidebar activeView={activeView} setActiveView={(view) => setActiveView(view as ViewKey)} />
        </div>
      </main>
    </>
  );
};

export default HomePage;

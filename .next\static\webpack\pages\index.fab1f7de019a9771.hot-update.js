"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/views/School/departments/TeacherDashboard.tsx":
/*!***********************************************************!*\
  !*** ./src/views/School/departments/TeacherDashboard.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TeacherDashboard: function() { return /* binding */ TeacherDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../components/Card */ \"./src/components/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BookOpenIcon_ChartBarIcon_ClipboardDocumentListIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BookOpenIcon,ChartBarIcon,ClipboardDocumentListIcon,UserGroupIcon!=!@heroicons/react/24/solid */ \"__barrel_optimize__?names=AcademicCapIcon,BookOpenIcon,ChartBarIcon,ClipboardDocumentListIcon,UserGroupIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/solid/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst TeacherDashboard = (param)=>{\n    let { activeSubSection } = param;\n    _s();\n    const [teacherData, setTeacherData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchTeacherData = async ()=>{\n            try {\n                setLoading(true);\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                const mockData = {\n                    overview: {\n                        totalClasses: 6,\n                        totalStudents: 142,\n                        avgGrade: 87.5,\n                        pendingAssignments: 23,\n                        aiAssistantInteractions: 847,\n                        emotionalWellnessScore: 94,\n                        adaptiveLearningPaths: 156,\n                        hologramLessons: 12\n                    },\n                    classes: [\n                        {\n                            name: \"Quantum Physics VR\",\n                            students: 28,\n                            grade: \"Grade 11\",\n                            color: \"bg-blue-500/20 text-blue-400\",\n                            aiTutor: \"Einstein AI\",\n                            engagement: 96\n                        },\n                        {\n                            name: \"Neural Network Programming\",\n                            students: 24,\n                            grade: \"Grade 12\",\n                            color: \"bg-green-500/20 text-green-400\",\n                            aiTutor: \"Turing AI\",\n                            engagement: 94\n                        },\n                        {\n                            name: \"Bioengineering Lab\",\n                            students: 32,\n                            grade: \"Grade 10\",\n                            color: \"bg-purple-500/20 text-purple-400\",\n                            aiTutor: \"Darwin AI\",\n                            engagement: 92\n                        }\n                    ],\n                    aiInsights: [\n                        {\n                            student: \"Alex Chen\",\n                            insight: \"Shows exceptional pattern recognition in quantum mechanics\",\n                            confidence: 97,\n                            action: \"Advanced track recommended\"\n                        },\n                        {\n                            student: \"Maya Patel\",\n                            insight: \"Emotional stress detected during complex problems\",\n                            confidence: 89,\n                            action: \"Wellness check scheduled\"\n                        },\n                        {\n                            student: \"Jordan Smith\",\n                            insight: \"Learning style: Visual-Kinesthetic hybrid\",\n                            confidence: 94,\n                            action: \"VR modules assigned\"\n                        }\n                    ],\n                    recentAssignments: [\n                        {\n                            title: \"Quantum Entanglement Simulation\",\n                            class: \"Quantum Physics VR\",\n                            dueDate: \"March 18\",\n                            submitted: 24,\n                            total: 28,\n                            aiGraded: 18,\n                            avgTime: \"2.3h\"\n                        },\n                        {\n                            title: \"Neural Network Architecture\",\n                            class: \"Neural Network Programming\",\n                            dueDate: \"March 20\",\n                            submitted: 20,\n                            total: 24,\n                            aiGraded: 15,\n                            avgTime: \"3.1h\"\n                        },\n                        {\n                            title: \"Gene Editing Ethics Debate\",\n                            class: \"Bioengineering Lab\",\n                            dueDate: \"March 25\",\n                            submitted: 15,\n                            total: 32,\n                            aiGraded: 8,\n                            avgTime: \"1.8h\"\n                        }\n                    ]\n                };\n                setTeacherData(mockData);\n            } catch (error) {\n                console.error(\"Failed to fetch teacher data:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchTeacherData();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-cyan-400\",\n                children: \"Loading Teacher Dashboard...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, undefined);\n    }\n    const renderContent = ()=>{\n        switch(activeSubSection){\n            case \"dashboard\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-12 gap-3 h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                title: \"\\uD83E\\uDD16 AI-Enhanced Teaching Hub\",\n                                className: \"hud-card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-4 gap-3 p-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"metric-card\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-gradient-to-br from-green-400/20 to-green-600/20 rounded-lg flex items-center justify-center mx-auto mb-2 border border-green-400/30\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BookOpenIcon_ChartBarIcon_ClipboardDocumentListIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.AcademicCapIcon, {\n                                                        className: \"w-5 h-5 text-green-400\",\n                                                        style: {\n                                                            filter: \"drop-shadow(0 0 6px rgba(34, 197, 94, 0.6))\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl font-bold text-white mb-1\",\n                                                    style: {\n                                                        textShadow: \"0 0 10px rgba(255, 255, 255, 0.5)\"\n                                                    },\n                                                    children: teacherData.overview.hologramLessons\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-300 font-medium\",\n                                                    children: \"Hologram Lessons\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"metric-card\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-gradient-to-br from-blue-400/20 to-blue-600/20 rounded-lg flex items-center justify-center mx-auto mb-2 border border-blue-400/30\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BookOpenIcon_ChartBarIcon_ClipboardDocumentListIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.UserGroupIcon, {\n                                                        className: \"w-5 h-5 text-blue-400\",\n                                                        style: {\n                                                            filter: \"drop-shadow(0 0 6px rgba(59, 130, 246, 0.6))\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl font-bold text-white mb-1\",\n                                                    style: {\n                                                        textShadow: \"0 0 10px rgba(255, 255, 255, 0.5)\"\n                                                    },\n                                                    children: teacherData.overview.aiAssistantInteractions\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-300 font-medium\",\n                                                    children: \"AI Interactions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"metric-card\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-gradient-to-br from-yellow-400/20 to-yellow-600/20 rounded-lg flex items-center justify-center mx-auto mb-2 border border-yellow-400/30\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BookOpenIcon_ChartBarIcon_ClipboardDocumentListIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.ChartBarIcon, {\n                                                        className: \"w-5 h-5 text-yellow-400\",\n                                                        style: {\n                                                            filter: \"drop-shadow(0 0 6px rgba(234, 179, 8, 0.6))\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl font-bold text-white mb-1\",\n                                                    style: {\n                                                        textShadow: \"0 0 10px rgba(255, 255, 255, 0.5)\"\n                                                    },\n                                                    children: [\n                                                        teacherData.overview.emotionalWellnessScore,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-300 font-medium\",\n                                                    children: \"Wellness Score\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"metric-card\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-gradient-to-br from-purple-400/20 to-purple-600/20 rounded-lg flex items-center justify-center mx-auto mb-2 border border-purple-400/30\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BookOpenIcon_ChartBarIcon_ClipboardDocumentListIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.ClipboardDocumentListIcon, {\n                                                        className: \"w-5 h-5 text-purple-400\",\n                                                        style: {\n                                                            filter: \"drop-shadow(0 0 6px rgba(147, 51, 234, 0.6))\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl font-bold text-white mb-1\",\n                                                    style: {\n                                                        textShadow: \"0 0 10px rgba(255, 255, 255, 0.5)\"\n                                                    },\n                                                    children: teacherData.overview.adaptiveLearningPaths\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-300 font-medium\",\n                                                    children: \"Learning Paths\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-7\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                title: \"\\uD83E\\uDDE0 AI Student Insights\",\n                                className: \"hud-card h-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 p-3 max-h-64 overflow-y-auto\",\n                                    children: teacherData.aiInsights.map((insight, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 rounded-lg bg-gradient-to-r from-purple-900/30 to-blue-900/30 border border-purple-400/40 backdrop-filter backdrop-blur-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-semibold text-white\",\n                                                            children: insight.student\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"status-badge active text-xs\",\n                                                            children: [\n                                                                insight.confidence,\n                                                                \"% confidence\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                            lineNumber: 122,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-300 mb-2 leading-relaxed\",\n                                                    children: insight.insight\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-cyan-400 font-medium\",\n                                                    children: [\n                                                        \"\\uD83D\\uDCA1 \",\n                                                        insight.action\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                title: \"\\uD83D\\uDE80 Next-Gen Classes\",\n                                className: \"hud-card h-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 p-3\",\n                                    children: teacherData.classes.map((cls, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 rounded-lg \".concat(cls.color.split(\" \")[0], \" border border-gray-500/40 backdrop-filter backdrop-blur-sm transition-all duration-300 hover:scale-102 hover:border-cyan-400/60\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold text-sm \".concat(cls.color.split(\" \").slice(1).join(\" \")),\n                                                                children: cls.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400 mt-1\",\n                                                                children: [\n                                                                    cls.grade,\n                                                                    \" • \",\n                                                                    cls.students,\n                                                                    \" students\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                                lineNumber: 143,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-cyan-400 font-medium\",\n                                                                        children: [\n                                                                            \"\\uD83E\\uDD16 \",\n                                                                            cls.aiTutor\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                                        lineNumber: 145,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-1 h-1 bg-gray-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                                        lineNumber: 146,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-green-400 font-medium\",\n                                                                        children: [\n                                                                            cls.engagement,\n                                                                            \"% engaged\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                                        lineNumber: 147,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                                lineNumber: 144,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 rounded-lg \".concat(cls.color.split(\" \")[0], \" flex items-center justify-center border border-gray-400/30\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BookOpenIcon_ChartBarIcon_ClipboardDocumentListIcon_UserGroupIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.BookOpenIcon, {\n                                                            className: \"w-4 h-4 \".concat(cls.color.split(\" \").slice(1).join(\" \")),\n                                                            style: {\n                                                                filter: \"drop-shadow(0 0 4px rgba(59, 130, 246, 0.4))\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                title: \"\\uD83C\\uDFAF AI-Graded Assignments\",\n                                className: \"hud-card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 p-3\",\n                                    children: teacherData.recentAssignments.map((assignment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 rounded-lg bg-gradient-to-br from-gray-800/60 to-gray-700/60 border border-gray-500/40 backdrop-filter backdrop-blur-sm transition-all duration-300 hover:border-cyan-400/60 hover:scale-102\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-semibold text-white mb-2\",\n                                                    children: assignment.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-400 mb-3\",\n                                                    children: [\n                                                        assignment.class,\n                                                        \" • Due: \",\n                                                        assignment.dueDate\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 bg-gray-700/60 rounded-full h-2 border border-gray-600/30\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gradient-to-r from-cyan-400 to-blue-500 h-2 rounded-full transition-all duration-500 shadow-lg\",\n                                                                style: {\n                                                                    width: \"\".concat(assignment.submitted / assignment.total * 100, \"%\"),\n                                                                    boxShadow: \"0 0 8px rgba(0, 207, 255, 0.6)\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                                lineNumber: 172,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-300 font-medium\",\n                                                            children: [\n                                                                assignment.submitted,\n                                                                \"/\",\n                                                                assignment.total\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-purple-400 font-medium\",\n                                                            children: [\n                                                                \"\\uD83E\\uDD16 \",\n                                                                assignment.aiGraded,\n                                                                \" AI-graded\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-yellow-400 font-medium\",\n                                                            children: [\n                                                                \"⏱️ Avg: \",\n                                                                assignment.avgTime\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, undefined);\n            case \"classes\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"My Classes\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Class Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Manage your classes, view student rosters, and track attendance.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 11\n                }, undefined);\n            case \"students\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Student Management\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Student Overview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"View student profiles, track progress, and manage communications.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 11\n                }, undefined);\n            case \"curriculum\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Curriculum Planning\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Curriculum Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Plan lessons, manage curriculum standards, and track learning objectives.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 11\n                }, undefined);\n            case \"assignments\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Assignment Center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Assignment Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Create, distribute, and grade assignments across all your classes.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 11\n                }, undefined);\n            case \"grades\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Grade Book\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Grade Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Record grades, generate reports, and track student performance.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 11\n                }, undefined);\n            case \"resources\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Teaching Resources\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Resource Library\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Access teaching materials, lesson plans, and educational resources.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 11\n                }, undefined);\n            case \"settings\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Teacher Settings\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Personal Settings\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Configure your teaching preferences and account settings.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    title: \"Teacher Dashboard\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Welcome to Teacher Portal\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Select a section from the navigation to get started.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"dashboard-grid dashboard-grid-teacher content-no-scroll\",\n        children: renderContent()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\School\\\\departments\\\\TeacherDashboard.tsx\",\n        lineNumber: 279,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TeacherDashboard, \"i1gheQWXW7purOEwZqF0fYPpwyw=\");\n_c = TeacherDashboard;\nvar _c;\n$RefreshReg$(_c, \"TeacherDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/School/departments/TeacherDashboard.tsx\n"));

/***/ })

});
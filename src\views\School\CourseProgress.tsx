
import React from 'react';
import { Card } from '../../components/Card';

export type Course = {
  name: string;
  progress: number;
  color: 'cyan' | 'pink' | 'yellow';
};

interface CourseProgressProps {
  courses: Course[];
}

export const CourseProgress: React.FC<CourseProgressProps> = ({ courses }) => {
  return (
    <Card title="Course Progress">
      <ul className="space-y-4">
        {courses.map(course => (
          <li key={course.name}>
            <div className="flex justify-between items-center mb-1">
              <span className="text-white font-medium text-sm">{course.name}</span>
              <span className="text-gray-400 text-sm">{course.progress}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${course.color === 'cyan' ? 'bg-cyan-400' : course.color === 'pink' ? 'bg-pink-500' : 'bg-yellow-400'}`} 
                style={{ width: `${course.progress}%` }}
              ></div>
            </div>
          </li>
        ))}
      </ul>
    </Card>
  );
};

// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Deprecated Standin widget should have a default snapshot: first render 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        Read the excerpt and answer the question below. 
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="1"
    >
      <div
        class="paragraph"
      >
        The Governor and Council of the Massachusetts had much conference many days; and at last . . . . concluded a peace and friendship with 
        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            style="padding-top: 8px; padding-bottom: 8px;"
          >
            <div
              class="default_xu2jcg-o_O-containerOuter_1fa127e-o_O-infoBanner_rdpfpm"
              role="status"
            >
              <div
                class="default_xu2jcg-o_O-containerInner_1lza62o"
              >
                <span
                  aria-label="info"
                  class="svg_1q6jc65-o_O-icon_1c9fkuf-o_O-icon_18itqqa-o_O-infoIcon_cju5w7-o_O-inlineStyles_i5hedm"
                  data-testid="banner-kind-icon"
                  role="img"
                />
                <div
                  class="default_xu2jcg-o_O-labelAndButtonsContainer_1dntbm4"
                >
                  <div
                    class="default_xu2jcg-o_O-labelContainer_14mmd26"
                  >
                    <div
                      class="labelTypography_1y1267m"
                    >
                      Sorry, this part of the question is no longer available. 😅 Don't worry, you won't be graded on this part. Keep going!
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        , upon these conditions.
      </div>
    </div>
  </div>
</div>
`;

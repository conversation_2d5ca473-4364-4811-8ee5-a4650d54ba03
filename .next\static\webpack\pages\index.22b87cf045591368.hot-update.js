"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js":
/*!****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js ***!
  \****************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrayLikeToArray: function() { return /* binding */ _arrayLikeToArray; },\n/* harmony export */   arrayWithHoles: function() { return /* binding */ _arrayWithHoles; },\n/* harmony export */   defineProperty: function() { return /* binding */ _defineProperty; },\n/* harmony export */   iterableToArrayLimit: function() { return /* binding */ _iterableToArrayLimit; },\n/* harmony export */   nonIterableRest: function() { return /* binding */ _nonIterableRest; },\n/* harmony export */   objectSpread2: function() { return /* binding */ _objectSpread2; },\n/* harmony export */   objectWithoutProperties: function() { return /* binding */ _objectWithoutProperties; },\n/* harmony export */   objectWithoutPropertiesLoose: function() { return /* binding */ _objectWithoutPropertiesLoose; },\n/* harmony export */   slicedToArray: function() { return /* binding */ _slicedToArray; },\n/* harmony export */   unsupportedIterableToArray: function() { return /* binding */ _unsupportedIterableToArray; }\n/* harmony export */ });\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _e = undefined;\n\n  try {\n    for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvLnBucG0vQG1vbmFjby1lZGl0b3IrbG9hZGVyQDEuNS4wL25vZGVfbW9kdWxlcy9AbW9uYWNvLWVkaXRvci9sb2FkZXIvbGliL2VzL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLElBQUk7QUFDSjtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSxrQkFBa0Isc0JBQXNCO0FBQ3hDOztBQUVBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCxNQUFNO0FBQ047QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxjQUFjLHVCQUF1QjtBQUNyQztBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7O0FBRUE7O0FBRUE7QUFDQTs7QUFFQSxnQkFBZ0IsNkJBQTZCO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsOENBQThDLCtCQUErQjtBQUM3RTs7QUFFQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUEseUNBQXlDLFNBQVM7O0FBRWxEO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUU0YiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvLnBucG0vQG1vbmFjby1lZGl0b3IrbG9hZGVyQDEuNS4wL25vZGVfbW9kdWxlcy9AbW9uYWNvLWVkaXRvci9sb2FkZXIvbGliL2VzL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanM/MTRiMiJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBfZGVmaW5lUHJvcGVydHkob2JqLCBrZXksIHZhbHVlKSB7XG4gIGlmIChrZXkgaW4gb2JqKSB7XG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KG9iaiwga2V5LCB7XG4gICAgICB2YWx1ZTogdmFsdWUsXG4gICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgd3JpdGFibGU6IHRydWVcbiAgICB9KTtcbiAgfSBlbHNlIHtcbiAgICBvYmpba2V5XSA9IHZhbHVlO1xuICB9XG5cbiAgcmV0dXJuIG9iajtcbn1cblxuZnVuY3Rpb24gb3duS2V5cyhvYmplY3QsIGVudW1lcmFibGVPbmx5KSB7XG4gIHZhciBrZXlzID0gT2JqZWN0LmtleXMob2JqZWN0KTtcblxuICBpZiAoT2JqZWN0LmdldE93blByb3BlcnR5U3ltYm9scykge1xuICAgIHZhciBzeW1ib2xzID0gT2JqZWN0LmdldE93blByb3BlcnR5U3ltYm9scyhvYmplY3QpO1xuICAgIGlmIChlbnVtZXJhYmxlT25seSkgc3ltYm9scyA9IHN5bWJvbHMuZmlsdGVyKGZ1bmN0aW9uIChzeW0pIHtcbiAgICAgIHJldHVybiBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKG9iamVjdCwgc3ltKS5lbnVtZXJhYmxlO1xuICAgIH0pO1xuICAgIGtleXMucHVzaC5hcHBseShrZXlzLCBzeW1ib2xzKTtcbiAgfVxuXG4gIHJldHVybiBrZXlzO1xufVxuXG5mdW5jdGlvbiBfb2JqZWN0U3ByZWFkMih0YXJnZXQpIHtcbiAgZm9yICh2YXIgaSA9IDE7IGkgPCBhcmd1bWVudHMubGVuZ3RoOyBpKyspIHtcbiAgICB2YXIgc291cmNlID0gYXJndW1lbnRzW2ldICE9IG51bGwgPyBhcmd1bWVudHNbaV0gOiB7fTtcblxuICAgIGlmIChpICUgMikge1xuICAgICAgb3duS2V5cyhPYmplY3Qoc291cmNlKSwgdHJ1ZSkuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7XG4gICAgICAgIF9kZWZpbmVQcm9wZXJ0eSh0YXJnZXQsIGtleSwgc291cmNlW2tleV0pO1xuICAgICAgfSk7XG4gICAgfSBlbHNlIGlmIChPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9ycykge1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnRpZXModGFyZ2V0LCBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9ycyhzb3VyY2UpKTtcbiAgICB9IGVsc2Uge1xuICAgICAgb3duS2V5cyhPYmplY3Qoc291cmNlKSkuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0YXJnZXQsIGtleSwgT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihzb3VyY2UsIGtleSkpO1xuICAgICAgfSk7XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIHRhcmdldDtcbn1cblxuZnVuY3Rpb24gX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzTG9vc2Uoc291cmNlLCBleGNsdWRlZCkge1xuICBpZiAoc291cmNlID09IG51bGwpIHJldHVybiB7fTtcbiAgdmFyIHRhcmdldCA9IHt9O1xuICB2YXIgc291cmNlS2V5cyA9IE9iamVjdC5rZXlzKHNvdXJjZSk7XG4gIHZhciBrZXksIGk7XG5cbiAgZm9yIChpID0gMDsgaSA8IHNvdXJjZUtleXMubGVuZ3RoOyBpKyspIHtcbiAgICBrZXkgPSBzb3VyY2VLZXlzW2ldO1xuICAgIGlmIChleGNsdWRlZC5pbmRleE9mKGtleSkgPj0gMCkgY29udGludWU7XG4gICAgdGFyZ2V0W2tleV0gPSBzb3VyY2Vba2V5XTtcbiAgfVxuXG4gIHJldHVybiB0YXJnZXQ7XG59XG5cbmZ1bmN0aW9uIF9vYmplY3RXaXRob3V0UHJvcGVydGllcyhzb3VyY2UsIGV4Y2x1ZGVkKSB7XG4gIGlmIChzb3VyY2UgPT0gbnVsbCkgcmV0dXJuIHt9O1xuXG4gIHZhciB0YXJnZXQgPSBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZShzb3VyY2UsIGV4Y2x1ZGVkKTtcblxuICB2YXIga2V5LCBpO1xuXG4gIGlmIChPYmplY3QuZ2V0T3duUHJvcGVydHlTeW1ib2xzKSB7XG4gICAgdmFyIHNvdXJjZVN5bWJvbEtleXMgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlTeW1ib2xzKHNvdXJjZSk7XG5cbiAgICBmb3IgKGkgPSAwOyBpIDwgc291cmNlU3ltYm9sS2V5cy5sZW5ndGg7IGkrKykge1xuICAgICAga2V5ID0gc291cmNlU3ltYm9sS2V5c1tpXTtcbiAgICAgIGlmIChleGNsdWRlZC5pbmRleE9mKGtleSkgPj0gMCkgY29udGludWU7XG4gICAgICBpZiAoIU9iamVjdC5wcm90b3R5cGUucHJvcGVydHlJc0VudW1lcmFibGUuY2FsbChzb3VyY2UsIGtleSkpIGNvbnRpbnVlO1xuICAgICAgdGFyZ2V0W2tleV0gPSBzb3VyY2Vba2V5XTtcbiAgICB9XG4gIH1cblxuICByZXR1cm4gdGFyZ2V0O1xufVxuXG5mdW5jdGlvbiBfc2xpY2VkVG9BcnJheShhcnIsIGkpIHtcbiAgcmV0dXJuIF9hcnJheVdpdGhIb2xlcyhhcnIpIHx8IF9pdGVyYWJsZVRvQXJyYXlMaW1pdChhcnIsIGkpIHx8IF91bnN1cHBvcnRlZEl0ZXJhYmxlVG9BcnJheShhcnIsIGkpIHx8IF9ub25JdGVyYWJsZVJlc3QoKTtcbn1cblxuZnVuY3Rpb24gX2FycmF5V2l0aEhvbGVzKGFycikge1xuICBpZiAoQXJyYXkuaXNBcnJheShhcnIpKSByZXR1cm4gYXJyO1xufVxuXG5mdW5jdGlvbiBfaXRlcmFibGVUb0FycmF5TGltaXQoYXJyLCBpKSB7XG4gIGlmICh0eXBlb2YgU3ltYm9sID09PSBcInVuZGVmaW5lZFwiIHx8ICEoU3ltYm9sLml0ZXJhdG9yIGluIE9iamVjdChhcnIpKSkgcmV0dXJuO1xuICB2YXIgX2FyciA9IFtdO1xuICB2YXIgX24gPSB0cnVlO1xuICB2YXIgX2QgPSBmYWxzZTtcbiAgdmFyIF9lID0gdW5kZWZpbmVkO1xuXG4gIHRyeSB7XG4gICAgZm9yICh2YXIgX2kgPSBhcnJbU3ltYm9sLml0ZXJhdG9yXSgpLCBfczsgIShfbiA9IChfcyA9IF9pLm5leHQoKSkuZG9uZSk7IF9uID0gdHJ1ZSkge1xuICAgICAgX2Fyci5wdXNoKF9zLnZhbHVlKTtcblxuICAgICAgaWYgKGkgJiYgX2Fyci5sZW5ndGggPT09IGkpIGJyZWFrO1xuICAgIH1cbiAgfSBjYXRjaCAoZXJyKSB7XG4gICAgX2QgPSB0cnVlO1xuICAgIF9lID0gZXJyO1xuICB9IGZpbmFsbHkge1xuICAgIHRyeSB7XG4gICAgICBpZiAoIV9uICYmIF9pW1wicmV0dXJuXCJdICE9IG51bGwpIF9pW1wicmV0dXJuXCJdKCk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIGlmIChfZCkgdGhyb3cgX2U7XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIF9hcnI7XG59XG5cbmZ1bmN0aW9uIF91bnN1cHBvcnRlZEl0ZXJhYmxlVG9BcnJheShvLCBtaW5MZW4pIHtcbiAgaWYgKCFvKSByZXR1cm47XG4gIGlmICh0eXBlb2YgbyA9PT0gXCJzdHJpbmdcIikgcmV0dXJuIF9hcnJheUxpa2VUb0FycmF5KG8sIG1pbkxlbik7XG4gIHZhciBuID0gT2JqZWN0LnByb3RvdHlwZS50b1N0cmluZy5jYWxsKG8pLnNsaWNlKDgsIC0xKTtcbiAgaWYgKG4gPT09IFwiT2JqZWN0XCIgJiYgby5jb25zdHJ1Y3RvcikgbiA9IG8uY29uc3RydWN0b3IubmFtZTtcbiAgaWYgKG4gPT09IFwiTWFwXCIgfHwgbiA9PT0gXCJTZXRcIikgcmV0dXJuIEFycmF5LmZyb20obyk7XG4gIGlmIChuID09PSBcIkFyZ3VtZW50c1wiIHx8IC9eKD86VWl8SSludCg/Ojh8MTZ8MzIpKD86Q2xhbXBlZCk/QXJyYXkkLy50ZXN0KG4pKSByZXR1cm4gX2FycmF5TGlrZVRvQXJyYXkobywgbWluTGVuKTtcbn1cblxuZnVuY3Rpb24gX2FycmF5TGlrZVRvQXJyYXkoYXJyLCBsZW4pIHtcbiAgaWYgKGxlbiA9PSBudWxsIHx8IGxlbiA+IGFyci5sZW5ndGgpIGxlbiA9IGFyci5sZW5ndGg7XG5cbiAgZm9yICh2YXIgaSA9IDAsIGFycjIgPSBuZXcgQXJyYXkobGVuKTsgaSA8IGxlbjsgaSsrKSBhcnIyW2ldID0gYXJyW2ldO1xuXG4gIHJldHVybiBhcnIyO1xufVxuXG5mdW5jdGlvbiBfbm9uSXRlcmFibGVSZXN0KCkge1xuICB0aHJvdyBuZXcgVHlwZUVycm9yKFwiSW52YWxpZCBhdHRlbXB0IHRvIGRlc3RydWN0dXJlIG5vbi1pdGVyYWJsZSBpbnN0YW5jZS5cXG5JbiBvcmRlciB0byBiZSBpdGVyYWJsZSwgbm9uLWFycmF5IG9iamVjdHMgbXVzdCBoYXZlIGEgW1N5bWJvbC5pdGVyYXRvcl0oKSBtZXRob2QuXCIpO1xufVxuXG5leHBvcnQgeyBfYXJyYXlMaWtlVG9BcnJheSBhcyBhcnJheUxpa2VUb0FycmF5LCBfYXJyYXlXaXRoSG9sZXMgYXMgYXJyYXlXaXRoSG9sZXMsIF9kZWZpbmVQcm9wZXJ0eSBhcyBkZWZpbmVQcm9wZXJ0eSwgX2l0ZXJhYmxlVG9BcnJheUxpbWl0IGFzIGl0ZXJhYmxlVG9BcnJheUxpbWl0LCBfbm9uSXRlcmFibGVSZXN0IGFzIG5vbkl0ZXJhYmxlUmVzdCwgX29iamVjdFNwcmVhZDIgYXMgb2JqZWN0U3ByZWFkMiwgX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzIGFzIG9iamVjdFdpdGhvdXRQcm9wZXJ0aWVzLCBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZSBhcyBvYmplY3RXaXRob3V0UHJvcGVydGllc0xvb3NlLCBfc2xpY2VkVG9BcnJheSBhcyBzbGljZWRUb0FycmF5LCBfdW5zdXBwb3J0ZWRJdGVyYWJsZVRvQXJyYXkgYXMgdW5zdXBwb3J0ZWRJdGVyYWJsZVRvQXJyYXkgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js\n"));

/***/ }),

/***/ "./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/config/index.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/config/index.js ***!
  \******************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\nvar config = {\n  paths: {\n    vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs'\n  }\n};\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (config);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvLnBucG0vQG1vbmFjby1lZGl0b3IrbG9hZGVyQDEuNS4wL25vZGVfbW9kdWxlcy9AbW9uYWNvLWVkaXRvci9sb2FkZXIvbGliL2VzL2NvbmZpZy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiO0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSwrREFBZSxNQUFNLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Btb25hY28tZWRpdG9yK2xvYWRlckAxLjUuMC9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy9jb25maWcvaW5kZXguanM/NzA0MSJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgY29uZmlnID0ge1xuICBwYXRoczoge1xuICAgIHZzOiAnaHR0cHM6Ly9jZG4uanNkZWxpdnIubmV0L25wbS9tb25hY28tZWRpdG9yQDAuNTIuMi9taW4vdnMnXG4gIH1cbn07XG5cbmV4cG9ydCBkZWZhdWx0IGNvbmZpZztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/config/index.js\n"));

/***/ }),

/***/ "./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/index.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/index.js ***!
  \***********************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* reexport safe */ _loader_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _loader_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./loader/index.js */ \"./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/loader/index.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvLnBucG0vQG1vbmFjby1lZGl0b3IrbG9hZGVyQDEuNS4wL25vZGVfbW9kdWxlcy9AbW9uYWNvLWVkaXRvci9sb2FkZXIvbGliL2VzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXVDO0FBQ0siLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Btb25hY28tZWRpdG9yK2xvYWRlckAxLjUuMC9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy9pbmRleC5qcz9mMmE1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBsb2FkZXIgZnJvbSAnLi9sb2FkZXIvaW5kZXguanMnO1xuZXhwb3J0IHsgZGVmYXVsdCB9IGZyb20gJy4vbG9hZGVyL2luZGV4LmpzJztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/index.js\n"));

/***/ }),

/***/ "./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/loader/index.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/loader/index.js ***!
  \******************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var state_local__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! state-local */ \"./node_modules/.pnpm/state-local@1.0.7/node_modules/state-local/lib/es/state-local.js\");\n/* harmony import */ var _config_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../config/index.js */ \"./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/config/index.js\");\n/* harmony import */ var _validators_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../validators/index.js */ \"./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/validators/index.js\");\n/* harmony import */ var _utils_compose_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/compose.js */ \"./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/compose.js\");\n/* harmony import */ var _utils_deepMerge_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/deepMerge.js */ \"./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js\");\n/* harmony import */ var _utils_makeCancelable_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/makeCancelable.js */ \"./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js\");\n\n\n\n\n\n\n\n\n/** the local state of the module */\n\nvar _state$create = state_local__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n  config: _config_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n  isInitialized: false,\n  resolve: null,\n  reject: null,\n  monaco: null\n}),\n    _state$create2 = (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__.slicedToArray)(_state$create, 2),\n    getState = _state$create2[0],\n    setState = _state$create2[1];\n/**\n * set the loader configuration\n * @param {Object} config - the configuration object\n */\n\n\nfunction config(globalConfig) {\n  var _validators$config = _validators_index_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"].config(globalConfig),\n      monaco = _validators$config.monaco,\n      config = (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__.objectWithoutProperties)(_validators$config, [\"monaco\"]);\n\n  setState(function (state) {\n    return {\n      config: (0,_utils_deepMerge_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(state.config, config),\n      monaco: monaco\n    };\n  });\n}\n/**\n * handles the initialization of the monaco-editor\n * @return {Promise} - returns an instance of monaco (with a cancelable promise)\n */\n\n\nfunction init() {\n  var state = getState(function (_ref) {\n    var monaco = _ref.monaco,\n        isInitialized = _ref.isInitialized,\n        resolve = _ref.resolve;\n    return {\n      monaco: monaco,\n      isInitialized: isInitialized,\n      resolve: resolve\n    };\n  });\n\n  if (!state.isInitialized) {\n    setState({\n      isInitialized: true\n    });\n\n    if (state.monaco) {\n      state.resolve(state.monaco);\n      return (0,_utils_makeCancelable_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(wrapperPromise);\n    }\n\n    if (window.monaco && window.monaco.editor) {\n      storeMonacoInstance(window.monaco);\n      state.resolve(window.monaco);\n      return (0,_utils_makeCancelable_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(wrapperPromise);\n    }\n\n    (0,_utils_compose_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(injectScripts, getMonacoLoaderScript)(configureLoader);\n  }\n\n  return (0,_utils_makeCancelable_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(wrapperPromise);\n}\n/**\n * injects provided scripts into the document.body\n * @param {Object} script - an HTML script element\n * @return {Object} - the injected HTML script element\n */\n\n\nfunction injectScripts(script) {\n  return document.body.appendChild(script);\n}\n/**\n * creates an HTML script element with/without provided src\n * @param {string} [src] - the source path of the script\n * @return {Object} - the created HTML script element\n */\n\n\nfunction createScript(src) {\n  var script = document.createElement('script');\n  return src && (script.src = src), script;\n}\n/**\n * creates an HTML script element with the monaco loader src\n * @return {Object} - the created HTML script element\n */\n\n\nfunction getMonacoLoaderScript(configureLoader) {\n  var state = getState(function (_ref2) {\n    var config = _ref2.config,\n        reject = _ref2.reject;\n    return {\n      config: config,\n      reject: reject\n    };\n  });\n  var loaderScript = createScript(\"\".concat(state.config.paths.vs, \"/loader.js\"));\n\n  loaderScript.onload = function () {\n    return configureLoader();\n  };\n\n  loaderScript.onerror = state.reject;\n  return loaderScript;\n}\n/**\n * configures the monaco loader\n */\n\n\nfunction configureLoader() {\n  var state = getState(function (_ref3) {\n    var config = _ref3.config,\n        resolve = _ref3.resolve,\n        reject = _ref3.reject;\n    return {\n      config: config,\n      resolve: resolve,\n      reject: reject\n    };\n  });\n  var require = window.require;\n\n  require.config(state.config);\n\n  require(['vs/editor/editor.main'], function (monaco) {\n    storeMonacoInstance(monaco);\n    state.resolve(monaco);\n  }, function (error) {\n    state.reject(error);\n  });\n}\n/**\n * store monaco instance in local state\n */\n\n\nfunction storeMonacoInstance(monaco) {\n  if (!getState().monaco) {\n    setState({\n      monaco: monaco\n    });\n  }\n}\n/**\n * internal helper function\n * extracts stored monaco instance\n * @return {Object|null} - the monaco instance\n */\n\n\nfunction __getMonacoInstance() {\n  return getState(function (_ref4) {\n    var monaco = _ref4.monaco;\n    return monaco;\n  });\n}\n\nvar wrapperPromise = new Promise(function (resolve, reject) {\n  return setState({\n    resolve: resolve,\n    reject: reject\n  });\n});\nvar loader = {\n  config: config,\n  init: init,\n  __getMonacoInstance: __getMonacoInstance\n};\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (loader);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/loader/index.js\n"));

/***/ }),

/***/ "./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/compose.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/compose.js ***!
  \*******************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\nvar compose = function compose() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (x) {\n    return fns.reduceRight(function (y, f) {\n      return f(y);\n    }, x);\n  };\n};\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (compose);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvLnBucG0vQG1vbmFjby1lZGl0b3IrbG9hZGVyQDEuNS4wL25vZGVfbW9kdWxlcy9AbW9uYWNvLWVkaXRvci9sb2FkZXIvbGliL2VzL3V0aWxzL2NvbXBvc2UuanMiLCJtYXBwaW5ncyI6IjtBQUFBO0FBQ0EscUVBQXFFLGFBQWE7QUFDbEY7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQSwrREFBZSxPQUFPLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Btb25hY28tZWRpdG9yK2xvYWRlckAxLjUuMC9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9jb21wb3NlLmpzPzU4ZDgiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGNvbXBvc2UgPSBmdW5jdGlvbiBjb21wb3NlKCkge1xuICBmb3IgKHZhciBfbGVuID0gYXJndW1lbnRzLmxlbmd0aCwgZm5zID0gbmV3IEFycmF5KF9sZW4pLCBfa2V5ID0gMDsgX2tleSA8IF9sZW47IF9rZXkrKykge1xuICAgIGZuc1tfa2V5XSA9IGFyZ3VtZW50c1tfa2V5XTtcbiAgfVxuXG4gIHJldHVybiBmdW5jdGlvbiAoeCkge1xuICAgIHJldHVybiBmbnMucmVkdWNlUmlnaHQoZnVuY3Rpb24gKHksIGYpIHtcbiAgICAgIHJldHVybiBmKHkpO1xuICAgIH0sIHgpO1xuICB9O1xufTtcblxuZXhwb3J0IGRlZmF1bHQgY29tcG9zZTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/compose.js\n"));

/***/ }),

/***/ "./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/curry.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/curry.js ***!
  \*****************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\nfunction curry(fn) {\n  return function curried() {\n    var _this = this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return args.length >= fn.length ? fn.apply(this, args) : function () {\n      for (var _len2 = arguments.length, nextArgs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        nextArgs[_key2] = arguments[_key2];\n      }\n\n      return curried.apply(_this, [].concat(args, nextArgs));\n    };\n  };\n}\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (curry);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvLnBucG0vQG1vbmFjby1lZGl0b3IrbG9hZGVyQDEuNS4wL25vZGVfbW9kdWxlcy9AbW9uYWNvLWVkaXRvci9sb2FkZXIvbGliL2VzL3V0aWxzL2N1cnJ5LmpzIiwibWFwcGluZ3MiOiI7QUFBQTtBQUNBO0FBQ0E7O0FBRUEsd0VBQXdFLGFBQWE7QUFDckY7QUFDQTs7QUFFQTtBQUNBLGlGQUFpRixlQUFlO0FBQ2hHO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsK0RBQWUsS0FBSyxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AbW9uYWNvLWVkaXRvcitsb2FkZXJAMS41LjAvbm9kZV9tb2R1bGVzL0Btb25hY28tZWRpdG9yL2xvYWRlci9saWIvZXMvdXRpbHMvY3VycnkuanM/NTg3ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBjdXJyeShmbikge1xuICByZXR1cm4gZnVuY3Rpb24gY3VycmllZCgpIHtcbiAgICB2YXIgX3RoaXMgPSB0aGlzO1xuXG4gICAgZm9yICh2YXIgX2xlbiA9IGFyZ3VtZW50cy5sZW5ndGgsIGFyZ3MgPSBuZXcgQXJyYXkoX2xlbiksIF9rZXkgPSAwOyBfa2V5IDwgX2xlbjsgX2tleSsrKSB7XG4gICAgICBhcmdzW19rZXldID0gYXJndW1lbnRzW19rZXldO1xuICAgIH1cblxuICAgIHJldHVybiBhcmdzLmxlbmd0aCA+PSBmbi5sZW5ndGggPyBmbi5hcHBseSh0aGlzLCBhcmdzKSA6IGZ1bmN0aW9uICgpIHtcbiAgICAgIGZvciAodmFyIF9sZW4yID0gYXJndW1lbnRzLmxlbmd0aCwgbmV4dEFyZ3MgPSBuZXcgQXJyYXkoX2xlbjIpLCBfa2V5MiA9IDA7IF9rZXkyIDwgX2xlbjI7IF9rZXkyKyspIHtcbiAgICAgICAgbmV4dEFyZ3NbX2tleTJdID0gYXJndW1lbnRzW19rZXkyXTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIGN1cnJpZWQuYXBwbHkoX3RoaXMsIFtdLmNvbmNhdChhcmdzLCBuZXh0QXJncykpO1xuICAgIH07XG4gIH07XG59XG5cbmV4cG9ydCBkZWZhdWx0IGN1cnJ5O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/curry.js\n"));

/***/ }),

/***/ "./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js ***!
  \*********************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js\");\n\n\nfunction merge(target, source) {\n  Object.keys(source).forEach(function (key) {\n    if (source[key] instanceof Object) {\n      if (target[key]) {\n        Object.assign(source[key], merge(target[key], source[key]));\n      }\n    }\n  });\n  return (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__.objectSpread2)((0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__.objectSpread2)({}, target), source);\n}\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (merge);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvLnBucG0vQG1vbmFjby1lZGl0b3IrbG9hZGVyQDEuNS4wL25vZGVfbW9kdWxlcy9AbW9uYWNvLWVkaXRvci9sb2FkZXIvbGliL2VzL3V0aWxzL2RlZXBNZXJnZS5qcyIsIm1hcHBpbmdzIjoiOztBQUEyRjs7QUFFM0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsU0FBUyxtRkFBYyxDQUFDLG1GQUFjLEdBQUc7QUFDekM7O0FBRUEsK0RBQWUsS0FBSyxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AbW9uYWNvLWVkaXRvcitsb2FkZXJAMS41LjAvbm9kZV9tb2R1bGVzL0Btb25hY28tZWRpdG9yL2xvYWRlci9saWIvZXMvdXRpbHMvZGVlcE1lcmdlLmpzP2FjZGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgb2JqZWN0U3ByZWFkMiBhcyBfb2JqZWN0U3ByZWFkMiB9IGZyb20gJy4uL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanMnO1xuXG5mdW5jdGlvbiBtZXJnZSh0YXJnZXQsIHNvdXJjZSkge1xuICBPYmplY3Qua2V5cyhzb3VyY2UpLmZvckVhY2goZnVuY3Rpb24gKGtleSkge1xuICAgIGlmIChzb3VyY2Vba2V5XSBpbnN0YW5jZW9mIE9iamVjdCkge1xuICAgICAgaWYgKHRhcmdldFtrZXldKSB7XG4gICAgICAgIE9iamVjdC5hc3NpZ24oc291cmNlW2tleV0sIG1lcmdlKHRhcmdldFtrZXldLCBzb3VyY2Vba2V5XSkpO1xuICAgICAgfVxuICAgIH1cbiAgfSk7XG4gIHJldHVybiBfb2JqZWN0U3ByZWFkMihfb2JqZWN0U3ByZWFkMih7fSwgdGFyZ2V0KSwgc291cmNlKTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgbWVyZ2U7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js\n"));

/***/ }),

/***/ "./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/isObject.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/isObject.js ***!
  \********************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\nfunction isObject(value) {\n  return {}.toString.call(value).includes('Object');\n}\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (isObject);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvLnBucG0vQG1vbmFjby1lZGl0b3IrbG9hZGVyQDEuNS4wL25vZGVfbW9kdWxlcy9AbW9uYWNvLWVkaXRvci9sb2FkZXIvbGliL2VzL3V0aWxzL2lzT2JqZWN0LmpzIiwibWFwcGluZ3MiOiI7QUFBQTtBQUNBLFdBQVc7QUFDWDs7QUFFQSwrREFBZSxRQUFRLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Btb25hY28tZWRpdG9yK2xvYWRlckAxLjUuMC9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9pc09iamVjdC5qcz8zZjY1Il0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGlzT2JqZWN0KHZhbHVlKSB7XG4gIHJldHVybiB7fS50b1N0cmluZy5jYWxsKHZhbHVlKS5pbmNsdWRlcygnT2JqZWN0Jyk7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGlzT2JqZWN0O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/isObject.js\n"));

/***/ }),

/***/ "./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js ***!
  \**************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CANCELATION_MESSAGE: function() { return /* binding */ CANCELATION_MESSAGE; }\n/* harmony export */ });\n// The source (has been changed) is https://github.com/facebook/react/issues/5465#issuecomment-157888325\nvar CANCELATION_MESSAGE = {\n  type: 'cancelation',\n  msg: 'operation is manually canceled'\n};\n\nfunction makeCancelable(promise) {\n  var hasCanceled_ = false;\n  var wrappedPromise = new Promise(function (resolve, reject) {\n    promise.then(function (val) {\n      return hasCanceled_ ? reject(CANCELATION_MESSAGE) : resolve(val);\n    });\n    promise[\"catch\"](reject);\n  });\n  return wrappedPromise.cancel = function () {\n    return hasCanceled_ = true;\n  }, wrappedPromise;\n}\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (makeCancelable);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvLnBucG0vQG1vbmFjby1lZGl0b3IrbG9hZGVyQDEuNS4wL25vZGVfbW9kdWxlcy9AbW9uYWNvLWVkaXRvci9sb2FkZXIvbGliL2VzL3V0aWxzL21ha2VDYW5jZWxhYmxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLEdBQUc7QUFDSDs7QUFFQSwrREFBZSxjQUFjLEVBQUM7QUFDQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvLnBucG0vQG1vbmFjby1lZGl0b3IrbG9hZGVyQDEuNS4wL25vZGVfbW9kdWxlcy9AbW9uYWNvLWVkaXRvci9sb2FkZXIvbGliL2VzL3V0aWxzL21ha2VDYW5jZWxhYmxlLmpzPzBmMDUiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gVGhlIHNvdXJjZSAoaGFzIGJlZW4gY2hhbmdlZCkgaXMgaHR0cHM6Ly9naXRodWIuY29tL2ZhY2Vib29rL3JlYWN0L2lzc3Vlcy81NDY1I2lzc3VlY29tbWVudC0xNTc4ODgzMjVcbnZhciBDQU5DRUxBVElPTl9NRVNTQUdFID0ge1xuICB0eXBlOiAnY2FuY2VsYXRpb24nLFxuICBtc2c6ICdvcGVyYXRpb24gaXMgbWFudWFsbHkgY2FuY2VsZWQnXG59O1xuXG5mdW5jdGlvbiBtYWtlQ2FuY2VsYWJsZShwcm9taXNlKSB7XG4gIHZhciBoYXNDYW5jZWxlZF8gPSBmYWxzZTtcbiAgdmFyIHdyYXBwZWRQcm9taXNlID0gbmV3IFByb21pc2UoZnVuY3Rpb24gKHJlc29sdmUsIHJlamVjdCkge1xuICAgIHByb21pc2UudGhlbihmdW5jdGlvbiAodmFsKSB7XG4gICAgICByZXR1cm4gaGFzQ2FuY2VsZWRfID8gcmVqZWN0KENBTkNFTEFUSU9OX01FU1NBR0UpIDogcmVzb2x2ZSh2YWwpO1xuICAgIH0pO1xuICAgIHByb21pc2VbXCJjYXRjaFwiXShyZWplY3QpO1xuICB9KTtcbiAgcmV0dXJuIHdyYXBwZWRQcm9taXNlLmNhbmNlbCA9IGZ1bmN0aW9uICgpIHtcbiAgICByZXR1cm4gaGFzQ2FuY2VsZWRfID0gdHJ1ZTtcbiAgfSwgd3JhcHBlZFByb21pc2U7XG59XG5cbmV4cG9ydCBkZWZhdWx0IG1ha2VDYW5jZWxhYmxlO1xuZXhwb3J0IHsgQ0FOQ0VMQVRJT05fTUVTU0FHRSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js\n"));

/***/ }),

/***/ "./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/validators/index.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/validators/index.js ***!
  \**********************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   errorHandler: function() { return /* binding */ errorHandler; },\n/* harmony export */   errorMessages: function() { return /* binding */ errorMessages; }\n/* harmony export */ });\n/* harmony import */ var _utils_curry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/curry.js */ \"./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/curry.js\");\n/* harmony import */ var _utils_isObject_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/isObject.js */ \"./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/isObject.js\");\n\n\n\n/**\n * validates the configuration object and informs about deprecation\n * @param {Object} config - the configuration object \n * @return {Object} config - the validated configuration object\n */\n\nfunction validateConfig(config) {\n  if (!config) errorHandler('configIsRequired');\n  if (!(0,_utils_isObject_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(config)) errorHandler('configType');\n\n  if (config.urls) {\n    informAboutDeprecation();\n    return {\n      paths: {\n        vs: config.urls.monacoBase\n      }\n    };\n  }\n\n  return config;\n}\n/**\n * logs deprecation message\n */\n\n\nfunction informAboutDeprecation() {\n  console.warn(errorMessages.deprecation);\n}\n\nfunction throwError(errorMessages, type) {\n  throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\n\nvar errorMessages = {\n  configIsRequired: 'the configuration object is required',\n  configType: 'the configuration object should be an object',\n  \"default\": 'an unknown error accured in `@monaco-editor/loader` package',\n  deprecation: \"Deprecation warning!\\n    You are using deprecated way of configuration.\\n\\n    Instead of using\\n      monaco.config({ urls: { monacoBase: '...' } })\\n    use\\n      monaco.config({ paths: { vs: '...' } })\\n\\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\\n  \"\n};\nvar errorHandler = (0,_utils_curry_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(throwError)(errorMessages);\nvar validators = {\n  config: validateConfig\n};\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (validators);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/validators/index.js\n"));

/***/ }),

/***/ "./src/views/Coder/CoderView.tsx":
/*!***************************************!*\
  !*** ./src/views/Coder/CoderView.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CoderView: function() { return /* binding */ CoderView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"./node_modules/.pnpm/@monaco-editor+react@4.7.0__1ce2f918ff59bd31eee90d0f34fee1f7/node_modules/@monaco-editor/react/dist/index.mjs\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst FileTreeNode = (param)=>{\n    let { item, level, onFileClick } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(item.isOpen || false);\n    const handleClick = ()=>{\n        if (item.type === \"folder\") {\n            setIsOpen(!isOpen);\n        } else {\n            onFileClick(item.name);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2 px-2 py-1 hover:bg-gray-700/50 cursor-pointer text-sm\",\n                style: {\n                    paddingLeft: \"\".concat(level * 16 + 8, \"px\")\n                },\n                onClick: handleClick,\n                children: [\n                    item.type === \"folder\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-400\",\n                        children: isOpen ? \"\\uD83D\\uDCC2\" : \"\\uD83D\\uDCC1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, undefined),\n                    item.type === \"file\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-blue-400\",\n                        children: \"\\uD83D\\uDCC4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-300\",\n                        children: item.name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined),\n            item.type === \"folder\" && isOpen && item.children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: item.children.map((child, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileTreeNode, {\n                        item: child,\n                        level: level + 1,\n                        onFileClick: onFileClick\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 44,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FileTreeNode, \"1NLt9oXF2DSJYlKhLhMlvItPqek=\");\n_c = FileTreeNode;\nconst CoderView = ()=>{\n    _s1();\n    const [activeFile, setActiveFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"App.tsx\");\n    const [openFiles, setOpenFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"App.tsx\"\n    ]);\n    const [showTerminal, setShowTerminal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sidebarTab, setSidebarTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"files\");\n    const [files, setFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"App.tsx\": \"import React, { useState } from 'react';\\nimport './App.css';\\n\\nfunction App() {\\n  const [count, setCount] = useState(0);\\n\\n  return (\\n    <div className=\\\"App\\\">\\n      <header className=\\\"App-header\\\">\\n        <h1>Welcome to React IDE</h1>\\n        <p>Count: {count}</p>\\n        <button onClick={() => setCount(count + 1)}>\\n          Increment\\n        </button>\\n        <button onClick={() => setCount(count - 1)}>\\n          Decrement\\n        </button>\\n      </header>\\n    </div>\\n  );\\n}\\n\\nexport default App;\",\n        \"App.css\": \"body {\\n  margin: 0;\\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\\n    sans-serif;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n.App {\\n  text-align: center;\\n}\\n\\n.App-header {\\n  background-color: #282c34;\\n  padding: 20px;\\n  color: white;\\n  min-height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: calc(10px + 2vmin);\\n}\\n\\nbutton {\\n  background-color: #61dafb;\\n  border: none;\\n  padding: 10px 20px;\\n  margin: 10px;\\n  border-radius: 5px;\\n  cursor: pointer;\\n  font-size: 16px;\\n}\\n\\nbutton:hover {\\n  background-color: #21a1c4;\\n}\",\n        \"package.json\": '{\\n  \"name\": \"react-ide-project\",\\n  \"version\": \"1.0.0\",\\n  \"private\": true,\\n  \"dependencies\": {\\n    \"react\": \"^18.2.0\",\\n    \"react-dom\": \"^18.2.0\",\\n    \"typescript\": \"^4.9.5\"\\n  },\\n  \"scripts\": {\\n    \"start\": \"react-scripts start\",\\n    \"build\": \"react-scripts build\",\\n    \"test\": \"react-scripts test\",\\n    \"eject\": \"react-scripts eject\"\\n  },\\n  \"eslintConfig\": {\\n    \"extends\": [\\n      \"react-app\",\\n      \"react-app/jest\"\\n    ]\\n  },\\n  \"browserslist\": {\\n    \"production\": [\\n      \">0.2%\",\\n      \"not dead\",\\n      \"not op_mini all\"\\n    ],\\n    \"development\": [\\n      \"last 1 chrome version\",\\n      \"last 1 firefox version\",\\n      \"last 1 safari version\"\\n    ]\\n  }\\n}',\n        \"README.md\": \"# React IDE Project\\n\\nThis is a sample React project created in the IDE.\\n\\n## Available Scripts\\n\\n- `npm start` - Runs the app in development mode\\n- `npm build` - Builds the app for production\\n- `npm test` - Launches the test runner\\n\\n## Features\\n\\n- React with TypeScript\\n- Modern CSS styling\\n- Interactive components\\n- Hot reloading\\n\\n## Getting Started\\n\\n1. Install dependencies: `npm install`\\n2. Start the development server: `npm start`\\n3. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.\\n\"\n    });\n    const editorRef = useRef(null);\n    const fileTree = [\n        {\n            name: \"src\",\n            type: \"folder\",\n            isOpen: true,\n            children: [\n                {\n                    name: \"App.tsx\",\n                    type: \"file\"\n                },\n                {\n                    name: \"App.css\",\n                    type: \"file\"\n                },\n                {\n                    name: \"index.tsx\",\n                    type: \"file\"\n                }\n            ]\n        },\n        {\n            name: \"public\",\n            type: \"folder\",\n            children: [\n                {\n                    name: \"index.html\",\n                    type: \"file\"\n                },\n                {\n                    name: \"favicon.ico\",\n                    type: \"file\"\n                }\n            ]\n        },\n        {\n            name: \"package.json\",\n            type: \"file\"\n        },\n        {\n            name: \"README.md\",\n            type: \"file\"\n        }\n    ];\n    const handleEditorDidMount = (editor, monaco)=>{\n        editorRef.current = editor;\n        // Configure Monaco Editor\n        monaco.editor.defineTheme(\"vs-dark-custom\", {\n            base: \"vs-dark\",\n            inherit: true,\n            rules: [],\n            colors: {\n                \"editor.background\": \"#1e1e1e\",\n                \"editor.foreground\": \"#d4d4d4\",\n                \"editorLineNumber.foreground\": \"#858585\",\n                \"editor.selectionBackground\": \"#264f78\",\n                \"editor.inactiveSelectionBackground\": \"#3a3d41\"\n            }\n        });\n        monaco.editor.setTheme(\"vs-dark-custom\");\n    };\n    const handleEditorChange = (value)=>{\n        if (value !== undefined && activeFile) {\n            setFiles((prev)=>({\n                    ...prev,\n                    [activeFile]: value\n                }));\n        }\n    };\n    const handleFileClick = (fileName)=>{\n        // Only open files that exist in our files object\n        if (files[fileName]) {\n            setActiveFile(fileName);\n            if (!openFiles.includes(fileName)) {\n                setOpenFiles([\n                    ...openFiles,\n                    fileName\n                ]);\n            }\n        }\n    };\n    const closeFile = (fileName)=>{\n        const newOpenFiles = openFiles.filter((f)=>f !== fileName);\n        setOpenFiles(newOpenFiles);\n        if (activeFile === fileName && newOpenFiles.length > 0) {\n            setActiveFile(newOpenFiles[newOpenFiles.length - 1]);\n        } else if (newOpenFiles.length === 0) {\n            setActiveFile(\"\");\n        }\n    };\n    const getFileLanguage = (fileName)=>{\n        const ext = fileName.split(\".\").pop();\n        switch(ext){\n            case \"tsx\":\n            case \"ts\":\n                return \"typescript\";\n            case \"jsx\":\n            case \"js\":\n                return \"javascript\";\n            case \"css\":\n                return \"css\";\n            case \"json\":\n                return \"json\";\n            case \"md\":\n                return \"markdown\";\n            case \"html\":\n                return \"html\";\n            default:\n                return \"plaintext\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-panel-bg rounded-xl overflow-hidden flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-64 bg-gray-900/50 border-r border-gray-700/50 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex border-b border-gray-700/50\",\n                        children: [\n                            {\n                                id: \"files\",\n                                icon: \"\\uD83D\\uDCC1\",\n                                label: \"Files\"\n                            },\n                            {\n                                id: \"git\",\n                                icon: \"\\uD83D\\uDD00\",\n                                label: \"Git\"\n                            },\n                            {\n                                id: \"extensions\",\n                                icon: \"\\uD83E\\uDDE9\",\n                                label: \"Extensions\"\n                            }\n                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSidebarTab(tab.id),\n                                className: \"flex-1 p-3 text-sm font-medium transition-colors duration-200 \".concat(sidebarTab === tab.id ? \"bg-blue-500/20 text-blue-400 border-b-2 border-blue-400\" : \"text-gray-400 hover:text-white hover:bg-gray-800/60\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-2\",\n                                        children: tab.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    tab.label\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: [\n                            sidebarTab === \"files\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-gray-400 text-xs uppercase font-semibold mb-2 px-2\",\n                                        children: \"Explorer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    fileTree.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileTreeNode, {\n                                            item: item,\n                                            level: 0,\n                                            onFileClick: handleFileClick\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 17\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, undefined),\n                            sidebarTab === \"git\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-white font-semibold mb-4\",\n                                        children: \"Source Control\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-green-400 text-sm\",\n                                                children: \"✓ 3 files staged\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-yellow-400 text-sm\",\n                                                children: \"⚠ 2 files modified\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-red-400 text-sm\",\n                                                children: \"✗ 1 file deleted\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, undefined),\n                            sidebarTab === \"extensions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-white font-semibold mb-4\",\n                                        children: \"Extensions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-blue-500 rounded flex items-center justify-center text-xs\",\n                                                        children: \"TS\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white text-sm\",\n                                                                children: \"TypeScript\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-400 text-xs\",\n                                                                children: \"Installed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-green-500 rounded flex items-center justify-center text-xs\",\n                                                        children: \"ES\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white text-sm\",\n                                                                children: \"ESLint\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-400 text-xs\",\n                                                                children: \"Installed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-800/40 border-b border-gray-700/50\",\n                        children: openFiles.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 px-4 py-2 border-r border-gray-700/50 cursor-pointer \".concat(activeFile === file ? \"bg-panel-bg text-white\" : \"text-gray-400 hover:text-white hover:bg-gray-800/60\"),\n                                onClick: ()=>setActiveFile(file),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: file\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            closeFile(file);\n                                        },\n                                        className: \"text-gray-500 hover:text-white\",\n                                        children: \"\\xd7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, file, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                            lineNumber: 387,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 9\n                    }, undefined),\n                    showTerminal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-48 bg-black border-t border-gray-700/50 flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between px-4 py-2 bg-gray-800/60 border-b border-gray-700/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-semibold text-sm\",\n                                                children: \"Terminal\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400 text-xs\",\n                                                children: \"bash\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowTerminal(false),\n                                            className: \"text-gray-400 hover:text-white text-sm px-2 py-1 rounded hover:bg-gray-700/50\",\n                                            children: \"\\xd7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-4 overflow-y-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-green-400 font-mono text-sm space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-400\",\n                                            children: \"Welcome to React IDE Terminal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"$ npm install\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-300\",\n                                            children: \"✓ Dependencies installed successfully\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"$ npm start\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-300\",\n                                            children: \"Starting development server...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-blue-400\",\n                                            children: \"Local:    http://localhost:3000\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-blue-400\",\n                                            children: \"Network:  http://*************:3000\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-300\",\n                                            children: \"webpack compiled successfully\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400\",\n                                                    children: \"$ \"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-transparent border-none outline-none text-green-400 ml-1 animate-pulse\",\n                                                    children: \"|\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 358,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-80 bg-gray-900/50 border-l border-gray-700/50 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-white font-semibold mb-4\",\n                        children: \"\\uD83E\\uDD16 AI Assistant\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800/40 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-400 text-sm font-medium mb-1\",\n                                        children: \"Code Suggestion\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Consider adding error handling to your React component.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800/40 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-green-400 text-sm font-medium mb-1\",\n                                        children: \"Performance Tip\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Use React.memo() to optimize component re-renders.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowTerminal(!showTerminal),\n                            className: \"w-full bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 py-2 px-4 rounded-lg transition-colors duration-200\",\n                            children: showTerminal ? \"Hide Terminal\" : \"Show Terminal\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                        lineNumber: 445,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n                lineNumber: 428,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Coder\\\\CoderView.tsx\",\n        lineNumber: 281,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CoderView, \"ulaYZ8G61Myo3h5ijmvuoCZ0cGQ=\");\n_c1 = CoderView;\nvar _c, _c1;\n$RefreshReg$(_c, \"FileTreeNode\");\n$RefreshReg$(_c1, \"CoderView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvdmlld3MvQ29kZXIvQ29kZXJWaWV3LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUF3QztBQUNFO0FBUzFDLE1BQU1HLGVBQXFHO1FBQUMsRUFDMUdDLElBQUksRUFDSkMsS0FBSyxFQUNMQyxXQUFXLEVBQ1o7O0lBQ0MsTUFBTSxDQUFDQyxRQUFRQyxVQUFVLEdBQUdQLCtDQUFRQSxDQUFDRyxLQUFLRyxNQUFNLElBQUk7SUFFcEQsTUFBTUUsY0FBYztRQUNsQixJQUFJTCxLQUFLTSxJQUFJLEtBQUssVUFBVTtZQUMxQkYsVUFBVSxDQUFDRDtRQUNiLE9BQU87WUFDTEQsWUFBWUYsS0FBS08sSUFBSTtRQUN2QjtJQUNGO0lBRUEscUJBQ0UsOERBQUNDOzswQkFDQyw4REFBQ0E7Z0JBQ0NDLFdBQVU7Z0JBQ1ZDLE9BQU87b0JBQUVDLGFBQWEsR0FBa0IsT0FBZlYsUUFBUSxLQUFLLEdBQUU7Z0JBQUk7Z0JBQzVDVyxTQUFTUDs7b0JBRVJMLEtBQUtNLElBQUksS0FBSywwQkFDYiw4REFBQ087d0JBQUtKLFdBQVU7a0NBQ2JOLFNBQVMsaUJBQU87Ozs7OztvQkFHcEJILEtBQUtNLElBQUksS0FBSyx3QkFDYiw4REFBQ087d0JBQUtKLFdBQVU7a0NBQWdCOzs7Ozs7a0NBRWxDLDhEQUFDSTt3QkFBS0osV0FBVTtrQ0FBaUJULEtBQUtPLElBQUk7Ozs7Ozs7Ozs7OztZQUUzQ1AsS0FBS00sSUFBSSxLQUFLLFlBQVlILFVBQVVILEtBQUtjLFFBQVEsa0JBQ2hELDhEQUFDTjswQkFDRVIsS0FBS2MsUUFBUSxDQUFDQyxHQUFHLENBQUMsQ0FBQ0MsT0FBT0Msc0JBQ3pCLDhEQUFDbEI7d0JBRUNDLE1BQU1nQjt3QkFDTmYsT0FBT0EsUUFBUTt3QkFDZkMsYUFBYUE7dUJBSFJlOzs7Ozs7Ozs7Ozs7Ozs7O0FBVW5CO0dBOUNNbEI7S0FBQUE7QUFnREMsTUFBTW1CLFlBQXNCOztJQUNqQyxNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR3ZCLCtDQUFRQSxDQUFTO0lBQ3JELE1BQU0sQ0FBQ3dCLFdBQVdDLGFBQWEsR0FBR3pCLCtDQUFRQSxDQUFXO1FBQUM7S0FBVTtJQUNoRSxNQUFNLENBQUMwQixjQUFjQyxnQkFBZ0IsR0FBRzNCLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQzRCLFlBQVlDLGNBQWMsR0FBRzdCLCtDQUFRQSxDQUFpQztJQUM3RSxNQUFNLENBQUM4QixPQUFPQyxTQUFTLEdBQUcvQiwrQ0FBUUEsQ0FBeUI7UUFDekQsV0FBWTtRQXVCWixXQUFZO1FBc0NaLGdCQUFpQjtRQWtDakIsYUFBYztJQXVCaEI7SUFDQSxNQUFNZ0MsWUFBWUMsT0FBWTtJQUU5QixNQUFNQyxXQUEyQjtRQUMvQjtZQUNFeEIsTUFBTTtZQUNORCxNQUFNO1lBQ05ILFFBQVE7WUFDUlcsVUFBVTtnQkFDUjtvQkFBRVAsTUFBTTtvQkFBV0QsTUFBTTtnQkFBTztnQkFDaEM7b0JBQUVDLE1BQU07b0JBQVdELE1BQU07Z0JBQU87Z0JBQ2hDO29CQUFFQyxNQUFNO29CQUFhRCxNQUFNO2dCQUFPO2FBQ25DO1FBQ0g7UUFDQTtZQUNFQyxNQUFNO1lBQ05ELE1BQU07WUFDTlEsVUFBVTtnQkFDUjtvQkFBRVAsTUFBTTtvQkFBY0QsTUFBTTtnQkFBTztnQkFDbkM7b0JBQUVDLE1BQU07b0JBQWVELE1BQU07Z0JBQU87YUFDckM7UUFDSDtRQUNBO1lBQUVDLE1BQU07WUFBZ0JELE1BQU07UUFBTztRQUNyQztZQUFFQyxNQUFNO1lBQWFELE1BQU07UUFBTztLQUNuQztJQUVELE1BQU0wQix1QkFBdUIsQ0FBQ0MsUUFBYUM7UUFDekNMLFVBQVVNLE9BQU8sR0FBR0Y7UUFFcEIsMEJBQTBCO1FBQzFCQyxPQUFPRCxNQUFNLENBQUNHLFdBQVcsQ0FBQyxrQkFBa0I7WUFDMUNDLE1BQU07WUFDTkMsU0FBUztZQUNUQyxPQUFPLEVBQUU7WUFDVEMsUUFBUTtnQkFDTixxQkFBcUI7Z0JBQ3JCLHFCQUFxQjtnQkFDckIsK0JBQStCO2dCQUMvQiw4QkFBOEI7Z0JBQzlCLHNDQUFzQztZQUN4QztRQUNGO1FBRUFOLE9BQU9ELE1BQU0sQ0FBQ1EsUUFBUSxDQUFDO0lBQ3pCO0lBRUEsTUFBTUMscUJBQXFCLENBQUNDO1FBQzFCLElBQUlBLFVBQVVDLGFBQWF6QixZQUFZO1lBQ3JDUyxTQUFTaUIsQ0FBQUEsT0FBUztvQkFDaEIsR0FBR0EsSUFBSTtvQkFDUCxDQUFDMUIsV0FBVyxFQUFFd0I7Z0JBQ2hCO1FBQ0Y7SUFDRjtJQUVBLE1BQU1HLGtCQUFrQixDQUFDQztRQUN2QixpREFBaUQ7UUFDakQsSUFBSXBCLEtBQUssQ0FBQ29CLFNBQVMsRUFBRTtZQUNuQjNCLGNBQWMyQjtZQUNkLElBQUksQ0FBQzFCLFVBQVUyQixRQUFRLENBQUNELFdBQVc7Z0JBQ2pDekIsYUFBYTt1QkFBSUQ7b0JBQVcwQjtpQkFBUztZQUN2QztRQUNGO0lBQ0Y7SUFFQSxNQUFNRSxZQUFZLENBQUNGO1FBQ2pCLE1BQU1HLGVBQWU3QixVQUFVOEIsTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxNQUFNTDtRQUNqRHpCLGFBQWE0QjtRQUNiLElBQUkvQixlQUFlNEIsWUFBWUcsYUFBYUcsTUFBTSxHQUFHLEdBQUc7WUFDdERqQyxjQUFjOEIsWUFBWSxDQUFDQSxhQUFhRyxNQUFNLEdBQUcsRUFBRTtRQUNyRCxPQUFPLElBQUlILGFBQWFHLE1BQU0sS0FBSyxHQUFHO1lBQ3BDakMsY0FBYztRQUNoQjtJQUNGO0lBRUEsTUFBTWtDLGtCQUFrQixDQUFDUDtRQUN2QixNQUFNUSxNQUFNUixTQUFTUyxLQUFLLENBQUMsS0FBS0MsR0FBRztRQUNuQyxPQUFRRjtZQUNOLEtBQUs7WUFDTCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO1lBQ0wsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1Q7Z0JBQ0UsT0FBTztRQUNYO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQy9DO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNaOzRCQUNDO2dDQUFFaUQsSUFBSTtnQ0FBU0MsTUFBTTtnQ0FBTUMsT0FBTzs0QkFBUTs0QkFDMUM7Z0NBQUVGLElBQUk7Z0NBQU9DLE1BQU07Z0NBQU1DLE9BQU87NEJBQU07NEJBQ3RDO2dDQUFFRixJQUFJO2dDQUFjQyxNQUFNO2dDQUFNQyxPQUFPOzRCQUFhO3lCQUNyRCxDQUFDN0MsR0FBRyxDQUFDLENBQUM4QyxvQkFDTCw4REFBQ0M7Z0NBRUNsRCxTQUFTLElBQU1jLGNBQWNtQyxJQUFJSCxFQUFFO2dDQUNuQ2pELFdBQVcsaUVBSVYsT0FIQ2dCLGVBQWVvQyxJQUFJSCxFQUFFLEdBQ2pCLDREQUNBOztrREFHTiw4REFBQzdDO3dDQUFLSixXQUFVO2tEQUFRb0QsSUFBSUYsSUFBSTs7Ozs7O29DQUMvQkUsSUFBSUQsS0FBSzs7K0JBVExDLElBQUlILEVBQUU7Ozs7Ozs7Ozs7a0NBZWpCLDhEQUFDbEQ7d0JBQUlDLFdBQVU7OzRCQUNaZ0IsZUFBZSx5QkFDZCw4REFBQ2pCO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3NEO3dDQUFHdEQsV0FBVTtrREFBMEQ7Ozs7OztvQ0FDdkVzQixTQUFTaEIsR0FBRyxDQUFDLENBQUNmLE1BQU1pQixzQkFDbkIsOERBQUNsQjs0Q0FFQ0MsTUFBTUE7NENBQ05DLE9BQU87NENBQ1BDLGFBQWE0QzsyQ0FIUjdCOzs7Ozs7Ozs7Ozs0QkFTWlEsZUFBZSx1QkFDZCw4REFBQ2pCO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3NEO3dDQUFHdEQsV0FBVTtrREFBZ0M7Ozs7OztrREFDOUMsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQXlCOzs7Ozs7MERBQ3hDLDhEQUFDRDtnREFBSUMsV0FBVTswREFBMEI7Ozs7OzswREFDekMsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUF1Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzRCQUszQ2dCLGVBQWUsOEJBQ2QsOERBQUNqQjtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNzRDt3Q0FBR3RELFdBQVU7a0RBQWdDOzs7Ozs7a0RBQzlDLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7a0VBQXVFOzs7Ozs7a0VBQ3RGLDhEQUFDRDs7MEVBQ0MsOERBQUNBO2dFQUFJQyxXQUFVOzBFQUFxQjs7Ozs7OzBFQUNwQyw4REFBQ0Q7Z0VBQUlDLFdBQVU7MEVBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBRzNDLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEO3dEQUFJQyxXQUFVO2tFQUF3RTs7Ozs7O2tFQUN2Riw4REFBQ0Q7OzBFQUNDLDhEQUFDQTtnRUFBSUMsV0FBVTswRUFBcUI7Ozs7OzswRUFDcEMsOERBQUNEO2dFQUFJQyxXQUFVOzBFQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVVyRCw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDWlksVUFBVU4sR0FBRyxDQUFDLENBQUNpRCxxQkFDZCw4REFBQ3hEO2dDQUVDQyxXQUFXLGdGQUlWLE9BSENVLGVBQWU2QyxPQUNYLDJCQUNBO2dDQUVOcEQsU0FBUyxJQUFNUSxjQUFjNEM7O2tEQUU3Qiw4REFBQ25EO2tEQUFNbUQ7Ozs7OztrREFDUCw4REFBQ0Y7d0NBQ0NsRCxTQUFTLENBQUNxRDs0Q0FDUkEsRUFBRUMsZUFBZTs0Q0FDakJqQixVQUFVZTt3Q0FDWjt3Q0FDQXZELFdBQVU7a0RBQ1g7Ozs7Ozs7K0JBZkl1RDs7Ozs7Ozs7OztrQ0F1QlgsOERBQUN4RDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ1gsNERBQU1BOzs7Ozs7Ozs7O29CQUlSeUIsOEJBQ0MsOERBQUNmO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNJO2dEQUFLSixXQUFVOzBEQUFtQzs7Ozs7OzBEQUNuRCw4REFBQ0k7Z0RBQUtKLFdBQVU7MERBQXdCOzs7Ozs7Ozs7Ozs7a0RBRTFDLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ3FEOzRDQUNDbEQsU0FBUyxJQUFNWSxnQkFBZ0I7NENBQy9CZixXQUFVO3NEQUNYOzs7Ozs7Ozs7Ozs7Ozs7OzswQ0FLTCw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQWdCOzs7Ozs7c0RBQy9CLDhEQUFDRDtzREFBSTs7Ozs7O3NEQUNMLDhEQUFDQTs0Q0FBSUMsV0FBVTtzREFBZ0I7Ozs7OztzREFDL0IsOERBQUNEO3NEQUFJOzs7Ozs7c0RBQ0wsOERBQUNBOzRDQUFJQyxXQUFVO3NEQUFnQjs7Ozs7O3NEQUMvQiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQWdCOzs7Ozs7c0RBQy9CLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFBZ0I7Ozs7OztzREFDL0IsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUFnQjs7Ozs7O3NEQUMvQiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDSTtvREFBS0osV0FBVTs4REFBaUI7Ozs7Ozs4REFDakMsOERBQUNJO29EQUFLSixXQUFVOzhEQUE0RTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBU3hHLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNzRDt3QkFBR3RELFdBQVU7a0NBQWdDOzs7Ozs7a0NBQzlDLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQXlDOzs7Ozs7a0RBQ3hELDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFBd0I7Ozs7Ozs7Ozs7OzswQ0FJekMsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQTBDOzs7Ozs7a0RBQ3pELDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNM0MsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDcUQ7NEJBQ0NsRCxTQUFTLElBQU1ZLGdCQUFnQixDQUFDRDs0QkFDaENkLFdBQVU7c0NBRVRjLGVBQWUsa0JBQWtCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU05QyxFQUFFO0lBN1lXTDtNQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvdmlld3MvQ29kZXIvQ29kZXJWaWV3LnRzeD82Y2U4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBFZGl0b3IgZnJvbSAnQG1vbmFjby1lZGl0b3IvcmVhY3QnO1xuXG5pbnRlcmZhY2UgRmlsZVRyZWVJdGVtIHtcbiAgbmFtZTogc3RyaW5nO1xuICB0eXBlOiAnZmlsZScgfCAnZm9sZGVyJztcbiAgY2hpbGRyZW4/OiBGaWxlVHJlZUl0ZW1bXTtcbiAgaXNPcGVuPzogYm9vbGVhbjtcbn1cblxuY29uc3QgRmlsZVRyZWVOb2RlOiBSZWFjdC5GQzx7IGl0ZW06IEZpbGVUcmVlSXRlbTsgbGV2ZWw6IG51bWJlcjsgb25GaWxlQ2xpY2s6IChuYW1lOiBzdHJpbmcpID0+IHZvaWQgfT4gPSAoeyBcbiAgaXRlbSwgXG4gIGxldmVsLCBcbiAgb25GaWxlQ2xpY2sgXG59KSA9PiB7XG4gIGNvbnN0IFtpc09wZW4sIHNldElzT3Blbl0gPSB1c2VTdGF0ZShpdGVtLmlzT3BlbiB8fCBmYWxzZSk7XG5cbiAgY29uc3QgaGFuZGxlQ2xpY2sgPSAoKSA9PiB7XG4gICAgaWYgKGl0ZW0udHlwZSA9PT0gJ2ZvbGRlcicpIHtcbiAgICAgIHNldElzT3BlbighaXNPcGVuKTtcbiAgICB9IGVsc2Uge1xuICAgICAgb25GaWxlQ2xpY2soaXRlbS5uYW1lKTtcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2PlxuICAgICAgPGRpdlxuICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBweC0yIHB5LTEgaG92ZXI6YmctZ3JheS03MDAvNTAgY3Vyc29yLXBvaW50ZXIgdGV4dC1zbVwiXG4gICAgICAgIHN0eWxlPXt7IHBhZGRpbmdMZWZ0OiBgJHtsZXZlbCAqIDE2ICsgOH1weGAgfX1cbiAgICAgICAgb25DbGljaz17aGFuZGxlQ2xpY2t9XG4gICAgICA+XG4gICAgICAgIHtpdGVtLnR5cGUgPT09ICdmb2xkZXInICYmIChcbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICB7aXNPcGVuID8gJ/Cfk4InIDogJ/Cfk4EnfVxuICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgKX1cbiAgICAgICAge2l0ZW0udHlwZSA9PT0gJ2ZpbGUnICYmIChcbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNDAwXCI+8J+ThDwvc3Bhbj5cbiAgICAgICAgKX1cbiAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMFwiPntpdGVtLm5hbWV9PC9zcGFuPlxuICAgICAgPC9kaXY+XG4gICAgICB7aXRlbS50eXBlID09PSAnZm9sZGVyJyAmJiBpc09wZW4gJiYgaXRlbS5jaGlsZHJlbiAmJiAoXG4gICAgICAgIDxkaXY+XG4gICAgICAgICAge2l0ZW0uY2hpbGRyZW4ubWFwKChjaGlsZCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgIDxGaWxlVHJlZU5vZGVcbiAgICAgICAgICAgICAga2V5PXtpbmRleH1cbiAgICAgICAgICAgICAgaXRlbT17Y2hpbGR9XG4gICAgICAgICAgICAgIGxldmVsPXtsZXZlbCArIDF9XG4gICAgICAgICAgICAgIG9uRmlsZUNsaWNrPXtvbkZpbGVDbGlja31cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBjb25zdCBDb2RlclZpZXc6IFJlYWN0LkZDID0gKCkgPT4ge1xuICBjb25zdCBbYWN0aXZlRmlsZSwgc2V0QWN0aXZlRmlsZV0gPSB1c2VTdGF0ZTxzdHJpbmc+KCdBcHAudHN4Jyk7XG4gIGNvbnN0IFtvcGVuRmlsZXMsIHNldE9wZW5GaWxlc10gPSB1c2VTdGF0ZTxzdHJpbmdbXT4oWydBcHAudHN4J10pO1xuICBjb25zdCBbc2hvd1Rlcm1pbmFsLCBzZXRTaG93VGVybWluYWxdID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtzaWRlYmFyVGFiLCBzZXRTaWRlYmFyVGFiXSA9IHVzZVN0YXRlPCdmaWxlcycgfCAnZ2l0JyB8ICdleHRlbnNpb25zJz4oJ2ZpbGVzJyk7XG4gIGNvbnN0IFtmaWxlcywgc2V0RmlsZXNdID0gdXNlU3RhdGU8UmVjb3JkPHN0cmluZywgc3RyaW5nPj4oe1xuICAgICdBcHAudHN4JzogYGltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCAnLi9BcHAuY3NzJztcblxuZnVuY3Rpb24gQXBwKCkge1xuICBjb25zdCBbY291bnQsIHNldENvdW50XSA9IHVzZVN0YXRlKDApO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJBcHBcIj5cbiAgICAgIDxoZWFkZXIgY2xhc3NOYW1lPVwiQXBwLWhlYWRlclwiPlxuICAgICAgICA8aDE+V2VsY29tZSB0byBSZWFjdCBJREU8L2gxPlxuICAgICAgICA8cD5Db3VudDoge2NvdW50fTwvcD5cbiAgICAgICAgPGJ1dHRvbiBvbkNsaWNrPXsoKSA9PiBzZXRDb3VudChjb3VudCArIDEpfT5cbiAgICAgICAgICBJbmNyZW1lbnRcbiAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDxidXR0b24gb25DbGljaz17KCkgPT4gc2V0Q291bnQoY291bnQgLSAxKX0+XG4gICAgICAgICAgRGVjcmVtZW50XG4gICAgICAgIDwvYnV0dG9uPlxuICAgICAgPC9oZWFkZXI+XG4gICAgPC9kaXY+XG4gICk7XG59XG5cbmV4cG9ydCBkZWZhdWx0IEFwcDtgLFxuICAgICdBcHAuY3NzJzogYGJvZHkge1xuICBtYXJnaW46IDA7XG4gIGZvbnQtZmFtaWx5OiAtYXBwbGUtc3lzdGVtLCBCbGlua01hY1N5c3RlbUZvbnQsICdTZWdvZSBVSScsICdSb2JvdG8nLCAnT3h5Z2VuJyxcbiAgICAnVWJ1bnR1JywgJ0NhbnRhcmVsbCcsICdGaXJhIFNhbnMnLCAnRHJvaWQgU2FucycsICdIZWx2ZXRpY2EgTmV1ZScsXG4gICAgc2Fucy1zZXJpZjtcbiAgLXdlYmtpdC1mb250LXNtb290aGluZzogYW50aWFsaWFzZWQ7XG4gIC1tb3otb3N4LWZvbnQtc21vb3RoaW5nOiBncmF5c2NhbGU7XG59XG5cbi5BcHAge1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG59XG5cbi5BcHAtaGVhZGVyIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogIzI4MmMzNDtcbiAgcGFkZGluZzogMjBweDtcbiAgY29sb3I6IHdoaXRlO1xuICBtaW4taGVpZ2h0OiAxMDB2aDtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIGZvbnQtc2l6ZTogY2FsYygxMHB4ICsgMnZtaW4pO1xufVxuXG5idXR0b24ge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjNjFkYWZiO1xuICBib3JkZXI6IG5vbmU7XG4gIHBhZGRpbmc6IDEwcHggMjBweDtcbiAgbWFyZ2luOiAxMHB4O1xuICBib3JkZXItcmFkaXVzOiA1cHg7XG4gIGN1cnNvcjogcG9pbnRlcjtcbiAgZm9udC1zaXplOiAxNnB4O1xufVxuXG5idXR0b246aG92ZXIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjMjFhMWM0O1xufWAsXG4gICAgJ3BhY2thZ2UuanNvbic6IGB7XG4gIFwibmFtZVwiOiBcInJlYWN0LWlkZS1wcm9qZWN0XCIsXG4gIFwidmVyc2lvblwiOiBcIjEuMC4wXCIsXG4gIFwicHJpdmF0ZVwiOiB0cnVlLFxuICBcImRlcGVuZGVuY2llc1wiOiB7XG4gICAgXCJyZWFjdFwiOiBcIl4xOC4yLjBcIixcbiAgICBcInJlYWN0LWRvbVwiOiBcIl4xOC4yLjBcIixcbiAgICBcInR5cGVzY3JpcHRcIjogXCJeNC45LjVcIlxuICB9LFxuICBcInNjcmlwdHNcIjoge1xuICAgIFwic3RhcnRcIjogXCJyZWFjdC1zY3JpcHRzIHN0YXJ0XCIsXG4gICAgXCJidWlsZFwiOiBcInJlYWN0LXNjcmlwdHMgYnVpbGRcIixcbiAgICBcInRlc3RcIjogXCJyZWFjdC1zY3JpcHRzIHRlc3RcIixcbiAgICBcImVqZWN0XCI6IFwicmVhY3Qtc2NyaXB0cyBlamVjdFwiXG4gIH0sXG4gIFwiZXNsaW50Q29uZmlnXCI6IHtcbiAgICBcImV4dGVuZHNcIjogW1xuICAgICAgXCJyZWFjdC1hcHBcIixcbiAgICAgIFwicmVhY3QtYXBwL2plc3RcIlxuICAgIF1cbiAgfSxcbiAgXCJicm93c2Vyc2xpc3RcIjoge1xuICAgIFwicHJvZHVjdGlvblwiOiBbXG4gICAgICBcIj4wLjIlXCIsXG4gICAgICBcIm5vdCBkZWFkXCIsXG4gICAgICBcIm5vdCBvcF9taW5pIGFsbFwiXG4gICAgXSxcbiAgICBcImRldmVsb3BtZW50XCI6IFtcbiAgICAgIFwibGFzdCAxIGNocm9tZSB2ZXJzaW9uXCIsXG4gICAgICBcImxhc3QgMSBmaXJlZm94IHZlcnNpb25cIixcbiAgICAgIFwibGFzdCAxIHNhZmFyaSB2ZXJzaW9uXCJcbiAgICBdXG4gIH1cbn1gLFxuICAgICdSRUFETUUubWQnOiBgIyBSZWFjdCBJREUgUHJvamVjdFxuXG5UaGlzIGlzIGEgc2FtcGxlIFJlYWN0IHByb2plY3QgY3JlYXRlZCBpbiB0aGUgSURFLlxuXG4jIyBBdmFpbGFibGUgU2NyaXB0c1xuXG4tIFxcYG5wbSBzdGFydFxcYCAtIFJ1bnMgdGhlIGFwcCBpbiBkZXZlbG9wbWVudCBtb2RlXG4tIFxcYG5wbSBidWlsZFxcYCAtIEJ1aWxkcyB0aGUgYXBwIGZvciBwcm9kdWN0aW9uXG4tIFxcYG5wbSB0ZXN0XFxgIC0gTGF1bmNoZXMgdGhlIHRlc3QgcnVubmVyXG5cbiMjIEZlYXR1cmVzXG5cbi0gUmVhY3Qgd2l0aCBUeXBlU2NyaXB0XG4tIE1vZGVybiBDU1Mgc3R5bGluZ1xuLSBJbnRlcmFjdGl2ZSBjb21wb25lbnRzXG4tIEhvdCByZWxvYWRpbmdcblxuIyMgR2V0dGluZyBTdGFydGVkXG5cbjEuIEluc3RhbGwgZGVwZW5kZW5jaWVzOiBcXGBucG0gaW5zdGFsbFxcYFxuMi4gU3RhcnQgdGhlIGRldmVsb3BtZW50IHNlcnZlcjogXFxgbnBtIHN0YXJ0XFxgXG4zLiBPcGVuIFtodHRwOi8vbG9jYWxob3N0OjMwMDBdKGh0dHA6Ly9sb2NhbGhvc3Q6MzAwMCkgdG8gdmlldyBpdCBpbiB0aGUgYnJvd3Nlci5cbmBcbiAgfSk7XG4gIGNvbnN0IGVkaXRvclJlZiA9IHVzZVJlZjxhbnk+KG51bGwpO1xuXG4gIGNvbnN0IGZpbGVUcmVlOiBGaWxlVHJlZUl0ZW1bXSA9IFtcbiAgICB7XG4gICAgICBuYW1lOiAnc3JjJyxcbiAgICAgIHR5cGU6ICdmb2xkZXInLFxuICAgICAgaXNPcGVuOiB0cnVlLFxuICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgeyBuYW1lOiAnQXBwLnRzeCcsIHR5cGU6ICdmaWxlJyB9LFxuICAgICAgICB7IG5hbWU6ICdBcHAuY3NzJywgdHlwZTogJ2ZpbGUnIH0sXG4gICAgICAgIHsgbmFtZTogJ2luZGV4LnRzeCcsIHR5cGU6ICdmaWxlJyB9XG4gICAgICBdXG4gICAgfSxcbiAgICB7XG4gICAgICBuYW1lOiAncHVibGljJyxcbiAgICAgIHR5cGU6ICdmb2xkZXInLFxuICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgeyBuYW1lOiAnaW5kZXguaHRtbCcsIHR5cGU6ICdmaWxlJyB9LFxuICAgICAgICB7IG5hbWU6ICdmYXZpY29uLmljbycsIHR5cGU6ICdmaWxlJyB9XG4gICAgICBdXG4gICAgfSxcbiAgICB7IG5hbWU6ICdwYWNrYWdlLmpzb24nLCB0eXBlOiAnZmlsZScgfSxcbiAgICB7IG5hbWU6ICdSRUFETUUubWQnLCB0eXBlOiAnZmlsZScgfVxuICBdO1xuXG4gIGNvbnN0IGhhbmRsZUVkaXRvckRpZE1vdW50ID0gKGVkaXRvcjogYW55LCBtb25hY286IGFueSkgPT4ge1xuICAgIGVkaXRvclJlZi5jdXJyZW50ID0gZWRpdG9yO1xuXG4gICAgLy8gQ29uZmlndXJlIE1vbmFjbyBFZGl0b3JcbiAgICBtb25hY28uZWRpdG9yLmRlZmluZVRoZW1lKCd2cy1kYXJrLWN1c3RvbScsIHtcbiAgICAgIGJhc2U6ICd2cy1kYXJrJyxcbiAgICAgIGluaGVyaXQ6IHRydWUsXG4gICAgICBydWxlczogW10sXG4gICAgICBjb2xvcnM6IHtcbiAgICAgICAgJ2VkaXRvci5iYWNrZ3JvdW5kJzogJyMxZTFlMWUnLFxuICAgICAgICAnZWRpdG9yLmZvcmVncm91bmQnOiAnI2Q0ZDRkNCcsXG4gICAgICAgICdlZGl0b3JMaW5lTnVtYmVyLmZvcmVncm91bmQnOiAnIzg1ODU4NScsXG4gICAgICAgICdlZGl0b3Iuc2VsZWN0aW9uQmFja2dyb3VuZCc6ICcjMjY0Zjc4JyxcbiAgICAgICAgJ2VkaXRvci5pbmFjdGl2ZVNlbGVjdGlvbkJhY2tncm91bmQnOiAnIzNhM2Q0MSdcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIG1vbmFjby5lZGl0b3Iuc2V0VGhlbWUoJ3ZzLWRhcmstY3VzdG9tJyk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRWRpdG9yQ2hhbmdlID0gKHZhbHVlOiBzdHJpbmcgfCB1bmRlZmluZWQpID0+IHtcbiAgICBpZiAodmFsdWUgIT09IHVuZGVmaW5lZCAmJiBhY3RpdmVGaWxlKSB7XG4gICAgICBzZXRGaWxlcyhwcmV2ID0+ICh7XG4gICAgICAgIC4uLnByZXYsXG4gICAgICAgIFthY3RpdmVGaWxlXTogdmFsdWVcbiAgICAgIH0pKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRmlsZUNsaWNrID0gKGZpbGVOYW1lOiBzdHJpbmcpID0+IHtcbiAgICAvLyBPbmx5IG9wZW4gZmlsZXMgdGhhdCBleGlzdCBpbiBvdXIgZmlsZXMgb2JqZWN0XG4gICAgaWYgKGZpbGVzW2ZpbGVOYW1lXSkge1xuICAgICAgc2V0QWN0aXZlRmlsZShmaWxlTmFtZSk7XG4gICAgICBpZiAoIW9wZW5GaWxlcy5pbmNsdWRlcyhmaWxlTmFtZSkpIHtcbiAgICAgICAgc2V0T3BlbkZpbGVzKFsuLi5vcGVuRmlsZXMsIGZpbGVOYW1lXSk7XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGNsb3NlRmlsZSA9IChmaWxlTmFtZTogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgbmV3T3BlbkZpbGVzID0gb3BlbkZpbGVzLmZpbHRlcihmID0+IGYgIT09IGZpbGVOYW1lKTtcbiAgICBzZXRPcGVuRmlsZXMobmV3T3BlbkZpbGVzKTtcbiAgICBpZiAoYWN0aXZlRmlsZSA9PT0gZmlsZU5hbWUgJiYgbmV3T3BlbkZpbGVzLmxlbmd0aCA+IDApIHtcbiAgICAgIHNldEFjdGl2ZUZpbGUobmV3T3BlbkZpbGVzW25ld09wZW5GaWxlcy5sZW5ndGggLSAxXSk7XG4gICAgfSBlbHNlIGlmIChuZXdPcGVuRmlsZXMubGVuZ3RoID09PSAwKSB7XG4gICAgICBzZXRBY3RpdmVGaWxlKCcnKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZ2V0RmlsZUxhbmd1YWdlID0gKGZpbGVOYW1lOiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCBleHQgPSBmaWxlTmFtZS5zcGxpdCgnLicpLnBvcCgpO1xuICAgIHN3aXRjaCAoZXh0KSB7XG4gICAgICBjYXNlICd0c3gnOlxuICAgICAgY2FzZSAndHMnOlxuICAgICAgICByZXR1cm4gJ3R5cGVzY3JpcHQnO1xuICAgICAgY2FzZSAnanN4JzpcbiAgICAgIGNhc2UgJ2pzJzpcbiAgICAgICAgcmV0dXJuICdqYXZhc2NyaXB0JztcbiAgICAgIGNhc2UgJ2Nzcyc6XG4gICAgICAgIHJldHVybiAnY3NzJztcbiAgICAgIGNhc2UgJ2pzb24nOlxuICAgICAgICByZXR1cm4gJ2pzb24nO1xuICAgICAgY2FzZSAnbWQnOlxuICAgICAgICByZXR1cm4gJ21hcmtkb3duJztcbiAgICAgIGNhc2UgJ2h0bWwnOlxuICAgICAgICByZXR1cm4gJ2h0bWwnO1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuICdwbGFpbnRleHQnO1xuICAgIH1cbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1mdWxsIGJnLXBhbmVsLWJnIHJvdW5kZWQteGwgb3ZlcmZsb3ctaGlkZGVuIGZsZXhcIj5cbiAgICAgIHsvKiBMZWZ0IFNpZGViYXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNjQgYmctZ3JheS05MDAvNTAgYm9yZGVyLXIgYm9yZGVyLWdyYXktNzAwLzUwIGZsZXggZmxleC1jb2xcIj5cbiAgICAgICAgey8qIFNpZGViYXIgVGFicyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGJvcmRlci1iIGJvcmRlci1ncmF5LTcwMC81MFwiPlxuICAgICAgICAgIHtbXG4gICAgICAgICAgICB7IGlkOiAnZmlsZXMnLCBpY29uOiAn8J+TgScsIGxhYmVsOiAnRmlsZXMnIH0sXG4gICAgICAgICAgICB7IGlkOiAnZ2l0JywgaWNvbjogJ/CflIAnLCBsYWJlbDogJ0dpdCcgfSxcbiAgICAgICAgICAgIHsgaWQ6ICdleHRlbnNpb25zJywgaWNvbjogJ/Cfp6knLCBsYWJlbDogJ0V4dGVuc2lvbnMnIH1cbiAgICAgICAgICBdLm1hcCgodGFiKSA9PiAoXG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIGtleT17dGFiLmlkfVxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaWRlYmFyVGFiKHRhYi5pZCBhcyBhbnkpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4LTEgcC0zIHRleHQtc20gZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwICR7XG4gICAgICAgICAgICAgICAgc2lkZWJhclRhYiA9PT0gdGFiLmlkXG4gICAgICAgICAgICAgICAgICA/ICdiZy1ibHVlLTUwMC8yMCB0ZXh0LWJsdWUtNDAwIGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNDAwJ1xuICAgICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LXdoaXRlIGhvdmVyOmJnLWdyYXktODAwLzYwJ1xuICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibXItMlwiPnt0YWIuaWNvbn08L3NwYW4+XG4gICAgICAgICAgICAgIHt0YWIubGFiZWx9XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFNpZGViYXIgQ29udGVudCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAge3NpZGViYXJUYWIgPT09ICdmaWxlcycgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTJcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC14cyB1cHBlcmNhc2UgZm9udC1zZW1pYm9sZCBtYi0yIHB4LTJcIj5FeHBsb3JlcjwvaDM+XG4gICAgICAgICAgICAgIHtmaWxlVHJlZS5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgPEZpbGVUcmVlTm9kZVxuICAgICAgICAgICAgICAgICAga2V5PXtpbmRleH1cbiAgICAgICAgICAgICAgICAgIGl0ZW09e2l0ZW19XG4gICAgICAgICAgICAgICAgICBsZXZlbD17MH1cbiAgICAgICAgICAgICAgICAgIG9uRmlsZUNsaWNrPXtoYW5kbGVGaWxlQ2xpY2t9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICAgIFxuICAgICAgICAgIHtzaWRlYmFyVGFiID09PSAnZ2l0JyAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNFwiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LXNlbWlib2xkIG1iLTRcIj5Tb3VyY2UgQ29udHJvbDwvaDM+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTQwMCB0ZXh0LXNtXCI+4pyTIDMgZmlsZXMgc3RhZ2VkPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy00MDAgdGV4dC1zbVwiPuKaoCAyIGZpbGVzIG1vZGlmaWVkPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC00MDAgdGV4dC1zbVwiPuKclyAxIGZpbGUgZGVsZXRlZDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgICAgXG4gICAgICAgICAge3NpZGViYXJUYWIgPT09ICdleHRlbnNpb25zJyAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNFwiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LXNlbWlib2xkIG1iLTRcIj5FeHRlbnNpb25zPC9oMz5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctYmx1ZS01MDAgcm91bmRlZCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0ZXh0LXhzXCI+VFM8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LXNtXCI+VHlwZVNjcmlwdDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC14c1wiPkluc3RhbGxlZDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtM1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLWdyZWVuLTUwMCByb3VuZGVkIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHRleHQteHNcIj5FUzwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQtc21cIj5FU0xpbnQ8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQteHNcIj5JbnN0YWxsZWQ8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBNYWluIEVkaXRvciBBcmVhICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBmbGV4LWNvbFwiPlxuICAgICAgICB7LyogRmlsZSBUYWJzICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggYmctZ3JheS04MDAvNDAgYm9yZGVyLWIgYm9yZGVyLWdyYXktNzAwLzUwXCI+XG4gICAgICAgICAge29wZW5GaWxlcy5tYXAoKGZpbGUpID0+IChcbiAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAga2V5PXtmaWxlfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBweC00IHB5LTIgYm9yZGVyLXIgYm9yZGVyLWdyYXktNzAwLzUwIGN1cnNvci1wb2ludGVyICR7XG4gICAgICAgICAgICAgICAgYWN0aXZlRmlsZSA9PT0gZmlsZVxuICAgICAgICAgICAgICAgICAgPyAnYmctcGFuZWwtYmcgdGV4dC13aGl0ZSdcbiAgICAgICAgICAgICAgICAgIDogJ3RleHQtZ3JheS00MDAgaG92ZXI6dGV4dC13aGl0ZSBob3ZlcjpiZy1ncmF5LTgwMC82MCdcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZUZpbGUoZmlsZSl9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxzcGFuPntmaWxlfTwvc3Bhbj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgICAgICAgY2xvc2VGaWxlKGZpbGUpO1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCBob3Zlcjp0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIMOXXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBDb2RlIEVkaXRvciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgcmVsYXRpdmVcIj5cbiAgICAgICAgICA8RWRpdG9yIC8+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBUZXJtaW5hbCAqL31cbiAgICAgICAge3Nob3dUZXJtaW5hbCAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTQ4IGJnLWJsYWNrIGJvcmRlci10IGJvcmRlci1ncmF5LTcwMC81MCBmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBweC00IHB5LTIgYmctZ3JheS04MDAvNjAgYm9yZGVyLWIgYm9yZGVyLWdyYXktNzAwLzUwXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGQgdGV4dC1zbVwiPlRlcm1pbmFsPC9zcGFuPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC14c1wiPmJhc2g8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd1Rlcm1pbmFsKGZhbHNlKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC13aGl0ZSB0ZXh0LXNtIHB4LTIgcHktMSByb3VuZGVkIGhvdmVyOmJnLWdyYXktNzAwLzUwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICDDl1xuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgcC00IG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNDAwIGZvbnQtbW9ubyB0ZXh0LXNtIHNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPldlbGNvbWUgdG8gUmVhY3QgSURFIFRlcm1pbmFsPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdj4kIG5wbSBpbnN0YWxsPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwXCI+4pyTIERlcGVuZGVuY2llcyBpbnN0YWxsZWQgc3VjY2Vzc2Z1bGx5PC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdj4kIG5wbSBzdGFydDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMFwiPlN0YXJ0aW5nIGRldmVsb3BtZW50IHNlcnZlci4uLjwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTQwMFwiPkxvY2FsOiAgICBodHRwOi8vbG9jYWxob3N0OjMwMDA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtYmx1ZS00MDBcIj5OZXR3b3JrOiAgaHR0cDovLzE5Mi4xNjguMS4xMDA6MzAwMDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMFwiPndlYnBhY2sgY29tcGlsZWQgc3VjY2Vzc2Z1bGx5PC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBtdC0yXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTQwMFwiPiQgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYmctdHJhbnNwYXJlbnQgYm9yZGVyLW5vbmUgb3V0bGluZS1ub25lIHRleHQtZ3JlZW4tNDAwIG1sLTEgYW5pbWF0ZS1wdWxzZVwiPnw8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFJpZ2h0IFNpZGViYXIgLSBBSSBBc3Npc3RhbnQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInctODAgYmctZ3JheS05MDAvNTAgYm9yZGVyLWwgYm9yZGVyLWdyYXktNzAwLzUwIHAtNFwiPlxuICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LXNlbWlib2xkIG1iLTRcIj7wn6SWIEFJIEFzc2lzdGFudDwvaDM+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMC80MCByb3VuZGVkLWxnIHAtM1wiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNDAwIHRleHQtc20gZm9udC1tZWRpdW0gbWItMVwiPkNvZGUgU3VnZ2VzdGlvbjwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgQ29uc2lkZXIgYWRkaW5nIGVycm9yIGhhbmRsaW5nIHRvIHlvdXIgUmVhY3QgY29tcG9uZW50LlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMC80MCByb3VuZGVkLWxnIHAtM1wiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTQwMCB0ZXh0LXNtIGZvbnQtbWVkaXVtIG1iLTFcIj5QZXJmb3JtYW5jZSBUaXA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgIFVzZSBSZWFjdC5tZW1vKCkgdG8gb3B0aW1pemUgY29tcG9uZW50IHJlLXJlbmRlcnMuXG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTZcIj5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93VGVybWluYWwoIXNob3dUZXJtaW5hbCl9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctYmx1ZS01MDAvMjAgaG92ZXI6YmctYmx1ZS01MDAvMzAgdGV4dC1ibHVlLTQwMCBweS0yIHB4LTQgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIHtzaG93VGVybWluYWwgPyAnSGlkZSBUZXJtaW5hbCcgOiAnU2hvdyBUZXJtaW5hbCd9XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJFZGl0b3IiLCJGaWxlVHJlZU5vZGUiLCJpdGVtIiwibGV2ZWwiLCJvbkZpbGVDbGljayIsImlzT3BlbiIsInNldElzT3BlbiIsImhhbmRsZUNsaWNrIiwidHlwZSIsIm5hbWUiLCJkaXYiLCJjbGFzc05hbWUiLCJzdHlsZSIsInBhZGRpbmdMZWZ0Iiwib25DbGljayIsInNwYW4iLCJjaGlsZHJlbiIsIm1hcCIsImNoaWxkIiwiaW5kZXgiLCJDb2RlclZpZXciLCJhY3RpdmVGaWxlIiwic2V0QWN0aXZlRmlsZSIsIm9wZW5GaWxlcyIsInNldE9wZW5GaWxlcyIsInNob3dUZXJtaW5hbCIsInNldFNob3dUZXJtaW5hbCIsInNpZGViYXJUYWIiLCJzZXRTaWRlYmFyVGFiIiwiZmlsZXMiLCJzZXRGaWxlcyIsImVkaXRvclJlZiIsInVzZVJlZiIsImZpbGVUcmVlIiwiaGFuZGxlRWRpdG9yRGlkTW91bnQiLCJlZGl0b3IiLCJtb25hY28iLCJjdXJyZW50IiwiZGVmaW5lVGhlbWUiLCJiYXNlIiwiaW5oZXJpdCIsInJ1bGVzIiwiY29sb3JzIiwic2V0VGhlbWUiLCJoYW5kbGVFZGl0b3JDaGFuZ2UiLCJ2YWx1ZSIsInVuZGVmaW5lZCIsInByZXYiLCJoYW5kbGVGaWxlQ2xpY2siLCJmaWxlTmFtZSIsImluY2x1ZGVzIiwiY2xvc2VGaWxlIiwibmV3T3BlbkZpbGVzIiwiZmlsdGVyIiwiZiIsImxlbmd0aCIsImdldEZpbGVMYW5ndWFnZSIsImV4dCIsInNwbGl0IiwicG9wIiwiaWQiLCJpY29uIiwibGFiZWwiLCJ0YWIiLCJidXR0b24iLCJoMyIsImZpbGUiLCJlIiwic3RvcFByb3BhZ2F0aW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/views/Coder/CoderView.tsx\n"));

/***/ }),

/***/ "./node_modules/.pnpm/state-local@1.0.7/node_modules/state-local/lib/es/state-local.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/.pnpm/state-local@1.0.7/node_modules/state-local/lib/es/state-local.js ***!
  \*********************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction compose() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (x) {\n    return fns.reduceRight(function (y, f) {\n      return f(y);\n    }, x);\n  };\n}\n\nfunction curry(fn) {\n  return function curried() {\n    var _this = this;\n\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n\n    return args.length >= fn.length ? fn.apply(this, args) : function () {\n      for (var _len3 = arguments.length, nextArgs = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        nextArgs[_key3] = arguments[_key3];\n      }\n\n      return curried.apply(_this, [].concat(args, nextArgs));\n    };\n  };\n}\n\nfunction isObject(value) {\n  return {}.toString.call(value).includes('Object');\n}\n\nfunction isEmpty(obj) {\n  return !Object.keys(obj).length;\n}\n\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\n\nfunction hasOwnProperty(object, property) {\n  return Object.prototype.hasOwnProperty.call(object, property);\n}\n\nfunction validateChanges(initial, changes) {\n  if (!isObject(changes)) errorHandler('changeType');\n  if (Object.keys(changes).some(function (field) {\n    return !hasOwnProperty(initial, field);\n  })) errorHandler('changeField');\n  return changes;\n}\n\nfunction validateSelector(selector) {\n  if (!isFunction(selector)) errorHandler('selectorType');\n}\n\nfunction validateHandler(handler) {\n  if (!(isFunction(handler) || isObject(handler))) errorHandler('handlerType');\n  if (isObject(handler) && Object.values(handler).some(function (_handler) {\n    return !isFunction(_handler);\n  })) errorHandler('handlersType');\n}\n\nfunction validateInitial(initial) {\n  if (!initial) errorHandler('initialIsRequired');\n  if (!isObject(initial)) errorHandler('initialType');\n  if (isEmpty(initial)) errorHandler('initialContent');\n}\n\nfunction throwError(errorMessages, type) {\n  throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\n\nvar errorMessages = {\n  initialIsRequired: 'initial state is required',\n  initialType: 'initial state should be an object',\n  initialContent: 'initial state shouldn\\'t be an empty object',\n  handlerType: 'handler should be an object or a function',\n  handlersType: 'all handlers should be a functions',\n  selectorType: 'selector should be a function',\n  changeType: 'provided value of changes should be an object',\n  changeField: 'it seams you want to change a field in the state which is not specified in the \"initial\" state',\n  \"default\": 'an unknown error accured in `state-local` package'\n};\nvar errorHandler = curry(throwError)(errorMessages);\nvar validators = {\n  changes: validateChanges,\n  selector: validateSelector,\n  handler: validateHandler,\n  initial: validateInitial\n};\n\nfunction create(initial) {\n  var handler = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  validators.initial(initial);\n  validators.handler(handler);\n  var state = {\n    current: initial\n  };\n  var didUpdate = curry(didStateUpdate)(state, handler);\n  var update = curry(updateState)(state);\n  var validate = curry(validators.changes)(initial);\n  var getChanges = curry(extractChanges)(state);\n\n  function getState() {\n    var selector = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : function (state) {\n      return state;\n    };\n    validators.selector(selector);\n    return selector(state.current);\n  }\n\n  function setState(causedChanges) {\n    compose(didUpdate, update, validate, getChanges)(causedChanges);\n  }\n\n  return [getState, setState];\n}\n\nfunction extractChanges(state, causedChanges) {\n  return isFunction(causedChanges) ? causedChanges(state.current) : causedChanges;\n}\n\nfunction updateState(state, changes) {\n  state.current = _objectSpread2(_objectSpread2({}, state.current), changes);\n  return changes;\n}\n\nfunction didStateUpdate(state, handler, changes) {\n  isFunction(handler) ? handler(state.current) : Object.keys(changes).forEach(function (field) {\n    var _handler$field;\n\n    return (_handler$field = handler[field]) === null || _handler$field === void 0 ? void 0 : _handler$field.call(handler, state.current[field]);\n  });\n  return changes;\n}\n\nvar index = {\n  create: create\n};\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (index);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/.pnpm/state-local@1.0.7/node_modules/state-local/lib/es/state-local.js\n"));

/***/ }),

/***/ "./node_modules/.pnpm/@monaco-editor+react@4.7.0__1ce2f918ff59bd31eee90d0f34fee1f7/node_modules/@monaco-editor/react/dist/index.mjs":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@monaco-editor+react@4.7.0__1ce2f918ff59bd31eee90d0f34fee1f7/node_modules/@monaco-editor/react/dist/index.mjs ***!
  \******************************************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiffEditor: function() { return /* binding */ we; },\n/* harmony export */   Editor: function() { return /* binding */ de; },\n/* harmony export */   \"default\": function() { return /* binding */ Ft; },\n/* harmony export */   loader: function() { return /* reexport safe */ _monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   useMonaco: function() { return /* binding */ Le; }\n/* harmony export */ });\n/* harmony import */ var _monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @monaco-editor/loader */ \"./node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\nvar le={wrapper:{display:\"flex\",position:\"relative\",textAlign:\"initial\"},fullWidth:{width:\"100%\"},hide:{display:\"none\"}},v=le;var ae={container:{display:\"flex\",height:\"100%\",width:\"100%\",justifyContent:\"center\",alignItems:\"center\"}},Y=ae;function Me({children:e}){return react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\",{style:Y.container},e)}var Z=Me;var $=Z;function Ee({width:e,height:r,isEditorReady:n,loading:t,_ref:a,className:m,wrapperProps:E}){return react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"section\",{style:{...v.wrapper,width:e,height:r},...E},!n&&react__WEBPACK_IMPORTED_MODULE_1__.createElement($,null,t),react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\",{ref:a,style:{...v.fullWidth,...!n&&v.hide},className:m}))}var ee=Ee;var H=(0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(ee);function Ce(e){(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(e,[])}var k=Ce;function he(e,r,n=!0){let t=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(!0);(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(t.current||!n?()=>{t.current=!1}:e,r)}var l=he;function D(){}function h(e,r,n,t){return De(e,t)||be(e,r,n,t)}function De(e,r){return e.editor.getModel(te(e,r))}function be(e,r,n,t){return e.editor.createModel(r,n,t?te(e,t):void 0)}function te(e,r){return e.Uri.parse(r)}function Oe({original:e,modified:r,language:n,originalLanguage:t,modifiedLanguage:a,originalModelPath:m,modifiedModelPath:E,keepCurrentOriginalModel:g=!1,keepCurrentModifiedModel:N=!1,theme:x=\"light\",loading:P=\"Loading...\",options:y={},height:V=\"100%\",width:z=\"100%\",className:F,wrapperProps:j={},beforeMount:A=D,onMount:q=D}){let[M,O]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!1),[T,s]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!0),u=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),c=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),w=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),d=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(q),o=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(A),b=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(!1);k(()=>{let i=_monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"].init();return i.then(f=>(c.current=f)&&s(!1)).catch(f=>f?.type!==\"cancelation\"&&console.error(\"Monaco initialization: error:\",f)),()=>u.current?I():i.cancel()}),l(()=>{if(u.current&&c.current){let i=u.current.getOriginalEditor(),f=h(c.current,e||\"\",t||n||\"text\",m||\"\");f!==i.getModel()&&i.setModel(f)}},[m],M),l(()=>{if(u.current&&c.current){let i=u.current.getModifiedEditor(),f=h(c.current,r||\"\",a||n||\"text\",E||\"\");f!==i.getModel()&&i.setModel(f)}},[E],M),l(()=>{let i=u.current.getModifiedEditor();i.getOption(c.current.editor.EditorOption.readOnly)?i.setValue(r||\"\"):r!==i.getValue()&&(i.executeEdits(\"\",[{range:i.getModel().getFullModelRange(),text:r||\"\",forceMoveMarkers:!0}]),i.pushUndoStop())},[r],M),l(()=>{u.current?.getModel()?.original.setValue(e||\"\")},[e],M),l(()=>{let{original:i,modified:f}=u.current.getModel();c.current.editor.setModelLanguage(i,t||n||\"text\"),c.current.editor.setModelLanguage(f,a||n||\"text\")},[n,t,a],M),l(()=>{c.current?.editor.setTheme(x)},[x],M),l(()=>{u.current?.updateOptions(y)},[y],M);let L=(0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{if(!c.current)return;o.current(c.current);let i=h(c.current,e||\"\",t||n||\"text\",m||\"\"),f=h(c.current,r||\"\",a||n||\"text\",E||\"\");u.current?.setModel({original:i,modified:f})},[n,r,a,e,t,m,E]),U=(0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{!b.current&&w.current&&(u.current=c.current.editor.createDiffEditor(w.current,{automaticLayout:!0,...y}),L(),c.current?.editor.setTheme(x),O(!0),b.current=!0)},[y,x,L]);(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{M&&d.current(u.current,c.current)},[M]),(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{!T&&!M&&U()},[T,M,U]);function I(){let i=u.current?.getModel();g||i?.original?.dispose(),N||i?.modified?.dispose(),u.current?.dispose()}return react__WEBPACK_IMPORTED_MODULE_1__.createElement(H,{width:z,height:V,isEditorReady:M,loading:P,_ref:w,className:F,wrapperProps:j})}var ie=Oe;var we=(0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(ie);function Pe(){let[e,r]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"].__getMonacoInstance());return k(()=>{let n;return e||(n=_monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"].init(),n.then(t=>{r(t)})),()=>n?.cancel()}),e}var Le=Pe;function He(e){let r=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();return (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{r.current=e},[e]),r.current}var se=He;var _=new Map;function Ve({defaultValue:e,defaultLanguage:r,defaultPath:n,value:t,language:a,path:m,theme:E=\"light\",line:g,loading:N=\"Loading...\",options:x={},overrideServices:P={},saveViewState:y=!0,keepCurrentModel:V=!1,width:z=\"100%\",height:F=\"100%\",className:j,wrapperProps:A={},beforeMount:q=D,onMount:M=D,onChange:O,onValidate:T=D}){let[s,u]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!1),[c,w]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!0),d=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),o=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),b=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),L=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(M),U=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(q),I=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(),i=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(t),f=se(m),Q=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(!1),B=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(!1);k(()=>{let p=_monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"].init();return p.then(R=>(d.current=R)&&w(!1)).catch(R=>R?.type!==\"cancelation\"&&console.error(\"Monaco initialization: error:\",R)),()=>o.current?pe():p.cancel()}),l(()=>{let p=h(d.current,e||t||\"\",r||a||\"\",m||n||\"\");p!==o.current?.getModel()&&(y&&_.set(f,o.current?.saveViewState()),o.current?.setModel(p),y&&o.current?.restoreViewState(_.get(m)))},[m],s),l(()=>{o.current?.updateOptions(x)},[x],s),l(()=>{!o.current||t===void 0||(o.current.getOption(d.current.editor.EditorOption.readOnly)?o.current.setValue(t):t!==o.current.getValue()&&(B.current=!0,o.current.executeEdits(\"\",[{range:o.current.getModel().getFullModelRange(),text:t,forceMoveMarkers:!0}]),o.current.pushUndoStop(),B.current=!1))},[t],s),l(()=>{let p=o.current?.getModel();p&&a&&d.current?.editor.setModelLanguage(p,a)},[a],s),l(()=>{g!==void 0&&o.current?.revealLine(g)},[g],s),l(()=>{d.current?.editor.setTheme(E)},[E],s);let X=(0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{if(!(!b.current||!d.current)&&!Q.current){U.current(d.current);let p=m||n,R=h(d.current,t||e||\"\",r||a||\"\",p||\"\");o.current=d.current?.editor.create(b.current,{model:R,automaticLayout:!0,...x},P),y&&o.current.restoreViewState(_.get(p)),d.current.editor.setTheme(E),g!==void 0&&o.current.revealLine(g),u(!0),Q.current=!0}},[e,r,n,t,a,m,x,P,y,E,g]);(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{s&&L.current(o.current,d.current)},[s]),(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{!c&&!s&&X()},[c,s,X]),i.current=t,(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{s&&O&&(I.current?.dispose(),I.current=o.current?.onDidChangeModelContent(p=>{B.current||O(o.current.getValue(),p)}))},[s,O]),(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{if(s){let p=d.current.editor.onDidChangeMarkers(R=>{let G=o.current.getModel()?.uri;if(G&&R.find(J=>J.path===G.path)){let J=d.current.editor.getModelMarkers({resource:G});T?.(J)}});return()=>{p?.dispose()}}return()=>{}},[s,T]);function pe(){I.current?.dispose(),V?y&&_.set(m,o.current.saveViewState()):o.current.getModel()?.dispose(),o.current.dispose()}return react__WEBPACK_IMPORTED_MODULE_1__.createElement(H,{width:z,height:F,isEditorReady:s,loading:N,_ref:b,className:j,wrapperProps:A})}var fe=Ve;var de=(0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(fe);var Ft=de;\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvLnBucG0vQG1vbmFjby1lZGl0b3IrcmVhY3RANC43LjBfXzFjZTJmOTE4ZmY1OWJkMzFlZWU5MGQwZjM0ZmVlMWY3L25vZGVfbW9kdWxlcy9AbW9uYWNvLWVkaXRvci9yZWFjdC9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQWlQLFFBQVEsU0FBUyx1REFBdUQsWUFBWSxhQUFhLE9BQU8sZ0JBQWdCLE1BQTRCLFFBQVEsV0FBVyx1RkFBdUYsTUFBTSxhQUFhLFdBQVcsRUFBRSxPQUFPLGdEQUFnQixRQUFRLGtCQUFrQixJQUFJLFNBQVMsUUFBUSxhQUFhLDZFQUE2RSxFQUFFLE9BQU8sZ0RBQWUsWUFBWSxPQUFPLDhCQUE4QixNQUFNLEtBQUssZ0RBQWUsV0FBVyxnREFBZSxRQUFRLGFBQWEsNkJBQTZCLGFBQWEsR0FBRyxVQUFVLE1BQU0sMkNBQUUsS0FBd0MsZUFBZSxnREFBRSxPQUFPLFNBQXlELHNCQUFzQixNQUFNLDZDQUFFLEtBQUssZ0RBQUUsb0JBQW9CLGFBQWEsTUFBTSxTQUFTLGNBQWMsb0JBQW9CLDRCQUE0QixpQkFBaUIsa0NBQWtDLHFCQUFxQixrREFBa0QsaUJBQWlCLHNCQUFzQixhQUFhLDhOQUE4Tiw2REFBNkQsNkJBQTZCLEVBQUUsU0FBUywrQ0FBRSxXQUFXLCtDQUFFLE9BQU8sNkNBQUMsU0FBUyw2Q0FBQyxTQUFTLDZDQUFDLFNBQVMsNkNBQUMsTUFBTSw2Q0FBQyxNQUFNLDZDQUFDLEtBQUssT0FBTyxNQUFNLGtFQUFPLEdBQUcsd0pBQXdKLFNBQVMseUJBQXlCLDRFQUE0RSxpQ0FBaUMsZUFBZSx5QkFBeUIsNEVBQTRFLGlDQUFpQyxlQUFlLG9DQUFvQyw2R0FBNkcsc0VBQXNFLHFCQUFxQixlQUFlLGdEQUFnRCxlQUFlLElBQUksc0JBQXNCLHNCQUFzQixvR0FBb0csbUJBQW1CLDhCQUE4QixlQUFlLDRCQUE0QixRQUFRLE1BQU0sa0RBQUUsTUFBTSxxQkFBcUIscUJBQXFCLG9GQUFvRixxQkFBcUIsc0JBQXNCLEVBQUUsb0JBQW9CLGtEQUFFLE1BQU0sK0VBQStFLHdCQUF3Qix3REFBd0QsVUFBVSxnREFBRSxNQUFNLGtDQUFrQyxNQUFNLGdEQUFFLE1BQU0sWUFBWSxVQUFVLGFBQWEsNEJBQTRCLHlFQUF5RSxPQUFPLGdEQUFnQixJQUFJLDZFQUE2RSxFQUFFLFVBQVUsT0FBTywyQ0FBRSxLQUE2RSxjQUFjLFNBQVMsK0NBQUUsQ0FBQyxpRkFBc0IsSUFBSSxjQUFjLE1BQU0sYUFBYSxrRUFBTyxjQUFjLEtBQUssbUJBQW1CLElBQUksVUFBaU4sZUFBZSxNQUFNLDZDQUFFLEdBQUcsT0FBTyxnREFBRSxNQUFNLFlBQVksZ0JBQWdCLFVBQVUsY0FBYyxhQUFhLG1JQUFtSSxzQkFBc0Isc0dBQXNHLHVEQUF1RCxFQUFFLFNBQVMsK0NBQUUsV0FBVywrQ0FBRSxPQUFPLDZDQUFDLFNBQVMsNkNBQUMsU0FBUyw2Q0FBQyxTQUFTLDZDQUFDLE1BQU0sNkNBQUMsTUFBTSw2Q0FBQyxLQUFLLDZDQUFDLGNBQWMsNkNBQUMsT0FBTyw2Q0FBQyxLQUFLLE9BQU8sTUFBTSxrRUFBTyxHQUFHLHlKQUF5SixTQUFTLDhDQUE4QyxvSUFBb0ksZUFBZSw0QkFBNEIsZUFBZSwrS0FBK0ssMEVBQTBFLDJDQUEyQyxlQUFlLDRCQUE0Qiw4Q0FBOEMsZUFBZSxxQ0FBcUMsZUFBZSw4QkFBOEIsUUFBUSxNQUFNLGtEQUFFLE1BQU0sMENBQTBDLHFCQUFxQixrREFBa0QsOENBQThDLGdDQUFnQyxpSUFBaUksMEJBQTBCLGdEQUFDLE1BQU0sa0NBQWtDLE1BQU0sZ0RBQUMsTUFBTSxZQUFZLHNCQUFzQixnREFBQyxNQUFNLDZFQUE2RSxxQ0FBcUMsR0FBRyxRQUFRLGdEQUFDLE1BQU0sTUFBTSw4Q0FBOEMsZ0NBQWdDLGtDQUFrQyx3Q0FBd0MsV0FBVyxFQUFFLFFBQVEsRUFBRSxXQUFXLGNBQWMsYUFBYSxRQUFRLGNBQWMsaUhBQWlILE9BQU8sZ0RBQWdCLElBQUksNkVBQTZFLEVBQUUsVUFBVSxPQUFPLDJDQUFFLEtBQUssVUFBMkY7QUFDNzFNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AbW9uYWNvLWVkaXRvcityZWFjdEA0LjcuMF9fMWNlMmY5MThmZjU5YmQzMWVlZTkwZDBmMzRmZWUxZjcvbm9kZV9tb2R1bGVzL0Btb25hY28tZWRpdG9yL3JlYWN0L2Rpc3QvaW5kZXgubWpzP2VlMmEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF90IGZyb21cIkBtb25hY28tZWRpdG9yL2xvYWRlclwiO2ltcG9ydHttZW1vIGFzIFRlfWZyb21cInJlYWN0XCI7aW1wb3J0IGtlLHt1c2VTdGF0ZSBhcyByZSx1c2VSZWYgYXMgUyx1c2VDYWxsYmFjayBhcyBvZSx1c2VFZmZlY3QgYXMgbmV9ZnJvbVwicmVhY3RcIjtpbXBvcnQgU2UgZnJvbVwiQG1vbmFjby1lZGl0b3IvbG9hZGVyXCI7aW1wb3J0e21lbW8gYXMgeWV9ZnJvbVwicmVhY3RcIjtpbXBvcnQgSyBmcm9tXCJyZWFjdFwiO3ZhciBsZT17d3JhcHBlcjp7ZGlzcGxheTpcImZsZXhcIixwb3NpdGlvbjpcInJlbGF0aXZlXCIsdGV4dEFsaWduOlwiaW5pdGlhbFwifSxmdWxsV2lkdGg6e3dpZHRoOlwiMTAwJVwifSxoaWRlOntkaXNwbGF5Olwibm9uZVwifX0sdj1sZTtpbXBvcnQgbWUgZnJvbVwicmVhY3RcIjt2YXIgYWU9e2NvbnRhaW5lcjp7ZGlzcGxheTpcImZsZXhcIixoZWlnaHQ6XCIxMDAlXCIsd2lkdGg6XCIxMDAlXCIsanVzdGlmeUNvbnRlbnQ6XCJjZW50ZXJcIixhbGlnbkl0ZW1zOlwiY2VudGVyXCJ9fSxZPWFlO2Z1bmN0aW9uIE1lKHtjaGlsZHJlbjplfSl7cmV0dXJuIG1lLmNyZWF0ZUVsZW1lbnQoXCJkaXZcIix7c3R5bGU6WS5jb250YWluZXJ9LGUpfXZhciBaPU1lO3ZhciAkPVo7ZnVuY3Rpb24gRWUoe3dpZHRoOmUsaGVpZ2h0OnIsaXNFZGl0b3JSZWFkeTpuLGxvYWRpbmc6dCxfcmVmOmEsY2xhc3NOYW1lOm0sd3JhcHBlclByb3BzOkV9KXtyZXR1cm4gSy5jcmVhdGVFbGVtZW50KFwic2VjdGlvblwiLHtzdHlsZTp7Li4udi53cmFwcGVyLHdpZHRoOmUsaGVpZ2h0OnJ9LC4uLkV9LCFuJiZLLmNyZWF0ZUVsZW1lbnQoJCxudWxsLHQpLEsuY3JlYXRlRWxlbWVudChcImRpdlwiLHtyZWY6YSxzdHlsZTp7Li4udi5mdWxsV2lkdGgsLi4uIW4mJnYuaGlkZX0sY2xhc3NOYW1lOm19KSl9dmFyIGVlPUVlO3ZhciBIPXllKGVlKTtpbXBvcnR7dXNlRWZmZWN0IGFzIHhlfWZyb21cInJlYWN0XCI7ZnVuY3Rpb24gQ2UoZSl7eGUoZSxbXSl9dmFyIGs9Q2U7aW1wb3J0e3VzZUVmZmVjdCBhcyBnZSx1c2VSZWYgYXMgUmV9ZnJvbVwicmVhY3RcIjtmdW5jdGlvbiBoZShlLHIsbj0hMCl7bGV0IHQ9UmUoITApO2dlKHQuY3VycmVudHx8IW4/KCk9Pnt0LmN1cnJlbnQ9ITF9OmUscil9dmFyIGw9aGU7ZnVuY3Rpb24gRCgpe31mdW5jdGlvbiBoKGUscixuLHQpe3JldHVybiBEZShlLHQpfHxiZShlLHIsbix0KX1mdW5jdGlvbiBEZShlLHIpe3JldHVybiBlLmVkaXRvci5nZXRNb2RlbCh0ZShlLHIpKX1mdW5jdGlvbiBiZShlLHIsbix0KXtyZXR1cm4gZS5lZGl0b3IuY3JlYXRlTW9kZWwocixuLHQ/dGUoZSx0KTp2b2lkIDApfWZ1bmN0aW9uIHRlKGUscil7cmV0dXJuIGUuVXJpLnBhcnNlKHIpfWZ1bmN0aW9uIE9lKHtvcmlnaW5hbDplLG1vZGlmaWVkOnIsbGFuZ3VhZ2U6bixvcmlnaW5hbExhbmd1YWdlOnQsbW9kaWZpZWRMYW5ndWFnZTphLG9yaWdpbmFsTW9kZWxQYXRoOm0sbW9kaWZpZWRNb2RlbFBhdGg6RSxrZWVwQ3VycmVudE9yaWdpbmFsTW9kZWw6Zz0hMSxrZWVwQ3VycmVudE1vZGlmaWVkTW9kZWw6Tj0hMSx0aGVtZTp4PVwibGlnaHRcIixsb2FkaW5nOlA9XCJMb2FkaW5nLi4uXCIsb3B0aW9uczp5PXt9LGhlaWdodDpWPVwiMTAwJVwiLHdpZHRoOno9XCIxMDAlXCIsY2xhc3NOYW1lOkYsd3JhcHBlclByb3BzOmo9e30sYmVmb3JlTW91bnQ6QT1ELG9uTW91bnQ6cT1EfSl7bGV0W00sT109cmUoITEpLFtULHNdPXJlKCEwKSx1PVMobnVsbCksYz1TKG51bGwpLHc9UyhudWxsKSxkPVMocSksbz1TKEEpLGI9UyghMSk7aygoKT0+e2xldCBpPVNlLmluaXQoKTtyZXR1cm4gaS50aGVuKGY9PihjLmN1cnJlbnQ9ZikmJnMoITEpKS5jYXRjaChmPT5mPy50eXBlIT09XCJjYW5jZWxhdGlvblwiJiZjb25zb2xlLmVycm9yKFwiTW9uYWNvIGluaXRpYWxpemF0aW9uOiBlcnJvcjpcIixmKSksKCk9PnUuY3VycmVudD9JKCk6aS5jYW5jZWwoKX0pLGwoKCk9PntpZih1LmN1cnJlbnQmJmMuY3VycmVudCl7bGV0IGk9dS5jdXJyZW50LmdldE9yaWdpbmFsRWRpdG9yKCksZj1oKGMuY3VycmVudCxlfHxcIlwiLHR8fG58fFwidGV4dFwiLG18fFwiXCIpO2YhPT1pLmdldE1vZGVsKCkmJmkuc2V0TW9kZWwoZil9fSxbbV0sTSksbCgoKT0+e2lmKHUuY3VycmVudCYmYy5jdXJyZW50KXtsZXQgaT11LmN1cnJlbnQuZ2V0TW9kaWZpZWRFZGl0b3IoKSxmPWgoYy5jdXJyZW50LHJ8fFwiXCIsYXx8bnx8XCJ0ZXh0XCIsRXx8XCJcIik7ZiE9PWkuZ2V0TW9kZWwoKSYmaS5zZXRNb2RlbChmKX19LFtFXSxNKSxsKCgpPT57bGV0IGk9dS5jdXJyZW50LmdldE1vZGlmaWVkRWRpdG9yKCk7aS5nZXRPcHRpb24oYy5jdXJyZW50LmVkaXRvci5FZGl0b3JPcHRpb24ucmVhZE9ubHkpP2kuc2V0VmFsdWUocnx8XCJcIik6ciE9PWkuZ2V0VmFsdWUoKSYmKGkuZXhlY3V0ZUVkaXRzKFwiXCIsW3tyYW5nZTppLmdldE1vZGVsKCkuZ2V0RnVsbE1vZGVsUmFuZ2UoKSx0ZXh0OnJ8fFwiXCIsZm9yY2VNb3ZlTWFya2VyczohMH1dKSxpLnB1c2hVbmRvU3RvcCgpKX0sW3JdLE0pLGwoKCk9Pnt1LmN1cnJlbnQ/LmdldE1vZGVsKCk/Lm9yaWdpbmFsLnNldFZhbHVlKGV8fFwiXCIpfSxbZV0sTSksbCgoKT0+e2xldHtvcmlnaW5hbDppLG1vZGlmaWVkOmZ9PXUuY3VycmVudC5nZXRNb2RlbCgpO2MuY3VycmVudC5lZGl0b3Iuc2V0TW9kZWxMYW5ndWFnZShpLHR8fG58fFwidGV4dFwiKSxjLmN1cnJlbnQuZWRpdG9yLnNldE1vZGVsTGFuZ3VhZ2UoZixhfHxufHxcInRleHRcIil9LFtuLHQsYV0sTSksbCgoKT0+e2MuY3VycmVudD8uZWRpdG9yLnNldFRoZW1lKHgpfSxbeF0sTSksbCgoKT0+e3UuY3VycmVudD8udXBkYXRlT3B0aW9ucyh5KX0sW3ldLE0pO2xldCBMPW9lKCgpPT57aWYoIWMuY3VycmVudClyZXR1cm47by5jdXJyZW50KGMuY3VycmVudCk7bGV0IGk9aChjLmN1cnJlbnQsZXx8XCJcIix0fHxufHxcInRleHRcIixtfHxcIlwiKSxmPWgoYy5jdXJyZW50LHJ8fFwiXCIsYXx8bnx8XCJ0ZXh0XCIsRXx8XCJcIik7dS5jdXJyZW50Py5zZXRNb2RlbCh7b3JpZ2luYWw6aSxtb2RpZmllZDpmfSl9LFtuLHIsYSxlLHQsbSxFXSksVT1vZSgoKT0+eyFiLmN1cnJlbnQmJncuY3VycmVudCYmKHUuY3VycmVudD1jLmN1cnJlbnQuZWRpdG9yLmNyZWF0ZURpZmZFZGl0b3Iody5jdXJyZW50LHthdXRvbWF0aWNMYXlvdXQ6ITAsLi4ueX0pLEwoKSxjLmN1cnJlbnQ/LmVkaXRvci5zZXRUaGVtZSh4KSxPKCEwKSxiLmN1cnJlbnQ9ITApfSxbeSx4LExdKTtuZSgoKT0+e00mJmQuY3VycmVudCh1LmN1cnJlbnQsYy5jdXJyZW50KX0sW01dKSxuZSgoKT0+eyFUJiYhTSYmVSgpfSxbVCxNLFVdKTtmdW5jdGlvbiBJKCl7bGV0IGk9dS5jdXJyZW50Py5nZXRNb2RlbCgpO2d8fGk/Lm9yaWdpbmFsPy5kaXNwb3NlKCksTnx8aT8ubW9kaWZpZWQ/LmRpc3Bvc2UoKSx1LmN1cnJlbnQ/LmRpc3Bvc2UoKX1yZXR1cm4ga2UuY3JlYXRlRWxlbWVudChILHt3aWR0aDp6LGhlaWdodDpWLGlzRWRpdG9yUmVhZHk6TSxsb2FkaW5nOlAsX3JlZjp3LGNsYXNzTmFtZTpGLHdyYXBwZXJQcm9wczpqfSl9dmFyIGllPU9lO3ZhciB3ZT1UZShpZSk7aW1wb3J0e3VzZVN0YXRlIGFzIEllfWZyb21cInJlYWN0XCI7aW1wb3J0IGNlIGZyb21cIkBtb25hY28tZWRpdG9yL2xvYWRlclwiO2Z1bmN0aW9uIFBlKCl7bGV0W2Uscl09SWUoY2UuX19nZXRNb25hY29JbnN0YW5jZSgpKTtyZXR1cm4gaygoKT0+e2xldCBuO3JldHVybiBlfHwobj1jZS5pbml0KCksbi50aGVuKHQ9PntyKHQpfSkpLCgpPT5uPy5jYW5jZWwoKX0pLGV9dmFyIExlPVBlO2ltcG9ydHttZW1vIGFzIHplfWZyb21cInJlYWN0XCI7aW1wb3J0IFdlLHt1c2VTdGF0ZSBhcyB1ZSx1c2VFZmZlY3QgYXMgVyx1c2VSZWYgYXMgQyx1c2VDYWxsYmFjayBhcyBfZX1mcm9tXCJyZWFjdFwiO2ltcG9ydCBOZSBmcm9tXCJAbW9uYWNvLWVkaXRvci9sb2FkZXJcIjtpbXBvcnR7dXNlRWZmZWN0IGFzIFVlLHVzZVJlZiBhcyB2ZX1mcm9tXCJyZWFjdFwiO2Z1bmN0aW9uIEhlKGUpe2xldCByPXZlKCk7cmV0dXJuIFVlKCgpPT57ci5jdXJyZW50PWV9LFtlXSksci5jdXJyZW50fXZhciBzZT1IZTt2YXIgXz1uZXcgTWFwO2Z1bmN0aW9uIFZlKHtkZWZhdWx0VmFsdWU6ZSxkZWZhdWx0TGFuZ3VhZ2U6cixkZWZhdWx0UGF0aDpuLHZhbHVlOnQsbGFuZ3VhZ2U6YSxwYXRoOm0sdGhlbWU6RT1cImxpZ2h0XCIsbGluZTpnLGxvYWRpbmc6Tj1cIkxvYWRpbmcuLi5cIixvcHRpb25zOng9e30sb3ZlcnJpZGVTZXJ2aWNlczpQPXt9LHNhdmVWaWV3U3RhdGU6eT0hMCxrZWVwQ3VycmVudE1vZGVsOlY9ITEsd2lkdGg6ej1cIjEwMCVcIixoZWlnaHQ6Rj1cIjEwMCVcIixjbGFzc05hbWU6aix3cmFwcGVyUHJvcHM6QT17fSxiZWZvcmVNb3VudDpxPUQsb25Nb3VudDpNPUQsb25DaGFuZ2U6TyxvblZhbGlkYXRlOlQ9RH0pe2xldFtzLHVdPXVlKCExKSxbYyx3XT11ZSghMCksZD1DKG51bGwpLG89QyhudWxsKSxiPUMobnVsbCksTD1DKE0pLFU9QyhxKSxJPUMoKSxpPUModCksZj1zZShtKSxRPUMoITEpLEI9QyghMSk7aygoKT0+e2xldCBwPU5lLmluaXQoKTtyZXR1cm4gcC50aGVuKFI9PihkLmN1cnJlbnQ9UikmJncoITEpKS5jYXRjaChSPT5SPy50eXBlIT09XCJjYW5jZWxhdGlvblwiJiZjb25zb2xlLmVycm9yKFwiTW9uYWNvIGluaXRpYWxpemF0aW9uOiBlcnJvcjpcIixSKSksKCk9Pm8uY3VycmVudD9wZSgpOnAuY2FuY2VsKCl9KSxsKCgpPT57bGV0IHA9aChkLmN1cnJlbnQsZXx8dHx8XCJcIixyfHxhfHxcIlwiLG18fG58fFwiXCIpO3AhPT1vLmN1cnJlbnQ/LmdldE1vZGVsKCkmJih5JiZfLnNldChmLG8uY3VycmVudD8uc2F2ZVZpZXdTdGF0ZSgpKSxvLmN1cnJlbnQ/LnNldE1vZGVsKHApLHkmJm8uY3VycmVudD8ucmVzdG9yZVZpZXdTdGF0ZShfLmdldChtKSkpfSxbbV0scyksbCgoKT0+e28uY3VycmVudD8udXBkYXRlT3B0aW9ucyh4KX0sW3hdLHMpLGwoKCk9Pnshby5jdXJyZW50fHx0PT09dm9pZCAwfHwoby5jdXJyZW50LmdldE9wdGlvbihkLmN1cnJlbnQuZWRpdG9yLkVkaXRvck9wdGlvbi5yZWFkT25seSk/by5jdXJyZW50LnNldFZhbHVlKHQpOnQhPT1vLmN1cnJlbnQuZ2V0VmFsdWUoKSYmKEIuY3VycmVudD0hMCxvLmN1cnJlbnQuZXhlY3V0ZUVkaXRzKFwiXCIsW3tyYW5nZTpvLmN1cnJlbnQuZ2V0TW9kZWwoKS5nZXRGdWxsTW9kZWxSYW5nZSgpLHRleHQ6dCxmb3JjZU1vdmVNYXJrZXJzOiEwfV0pLG8uY3VycmVudC5wdXNoVW5kb1N0b3AoKSxCLmN1cnJlbnQ9ITEpKX0sW3RdLHMpLGwoKCk9PntsZXQgcD1vLmN1cnJlbnQ/LmdldE1vZGVsKCk7cCYmYSYmZC5jdXJyZW50Py5lZGl0b3Iuc2V0TW9kZWxMYW5ndWFnZShwLGEpfSxbYV0scyksbCgoKT0+e2chPT12b2lkIDAmJm8uY3VycmVudD8ucmV2ZWFsTGluZShnKX0sW2ddLHMpLGwoKCk9PntkLmN1cnJlbnQ/LmVkaXRvci5zZXRUaGVtZShFKX0sW0VdLHMpO2xldCBYPV9lKCgpPT57aWYoISghYi5jdXJyZW50fHwhZC5jdXJyZW50KSYmIVEuY3VycmVudCl7VS5jdXJyZW50KGQuY3VycmVudCk7bGV0IHA9bXx8bixSPWgoZC5jdXJyZW50LHR8fGV8fFwiXCIscnx8YXx8XCJcIixwfHxcIlwiKTtvLmN1cnJlbnQ9ZC5jdXJyZW50Py5lZGl0b3IuY3JlYXRlKGIuY3VycmVudCx7bW9kZWw6UixhdXRvbWF0aWNMYXlvdXQ6ITAsLi4ueH0sUCkseSYmby5jdXJyZW50LnJlc3RvcmVWaWV3U3RhdGUoXy5nZXQocCkpLGQuY3VycmVudC5lZGl0b3Iuc2V0VGhlbWUoRSksZyE9PXZvaWQgMCYmby5jdXJyZW50LnJldmVhbExpbmUoZyksdSghMCksUS5jdXJyZW50PSEwfX0sW2UscixuLHQsYSxtLHgsUCx5LEUsZ10pO1coKCk9PntzJiZMLmN1cnJlbnQoby5jdXJyZW50LGQuY3VycmVudCl9LFtzXSksVygoKT0+eyFjJiYhcyYmWCgpfSxbYyxzLFhdKSxpLmN1cnJlbnQ9dCxXKCgpPT57cyYmTyYmKEkuY3VycmVudD8uZGlzcG9zZSgpLEkuY3VycmVudD1vLmN1cnJlbnQ/Lm9uRGlkQ2hhbmdlTW9kZWxDb250ZW50KHA9PntCLmN1cnJlbnR8fE8oby5jdXJyZW50LmdldFZhbHVlKCkscCl9KSl9LFtzLE9dKSxXKCgpPT57aWYocyl7bGV0IHA9ZC5jdXJyZW50LmVkaXRvci5vbkRpZENoYW5nZU1hcmtlcnMoUj0+e2xldCBHPW8uY3VycmVudC5nZXRNb2RlbCgpPy51cmk7aWYoRyYmUi5maW5kKEo9PkoucGF0aD09PUcucGF0aCkpe2xldCBKPWQuY3VycmVudC5lZGl0b3IuZ2V0TW9kZWxNYXJrZXJzKHtyZXNvdXJjZTpHfSk7VD8uKEopfX0pO3JldHVybigpPT57cD8uZGlzcG9zZSgpfX1yZXR1cm4oKT0+e319LFtzLFRdKTtmdW5jdGlvbiBwZSgpe0kuY3VycmVudD8uZGlzcG9zZSgpLFY/eSYmXy5zZXQobSxvLmN1cnJlbnQuc2F2ZVZpZXdTdGF0ZSgpKTpvLmN1cnJlbnQuZ2V0TW9kZWwoKT8uZGlzcG9zZSgpLG8uY3VycmVudC5kaXNwb3NlKCl9cmV0dXJuIFdlLmNyZWF0ZUVsZW1lbnQoSCx7d2lkdGg6eixoZWlnaHQ6Rixpc0VkaXRvclJlYWR5OnMsbG9hZGluZzpOLF9yZWY6YixjbGFzc05hbWU6aix3cmFwcGVyUHJvcHM6QX0pfXZhciBmZT1WZTt2YXIgZGU9emUoZmUpO3ZhciBGdD1kZTtleHBvcnR7d2UgYXMgRGlmZkVkaXRvcixkZSBhcyBFZGl0b3IsRnQgYXMgZGVmYXVsdCxfdCBhcyBsb2FkZXIsTGUgYXMgdXNlTW9uYWNvfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/.pnpm/@monaco-editor+react@4.7.0__1ce2f918ff59bd31eee90d0f34fee1f7/node_modules/@monaco-editor/react/dist/index.mjs\n"));

/***/ })

});
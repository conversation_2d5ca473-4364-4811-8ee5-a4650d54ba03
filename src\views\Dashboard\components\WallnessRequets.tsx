
import React from 'react';
import { Card } from '../../../components/Card';
import { UserCircleIcon, BuildingOfficeIcon, PuzzlePieceIcon, CogIcon } from '@heroicons/react/24/outline';

const SnowflakeIcon: React.FC<{className?: string}> = ({className}) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 2L12 22M2 12H22M4.2 19.8L19.8 4.2M4.2 4.2L19.8 19.8M7 4.9L7 19.1M17 4.9V19.1" />
    </svg>
);

const MemoryChipIcon: React.FC<{className?: string}> = ({className}) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 3.75H19.5a2.25 2.25 0 0 1 2.25 2.25v13.5A2.25 2.25 0 0 1 19.5 21.75H8.25A2.25 2.25 0 0 1 6 19.5V6A2.25 2.25 0 0 1 8.25 3.75Z" />
        <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 12h1.5m-1.5 3h1.5m-1.5 3h1.5m-1.5 3h1.5M18 12h1.5m-1.5 3h1.5m-1.5 3h1.5m-1.5 3h1.5M12 4.5v1.5m3 0v1.5m3 0v1.5m-9-1.5v1.5" />
    </svg>
);


export const WellnessRequests: React.FC = () => {
    return (
        <Card title="System Status & Requests">
            <div className="grid grid-cols-2 gap-4 h-full">
                {/* Left Column */}
                <div className="space-y-3 text-xs sm:text-sm flex flex-col justify-center">
                    <div className="flex items-center gap-3 text-gray-300">
                        <UserCircleIcon className="w-5 h-5 text-cyan-400 flex-shrink-0" />
                        <span>Security Patch Update</span>
                    </div>
                    <div className="flex items-center gap-3 text-gray-300">
                        <UserCircleIcon className="w-5 h-5 text-cyan-400 flex-shrink-0" />
                        <span>Network Latency Alert</span>
                    </div>
                    <div className="flex items-center gap-3 text-gray-300">
                        <UserCircleIcon className="w-5 h-5 text-cyan-400 flex-shrink-0" />
                        <span>Data Sync Complete</span>
                    </div>
                </div>

                {/* Right Column */}
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 text-center">
                    <div className="flex flex-col items-center justify-center bg-gray-700/30 rounded-lg p-1 sm:p-2">
                        <BuildingOfficeIcon className="w-5 h-5 sm:w-6 sm:h-6 text-cyan-400"/>
                        <p className="text-base sm:text-xl font-bold text-white mt-1">1597</p>
                        <p className="text-[10px] text-gray-400">Processes</p>
                    </div>
                    <div className="flex flex-col items-center justify-center bg-gray-700/30 rounded-lg p-1 sm:p-2">
                        <PuzzlePieceIcon className="w-5 h-5 sm:w-6 sm:h-6 text-cyan-400"/>
                        <p className="text-base sm:text-xl font-bold text-white mt-1">97</p>
                        <p className="text-[10px] text-gray-400">Plugins</p>
                    </div>
                    <div className="flex flex-col items-center justify-center bg-gray-700/30 rounded-lg p-1 sm:p-2">
                        <SnowflakeIcon className="w-5 h-5 sm:w-6 sm:h-6 text-cyan-400"/>
                         <p className="text-base sm:text-xl font-bold text-white mt-1">24°C</p>
                        <p className="text-[10px] text-gray-400">Core Temp</p>
                    </div>
                    <div className="flex flex-col items-center justify-center bg-gray-700/30 rounded-lg p-1 sm:p-2">
                        <MemoryChipIcon className="w-5 h-5 sm:w-6 sm:h-6 text-cyan-400"/>
                        <p className="text-base sm:text-xl font-bold text-white">84%</p>
                        <p className="text-[10px] sm:text-xs text-gray-400">RAM Usage</p>
                    </div>
                    <div className="col-span-2 flex flex-col items-center justify-center bg-gray-700/30 rounded-lg p-1 sm:p-2">
                        <CogIcon className="w-5 h-5 sm:w-6 sm:h-6 text-cyan-400"/>
                        <p className="text-base sm:text-xl font-bold text-white">37%</p>
                        <p className="text-[10px] sm:text-xs text-gray-400">Automation Capacity</p>
                    </div>
                </div>
            </div>
        </Card>
    );
};

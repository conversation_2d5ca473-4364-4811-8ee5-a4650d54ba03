{"name": "@theia/workspace", "version": "1.63.0", "description": "Theia - Workspace Extension", "dependencies": {"@theia/core": "1.63.0", "@theia/filesystem": "1.63.0", "@theia/variable-resolver": "1.63.0", "jsonc-parser": "^2.2.0", "tslib": "^2.6.2", "valid-filename": "^2.0.1"}, "publishConfig": {"access": "public"}, "theiaExtensions": [{"frontend": "lib/browser/workspace-frontend-module", "backend": "lib/node/workspace-backend-module"}, {"frontendOnly": "lib/browser-only/workspace-frontend-only-module"}], "keywords": ["theia-extension"], "license": "EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0", "repository": {"type": "git", "url": "https://github.com/eclipse-theia/theia.git"}, "bugs": {"url": "https://github.com/eclipse-theia/theia/issues"}, "homepage": "https://github.com/eclipse-theia/theia", "files": ["lib", "src"], "scripts": {"build": "theiaext build", "clean": "theiaext clean", "compile": "theiaext compile", "lint": "theiaext lint", "test": "theiaext test", "watch": "theiaext watch"}, "devDependencies": {"@theia/ext-scripts": "1.63.0"}, "nyc": {"extends": "../../configs/nyc.json"}, "gitHead": "21358137e41342742707f660b8e222f940a27652"}
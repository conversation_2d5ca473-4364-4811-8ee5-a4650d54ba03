"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/school/staff";
exports.ids = ["pages/api/school/staff"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool%2Fstaff&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool%5Cstaff.ts&middlewareConfigBase64=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool%2Fstaff&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool%5Cstaff.ts&middlewareConfigBase64=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_school_staff_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\school\\staff.ts */ \"(api)/./pages/api/school/staff.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_school_staff_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_school_staff_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/school/staff\",\n        pathname: \"/api/school/staff\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_school_staff_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool%2Fstaff&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool%5Cstaff.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./pages/api/school/staff.ts":
/*!***********************************!*\
  !*** ./pages/api/school/staff.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n// Real Staff Management Data with Professional Structure\nconst staffData = {\n    organizationChart: {\n        principal: {\n            id: \"principal-001\",\n            name: \"Dr. Sarah Mitchell\",\n            position: \"Principal & CEO\",\n            department: \"Executive Leadership\",\n            photo: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face\",\n            email: \"<EMAIL>\",\n            phone: \"+****************\",\n            experience: \"15 years\",\n            education: \"PhD in Educational Leadership, Harvard\",\n            specialization: \"AI-Enhanced Education\",\n            startDate: \"2020-01-15\",\n            salary: 250000,\n            performance: 98,\n            directReports: [\n                \"vp-academic\",\n                \"vp-operations\",\n                \"vp-technology\"\n            ]\n        },\n        leadership: [\n            {\n                id: \"vp-academic\",\n                name: \"Prof. Michael Chen\",\n                position: \"VP of Academic Affairs\",\n                department: \"Academic Leadership\",\n                photo: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\",\n                email: \"<EMAIL>\",\n                phone: \"+****************\",\n                experience: \"12 years\",\n                education: \"PhD in Quantum Physics, MIT\",\n                specialization: \"Quantum Education\",\n                startDate: \"2020-03-01\",\n                salary: 180000,\n                performance: 96,\n                reportsTo: \"principal-001\",\n                directReports: [\n                    \"dept-ai\",\n                    \"dept-quantum\",\n                    \"dept-bio\"\n                ]\n            },\n            {\n                id: \"vp-operations\",\n                name: \"Dr. Elena Rodriguez\",\n                position: \"VP of Operations\",\n                department: \"Operations\",\n                photo: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face\",\n                email: \"<EMAIL>\",\n                phone: \"+****************\",\n                experience: \"10 years\",\n                education: \"MBA Operations, Stanford\",\n                specialization: \"Educational Operations\",\n                startDate: \"2020-06-15\",\n                salary: 160000,\n                performance: 94,\n                reportsTo: \"principal-001\",\n                directReports: [\n                    \"hr-director\",\n                    \"finance-director\",\n                    \"facilities-director\"\n                ]\n            },\n            {\n                id: \"vp-technology\",\n                name: \"Dr. James Wilson\",\n                position: \"VP of Technology\",\n                department: \"Technology\",\n                photo: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face\",\n                email: \"<EMAIL>\",\n                phone: \"+****************\",\n                experience: \"14 years\",\n                education: \"PhD Computer Science, Carnegie Mellon\",\n                specialization: \"AI & Quantum Computing\",\n                startDate: \"2020-02-01\",\n                salary: 200000,\n                performance: 97,\n                reportsTo: \"principal-001\",\n                directReports: [\n                    \"it-director\",\n                    \"ai-director\",\n                    \"security-director\"\n                ]\n            }\n        ],\n        departmentHeads: [\n            {\n                id: \"dept-ai\",\n                name: \"Dr. Lisa Wang\",\n                position: \"Head of AI & Robotics\",\n                department: \"AI & Robotics\",\n                photo: \"https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face\",\n                email: \"<EMAIL>\",\n                phone: \"+****************\",\n                experience: \"8 years\",\n                education: \"PhD AI, Stanford\",\n                specialization: \"Neural Networks\",\n                startDate: \"2021-01-15\",\n                salary: 140000,\n                performance: 95,\n                reportsTo: \"vp-academic\",\n                teamSize: 18,\n                students: 412\n            },\n            {\n                id: \"dept-quantum\",\n                name: \"Prof. Ahmed Hassan\",\n                position: \"Head of Quantum Computing\",\n                department: \"Quantum Computing\",\n                photo: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face\",\n                email: \"<EMAIL>\",\n                phone: \"+****************\",\n                experience: \"11 years\",\n                education: \"PhD Quantum Physics, Oxford\",\n                specialization: \"Quantum Algorithms\",\n                startDate: \"2020-09-01\",\n                salary: 145000,\n                performance: 98,\n                reportsTo: \"vp-academic\",\n                teamSize: 16,\n                students: 298\n            },\n            {\n                id: \"dept-bio\",\n                name: \"Dr. Maria Santos\",\n                position: \"Head of Bioengineering\",\n                department: \"Bioengineering\",\n                photo: \"https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=150&h=150&fit=crop&crop=face\",\n                email: \"<EMAIL>\",\n                phone: \"+****************\",\n                experience: \"9 years\",\n                education: \"PhD Bioengineering, MIT\",\n                specialization: \"Gene Editing\",\n                startDate: \"2021-03-15\",\n                salary: 135000,\n                performance: 93,\n                reportsTo: \"vp-academic\",\n                teamSize: 12,\n                students: 267\n            }\n        ],\n        supportStaff: [\n            {\n                id: \"hr-director\",\n                name: \"Jennifer Kim\",\n                position: \"HR Director\",\n                department: \"Human Resources\",\n                photo: \"https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150&h=150&fit=crop&crop=face\",\n                email: \"<EMAIL>\",\n                phone: \"+****************\",\n                experience: \"7 years\",\n                education: \"MBA HR, Wharton\",\n                specialization: \"Talent Management\",\n                startDate: \"2021-05-01\",\n                salary: 95000,\n                performance: 91,\n                reportsTo: \"vp-operations\",\n                teamSize: 8\n            },\n            {\n                id: \"finance-director\",\n                name: \"Robert Thompson\",\n                position: \"Finance Director\",\n                department: \"Finance\",\n                photo: \"https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face\",\n                email: \"<EMAIL>\",\n                phone: \"+****************\",\n                experience: \"12 years\",\n                education: \"CPA, MBA Finance\",\n                specialization: \"Educational Finance\",\n                startDate: \"2020-08-15\",\n                salary: 110000,\n                performance: 96,\n                reportsTo: \"vp-operations\",\n                teamSize: 6\n            }\n        ]\n    },\n    statistics: {\n        totalEmployees: 156,\n        totalDepartments: 12,\n        averageTenure: 3.2,\n        averageSalary: 125000,\n        performanceAverage: 94.5,\n        retentionRate: 96.8,\n        diversityIndex: 87.3,\n        satisfactionScore: 4.6\n    },\n    recentHires: [\n        {\n            name: \"Dr. Alex Chen\",\n            position: \"Quantum AI Researcher\",\n            department: \"AI & Robotics\",\n            startDate: \"2024-03-01\",\n            photo: \"https://images.unsplash.com/photo-1519345182560-3f2917c472ef?w=150&h=150&fit=crop&crop=face\"\n        },\n        {\n            name: \"Prof. Sarah Kim\",\n            position: \"Metaverse Learning Designer\",\n            department: \"Metaverse Studies\",\n            startDate: \"2024-02-15\",\n            photo: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face\"\n        }\n    ],\n    upcomingReviews: [\n        {\n            employeeName: \"Dr. Lisa Wang\",\n            position: \"Head of AI & Robotics\",\n            reviewDate: \"2024-03-25\",\n            reviewType: \"Annual Performance Review\",\n            currentRating: 95\n        },\n        {\n            employeeName: \"Prof. Ahmed Hassan\",\n            position: \"Head of Quantum Computing\",\n            reviewDate: \"2024-03-28\",\n            reviewType: \"Promotion Review\",\n            currentRating: 98\n        }\n    ]\n};\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        // Simulate processing time\n        await new Promise((resolve)=>setTimeout(resolve, 200));\n        res.status(200).json(staffData);\n    } catch (error) {\n        console.error(\"API error:\", error);\n        res.status(500).json({\n            message: \"Internal server error\",\n            error:  true ? error.message : 0\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(api)/./pages/api/school/staff.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0"], () => (__webpack_exec__("(api)/./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fschool%2Fstaff&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cschool%5Cstaff.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();
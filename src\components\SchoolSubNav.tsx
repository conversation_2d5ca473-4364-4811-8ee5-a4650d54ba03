import React from 'react';
import { SchoolDepartment } from './SchoolHeader';
import { 
  ChartBarIcon,
  UserGroupIcon,
  DocumentTextIcon,
  CalendarIcon,
  ClipboardDocumentListIcon,
  BellIcon,
  CogIcon,
  HomeIcon,
  // Additional icons for different departments
  BookOpenIcon,
  PresentationChartBarIcon,
  AcademicCapIcon,
  BuildingLibraryIcon,
  ComputerDesktopIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon
} from '@heroicons/react/24/solid';

interface SchoolSubNavProps {
  department: SchoolDepartment;
  activeSubSection: string;
  setActiveSubSection: (section: string) => void;
}

export const SchoolSubNav: React.FC<SchoolSubNavProps> = ({
  department,
  activeSubSection,
  setActiveSubSection
}) => {
  const getSubSections = (dept: SchoolDepartment) => {
    const baseSections = [
      { id: 'dashboard', name: 'Dashboard', icon: HomeIcon, color: 'text-blue-400' },
      { id: 'analytics', name: 'Analytics', icon: ChartBarIcon, color: 'text-green-400' },
      { id: 'reports', name: 'Reports', icon: DocumentTextIcon, color: 'text-purple-400' },
      { id: 'calendar', name: 'Calendar', icon: CalendarIcon, color: 'text-yellow-400' },
      { id: 'tasks', name: 'Tasks', icon: ClipboardDocumentListIcon, color: 'text-pink-400' },
      { id: 'notifications', name: 'Notifications', icon: BellIcon, color: 'text-red-400' },
      { id: 'settings', name: 'Settings', icon: CogIcon, color: 'text-gray-400' }
    ];

    switch (dept) {
      case 'school':
        return [
          ...baseSections.slice(0, 1), // Dashboard
          { id: 'overview', name: 'Overview', icon: PresentationChartBarIcon, color: 'text-cyan-400' },
          { id: 'departments', name: 'Departments', icon: BuildingLibraryIcon, color: 'text-indigo-400' },
          { id: 'facilities', name: 'Facilities', icon: MapPinIcon, color: 'text-teal-400' },
          { id: 'events', name: 'Events', icon: CalendarIcon, color: 'text-orange-400' },
          { id: 'announcements', name: 'Announcements', icon: BellIcon, color: 'text-red-400' },
          { id: 'performance', name: 'Performance', icon: ChartBarIcon, color: 'text-green-400' },
          ...baseSections.slice(-1) // Settings
        ];
      
      case 'administration':
        return [
          ...baseSections.slice(0, 1), // Dashboard
          { id: 'staff', name: 'Staff Management', icon: UserGroupIcon, color: 'text-purple-400' },
          { id: 'policies', name: 'Policies', icon: DocumentTextIcon, color: 'text-blue-400' },
          { id: 'compliance', name: 'Compliance', icon: ClipboardDocumentListIcon, color: 'text-green-400' },
          { id: 'meetings', name: 'Meetings', icon: CalendarIcon, color: 'text-yellow-400' },
          { id: 'communications', name: 'Communications', icon: EnvelopeIcon, color: 'text-pink-400' },
          { id: 'documents', name: 'Documents', icon: DocumentTextIcon, color: 'text-cyan-400' },
          ...baseSections.slice(-1) // Settings
        ];
      
      case 'teacher':
        return [
          ...baseSections.slice(0, 1), // Dashboard
          { id: 'classes', name: 'My Classes', icon: AcademicCapIcon, color: 'text-green-400' },
          { id: 'students', name: 'Students', icon: UserGroupIcon, color: 'text-blue-400' },
          { id: 'curriculum', name: 'Curriculum', icon: BookOpenIcon, color: 'text-purple-400' },
          { id: 'assignments', name: 'Assignments', icon: ClipboardDocumentListIcon, color: 'text-yellow-400' },
          { id: 'grades', name: 'Grades', icon: ChartBarIcon, color: 'text-pink-400' },
          { id: 'resources', name: 'Resources', icon: BuildingLibraryIcon, color: 'text-cyan-400' },
          ...baseSections.slice(-1) // Settings
        ];
      
      case 'finance':
        return [
          ...baseSections.slice(0, 1), // Dashboard
          { id: 'budget', name: 'Budget', icon: ChartBarIcon, color: 'text-green-400' },
          { id: 'expenses', name: 'Expenses', icon: DocumentTextIcon, color: 'text-red-400' },
          { id: 'revenue', name: 'Revenue', icon: PresentationChartBarIcon, color: 'text-blue-400' },
          { id: 'payroll', name: 'Payroll', icon: UserGroupIcon, color: 'text-purple-400' },
          { id: 'invoices', name: 'Invoices', icon: ClipboardDocumentListIcon, color: 'text-yellow-400' },
          { id: 'audit', name: 'Audit', icon: DocumentTextIcon, color: 'text-pink-400' },
          ...baseSections.slice(-1) // Settings
        ];
      
      case 'marketing':
        return [
          ...baseSections.slice(0, 1), // Dashboard
          { id: 'campaigns', name: 'Campaigns', icon: PresentationChartBarIcon, color: 'text-pink-400' },
          { id: 'social', name: 'Social Media', icon: ComputerDesktopIcon, color: 'text-blue-400' },
          { id: 'website', name: 'Website', icon: ComputerDesktopIcon, color: 'text-green-400' },
          { id: 'events', name: 'Events', icon: CalendarIcon, color: 'text-purple-400' },
          { id: 'branding', name: 'Branding', icon: DocumentTextIcon, color: 'text-yellow-400' },
          { id: 'outreach', name: 'Outreach', icon: EnvelopeIcon, color: 'text-cyan-400' },
          ...baseSections.slice(-1) // Settings
        ];
      
      case 'parent':
        return [
          ...baseSections.slice(0, 1), // Dashboard
          { id: 'children', name: 'My Children', icon: UserGroupIcon, color: 'text-blue-400' },
          { id: 'grades', name: 'Grades', icon: ChartBarIcon, color: 'text-green-400' },
          { id: 'attendance', name: 'Attendance', icon: CalendarIcon, color: 'text-purple-400' },
          { id: 'teachers', name: 'Teachers', icon: AcademicCapIcon, color: 'text-yellow-400' },
          { id: 'payments', name: 'Payments', icon: DocumentTextIcon, color: 'text-pink-400' },
          { id: 'communication', name: 'Messages', icon: EnvelopeIcon, color: 'text-cyan-400' },
          ...baseSections.slice(-1) // Settings
        ];
      
      case 'student':
        return [
          ...baseSections.slice(0, 1), // Dashboard
          { id: 'courses', name: 'My Courses', icon: BookOpenIcon, color: 'text-blue-400' },
          { id: 'schedule', name: 'Schedule', icon: CalendarIcon, color: 'text-green-400' },
          { id: 'assignments', name: 'Assignments', icon: ClipboardDocumentListIcon, color: 'text-purple-400' },
          { id: 'grades', name: 'My Grades', icon: ChartBarIcon, color: 'text-yellow-400' },
          { id: 'library', name: 'Library', icon: BuildingLibraryIcon, color: 'text-pink-400' },
          { id: 'activities', name: 'Activities', icon: CalendarIcon, color: 'text-cyan-400' },
          ...baseSections.slice(-1) // Settings
        ];
      
      default:
        return baseSections;
    }
  };

  const subSections = getSubSections(department);

  return (
    <div className="bg-panel-bg border border-gray-700/50 rounded-xl p-3 flex flex-col gap-2 w-64 h-full">
      {/* Header */}
      <div className="pb-3 border-b border-gray-700/50">
        <h3 className="text-sm font-semibold text-white capitalize">{department} Menu</h3>
        <p className="text-xs text-gray-400 mt-1">Navigate through sections</p>
      </div>

      {/* Navigation Items */}
      <div className="flex flex-col gap-1 flex-1">
        {subSections.map((section) => {
          const IconComponent = section.icon;
          const isActive = activeSubSection === section.id;
          
          return (
            <button
              key={section.id}
              onClick={() => setActiveSubSection(section.id)}
              className={`
                group flex items-center gap-3 px-3 py-2.5 rounded-lg text-left
                transition-all duration-200 ease-out
                ${isActive 
                  ? 'bg-gray-700/60 border border-gray-600/50 shadow-lg' 
                  : 'hover:bg-gray-800/40 border border-transparent'
                }
              `}
            >
              {/* Icon */}
              <div className={`
                w-8 h-8 rounded-lg flex items-center justify-center
                transition-all duration-200
                ${isActive 
                  ? 'bg-gray-600/50' 
                  : 'bg-gray-800/50 group-hover:bg-gray-700/50'
                }
              `}>
                <IconComponent className={`
                  w-4 h-4 transition-colors duration-200
                  ${isActive ? section.color : 'text-gray-400 group-hover:text-white'}
                `} />
              </div>
              
              {/* Label */}
              <div className="flex-1">
                <span className={`
                  text-sm font-medium transition-colors duration-200
                  ${isActive ? 'text-white' : 'text-gray-300 group-hover:text-white'}
                `}>
                  {section.name}
                </span>
              </div>
              
              {/* Active indicator */}
              {isActive && (
                <div className="w-1 h-6 bg-cyan-400 rounded-full" />
              )}
            </button>
          );
        })}
      </div>
    </div>
  );
};

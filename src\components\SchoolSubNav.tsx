import React from 'react';
import { SchoolDepartment } from './Header';
import { 
  ChartBarIcon,
  UserGroupIcon,
  DocumentTextIcon,
  CalendarIcon,
  ClipboardDocumentListIcon,
  BellIcon,
  CogIcon,
  HomeIcon,
  // Additional icons for different departments
  BookOpenIcon,
  PresentationChartBarIcon,
  AcademicCapIcon,
  BuildingLibraryIcon,
  ComputerDesktopIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon
} from '@heroicons/react/24/solid';

interface SchoolSubNavProps {
  department: SchoolDepartment;
  activeSubSection: string;
  setActiveSubSection: (section: string) => void;
}

export const SchoolSubNav: React.FC<SchoolSubNavProps> = ({
  department,
  activeSubSection,
  setActiveSubSection
}) => {
  const getSubSections = (dept: SchoolDepartment) => {
    const baseSections = [
      { id: 'dashboard', name: 'Dashboard', icon: HomeIcon, color: 'text-blue-400' },
      { id: 'analytics', name: 'Analytics', icon: ChartBarIcon, color: 'text-green-400' },
      { id: 'reports', name: 'Reports', icon: DocumentTextIcon, color: 'text-purple-400' },
      { id: 'calendar', name: 'Calendar', icon: CalendarIcon, color: 'text-yellow-400' },
      { id: 'tasks', name: 'Tasks', icon: ClipboardDocumentListIcon, color: 'text-pink-400' },
      { id: 'notifications', name: 'Notifications', icon: BellIcon, color: 'text-red-400' },
      { id: 'settings', name: 'Settings', icon: CogIcon, color: 'text-gray-400' }
    ];

    switch (dept) {
      case 'school':
        return [
          ...baseSections.slice(0, 1), // Dashboard
          { id: 'overview', name: 'Overview', icon: PresentationChartBarIcon, color: 'text-cyan-400' },
          { id: 'departments', name: 'Departments', icon: BuildingLibraryIcon, color: 'text-indigo-400' },
          { id: 'facilities', name: 'Facilities', icon: MapPinIcon, color: 'text-teal-400' },
          { id: 'events', name: 'Events', icon: CalendarIcon, color: 'text-orange-400' },
          { id: 'announcements', name: 'Announcements', icon: BellIcon, color: 'text-red-400' },
          { id: 'performance', name: 'Performance', icon: ChartBarIcon, color: 'text-green-400' },
          ...baseSections.slice(-1) // Settings
        ];
      
      case 'administration':
        return [
          ...baseSections.slice(0, 1), // Dashboard
          { id: 'staff', name: 'Staff Management', icon: UserGroupIcon, color: 'text-purple-400' },
          { id: 'policies', name: 'Policies', icon: DocumentTextIcon, color: 'text-blue-400' },
          { id: 'compliance', name: 'Compliance', icon: ClipboardDocumentListIcon, color: 'text-green-400' },
          { id: 'meetings', name: 'Meetings', icon: CalendarIcon, color: 'text-yellow-400' },
          { id: 'communications', name: 'Communications', icon: EnvelopeIcon, color: 'text-pink-400' },
          { id: 'documents', name: 'Documents', icon: DocumentTextIcon, color: 'text-cyan-400' },
          ...baseSections.slice(-1) // Settings
        ];
      
      case 'teacher':
        return [
          ...baseSections.slice(0, 1), // Dashboard
          { id: 'classes', name: 'My Classes', icon: AcademicCapIcon, color: 'text-green-400' },
          { id: 'students', name: 'Students', icon: UserGroupIcon, color: 'text-blue-400' },
          { id: 'curriculum', name: 'Curriculum', icon: BookOpenIcon, color: 'text-purple-400' },
          { id: 'assignments', name: 'Assignments', icon: ClipboardDocumentListIcon, color: 'text-yellow-400' },
          { id: 'grades', name: 'Grades', icon: ChartBarIcon, color: 'text-pink-400' },
          { id: 'resources', name: 'Resources', icon: BuildingLibraryIcon, color: 'text-cyan-400' },
          ...baseSections.slice(-1) // Settings
        ];
      
      case 'finance':
        return [
          ...baseSections.slice(0, 1), // Dashboard
          { id: 'budget', name: 'Budget', icon: ChartBarIcon, color: 'text-green-400' },
          { id: 'expenses', name: 'Expenses', icon: DocumentTextIcon, color: 'text-red-400' },
          { id: 'revenue', name: 'Revenue', icon: PresentationChartBarIcon, color: 'text-blue-400' },
          { id: 'payroll', name: 'Payroll', icon: UserGroupIcon, color: 'text-purple-400' },
          { id: 'invoices', name: 'Invoices', icon: ClipboardDocumentListIcon, color: 'text-yellow-400' },
          { id: 'audit', name: 'Audit', icon: DocumentTextIcon, color: 'text-pink-400' },
          ...baseSections.slice(-1) // Settings
        ];
      
      case 'marketing':
        return [
          ...baseSections.slice(0, 1), // Dashboard
          { id: 'campaigns', name: 'Campaigns', icon: PresentationChartBarIcon, color: 'text-pink-400' },
          { id: 'social', name: 'Social Media', icon: ComputerDesktopIcon, color: 'text-blue-400' },
          { id: 'website', name: 'Website', icon: ComputerDesktopIcon, color: 'text-green-400' },
          { id: 'events', name: 'Events', icon: CalendarIcon, color: 'text-purple-400' },
          { id: 'branding', name: 'Branding', icon: DocumentTextIcon, color: 'text-yellow-400' },
          { id: 'outreach', name: 'Outreach', icon: EnvelopeIcon, color: 'text-cyan-400' },
          ...baseSections.slice(-1) // Settings
        ];
      
      case 'parent':
        return [
          ...baseSections.slice(0, 1), // Dashboard
          { id: 'children', name: 'My Children', icon: UserGroupIcon, color: 'text-blue-400' },
          { id: 'grades', name: 'Grades', icon: ChartBarIcon, color: 'text-green-400' },
          { id: 'attendance', name: 'Attendance', icon: CalendarIcon, color: 'text-purple-400' },
          { id: 'teachers', name: 'Teachers', icon: AcademicCapIcon, color: 'text-yellow-400' },
          { id: 'payments', name: 'Payments', icon: DocumentTextIcon, color: 'text-pink-400' },
          { id: 'communication', name: 'Messages', icon: EnvelopeIcon, color: 'text-cyan-400' },
          ...baseSections.slice(-1) // Settings
        ];
      
      case 'student':
        return [
          ...baseSections.slice(0, 1), // Dashboard
          { id: 'courses', name: 'My Courses', icon: BookOpenIcon, color: 'text-blue-400' },
          { id: 'schedule', name: 'Schedule', icon: CalendarIcon, color: 'text-green-400' },
          { id: 'assignments', name: 'Assignments', icon: ClipboardDocumentListIcon, color: 'text-purple-400' },
          { id: 'grades', name: 'My Grades', icon: ChartBarIcon, color: 'text-yellow-400' },
          { id: 'library', name: 'Library', icon: BuildingLibraryIcon, color: 'text-pink-400' },
          { id: 'activities', name: 'Activities', icon: CalendarIcon, color: 'text-cyan-400' },
          ...baseSections.slice(-1) // Settings
        ];
      
      default:
        return baseSections;
    }
  };

  const subSections = getSubSections(department);

  return (
    <div className="flex flex-col h-full">
      {/* Header with more space */}
      <div className="bg-panel-bg border border-gray-700/50 responsive-border-radius" style={{ padding: 'calc(var(--base-spacing) * 1)', marginBottom: 'calc(var(--base-gap) * 2)' }}>
        <h3 className="font-semibold text-white capitalize" style={{ fontSize: 'calc(var(--base-font-size) * 0.875)' }}>{department} Menu</h3>
        <p className="text-gray-400 mt-1" style={{ fontSize: 'calc(var(--base-font-size) * 0.75)' }}>Navigate sections</p>
      </div>

      {/* Neumorphic HUD Navigation - No scroll, better spacing */}
      <div className="flex-1">
        <div className="subnav-hud-vertical">
          {subSections.slice(0, 6).map((section) => {
            const IconComponent = section.icon;
            const isActive = activeSubSection === section.id;

            return (
              <button
                key={section.id}
                onClick={() => setActiveSubSection(section.id)}
                className={`subnav-button ${isActive ? 'active' : ''}`}
              >
                {/* Icon */}
                <IconComponent className="subnav-icon" />

                {/* Label */}
                <span className="subnav-label">{section.name}</span>
              </button>
            );
          })}
        </div>
      </div>
    </div>
  );
};

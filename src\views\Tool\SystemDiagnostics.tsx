
import React from 'react';
import { Card } from '../../components/Card';

const Gauge = ({ label, value, color }: { label: string, value: number, color: string }) => {
  const radius = 40;
  const circumference = 2 * Math.PI * radius;
  const offset = circumference - (value / 100) * circumference;

  return (
    <div className="flex flex-col items-center gap-2">
      <div className="relative w-24 h-24">
        <svg className="w-full h-full" viewBox="0 0 100 100">
          <circle cx="50" cy="50" r={radius} fill="none" stroke="rgba(255,255,255,0.1)" strokeWidth="8"/>
          <circle
            cx="50"
            cy="50"
            r={radius}
            fill="none"
            stroke={color}
            strokeWidth="8"
            strokeDasharray={circumference}
            strokeDashoffset={offset}
            strokeLinecap="round"
            transform="rotate(-90 50 50)"
          />
        </svg>
        <div className="absolute inset-0 flex items-center justify-center text-white text-xl font-bold">
          {value}%
        </div>
      </div>
      <p className="text-sm text-gray-300">{label}</p>
    </div>
  );
}

export type SystemDiagnosticsData = {
    cpuLoad: number;
    ramUsage: number;
}

interface SystemDiagnosticsProps {
    diagnostics: SystemDiagnosticsData;
}

export const SystemDiagnostics: React.FC<SystemDiagnosticsProps> = ({ diagnostics }) => {
  if (!diagnostics) {
    return (
      <Card title="System Diagnostics">
        <div className="flex justify-center items-center h-full text-gray-400">
          Loading diagnostics...
        </div>
      </Card>
    );
  }

  return (
    <Card title="System Diagnostics">
      <div className="flex justify-around items-center h-full">
        <Gauge label="CPU Load" value={diagnostics.cpuLoad} color="#00FFFF" />
        <Gauge label="RAM Usage" value={diagnostics.ramUsage} color="#FF007F" />
      </div>
    </Card>
  );
};

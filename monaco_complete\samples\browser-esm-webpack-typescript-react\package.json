{"name": "monaco-esm-webpack-typescript", "scripts": {"start": "node ../node_modules/webpack-dev-server/bin/webpack-dev-server.js", "build": "NODE_ENV='production' node ../node_modules/webpack/bin/webpack.js --progress"}, "dependencies": {}, "devDependencies": {"@babel/core": "^7.17.0", "@babel/preset-env": "^7.16.11", "@babel/preset-react": "^7.16.7", "@babel/preset-typescript": "^7.16.7", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.4", "@types/react": "^17.0.39", "@types/react-dom": "^17.0.11", "babel-loader": "^8.2.3", "react": "^17.0.2", "react-dom": "^17.0.2", "react-refresh": "^0.11.0"}}
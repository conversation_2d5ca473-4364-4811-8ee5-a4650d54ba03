"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[1]!./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[2]!./styles/globals.css":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[1]!./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[2]!./styles/globals.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_pnpm_next_14_2_3_react_dom_18_2_0_react_18_2_0_react_18_2_0_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_pnpm_next_14_2_3_react_dom_18_2_0_react_18_2_0_react_18_2_0_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_pnpm_next_14_2_3_react_dom_18_2_0_react_18_2_0_react_18_2_0_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_pnpm_next_14_2_3_react_dom_18_2_0_react_18_2_0_react_18_2_0_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');\\n\\n*, ::before, ::after{\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n::backdrop{\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*/\\n\\n/*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\n\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\"; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\n\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\n.container{\\n  width: 100%;\\n}\\n.\\\\!container{\\n  width: 100% !important;\\n}\\n@media (min-width: 640px){\\n\\n  .container{\\n    max-width: 640px;\\n  }\\n\\n  .\\\\!container{\\n    max-width: 640px !important;\\n  }\\n}\\n@media (min-width: 768px){\\n\\n  .container{\\n    max-width: 768px;\\n  }\\n\\n  .\\\\!container{\\n    max-width: 768px !important;\\n  }\\n}\\n@media (min-width: 1024px){\\n\\n  .container{\\n    max-width: 1024px;\\n  }\\n\\n  .\\\\!container{\\n    max-width: 1024px !important;\\n  }\\n}\\n@media (min-width: 1280px){\\n\\n  .container{\\n    max-width: 1280px;\\n  }\\n\\n  .\\\\!container{\\n    max-width: 1280px !important;\\n  }\\n}\\n@media (min-width: 1536px){\\n\\n  .container{\\n    max-width: 1536px;\\n  }\\n\\n  .\\\\!container{\\n    max-width: 1536px !important;\\n  }\\n}\\n.visible{\\n  visibility: visible;\\n}\\n.\\\\!visible{\\n  visibility: visible !important;\\n}\\n.invisible{\\n  visibility: hidden;\\n}\\n.collapse{\\n  visibility: collapse;\\n}\\n.static{\\n  position: static;\\n}\\n.fixed{\\n  position: fixed;\\n}\\n.absolute{\\n  position: absolute;\\n}\\n.relative{\\n  position: relative;\\n}\\n.sticky{\\n  position: sticky;\\n}\\n.inset-0{\\n  inset: 0px;\\n}\\n.-bottom-1{\\n  bottom: -0.25rem;\\n}\\n.-left-1\\\\/2{\\n  left: -50%;\\n}\\n.-right-1{\\n  right: -0.25rem;\\n}\\n.-top-1{\\n  top: -0.25rem;\\n}\\n.-top-1\\\\/2{\\n  top: -50%;\\n}\\n.left-0{\\n  left: 0px;\\n}\\n.left-1\\\\/2{\\n  left: 50%;\\n}\\n.left-20{\\n  left: 5rem;\\n}\\n.right-4{\\n  right: 1rem;\\n}\\n.top-0{\\n  top: 0px;\\n}\\n.top-1\\\\/2{\\n  top: 50%;\\n}\\n.top-4{\\n  top: 1rem;\\n}\\n.isolate{\\n  isolation: isolate;\\n}\\n.z-10{\\n  z-index: 10;\\n}\\n.z-30{\\n  z-index: 30;\\n}\\n.z-40{\\n  z-index: 40;\\n}\\n.z-50{\\n  z-index: 50;\\n}\\n.col-span-2{\\n  grid-column: span 2 / span 2;\\n}\\n.mx-auto{\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n.\\\\!mt-6{\\n  margin-top: 1.5rem !important;\\n}\\n.mb-1{\\n  margin-bottom: 0.25rem;\\n}\\n.mb-2{\\n  margin-bottom: 0.5rem;\\n}\\n.mb-3{\\n  margin-bottom: 0.75rem;\\n}\\n.mb-4{\\n  margin-bottom: 1rem;\\n}\\n.mb-6{\\n  margin-bottom: 1.5rem;\\n}\\n.mb-8{\\n  margin-bottom: 2rem;\\n}\\n.mr-2{\\n  margin-right: 0.5rem;\\n}\\n.mt-1{\\n  margin-top: 0.25rem;\\n}\\n.mt-2{\\n  margin-top: 0.5rem;\\n}\\n.mt-3{\\n  margin-top: 0.75rem;\\n}\\n.mt-4{\\n  margin-top: 1rem;\\n}\\n.mt-8{\\n  margin-top: 2rem;\\n}\\n.ml-1{\\n  margin-left: 0.25rem;\\n}\\n.mt-6{\\n  margin-top: 1.5rem;\\n}\\n.block{\\n  display: block;\\n}\\n.inline-block{\\n  display: inline-block;\\n}\\n.inline{\\n  display: inline;\\n}\\n.flex{\\n  display: flex;\\n}\\n.table{\\n  display: table;\\n}\\n.grid{\\n  display: grid;\\n}\\n.inline-grid{\\n  display: inline-grid;\\n}\\n.contents{\\n  display: contents;\\n}\\n.hidden{\\n  display: none;\\n}\\n.h-0\\\\.5{\\n  height: 0.125rem;\\n}\\n.h-1{\\n  height: 0.25rem;\\n}\\n.h-10{\\n  height: 2.5rem;\\n}\\n.h-12{\\n  height: 3rem;\\n}\\n.h-14{\\n  height: 3.5rem;\\n}\\n.h-16{\\n  height: 4rem;\\n}\\n.h-2{\\n  height: 0.5rem;\\n}\\n.h-24{\\n  height: 6rem;\\n}\\n.h-3{\\n  height: 0.75rem;\\n}\\n.h-32{\\n  height: 8rem;\\n}\\n.h-4{\\n  height: 1rem;\\n}\\n.h-40{\\n  height: 10rem;\\n}\\n.h-48{\\n  height: 12rem;\\n}\\n.h-5{\\n  height: 1.25rem;\\n}\\n.h-6{\\n  height: 1.5rem;\\n}\\n.h-64{\\n  height: 16rem;\\n}\\n.h-8{\\n  height: 2rem;\\n}\\n.h-\\\\[200\\\\%\\\\]{\\n  height: 200%;\\n}\\n.h-full{\\n  height: 100%;\\n}\\n.max-h-64{\\n  max-height: 16rem;\\n}\\n.min-h-0{\\n  min-height: 0px;\\n}\\n.min-h-\\\\[400px\\\\]{\\n  min-height: 400px;\\n}\\n.w-1{\\n  width: 0.25rem;\\n}\\n.w-1\\\\/4{\\n  width: 25%;\\n}\\n.w-10{\\n  width: 2.5rem;\\n}\\n.w-12{\\n  width: 3rem;\\n}\\n.w-14{\\n  width: 3.5rem;\\n}\\n.w-16{\\n  width: 4rem;\\n}\\n.w-2{\\n  width: 0.5rem;\\n}\\n.w-24{\\n  width: 6rem;\\n}\\n.w-3{\\n  width: 0.75rem;\\n}\\n.w-32{\\n  width: 8rem;\\n}\\n.w-4{\\n  width: 1rem;\\n}\\n.w-5{\\n  width: 1.25rem;\\n}\\n.w-6{\\n  width: 1.5rem;\\n}\\n.w-8{\\n  width: 2rem;\\n}\\n.w-\\\\[200\\\\%\\\\]{\\n  width: 200%;\\n}\\n.w-full{\\n  width: 100%;\\n}\\n.w-64{\\n  width: 16rem;\\n}\\n.w-80{\\n  width: 20rem;\\n}\\n.max-w-md{\\n  max-width: 28rem;\\n}\\n.max-w-sm{\\n  max-width: 24rem;\\n}\\n.flex-1{\\n  flex: 1 1 0%;\\n}\\n.flex-shrink-0{\\n  flex-shrink: 0;\\n}\\n.shrink{\\n  flex-shrink: 1;\\n}\\n.flex-grow{\\n  flex-grow: 1;\\n}\\n.grow{\\n  flex-grow: 1;\\n}\\n.-translate-x-1\\\\/2{\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.-translate-y-1\\\\/2{\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.translate-x-0{\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.translate-x-5{\\n  --tw-translate-x: 1.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.scale-105{\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.scale-90{\\n  --tw-scale-x: .9;\\n  --tw-scale-y: .9;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.scale-95{\\n  --tw-scale-x: .95;\\n  --tw-scale-y: .95;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.transform{\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.\\\\!transform{\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important;\\n}\\n@keyframes pulse{\\n\\n  50%{\\n    opacity: .5;\\n  }\\n}\\n.animate-pulse{\\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\n@keyframes spin{\\n\\n  to{\\n    transform: rotate(360deg);\\n  }\\n}\\n.animate-spin{\\n  animation: spin 1s linear infinite;\\n}\\n.cursor-pointer{\\n  cursor: pointer;\\n}\\n.resize{\\n  resize: both;\\n}\\n.grid-cols-1{\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\n.grid-cols-2{\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\n.grid-cols-3{\\n  grid-template-columns: repeat(3, minmax(0, 1fr));\\n}\\n.grid-cols-4{\\n  grid-template-columns: repeat(4, minmax(0, 1fr));\\n}\\n.flex-col{\\n  flex-direction: column;\\n}\\n.flex-wrap{\\n  flex-wrap: wrap;\\n}\\n.items-start{\\n  align-items: flex-start;\\n}\\n.items-end{\\n  align-items: flex-end;\\n}\\n.items-center{\\n  align-items: center;\\n}\\n.justify-end{\\n  justify-content: flex-end;\\n}\\n.justify-center{\\n  justify-content: center;\\n}\\n.justify-between{\\n  justify-content: space-between;\\n}\\n.justify-around{\\n  justify-content: space-around;\\n}\\n.gap-1{\\n  gap: 0.25rem;\\n}\\n.gap-2{\\n  gap: 0.5rem;\\n}\\n.gap-3{\\n  gap: 0.75rem;\\n}\\n.gap-4{\\n  gap: 1rem;\\n}\\n.gap-6{\\n  gap: 1.5rem;\\n}\\n.space-y-1 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\n.space-y-2 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-3 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\n.space-y-4 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\n.overflow-auto{\\n  overflow: auto;\\n}\\n.overflow-hidden{\\n  overflow: hidden;\\n}\\n.overflow-x-auto{\\n  overflow-x: auto;\\n}\\n.overflow-y-auto{\\n  overflow-y: auto;\\n}\\n.truncate{\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.whitespace-nowrap{\\n  white-space: nowrap;\\n}\\n.rounded-full{\\n  border-radius: 9999px;\\n}\\n.rounded-lg{\\n  border-radius: 0.5rem;\\n}\\n.rounded-md{\\n  border-radius: 0.375rem;\\n}\\n.rounded-sm{\\n  border-radius: 0.125rem;\\n}\\n.rounded-xl{\\n  border-radius: 0.75rem;\\n}\\n.rounded{\\n  border-radius: 0.25rem;\\n}\\n.rounded-r-full{\\n  border-top-right-radius: 9999px;\\n  border-bottom-right-radius: 9999px;\\n}\\n.border{\\n  border-width: 1px;\\n}\\n.border-2{\\n  border-width: 2px;\\n}\\n.border-b-2{\\n  border-bottom-width: 2px;\\n}\\n.border-t{\\n  border-top-width: 1px;\\n}\\n.border-b{\\n  border-bottom-width: 1px;\\n}\\n.border-l{\\n  border-left-width: 1px;\\n}\\n.border-r{\\n  border-right-width: 1px;\\n}\\n.border-none{\\n  border-style: none;\\n}\\n.border-blue-400\\\\/30{\\n  border-color: rgb(96 165 250 / 0.3);\\n}\\n.border-blue-500\\\\/30{\\n  border-color: rgb(59 130 246 / 0.3);\\n}\\n.border-brand-cyan{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(0 255 255 / var(--tw-border-opacity, 1));\\n}\\n.border-cyan-400{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(34 211 238 / var(--tw-border-opacity, 1));\\n}\\n.border-cyan-400\\\\/30{\\n  border-color: rgb(34 211 238 / 0.3);\\n}\\n.border-cyan-500\\\\/30{\\n  border-color: rgb(6 182 212 / 0.3);\\n}\\n.border-cyan-500\\\\/50{\\n  border-color: rgb(6 182 212 / 0.5);\\n}\\n.border-gray-400\\\\/30{\\n  border-color: rgb(156 163 175 / 0.3);\\n}\\n.border-gray-500\\\\/40{\\n  border-color: rgb(107 114 128 / 0.4);\\n}\\n.border-gray-600{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-600\\\\/30{\\n  border-color: rgb(75 85 99 / 0.3);\\n}\\n.border-gray-700{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-700\\\\/30{\\n  border-color: rgb(55 65 81 / 0.3);\\n}\\n.border-gray-700\\\\/50{\\n  border-color: rgb(55 65 81 / 0.5);\\n}\\n.border-green-400\\\\/30{\\n  border-color: rgb(74 222 128 / 0.3);\\n}\\n.border-green-500\\\\/30{\\n  border-color: rgb(34 197 94 / 0.3);\\n}\\n.border-pink-400\\\\/30{\\n  border-color: rgb(244 114 182 / 0.3);\\n}\\n.border-pink-500\\\\/90{\\n  border-color: rgb(236 72 153 / 0.9);\\n}\\n.border-purple-400\\\\/30{\\n  border-color: rgb(192 132 252 / 0.3);\\n}\\n.border-purple-400\\\\/40{\\n  border-color: rgb(192 132 252 / 0.4);\\n}\\n.border-purple-500\\\\/30{\\n  border-color: rgb(168 85 247 / 0.3);\\n}\\n.border-red-400\\\\/30{\\n  border-color: rgb(248 113 113 / 0.3);\\n}\\n.border-red-500\\\\/30{\\n  border-color: rgb(239 68 68 / 0.3);\\n}\\n.border-yellow-400\\\\/30{\\n  border-color: rgb(250 204 21 / 0.3);\\n}\\n.border-yellow-400\\\\/50{\\n  border-color: rgb(250 204 21 / 0.5);\\n}\\n.border-yellow-500\\\\/30{\\n  border-color: rgb(234 179 8 / 0.3);\\n}\\n.border-blue-400{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));\\n}\\n.bg-black\\\\/20{\\n  background-color: rgb(0 0 0 / 0.2);\\n}\\n.bg-black\\\\/60{\\n  background-color: rgb(0 0 0 / 0.6);\\n}\\n.bg-black\\\\/70{\\n  background-color: rgb(0 0 0 / 0.7);\\n}\\n.bg-blue-400{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-500\\\\/20{\\n  background-color: rgb(59 130 246 / 0.2);\\n}\\n.bg-brand-cyan{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-brand-cyan\\\\/20{\\n  background-color: rgb(0 255 255 / 0.2);\\n}\\n.bg-brand-pink\\\\/20{\\n  background-color: rgb(255 0 127 / 0.2);\\n}\\n.bg-container-bg{\\n  background-color: rgba(10, 20, 35, 0.7);\\n}\\n.bg-cyan-400{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 211 238 / var(--tw-bg-opacity, 1));\\n}\\n.bg-cyan-400\\\\/10{\\n  background-color: rgb(34 211 238 / 0.1);\\n}\\n.bg-cyan-400\\\\/20{\\n  background-color: rgb(34 211 238 / 0.2);\\n}\\n.bg-cyan-400\\\\/50{\\n  background-color: rgb(34 211 238 / 0.5);\\n}\\n.bg-cyan-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(6 182 212 / var(--tw-bg-opacity, 1));\\n}\\n.bg-cyan-500\\\\/20{\\n  background-color: rgb(6 182 212 / 0.2);\\n}\\n.bg-cyan-500\\\\/30{\\n  background-color: rgb(6 182 212 / 0.3);\\n}\\n.bg-cyan-500\\\\/50{\\n  background-color: rgb(6 182 212 / 0.5);\\n}\\n.bg-emerald-500\\\\/20{\\n  background-color: rgb(16 185 129 / 0.2);\\n}\\n.bg-gray-200\\\\/20{\\n  background-color: rgb(229 231 235 / 0.2);\\n}\\n.bg-gray-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-500\\\\/20{\\n  background-color: rgb(107 114 128 / 0.2);\\n}\\n.bg-gray-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-700{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-700\\\\/30{\\n  background-color: rgb(55 65 81 / 0.3);\\n}\\n.bg-gray-700\\\\/50{\\n  background-color: rgb(55 65 81 / 0.5);\\n}\\n.bg-gray-700\\\\/60{\\n  background-color: rgb(55 65 81 / 0.6);\\n}\\n.bg-gray-700\\\\/80{\\n  background-color: rgb(55 65 81 / 0.8);\\n}\\n.bg-gray-800\\\\/40{\\n  background-color: rgb(31 41 55 / 0.4);\\n}\\n.bg-green-400{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-500\\\\/20{\\n  background-color: rgb(34 197 94 / 0.2);\\n}\\n.bg-panel-bg{\\n  background-color: rgba(25, 40, 60, 0.6);\\n}\\n.bg-pink-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(236 72 153 / var(--tw-bg-opacity, 1));\\n}\\n.bg-pink-500\\\\/20{\\n  background-color: rgb(236 72 153 / 0.2);\\n}\\n.bg-pink-500\\\\/80{\\n  background-color: rgb(236 72 153 / 0.8);\\n}\\n.bg-purple-500\\\\/20{\\n  background-color: rgb(168 85 247 / 0.2);\\n}\\n.bg-red-500\\\\/20{\\n  background-color: rgb(239 68 68 / 0.2);\\n}\\n.bg-white{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-white\\\\/20{\\n  background-color: rgb(255 255 255 / 0.2);\\n}\\n.bg-yellow-400{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-500\\\\/20{\\n  background-color: rgb(234 179 8 / 0.2);\\n}\\n.bg-gray-800\\\\/60{\\n  background-color: rgb(31 41 55 / 0.6);\\n}\\n.bg-black\\\\/80{\\n  background-color: rgb(0 0 0 / 0.8);\\n}\\n.bg-blue-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-900\\\\/30{\\n  background-color: rgb(17 24 39 / 0.3);\\n}\\n.bg-gray-900\\\\/50{\\n  background-color: rgb(17 24 39 / 0.5);\\n}\\n.bg-green-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\\n}\\n.bg-transparent{\\n  background-color: transparent;\\n}\\n.bg-gray-800{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n}\\n.bg-\\\\[radial-gradient\\\\(circle_at_center\\\\2c _rgba\\\\(255\\\\2c 0\\\\2c 127\\\\2c 0\\\\.5\\\\)\\\\2c _transparent_40\\\\%\\\\)\\\\]{\\n  background-image: radial-gradient(circle at center, rgba(255,0,127,0.5), transparent 40%);\\n}\\n.bg-gradient-to-br{\\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\\n}\\n.bg-gradient-to-r{\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n}\\n.from-\\\\[\\\\#FF007F\\\\]{\\n  --tw-gradient-from: #FF007F var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(255 0 127 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-400\\\\/20{\\n  --tw-gradient-from: rgb(96 165 250 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-500{\\n  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-900\\\\/20{\\n  --tw-gradient-from: rgb(30 58 138 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-cyan-400{\\n  --tw-gradient-from: #22d3ee var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 211 238 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-cyan-400\\\\/20{\\n  --tw-gradient-from: rgb(34 211 238 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 211 238 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-cyan-500{\\n  --tw-gradient-from: #06b6d4 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(6 182 212 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-cyan-900\\\\/20{\\n  --tw-gradient-from: rgb(22 78 99 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(22 78 99 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-gray-800\\\\/40{\\n  --tw-gradient-from: rgb(31 41 55 / 0.4) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-gray-800\\\\/60{\\n  --tw-gradient-from: rgb(31 41 55 / 0.6) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-gray-900\\\\/40{\\n  --tw-gradient-from: rgb(17 24 39 / 0.4) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-green-400\\\\/20{\\n  --tw-gradient-from: rgb(74 222 128 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(74 222 128 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-green-500{\\n  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-green-900\\\\/20{\\n  --tw-gradient-from: rgb(20 83 45 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(20 83 45 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-purple-400\\\\/20{\\n  --tw-gradient-from: rgb(192 132 252 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(192 132 252 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-purple-500{\\n  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-purple-900\\\\/20{\\n  --tw-gradient-from: rgb(88 28 135 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-purple-900\\\\/30{\\n  --tw-gradient-from: rgb(88 28 135 / 0.3) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-red-500{\\n  --tw-gradient-from: #ef4444 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-red-900\\\\/20{\\n  --tw-gradient-from: rgb(127 29 29 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(127 29 29 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-white{\\n  --tw-gradient-from: #fff var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-yellow-400\\\\/20{\\n  --tw-gradient-from: rgb(250 204 21 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(250 204 21 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-yellow-500{\\n  --tw-gradient-from: #eab308 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(234 179 8 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-yellow-900\\\\/20{\\n  --tw-gradient-from: rgb(113 63 18 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(113 63 18 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.to-\\\\[\\\\#d6006a\\\\]{\\n  --tw-gradient-to: #d6006a var(--tw-gradient-to-position);\\n}\\n.to-blue-500{\\n  --tw-gradient-to: #3b82f6 var(--tw-gradient-to-position);\\n}\\n.to-blue-500\\\\/20{\\n  --tw-gradient-to: rgb(59 130 246 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-blue-600{\\n  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);\\n}\\n.to-blue-600\\\\/20{\\n  --tw-gradient-to: rgb(37 99 235 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-blue-800\\\\/20{\\n  --tw-gradient-to: rgb(30 64 175 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-blue-900\\\\/20{\\n  --tw-gradient-to: rgb(30 58 138 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-blue-900\\\\/30{\\n  --tw-gradient-to: rgb(30 58 138 / 0.3) var(--tw-gradient-to-position);\\n}\\n.to-cyan-600{\\n  --tw-gradient-to: #0891b2 var(--tw-gradient-to-position);\\n}\\n.to-cyan-800\\\\/20{\\n  --tw-gradient-to: rgb(21 94 117 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-cyan-900\\\\/20{\\n  --tw-gradient-to: rgb(22 78 99 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-emerald-600{\\n  --tw-gradient-to: #059669 var(--tw-gradient-to-position);\\n}\\n.to-emerald-900\\\\/20{\\n  --tw-gradient-to: rgb(6 78 59 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-fuchsia-500{\\n  --tw-gradient-to: #d946ef var(--tw-gradient-to-position);\\n}\\n.to-gray-700\\\\/40{\\n  --tw-gradient-to: rgb(55 65 81 / 0.4) var(--tw-gradient-to-position);\\n}\\n.to-gray-700\\\\/60{\\n  --tw-gradient-to: rgb(55 65 81 / 0.6) var(--tw-gradient-to-position);\\n}\\n.to-gray-800\\\\/40{\\n  --tw-gradient-to: rgb(31 41 55 / 0.4) var(--tw-gradient-to-position);\\n}\\n.to-gray-900\\\\/60{\\n  --tw-gradient-to: rgb(17 24 39 / 0.6) var(--tw-gradient-to-position);\\n}\\n.to-green-600\\\\/20{\\n  --tw-gradient-to: rgb(22 163 74 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-green-800\\\\/20{\\n  --tw-gradient-to: rgb(22 101 52 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-orange-600{\\n  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);\\n}\\n.to-orange-900\\\\/20{\\n  --tw-gradient-to: rgb(124 45 18 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-pink-600{\\n  --tw-gradient-to: #db2777 var(--tw-gradient-to-position);\\n}\\n.to-pink-900\\\\/20{\\n  --tw-gradient-to: rgb(131 24 67 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-purple-600{\\n  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);\\n}\\n.to-purple-600\\\\/20{\\n  --tw-gradient-to: rgb(147 51 234 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-purple-800\\\\/20{\\n  --tw-gradient-to: rgb(107 33 168 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-purple-900\\\\/20{\\n  --tw-gradient-to: rgb(88 28 135 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-transparent{\\n  --tw-gradient-to: transparent var(--tw-gradient-to-position);\\n}\\n.to-yellow-600\\\\/20{\\n  --tw-gradient-to: rgb(202 138 4 / 0.2) var(--tw-gradient-to-position);\\n}\\n.object-cover{\\n  -o-object-fit: cover;\\n     object-fit: cover;\\n}\\n.p-0{\\n  padding: 0px;\\n}\\n.p-1{\\n  padding: 0.25rem;\\n}\\n.p-2{\\n  padding: 0.5rem;\\n}\\n.p-3{\\n  padding: 0.75rem;\\n}\\n.p-4{\\n  padding: 1rem;\\n}\\n.p-6{\\n  padding: 1.5rem;\\n}\\n.px-2{\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\n.px-3{\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\n.px-4{\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n.py-1{\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\n.py-1\\\\.5{\\n  padding-top: 0.375rem;\\n  padding-bottom: 0.375rem;\\n}\\n.py-2{\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\n.pb-1{\\n  padding-bottom: 0.25rem;\\n}\\n.pt-2{\\n  padding-top: 0.5rem;\\n}\\n.pt-4{\\n  padding-top: 1rem;\\n}\\n.text-left{\\n  text-align: left;\\n}\\n.text-center{\\n  text-align: center;\\n}\\n.text-right{\\n  text-align: right;\\n}\\n.font-poppins{\\n  font-family: Poppins, sans-serif;\\n}\\n.font-mono{\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace;\\n}\\n.text-2xl{\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\n.text-3xl{\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\n.text-\\\\[10px\\\\]{\\n  font-size: 10px;\\n}\\n.text-base{\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\n.text-lg{\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\n.text-sm{\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n.text-xl{\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n.text-xs{\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\n.text-4xl{\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\n.text-6xl{\\n  font-size: 3.75rem;\\n  line-height: 1;\\n}\\n.font-bold{\\n  font-weight: 700;\\n}\\n.font-medium{\\n  font-weight: 500;\\n}\\n.font-normal{\\n  font-weight: 400;\\n}\\n.font-semibold{\\n  font-weight: 600;\\n}\\n.uppercase{\\n  text-transform: uppercase;\\n}\\n.capitalize{\\n  text-transform: capitalize;\\n}\\n.leading-relaxed{\\n  line-height: 1.625;\\n}\\n.tracking-wider{\\n  letter-spacing: 0.05em;\\n}\\n.text-\\\\[\\\\#A0A0B0\\\\]{\\n  --tw-text-opacity: 1;\\n  color: rgb(160 160 176 / var(--tw-text-opacity, 1));\\n}\\n.text-\\\\[\\\\#E0E0E0\\\\]{\\n  --tw-text-opacity: 1;\\n  color: rgb(224 224 224 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\\n}\\n.text-brand-cyan{\\n  --tw-text-opacity: 1;\\n  color: rgb(0 255 255 / var(--tw-text-opacity, 1));\\n}\\n.text-cyan-300{\\n  --tw-text-opacity: 1;\\n  color: rgb(103 232 249 / var(--tw-text-opacity, 1));\\n}\\n.text-cyan-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(34 211 238 / var(--tw-text-opacity, 1));\\n}\\n.text-emerald-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(52 211 153 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-300{\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-500{\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-900{\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\n.text-green-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\\n}\\n.text-indigo-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(129 140 248 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(251 146 60 / var(--tw-text-opacity, 1));\\n}\\n.text-pink-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(244 114 182 / var(--tw-text-opacity, 1));\\n}\\n.text-purple-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(192 132 252 / var(--tw-text-opacity, 1));\\n}\\n.text-red-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\\n}\\n.text-teal-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(45 212 191 / var(--tw-text-opacity, 1));\\n}\\n.text-white{\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\\n}\\n.overline{\\n  text-decoration-line: overline;\\n}\\n.opacity-0{\\n  opacity: 0;\\n}\\n.opacity-30{\\n  opacity: 0.3;\\n}\\n.opacity-50{\\n  opacity: 0.5;\\n}\\n.opacity-90{\\n  opacity: 0.9;\\n}\\n.shadow-inner{\\n  --tw-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: inset 0 2px 4px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-lg{\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-md{\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow{\\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.outline-none{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.outline{\\n  outline-style: solid;\\n}\\n.ring{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.blur-sm{\\n  --tw-blur: blur(4px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.blur{\\n  --tw-blur: blur(8px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.drop-shadow{\\n  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.invert{\\n  --tw-invert: invert(100%);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.filter{\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.backdrop-blur-sm{\\n  --tw-backdrop-blur: blur(4px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.backdrop-blur-xl{\\n  --tw-backdrop-blur: blur(24px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.backdrop-filter{\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.transition{\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-all{\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-colors{\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-opacity{\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-transform{\\n  transition-property: transform;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.duration-200{\\n  transition-duration: 200ms;\\n}\\n.duration-300{\\n  transition-duration: 300ms;\\n}\\n.duration-500{\\n  transition-duration: 500ms;\\n}\\n.ease-in-out{\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.ease-out{\\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n}\\n.ease-in{\\n  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);\\n}\\n\\n/* CSS Custom Properties for Responsive Scaling - Optimized for Content Fit */\\n:root {\\n  --scale-factor: 1;\\n  --content-scale: 3;\\n  --base-font-size: calc(14px * var(--content-scale));\\n  --base-spacing: calc(6px * var(--content-scale));\\n  --base-border-radius: calc(8px * var(--content-scale));\\n  --base-icon-size: calc(20px * var(--content-scale));\\n  --base-button-height: calc(36px * var(--content-scale));\\n  --base-card-padding: calc(1px * var(--content-scale));\\n  --base-gap: calc(6px * var(--content-scale));\\n  --header-height: calc(60px * var(--content-scale));\\n  --footer-height: calc(50px * var(--content-scale));\\n  --sidebar-width: calc(80px * var(--content-scale));\\n\\n  /* Unified Dark Theme Color Palette */\\n  --primary-bg: #0f1419;\\n  --secondary-bg: #1a1f2e;\\n  --tertiary-bg: #252b3d;\\n\\n  /* Glassmorphism Dark Theme */\\n  --glass-bg: rgba(26, 31, 46, 0.7);\\n  --glass-bg-light: rgba(37, 43, 61, 0.6);\\n  --glass-border: rgba(100, 116, 139, 0.2);\\n  --glass-border-glow: rgba(100, 116, 139, 0.4);\\n  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 2px 8px rgba(0, 0, 0, 0.2);\\n\\n  /* Consistent Text Colors */\\n  --text-primary: #f8fafc;\\n  --text-secondary: #cbd5e1;\\n  --text-muted: #94a3b8;\\n  --text-accent: #64748b;\\n\\n  /* Unified Accent Colors */\\n  --accent-blue: #3b82f6;\\n  --accent-cyan: #06b6d4;\\n  --accent-green: #10b981;\\n  --accent-yellow: #f59e0b;\\n  --accent-orange: #f97316;\\n  --accent-red: #ef4444;\\n  --accent-purple: #8b5cf6;\\n  --accent-pink: #ec4899;\\n}\\n\\n/* Responsive scaling variables - now handled by --content-scale */\\n@media (max-width: 1920px) and (min-width: 1600px) {\\n  :root {\\n    --scale-factor: 0.9;\\n    --content-scale: 0.9;\\n  }\\n}\\n\\n@media (max-width: 1599px) and (min-width: 1400px) {\\n  :root {\\n    --scale-factor: 0.8;\\n    --content-scale: 0.8;\\n  }\\n}\\n\\n@media (max-width: 1399px) and (min-width: 1200px) {\\n  :root {\\n    --scale-factor: 0.7;\\n    --content-scale: 0.7;\\n  }\\n}\\n\\n@media (max-width: 1199px) and (min-width: 1000px) {\\n  :root {\\n    --scale-factor: 0.6;\\n    --content-scale: 0.6;\\n  }\\n}\\n\\n@media (max-width: 999px) {\\n  :root {\\n    --scale-factor: 0.5;\\n    --content-scale: 0.5;\\n  }\\n}\\n\\n/* Ensure base styles are applied */\\n* {\\n  box-sizing: border-box;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nhtml, body {\\n  height: 100vh;\\n  width: 100vw;\\n  overflow: hidden;\\n  margin: 0;\\n  padding: 0;\\n  background: var(--primary-bg);\\n  color: var(--text-primary);\\n  font-family: 'Poppins', sans-serif;\\n}\\n\\nbody {\\n  font-family: 'Poppins', sans-serif;\\n  font-size: var(--base-font-size);\\n  line-height: 1.5;\\n  background: var(--primary-bg);\\n  color: var(--text-primary);\\n}\\n\\n#__next {\\n  height: 100vh;\\n  width: 100vw;\\n  overflow: hidden;\\n}\\n\\n.main-background {\\n  background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 50%, var(--tertiary-bg) 100%);\\n  background-attachment: fixed;\\n  position: relative;\\n}\\n\\n.main-background::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(ellipse at 20% 30%, rgba(209, 160, 255, 0.1) 0%, transparent 50%),\\n              radial-gradient(ellipse at 80% 70%, rgba(188, 19, 254, 0.08) 0%, transparent 50%),\\n              radial-gradient(ellipse at 50% 50%, rgba(41, 52, 95, 0.05) 0%, transparent 70%);\\n  pointer-events: none;\\n}\\n\\n/* Global Responsive Classes */\\n.responsive-text {\\n  font-size: var(--base-font-size);\\n}\\n\\n.responsive-text-sm {\\n  font-size: calc(var(--base-font-size) * 0.875);\\n}\\n\\n.responsive-text-xs {\\n  font-size: calc(var(--base-font-size) * 0.75);\\n}\\n\\n.responsive-text-lg {\\n  font-size: calc(var(--base-font-size) * 1.125);\\n}\\n\\n.responsive-text-xl {\\n  font-size: calc(var(--base-font-size) * 1.25);\\n}\\n\\n.responsive-text-2xl {\\n  font-size: calc(var(--base-font-size) * 1.5);\\n}\\n\\n.responsive-spacing {\\n  padding: var(--base-spacing);\\n}\\n\\n.responsive-spacing-sm {\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.responsive-spacing-lg {\\n  padding: calc(var(--base-spacing) * 1.5);\\n}\\n\\n.responsive-gap {\\n  gap: var(--base-gap);\\n}\\n\\n.responsive-gap-sm {\\n  gap: calc(var(--base-gap) * 0.5);\\n}\\n\\n.responsive-gap-lg {\\n  gap: calc(var(--base-gap) * 1.5);\\n}\\n\\n.responsive-border-radius {\\n  border-radius: var(--base-border-radius);\\n}\\n\\n.responsive-icon {\\n  width: var(--base-icon-size);\\n  height: var(--base-icon-size);\\n}\\n\\n.responsive-button {\\n  height: var(--base-button-height);\\n  padding: 0 var(--base-spacing);\\n  font-size: var(--base-font-size);\\n  border-radius: var(--base-border-radius);\\n}\\n\\n.responsive-card {\\n  padding: var(--base-card-padding);\\n  border-radius: calc(var(--base-border-radius) * 1.5);\\n}\\n\\n/* Enhanced HUD Card System */\\n.hud-card {\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  border-radius: calc(var(--base-border-radius) * 1.5);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  box-shadow: var(--glass-shadow);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.hud-card::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(90deg, transparent, var(--accent-blue), transparent);\\n  opacity: 0.6;\\n}\\n\\n.hud-card:hover {\\n  border-color: var(--glass-border-glow);\\n  box-shadow: 0 8px 40px rgba(59, 130, 246, 0.15), var(--glass-shadow);\\n  transform: translateY(-2px);\\n}\\n\\n/* Compact HUD card padding */\\n.hud-card .ant-card-head {\\n  padding: calc(var(--base-spacing) * 0.75) calc(var(--base-spacing) * 1.5) !important;\\n  min-height: auto !important;\\n}\\n\\n.hud-card .ant-card-body {\\n  padding: calc(var(--base-spacing) * 1) !important;\\n}\\n\\n/* Compact metric cards */\\n.metric-card {\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  border-radius: var(--base-border-radius);\\n  padding: calc(var(--base-spacing) * 0.3);\\n  text-align: center;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n  min-height: 60px;\\n  max-height: 80px;\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  box-shadow: var(--glass-shadow);\\n}\\n\\n.metric-card::after {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(0, 207, 255, 0.05) 0%, rgba(0, 255, 189, 0.05) 100%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.metric-card:hover::after {\\n  opacity: 1;\\n}\\n\\n.metric-card:hover {\\n  border-color: var(--accent-blue);\\n  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);\\n  transform: scale(1.02);\\n}\\n\\n/* Compact metric card text */\\n.metric-card p {\\n  margin-bottom: calc(var(--base-spacing) * 0.1) !important;\\n  line-height: 1.1;\\n  font-size: calc(var(--base-font-size) * 0.7) !important;\\n  color: var(--text-secondary) !important;\\n}\\n\\n.metric-card .text-xl {\\n  font-size: calc(var(--base-font-size) * 1.1) !important;\\n  line-height: 1.0;\\n  margin-bottom: calc(var(--base-spacing) * 0.1) !important;\\n  color: var(--text-primary) !important;\\n  font-weight: 700 !important;\\n}\\n\\n/* Compact metric card icons */\\n.metric-card .w-10.h-10 {\\n  width: calc(var(--base-icon-size) * 1.5) !important;\\n  height: calc(var(--base-icon-size) * 1.5) !important;\\n  margin-bottom: calc(var(--base-spacing) * 0.5) !important;\\n}\\n\\n/* Beautiful gradient animation */\\n@keyframes gradientShift {\\n  0% { background-position: 0% 50%; }\\n  50% { background-position: 100% 50%; }\\n  100% { background-position: 0% 50%; }\\n}\\n\\n/* Status badges with glow effects */\\n.status-badge {\\n  padding: calc(var(--base-spacing) * 0.5) calc(var(--base-spacing) * 1);\\n  border-radius: calc(var(--base-border-radius) * 2);\\n  font-size: calc(10px * var(--content-scale));\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  border: 1px solid;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.status-badge.active {\\n  background: rgba(16, 185, 129, 0.2);\\n  color: var(--accent-green);\\n  border-color: var(--accent-green);\\n  box-shadow: 0 0 10px rgba(16, 185, 129, 0.3);\\n}\\n\\n.status-badge.beta {\\n  background: rgba(245, 158, 11, 0.2);\\n  color: var(--accent-yellow);\\n  border-color: var(--accent-yellow);\\n  box-shadow: 0 0 10px rgba(245, 158, 11, 0.3);\\n}\\n\\n.status-badge.live {\\n  background: rgba(59, 130, 246, 0.2);\\n  color: var(--accent-blue);\\n  border-color: var(--accent-blue);\\n  box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);\\n}\\n\\n.status-badge.testing {\\n  background: rgba(139, 92, 246, 0.2);\\n  color: var(--accent-purple);\\n  border-color: var(--accent-purple);\\n  box-shadow: 0 0 10px rgba(139, 92, 246, 0.3);\\n}\\n\\n/* Enhanced Header Styling */\\n.header-hud {\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n  position: relative;\\n}\\n\\n.header-hud::before {\\n  content: '';\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(90deg, transparent, var(--accent-blue), transparent);\\n  opacity: 0.4;\\n}\\n\\n/* Enhanced Department Buttons */\\n.dept-button {\\n  background: var(--glass-bg-light);\\n  border: 1px solid var(--glass-border);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.dept-button:hover {\\n  background: rgba(59, 130, 246, 0.1);\\n  border-color: var(--glass-border-glow);\\n  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);\\n  transform: scale(1.05);\\n}\\n\\n.dept-button.active {\\n  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(6, 182, 212, 0.1) 100%);\\n  border-color: var(--accent-blue);\\n  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);\\n}\\n\\n/* Panel Background Utilities */\\n.bg-panel-bg {\\n  background: var(--glass-bg) !important;\\n}\\n\\n.bg-container-bg {\\n  background: var(--secondary-bg) !important;\\n}\\n\\n.dept-button.active::after {\\n  content: '';\\n  position: absolute;\\n  inset: 0;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\\n  pointer-events: none;\\n}\\n\\n/* Dashboard Auto-Scale - Scales ALL content including text, icons, spacing */\\n.dashboard-auto-scale {\\n  transform-origin: top left;\\n  width: 100vw !important;\\n  height: 100vh !important;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  overflow: hidden;\\n  transition: transform 0.3s ease-out;\\n}\\n\\n/* Scale everything - content, text, icons, spacing */\\n@media (max-width: 1920px) and (min-width: 1600px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.9);\\n    width: calc(100vw / 0.9) !important;\\n    height: calc(100vh / 0.9) !important;\\n  }\\n  :root {\\n    --content-scale: 0.9;\\n  }\\n}\\n\\n@media (max-width: 1599px) and (min-width: 1400px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.8);\\n    width: calc(100vw / 0.8) !important;\\n    height: calc(100vh / 0.8) !important;\\n  }\\n  :root {\\n    --content-scale: 0.8;\\n  }\\n}\\n\\n@media (max-width: 1399px) and (min-width: 1200px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.7);\\n    width: calc(100vw / 0.7) !important;\\n    height: calc(100vh / 0.7) !important;\\n  }\\n  :root {\\n    --content-scale: 0.7;\\n  }\\n}\\n\\n@media (max-width: 1199px) and (min-width: 1000px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.6);\\n    width: calc(100vw / 0.6) !important;\\n    height: calc(100vh / 0.6) !important;\\n  }\\n  :root {\\n    --content-scale: 0.6;\\n  }\\n}\\n\\n@media (max-width: 999px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.5);\\n    width: calc(100vw / 0.5) !important;\\n    height: calc(100vh / 0.5) !important;\\n  }\\n  :root {\\n    --content-scale: 0.5;\\n  }\\n}\\n\\n/* Default content scale */\\n:root {\\n  --content-scale: 1;\\n}\\n\\n/* Universal content scaling - applies to ALL elements */\\n* {\\n  font-size: inherit;\\n}\\n\\n/* Scale all text elements in main content */\\n.main-section h1, .main-section h2, .main-section h3, .main-section h4, .main-section h5, .main-section h6,\\n.main-section p, .main-section span, .main-section div, .main-section button,\\n.main-section input, .main-section textarea, .main-section select, .main-section label, .main-section a {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale card content specifically */\\n.card-content * {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale chart text and numbers */\\n.recharts-text, .recharts-label, .chart-text, .metric-value, .metric-label {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale all icons and images */\\nsvg, img, .icon {\\n  width: calc(var(--base-icon-size));\\n  height: calc(var(--base-icon-size));\\n}\\n\\n/* Scale all spacing */\\n.p-1 { padding: calc(4px * var(--content-scale)) !important; }\\n.p-2 { padding: calc(8px * var(--content-scale)) !important; }\\n.p-3 { padding: calc(12px * var(--content-scale)) !important; }\\n.p-4 { padding: calc(16px * var(--content-scale)) !important; }\\n.p-5 { padding: calc(20px * var(--content-scale)) !important; }\\n.p-6 { padding: calc(24px * var(--content-scale)) !important; }\\n\\n.m-1 { margin: calc(4px * var(--content-scale)) !important; }\\n.m-2 { margin: calc(8px * var(--content-scale)) !important; }\\n.m-3 { margin: calc(12px * var(--content-scale)) !important; }\\n.m-4 { margin: calc(16px * var(--content-scale)) !important; }\\n.m-5 { margin: calc(20px * var(--content-scale)) !important; }\\n.m-6 { margin: calc(24px * var(--content-scale)) !important; }\\n\\n/* Scale gaps */\\n.gap-1 { gap: calc(4px * var(--content-scale)) !important; }\\n.gap-2 { gap: calc(8px * var(--content-scale)) !important; }\\n.gap-3 { gap: calc(12px * var(--content-scale)) !important; }\\n.gap-4 { gap: calc(16px * var(--content-scale)) !important; }\\n.gap-5 { gap: calc(20px * var(--content-scale)) !important; }\\n.gap-6 { gap: calc(24px * var(--content-scale)) !important; }\\n\\n/* Scale border radius */\\n.rounded { border-radius: calc(4px * var(--content-scale)) !important; }\\n.rounded-md { border-radius: calc(6px * var(--content-scale)) !important; }\\n.rounded-lg { border-radius: calc(8px * var(--content-scale)) !important; }\\n.rounded-xl { border-radius: calc(12px * var(--content-scale)) !important; }\\n.rounded-2xl { border-radius: calc(16px * var(--content-scale)) !important; }\\n\\n@keyframes fade-in-up {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n.animate-fade-in-up {\\n  animation: fade-in-up 0.3s ease-out forwards;\\n}\\n\\n@keyframes fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n.animate-fadeIn {\\n  animation: fadeIn 0.5s ease-in-out;\\n}\\n\\n/* Override Tailwind text sizes with content scaling */\\n.text-xs { font-size: calc(12px * var(--content-scale)) !important; }\\n.text-sm { font-size: calc(14px * var(--content-scale)) !important; }\\n.text-base { font-size: calc(16px * var(--content-scale)) !important; }\\n.text-lg { font-size: calc(18px * var(--content-scale)) !important; }\\n.text-xl { font-size: calc(20px * var(--content-scale)) !important; }\\n.text-2xl { font-size: calc(24px * var(--content-scale)) !important; }\\n.text-3xl { font-size: calc(30px * var(--content-scale)) !important; }\\n.text-4xl { font-size: calc(36px * var(--content-scale)) !important; }\\n\\n/* Override Tailwind width/height for icons */\\n.w-3 { width: calc(12px * var(--content-scale)) !important; }\\n.w-4 { width: calc(16px * var(--content-scale)) !important; }\\n.w-5 { width: calc(20px * var(--content-scale)) !important; }\\n.w-6 { width: calc(24px * var(--content-scale)) !important; }\\n.w-8 { width: calc(32px * var(--content-scale)) !important; }\\n.w-10 { width: calc(40px * var(--content-scale)) !important; }\\n.w-12 { width: calc(48px * var(--content-scale)) !important; }\\n\\n.h-3 { height: calc(12px * var(--content-scale)) !important; }\\n.h-4 { height: calc(16px * var(--content-scale)) !important; }\\n.h-5 { height: calc(20px * var(--content-scale)) !important; }\\n.h-6 { height: calc(24px * var(--content-scale)) !important; }\\n.h-8 { height: calc(32px * var(--content-scale)) !important; }\\n.h-10 { height: calc(40px * var(--content-scale)) !important; }\\n.h-12 { height: calc(48px * var(--content-scale)) !important; }\\n\\n/* Optimized Layout Classes for Perfect Content Fit */\\n.main-layout {\\n  height: 100vh;\\n  width: 100vw;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.content-area {\\n  flex: 1;\\n  display: flex;\\n  min-height: 0;\\n  overflow: hidden;\\n}\\n\\n.main-content {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 0;\\n  overflow: hidden;\\n}\\n\\n.header-section {\\n  height: var(--header-height);\\n  flex-shrink: 0;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.main-section {\\n  flex: 1;\\n  min-height: 0;\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.footer-section {\\n  height: calc(var(--footer-height) * 1.5);\\n  flex-shrink: 0;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.sidebar-left {\\n  width: var(--sidebar-width);\\n  flex-shrink: 0;\\n}\\n\\n.sidebar-right {\\n  width: var(--sidebar-width);\\n  flex-shrink: 0;\\n}\\n\\n/* Enhanced Dashboard Grid for No-Scroll Layout */\\n.dashboard-grid {\\n  display: grid;\\n  gap: calc(var(--base-gap) * 0.75);\\n  height: 100%;\\n  overflow: hidden;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.dashboard-grid-teacher {\\n  grid-template-rows: auto 1fr auto;\\n  grid-template-columns: 1fr;\\n}\\n\\n.dashboard-grid-school {\\n  grid-template-rows: auto 1fr;\\n  grid-template-columns: 1fr 1fr;\\n  gap: calc(var(--base-gap) * 1);\\n}\\n\\n/* Compact content areas */\\n.content-compact {\\n  max-height: calc(100vh - var(--header-height) - var(--footer-height) - calc(var(--base-spacing) * 4));\\n  overflow-y: auto;\\n}\\n\\n.content-no-scroll {\\n  height: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n/* Enhanced Animations */\\n@keyframes pulse-glow {\\n  0%, 100% {\\n    box-shadow: 0 0 5px rgba(0, 207, 255, 0.3);\\n  }\\n  50% {\\n    box-shadow: 0 0 20px rgba(0, 207, 255, 0.6), 0 0 30px rgba(0, 207, 255, 0.4);\\n  }\\n}\\n\\n@keyframes slide-in-up {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes fade-in-scale {\\n  from {\\n    opacity: 0;\\n    transform: scale(0.95);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n}\\n\\n.animate-pulse-glow {\\n  animation: pulse-glow 2s ease-in-out infinite;\\n}\\n\\n.animate-slide-in-up {\\n  animation: slide-in-up 0.5s ease-out;\\n}\\n\\n.animate-fade-in-scale {\\n  animation: fade-in-scale 0.3s ease-out;\\n}\\n\\n/* Hover Effects */\\n.hover-lift {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.hover-lift:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(59, 130, 246, 0.2);\\n}\\n\\n/* Progress Bar Animations */\\n.progress-bar {\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.progress-bar::after {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\\n  animation: shimmer 2s infinite;\\n}\\n\\n@keyframes shimmer {\\n  0% {\\n    left: -100%;\\n  }\\n  100% {\\n    left: 100%;\\n  }\\n}\\n\\n/* Optimized Card Grid */\\n.card-grid {\\n  display: grid;\\n  gap: var(--base-gap);\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n.card-grid-2 {\\n  grid-template-columns: 1fr 1fr;\\n}\\n\\n.card-grid-3 {\\n  grid-template-columns: 1fr 1fr 1fr;\\n}\\n\\n.card-grid-4 {\\n  grid-template-columns: 1fr 1fr 1fr 1fr;\\n}\\n\\n.card-grid-auto {\\n  grid-template-columns: repeat(auto-fit, minmax(calc(250px * var(--content-scale)), 1fr));\\n}\\n\\n/* Optimized Card Sizing */\\n.card-compact {\\n  padding: calc(var(--base-card-padding) * 0.75);\\n  min-height: calc(120px * var(--content-scale));\\n}\\n\\n.card-standard {\\n  padding: var(--base-card-padding);\\n  min-height: calc(160px * var(--content-scale));\\n}\\n\\n.card-large {\\n  padding: calc(var(--base-card-padding) * 1.25);\\n  min-height: calc(200px * var(--content-scale));\\n}\\n\\n/* Neumorphic HUD Subnav Styles */\\n.subnav-hud {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  padding: calc(var(--base-spacing) * 1.5) 0;\\n  margin: 0 auto;\\n  max-width: calc(72px * 8 + var(--base-gap) * 7); /* 8 buttons with gaps */\\n}\\n\\n.subnav-button {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  width: calc(72px * var(--content-scale));\\n  height: calc(72px * var(--content-scale));\\n  margin: 0 calc(var(--base-gap) * 0.5);\\n  border-radius: calc(16px * var(--content-scale));\\n  background: var(--glass-bg);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  box-shadow: var(--glass-shadow);\\n  border: 1px solid var(--glass-border);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.subnav-button:hover {\\n  transform: translateY(calc(-2px * var(--content-scale)));\\n  background: var(--glass-bg-light);\\n  border-color: var(--glass-border-glow);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 2px 8px rgba(59, 130, 246, 0.2);\\n}\\n\\n.subnav-button.active {\\n  background: var(--glass-bg-light);\\n  border: 1px solid var(--accent-blue);\\n  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3), var(--glass-shadow);\\n}\\n\\n.subnav-icon {\\n  width: calc(24px * var(--content-scale));\\n  height: calc(24px * var(--content-scale));\\n  margin-bottom: calc(4px * var(--content-scale));\\n  color: var(--text-muted);\\n  transition: color 0.3s ease;\\n}\\n\\n.subnav-button:hover .subnav-icon,\\n.subnav-button.active .subnav-icon {\\n  color: var(--accent-blue);\\n}\\n\\n.subnav-label {\\n  font-size: calc(10px * var(--content-scale));\\n  font-weight: 500;\\n  color: var(--text-muted);\\n  text-align: center;\\n  line-height: 1.2;\\n  transition: color 0.3s ease;\\n}\\n\\n.subnav-button:hover .subnav-label,\\n.subnav-button.active .subnav-label {\\n  color: var(--accent-blue);\\n}\\n\\n/* HUD Glow Effect */\\n.subnav-button::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(circle at center, rgba(59, 130, 246, 0.1) 0%, transparent 70%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n  border-radius: inherit;\\n}\\n\\n.subnav-button:hover::before,\\n.subnav-button.active::before {\\n  opacity: 1;\\n}\\n\\n/* Collapsible Sidebar Styles */\\n.sidebar-toggle {\\n  position: fixed;\\n  top: calc(var(--base-spacing) * 2);\\n  left: calc(var(--base-spacing) * 2);\\n  z-index: 50;\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border-radius: var(--base-border-radius);\\n  padding: calc(var(--base-spacing) * 0.75);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  box-shadow: var(--glass-shadow);\\n}\\n\\n.sidebar-toggle:hover {\\n  background: var(--glass-bg-light);\\n  transform: scale(1.05);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 2px 8px rgba(59, 130, 246, 0.2);\\n}\\n\\n.collapsible-sidebar {\\n  position: fixed;\\n  left: 0;\\n  top: 0;\\n  height: 100vh;\\n  background: var(--glass-bg);\\n  border-right: 1px solid var(--glass-border);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  z-index: 40;\\n  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  box-shadow: var(--glass-shadow);\\n  width: calc(280px * var(--content-scale));\\n}\\n\\n.collapsible-sidebar.collapsed {\\n  transform: translateX(-100%);\\n}\\n\\n.collapsible-sidebar.expanded {\\n  transform: translateX(0);\\n}\\n\\n.sidebar-overlay {\\n  position: fixed;\\n  inset: 0;\\n  background: rgba(0, 0, 0, 0.2);\\n  -webkit-backdrop-filter: blur(2px);\\n          backdrop-filter: blur(2px);\\n  z-index: 30;\\n}\\n\\n/* Function Button Styles */\\n.function-button {\\n  width: 100%;\\n  display: flex;\\n  align-items: center;\\n  gap: calc(var(--base-spacing) * 2);\\n  padding: calc(var(--base-spacing) * 2);\\n  border-radius: calc(var(--base-border-radius) * 1.5);\\n  background: var(--glass-bg-light);\\n  border: 1px solid var(--glass-border);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.function-button:hover {\\n  background: rgba(59, 130, 246, 0.1);\\n  border-color: var(--glass-border-glow);\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 2px 8px rgba(59, 130, 246, 0.2);\\n}\\n\\n.function-button.active {\\n  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(6, 182, 212, 0.1) 100%);\\n  border-color: var(--accent-blue);\\n  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);\\n}\\n\\n.function-button-icon {\\n  font-size: calc(24px * var(--content-scale));\\n  line-height: 1;\\n}\\n\\n.function-button-content {\\n  flex: 1;\\n  text-align: left;\\n}\\n\\n.function-button-title {\\n  font-weight: 600;\\n  font-size: calc(16px * var(--content-scale));\\n  color: var(--text-primary);\\n  margin-bottom: calc(var(--base-spacing) * 0.25);\\n}\\n\\n.function-button-description {\\n  font-size: calc(12px * var(--content-scale));\\n  color: var(--text-muted);\\n}\\n\\n.function-button.active .function-button-title {\\n  color: var(--accent-blue);\\n}\\n\\n.function-button-indicator {\\n  width: calc(8px * var(--content-scale));\\n  height: calc(8px * var(--content-scale));\\n  border-radius: 50%;\\n  background: transparent;\\n  transition: background-color 0.3s ease;\\n}\\n\\n.function-button.active .function-button-indicator {\\n  background: var(--accent-blue);\\n}\\n\\n/* Enhanced HUD Vertical Layout for Left Sidebar */\\n.subnav-hud-vertical {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: stretch;\\n  justify-content: flex-start;\\n  gap: calc(var(--base-spacing) * 0.5);\\n  padding: calc(var(--base-spacing) * 0.75);\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n/* Enhanced subnav button with glassmorphism */\\n.subnav-hud-vertical .subnav-button {\\n  width: 100%;\\n  height: calc(36px * var(--content-scale));\\n  margin: 0;\\n  flex-shrink: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  padding: calc(var(--base-spacing) * 0.75) calc(var(--base-spacing) * 1);\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  border-radius: var(--base-border-radius);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n  box-shadow: var(--glass-shadow);\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover {\\n  background: rgba(59, 130, 246, 0.1);\\n  border-color: var(--glass-border-glow);\\n  transform: translateX(4px);\\n  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);\\n}\\n\\n.subnav-hud-vertical .subnav-button.active {\\n  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(6, 182, 212, 0.1) 100%);\\n  border-color: var(--accent-blue);\\n  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n}\\n\\n/* Enhanced subnav icon */\\n.subnav-hud-vertical .subnav-icon {\\n  width: calc(18px * var(--content-scale));\\n  height: calc(18px * var(--content-scale));\\n  margin-right: calc(var(--base-spacing) * 1.5);\\n  color: var(--text-secondary);\\n  transition: all 0.3s ease;\\n  flex-shrink: 0;\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover .subnav-icon {\\n  color: var(--accent-blue);\\n  filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.6));\\n}\\n\\n.subnav-hud-vertical .subnav-button.active .subnav-icon {\\n  color: var(--text-primary);\\n  filter: drop-shadow(0 0 12px rgba(59, 130, 246, 0.8));\\n}\\n\\n/* Enhanced subnav label - full text display */\\n.subnav-hud-vertical .subnav-label {\\n  color: var(--text-primary);\\n  font-size: calc(12px * var(--content-scale));\\n  font-weight: 500;\\n  line-height: 1.2;\\n  white-space: nowrap;\\n  transition: all 0.3s ease;\\n  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover .subnav-label {\\n  color: var(--text-primary);\\n  text-shadow: 0 0 8px rgba(59, 130, 246, 0.6);\\n}\\n\\n.subnav-hud-vertical .subnav-button.active .subnav-label {\\n  color: var(--text-primary);\\n  font-weight: 600;\\n  text-shadow: 0 0 12px rgba(59, 130, 246, 0.8);\\n}\\n\\n.hover\\\\:scale-105:hover{\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.hover\\\\:border-cyan-400\\\\/50:hover{\\n  border-color: rgb(34 211 238 / 0.5);\\n}\\n\\n.hover\\\\:border-cyan-400\\\\/60:hover{\\n  border-color: rgb(34 211 238 / 0.6);\\n}\\n\\n.hover\\\\:border-gray-500\\\\/50:hover{\\n  border-color: rgb(107 114 128 / 0.5);\\n}\\n\\n.hover\\\\:border-blue-400\\\\/30:hover{\\n  border-color: rgb(96 165 250 / 0.3);\\n}\\n\\n.hover\\\\:bg-cyan-500\\\\/50:hover{\\n  background-color: rgb(6 182 212 / 0.5);\\n}\\n\\n.hover\\\\:bg-gray-700\\\\/30:hover{\\n  background-color: rgb(55 65 81 / 0.3);\\n}\\n\\n.hover\\\\:bg-gray-700\\\\/50:hover{\\n  background-color: rgb(55 65 81 / 0.5);\\n}\\n\\n.hover\\\\:bg-gray-700\\\\/60:hover{\\n  background-color: rgb(55 65 81 / 0.6);\\n}\\n\\n.hover\\\\:bg-gray-800\\\\/40:hover{\\n  background-color: rgb(31 41 55 / 0.4);\\n}\\n\\n.hover\\\\:bg-gray-800\\\\/60:hover{\\n  background-color: rgb(31 41 55 / 0.6);\\n}\\n\\n.hover\\\\:bg-pink-500\\\\/90:hover{\\n  background-color: rgb(236 72 153 / 0.9);\\n}\\n\\n.hover\\\\:bg-white\\\\/30:hover{\\n  background-color: rgb(255 255 255 / 0.3);\\n}\\n\\n.hover\\\\:bg-blue-500\\\\/30:hover{\\n  background-color: rgb(59 130 246 / 0.3);\\n}\\n\\n.hover\\\\:text-white:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:shadow-lg:hover{\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.hover\\\\:shadow-cyan-400\\\\/20:hover{\\n  --tw-shadow-color: rgb(34 211 238 / 0.2);\\n  --tw-shadow: var(--tw-shadow-colored);\\n}\\n\\n.focus\\\\:outline-none:focus{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n\\n.focus-visible\\\\:ring-1:focus-visible{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n\\n.focus-visible\\\\:ring-2:focus-visible{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n\\n.focus-visible\\\\:ring-brand-cyan:focus-visible{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(0 255 255 / var(--tw-ring-opacity, 1));\\n}\\n\\n.focus-visible\\\\:ring-white:focus-visible{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity, 1));\\n}\\n\\n.focus-visible\\\\:ring-offset-2:focus-visible{\\n  --tw-ring-offset-width: 2px;\\n}\\n\\n.focus-visible\\\\:ring-offset-gray-800:focus-visible{\\n  --tw-ring-offset-color: #1f2937;\\n}\\n\\n.disabled\\\\:cursor-not-allowed:disabled{\\n  cursor: not-allowed;\\n}\\n\\n.disabled\\\\:cursor-wait:disabled{\\n  cursor: wait;\\n}\\n\\n.disabled\\\\:opacity-50:disabled{\\n  opacity: 0.5;\\n}\\n\\n.group:hover .group-hover\\\\:scale-105{\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.group:hover .group-hover\\\\:scale-110{\\n  --tw-scale-x: 1.1;\\n  --tw-scale-y: 1.1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.group:hover .group-hover\\\\:text-cyan-300{\\n  --tw-text-opacity: 1;\\n  color: rgb(103 232 249 / var(--tw-text-opacity, 1));\\n}\\n\\n.group:hover .group-hover\\\\:text-white{\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n\\n.group:hover .group-hover\\\\:opacity-20{\\n  opacity: 0.2;\\n}\\n\\n@media (prefers-reduced-motion: no-preference){\\n\\n  @keyframes pulse{\\n\\n    50%{\\n      opacity: .5;\\n    }\\n  }\\n\\n  .motion-safe\\\\:animate-pulse{\\n    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n  }\\n}\\n\\n@media (min-width: 640px){\\n\\n  .sm\\\\:mr-3{\\n    margin-right: 0.75rem;\\n  }\\n\\n  .sm\\\\:mt-4{\\n    margin-top: 1rem;\\n  }\\n\\n  .sm\\\\:h-10{\\n    height: 2.5rem;\\n  }\\n\\n  .sm\\\\:h-12{\\n    height: 3rem;\\n  }\\n\\n  .sm\\\\:h-16{\\n    height: 4rem;\\n  }\\n\\n  .sm\\\\:h-3{\\n    height: 0.75rem;\\n  }\\n\\n  .sm\\\\:h-36{\\n    height: 9rem;\\n  }\\n\\n  .sm\\\\:h-4{\\n    height: 1rem;\\n  }\\n\\n  .sm\\\\:h-40{\\n    height: 10rem;\\n  }\\n\\n  .sm\\\\:h-48{\\n    height: 12rem;\\n  }\\n\\n  .sm\\\\:h-6{\\n    height: 1.5rem;\\n  }\\n\\n  .sm\\\\:h-8{\\n    height: 2rem;\\n  }\\n\\n  .sm\\\\:w-10{\\n    width: 2.5rem;\\n  }\\n\\n  .sm\\\\:w-12{\\n    width: 3rem;\\n  }\\n\\n  .sm\\\\:w-16{\\n    width: 4rem;\\n  }\\n\\n  .sm\\\\:w-3{\\n    width: 0.75rem;\\n  }\\n\\n  .sm\\\\:w-36{\\n    width: 9rem;\\n  }\\n\\n  .sm\\\\:w-4{\\n    width: 1rem;\\n  }\\n\\n  .sm\\\\:w-40{\\n    width: 10rem;\\n  }\\n\\n  .sm\\\\:w-6{\\n    width: 1.5rem;\\n  }\\n\\n  .sm\\\\:w-8{\\n    width: 2rem;\\n  }\\n\\n  .sm\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .sm\\\\:gap-4{\\n    gap: 1rem;\\n  }\\n\\n  .sm\\\\:gap-6{\\n    gap: 1.5rem;\\n  }\\n\\n  .sm\\\\:p-2{\\n    padding: 0.5rem;\\n  }\\n\\n  .sm\\\\:p-3{\\n    padding: 0.75rem;\\n  }\\n\\n  .sm\\\\:p-4{\\n    padding: 1rem;\\n  }\\n\\n  .sm\\\\:p-6{\\n    padding: 1.5rem;\\n  }\\n\\n  .sm\\\\:px-4{\\n    padding-left: 1rem;\\n    padding-right: 1rem;\\n  }\\n\\n  .sm\\\\:py-2{\\n    padding-top: 0.5rem;\\n    padding-bottom: 0.5rem;\\n  }\\n\\n  .sm\\\\:text-base{\\n    font-size: 1rem;\\n    line-height: 1.5rem;\\n  }\\n\\n  .sm\\\\:text-lg{\\n    font-size: 1.125rem;\\n    line-height: 1.75rem;\\n  }\\n\\n  .sm\\\\:text-sm{\\n    font-size: 0.875rem;\\n    line-height: 1.25rem;\\n  }\\n\\n  .sm\\\\:text-xl{\\n    font-size: 1.25rem;\\n    line-height: 1.75rem;\\n  }\\n\\n  .sm\\\\:text-xs{\\n    font-size: 0.75rem;\\n    line-height: 1rem;\\n  }\\n}\\n\\n@media (min-width: 768px){\\n\\n  .md\\\\:col-span-2{\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .md\\\\:grid-cols-2{\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-4{\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n}\\n\\n@media (min-width: 1024px){\\n\\n  .lg\\\\:col-span-12{\\n    grid-column: span 12 / span 12;\\n  }\\n\\n  .lg\\\\:col-span-2{\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .lg\\\\:col-span-3{\\n    grid-column: span 3 / span 3;\\n  }\\n\\n  .lg\\\\:col-span-4{\\n    grid-column: span 4 / span 4;\\n  }\\n\\n  .lg\\\\:col-span-5{\\n    grid-column: span 5 / span 5;\\n  }\\n\\n  .lg\\\\:col-span-7{\\n    grid-column: span 7 / span 7;\\n  }\\n\\n  .lg\\\\:h-40{\\n    height: 10rem;\\n  }\\n\\n  .lg\\\\:h-48{\\n    height: 12rem;\\n  }\\n\\n  .lg\\\\:w-40{\\n    width: 10rem;\\n  }\\n\\n  .lg\\\\:w-48{\\n    width: 12rem;\\n  }\\n\\n  .lg\\\\:grid-cols-12{\\n    grid-template-columns: repeat(12, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-4{\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:text-xl{\\n    font-size: 1.25rem;\\n    line-height: 1.75rem;\\n  }\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA,qGAAqG;;AAErG;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;;CAAc;;AAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,+HAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,+GAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;;AAAd;EAAA,aAAc;AAAA;AACd;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;;EAAA;IAAA;EAAoB;;EAApB;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;;EAApB;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;;EAApB;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;;EAApB;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;;EAApB;IAAA;EAAoB;AAAA;AACpB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,yBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,2EAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yDAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;KAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gDAAmB;EAAnB,6DAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,6EAAmB;EAAnB,iGAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,8FAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,2GAAmB;EAAnB,yGAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kGAAmB;EAAnB;AAAmB;AAAnB;EAAA,yBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,gKAAmB;EAAnB,wJAAmB;EAAnB,iLAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;;AAEnB,6EAA6E;AAC7E;EACE,iBAAiB;EACjB,kBAAkB;EAClB,mDAAmD;EACnD,gDAAgD;EAChD,sDAAsD;EACtD,mDAAmD;EACnD,uDAAuD;EACvD,qDAAqD;EACrD,4CAA4C;EAC5C,kDAAkD;EAClD,kDAAkD;EAClD,kDAAkD;;EAElD,qCAAqC;EACrC,qBAAqB;EACrB,uBAAuB;EACvB,sBAAsB;;EAEtB,6BAA6B;EAC7B,iCAAiC;EACjC,uCAAuC;EACvC,wCAAwC;EACxC,6CAA6C;EAC7C,2EAA2E;;EAE3E,2BAA2B;EAC3B,uBAAuB;EACvB,yBAAyB;EACzB,qBAAqB;EACrB,sBAAsB;;EAEtB,0BAA0B;EAC1B,sBAAsB;EACtB,sBAAsB;EACtB,uBAAuB;EACvB,wBAAwB;EACxB,wBAAwB;EACxB,qBAAqB;EACrB,wBAAwB;EACxB,sBAAsB;AACxB;;AAEA,kEAAkE;AAClE;EACE;IACE,mBAAmB;IACnB,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,mBAAmB;IACnB,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,mBAAmB;IACnB,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,mBAAmB;IACnB,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,mBAAmB;IACnB,oBAAoB;EACtB;AACF;;AAEA,mCAAmC;AACnC;EACE,sBAAsB;EACtB,SAAS;EACT,UAAU;AACZ;;AAEA;EACE,aAAa;EACb,YAAY;EACZ,gBAAgB;EAChB,SAAS;EACT,UAAU;EACV,6BAA6B;EAC7B,0BAA0B;EAC1B,kCAAkC;AACpC;;AAEA;EACE,kCAAkC;EAClC,gCAAgC;EAChC,gBAAgB;EAChB,6BAA6B;EAC7B,0BAA0B;AAC5B;;AAEA;EACE,aAAa;EACb,YAAY;EACZ,gBAAgB;AAClB;;AAEA;EACE,2GAA2G;EAC3G,4BAA4B;EAC5B,kBAAkB;AACpB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT;;6FAE2F;EAC3F,oBAAoB;AACtB;;AAEA,8BAA8B;AAC9B;EACE,gCAAgC;AAClC;;AAEA;EACE,8CAA8C;AAChD;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,8CAA8C;AAChD;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4BAA4B;AAC9B;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;EACE,oBAAoB;AACtB;;AAEA;EACE,gCAAgC;AAClC;;AAEA;EACE,gCAAgC;AAClC;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;EACE,4BAA4B;EAC5B,6BAA6B;AAC/B;;AAEA;EACE,iCAAiC;EACjC,8BAA8B;EAC9B,gCAAgC;EAChC,wCAAwC;AAC1C;;AAEA;EACE,iCAAiC;EACjC,oDAAoD;AACtD;;AAEA,6BAA6B;AAC7B;EACE,2BAA2B;EAC3B,qCAAqC;EACrC,oDAAoD;EACpD,mCAA2B;UAA3B,2BAA2B;EAC3B,+BAA+B;EAC/B,iDAAiD;EACjD,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,WAAW;EACX,gFAAgF;EAChF,YAAY;AACd;;AAEA;EACE,sCAAsC;EACtC,oEAAoE;EACpE,2BAA2B;AAC7B;;AAEA,6BAA6B;AAC7B;EACE,oFAAoF;EACpF,2BAA2B;AAC7B;;AAEA;EACE,iDAAiD;AACnD;;AAEA,yBAAyB;AACzB;EACE,2BAA2B;EAC3B,qCAAqC;EACrC,wCAAwC;EACxC,wCAAwC;EACxC,kBAAkB;EAClB,yBAAyB;EACzB,kBAAkB;EAClB,gBAAgB;EAChB,gBAAgB;EAChB,gBAAgB;EAChB,wBAAmB;EAAnB,mBAAmB;EACnB,mCAA2B;UAA3B,2BAA2B;EAC3B,+BAA+B;AACjC;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT,6FAA6F;EAC7F,UAAU;EACV,6BAA6B;AAC/B;;AAEA;EACE,UAAU;AACZ;;AAEA;EACE,gCAAgC;EAChC,8CAA8C;EAC9C,sBAAsB;AACxB;;AAEA,6BAA6B;AAC7B;EACE,yDAAyD;EACzD,gBAAgB;EAChB,uDAAuD;EACvD,uCAAuC;AACzC;;AAEA;EACE,uDAAuD;EACvD,gBAAgB;EAChB,yDAAyD;EACzD,qCAAqC;EACrC,2BAA2B;AAC7B;;AAEA,8BAA8B;AAC9B;EACE,mDAAmD;EACnD,oDAAoD;EACpD,yDAAyD;AAC3D;;AAEA,iCAAiC;AACjC;EACE,KAAK,2BAA2B,EAAE;EAClC,MAAM,6BAA6B,EAAE;EACrC,OAAO,2BAA2B,EAAE;AACtC;;AAEA,oCAAoC;AACpC;EACE,sEAAsE;EACtE,kDAAkD;EAClD,4CAA4C;EAC5C,gBAAgB;EAChB,yBAAyB;EACzB,qBAAqB;EACrB,iBAAiB;EACjB,mCAA2B;UAA3B,2BAA2B;EAC3B,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,mCAAmC;EACnC,0BAA0B;EAC1B,iCAAiC;EACjC,4CAA4C;AAC9C;;AAEA;EACE,mCAAmC;EACnC,2BAA2B;EAC3B,kCAAkC;EAClC,4CAA4C;AAC9C;;AAEA;EACE,mCAAmC;EACnC,yBAAyB;EACzB,gCAAgC;EAChC,4CAA4C;AAC9C;;AAEA;EACE,mCAAmC;EACnC,2BAA2B;EAC3B,kCAAkC;EAClC,4CAA4C;AAC9C;;AAEA,4BAA4B;AAC5B;EACE,2BAA2B;EAC3B,qCAAqC;EACrC,mCAA2B;UAA3B,2BAA2B;EAC3B,yCAAyC;EACzC,kBAAkB;AACpB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,SAAS;EACT,OAAO;EACP,QAAQ;EACR,WAAW;EACX,gFAAgF;EAChF,YAAY;AACd;;AAEA,gCAAgC;AAChC;EACE,iCAAiC;EACjC,qCAAqC;EACrC,mCAA2B;UAA3B,2BAA2B;EAC3B,iDAAiD;EACjD,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,mCAAmC;EACnC,sCAAsC;EACtC,8CAA8C;EAC9C,sBAAsB;AACxB;;AAEA;EACE,4FAA4F;EAC5F,gCAAgC;EAChC,4CAA4C;AAC9C;;AAEA,+BAA+B;AAC/B;EACE,sCAAsC;AACxC;;AAEA;EACE,0CAA0C;AAC5C;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,QAAQ;EACR,iFAAiF;EACjF,oBAAoB;AACtB;;AAEA,6EAA6E;AAC7E;EACE,0BAA0B;EAC1B,uBAAuB;EACvB,wBAAwB;EACxB,eAAe;EACf,MAAM;EACN,OAAO;EACP,gBAAgB;EAChB,mCAAmC;AACrC;;AAEA,qDAAqD;AACrD;EACE;IACE,qBAAqB;IACrB,mCAAmC;IACnC,oCAAoC;EACtC;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,mCAAmC;IACnC,oCAAoC;EACtC;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,mCAAmC;IACnC,oCAAoC;EACtC;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,mCAAmC;IACnC,oCAAoC;EACtC;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,mCAAmC;IACnC,oCAAoC;EACtC;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA,0BAA0B;AAC1B;EACE,kBAAkB;AACpB;;AAEA,wDAAwD;AACxD;EACE,kBAAkB;AACpB;;AAEA,4CAA4C;AAC5C;;;EAGE,wEAAwE;AAC1E;;AAEA,oCAAoC;AACpC;EACE,wEAAwE;AAC1E;;AAEA,iCAAiC;AACjC;EACE,wEAAwE;AAC1E;;AAEA,+BAA+B;AAC/B;EACE,kCAAkC;EAClC,mCAAmC;AACrC;;AAEA,sBAAsB;AACtB,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,qDAAqD,EAAE;AAC9D,OAAO,qDAAqD,EAAE;AAC9D,OAAO,qDAAqD,EAAE;AAC9D,OAAO,qDAAqD,EAAE;;AAE9D,OAAO,mDAAmD,EAAE;AAC5D,OAAO,mDAAmD,EAAE;AAC5D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;;AAE7D,eAAe;AACf,SAAS,gDAAgD,EAAE;AAC3D,SAAS,gDAAgD,EAAE;AAC3D,SAAS,iDAAiD,EAAE;AAC5D,SAAS,iDAAiD,EAAE;AAC5D,SAAS,iDAAiD,EAAE;AAC5D,SAAS,iDAAiD,EAAE;;AAE5D,wBAAwB;AACxB,WAAW,0DAA0D,EAAE;AACvE,cAAc,0DAA0D,EAAE;AAC1E,cAAc,0DAA0D,EAAE;AAC1E,cAAc,2DAA2D,EAAE;AAC3E,eAAe,2DAA2D,EAAE;;AAE5E;EACE;IACE,UAAU;IACV,2BAA2B;EAC7B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE;IACE,UAAU;EACZ;EACA;IACE,UAAU;EACZ;AACF;;AAEA;EACE,kCAAkC;AACpC;;AAEA,sDAAsD;AACtD,WAAW,uDAAuD,EAAE;AACpE,WAAW,uDAAuD,EAAE;AACpE,aAAa,uDAAuD,EAAE;AACtE,WAAW,uDAAuD,EAAE;AACpE,WAAW,uDAAuD,EAAE;AACpE,YAAY,uDAAuD,EAAE;AACrE,YAAY,uDAAuD,EAAE;AACrE,YAAY,uDAAuD,EAAE;;AAErE,6CAA6C;AAC7C,OAAO,mDAAmD,EAAE;AAC5D,OAAO,mDAAmD,EAAE;AAC5D,OAAO,mDAAmD,EAAE;AAC5D,OAAO,mDAAmD,EAAE;AAC5D,OAAO,mDAAmD,EAAE;AAC5D,QAAQ,mDAAmD,EAAE;AAC7D,QAAQ,mDAAmD,EAAE;;AAE7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,OAAO,oDAAoD,EAAE;AAC7D,QAAQ,oDAAoD,EAAE;AAC9D,QAAQ,oDAAoD,EAAE;;AAE9D,qDAAqD;AACrD;EACE,aAAa;EACb,YAAY;EACZ,aAAa;EACb,sBAAsB;EACtB,gBAAgB;AAClB;;AAEA;EACE,OAAO;EACP,aAAa;EACb,aAAa;EACb,gBAAgB;AAClB;;AAEA;EACE,OAAO;EACP,aAAa;EACb,sBAAsB;EACtB,aAAa;EACb,gBAAgB;AAClB;;AAEA;EACE,4BAA4B;EAC5B,cAAc;EACd,wCAAwC;AAC1C;;AAEA;EACE,OAAO;EACP,aAAa;EACb,gBAAgB;EAChB,kBAAkB;EAClB,wCAAwC;AAC1C;;AAEA;EACE,wCAAwC;EACxC,cAAc;EACd,wCAAwC;AAC1C;;AAEA;EACE,2BAA2B;EAC3B,cAAc;AAChB;;AAEA;EACE,2BAA2B;EAC3B,cAAc;AAChB;;AAEA,iDAAiD;AACjD;EACE,aAAa;EACb,iCAAiC;EACjC,YAAY;EACZ,gBAAgB;EAChB,wCAAwC;AAC1C;;AAEA;EACE,iCAAiC;EACjC,0BAA0B;AAC5B;;AAEA;EACE,4BAA4B;EAC5B,8BAA8B;EAC9B,8BAA8B;AAChC;;AAEA,0BAA0B;AAC1B;EACE,qGAAqG;EACrG,gBAAgB;AAClB;;AAEA;EACE,YAAY;EACZ,gBAAgB;EAChB,aAAa;EACb,sBAAsB;AACxB;;AAEA,wBAAwB;AACxB;EACE;IACE,0CAA0C;EAC5C;EACA;IACE,4EAA4E;EAC9E;AACF;;AAEA;EACE;IACE,UAAU;IACV,2BAA2B;EAC7B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE;IACE,UAAU;IACV,sBAAsB;EACxB;EACA;IACE,UAAU;IACV,mBAAmB;EACrB;AACF;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,oCAAoC;AACtC;;AAEA;EACE,sCAAsC;AACxC;;AAEA,kBAAkB;AAClB;EACE,iDAAiD;AACnD;;AAEA;EACE,2BAA2B;EAC3B,2EAA2E;AAC7E;;AAEA,4BAA4B;AAC5B;EACE,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,WAAW;EACX,WAAW;EACX,YAAY;EACZ,sFAAsF;EACtF,8BAA8B;AAChC;;AAEA;EACE;IACE,WAAW;EACb;EACA;IACE,UAAU;EACZ;AACF;;AAEA,wBAAwB;AACxB;EACE,aAAa;EACb,oBAAoB;EACpB,YAAY;EACZ,gBAAgB;AAClB;;AAEA;EACE,8BAA8B;AAChC;;AAEA;EACE,kCAAkC;AACpC;;AAEA;EACE,sCAAsC;AACxC;;AAEA;EACE,wFAAwF;AAC1F;;AAEA,0BAA0B;AAC1B;EACE,8CAA8C;EAC9C,8CAA8C;AAChD;;AAEA;EACE,iCAAiC;EACjC,8CAA8C;AAChD;;AAEA;EACE,8CAA8C;EAC9C,8CAA8C;AAChD;;AAEA,iCAAiC;AACjC;EACE,aAAa;EACb,uBAAuB;EACvB,mBAAmB;EACnB,0CAA0C;EAC1C,cAAc;EACd,+CAA+C,EAAE,wBAAwB;AAC3E;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,mBAAmB;EACnB,uBAAuB;EACvB,wCAAwC;EACxC,yCAAyC;EACzC,qCAAqC;EACrC,gDAAgD;EAChD,2BAA2B;EAC3B,mCAA2B;UAA3B,2BAA2B;EAC3B,+BAA+B;EAC/B,qCAAqC;EACrC,yBAAyB;EACzB,eAAe;EACf,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,wDAAwD;EACxD,iCAAiC;EACjC,sCAAsC;EACtC,4EAA4E;AAC9E;;AAEA;EACE,iCAAiC;EACjC,oCAAoC;EACpC,iEAAiE;AACnE;;AAEA;EACE,wCAAwC;EACxC,yCAAyC;EACzC,+CAA+C;EAC/C,wBAAwB;EACxB,2BAA2B;AAC7B;;AAEA;;EAEE,yBAAyB;AAC3B;;AAEA;EACE,4CAA4C;EAC5C,gBAAgB;EAChB,wBAAwB;EACxB,kBAAkB;EAClB,gBAAgB;EAChB,2BAA2B;AAC7B;;AAEA;;EAEE,yBAAyB;AAC3B;;AAEA,oBAAoB;AACpB;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT,0FAA0F;EAC1F,UAAU;EACV,6BAA6B;EAC7B,sBAAsB;AACxB;;AAEA;;EAEE,UAAU;AACZ;;AAEA,+BAA+B;AAC/B;EACE,eAAe;EACf,kCAAkC;EAClC,mCAAmC;EACnC,WAAW;EACX,2BAA2B;EAC3B,qCAAqC;EACrC,mCAA2B;UAA3B,2BAA2B;EAC3B,wCAAwC;EACxC,yCAAyC;EACzC,iDAAiD;EACjD,+BAA+B;AACjC;;AAEA;EACE,iCAAiC;EACjC,sBAAsB;EACtB,4EAA4E;AAC9E;;AAEA;EACE,eAAe;EACf,OAAO;EACP,MAAM;EACN,aAAa;EACb,2BAA2B;EAC3B,2CAA2C;EAC3C,mCAA2B;UAA3B,2BAA2B;EAC3B,WAAW;EACX,uDAAuD;EACvD,+BAA+B;EAC/B,yCAAyC;AAC3C;;AAEA;EACE,4BAA4B;AAC9B;;AAEA;EACE,wBAAwB;AAC1B;;AAEA;EACE,eAAe;EACf,QAAQ;EACR,8BAA8B;EAC9B,kCAA0B;UAA1B,0BAA0B;EAC1B,WAAW;AACb;;AAEA,2BAA2B;AAC3B;EACE,WAAW;EACX,aAAa;EACb,mBAAmB;EACnB,kCAAkC;EAClC,sCAAsC;EACtC,oDAAoD;EACpD,iCAAiC;EACjC,qCAAqC;EACrC,iDAAiD;EACjD,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,mCAAmC;EACnC,sCAAsC;EACtC,2BAA2B;EAC3B,4EAA4E;AAC9E;;AAEA;EACE,4FAA4F;EAC5F,gCAAgC;EAChC,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;EAC5C,cAAc;AAChB;;AAEA;EACE,OAAO;EACP,gBAAgB;AAClB;;AAEA;EACE,gBAAgB;EAChB,4CAA4C;EAC5C,0BAA0B;EAC1B,+CAA+C;AACjD;;AAEA;EACE,4CAA4C;EAC5C,wBAAwB;AAC1B;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;EACE,uCAAuC;EACvC,wCAAwC;EACxC,kBAAkB;EAClB,uBAAuB;EACvB,sCAAsC;AACxC;;AAEA;EACE,8BAA8B;AAChC;;AAEA,kDAAkD;AAClD;EACE,aAAa;EACb,sBAAsB;EACtB,oBAAoB;EACpB,2BAA2B;EAC3B,oCAAoC;EACpC,yCAAyC;EACzC,YAAY;EACZ,gBAAgB;AAClB;;AAEA,8CAA8C;AAC9C;EACE,WAAW;EACX,yCAAyC;EACzC,SAAS;EACT,cAAc;EACd,aAAa;EACb,mBAAmB;EACnB,2BAA2B;EAC3B,uEAAuE;EACvE,2BAA2B;EAC3B,qCAAqC;EACrC,wCAAwC;EACxC,mCAA2B;UAA3B,2BAA2B;EAC3B,iDAAiD;EACjD,kBAAkB;EAClB,gBAAgB;EAChB,+BAA+B;AACjC;;AAEA;EACE,mCAAmC;EACnC,sCAAsC;EACtC,0BAA0B;EAC1B,8CAA8C;AAChD;;AAEA;EACE,4FAA4F;EAC5F,gCAAgC;EAChC,oFAAoF;AACtF;;AAEA,yBAAyB;AACzB;EACE,wCAAwC;EACxC,yCAAyC;EACzC,6CAA6C;EAC7C,4BAA4B;EAC5B,yBAAyB;EACzB,cAAc;AAChB;;AAEA;EACE,yBAAyB;EACzB,oDAAoD;AACtD;;AAEA;EACE,0BAA0B;EAC1B,qDAAqD;AACvD;;AAEA,8CAA8C;AAC9C;EACE,0BAA0B;EAC1B,4CAA4C;EAC5C,gBAAgB;EAChB,gBAAgB;EAChB,mBAAmB;EACnB,yBAAyB;EACzB,+CAA+C;AACjD;;AAEA;EACE,0BAA0B;EAC1B,4CAA4C;AAC9C;;AAEA;EACE,0BAA0B;EAC1B,gBAAgB;EAChB,6CAA6C;AAC/C;;AA5kCA;EAAA,kBA6kCA;EA7kCA,kBA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA,oBA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA,+EA6kCA;EA7kCA,mGA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA,wCA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA,8BA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA,2GA6kCA;EA7kCA,yGA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA,2GA6kCA;EA7kCA,yGA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA,oBA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA,oBA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;EAAA,kBA6kCA;EA7kCA,kBA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA,iBA6kCA;EA7kCA,iBA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA,oBA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA,oBA6kCA;EA7kCA;AA6kCA;;AA7kCA;EAAA;AA6kCA;;AA7kCA;;EAAA;;IAAA;MAAA;IA6kCA;EAAA;;EA7kCA;IAAA;EA6kCA;AAAA;;AA7kCA;;EAAA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA,kBA6kCA;IA7kCA;EA6kCA;;EA7kCA;IAAA,mBA6kCA;IA7kCA;EA6kCA;;EA7kCA;IAAA,eA6kCA;IA7kCA;EA6kCA;;EA7kCA;IAAA,mBA6kCA;IA7kCA;EA6kCA;;EA7kCA;IAAA,mBA6kCA;IA7kCA;EA6kCA;;EA7kCA;IAAA,kBA6kCA;IA7kCA;EA6kCA;;EA7kCA;IAAA,kBA6kCA;IA7kCA;EA6kCA;AAAA;;AA7kCA;;EAAA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;AAAA;;AA7kCA;;EAAA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA;EA6kCA;;EA7kCA;IAAA,kBA6kCA;IA7kCA;EA6kCA;AAAA\",\"sourcesContent\":[\"@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');\\n\\n@tailwind base;\\n@tailwind components;\\n@tailwind utilities;\\n\\n/* CSS Custom Properties for Responsive Scaling - Optimized for Content Fit */\\n:root {\\n  --scale-factor: 1;\\n  --content-scale: 3;\\n  --base-font-size: calc(14px * var(--content-scale));\\n  --base-spacing: calc(6px * var(--content-scale));\\n  --base-border-radius: calc(8px * var(--content-scale));\\n  --base-icon-size: calc(20px * var(--content-scale));\\n  --base-button-height: calc(36px * var(--content-scale));\\n  --base-card-padding: calc(1px * var(--content-scale));\\n  --base-gap: calc(6px * var(--content-scale));\\n  --header-height: calc(60px * var(--content-scale));\\n  --footer-height: calc(50px * var(--content-scale));\\n  --sidebar-width: calc(80px * var(--content-scale));\\n\\n  /* Unified Dark Theme Color Palette */\\n  --primary-bg: #0f1419;\\n  --secondary-bg: #1a1f2e;\\n  --tertiary-bg: #252b3d;\\n\\n  /* Glassmorphism Dark Theme */\\n  --glass-bg: rgba(26, 31, 46, 0.7);\\n  --glass-bg-light: rgba(37, 43, 61, 0.6);\\n  --glass-border: rgba(100, 116, 139, 0.2);\\n  --glass-border-glow: rgba(100, 116, 139, 0.4);\\n  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 2px 8px rgba(0, 0, 0, 0.2);\\n\\n  /* Consistent Text Colors */\\n  --text-primary: #f8fafc;\\n  --text-secondary: #cbd5e1;\\n  --text-muted: #94a3b8;\\n  --text-accent: #64748b;\\n\\n  /* Unified Accent Colors */\\n  --accent-blue: #3b82f6;\\n  --accent-cyan: #06b6d4;\\n  --accent-green: #10b981;\\n  --accent-yellow: #f59e0b;\\n  --accent-orange: #f97316;\\n  --accent-red: #ef4444;\\n  --accent-purple: #8b5cf6;\\n  --accent-pink: #ec4899;\\n}\\n\\n/* Responsive scaling variables - now handled by --content-scale */\\n@media (max-width: 1920px) and (min-width: 1600px) {\\n  :root {\\n    --scale-factor: 0.9;\\n    --content-scale: 0.9;\\n  }\\n}\\n\\n@media (max-width: 1599px) and (min-width: 1400px) {\\n  :root {\\n    --scale-factor: 0.8;\\n    --content-scale: 0.8;\\n  }\\n}\\n\\n@media (max-width: 1399px) and (min-width: 1200px) {\\n  :root {\\n    --scale-factor: 0.7;\\n    --content-scale: 0.7;\\n  }\\n}\\n\\n@media (max-width: 1199px) and (min-width: 1000px) {\\n  :root {\\n    --scale-factor: 0.6;\\n    --content-scale: 0.6;\\n  }\\n}\\n\\n@media (max-width: 999px) {\\n  :root {\\n    --scale-factor: 0.5;\\n    --content-scale: 0.5;\\n  }\\n}\\n\\n/* Ensure base styles are applied */\\n* {\\n  box-sizing: border-box;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nhtml, body {\\n  height: 100vh;\\n  width: 100vw;\\n  overflow: hidden;\\n  margin: 0;\\n  padding: 0;\\n  background: var(--primary-bg);\\n  color: var(--text-primary);\\n  font-family: 'Poppins', sans-serif;\\n}\\n\\nbody {\\n  font-family: 'Poppins', sans-serif;\\n  font-size: var(--base-font-size);\\n  line-height: 1.5;\\n  background: var(--primary-bg);\\n  color: var(--text-primary);\\n}\\n\\n#__next {\\n  height: 100vh;\\n  width: 100vw;\\n  overflow: hidden;\\n}\\n\\n.main-background {\\n  background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 50%, var(--tertiary-bg) 100%);\\n  background-attachment: fixed;\\n  position: relative;\\n}\\n\\n.main-background::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(ellipse at 20% 30%, rgba(209, 160, 255, 0.1) 0%, transparent 50%),\\n              radial-gradient(ellipse at 80% 70%, rgba(188, 19, 254, 0.08) 0%, transparent 50%),\\n              radial-gradient(ellipse at 50% 50%, rgba(41, 52, 95, 0.05) 0%, transparent 70%);\\n  pointer-events: none;\\n}\\n\\n/* Global Responsive Classes */\\n.responsive-text {\\n  font-size: var(--base-font-size);\\n}\\n\\n.responsive-text-sm {\\n  font-size: calc(var(--base-font-size) * 0.875);\\n}\\n\\n.responsive-text-xs {\\n  font-size: calc(var(--base-font-size) * 0.75);\\n}\\n\\n.responsive-text-lg {\\n  font-size: calc(var(--base-font-size) * 1.125);\\n}\\n\\n.responsive-text-xl {\\n  font-size: calc(var(--base-font-size) * 1.25);\\n}\\n\\n.responsive-text-2xl {\\n  font-size: calc(var(--base-font-size) * 1.5);\\n}\\n\\n.responsive-spacing {\\n  padding: var(--base-spacing);\\n}\\n\\n.responsive-spacing-sm {\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.responsive-spacing-lg {\\n  padding: calc(var(--base-spacing) * 1.5);\\n}\\n\\n.responsive-gap {\\n  gap: var(--base-gap);\\n}\\n\\n.responsive-gap-sm {\\n  gap: calc(var(--base-gap) * 0.5);\\n}\\n\\n.responsive-gap-lg {\\n  gap: calc(var(--base-gap) * 1.5);\\n}\\n\\n.responsive-border-radius {\\n  border-radius: var(--base-border-radius);\\n}\\n\\n.responsive-icon {\\n  width: var(--base-icon-size);\\n  height: var(--base-icon-size);\\n}\\n\\n.responsive-button {\\n  height: var(--base-button-height);\\n  padding: 0 var(--base-spacing);\\n  font-size: var(--base-font-size);\\n  border-radius: var(--base-border-radius);\\n}\\n\\n.responsive-card {\\n  padding: var(--base-card-padding);\\n  border-radius: calc(var(--base-border-radius) * 1.5);\\n}\\n\\n/* Enhanced HUD Card System */\\n.hud-card {\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  border-radius: calc(var(--base-border-radius) * 1.5);\\n  backdrop-filter: blur(20px);\\n  box-shadow: var(--glass-shadow);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.hud-card::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(90deg, transparent, var(--accent-blue), transparent);\\n  opacity: 0.6;\\n}\\n\\n.hud-card:hover {\\n  border-color: var(--glass-border-glow);\\n  box-shadow: 0 8px 40px rgba(59, 130, 246, 0.15), var(--glass-shadow);\\n  transform: translateY(-2px);\\n}\\n\\n/* Compact HUD card padding */\\n.hud-card .ant-card-head {\\n  padding: calc(var(--base-spacing) * 0.75) calc(var(--base-spacing) * 1.5) !important;\\n  min-height: auto !important;\\n}\\n\\n.hud-card .ant-card-body {\\n  padding: calc(var(--base-spacing) * 1) !important;\\n}\\n\\n/* Compact metric cards */\\n.metric-card {\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  border-radius: var(--base-border-radius);\\n  padding: calc(var(--base-spacing) * 0.3);\\n  text-align: center;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n  min-height: 60px;\\n  max-height: 80px;\\n  height: fit-content;\\n  backdrop-filter: blur(20px);\\n  box-shadow: var(--glass-shadow);\\n}\\n\\n.metric-card::after {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(0, 207, 255, 0.05) 0%, rgba(0, 255, 189, 0.05) 100%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.metric-card:hover::after {\\n  opacity: 1;\\n}\\n\\n.metric-card:hover {\\n  border-color: var(--accent-blue);\\n  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);\\n  transform: scale(1.02);\\n}\\n\\n/* Compact metric card text */\\n.metric-card p {\\n  margin-bottom: calc(var(--base-spacing) * 0.1) !important;\\n  line-height: 1.1;\\n  font-size: calc(var(--base-font-size) * 0.7) !important;\\n  color: var(--text-secondary) !important;\\n}\\n\\n.metric-card .text-xl {\\n  font-size: calc(var(--base-font-size) * 1.1) !important;\\n  line-height: 1.0;\\n  margin-bottom: calc(var(--base-spacing) * 0.1) !important;\\n  color: var(--text-primary) !important;\\n  font-weight: 700 !important;\\n}\\n\\n/* Compact metric card icons */\\n.metric-card .w-10.h-10 {\\n  width: calc(var(--base-icon-size) * 1.5) !important;\\n  height: calc(var(--base-icon-size) * 1.5) !important;\\n  margin-bottom: calc(var(--base-spacing) * 0.5) !important;\\n}\\n\\n/* Beautiful gradient animation */\\n@keyframes gradientShift {\\n  0% { background-position: 0% 50%; }\\n  50% { background-position: 100% 50%; }\\n  100% { background-position: 0% 50%; }\\n}\\n\\n/* Status badges with glow effects */\\n.status-badge {\\n  padding: calc(var(--base-spacing) * 0.5) calc(var(--base-spacing) * 1);\\n  border-radius: calc(var(--base-border-radius) * 2);\\n  font-size: calc(10px * var(--content-scale));\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  border: 1px solid;\\n  backdrop-filter: blur(10px);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.status-badge.active {\\n  background: rgba(16, 185, 129, 0.2);\\n  color: var(--accent-green);\\n  border-color: var(--accent-green);\\n  box-shadow: 0 0 10px rgba(16, 185, 129, 0.3);\\n}\\n\\n.status-badge.beta {\\n  background: rgba(245, 158, 11, 0.2);\\n  color: var(--accent-yellow);\\n  border-color: var(--accent-yellow);\\n  box-shadow: 0 0 10px rgba(245, 158, 11, 0.3);\\n}\\n\\n.status-badge.live {\\n  background: rgba(59, 130, 246, 0.2);\\n  color: var(--accent-blue);\\n  border-color: var(--accent-blue);\\n  box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);\\n}\\n\\n.status-badge.testing {\\n  background: rgba(139, 92, 246, 0.2);\\n  color: var(--accent-purple);\\n  border-color: var(--accent-purple);\\n  box-shadow: 0 0 10px rgba(139, 92, 246, 0.3);\\n}\\n\\n/* Enhanced Header Styling */\\n.header-hud {\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  backdrop-filter: blur(20px);\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n  position: relative;\\n}\\n\\n.header-hud::before {\\n  content: '';\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(90deg, transparent, var(--accent-blue), transparent);\\n  opacity: 0.4;\\n}\\n\\n/* Enhanced Department Buttons */\\n.dept-button {\\n  background: var(--glass-bg-light);\\n  border: 1px solid var(--glass-border);\\n  backdrop-filter: blur(10px);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.dept-button:hover {\\n  background: rgba(59, 130, 246, 0.1);\\n  border-color: var(--glass-border-glow);\\n  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);\\n  transform: scale(1.05);\\n}\\n\\n.dept-button.active {\\n  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(6, 182, 212, 0.1) 100%);\\n  border-color: var(--accent-blue);\\n  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);\\n}\\n\\n/* Panel Background Utilities */\\n.bg-panel-bg {\\n  background: var(--glass-bg) !important;\\n}\\n\\n.bg-container-bg {\\n  background: var(--secondary-bg) !important;\\n}\\n\\n.dept-button.active::after {\\n  content: '';\\n  position: absolute;\\n  inset: 0;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\\n  pointer-events: none;\\n}\\n\\n/* Dashboard Auto-Scale - Scales ALL content including text, icons, spacing */\\n.dashboard-auto-scale {\\n  transform-origin: top left;\\n  width: 100vw !important;\\n  height: 100vh !important;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  overflow: hidden;\\n  transition: transform 0.3s ease-out;\\n}\\n\\n/* Scale everything - content, text, icons, spacing */\\n@media (max-width: 1920px) and (min-width: 1600px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.9);\\n    width: calc(100vw / 0.9) !important;\\n    height: calc(100vh / 0.9) !important;\\n  }\\n  :root {\\n    --content-scale: 0.9;\\n  }\\n}\\n\\n@media (max-width: 1599px) and (min-width: 1400px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.8);\\n    width: calc(100vw / 0.8) !important;\\n    height: calc(100vh / 0.8) !important;\\n  }\\n  :root {\\n    --content-scale: 0.8;\\n  }\\n}\\n\\n@media (max-width: 1399px) and (min-width: 1200px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.7);\\n    width: calc(100vw / 0.7) !important;\\n    height: calc(100vh / 0.7) !important;\\n  }\\n  :root {\\n    --content-scale: 0.7;\\n  }\\n}\\n\\n@media (max-width: 1199px) and (min-width: 1000px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.6);\\n    width: calc(100vw / 0.6) !important;\\n    height: calc(100vh / 0.6) !important;\\n  }\\n  :root {\\n    --content-scale: 0.6;\\n  }\\n}\\n\\n@media (max-width: 999px) {\\n  .dashboard-auto-scale {\\n    transform: scale(0.5);\\n    width: calc(100vw / 0.5) !important;\\n    height: calc(100vh / 0.5) !important;\\n  }\\n  :root {\\n    --content-scale: 0.5;\\n  }\\n}\\n\\n/* Default content scale */\\n:root {\\n  --content-scale: 1;\\n}\\n\\n/* Universal content scaling - applies to ALL elements */\\n* {\\n  font-size: inherit;\\n}\\n\\n/* Scale all text elements in main content */\\n.main-section h1, .main-section h2, .main-section h3, .main-section h4, .main-section h5, .main-section h6,\\n.main-section p, .main-section span, .main-section div, .main-section button,\\n.main-section input, .main-section textarea, .main-section select, .main-section label, .main-section a {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale card content specifically */\\n.card-content * {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale chart text and numbers */\\n.recharts-text, .recharts-label, .chart-text, .metric-value, .metric-label {\\n  font-size: calc(var(--base-font-size) * var(--content-scale)) !important;\\n}\\n\\n/* Scale all icons and images */\\nsvg, img, .icon {\\n  width: calc(var(--base-icon-size));\\n  height: calc(var(--base-icon-size));\\n}\\n\\n/* Scale all spacing */\\n.p-1 { padding: calc(4px * var(--content-scale)) !important; }\\n.p-2 { padding: calc(8px * var(--content-scale)) !important; }\\n.p-3 { padding: calc(12px * var(--content-scale)) !important; }\\n.p-4 { padding: calc(16px * var(--content-scale)) !important; }\\n.p-5 { padding: calc(20px * var(--content-scale)) !important; }\\n.p-6 { padding: calc(24px * var(--content-scale)) !important; }\\n\\n.m-1 { margin: calc(4px * var(--content-scale)) !important; }\\n.m-2 { margin: calc(8px * var(--content-scale)) !important; }\\n.m-3 { margin: calc(12px * var(--content-scale)) !important; }\\n.m-4 { margin: calc(16px * var(--content-scale)) !important; }\\n.m-5 { margin: calc(20px * var(--content-scale)) !important; }\\n.m-6 { margin: calc(24px * var(--content-scale)) !important; }\\n\\n/* Scale gaps */\\n.gap-1 { gap: calc(4px * var(--content-scale)) !important; }\\n.gap-2 { gap: calc(8px * var(--content-scale)) !important; }\\n.gap-3 { gap: calc(12px * var(--content-scale)) !important; }\\n.gap-4 { gap: calc(16px * var(--content-scale)) !important; }\\n.gap-5 { gap: calc(20px * var(--content-scale)) !important; }\\n.gap-6 { gap: calc(24px * var(--content-scale)) !important; }\\n\\n/* Scale border radius */\\n.rounded { border-radius: calc(4px * var(--content-scale)) !important; }\\n.rounded-md { border-radius: calc(6px * var(--content-scale)) !important; }\\n.rounded-lg { border-radius: calc(8px * var(--content-scale)) !important; }\\n.rounded-xl { border-radius: calc(12px * var(--content-scale)) !important; }\\n.rounded-2xl { border-radius: calc(16px * var(--content-scale)) !important; }\\n\\n@keyframes fade-in-up {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n.animate-fade-in-up {\\n  animation: fade-in-up 0.3s ease-out forwards;\\n}\\n\\n@keyframes fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n.animate-fadeIn {\\n  animation: fadeIn 0.5s ease-in-out;\\n}\\n\\n/* Override Tailwind text sizes with content scaling */\\n.text-xs { font-size: calc(12px * var(--content-scale)) !important; }\\n.text-sm { font-size: calc(14px * var(--content-scale)) !important; }\\n.text-base { font-size: calc(16px * var(--content-scale)) !important; }\\n.text-lg { font-size: calc(18px * var(--content-scale)) !important; }\\n.text-xl { font-size: calc(20px * var(--content-scale)) !important; }\\n.text-2xl { font-size: calc(24px * var(--content-scale)) !important; }\\n.text-3xl { font-size: calc(30px * var(--content-scale)) !important; }\\n.text-4xl { font-size: calc(36px * var(--content-scale)) !important; }\\n\\n/* Override Tailwind width/height for icons */\\n.w-3 { width: calc(12px * var(--content-scale)) !important; }\\n.w-4 { width: calc(16px * var(--content-scale)) !important; }\\n.w-5 { width: calc(20px * var(--content-scale)) !important; }\\n.w-6 { width: calc(24px * var(--content-scale)) !important; }\\n.w-8 { width: calc(32px * var(--content-scale)) !important; }\\n.w-10 { width: calc(40px * var(--content-scale)) !important; }\\n.w-12 { width: calc(48px * var(--content-scale)) !important; }\\n\\n.h-3 { height: calc(12px * var(--content-scale)) !important; }\\n.h-4 { height: calc(16px * var(--content-scale)) !important; }\\n.h-5 { height: calc(20px * var(--content-scale)) !important; }\\n.h-6 { height: calc(24px * var(--content-scale)) !important; }\\n.h-8 { height: calc(32px * var(--content-scale)) !important; }\\n.h-10 { height: calc(40px * var(--content-scale)) !important; }\\n.h-12 { height: calc(48px * var(--content-scale)) !important; }\\n\\n/* Optimized Layout Classes for Perfect Content Fit */\\n.main-layout {\\n  height: 100vh;\\n  width: 100vw;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.content-area {\\n  flex: 1;\\n  display: flex;\\n  min-height: 0;\\n  overflow: hidden;\\n}\\n\\n.main-content {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 0;\\n  overflow: hidden;\\n}\\n\\n.header-section {\\n  height: var(--header-height);\\n  flex-shrink: 0;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.main-section {\\n  flex: 1;\\n  min-height: 0;\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.footer-section {\\n  height: calc(var(--footer-height) * 1.5);\\n  flex-shrink: 0;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.sidebar-left {\\n  width: var(--sidebar-width);\\n  flex-shrink: 0;\\n}\\n\\n.sidebar-right {\\n  width: var(--sidebar-width);\\n  flex-shrink: 0;\\n}\\n\\n/* Enhanced Dashboard Grid for No-Scroll Layout */\\n.dashboard-grid {\\n  display: grid;\\n  gap: calc(var(--base-gap) * 0.75);\\n  height: 100%;\\n  overflow: hidden;\\n  padding: calc(var(--base-spacing) * 0.5);\\n}\\n\\n.dashboard-grid-teacher {\\n  grid-template-rows: auto 1fr auto;\\n  grid-template-columns: 1fr;\\n}\\n\\n.dashboard-grid-school {\\n  grid-template-rows: auto 1fr;\\n  grid-template-columns: 1fr 1fr;\\n  gap: calc(var(--base-gap) * 1);\\n}\\n\\n/* Compact content areas */\\n.content-compact {\\n  max-height: calc(100vh - var(--header-height) - var(--footer-height) - calc(var(--base-spacing) * 4));\\n  overflow-y: auto;\\n}\\n\\n.content-no-scroll {\\n  height: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n/* Enhanced Animations */\\n@keyframes pulse-glow {\\n  0%, 100% {\\n    box-shadow: 0 0 5px rgba(0, 207, 255, 0.3);\\n  }\\n  50% {\\n    box-shadow: 0 0 20px rgba(0, 207, 255, 0.6), 0 0 30px rgba(0, 207, 255, 0.4);\\n  }\\n}\\n\\n@keyframes slide-in-up {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes fade-in-scale {\\n  from {\\n    opacity: 0;\\n    transform: scale(0.95);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n}\\n\\n.animate-pulse-glow {\\n  animation: pulse-glow 2s ease-in-out infinite;\\n}\\n\\n.animate-slide-in-up {\\n  animation: slide-in-up 0.5s ease-out;\\n}\\n\\n.animate-fade-in-scale {\\n  animation: fade-in-scale 0.3s ease-out;\\n}\\n\\n/* Hover Effects */\\n.hover-lift {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.hover-lift:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(59, 130, 246, 0.2);\\n}\\n\\n/* Progress Bar Animations */\\n.progress-bar {\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.progress-bar::after {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\\n  animation: shimmer 2s infinite;\\n}\\n\\n@keyframes shimmer {\\n  0% {\\n    left: -100%;\\n  }\\n  100% {\\n    left: 100%;\\n  }\\n}\\n\\n/* Optimized Card Grid */\\n.card-grid {\\n  display: grid;\\n  gap: var(--base-gap);\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n.card-grid-2 {\\n  grid-template-columns: 1fr 1fr;\\n}\\n\\n.card-grid-3 {\\n  grid-template-columns: 1fr 1fr 1fr;\\n}\\n\\n.card-grid-4 {\\n  grid-template-columns: 1fr 1fr 1fr 1fr;\\n}\\n\\n.card-grid-auto {\\n  grid-template-columns: repeat(auto-fit, minmax(calc(250px * var(--content-scale)), 1fr));\\n}\\n\\n/* Optimized Card Sizing */\\n.card-compact {\\n  padding: calc(var(--base-card-padding) * 0.75);\\n  min-height: calc(120px * var(--content-scale));\\n}\\n\\n.card-standard {\\n  padding: var(--base-card-padding);\\n  min-height: calc(160px * var(--content-scale));\\n}\\n\\n.card-large {\\n  padding: calc(var(--base-card-padding) * 1.25);\\n  min-height: calc(200px * var(--content-scale));\\n}\\n\\n/* Neumorphic HUD Subnav Styles */\\n.subnav-hud {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  padding: calc(var(--base-spacing) * 1.5) 0;\\n  margin: 0 auto;\\n  max-width: calc(72px * 8 + var(--base-gap) * 7); /* 8 buttons with gaps */\\n}\\n\\n.subnav-button {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  width: calc(72px * var(--content-scale));\\n  height: calc(72px * var(--content-scale));\\n  margin: 0 calc(var(--base-gap) * 0.5);\\n  border-radius: calc(16px * var(--content-scale));\\n  background: var(--glass-bg);\\n  backdrop-filter: blur(20px);\\n  box-shadow: var(--glass-shadow);\\n  border: 1px solid var(--glass-border);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.subnav-button:hover {\\n  transform: translateY(calc(-2px * var(--content-scale)));\\n  background: var(--glass-bg-light);\\n  border-color: var(--glass-border-glow);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 2px 8px rgba(59, 130, 246, 0.2);\\n}\\n\\n.subnav-button.active {\\n  background: var(--glass-bg-light);\\n  border: 1px solid var(--accent-blue);\\n  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3), var(--glass-shadow);\\n}\\n\\n.subnav-icon {\\n  width: calc(24px * var(--content-scale));\\n  height: calc(24px * var(--content-scale));\\n  margin-bottom: calc(4px * var(--content-scale));\\n  color: var(--text-muted);\\n  transition: color 0.3s ease;\\n}\\n\\n.subnav-button:hover .subnav-icon,\\n.subnav-button.active .subnav-icon {\\n  color: var(--accent-blue);\\n}\\n\\n.subnav-label {\\n  font-size: calc(10px * var(--content-scale));\\n  font-weight: 500;\\n  color: var(--text-muted);\\n  text-align: center;\\n  line-height: 1.2;\\n  transition: color 0.3s ease;\\n}\\n\\n.subnav-button:hover .subnav-label,\\n.subnav-button.active .subnav-label {\\n  color: var(--accent-blue);\\n}\\n\\n/* HUD Glow Effect */\\n.subnav-button::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(circle at center, rgba(59, 130, 246, 0.1) 0%, transparent 70%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n  border-radius: inherit;\\n}\\n\\n.subnav-button:hover::before,\\n.subnav-button.active::before {\\n  opacity: 1;\\n}\\n\\n/* Collapsible Sidebar Styles */\\n.sidebar-toggle {\\n  position: fixed;\\n  top: calc(var(--base-spacing) * 2);\\n  left: calc(var(--base-spacing) * 2);\\n  z-index: 50;\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  backdrop-filter: blur(20px);\\n  border-radius: var(--base-border-radius);\\n  padding: calc(var(--base-spacing) * 0.75);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  box-shadow: var(--glass-shadow);\\n}\\n\\n.sidebar-toggle:hover {\\n  background: var(--glass-bg-light);\\n  transform: scale(1.05);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 2px 8px rgba(59, 130, 246, 0.2);\\n}\\n\\n.collapsible-sidebar {\\n  position: fixed;\\n  left: 0;\\n  top: 0;\\n  height: 100vh;\\n  background: var(--glass-bg);\\n  border-right: 1px solid var(--glass-border);\\n  backdrop-filter: blur(20px);\\n  z-index: 40;\\n  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  box-shadow: var(--glass-shadow);\\n  width: calc(280px * var(--content-scale));\\n}\\n\\n.collapsible-sidebar.collapsed {\\n  transform: translateX(-100%);\\n}\\n\\n.collapsible-sidebar.expanded {\\n  transform: translateX(0);\\n}\\n\\n.sidebar-overlay {\\n  position: fixed;\\n  inset: 0;\\n  background: rgba(0, 0, 0, 0.2);\\n  backdrop-filter: blur(2px);\\n  z-index: 30;\\n}\\n\\n/* Function Button Styles */\\n.function-button {\\n  width: 100%;\\n  display: flex;\\n  align-items: center;\\n  gap: calc(var(--base-spacing) * 2);\\n  padding: calc(var(--base-spacing) * 2);\\n  border-radius: calc(var(--base-border-radius) * 1.5);\\n  background: var(--glass-bg-light);\\n  border: 1px solid var(--glass-border);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.function-button:hover {\\n  background: rgba(59, 130, 246, 0.1);\\n  border-color: var(--glass-border-glow);\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 2px 8px rgba(59, 130, 246, 0.2);\\n}\\n\\n.function-button.active {\\n  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(6, 182, 212, 0.1) 100%);\\n  border-color: var(--accent-blue);\\n  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);\\n}\\n\\n.function-button-icon {\\n  font-size: calc(24px * var(--content-scale));\\n  line-height: 1;\\n}\\n\\n.function-button-content {\\n  flex: 1;\\n  text-align: left;\\n}\\n\\n.function-button-title {\\n  font-weight: 600;\\n  font-size: calc(16px * var(--content-scale));\\n  color: var(--text-primary);\\n  margin-bottom: calc(var(--base-spacing) * 0.25);\\n}\\n\\n.function-button-description {\\n  font-size: calc(12px * var(--content-scale));\\n  color: var(--text-muted);\\n}\\n\\n.function-button.active .function-button-title {\\n  color: var(--accent-blue);\\n}\\n\\n.function-button-indicator {\\n  width: calc(8px * var(--content-scale));\\n  height: calc(8px * var(--content-scale));\\n  border-radius: 50%;\\n  background: transparent;\\n  transition: background-color 0.3s ease;\\n}\\n\\n.function-button.active .function-button-indicator {\\n  background: var(--accent-blue);\\n}\\n\\n/* Enhanced HUD Vertical Layout for Left Sidebar */\\n.subnav-hud-vertical {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: stretch;\\n  justify-content: flex-start;\\n  gap: calc(var(--base-spacing) * 0.5);\\n  padding: calc(var(--base-spacing) * 0.75);\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n/* Enhanced subnav button with glassmorphism */\\n.subnav-hud-vertical .subnav-button {\\n  width: 100%;\\n  height: calc(36px * var(--content-scale));\\n  margin: 0;\\n  flex-shrink: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  padding: calc(var(--base-spacing) * 0.75) calc(var(--base-spacing) * 1);\\n  background: var(--glass-bg);\\n  border: 1px solid var(--glass-border);\\n  border-radius: var(--base-border-radius);\\n  backdrop-filter: blur(20px);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n  box-shadow: var(--glass-shadow);\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover {\\n  background: rgba(59, 130, 246, 0.1);\\n  border-color: var(--glass-border-glow);\\n  transform: translateX(4px);\\n  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);\\n}\\n\\n.subnav-hud-vertical .subnav-button.active {\\n  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(6, 182, 212, 0.1) 100%);\\n  border-color: var(--accent-blue);\\n  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n}\\n\\n/* Enhanced subnav icon */\\n.subnav-hud-vertical .subnav-icon {\\n  width: calc(18px * var(--content-scale));\\n  height: calc(18px * var(--content-scale));\\n  margin-right: calc(var(--base-spacing) * 1.5);\\n  color: var(--text-secondary);\\n  transition: all 0.3s ease;\\n  flex-shrink: 0;\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover .subnav-icon {\\n  color: var(--accent-blue);\\n  filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.6));\\n}\\n\\n.subnav-hud-vertical .subnav-button.active .subnav-icon {\\n  color: var(--text-primary);\\n  filter: drop-shadow(0 0 12px rgba(59, 130, 246, 0.8));\\n}\\n\\n/* Enhanced subnav label - full text display */\\n.subnav-hud-vertical .subnav-label {\\n  color: var(--text-primary);\\n  font-size: calc(12px * var(--content-scale));\\n  font-weight: 500;\\n  line-height: 1.2;\\n  white-space: nowrap;\\n  transition: all 0.3s ease;\\n  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);\\n}\\n\\n.subnav-hud-vertical .subnav-button:hover .subnav-label {\\n  color: var(--text-primary);\\n  text-shadow: 0 0 8px rgba(59, 130, 246, 0.6);\\n}\\n\\n.subnav-hud-vertical .subnav-button.active .subnav-label {\\n  color: var(--text-primary);\\n  font-weight: 600;\\n  text-shadow: 0 0 12px rgba(59, 130, 246, 0.8);\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[1]!./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[14].use[2]!./styles/globals.css\n"));

/***/ })

});
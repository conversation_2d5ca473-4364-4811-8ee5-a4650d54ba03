{"name": "@theia/test", "version": "1.63.0", "description": "Theia - Test Extension", "dependencies": {"@theia/core": "1.63.0", "@theia/editor": "1.63.0", "@theia/filesystem": "1.63.0", "@theia/navigator": "1.63.0", "@theia/terminal": "1.63.0", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0"}, "publishConfig": {"access": "public"}, "theiaExtensions": [{"frontend": "lib/browser/view/test-view-frontend-module"}], "keywords": ["theia-extension"], "license": "EPL-2.0 OR GPL-2.0 WITH Classpath-exception-2.0", "repository": {"type": "git", "url": "https://github.com/eclipse-theia/theia.git"}, "bugs": {"url": "https://github.com/eclipse-theia/theia/issues"}, "homepage": "https://github.com/eclipse-theia/theia", "files": ["lib", "src"], "scripts": {"build": "theiaext build", "clean": "theiaext clean", "compile": "theiaext compile", "lint": "theiaext lint", "test": "theiaext test", "watch": "theiaext watch"}, "devDependencies": {"@theia/ext-scripts": "1.63.0"}, "nyc": {"extends": "../../configs/nyc.json"}, "gitHead": "21358137e41342742707f660b8e222f940a27652"}
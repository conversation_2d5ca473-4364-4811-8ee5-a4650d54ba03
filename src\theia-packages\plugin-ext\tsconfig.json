{"extends": "../../configs/base.tsconfig", "compilerOptions": {"composite": true, "rootDir": "src", "outDir": "lib", "lib": ["es6", "dom", "webworker"]}, "include": ["src"], "references": [{"path": "../ai-mcp"}, {"path": "../bulk-edit"}, {"path": "../callhierarchy"}, {"path": "../console"}, {"path": "../core"}, {"path": "../debug"}, {"path": "../editor"}, {"path": "../editor-preview"}, {"path": "../file-search"}, {"path": "../filesystem"}, {"path": "../markers"}, {"path": "../messages"}, {"path": "../monaco"}, {"path": "../navigator"}, {"path": "../notebook"}, {"path": "../output"}, {"path": "../plugin"}, {"path": "../preferences"}, {"path": "../scm"}, {"path": "../search-in-workspace"}, {"path": "../task"}, {"path": "../terminal"}, {"path": "../test"}, {"path": "../timeline"}, {"path": "../typehierarchy"}, {"path": "../variable-resolver"}, {"path": "../workspace"}]}
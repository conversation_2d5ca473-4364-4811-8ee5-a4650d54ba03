
import type { NextApiRequest, NextApiResponse } from 'next';
import { executeQuery, initializeDatabase, seedDatabase } from '../../backend/lib/database';

export type Book = {
    title: string;
    author: string;
    image: string;
};

type DatabaseBook = {
  id: number;
  title: string;
  author: string;
  price: number;
  cover_image: string;
  genre: string;
  rating: number;
  created_at: string;
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<Book[] | { error: string }>
) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  try {
    // Initialize database if needed
    await initializeDatabase();
    await seedDatabase();

    // Get books from database
    const dbBooks = await executeQuery<DatabaseBook>(`
      SELECT * FROM books
      ORDER BY rating DESC
      LIMIT 6
    `);

    if (dbBooks.length > 0) {
      const books: Book[] = dbBooks.map((book, index) => ({
        title: book.title,
        author: book.author,
        image: book.cover_image || `https://picsum.photos/seed/book${index}/300/400`
      }));
      res.status(200).json(books);
    } else {
      // Fallback data if no books exist
      const fallbackBooks: Book[] = [
        { title: "Neural Networks Fundamentals", author: "Dr. Sarah Chen", image: "https://picsum.photos/seed/book0/300/400" },
        { title: "Quantum Physics Made Simple", author: "Prof. Michael Torres", image: "https://picsum.photos/seed/book1/300/400" },
        { title: "AI Ethics in Practice", author: "Dr. Aisha Patel", image: "https://picsum.photos/seed/book2/300/400" },
        { title: "Future of Computing", author: "James Rodriguez", image: "https://picsum.photos/seed/book3/300/400" }
      ];
      res.status(200).json(fallbackBooks);
    }
  } catch (error) {
    console.error('Books API error:', error);

    // Fallback data if database fails
    const fallbackBooks: Book[] = [
      { title: "Neural Networks Fundamentals", author: "Dr. Sarah Chen", image: "https://picsum.photos/seed/book0/300/400" },
      { title: "Quantum Physics Made Simple", author: "Prof. Michael Torres", image: "https://picsum.photos/seed/book1/300/400" },
      { title: "AI Ethics in Practice", author: "Dr. Aisha Patel", image: "https://picsum.photos/seed/book2/300/400" },
      { title: "Future of Computing", author: "James Rodriguez", image: "https://picsum.photos/seed/book3/300/400" }
    ];
    res.status(200).json(fallbackBooks);
  }
}


import type { NextApiRequest, NextApiResponse } from 'next';
import { generateStructuredData, Type } from '../../backend/lib/gemini';

export type Book = {
    title: string;
    author: string;
    image: string;
};

const booksSchema = {
    type: Type.ARRAY,
    description: "A list of 4 futuristic or sci-fi book titles and authors.",
    items: {
        type: Type.OBJECT,
        properties: {
            title: { type: Type.STRING, description: "A creative, futuristic book title." },
            author: { type: Type.STRING, description: "A plausible-sounding author name." }
        },
        required: ["title", "author"]
    }
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<Book[] | { error: string }>
) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  try {
    const prompt = "Generate a list of exactly 4 creative, futuristic sci-fi book titles and authors.";
    const generatedBooks = await generateStructuredData<{ title: string; author: string }[]>(prompt, booksSchema);
    
    // Add image URLs to the generated data
    const booksWithImages: Book[] = generatedBooks.map((book, index) => ({
        ...book,
        image: `https://picsum.photos/seed/book${index + 1}/300/400`
    }));

    res.status(200).json(booksWithImages);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Failed to fetch book data from AI.' });
  }
}

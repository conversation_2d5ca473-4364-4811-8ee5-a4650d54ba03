import React from 'react';
import { Card } from '../../../components/Card';

interface ParentDashboardProps {
  activeSubSection: string;
}

export const ParentDashboard: React.FC<ParentDashboardProps> = ({ activeSubSection }) => {
  return (
    <div className="p-4 h-full">
      <Card title="Parent Portal">
        <div className="p-6 text-center">
          <h3 className="text-xl font-bold text-white mb-4">Parent Dashboard</h3>
          <p className="text-gray-400">Track your child's progress and communicate with teachers.</p>
        </div>
      </Card>
    </div>
  );
};

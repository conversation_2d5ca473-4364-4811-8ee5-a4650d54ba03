import React, { useState, useEffect } from 'react';
import { Card } from '../../../components/Card';
import {
  HeartIcon,
  ChartBarIcon,
  CalendarIcon,
  AcademicCapIcon,
  UserIcon,
  ChatBubbleLeftRightIcon,
  ShieldCheckIcon,
  StarIcon
} from '@heroicons/react/24/solid';

interface ParentDashboardProps {
  activeSubSection: string;
}

export const ParentDashboard: React.FC<ParentDashboardProps> = ({ activeSubSection }) => {
  const [parentData, setParentData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchParentData = async () => {
      try {
        setLoading(true);
        await new Promise(resolve => setTimeout(resolve, 500));

        const mockData = {
          children: [
            {
              name: '<PERSON> <PERSON>',
              grade: 'Grade 11',
              photo: '👦',
              gpa: 3.94,
              emotionalState: 'Motivated',
              aiMentor: 'Socrates AI',
              learningStyle: 'Visual-Kinesthetic',
              strengths: ['Quantum Physics', 'Neural Networks', 'Creative Problem Solving'],
              concerns: ['Time Management', 'Exam Anxiety'],
              nextParentTeacher: '2024-03-20'
            }
          ],
          aiInsights: [
            {
              type: 'Academic Performance',
              insight: 'Alex shows exceptional aptitude in STEM subjects, particularly quantum physics',
              confidence: 96,
              recommendation: 'Consider advanced placement in quantum computing track',
              priority: 'high'
            },
            {
              type: 'Emotional Wellbeing',
              insight: 'Stress levels elevated before major exams, but overall emotional health is excellent',
              confidence: 89,
              recommendation: 'Implement mindfulness techniques during exam periods',
              priority: 'medium'
            },
            {
              type: 'Social Development',
              insight: 'Strong collaborative skills in group projects, natural leadership qualities',
              confidence: 94,
              recommendation: 'Encourage participation in student government or debate team',
              priority: 'low'
            }
          ],
          recentActivities: [
            { activity: 'Quantum Physics VR Lab', date: '2024-03-15', grade: 'A+', teacher: 'Dr. Einstein AI' },
            { activity: 'Neural Network Project', date: '2024-03-12', grade: 'A', teacher: 'Prof. Turing AI' },
            { activity: 'Bioengineering Ethics Debate', date: '2024-03-10', grade: 'A-', teacher: 'Dr. Darwin AI' }
          ],
          upcomingEvents: [
            { event: 'Parent-Teacher AI Conference', date: '2024-03-20', type: 'Virtual Reality Meeting' },
            { event: 'Holographic Science Fair', date: '2024-03-25', type: 'Student Presentation' },
            { event: 'AI Ethics Symposium', date: '2024-03-30', type: 'Family Workshop' }
          ],
          communicationLog: [
            { from: 'Einstein AI', message: 'Alex demonstrated exceptional understanding of quantum entanglement today', time: '2h ago', type: 'achievement' },
            { from: 'Wellness AI', message: 'Recommended meditation session scheduled for tomorrow', time: '4h ago', type: 'wellness' },
            { from: 'Turing AI', message: 'Neural network project submitted early with innovative approach', time: '1d ago', type: 'achievement' }
          ]
        };

        setParentData(mockData);
      } catch (error) {
        console.error('Failed to fetch parent data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchParentData();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-cyan-400">Loading Parent Portal...</div>
      </div>
    );
  }

  const renderContent = () => {
    switch (activeSubSection) {
      case 'dashboard':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Child Profile */}
            <Card title="👨‍👩‍👧‍👦 My Child's AI Profile" className="lg:col-span-3">
              {parentData.children.map((child: any, index: number) => (
                <div key={index} className="p-4">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-2xl">
                      {child.photo}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-white">{child.name}</h3>
                      <p className="text-gray-400">{child.grade}</p>
                      <div className="flex items-center gap-4 mt-2">
                        <span className="text-sm text-cyan-400">🤖 AI Mentor: {child.aiMentor}</span>
                        <span className="text-sm text-purple-400">🧠 Style: {child.learningStyle}</span>
                        <span className="text-sm text-green-400">😊 {child.emotionalState}</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-3xl font-bold text-white">{child.gpa}</div>
                      <div className="text-sm text-gray-400">Neural GPA</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-semibold text-green-400 mb-2">🌟 Strengths</h4>
                      <div className="space-y-1">
                        {child.strengths.map((strength: string, idx: number) => (
                          <span key={idx} className="inline-block bg-green-500/20 text-green-400 px-2 py-1 rounded-full text-xs mr-2">
                            {strength}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h4 className="text-sm font-semibold text-yellow-400 mb-2">⚠️ Areas for Growth</h4>
                      <div className="space-y-1">
                        {child.concerns.map((concern: string, idx: number) => (
                          <span key={idx} className="inline-block bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded-full text-xs mr-2">
                            {concern}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </Card>

            {/* AI Insights */}
            <Card title="🧠 AI Parenting Insights" className="lg:col-span-2">
              <div className="space-y-3 p-4">
                {parentData.aiInsights.map((insight: any, index: number) => (
                  <div key={index} className={`p-3 rounded-lg border ${
                    insight.priority === 'high' ? 'bg-gradient-to-r from-red-900/20 to-pink-900/20 border-red-500/30' :
                    insight.priority === 'medium' ? 'bg-gradient-to-r from-yellow-900/20 to-orange-900/20 border-yellow-500/30' :
                    'bg-gradient-to-r from-blue-900/20 to-cyan-900/20 border-blue-500/30'
                  }`}>
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-sm font-medium text-white">{insight.type}</h4>
                      <span className="px-2 py-1 rounded-full text-xs bg-purple-500/20 text-purple-400">
                        {insight.confidence}% confidence
                      </span>
                    </div>
                    <p className="text-xs text-gray-300 mb-2">{insight.insight}</p>
                    <p className="text-xs text-cyan-400">💡 {insight.recommendation}</p>
                  </div>
                ))}
              </div>
            </Card>

            {/* AI Communication Log */}
            <Card title="💬 AI Teacher Messages">
              <div className="space-y-3 p-4">
                {parentData.communicationLog.map((message: any, index: number) => (
                  <div key={index} className="p-3 rounded-lg bg-gray-800/40 border border-gray-600/30">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium text-cyan-400">{message.from}</span>
                      <span className="text-xs text-gray-400">{message.time}</span>
                    </div>
                    <p className="text-sm text-gray-300">{message.message}</p>
                    <span className={`inline-block mt-2 px-2 py-1 rounded-full text-xs ${
                      message.type === 'achievement' ? 'bg-green-500/20 text-green-400' :
                      message.type === 'wellness' ? 'bg-blue-500/20 text-blue-400' :
                      'bg-purple-500/20 text-purple-400'
                    }`}>
                      {message.type}
                    </span>
                  </div>
                ))}
              </div>
            </Card>
          </div>
        );

      case 'children':
        return (
          <Card title="👨‍👩‍👧‍👦 My Children">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Children Overview</h3>
              <p className="text-gray-400">Detailed profiles and progress tracking for each child.</p>
            </div>
          </Card>
        );

      default:
        return (
          <Card title="AI-Powered Parent Portal">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Welcome to the Future of Parenting</h3>
              <p className="text-gray-400">AI-powered insights to help you support your child's educational journey.</p>
            </div>
          </Card>
        );
    }
  };

  return (
    <div className="p-4 h-full">
      {renderContent()}
    </div>
  );
};

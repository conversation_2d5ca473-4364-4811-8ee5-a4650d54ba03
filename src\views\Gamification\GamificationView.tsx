import React, { useState } from 'react';

interface GameProps {
  title: string;
  description: string;
  icon: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  category: string;
  onClick: () => void;
}

const GameCard: React.FC<GameProps> = ({ title, description, icon, difficulty, category, onClick }) => {
  const difficultyColors = {
    Easy: 'bg-green-500/20 text-green-400 border-green-400/30',
    Medium: 'bg-yellow-500/20 text-yellow-400 border-yellow-400/30',
    Hard: 'bg-red-500/20 text-red-400 border-red-400/30'
  };

  return (
    <div 
      onClick={onClick}
      className="bg-panel-bg border border-gray-700/50 rounded-xl p-6 hover:bg-gray-800/60 transition-all duration-300 cursor-pointer hover:scale-105 hover:border-blue-400/30"
    >
      <div className="flex items-center gap-4 mb-4">
        <div className="text-4xl">{icon}</div>
        <div className="flex-1">
          <h3 className="text-white font-semibold text-lg">{title}</h3>
          <p className="text-gray-400 text-sm">{description}</p>
        </div>
      </div>
      
      <div className="flex items-center justify-between">
        <span className="text-blue-400 text-sm font-medium">{category}</span>
        <span className={`px-3 py-1 rounded-full text-xs font-medium border ${difficultyColors[difficulty]}`}>
          {difficulty}
        </span>
      </div>
    </div>
  );
};

export const GamificationView: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [activeGame, setActiveGame] = useState<string | null>(null);

  const categories = ['All', 'Math', 'Language', 'Logic', 'Memory', 'Typing'];

  const games = [
    {
      id: 'sudoku',
      title: 'Sudoku Master',
      description: 'Classic number puzzle game with multiple difficulty levels',
      icon: '🔢',
      difficulty: 'Medium' as const,
      category: 'Logic'
    },
    {
      id: 'memory-match',
      title: 'Memory Match',
      description: 'Flip cards and match pairs to improve memory skills',
      icon: '🧠',
      difficulty: 'Easy' as const,
      category: 'Memory'
    },
    {
      id: 'typing-speed',
      title: 'Typing Speed Test',
      description: 'Improve your typing speed and accuracy',
      icon: '⌨️',
      difficulty: 'Easy' as const,
      category: 'Typing'
    },
    {
      id: 'spelling-bee',
      title: 'Spelling Bee',
      description: 'Test and improve your spelling skills',
      icon: '🐝',
      difficulty: 'Medium' as const,
      category: 'Language'
    },
    {
      id: 'math-race',
      title: 'Math Race',
      description: 'Solve math problems as fast as you can',
      icon: '🏃‍♂️',
      difficulty: 'Hard' as const,
      category: 'Math'
    },
    {
      id: 'logic-puzzles',
      title: 'Logic Puzzles',
      description: 'Challenge your logical thinking with brain teasers',
      icon: '🧩',
      difficulty: 'Hard' as const,
      category: 'Logic'
    }
  ];

  const filteredGames = selectedCategory === 'All' 
    ? games 
    : games.filter(game => game.category === selectedCategory);

  const handleGameClick = (gameId: string) => {
    setActiveGame(gameId);
    // Here we would load the actual game component
    console.log(`Loading game: ${gameId}`);
  };

  if (activeGame) {
    return (
      <div className="h-full bg-panel-bg rounded-xl p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-white text-2xl font-bold">
            {games.find(g => g.id === activeGame)?.title}
          </h2>
          <button
            onClick={() => setActiveGame(null)}
            className="bg-gray-800/60 hover:bg-gray-700/60 text-white px-4 py-2 rounded-lg transition-colors duration-200"
          >
            ← Back to Games
          </button>
        </div>
        
        <div className="h-full flex items-center justify-center">
          <div className="text-center">
            <div className="text-6xl mb-4">
              {games.find(g => g.id === activeGame)?.icon}
            </div>
            <h3 className="text-white text-xl mb-2">Game Loading...</h3>
            <p className="text-gray-400">
              {activeGame} implementation will be added here using Phaser.js
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-panel-bg rounded-xl p-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-white text-3xl font-bold mb-2">🎮 Learning Games</h1>
        <p className="text-gray-400">Choose a game to start learning and having fun!</p>
      </div>

      {/* Category Filter */}
      <div className="flex gap-2 mb-6 overflow-x-auto">
        {categories.map((category) => (
          <button
            key={category}
            onClick={() => setSelectedCategory(category)}
            className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 whitespace-nowrap ${
              selectedCategory === category
                ? 'bg-blue-500/20 text-blue-400 border border-blue-400/30'
                : 'bg-gray-800/40 text-gray-300 hover:bg-gray-700/60 hover:text-white'
            }`}
          >
            {category}
          </button>
        ))}
      </div>

      {/* Games Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredGames.map((game) => (
          <GameCard
            key={game.id}
            title={game.title}
            description={game.description}
            icon={game.icon}
            difficulty={game.difficulty}
            category={game.category}
            onClick={() => handleGameClick(game.id)}
          />
        ))}
      </div>

      {/* Stats Section */}
      <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-gray-800/40 rounded-lg p-4">
          <div className="text-2xl font-bold text-green-400">12</div>
          <div className="text-gray-400 text-sm">Games Completed</div>
        </div>
        <div className="bg-gray-800/40 rounded-lg p-4">
          <div className="text-2xl font-bold text-blue-400">1,247</div>
          <div className="text-gray-400 text-sm">Total Score</div>
        </div>
        <div className="bg-gray-800/40 rounded-lg p-4">
          <div className="text-2xl font-bold text-purple-400">Level 8</div>
          <div className="text-gray-400 text-sm">Current Level</div>
        </div>
      </div>
    </div>
  );
};

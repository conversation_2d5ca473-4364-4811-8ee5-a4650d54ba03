import React, { useState } from 'react';
import { PerseusRenderer } from '../../components/PerseusRenderer';

// Educational game categories with real Perseus widgets
const gameCategories = {
  math: {
    name: 'Mathematics',
    icon: '🔢',
    games: [
      { id: 'numeric-input', name: 'Number Practice', description: 'Practice number input and calculations' },
      { id: 'expression', name: 'Math Expressions', description: 'Work with mathematical expressions' },
      { id: 'grapher', name: 'Graph Explorer', description: 'Interactive graphing exercises' },
      { id: 'matrix', name: 'Matrix Operations', description: 'Learn matrix mathematics' }
    ]
  },
  science: {
    name: 'Science',
    icon: '🔬',
    games: [
      { id: 'molecule', name: 'Molecule Builder', description: 'Build and explore molecular structures' },
      { id: 'phet-simulation', name: 'Physics Simulations', description: 'Interactive physics experiments' },
      { id: 'interactive-graph', name: 'Data Analysis', description: 'Analyze scientific data with graphs' }
    ]
  },
  language: {
    name: 'Language Arts',
    icon: '📚',
    games: [
      { id: 'passage', name: 'Reading Comprehension', description: 'Practice reading and understanding texts' },
      { id: 'categorizer', name: 'Word Categorizer', description: 'Categorize words and concepts' },
      { id: 'matcher', name: 'Word Matching', description: 'Match words with their meanings' }
    ]
  },
  logic: {
    name: 'Logic & Reasoning',
    icon: '🧩',
    games: [
      { id: 'orderer', name: 'Sequence Ordering', description: 'Put items in logical order' },
      { id: 'sorter', name: 'Logic Sorter', description: 'Sort items by logical rules' },
      { id: 'radio', name: 'Multiple Choice Logic', description: 'Logical reasoning questions' }
    ]
  },
  programming: {
    name: 'Programming',
    icon: '💻',
    games: [
      { id: 'cs-program', name: 'Computer Science', description: 'Learn programming concepts' },
      { id: 'python-program', name: 'Python Programming', description: 'Interactive Python coding' }
    ]
  }
};



export const GamificationView: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('math');
  const [activeGame, setActiveGame] = useState<string | null>(null);

  const handleGameClick = (gameId: string) => {
    setActiveGame(gameId);
  };

  if (activeGame) {
    // Find the game details
    let gameDetails = null;
    let categoryName = '';

    for (const [catKey, category] of Object.entries(gameCategories)) {
      const game = category.games.find(g => g.id === activeGame);
      if (game) {
        gameDetails = game;
        categoryName = category.name;
        break;
      }
    }

    return (
      <div className="h-full bg-panel-bg rounded-xl p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-white text-2xl font-bold">{gameDetails?.name}</h2>
            <p className="text-gray-400">{categoryName}</p>
          </div>
          <button
            onClick={() => setActiveGame(null)}
            className="bg-gray-800/60 hover:bg-gray-700/60 text-white px-4 py-2 rounded-lg transition-colors duration-200"
          >
            ← Back to Games
          </button>
        </div>

        <div className="h-full bg-gray-900/50 rounded-lg overflow-hidden">
          <PerseusRenderer widgetType={activeGame} />
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-panel-bg rounded-xl p-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-white text-3xl font-bold mb-2">🎮 Educational Games</h1>
        <p className="text-gray-400">Interactive learning games powered by Khan Academy Perseus</p>
      </div>

      {/* Category Tabs */}
      <div className="flex gap-2 mb-6 overflow-x-auto">
        {Object.entries(gameCategories).map(([key, category]) => (
          <button
            key={key}
            onClick={() => setSelectedCategory(key)}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 whitespace-nowrap ${
              selectedCategory === key
                ? 'bg-blue-500/20 text-blue-400 border border-blue-400/30'
                : 'bg-gray-800/40 text-gray-300 hover:bg-gray-700/60 hover:text-white'
            }`}
          >
            <span>{category.icon}</span>
            <span>{category.name}</span>
          </button>
        ))}
      </div>

      {/* Games Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
        {gameCategories[selectedCategory as keyof typeof gameCategories]?.games.map((game) => (
          <div
            key={game.id}
            onClick={() => handleGameClick(game.id)}
            className="bg-gray-800/40 border border-gray-700/50 rounded-xl p-4 hover:bg-gray-700/60 transition-all duration-300 cursor-pointer hover:scale-105 hover:border-blue-400/30"
          >
            <div className="flex items-center gap-3 mb-3">
              <div className="text-2xl">🎯</div>
              <div className="flex-1">
                <h3 className="text-white font-semibold">{game.name}</h3>
                <p className="text-gray-400 text-sm">{game.description}</p>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-blue-400 text-xs font-medium bg-blue-500/20 px-2 py-1 rounded">
                {game.id}
              </span>
              <span className="text-gray-500 text-xs">Perseus Widget</span>
            </div>
          </div>
        ))}
      </div>


    </div>
  );
};

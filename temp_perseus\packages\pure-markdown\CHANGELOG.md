# @khanacademy/pure-markdown

## 2.1.0

### Minor Changes

-   [#2694](https://github.com/Khan/perseus/pull/2694) [`0431a161f`](https://github.com/Khan/perseus/commit/0431a161f2652fffd785e71cd8dac395119c5ff0) Thanks [@jeremywiebe](https://github.com/jeremywiebe)! - Introduce `traverseContent` function to traverse arbitrary Markdown AST's produced by `parse()` function.

## 2.0.10

### Patch Changes

-   [#2672](https://github.com/Khan/perseus/pull/2672) [`c44219a98`](https://github.com/Khan/perseus/commit/c44219a98fdbb8128c614419439c83b6cf33f79e) Thanks [@benchristel](https://github.com/benchristel)! - Update peer dependency versions

-   Updated dependencies [[`c44219a98`](https://github.com/Khan/perseus/commit/c44219a98fdbb8128c614419439c83b6cf33f79e)]:
    -   @khanacademy/simple-markdown@2.0.10

## 2.0.9

### Patch Changes

-   Updated dependencies []:
    -   @khanacademy/perseus-utils@2.0.5
    -   @khanacademy/simple-markdown@2.0.9

## 2.0.8

### Patch Changes

-   [#2624](https://github.com/Khan/perseus/pull/2624) [`7de17a3b1`](https://github.com/Khan/perseus/commit/7de17a3b18698700b4ff2c809e5d39c8625795d6) Thanks [@anakaren-rojas](https://github.com/anakaren-rojas)! - Sync dev dependencies with frontend

-   Updated dependencies [[`7de17a3b1`](https://github.com/Khan/perseus/commit/7de17a3b18698700b4ff2c809e5d39c8625795d6)]:
    -   @khanacademy/perseus-utils@2.0.5
    -   @khanacademy/simple-markdown@2.0.8

## 2.0.7

### Patch Changes

-   Updated dependencies []:
    -   @khanacademy/perseus-utils@2.0.4
    -   @khanacademy/simple-markdown@2.0.7

## 2.0.6

### Patch Changes

-   Updated dependencies []:
    -   @khanacademy/perseus-utils@2.0.4
    -   @khanacademy/simple-markdown@2.0.6

## 2.0.5

### Patch Changes

-   [#2519](https://github.com/Khan/perseus/pull/2519) [`cf71982e0`](https://github.com/Khan/perseus/commit/cf71982e0fe9f831456d760fc4e98b1e93748c4f) Thanks [@dependabot](https://github.com/apps/dependabot)! - Updating wonderblock dependencies.

-   Updated dependencies [[`cf71982e0`](https://github.com/Khan/perseus/commit/cf71982e0fe9f831456d760fc4e98b1e93748c4f)]:
    -   @khanacademy/perseus-utils@2.0.4
    -   @khanacademy/simple-markdown@2.0.5

## 2.0.4

### Patch Changes

-   Updated dependencies [[`3389e0611`](https://github.com/Khan/perseus/commit/3389e06111b2c801fba6c34ed4e5bdc747ef1ed1)]:
    -   @khanacademy/perseus-utils@2.0.3
    -   @khanacademy/simple-markdown@2.0.4

## 2.0.3

### Patch Changes

-   [#2414](https://github.com/Khan/perseus/pull/2414) [`e7807485e`](https://github.com/Khan/perseus/commit/e7807485e0d33621efa4468933e6c77ce9a53def) Thanks [@jeremywiebe](https://github.com/jeremywiebe)! - Fix dependencies so that the package correctly depends on all of the packages it uses

-   Updated dependencies [[`e7807485e`](https://github.com/Khan/perseus/commit/e7807485e0d33621efa4468933e6c77ce9a53def), [`3f32593c9`](https://github.com/Khan/perseus/commit/3f32593c9dd46140b4d8891d50e34f97e751783f)]:
    -   @khanacademy/perseus-utils@2.0.2
    -   @khanacademy/simple-markdown@2.0.3

## 2.0.2

### Patch Changes

-   Updated dependencies []:
    -   @khanacademy/perseus-utils@2.0.1
    -   @khanacademy/simple-markdown@2.0.2

## 2.0.1

### Patch Changes

-   [#2334](https://github.com/Khan/perseus/pull/2334) [`6c5a0121d`](https://github.com/Khan/perseus/commit/6c5a0121dae2f0452baccc30401888379b5def95) Thanks [@handeyeco](https://github.com/handeyeco)! - Add Eslint rule "@typescript-eslint/no-restricted-imports" to help prevent circular dependencies

-   Updated dependencies [[`6c5a0121d`](https://github.com/Khan/perseus/commit/6c5a0121dae2f0452baccc30401888379b5def95)]:
    -   @khanacademy/perseus-utils@2.0.1
    -   @khanacademy/simple-markdown@2.0.1

## 2.0.0

### Major Changes

-   [#2339](https://github.com/Khan/perseus/pull/2339) [`ef5fea555`](https://github.com/Khan/perseus/commit/ef5fea5551230f49af5b31705e84b23493f76883) Thanks [@jeremywiebe](https://github.com/jeremywiebe)! - Revert move to ESM-only packages (package again ships with CJS and ESM builds)

### Patch Changes

-   Updated dependencies [[`ef5fea555`](https://github.com/Khan/perseus/commit/ef5fea5551230f49af5b31705e84b23493f76883)]:
    -   @khanacademy/perseus-utils@2.0.0
    -   @khanacademy/simple-markdown@2.0.0

## 1.0.0

### Major Changes

-   [#2331](https://github.com/Khan/perseus/pull/2331) [`c2e33e522`](https://github.com/Khan/perseus/commit/c2e33e52291d6379799754e27c5d33b02ff4b1fa) Thanks [@jeremywiebe](https://github.com/jeremywiebe)! - Remove CJS output from package (package is now ESM only)

### Patch Changes

-   Updated dependencies [[`c2e33e522`](https://github.com/Khan/perseus/commit/c2e33e52291d6379799754e27c5d33b02ff4b1fa)]:
    -   @khanacademy/perseus-utils@1.0.0
    -   @khanacademy/simple-markdown@1.0.0

## 0.4.1

### Patch Changes

-   [#2322](https://github.com/Khan/perseus/pull/2322) [`4bd882b43`](https://github.com/Khan/perseus/commit/4bd882b43b15d9d3d5ca850f5148eba57c7dca59) Thanks [@jeremywiebe](https://github.com/jeremywiebe)! - Change how version injection code is shared/bundled

-   Updated dependencies [[`4bd882b43`](https://github.com/Khan/perseus/commit/4bd882b43b15d9d3d5ca850f5148eba57c7dca59)]:
    -   @khanacademy/perseus-utils@0.0.2
    -   @khanacademy/simple-markdown@0.14.1

## 0.4.0

### Minor Changes

-   [#2202](https://github.com/Khan/perseus/pull/2202) [`c7f6f63c8`](https://github.com/Khan/perseus/commit/c7f6f63c845566d99dae6df604426e5fb14a7e85) Thanks [@jeremywiebe](https://github.com/jeremywiebe)! - Tooling:

    -   Switching to `pnpm`.

### Patch Changes

-   [#2259](https://github.com/Khan/perseus/pull/2259) [`a90cf7901`](https://github.com/Khan/perseus/commit/a90cf790159fefbee41437f20ac9a403a06f148f) Thanks [@jeremywiebe](https://github.com/jeremywiebe)! - Minor change to how each package embeds it's package version in itself (slightly larger bundle size)

-   Updated dependencies [[`c7f6f63c8`](https://github.com/Khan/perseus/commit/c7f6f63c845566d99dae6df604426e5fb14a7e85), [`a90cf7901`](https://github.com/Khan/perseus/commit/a90cf790159fefbee41437f20ac9a403a06f148f)]:
    -   @khanacademy/simple-markdown@0.14.0

## 0.3.27

### Patch Changes

-   Updated dependencies [[`e63f83d0d`](https://github.com/Khan/perseus/commit/e63f83d0d89fd5b8e7aee3ab7248bcb19ec9be8a), [`e187c6b67`](https://github.com/Khan/perseus/commit/e187c6b67cb4d83e42907527acfe6562346e92d8), [`62ed407b8`](https://github.com/Khan/perseus/commit/62ed407b8647472f955467b8ce64261182bb8b59), [`cbd5a6528`](https://github.com/Khan/perseus/commit/cbd5a652818554aa368bcddb0381d4716bc7a8ba)]:
    -   @khanacademy/perseus-core@3.7.0
    -   @khanacademy/simple-markdown@0.13.20

## 0.3.26

### Patch Changes

-   Updated dependencies [[`f8a4becb0`](https://github.com/Khan/perseus/commit/f8a4becb03c543b034cc47d91d3335078bce76c0), [`ae29e2b2f`](https://github.com/Khan/perseus/commit/ae29e2b2fd3b4ec9533b3a1845d2ca94d05d4ed7), [`ab2041897`](https://github.com/Khan/perseus/commit/ab2041897dff393e2e86d4d4a6e5ad278eff17bd), [`1ade12c18`](https://github.com/Khan/perseus/commit/1ade12c184ba9ef657a7c7d53b81da70fe85de31), [`ce320b496`](https://github.com/Khan/perseus/commit/ce320b496bdc9580c194f878674773b845bb27b3)]:
    -   @khanacademy/perseus-core@3.6.0
    -   @khanacademy/simple-markdown@0.13.19

## 0.3.25

### Patch Changes

-   Updated dependencies [[`fd606f43d`](https://github.com/Khan/perseus/commit/fd606f43d7687a15d6dc2cabd0e85fc71b5ed878), [`3ba74d173`](https://github.com/Khan/perseus/commit/3ba74d1731ceff13c9794a3aeaf79f1735b5fb86), [`097176a26`](https://github.com/Khan/perseus/commit/097176a26db7b0c80b3be5e6fe469539f65de0ea), [`b3c562ac2`](https://github.com/Khan/perseus/commit/b3c562ac2cc6d02c433bf0587379c09a49080795), [`649e6b16a`](https://github.com/Khan/perseus/commit/649e6b16ab67fad694cde5473bcfb3abb719a57d), [`07779783a`](https://github.com/Khan/perseus/commit/07779783ae0e3a19c0c72e95a8eaa0b58a9cc968), [`cac39013b`](https://github.com/Khan/perseus/commit/cac39013bd59a5ef73f151e1170dec83b463f076), [`163dd67d2`](https://github.com/Khan/perseus/commit/163dd67d2a8e119bc18191816668352e43292da2)]:
    -   @khanacademy/perseus-core@3.5.0
    -   @khanacademy/simple-markdown@0.13.18

## 0.3.24

### Patch Changes

-   Updated dependencies [[`a21fd908d`](https://github.com/Khan/perseus/commit/a21fd908d705c5b9de56f29af54d726824f5668e), [`af8f5d3ca`](https://github.com/Khan/perseus/commit/af8f5d3cac1f642bb5f0c96a2f536990c277224f), [`a470c799e`](https://github.com/Khan/perseus/commit/a470c799eb53c87e08fb2f829b27e114ca80f63f), [`97e07c8ba`](https://github.com/Khan/perseus/commit/97e07c8baee12a37e471e8292dedbcf0588e2f50), [`dbd496769`](https://github.com/Khan/perseus/commit/dbd496769e210fc4aca33778a567a99ff1654e7e), [`3c4c6bc92`](https://github.com/Khan/perseus/commit/3c4c6bc9207f6f2d65312df1c2bd5bf5246182a2), [`c8e383b46`](https://github.com/Khan/perseus/commit/c8e383b469426182a1392ca6ad2cde21b61e2f40), [`564447af2`](https://github.com/Khan/perseus/commit/564447af2c030143c303c7ec88b055bab324fff1)]:
    -   @khanacademy/perseus-core@3.4.0
    -   @khanacademy/simple-markdown@0.13.17

## 0.3.23

### Patch Changes

-   Updated dependencies [[`8f8955718`](https://github.com/Khan/perseus/commit/8f89557185f7bed910251520863ed1c8ed3a4410), [`d7bcb14c3`](https://github.com/Khan/perseus/commit/d7bcb14c398059be0be20bea118f9fee1dfc93f6), [`685774f2e`](https://github.com/Khan/perseus/commit/685774f2eae44e4cd5e0d6341a209012cf7e9bcb), [`8a489600e`](https://github.com/Khan/perseus/commit/8a489600e3b0b474da36cc492671879d1372ea46), [`1a75ca628`](https://github.com/Khan/perseus/commit/1a75ca628405dbd9cbe8ee21d7a9039a78327c47), [`459c25074`](https://github.com/Khan/perseus/commit/459c2507472f104f521b5410feaa64402d473a43), [`0df0b1940`](https://github.com/Khan/perseus/commit/0df0b194012627a98708cfcafd1ad5eb76ad91e2), [`dc8118aa1`](https://github.com/Khan/perseus/commit/dc8118aa1e28e77d78a57bc13e50d1954e3f8f69), [`82fa90299`](https://github.com/Khan/perseus/commit/82fa902999d9d79a050fe9acf0031ba886b387fa), [`b4b3a3dbb`](https://github.com/Khan/perseus/commit/b4b3a3dbb5097b1225e9e5acdda254f2f1e66122), [`117e78d03`](https://github.com/Khan/perseus/commit/117e78d03f29304274c1d7cc206743439f94d6ef), [`7a984eba6`](https://github.com/Khan/perseus/commit/7a984eba6f1cec3df314ec245d2176f5db190548), [`f8c9d3574`](https://github.com/Khan/perseus/commit/f8c9d35743d2e8ccf12875ef91498543e2015576), [`1355d6cfc`](https://github.com/Khan/perseus/commit/1355d6cfcbd4cb44de3f05084744dbdefd19def8), [`75f43a8f4`](https://github.com/Khan/perseus/commit/75f43a8f41739df4831e589e0a2724e1c7169312), [`32cc4a45b`](https://github.com/Khan/perseus/commit/32cc4a45bd2df34a0620729ca659a8aec6bcd62a), [`ebf3695b6`](https://github.com/Khan/perseus/commit/ebf3695b69c7526279ef1c999f13b4e24be885be)]:
    -   @khanacademy/perseus-core@3.3.0
    -   @khanacademy/simple-markdown@0.13.16

## 0.3.22

### Patch Changes

-   Updated dependencies [[`9cabe689a`](https://github.com/Khan/perseus/commit/9cabe689a7aa143f95adf4556bf5c10d654a66ae)]:
    -   @khanacademy/perseus-core@3.2.0
    -   @khanacademy/simple-markdown@0.13.15

## 0.3.21

### Patch Changes

-   [#2072](https://github.com/Khan/perseus/pull/2072) [`6cf647729`](https://github.com/Khan/perseus/commit/6cf6477291053d85faac48028b8f038fd0c28930) Thanks [@SonicScrewdriver](https://github.com/SonicScrewdriver)! - The creation of a new Mock Widget for tests.

-   Updated dependencies [[`bbf7f3b1b`](https://github.com/Khan/perseus/commit/bbf7f3b1be657c588270a3b47983c0aecbf84418), [`6cf647729`](https://github.com/Khan/perseus/commit/6cf6477291053d85faac48028b8f038fd0c28930), [`5173c2e43`](https://github.com/Khan/perseus/commit/5173c2e43bf939159f420dcd448b90691d52353b), [`bc3d955b5`](https://github.com/Khan/perseus/commit/bc3d955b57e847a379328fcc7cf276f42e0874dd), [`d2797bb2d`](https://github.com/Khan/perseus/commit/d2797bb2dc51bd80cb03f2c1eeb39286e4dfa45c)]:
    -   @khanacademy/perseus-core@3.1.0
    -   @khanacademy/simple-markdown@0.13.14

## 0.3.20

### Patch Changes

-   [#2040](https://github.com/Khan/perseus/pull/2040) [`1496a7a93`](https://github.com/Khan/perseus/commit/1496a7a93ef691c8e34da309c10cb77d35627bf3) Thanks [@somewhatabstract](https://github.com/somewhatabstract)! - Bump versions to fix release

-   Updated dependencies [[`1496a7a93`](https://github.com/Khan/perseus/commit/1496a7a93ef691c8e34da309c10cb77d35627bf3)]:
    -   @khanacademy/perseus-core@3.0.5
    -   @khanacademy/simple-markdown@0.13.13

## 0.3.19

### Patch Changes

-   [#2037](https://github.com/Khan/perseus/pull/2037) [`b80e7882b`](https://github.com/Khan/perseus/commit/b80e7882bf58f8e71cbf9482585577032c317428) Thanks [@somewhatabstract](https://github.com/somewhatabstract)! - Nothing has changed, but our action requires a changeset per package and I don't know how to do an infrastructure update like this and pass that check

-   Updated dependencies [[`b80e7882b`](https://github.com/Khan/perseus/commit/b80e7882bf58f8e71cbf9482585577032c317428)]:
    -   @khanacademy/perseus-core@3.0.4
    -   @khanacademy/simple-markdown@0.13.12

## 0.3.18

### Patch Changes

-   [#2028](https://github.com/Khan/perseus/pull/2028) [`762b295ec`](https://github.com/Khan/perseus/commit/762b295eccd7d0dbc344edd271d3300b506adb93) Thanks [@jeremywiebe](https://github.com/jeremywiebe)! - Forcing release

-   Updated dependencies [[`762b295ec`](https://github.com/Khan/perseus/commit/762b295eccd7d0dbc344edd271d3300b506adb93)]:
    -   @khanacademy/perseus-core@3.0.3
    -   @khanacademy/simple-markdown@0.13.11

## 0.3.17

### Patch Changes

-   [#2027](https://github.com/Khan/perseus/pull/2027) [`368e222a6`](https://github.com/Khan/perseus/commit/368e222a6577dff38143d1584d6773129e8abbd7) Thanks [@handeyeco](https://github.com/handeyeco)! - Bump all packages to reset releases

-   Updated dependencies [[`368e222a6`](https://github.com/Khan/perseus/commit/368e222a6577dff38143d1584d6773129e8abbd7)]:
    -   @khanacademy/perseus-core@3.0.2
    -   @khanacademy/simple-markdown@0.13.10

## 0.3.16

### Patch Changes

-   Updated dependencies [[`e21ead80e`](https://github.com/Khan/perseus/commit/e21ead80e7cf467a2003fc145bfa1f65973eb270)]:
    -   @khanacademy/perseus-core@3.0.1
    -   @khanacademy/simple-markdown@0.13.9

## 0.3.15

### Patch Changes

-   Updated dependencies [[`ea1bf0c2c`](https://github.com/Khan/perseus/commit/ea1bf0c2cfc7ae552d039549950d1973b56f5ca9)]:
    -   @khanacademy/perseus-core@3.0.0
    -   @khanacademy/simple-markdown@0.13.8

## 0.3.14

### Patch Changes

-   [#1971](https://github.com/Khan/perseus/pull/1971) [`341d316aa`](https://github.com/Khan/perseus/commit/341d316aa8727ebb9e7fde28fc4e2d8779aa3e82) Thanks [@jeremywiebe](https://github.com/jeremywiebe)! - Move to using optional chaining in a few places to resolve new lint rule violations.

-   Updated dependencies [[`f9906728c`](https://github.com/Khan/perseus/commit/f9906728c1a8f09c91c11d824718b8a06d6a7609), [`341d316aa`](https://github.com/Khan/perseus/commit/341d316aa8727ebb9e7fde28fc4e2d8779aa3e82)]:
    -   @khanacademy/perseus-core@2.0.0
    -   @khanacademy/simple-markdown@0.13.7

## 0.3.13

### Patch Changes

-   Updated dependencies [[`88ba71bef`](https://github.com/Khan/perseus/commit/88ba71bef0cdd75fa0c8b467dcea2cccc637d034)]:
    -   @khanacademy/simple-markdown@0.13.6

## 0.3.12

### Patch Changes

-   Updated dependencies [[`55a532175`](https://github.com/Khan/perseus/commit/55a532175192bfee3dc550c7eb7ce74d4a6542a9)]:
    -   @khanacademy/simple-markdown@0.13.5

## 0.3.11

### Patch Changes

-   [#1791](https://github.com/Khan/perseus/pull/1791) [`b119147fc`](https://github.com/Khan/perseus/commit/b119147fc042bf71193d61f9cea99b5f3d73b484) Thanks [@handeyeco](https://github.com/handeyeco)! - Check types for import/no-extraneous-dependencies eslint check

-   Updated dependencies [[`b119147fc`](https://github.com/Khan/perseus/commit/b119147fc042bf71193d61f9cea99b5f3d73b484)]:
    -   @khanacademy/perseus-core@1.5.3
    -   @khanacademy/simple-markdown@0.13.4

## 0.3.10

### Patch Changes

-   Updated dependencies [[`d4f4e2be1`](https://github.com/Khan/perseus/commit/d4f4e2be1408c4531a146bcd496344a629d90bd1)]:
    -   @khanacademy/perseus-core@1.5.2
    -   @khanacademy/simple-markdown@0.13.3

## 0.3.9

### Patch Changes

-   Updated dependencies [[`eb733b3ec`](https://github.com/Khan/perseus/commit/eb733b3ec2e3354a0c4647e9993b6f08a1b77e4a)]:
    -   @khanacademy/perseus-core@1.5.1
    -   @khanacademy/simple-markdown@0.13.2

## 0.3.8

### Patch Changes

-   Updated dependencies [[`40d0b67a8`](https://github.com/Khan/perseus/commit/40d0b67a87bbb2ef5e3afcb7421ff8b64406adcb)]:
    -   @khanacademy/simple-markdown@0.13.1

## 0.3.7

### Patch Changes

-   Updated dependencies [[`c6a5cbe13`](https://github.com/Khan/perseus/commit/c6a5cbe13c5b586f7511e2c9cc3392d180b002df), [`c6a5cbe13`](https://github.com/Khan/perseus/commit/c6a5cbe13c5b586f7511e2c9cc3392d180b002df)]:
    -   @khanacademy/simple-markdown@0.13.0

## 0.3.6

### Patch Changes

-   [#1407](https://github.com/Khan/perseus/pull/1407) [`be7f14153`](https://github.com/Khan/perseus/commit/be7f141536b6ed69bba8a4378a1ddae51fd5307e) Thanks [@handeyeco](https://github.com/handeyeco)! - Updates to README files

-   Updated dependencies [[`be7f14153`](https://github.com/Khan/perseus/commit/be7f141536b6ed69bba8a4378a1ddae51fd5307e), [`b0df85a80`](https://github.com/Khan/perseus/commit/b0df85a803444a5de1f74672c5f0f5ccc3aa5617)]:
    -   @khanacademy/simple-markdown@0.12.1
    -   @khanacademy/perseus-core@1.5.0

## 0.3.5

### Patch Changes

-   Updated dependencies [[`3b85777c7`](https://github.com/Khan/perseus/commit/3b85777c7b2b970121a9c5242d34a2f9cdd2319b)]:
    -   @khanacademy/simple-markdown@0.12.0

## 0.3.4

### Patch Changes

-   Updated dependencies [[`c07644f63`](https://github.com/Khan/perseus/commit/c07644f63957ada78ba60c9b3ecd7b42e289f67b)]:
    -   @khanacademy/perseus-error@0.3.0

## 0.3.3

## 0.3.2

## 0.3.1

### Patch Changes

-   Updated dependencies [[`11e04962`](https://github.com/Khan/perseus/commit/11e04962fee178c997ca50d96eaebc446d43e66c)]:
    -   @khanacademy/simple-markdown@0.11.4

## 0.3.0

### Minor Changes

-   [#1005](https://github.com/Khan/perseus/pull/1005) [`0562929c`](https://github.com/Khan/perseus/commit/0562929c03645028b68da497382489747f0fc2c6) Thanks [@mpolyak](https://github.com/mpolyak)! - In JIPT context rendering split sections into paragraphs in ArticleRenderer

*   [#996](https://github.com/Khan/perseus/pull/996) [`4e2b5100`](https://github.com/Khan/perseus/commit/4e2b51002e41c8f36051775a7ceff4fd1a564526) Thanks [@mpolyak](https://github.com/mpolyak)! - Revert disabling markdown parser list rule in JIPT context

## 0.2.15

### Patch Changes

-   Updated dependencies [[`a4ead994`](https://github.com/Khan/perseus/commit/a4ead9940cddc09434b823039ff51b85ecd9e639)]:
    -   @khanacademy/perseus-error@0.2.11

## 0.2.14

### Patch Changes

-   [#971](https://github.com/Khan/perseus/pull/971) [`90ff7a48`](https://github.com/Khan/perseus/commit/90ff7a483b01552a556c7852427e98153cc20417) Thanks [@benchristel](https://github.com/benchristel)! - Remove source files from the distributed NPM package

-   Updated dependencies [[`90ff7a48`](https://github.com/Khan/perseus/commit/90ff7a483b01552a556c7852427e98153cc20417)]:
    -   @khanacademy/perseus-core@1.4.2
    -   @khanacademy/perseus-error@0.2.10
    -   @khanacademy/simple-markdown@0.11.3

## 0.2.13

### Patch Changes

-   Updated dependencies [[`1f4e17ba`](https://github.com/Khan/perseus/commit/1f4e17ba77e1491523813655af18a70285a25989), [`8857950b`](https://github.com/Khan/perseus/commit/8857950bdeeb6e13bc3766b1c6545289b21cbe2a)]:
    -   @khanacademy/perseus-core@1.4.1
    -   @khanacademy/perseus-error@0.2.9
    -   @khanacademy/simple-markdown@0.11.2

## 0.2.12

### Patch Changes

-   [#814](https://github.com/Khan/perseus/pull/814) [`105d2060`](https://github.com/Khan/perseus/commit/105d20603d935d35cff237b17f0bfb57ca751e4c) Thanks [@jeremywiebe](https://github.com/jeremywiebe)! - Minor build change to how we provide Typescript type definitions (should be no change to build output).

-   Updated dependencies [[`a91c84fe`](https://github.com/Khan/perseus/commit/a91c84fe53827ff4333220777a9918882b7fe9f0), [`105d2060`](https://github.com/Khan/perseus/commit/105d20603d935d35cff237b17f0bfb57ca751e4c)]:
    -   @khanacademy/perseus-core@1.4.0
    -   @khanacademy/perseus-error@0.2.8
    -   @khanacademy/simple-markdown@0.11.1

## 0.2.11

### Patch Changes

-   Updated dependencies [[`57e0e18b`](https://github.com/Khan/perseus/commit/57e0e18bd3729cde2e35cfa4ec40b67a5700049c), [`57e0e18b`](https://github.com/Khan/perseus/commit/57e0e18bd3729cde2e35cfa4ec40b67a5700049c)]:
    -   @khanacademy/simple-markdown@0.11.0

## 0.2.10

### Patch Changes

-   Updated dependencies [[`79403e06`](https://github.com/Khan/perseus/commit/79403e06eedb597d7818d6c858bbba6f51ff3fe1)]:
    -   @khanacademy/perseus-core@1.3.0
    -   @khanacademy/perseus-error@0.2.7
    -   @khanacademy/simple-markdown@0.10.4

## 0.2.9

### Patch Changes

-   Updated dependencies [[`376eb0e4`](https://github.com/Khan/perseus/commit/376eb0e4aaaa4c7a90fd6107a84bb74d382b077c)]:
    -   @khanacademy/perseus-core@1.2.0
    -   @khanacademy/perseus-error@0.2.6
    -   @khanacademy/simple-markdown@0.10.3

## 0.2.8

### Patch Changes

-   Updated dependencies [22a9c408]
    -   @khanacademy/perseus-core@1.1.2
    -   @khanacademy/perseus-error@0.2.5
    -   @khanacademy/simple-markdown@0.10.2

## 0.2.7

### Patch Changes

-   55d4cd00: Print package name and version when loaded in the page
-   Updated dependencies [55d4cd00]
    -   @khanacademy/perseus-core@1.1.1
    -   @khanacademy/perseus-error@0.2.4
    -   @khanacademy/simple-markdown@0.10.1

## 0.2.6

### Patch Changes

-   388b6506: Move comment for mathMatcher to connect to it (so editors display it correctly).
-   Updated dependencies [388b6506]
    -   @khanacademy/perseus-error@0.2.3

## 0.2.5

### Patch Changes

-   Updated dependencies [a383823d]
    -   @khanacademy/perseus-error@0.2.2

## 0.2.4

### Patch Changes

-   ce5e6297: Upgrade wonder-blocks deps to package versions without Flow types
-   Updated dependencies [afb14cff]
-   Updated dependencies [ce5e6297]
-   Updated dependencies [0993a46b]
    -   @khanacademy/simple-markdown@0.10.0

## 0.2.3

### Patch Changes

-   Updated dependencies [22c5d564]
    -   @khanacademy/simple-markdown@0.9.3

## 0.2.2

### Patch Changes

-   Updated dependencies [77391aa8]
    -   @khanacademy/simple-markdown@0.9.2

## 0.2.1

### Patch Changes

-   1f062e98: Bump all package versions since the build settings have been updated
-   Updated dependencies [1f062e98]
    -   @khanacademy/perseus-error@0.2.1
    -   @khanacademy/simple-markdown@0.9.1

## 0.2.0

### Minor Changes

-   53fd3768: Migrate source code to TypeScript

### Patch Changes

-   Updated dependencies [53fd3768]
    -   @khanacademy/perseus-error@0.2.0
    -   @khanacademy/simple-markdown@0.9.0

## 0.1.5

### Patch Changes

-   Updated dependencies [a1b4ab3c]
    -   @khanacademy/perseus-error@0.1.5

## 0.1.4

### Patch Changes

-   Updated dependencies [6a7f36be]
    -   @khanacademy/perseus-error@0.1.4

## 0.1.3

### Patch Changes

-   f567f660: Update the eslint config to look at both the package.json for the package and the one from the root
-   Updated dependencies [f567f660]
-   Updated dependencies [eeaa9010]
    -   @khanacademy/perseus-error@0.1.3
    -   @khanacademy/simple-markdown@0.8.6

## 0.1.2

### Patch Changes

-   bf180fe1: Fix our use of import/no-extraneous-dependencies
-   Updated dependencies [bf180fe1]
    -   @khanacademy/perseus-error@0.1.2
    -   @khanacademy/simple-markdown@0.8.5

## 0.1.1

### Patch Changes

-   98d283ff: Fix storybook
-   Updated dependencies [98d283ff]
    -   @khanacademy/perseus-error@0.1.1
    -   @khanacademy/simple-markdown@0.8.4

## 0.1.0

### Minor Changes

-   a4f10ace: Move Gorgon, PerseusError, PureMarkdown into their own packages

### Patch Changes

-   Updated dependencies [a4f10ace]
    -   @khanacademy/perseus-error@0.1.0

import type { NextApiRequest, NextApiResponse } from 'next';
import { generateStructuredData, Type } from '../../backend/lib/gemini';

type Contact = { name: string; detail: string; avatar: string; };
type SafetyItem = { label: string; color: string; };
type SystemRequest = { text: string; };
type SystemStat = { icon: string; value: string; label: string; };
type WidgetsData = {
  contacts: Contact[];
  safetyItems: SafetyItem[];
  safetyChartData: number[];
  systemRequests: SystemRequest[];
  systemStats: SystemStat[];
};

const dashboardWidgetsSchema = {
    type: Type.OBJECT,
    properties: {
        contacts: {
            type: Type.ARRAY,
            description: "A list of exactly 3 high-level contacts for direct access in a futuristic corporation.",
            items: {
                type: Type.OBJECT,
                properties: {
                    name: { type: Type.STRING, description: "Full name of the contact, e.g., 'Dr. Aris Thorne'." },
                    detail: { type: Type.STRING, description: "Job title or role, e.g., 'Chief Scientist'." },
                    avatar: { type: Type.STRING, description: "A unique, single-word seed for a placeholder avatar URL, e.g., 'aris'."}
                },
                required: ["name", "detail", "avatar"]
            }
        },
        safetyItems: {
            type: Type.ARRAY,
            description: "A list of exactly 6 system safety status items with appropriate sci-fi terminology.",
            items: {
                type: Type.OBJECT,
                properties: {
                    label: { type: Type.STRING, description: "The safety status label, e.g., 'Firewall Active', 'Sub-routine Anomaly'." },
                    color: { type: Type.STRING, enum: ['#3b82f6', '#ef4444', '#00FFFF'], description: "Color code: blue (#3b82f6) for status, red (#ef4444) for warning, cyan (#00FFFF) for secure." }
                },
                required: ["label", "color"]
            }
        },
        safetyChartData: {
            type: Type.ARRAY,
            description: "A list of exactly 6 numbers between 30 and 100 representing bar chart heights.",
            items: { type: Type.NUMBER }
        },
        systemRequests: {
            type: Type.ARRAY,
            description: "A list of exactly 3 recent system status updates or requests, with sci-fi themes.",
            items: {
                type: Type.OBJECT,
                properties: {
                    text: { type: Type.STRING, description: "The description of the request, e.g., 'Security Patch Update', 'Cryo-chamber diagnostics'." }
                },
                required: ["text"]
            }
        },
        systemStats: {
            type: Type.ARRAY,
            description: "A list of exactly 5 system performance statistics.",
            items: {
                type: Type.OBJECT,
                properties: {
                    icon: { type: Type.STRING, enum: ['BuildingOfficeIcon', 'PuzzlePieceIcon', 'SnowflakeIcon', 'MemoryChipIcon', 'CogIcon'], description: "The icon representing the stat." },
                    value: { type: Type.STRING, description: "The value of the stat, e.g., '1597' or '84%' or '24°C'." },
                    label: { type: Type.STRING, description: "The label for the stat, e.g., 'Processes' or 'RAM Usage'." }
                },
                 required: ["icon", "value", "label"]
            }
        }
    },
    required: ["contacts", "safetyItems", "safetyChartData", "systemRequests", "systemStats"]
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<WidgetsData | { error: string }>
) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  try {
    const prompt = "Generate plausible data for several widgets on a futuristic system dashboard. Create 3 contacts, 6 safety status items, 6 data points for a small bar chart, 3 system requests/updates, and 5 system performance stats. Use creative, sci-fi-themed names and labels.";
    const data = await generateStructuredData<WidgetsData>(prompt, dashboardWidgetsSchema);
    res.status(200).json(data);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Failed to fetch dashboard widget data from AI.' });
  }
}


import React, { useState } from 'react';
import { Card } from '../../components/Card';
import { ArrowDownTrayIcon, ArrowUpTrayIcon, WifiIcon } from '@heroicons/react/24/outline';

export type NetworkStatusData = {
    status: string;
    latency: number;
    download: number;
    upload: number;
};

interface NetworkToolProps {
    initialStatus: NetworkStatusData;
}

export const NetworkTool: React.FC<NetworkToolProps> = ({ initialStatus }) => {
  const [status, setStatus] = useState(initialStatus);
  const [testing, setTesting] = useState(false);

  const handleSpeedTest = async () => {
      setTesting(true);
      try {
          const response = await fetch('/api/tools', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ action: 'runSpeedTest' })
          });
          const newStatus = await response.json();
          setStatus(s => ({...s, ...newStatus}));
      } catch (error) {
          console.error("Speed test failed", error);
      } finally {
          setTesting(false);
      }
  }

  return (
    <Card title="Network Status">
      <div className="flex flex-col justify-between h-full">
        <div className="flex items-center gap-3">
          <WifiIcon className={`w-8 h-8 ${status.status === 'Connected' ? 'text-green-400' : 'text-red-400'}`} />
          <div>
            <p className="font-semibold text-white">{testing ? 'Testing...' : status.status}</p>
            <p className="text-xs text-gray-400">Latency: {testing ? '...' : `${status.latency}ms`}</p>
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4 mt-4">
          <div className="bg-gray-700/50 p-3 rounded-lg">
            <p className="text-xs text-gray-400 flex items-center gap-1"><ArrowDownTrayIcon className="w-4 h-4" /> Download</p>
            <p className="text-lg font-bold text-white">{testing ? '...' : status.download} <span className="text-sm font-normal">Mbps</span></p>
          </div>
          <div className="bg-gray-700/50 p-3 rounded-lg">
            <p className="text-xs text-gray-400 flex items-center gap-1"><ArrowUpTrayIcon className="w-4 h-4" /> Upload</p>
            <p className="text-lg font-bold text-white">{testing ? '...' : status.upload} <span className="text-sm font-normal">Mbps</span></p>
          </div>
        </div>
        <button 
            onClick={handleSpeedTest}
            disabled={testing}
            className="w-full mt-4 py-2 text-sm font-semibold text-white bg-cyan-500/30 border border-cyan-500/50 rounded-lg hover:bg-cyan-500/50 transition-colors disabled:opacity-50 disabled:cursor-wait"
        >
          {testing ? 'TESTING...' : 'Run Speed Test'}
        </button>
      </div>
    </Card>
  );
};

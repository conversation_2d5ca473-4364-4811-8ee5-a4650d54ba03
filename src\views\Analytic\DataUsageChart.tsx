
import React from 'react';
import { Card } from '../../components/Card';
import { AreaChart, Area, XAxis, YAxis, Tooltip, ResponsiveContainer } from 'recharts';

interface DataUsageChartProps {
  data: { name: string; usage: number }[];
}

export const DataUsageChart: React.FC<DataUsageChartProps> = ({ data }) => {
  return (
    <Card title="Data Usage Trend (GB)">
      <div className="w-full h-64">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
            <defs>
              <linearGradient id="colorUsage" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#00FFFF" stopOpacity={0.8}/>
                <stop offset="95%" stopColor="#00FFFF" stopOpacity={0}/>
              </linearGradient>
            </defs>
            <XAxis dataKey="name" tick={{ fill: '#A0A0B0', fontSize: 12 }} axisLine={false} tickLine={false} />
            <YAxis tick={{ fill: '#A0A0B0', fontSize: 12 }} axisLine={false} tickLine={false} />
            <Tooltip
              contentStyle={{
                background: 'rgba(25, 40, 60, 0.8)',
                borderColor: '#00FFFF',
                color: '#E0E0E0',
                borderRadius: '0.5rem'
              }}
            />
            <Area type="monotone" dataKey="usage" stroke="#00FFFF" fillOpacity={1} fill="url(#colorUsage)" />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </Card>
  );
};

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_views_Tool_ToolView_tsx";
exports.ids = ["src_views_Tool_ToolView_tsx"];
exports.modules = {

/***/ "__barrel_optimize__?names=ArrowDownTrayIcon,ArrowUpTrayIcon,WifiIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*********************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowDownTrayIcon,ArrowUpTrayIcon,WifiIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowDownTrayIcon: () => (/* reexport safe */ _ArrowDownTrayIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   ArrowUpTrayIcon: () => (/* reexport safe */ _ArrowUpTrayIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   WifiIcon: () => (/* reexport safe */ _WifiIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ArrowDownTrayIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArrowDownTrayIcon.js */ \"./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* harmony import */ var _ArrowUpTrayIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ArrowUpTrayIcon.js */ \"./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/outline/esm/ArrowUpTrayIcon.js\");\n/* harmony import */ var _WifiIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./WifiIcon.js */ \"./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/outline/esm/WifiIcon.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcnJvd0Rvd25UcmF5SWNvbixBcnJvd1VwVHJheUljb24sV2lmaUljb24hPSEuL25vZGVfbW9kdWxlcy8ucG5wbS9AaGVyb2ljb25zK3JlYWN0QDIuMi4wX3JlYWN0QDE4LjIuMC9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQ3FFO0FBQ0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9leWVzLXNoaWVsZC1mdXR1cmlzdGljLXVpLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0BoZXJvaWNvbnMrcmVhY3RAMi4yLjBfcmVhY3RAMTguMi4wL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzP2JlZmYiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEFycm93RG93blRyYXlJY29uIH0gZnJvbSBcIi4vQXJyb3dEb3duVHJheUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBBcnJvd1VwVHJheUljb24gfSBmcm9tIFwiLi9BcnJvd1VwVHJheUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBXaWZpSWNvbiB9IGZyb20gXCIuL1dpZmlJY29uLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArrowDownTrayIcon,ArrowUpTrayIcon,WifiIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "./src/views/Tool/NetworkTool.tsx":
/*!****************************************!*\
  !*** ./src/views/Tool/NetworkTool.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NetworkTool: () => (/* binding */ NetworkTool)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/Card */ \"./src/components/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowUpTrayIcon_WifiIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowUpTrayIcon,WifiIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowDownTrayIcon,ArrowUpTrayIcon,WifiIcon!=!./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.2.0/node_modules/@heroicons/react/24/outline/esm/index.js\");\n\n\n\n\nconst NetworkTool = ({ initialStatus })=>{\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialStatus);\n    const [testing, setTesting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSpeedTest = async ()=>{\n        setTesting(true);\n        try {\n            const response = await fetch(\"/api/tools\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    action: \"runSpeedTest\"\n                })\n            });\n            const newStatus = await response.json();\n            setStatus((s)=>({\n                    ...s,\n                    ...newStatus\n                }));\n        } catch (error) {\n            console.error(\"Speed test failed\", error);\n        } finally{\n            setTesting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        title: \"Network Status\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col justify-between h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowUpTrayIcon_WifiIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.WifiIcon, {\n                            className: `w-8 h-8 ${status.status === \"Connected\" ? \"text-green-400\" : \"text-red-400\"}`\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\NetworkTool.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-semibold text-white\",\n                                    children: testing ? \"Testing...\" : status.status\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\NetworkTool.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: [\n                                        \"Latency: \",\n                                        testing ? \"...\" : `${status.latency}ms`\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\NetworkTool.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\NetworkTool.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\NetworkTool.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4 mt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-700/50 p-3 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowUpTrayIcon_WifiIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ArrowDownTrayIcon, {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\NetworkTool.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 74\n                                        }, undefined),\n                                        \" Download\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\NetworkTool.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: [\n                                        testing ? \"...\" : status.download,\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-normal\",\n                                            children: \"Mbps\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\NetworkTool.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 93\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\NetworkTool.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\NetworkTool.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-700/50 p-3 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowUpTrayIcon_WifiIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ArrowUpTrayIcon, {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\NetworkTool.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 74\n                                        }, undefined),\n                                        \" Upload\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\NetworkTool.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: [\n                                        testing ? \"...\" : status.upload,\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-normal\",\n                                            children: \"Mbps\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\NetworkTool.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 91\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\NetworkTool.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\NetworkTool.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\NetworkTool.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleSpeedTest,\n                    disabled: testing,\n                    className: \"w-full mt-4 py-2 text-sm font-semibold text-white bg-cyan-500/30 border border-cyan-500/50 rounded-lg hover:bg-cyan-500/50 transition-colors disabled:opacity-50 disabled:cursor-wait\",\n                    children: testing ? \"TESTING...\" : \"Run Speed Test\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\NetworkTool.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\NetworkTool.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\NetworkTool.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/Tool/NetworkTool.tsx\n");

/***/ }),

/***/ "./src/views/Tool/SystemDiagnostics.tsx":
/*!**********************************************!*\
  !*** ./src/views/Tool/SystemDiagnostics.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SystemDiagnostics: () => (/* binding */ SystemDiagnostics)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/Card */ \"./src/components/Card.tsx\");\n\n\n\nconst Gauge = ({ label, value, color })=>{\n    const radius = 40;\n    const circumference = 2 * Math.PI * radius;\n    const offset = circumference - value / 100 * circumference;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center gap-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-24 h-24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-full h-full\",\n                        viewBox: \"0 0 100 100\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                cx: \"50\",\n                                cy: \"50\",\n                                r: radius,\n                                fill: \"none\",\n                                stroke: \"rgba(255,255,255,0.1)\",\n                                strokeWidth: \"8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\SystemDiagnostics.tsx\",\n                                lineNumber: 14,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                cx: \"50\",\n                                cy: \"50\",\n                                r: radius,\n                                fill: \"none\",\n                                stroke: color,\n                                strokeWidth: \"8\",\n                                strokeDasharray: circumference,\n                                strokeDashoffset: offset,\n                                strokeLinecap: \"round\",\n                                transform: \"rotate(-90 50 50)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\SystemDiagnostics.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\SystemDiagnostics.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center text-white text-xl font-bold\",\n                        children: [\n                            value,\n                            \"%\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\SystemDiagnostics.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\SystemDiagnostics.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-gray-300\",\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\SystemDiagnostics.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\SystemDiagnostics.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\nconst SystemDiagnostics = ({ diagnostics })=>{\n    if (!diagnostics) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            title: \"System Diagnostics\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-full text-gray-400\",\n                children: \"Loading diagnostics...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\SystemDiagnostics.tsx\",\n                lineNumber: 50,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\SystemDiagnostics.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        title: \"System Diagnostics\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-around items-center h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Gauge, {\n                    label: \"CPU Load\",\n                    value: diagnostics.cpuLoad,\n                    color: \"#00FFFF\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\SystemDiagnostics.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Gauge, {\n                    label: \"RAM Usage\",\n                    value: diagnostics.ramUsage,\n                    color: \"#FF007F\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\SystemDiagnostics.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\SystemDiagnostics.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\SystemDiagnostics.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/Tool/SystemDiagnostics.tsx\n");

/***/ }),

/***/ "./src/views/Tool/ToolView.tsx":
/*!*************************************!*\
  !*** ./src/views/Tool/ToolView.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToolView: () => (/* binding */ ToolView)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _SystemDiagnostics__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./SystemDiagnostics */ \"./src/views/Tool/SystemDiagnostics.tsx\");\n/* harmony import */ var _NetworkTool__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./NetworkTool */ \"./src/views/Tool/NetworkTool.tsx\");\n\n\n\n\nconst ToolView = ()=>{\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            try {\n                setLoading(true);\n                const response = await fetch(\"/api/tools\");\n                const result = await response.json();\n                setData(result);\n            } catch (error) {\n                console.error(\"Failed to fetch tool data\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchData();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-full text-cyan-400\",\n            children: \"Loading Tools...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\ToolView.tsx\",\n            lineNumber: 32,\n            columnNumber: 16\n        }, undefined);\n    }\n    if (!data) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-full text-red-400\",\n            children: \"Failed to load tool data.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\ToolView.tsx\",\n            lineNumber: 36,\n            columnNumber: 16\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-grow grid grid-cols-1 md:grid-cols-2 gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SystemDiagnostics__WEBPACK_IMPORTED_MODULE_2__.SystemDiagnostics, {\n                diagnostics: data.diagnostics\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\ToolView.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NetworkTool__WEBPACK_IMPORTED_MODULE_3__.NetworkTool, {\n                initialStatus: data.network\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\ToolView.tsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\views\\\\Tool\\\\ToolView.tsx\",\n        lineNumber: 40,\n        columnNumber: 9\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/views/Tool/ToolView.tsx\n");

/***/ })

};
;
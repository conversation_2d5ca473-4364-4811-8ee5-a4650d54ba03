
import type { NextApiRequest, NextApiResponse } from 'next';
import { generateStructuredData, Type } from '../../backend/lib/gemini';

type Course = {
  name: string;
  progress: number;
  color: 'cyan' | 'pink' | 'yellow';
};
type Assignment = {
  name: string;
  due: string;
  course: string;
};
type AcademicOverview = {
    gpa: string;
    credits: number;
    assignmentsDue: number;
};
type SchoolData = {
  overview: AcademicOverview;
  courses: Course[];
  assignments: Assignment[];
};

const schoolSchema = {
    type: Type.OBJECT,
    properties: {
        overview: {
            type: Type.OBJECT,
            properties: {
                gpa: { type: Type.STRING, description: "The student's GPA as a string, e.g., '3.85'." },
                credits: { type: Type.INTEGER, description: "Total credits for the semester (e.g., 12-18)." },
                assignmentsDue: { type: Type.INTEGER, description: "Number of assignments due this week (e.g., 2-5)." }
            },
            required: ['gpa', 'credits', 'assignmentsDue']
        },
        courses: {
            type: Type.ARRAY,
            description: "A list of 3 futuristic university courses.",
            items: {
                type: Type.OBJECT,
                properties: {
                    name: { type: Type.STRING, description: "Name of a futuristic university course (e.g., 'Quantum Mechanics', 'Astrobiology')." },
                    progress: { type: Type.INTEGER, description: "Progress percentage (0-100)." },
                    color: { type: Type.STRING, enum: ['cyan', 'pink', 'yellow'] }
                },
                required: ['name', 'progress', 'color']
            }
        },
        assignments: {
            type: Type.ARRAY,
            description: "A list of 3 upcoming assignments.",
            items: {
                type: Type.OBJECT,
                properties: {
                    name: { type: Type.STRING, description: "Name of a futuristic assignment." },
                    due: { type: Type.STRING, description: "A relative due date, e.g., '3 days' or '1 week'." },
                    course: { type: Type.STRING, description: "The name of the course this assignment belongs to." }
                },
                required: ['name', 'due', 'course']
            }
        }
    },
    required: ["overview", "courses", "assignments"]
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<SchoolData | { error: string }>
) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  try {
    const prompt = "Generate mock data for a student's dashboard in a futuristic university. The student is doing well. Provide an overview, 3 courses, and 3 upcoming assignments. Course names should sound advanced and sci-fi. Match the assignment courses to the generated course names.";
    const data = await generateStructuredData<SchoolData>(prompt, schoolSchema);
    res.status(200).json(data);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Failed to fetch school data from AI.' });
  }
}

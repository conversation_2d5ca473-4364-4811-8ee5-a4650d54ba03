
import type { NextApiRequest, NextApiResponse } from 'next';
import { executeQuerySingle, initializeDatabase, seedDatabase } from '../../backend/lib/database';

type Course = {
  name: string;
  progress: number;
  color: 'cyan' | 'pink' | 'yellow';
};
type Assignment = {
  name: string;
  due: string;
  course: string;
};
type AcademicOverview = {
    gpa: string;
    credits: number;
    assignmentsDue: number;
};
type SchoolData = {
  overview: AcademicOverview;
  courses: Course[];
  assignments: Assignment[];
};

type DatabaseSchoolData = {
  id: number;
  gpa: number;
  credits: number;
  attendance: number;
  assignments_completed: number;
  assignments_total: number;
  created_at: string;
  updated_at: string;
};



export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<SchoolData | { error: string }>
) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  try {
    // Initialize database if needed
    await initializeDatabase();
    await seedDatabase();

    // Get school data from database
    const schoolData = await executeQuerySingle<DatabaseSchoolData>(`
      SELECT * FROM school_data
      ORDER BY updated_at DESC
      LIMIT 1
    `);

    if (schoolData) {
      const assignmentsDue = schoolData.assignments_total - schoolData.assignments_completed;

      const data: SchoolData = {
        overview: {
          gpa: schoolData.gpa.toFixed(2),
          credits: schoolData.credits,
          assignmentsDue: assignmentsDue
        },
        courses: [
          { name: "Quantum Computing Theory", progress: 85, color: 'cyan' },
          { name: "Neural Network Architecture", progress: 92, color: 'pink' },
          { name: "Cybersecurity Protocols", progress: 78, color: 'yellow' }
        ],
        assignments: [
          { name: "Quantum Algorithm Implementation", due: "2 days", course: "Quantum Computing Theory" },
          { name: "Deep Learning Model Training", due: "5 days", course: "Neural Network Architecture" },
          { name: "Security Audit Report", due: "1 week", course: "Cybersecurity Protocols" },
          { name: "Research Paper: AI Ethics", due: "2 weeks", course: "Neural Network Architecture" }
        ]
      };

      res.status(200).json(data);
    } else {
      // Fallback data if no school data exists
      const fallbackData: SchoolData = {
        overview: {
          gpa: "3.85",
          credits: 120,
          assignmentsDue: 4
        },
        courses: [
          { name: "Quantum Computing Theory", progress: 85, color: 'cyan' },
          { name: "Neural Network Architecture", progress: 92, color: 'pink' },
          { name: "Cybersecurity Protocols", progress: 78, color: 'yellow' }
        ],
        assignments: [
          { name: "Quantum Algorithm Implementation", due: "2 days", course: "Quantum Computing Theory" },
          { name: "Deep Learning Model Training", due: "5 days", course: "Neural Network Architecture" },
          { name: "Security Audit Report", due: "1 week", course: "Cybersecurity Protocols" },
          { name: "Research Paper: AI Ethics", due: "2 weeks", course: "Neural Network Architecture" }
        ]
      };
      res.status(200).json(fallbackData);
    }
  } catch (error) {
    console.error('School API error:', error);

    // Fallback data if database fails
    const fallbackData: SchoolData = {
      overview: {
        gpa: "3.85",
        credits: 120,
        assignmentsDue: 4
      },
      courses: [
        { name: "Quantum Computing Theory", progress: 85, color: 'cyan' },
        { name: "Neural Network Architecture", progress: 92, color: 'pink' },
        { name: "Cybersecurity Protocols", progress: 78, color: 'yellow' }
      ],
      assignments: [
        { name: "Quantum Algorithm Implementation", due: "2 days", course: "Quantum Computing Theory" },
        { name: "Deep Learning Model Training", due: "5 days", course: "Neural Network Architecture" },
        { name: "Security Audit Report", due: "1 week", course: "Cybersecurity Protocols" },
        { name: "Research Paper: AI Ethics", due: "2 weeks", course: "Neural Network Architecture" }
      ]
    };
    res.status(200).json(fallbackData);
  }
}

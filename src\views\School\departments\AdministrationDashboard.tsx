import React from 'react';
import { Card } from '../../../components/Card';
import { 
  BuildingOfficeIcon,
  UserGroupIcon,
  DocumentTextIcon,
  ClipboardDocumentListIcon
} from '@heroicons/react/24/solid';

interface AdministrationDashboardProps {
  activeSubSection: string;
}

export const AdministrationDashboard: React.FC<AdministrationDashboardProps> = ({ activeSubSection }) => {
  const renderContent = () => {
    switch (activeSubSection) {
      case 'dashboard':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card title="Administration Overview" className="lg:col-span-3">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4">
                <div className="text-center">
                  <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <UserGroupIcon className="w-6 h-6 text-purple-400" />
                  </div>
                  <p className="text-2xl font-bold text-white">89</p>
                  <p className="text-sm text-gray-400">Staff Members</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <DocumentTextIcon className="w-6 h-6 text-blue-400" />
                  </div>
                  <p className="text-2xl font-bold text-white">156</p>
                  <p className="text-sm text-gray-400">Active Policies</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <ClipboardDocumentListIcon className="w-6 h-6 text-green-400" />
                  </div>
                  <p className="text-2xl font-bold text-white">23</p>
                  <p className="text-sm text-gray-400">Pending Tasks</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <BuildingOfficeIcon className="w-6 h-6 text-yellow-400" />
                  </div>
                  <p className="text-2xl font-bold text-white">12</p>
                  <p className="text-sm text-gray-400">Departments</p>
                </div>
              </div>
            </Card>
          </div>
        );
      case 'staff':
        return (
          <Card title="Staff Management">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Staff Management System</h3>
              <p className="text-gray-400">Manage staff records, schedules, and performance evaluations.</p>
            </div>
          </Card>
        );
      default:
        return (
          <Card title="Administration Dashboard">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Administration Portal</h3>
              <p className="text-gray-400">Select a section from the navigation to get started.</p>
            </div>
          </Card>
        );
    }
  };

  return (
    <div className="p-4 h-full">
      {renderContent()}
    </div>
  );
};

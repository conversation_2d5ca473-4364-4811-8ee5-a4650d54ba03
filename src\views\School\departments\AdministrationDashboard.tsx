import React, { useState, useEffect } from 'react';
import { Card } from '../../../components/Card';
import { StaffManagement } from '../../../components/StaffManagement';
import {
  BuildingOfficeIcon,
  UserGroupIcon,
  DocumentTextIcon,
  ClipboardDocumentListIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  CogIcon,
  BoltIcon
} from '@heroicons/react/24/solid';

interface AdministrationDashboardProps {
  activeSubSection: string;
}

export const AdministrationDashboard: React.FC<AdministrationDashboardProps> = ({ activeSubSection }) => {
  const [adminData, setAdminData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchAdminData = async () => {
      try {
        setLoading(true);
        await new Promise(resolve => setTimeout(resolve, 500));

        const mockData = {
          overview: {
            totalStaff: 89,
            aiAssistants: 156,
            automatedProcesses: 847,
            blockchainRecords: 2847,
            quantumSecurity: 99.8,
            holoMeetings: 23,
            neuralNetworkDecisions: 456,
            carbonNeutralScore: 94.7
          },
          staff: [
            { name: 'Dr. <PERSON>', role: 'AI Quantum Physics Director', department: 'Science', aiPartner: 'Einstein AI', efficiency: 97.2, status: 'Active' },
            { name: 'Prof. Marcus Johnson', role: 'Neural Network Coordinator', department: 'Technology', aiPartner: 'Turing AI', efficiency: 94.8, status: 'Active' },
            { name: 'Dr. Elena Rodriguez', role: 'Bioengineering Head', department: 'Life Sciences', aiPartner: 'Darwin AI', efficiency: 96.1, status: 'Active' },
            { name: 'Dr. James Wilson', role: 'Space Colonization Lead', department: 'Aerospace', aiPartner: 'Hawking AI', efficiency: 98.3, status: 'Active' }
          ],
          aiDecisions: [
            { decision: 'Optimize class schedules using quantum algorithms', impact: 'Increased efficiency by 34%', confidence: 96, status: 'Implemented' },
            { decision: 'Deploy holographic teachers for remote learning', impact: 'Reduced costs by $2.3M annually', confidence: 94, status: 'Active' },
            { decision: 'Implement blockchain-based credential verification', impact: '100% fraud prevention', confidence: 99, status: 'Live' },
            { decision: 'AI-powered emotional wellness monitoring', impact: '87% improvement in student wellbeing', confidence: 92, status: 'Testing' }
          ],
          systemMetrics: [
            { metric: 'Quantum Security Level', value: '99.8%', trend: 'up', color: 'text-green-400' },
            { metric: 'AI Decision Accuracy', value: '96.7%', trend: 'up', color: 'text-blue-400' },
            { metric: 'Process Automation', value: '89.4%', trend: 'up', color: 'text-purple-400' },
            { metric: 'Carbon Neutrality', value: '94.7%', trend: 'up', color: 'text-cyan-400' }
          ],
          upcomingTasks: [
            { task: 'Deploy Neural Interface Learning System', priority: 'High', deadline: '2024-03-25', assignee: 'AI Task Force' },
            { task: 'Quantum Encryption Upgrade', priority: 'Critical', deadline: '2024-03-20', assignee: 'Security AI' },
            { task: 'Hologram Teacher Training Program', priority: 'Medium', deadline: '2024-03-30', assignee: 'Education AI' }
          ]
        };

        setAdminData(mockData);
      } catch (error) {
        console.error('Failed to fetch admin data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAdminData();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-cyan-400">Loading AI Administration...</div>
      </div>
    );
  }

  const renderContent = () => {
    switch (activeSubSection) {
      case 'dashboard':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* AI Administration Overview */}
            <Card title="🤖 AI Administration Hub" className="lg:col-span-3">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4">
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <UserGroupIcon className="w-6 h-6 text-white" />
                  </div>
                  <p className="text-2xl font-bold text-white">{adminData.overview.aiAssistants}</p>
                  <p className="text-sm text-gray-400">AI Assistants</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <BoltIcon className="w-6 h-6 text-white" />
                  </div>
                  <p className="text-2xl font-bold text-white">{adminData.overview.automatedProcesses}</p>
                  <p className="text-sm text-gray-400">Automated Processes</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <ShieldCheckIcon className="w-6 h-6 text-white" />
                  </div>
                  <p className="text-2xl font-bold text-white">{adminData.overview.quantumSecurity}%</p>
                  <p className="text-sm text-gray-400">Quantum Security</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <ChartBarIcon className="w-6 h-6 text-white" />
                  </div>
                  <p className="text-2xl font-bold text-white">{adminData.overview.neuralNetworkDecisions}</p>
                  <p className="text-sm text-gray-400">AI Decisions</p>
                </div>
              </div>
            </Card>

            {/* AI-Enhanced Staff */}
            <Card title="👥 AI-Enhanced Staff" className="lg:col-span-2">
              <div className="space-y-3 p-4">
                {adminData.staff.map((staff: any, index: number) => (
                  <div key={index} className="p-3 rounded-lg bg-gradient-to-r from-gray-800/40 to-gray-700/40 border border-gray-600/30">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1">
                        <h4 className="font-semibold text-white">{staff.name}</h4>
                        <p className="text-sm text-gray-400">{staff.role}</p>
                        <p className="text-xs text-cyan-400 mt-1">🤖 Partner: {staff.aiPartner}</p>
                      </div>
                      <div className="text-right">
                        <span className="text-sm font-bold text-green-400">{staff.efficiency}%</span>
                        <p className="text-xs text-gray-400">Efficiency</p>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-purple-400">{staff.department}</span>
                      <span className="px-2 py-1 rounded-full text-xs bg-green-500/20 text-green-400">
                        {staff.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* AI Decision Log */}
            <Card title="🧠 AI Decision Log">
              <div className="space-y-3 p-4">
                {adminData.aiDecisions.map((decision: any, index: number) => (
                  <div key={index} className="p-3 rounded-lg bg-gradient-to-r from-purple-900/20 to-blue-900/20 border border-purple-500/30">
                    <div className="flex items-center justify-between mb-2">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        decision.status === 'Implemented' ? 'bg-green-500/20 text-green-400' :
                        decision.status === 'Active' ? 'bg-blue-500/20 text-blue-400' :
                        decision.status === 'Live' ? 'bg-cyan-500/20 text-cyan-400' :
                        'bg-yellow-500/20 text-yellow-400'
                      }`}>
                        {decision.status}
                      </span>
                      <span className="text-xs text-gray-400">{decision.confidence}% confidence</span>
                    </div>
                    <h4 className="text-sm font-medium text-white mb-1">{decision.decision}</h4>
                    <p className="text-xs text-cyan-400">💡 {decision.impact}</p>
                  </div>
                ))}
              </div>
            </Card>
          </div>
        );

      case 'staff':
        return <StaffManagement />;

      default:
        return (
          <Card title="AI Administration Portal">
            <div className="p-6 text-center">
              <h3 className="text-xl font-bold text-white mb-4">Future Administration</h3>
              <p className="text-gray-400">AI-powered administration with quantum security and neural network decision making.</p>
            </div>
          </Card>
        );
    }
  };

  return (
    <div className="p-4 h-full">
      {renderContent()}
    </div>
  );
};

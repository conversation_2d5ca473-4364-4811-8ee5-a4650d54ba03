"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/.pnpm/next@14.2.3_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _src_components_Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../src/components/Sidebar */ \"./src/components/Sidebar.tsx\");\n/* harmony import */ var _src_components_Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../src/components/Header */ \"./src/components/Header.tsx\");\n/* harmony import */ var _src_components_Footer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../src/components/Footer */ \"./src/components/Footer.tsx\");\n/* harmony import */ var _src_components_RightSidebar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../src/components/RightSidebar */ \"./src/components/RightSidebar.tsx\");\n/* harmony import */ var _src_views_School_SchoolView__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../src/views/School/SchoolView */ \"./src/views/School/SchoolView.tsx\");\n/* harmony import */ var _src_hooks_useAutoScale__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../src/hooks/useAutoScale */ \"./src/hooks/useAutoScale.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst LoadingComponent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-grow flex items-center justify-center text-cyan-400\",\n        children: \"Loading View...\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n        lineNumber: 12,\n        columnNumber: 32\n    }, undefined);\n_c = LoadingComponent;\n// Dynamically import views for code splitting and performance\nconst viewComponents = {\n    Dashboard: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Dashboard_DashboardView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Dashboard/DashboardView */ \"./src/views/Dashboard/DashboardView.tsx\")).then((mod)=>mod.DashboardView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Dashboard/DashboardView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    School: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/School/SchoolView */ \"./src/views/School/SchoolView.tsx\")).then((mod)=>mod.SchoolView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/School/SchoolView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Tool: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Tool_ToolView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Tool/ToolView */ \"./src/views/Tool/ToolView.tsx\")).then((mod)=>mod.ToolView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Tool/ToolView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Market: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Market_MarketView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Market/MarketView */ \"./src/views/Market/MarketView.tsx\")).then((mod)=>mod.MarketView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Market/MarketView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Bookstore: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Bookstore_BookstoreView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Bookstore/BookstoreView */ \"./src/views/Bookstore/BookstoreView.tsx\")).then((mod)=>mod.BookstoreView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Bookstore/BookstoreView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Concierge: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Concierge_ConciergeView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Concierge/ConciergeView */ \"./src/views/Concierge/ConciergeView.tsx\")).then((mod)=>mod.ConciergeView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Concierge/ConciergeView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Analytic: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Analytic_AnalyticView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Analytic/AnalyticView */ \"./src/views/Analytic/AnalyticView.tsx\")).then((mod)=>mod.AnalyticView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Analytic/AnalyticView\"\n            ]\n        },\n        loading: LoadingComponent\n    }),\n    Setting: next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"src_views_Setting_SettingView_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../src/views/Setting/SettingView */ \"./src/views/Setting/SettingView.tsx\")).then((mod)=>mod.SettingView), {\n        loadableGenerated: {\n            modules: [\n                \"pages\\\\index.tsx -> \" + \"../src/views/Setting/SettingView\"\n            ]\n        },\n        loading: LoadingComponent\n    })\n};\nconst HomePage = ()=>{\n    _s();\n    const [activeView, setActiveView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Dashboard\");\n    const [activeDepartment, setActiveDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"school\");\n    // Auto-scaling hook to fit content perfectly in viewport\n    const { containerRef } = (0,_src_hooks_useAutoScale__WEBPACK_IMPORTED_MODULE_9__.useAutoScale)({\n        targetWidth: 1920,\n        targetHeight: 1080,\n        minScale: 0.5,\n        maxScale: 1.0\n    });\n    const ActiveComponent = viewComponents[activeView] || viewComponents.Dashboard;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Eyes Shield UI\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"A futuristic, high-tech dashboard UI with a glassmorphism theme, displaying various data visualizations and system statuses on a semi-transparent, glowing panel.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1.0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"main-background dashboard-container font-poppins text-[#E0E0E0] flex\",\n                style: {\n                    fontSize: \"clamp(10px, 0.7vw, 14px)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/60 backdrop-blur-xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10 bg-container-bg flex-1 flex border-0 min-h-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Sidebar__WEBPACK_IMPORTED_MODULE_4__.Sidebar, {\n                                    activeView: activeView,\n                                    setActiveView: (view)=>setActiveView(view)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 flex flex-col min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-2 py-1\",\n                                            style: {\n                                                padding: \"clamp(4px, 0.5vw, 16px)\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Header__WEBPACK_IMPORTED_MODULE_5__.Header, {\n                                                showSchoolButtons: activeView === \"School\",\n                                                activeDepartment: activeDepartment,\n                                                setActiveDepartment: setActiveDepartment\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-h-0 auto-fit-content px-2\",\n                                            style: {\n                                                padding: \"0 clamp(4px, 0.5vw, 16px)\"\n                                            },\n                                            children: activeView === \"School\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_views_School_SchoolView__WEBPACK_IMPORTED_MODULE_8__.SchoolView, {\n                                                activeDepartment: activeDepartment,\n                                                setActiveDepartment: setActiveDepartment\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveComponent, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-2 py-1\",\n                                            style: {\n                                                padding: \"clamp(4px, 0.5vw, 16px)\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Footer__WEBPACK_IMPORTED_MODULE_6__.Footer, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_RightSidebar__WEBPACK_IMPORTED_MODULE_7__.RightSidebar, {\n                                    activeView: activeView,\n                                    setActiveView: (view)=>setActiveView(view)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\pages\\\\index.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(HomePage, \"lZWvVB+WwEO/PKVXjTCA+9U7koE=\", false, function() {\n    return [\n        _src_hooks_useAutoScale__WEBPACK_IMPORTED_MODULE_9__.useAutoScale\n    ];\n});\n_c1 = HomePage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HomePage);\nvar _c, _c1;\n$RefreshReg$(_c, \"LoadingComponent\");\n$RefreshReg$(_c1, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n"));

/***/ }),

/***/ "./src/hooks/useAutoScale.ts":
/*!***********************************!*\
  !*** ./src/hooks/useAutoScale.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAutoScale: function() { return /* binding */ useAutoScale; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useAutoScale = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const { targetWidth = 1920, targetHeight = 1080, minScale = 0.5, maxScale = 1.0 } = options;\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [scale, setScale] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(1);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const calculateScale = ()=>{\n            if (!containerRef.current) return;\n            const viewportWidth = window.innerWidth;\n            const viewportHeight = window.innerHeight;\n            // Calculate scale factors for both dimensions\n            const scaleX = viewportWidth / targetWidth;\n            const scaleY = viewportHeight / targetHeight;\n            // Use the smaller scale to ensure content fits in both dimensions\n            const calculatedScale = Math.min(scaleX, scaleY);\n            // Clamp the scale within the specified bounds\n            const finalScale = Math.max(minScale, Math.min(maxScale, calculatedScale));\n            setScale(finalScale);\n            // Apply the scale to the container\n            if (containerRef.current) {\n                containerRef.current.style.transform = \"scale(\".concat(finalScale, \")\");\n                containerRef.current.style.transformOrigin = \"top left\";\n                // Adjust container size to prevent overflow\n                const scaledWidth = targetWidth * finalScale;\n                const scaledHeight = targetHeight * finalScale;\n                if (scaledWidth < viewportWidth) {\n                    containerRef.current.style.left = \"\".concat((viewportWidth - scaledWidth) / 2, \"px\");\n                }\n                if (scaledHeight < viewportHeight) {\n                    containerRef.current.style.top = \"\".concat((viewportHeight - scaledHeight) / 2, \"px\");\n                }\n            }\n        };\n        // Calculate initial scale\n        calculateScale();\n        // Recalculate on window resize\n        const handleResize = ()=>{\n            calculateScale();\n        };\n        window.addEventListener(\"resize\", handleResize);\n        // Cleanup\n        return ()=>{\n            window.removeEventListener(\"resize\", handleResize);\n        };\n    }, [\n        targetWidth,\n        targetHeight,\n        minScale,\n        maxScale\n    ]);\n    return {\n        containerRef,\n        scale\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvaG9va3MvdXNlQXV0b1NjYWxlLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFvRDtBQVM3QyxNQUFNRyxlQUFlO1FBQUNDLDJFQUErQixDQUFDO0lBQzNELE1BQU0sRUFDSkMsY0FBYyxJQUFJLEVBQ2xCQyxlQUFlLElBQUksRUFDbkJDLFdBQVcsR0FBRyxFQUNkQyxXQUFXLEdBQUcsRUFDZixHQUFHSjtJQUVKLE1BQU1LLGVBQWVSLDZDQUFNQSxDQUFjO0lBQ3pDLE1BQU0sQ0FBQ1MsT0FBT0MsU0FBUyxHQUFHVCwrQ0FBUUEsQ0FBQztJQUVuQ0YsZ0RBQVNBLENBQUM7UUFDUixNQUFNWSxpQkFBaUI7WUFDckIsSUFBSSxDQUFDSCxhQUFhSSxPQUFPLEVBQUU7WUFFM0IsTUFBTUMsZ0JBQWdCQyxPQUFPQyxVQUFVO1lBQ3ZDLE1BQU1DLGlCQUFpQkYsT0FBT0csV0FBVztZQUV6Qyw4Q0FBOEM7WUFDOUMsTUFBTUMsU0FBU0wsZ0JBQWdCVDtZQUMvQixNQUFNZSxTQUFTSCxpQkFBaUJYO1lBRWhDLGtFQUFrRTtZQUNsRSxNQUFNZSxrQkFBa0JDLEtBQUtDLEdBQUcsQ0FBQ0osUUFBUUM7WUFFekMsOENBQThDO1lBQzlDLE1BQU1JLGFBQWFGLEtBQUtHLEdBQUcsQ0FBQ2xCLFVBQVVlLEtBQUtDLEdBQUcsQ0FBQ2YsVUFBVWE7WUFFekRWLFNBQVNhO1lBRVQsbUNBQW1DO1lBQ25DLElBQUlmLGFBQWFJLE9BQU8sRUFBRTtnQkFDeEJKLGFBQWFJLE9BQU8sQ0FBQ2EsS0FBSyxDQUFDQyxTQUFTLEdBQUcsU0FBb0IsT0FBWEgsWUFBVztnQkFDM0RmLGFBQWFJLE9BQU8sQ0FBQ2EsS0FBSyxDQUFDRSxlQUFlLEdBQUc7Z0JBRTdDLDRDQUE0QztnQkFDNUMsTUFBTUMsY0FBY3hCLGNBQWNtQjtnQkFDbEMsTUFBTU0sZUFBZXhCLGVBQWVrQjtnQkFFcEMsSUFBSUssY0FBY2YsZUFBZTtvQkFDL0JMLGFBQWFJLE9BQU8sQ0FBQ2EsS0FBSyxDQUFDSyxJQUFJLEdBQUcsR0FBcUMsT0FBbEMsQ0FBQ2pCLGdCQUFnQmUsV0FBVSxJQUFLLEdBQUU7Z0JBQ3pFO2dCQUVBLElBQUlDLGVBQWViLGdCQUFnQjtvQkFDakNSLGFBQWFJLE9BQU8sQ0FBQ2EsS0FBSyxDQUFDTSxHQUFHLEdBQUcsR0FBdUMsT0FBcEMsQ0FBQ2YsaUJBQWlCYSxZQUFXLElBQUssR0FBRTtnQkFDMUU7WUFDRjtRQUNGO1FBRUEsMEJBQTBCO1FBQzFCbEI7UUFFQSwrQkFBK0I7UUFDL0IsTUFBTXFCLGVBQWU7WUFDbkJyQjtRQUNGO1FBRUFHLE9BQU9tQixnQkFBZ0IsQ0FBQyxVQUFVRDtRQUVsQyxVQUFVO1FBQ1YsT0FBTztZQUNMbEIsT0FBT29CLG1CQUFtQixDQUFDLFVBQVVGO1FBQ3ZDO0lBQ0YsR0FBRztRQUFDNUI7UUFBYUM7UUFBY0M7UUFBVUM7S0FBUztJQUVsRCxPQUFPO1FBQUVDO1FBQWNDO0lBQU07QUFDL0IsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvaG9va3MvdXNlQXV0b1NjYWxlLnRzPzA4NjYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VSZWYsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuXG5pbnRlcmZhY2UgVXNlQXV0b1NjYWxlT3B0aW9ucyB7XG4gIHRhcmdldFdpZHRoPzogbnVtYmVyO1xuICB0YXJnZXRIZWlnaHQ/OiBudW1iZXI7XG4gIG1pblNjYWxlPzogbnVtYmVyO1xuICBtYXhTY2FsZT86IG51bWJlcjtcbn1cblxuZXhwb3J0IGNvbnN0IHVzZUF1dG9TY2FsZSA9IChvcHRpb25zOiBVc2VBdXRvU2NhbGVPcHRpb25zID0ge30pID0+IHtcbiAgY29uc3Qge1xuICAgIHRhcmdldFdpZHRoID0gMTkyMCxcbiAgICB0YXJnZXRIZWlnaHQgPSAxMDgwLFxuICAgIG1pblNjYWxlID0gMC41LFxuICAgIG1heFNjYWxlID0gMS4wXG4gIH0gPSBvcHRpb25zO1xuXG4gIGNvbnN0IGNvbnRhaW5lclJlZiA9IHVzZVJlZjxIVE1MRWxlbWVudD4obnVsbCk7XG4gIGNvbnN0IFtzY2FsZSwgc2V0U2NhbGVdID0gdXNlU3RhdGUoMSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBjYWxjdWxhdGVTY2FsZSA9ICgpID0+IHtcbiAgICAgIGlmICghY29udGFpbmVyUmVmLmN1cnJlbnQpIHJldHVybjtcblxuICAgICAgY29uc3Qgdmlld3BvcnRXaWR0aCA9IHdpbmRvdy5pbm5lcldpZHRoO1xuICAgICAgY29uc3Qgdmlld3BvcnRIZWlnaHQgPSB3aW5kb3cuaW5uZXJIZWlnaHQ7XG5cbiAgICAgIC8vIENhbGN1bGF0ZSBzY2FsZSBmYWN0b3JzIGZvciBib3RoIGRpbWVuc2lvbnNcbiAgICAgIGNvbnN0IHNjYWxlWCA9IHZpZXdwb3J0V2lkdGggLyB0YXJnZXRXaWR0aDtcbiAgICAgIGNvbnN0IHNjYWxlWSA9IHZpZXdwb3J0SGVpZ2h0IC8gdGFyZ2V0SGVpZ2h0O1xuXG4gICAgICAvLyBVc2UgdGhlIHNtYWxsZXIgc2NhbGUgdG8gZW5zdXJlIGNvbnRlbnQgZml0cyBpbiBib3RoIGRpbWVuc2lvbnNcbiAgICAgIGNvbnN0IGNhbGN1bGF0ZWRTY2FsZSA9IE1hdGgubWluKHNjYWxlWCwgc2NhbGVZKTtcblxuICAgICAgLy8gQ2xhbXAgdGhlIHNjYWxlIHdpdGhpbiB0aGUgc3BlY2lmaWVkIGJvdW5kc1xuICAgICAgY29uc3QgZmluYWxTY2FsZSA9IE1hdGgubWF4KG1pblNjYWxlLCBNYXRoLm1pbihtYXhTY2FsZSwgY2FsY3VsYXRlZFNjYWxlKSk7XG5cbiAgICAgIHNldFNjYWxlKGZpbmFsU2NhbGUpO1xuXG4gICAgICAvLyBBcHBseSB0aGUgc2NhbGUgdG8gdGhlIGNvbnRhaW5lclxuICAgICAgaWYgKGNvbnRhaW5lclJlZi5jdXJyZW50KSB7XG4gICAgICAgIGNvbnRhaW5lclJlZi5jdXJyZW50LnN0eWxlLnRyYW5zZm9ybSA9IGBzY2FsZSgke2ZpbmFsU2NhbGV9KWA7XG4gICAgICAgIGNvbnRhaW5lclJlZi5jdXJyZW50LnN0eWxlLnRyYW5zZm9ybU9yaWdpbiA9ICd0b3AgbGVmdCc7XG4gICAgICAgIFxuICAgICAgICAvLyBBZGp1c3QgY29udGFpbmVyIHNpemUgdG8gcHJldmVudCBvdmVyZmxvd1xuICAgICAgICBjb25zdCBzY2FsZWRXaWR0aCA9IHRhcmdldFdpZHRoICogZmluYWxTY2FsZTtcbiAgICAgICAgY29uc3Qgc2NhbGVkSGVpZ2h0ID0gdGFyZ2V0SGVpZ2h0ICogZmluYWxTY2FsZTtcbiAgICAgICAgXG4gICAgICAgIGlmIChzY2FsZWRXaWR0aCA8IHZpZXdwb3J0V2lkdGgpIHtcbiAgICAgICAgICBjb250YWluZXJSZWYuY3VycmVudC5zdHlsZS5sZWZ0ID0gYCR7KHZpZXdwb3J0V2lkdGggLSBzY2FsZWRXaWR0aCkgLyAyfXB4YDtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgaWYgKHNjYWxlZEhlaWdodCA8IHZpZXdwb3J0SGVpZ2h0KSB7XG4gICAgICAgICAgY29udGFpbmVyUmVmLmN1cnJlbnQuc3R5bGUudG9wID0gYCR7KHZpZXdwb3J0SGVpZ2h0IC0gc2NhbGVkSGVpZ2h0KSAvIDJ9cHhgO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfTtcblxuICAgIC8vIENhbGN1bGF0ZSBpbml0aWFsIHNjYWxlXG4gICAgY2FsY3VsYXRlU2NhbGUoKTtcblxuICAgIC8vIFJlY2FsY3VsYXRlIG9uIHdpbmRvdyByZXNpemVcbiAgICBjb25zdCBoYW5kbGVSZXNpemUgPSAoKSA9PiB7XG4gICAgICBjYWxjdWxhdGVTY2FsZSgpO1xuICAgIH07XG5cbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgaGFuZGxlUmVzaXplKTtcbiAgICBcbiAgICAvLyBDbGVhbnVwXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdyZXNpemUnLCBoYW5kbGVSZXNpemUpO1xuICAgIH07XG4gIH0sIFt0YXJnZXRXaWR0aCwgdGFyZ2V0SGVpZ2h0LCBtaW5TY2FsZSwgbWF4U2NhbGVdKTtcblxuICByZXR1cm4geyBjb250YWluZXJSZWYsIHNjYWxlIH07XG59O1xuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVJlZiIsInVzZVN0YXRlIiwidXNlQXV0b1NjYWxlIiwib3B0aW9ucyIsInRhcmdldFdpZHRoIiwidGFyZ2V0SGVpZ2h0IiwibWluU2NhbGUiLCJtYXhTY2FsZSIsImNvbnRhaW5lclJlZiIsInNjYWxlIiwic2V0U2NhbGUiLCJjYWxjdWxhdGVTY2FsZSIsImN1cnJlbnQiLCJ2aWV3cG9ydFdpZHRoIiwid2luZG93IiwiaW5uZXJXaWR0aCIsInZpZXdwb3J0SGVpZ2h0IiwiaW5uZXJIZWlnaHQiLCJzY2FsZVgiLCJzY2FsZVkiLCJjYWxjdWxhdGVkU2NhbGUiLCJNYXRoIiwibWluIiwiZmluYWxTY2FsZSIsIm1heCIsInN0eWxlIiwidHJhbnNmb3JtIiwidHJhbnNmb3JtT3JpZ2luIiwic2NhbGVkV2lkdGgiLCJzY2FsZWRIZWlnaHQiLCJsZWZ0IiwidG9wIiwiaGFuZGxlUmVzaXplIiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/hooks/useAutoScale.ts\n"));

/***/ })

});
{"name": "@theia/scm-extra", "version": "1.63.0", "description": "Theia - Source control extras Extension", "dependencies": {"@theia/core": "1.63.0", "@theia/editor": "1.63.0", "@theia/filesystem": "1.63.0", "@theia/navigator": "1.63.0", "@theia/scm": "1.63.0", "tslib": "^2.6.2"}, "publishConfig": {"access": "public"}, "theiaExtensions": [{"frontend": "lib/browser/scm-extra-frontend-module"}], "keywords": ["theia-extension"], "license": "EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0", "repository": {"type": "git", "url": "https://github.com/eclipse-theia/theia.git"}, "bugs": {"url": "https://github.com/eclipse-theia/theia/issues"}, "homepage": "https://github.com/eclipse-theia/theia", "files": ["lib", "src"], "scripts": {"build": "theiaext build", "clean": "theiaext clean", "compile": "theiaext compile", "docs": "theiaext docs", "lint": "theiaext lint", "watch": "theiaext watch"}, "devDependencies": {"@theia/ext-scripts": "1.63.0"}, "nyc": {"extends": "../../configs/nyc.json"}, "gitHead": "21358137e41342742707f660b8e222f940a27652"}
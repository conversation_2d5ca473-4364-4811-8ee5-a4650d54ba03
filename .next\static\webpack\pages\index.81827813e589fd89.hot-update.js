"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/.pnpm/react@18.2.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _icons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons */ \"./src/components/icons.tsx\");\n\nvar _s = $RefreshSig$();\n\n\nconst Sidebar = (param)=>{\n    let { activeView, setActiveView, activeApp, setActiveApp } = param;\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Main function buttons\n    const functionButtons = [\n        {\n            id: \"gamification\",\n            label: \"Gamification\",\n            icon: \"\\uD83C\\uDFAE\",\n            description: \"Learning Games\"\n        },\n        {\n            id: \"coder\",\n            label: \"Coder\",\n            icon: \"\\uD83D\\uDCBB\",\n            description: \"IDE View\"\n        },\n        {\n            id: \"studio\",\n            label: \"Studio\",\n            icon: \"\\uD83E\\uDDD1‍\\uD83C\\uDFA8\",\n            description: \"Creative Workspace\"\n        },\n        {\n            id: \"design\",\n            label: \"Design\",\n            icon: \"\\uD83C\\uDFA8\",\n            description: \"Design Tools\"\n        }\n    ];\n    // Original navigation items\n    const navItems = [\n        {\n            id: \"Dashboard\",\n            label: \"Home\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.HomeIcon, {\n                className: \"responsive-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 52,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: \"Analytic\",\n            label: \"Analytics\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChartBarIcon, {\n                className: \"responsive-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 57,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: \"Tool\",\n            label: \"Modules\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridIcon, {\n                className: \"responsive-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 62,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: \"Setting\",\n            label: \"Profile\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserCircleIcon, {\n                className: \"responsive-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 67,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: \"Setting\",\n            label: \"Settings\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SettingsIcon, {\n                className: \"responsive-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 72,\n                columnNumber: 13\n            }, undefined)\n        }\n    ];\n    const handleFunctionButtonClick = (appId)=>{\n        if (setActiveApp) {\n            setActiveApp(appId);\n        }\n        setIsExpanded(false); // Auto-collapse after selection\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black/20 backdrop-blur-sm z-30\",\n                        onClick: ()=>setIsExpanded(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed left-20 top-0 h-full bg-panel-bg border border-gray-700/50 backdrop-blur-xl z-40 p-6\",\n                        style: {\n                            width: \"calc(240px * var(--content-scale))\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-white font-semibold text-lg mb-2\",\n                                        children: \"Functions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Choose your workspace\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: functionButtons.map((button)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleFunctionButtonClick(button.id),\n                                        className: \"w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200 \".concat(activeApp === button.id ? \"bg-blue-500/20 text-blue-400 border border-blue-400/30\" : \"text-gray-300 hover:text-white hover:bg-gray-800/60\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl\",\n                                                children: button.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: button.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: button.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, button.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"bg-panel-bg border border-gray-700/50 responsive-border-radius sidebar-left flex flex-col items-center justify-between\",\n                style: {\n                    padding: \"calc(var(--base-spacing) * 0.75)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center w-full\",\n                        style: {\n                            gap: \"calc(var(--base-gap) * 0.75)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-cyan-400 to-fuchsia-500 rounded-full flex items-center justify-center text-white\",\n                                style: {\n                                    width: \"calc(var(--base-icon-size) * 1.25)\",\n                                    height: \"calc(var(--base-icon-size) * 1.25)\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.ShieldCheckIcon, {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full border-t border-gray-700\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, undefined),\n                            functionButtons.map((button)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        if (setActiveApp) {\n                                            setActiveApp(button.id);\n                                        }\n                                    },\n                                    \"aria-label\": button.label,\n                                    className: \"w-full flex items-center justify-center responsive-border-radius transition-colors duration-200 relative \".concat(activeApp === button.id ? \"bg-brand-cyan/20 text-white\" : \"text-gray-400 hover:bg-gray-700/50 hover:text-white\"),\n                                    style: {\n                                        padding: \"calc(var(--base-spacing) * 0.5)\",\n                                        height: \"var(--base-button-height)\"\n                                    },\n                                    children: [\n                                        activeApp === button.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute left-0 top-1/2 -translate-y-1/2 bg-brand-cyan rounded-r-full\",\n                                            style: {\n                                                height: \"calc(var(--base-button-height) * 0.6)\",\n                                                width: \"3px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl\",\n                                            children: button.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, button.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, undefined))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                setActiveView(\"Dashboard\");\n                                if (setActiveApp) setActiveApp(\"\");\n                            },\n                            \"aria-label\": \"Dashboard\",\n                            className: \"w-full flex items-center justify-center responsive-border-radius transition-colors duration-200 relative \".concat(activeView === \"Dashboard\" && !activeApp ? \"bg-brand-cyan/20 text-white\" : \"text-gray-400 hover:bg-gray-700/50 hover:text-white\"),\n                            style: {\n                                padding: \"calc(var(--base-spacing) * 0.5)\",\n                                height: \"var(--base-button-height)\"\n                            },\n                            children: [\n                                activeView === \"Dashboard\" && !activeApp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"absolute left-0 top-1/2 -translate-y-1/2 bg-brand-cyan rounded-r-full\",\n                                    style: {\n                                        height: \"calc(var(--base-button-height) * 0.6)\",\n                                        width: \"3px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons__WEBPACK_IMPORTED_MODULE_2__.HomeIcon, {\n                                    className: \"responsive-icon\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\lakhar_grow\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(Sidebar, \"FPNvbbHVlWWR4LKxxNntSxiIS38=\");\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Sidebar.tsx\n"));

/***/ })

});

import React, { useState, useEffect } from 'react';
import { ChartBarIcon, UserGroupIcon, CloudArrowUpIcon, ShieldCheckIcon } from '@heroicons/react/24/outline';
import { StatCard, StatCardProps } from './StatCard';
import { DataUsageChart } from './DataUsageChart';

type StatAPI = Omit<StatCardProps, 'icon'> & { id: string; icon: 'UserGroupIcon' | 'CloudArrowUpIcon' | 'ShieldCheckIcon' | 'ChartBarIcon' };
type ChartData = { name: string; usage: number };
type AnalyticsData = {
  stats: StatAPI[];
  chartData: ChartData[];
};

const iconMap: { [key: string]: React.ReactNode } = {
  UserGroupIcon: <UserGroupIcon className="w-6 h-6" />,
  CloudArrowUpIcon: <CloudArrowUpIcon className="w-6 h-6" />,
  ShieldCheckIcon: <ShieldCheckIcon className="w-6 h-6" />,
  ChartBarIcon: <ChartBarIcon className="w-6 h-6" />,
};

export const AnalyticView: React.FC = () => {
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/analytics');
        if (!response.ok) throw new Error('Network response was not ok');
        const result = await response.json();
        setData(result);
      } catch (error) {
        console.error("Failed to fetch analytics data", error);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  if (loading) {
    return <div className="flex justify-center items-center h-full text-cyan-400">Loading Analytics...</div>;
  }
  
  if (!data || !data.stats) {
     return <div className="flex justify-center items-center h-full text-red-400">Failed to load analytics data.</div>;
  }

  return (
    <div className="flex-grow grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 animate-fadeIn">
      {data.stats.map(stat => (
        <StatCard
          key={stat.id}
          icon={iconMap[stat.icon]}
          label={stat.label}
          value={stat.value}
          change={stat.change}
          changeType={stat.changeType}
        />
      ))}
      <div className="md:col-span-2 lg:col-span-4">
        <DataUsageChart data={data.chartData} />
      </div>
    </div>
  );
};

// Add this to your globals.css or a style tag if it's not there
// @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
// .animate-fadeIn { animation: fadeIn 0.5s ease-in-out; }

.MafsView .protractor-rotation-handle {
    cursor: grab;
    /* `pointer-events: all` is needed to make the hit target the size of the
     * entire <g> element and not just its visible subelements */
    pointer-events: all;
    transition: transform 0.15s ease-out;
}

.MafsView .protractor-rotation-handle:hover {
    transform: scale(1.2);
}

.MafsView .protractor-rotation-handle-arrow-arc {
    fill: none;
    stroke: var(--mafs-blue);
    stroke-width: 8;
    /* `strokeLinecap: square` prevents a hairline crack from appearing
     * between the arc and the arrowheads, by making the arc extend a bit
     * beyond its start and end coordinates.
     */
    stroke-linecap: square;
}

.MafsView .protractor-rotation-handle-arrowhead {
    fill: var(--mafs-blue);
    stroke: none;
}

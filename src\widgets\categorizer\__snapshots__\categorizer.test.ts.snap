// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`categorizer widget should snapshot on mobile: first mobile render 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        <strong>
          Classify each graph according to the kind of relationship it suggests.
        </strong>
      </div>
    </div>
    <div
      class="paragraph perseus-paragraph-centered"
      data-perseus-paragraph-index="1"
    >
      <div
        class="perseus-block-math"
        style="margin-left: -16px; margin-right: -16px;"
      >
        <div
          class="perseus-block-math-inner"
          style="overflow-x: auto; overflow-y: hidden; margin-top: -10px; margin-bottom: -10px; transform: translate3d(0,0,0); padding: 10px 16px 10px 16px;"
        >
          <span
            style="display: block; width: 100%; transform: scale(1, 1); transform-origin: 0 0; opacity: 1;"
          >
            <span
              class="mock-TeX"
            >
              \\qquad\\qquad\\quad\\text{Graph 1}\\qquad\\qquad\\quad\\qquad\\qquad\\quad\\text{Graph 2}
            </span>
          </span>
        </div>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="2"
    >
      <div
        class="paragraph"
      >
        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="categorizer-container fullBleedContainer_8ybp6"
          >
            <table
              class="categorizer-table mobileTable_1wmcwov"
            >
              <thead>
                <tr>
                  <td
                    class="emptyHeaderCell_1mttrgd"
                  />
                  <th
                    class="header_whrfk4"
                  >
                    <div
                      class="perseus-renderer perseus-renderer-responsive"
                    >
                      <div
                        class="paragraph"
                        data-perseus-paragraph-index="0"
                      >
                        <div
                          class="paragraph"
                        >
                          No relationship
                        </div>
                      </div>
                    </div>
                  </th>
                  <th
                    class="header_whrfk4"
                  >
                    <div
                      class="perseus-renderer perseus-renderer-responsive"
                    >
                      <div
                        class="paragraph"
                        data-perseus-paragraph-index="0"
                      >
                        <div
                          class="paragraph"
                        >
                          Positive linear relationship
                        </div>
                      </div>
                    </div>
                  </th>
                  <th
                    class="header_whrfk4"
                  >
                    <div
                      class="perseus-renderer perseus-renderer-responsive"
                    >
                      <div
                        class="paragraph"
                        data-perseus-paragraph-index="0"
                      >
                        <div
                          class="paragraph"
                        >
                          Negative linear relationship
                        </div>
                      </div>
                    </div>
                  </th>
                  <th
                    class="header_whrfk4"
                  >
                    <div
                      class="perseus-renderer perseus-renderer-responsive"
                    >
                      <div
                        class="paragraph"
                        data-perseus-paragraph-index="0"
                      >
                        <div
                          class="paragraph"
                        >
                          Nonlinear relationship
                        </div>
                      </div>
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>
                    <div
                      class="perseus-renderer perseus-renderer-responsive"
                    >
                      <div
                        class="paragraph"
                        data-perseus-paragraph-index="0"
                      >
                        <div
                          class="paragraph"
                        >
                          Graph 
                          <span
                            style="white-space: nowrap;"
                          >
                            <span />
                            <span
                              class="mock-TeX"
                            >
                              1
                            </span>
                            <span />
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td
                    class="category cell_13aakpm"
                  >
                    <div
                      aria-label="No relationship"
                      role="button"
                    >
                      <input
                        class="responsiveInput_1q2zj3l-o_O-responsiveRadioInput_17um91a"
                        name="perseus_radio_10_0"
                        type="radio"
                      />
                    </div>
                  </td>
                  <td
                    class="category cell_13aakpm"
                  >
                    <div
                      aria-label="Positive linear relationship"
                      role="button"
                    >
                      <input
                        class="responsiveInput_1q2zj3l-o_O-responsiveRadioInput_17um91a"
                        name="perseus_radio_10_0"
                        type="radio"
                      />
                    </div>
                  </td>
                  <td
                    class="category cell_13aakpm"
                  >
                    <div
                      aria-label="Negative linear relationship"
                      role="button"
                    >
                      <input
                        class="responsiveInput_1q2zj3l-o_O-responsiveRadioInput_17um91a"
                        name="perseus_radio_10_0"
                        type="radio"
                      />
                    </div>
                  </td>
                  <td
                    class="category cell_13aakpm"
                  >
                    <div
                      aria-label="Nonlinear relationship"
                      role="button"
                    >
                      <input
                        class="responsiveInput_1q2zj3l-o_O-responsiveRadioInput_17um91a"
                        name="perseus_radio_10_0"
                        type="radio"
                      />
                    </div>
                  </td>
                </tr>
                <tr>
                  <td>
                    <div
                      class="perseus-renderer perseus-renderer-responsive"
                    >
                      <div
                        class="paragraph"
                        data-perseus-paragraph-index="0"
                      >
                        <div
                          class="paragraph"
                        >
                          Graph 
                          <span
                            style="white-space: nowrap;"
                          >
                            <span />
                            <span
                              class="mock-TeX"
                            >
                              2
                            </span>
                            <span />
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td
                    class="category cell_13aakpm"
                  >
                    <div
                      aria-label="No relationship"
                      role="button"
                    >
                      <input
                        class="responsiveInput_1q2zj3l-o_O-responsiveRadioInput_17um91a"
                        name="perseus_radio_10_1"
                        type="radio"
                      />
                    </div>
                  </td>
                  <td
                    class="category cell_13aakpm"
                  >
                    <div
                      aria-label="Positive linear relationship"
                      role="button"
                    >
                      <input
                        class="responsiveInput_1q2zj3l-o_O-responsiveRadioInput_17um91a"
                        name="perseus_radio_10_1"
                        type="radio"
                      />
                    </div>
                  </td>
                  <td
                    class="category cell_13aakpm"
                  >
                    <div
                      aria-label="Negative linear relationship"
                      role="button"
                    >
                      <input
                        class="responsiveInput_1q2zj3l-o_O-responsiveRadioInput_17um91a"
                        name="perseus_radio_10_1"
                        type="radio"
                      />
                    </div>
                  </td>
                  <td
                    class="category cell_13aakpm"
                  >
                    <div
                      aria-label="Nonlinear relationship"
                      role="button"
                    >
                      <input
                        class="responsiveInput_1q2zj3l-o_O-responsiveRadioInput_17um91a"
                        name="perseus_radio_10_1"
                        type="radio"
                      />
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="3"
    >
      <div
        class="paragraph"
      >
        <strong>
          Graph 1.
        </strong>
      </div>
    </div>
    <div
      class="paragraph perseus-paragraph-centered"
      data-perseus-paragraph-index="4"
    >
      <div
        class="paragraph"
      >
        <div
          class="fixed-to-responsive svg-image"
          style="max-width: 220px; max-height: 223px;"
        >
          <div
            style="padding-bottom: 101.36%;"
          />
          <span
            style="top: 0px; left: 0px; width: 100%; height: 100%; position: absolute; min-width: 20px; display: flex; justify-content: center; align-content: center;"
          >
            <div
              class="default_xu2jcg-o_O-spinnerContainer_agrn11"
            >
              <svg
                height="48"
                viewBox="0 0 48 48"
                width="48"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  class="loadingSpinner_1dvgeb3-o_O-inlineStyles_8njutv"
                  d="M44.19 23.455a1.91 1.91 0 1 1 3.801 0h.003c.004.18.006.363.006.545 0 13.255-10.745 24-24 24S0 37.255 0 24 10.745 0 24 0c.182 0 .364.002.545.006V.01a1.91 1.91 0 1 1 0 3.801v.015A20.564 20.564 0 0 0 24 3.818C12.854 3.818 3.818 12.854 3.818 24c0 11.146 9.036 20.182 20.182 20.182 11.146 0 20.182-9.036 20.182-20.182 0-.182-.003-.364-.007-.545h.015z"
                  fill-rule="nonzero"
                />
              </svg>
            </div>
          </span>
        </div>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="5"
    >
      <div
        class="paragraph"
      >
        <strong>
          Graph 2.
        </strong>
      </div>
    </div>
    <div
      class="paragraph perseus-paragraph-centered"
      data-perseus-paragraph-index="6"
    >
      <div
        class="paragraph"
      >
        <div
          class="fixed-to-responsive svg-image"
          style="max-width: 244px; max-height: 223px;"
        >
          <div
            style="padding-bottom: 91.39%;"
          />
          <span
            style="top: 0px; left: 0px; width: 100%; height: 100%; position: absolute; min-width: 20px; display: flex; justify-content: center; align-content: center;"
          >
            <div
              class="default_xu2jcg-o_O-spinnerContainer_agrn11"
            >
              <svg
                height="48"
                viewBox="0 0 48 48"
                width="48"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  class="loadingSpinner_1dvgeb3-o_O-inlineStyles_8njutv"
                  d="M44.19 23.455a1.91 1.91 0 1 1 3.801 0h.003c.004.18.006.363.006.545 0 13.255-10.745 24-24 24S0 37.255 0 24 10.745 0 24 0c.182 0 .364.002.545.006V.01a1.91 1.91 0 1 1 0 3.801v.015A20.564 20.564 0 0 0 24 3.818C12.854 3.818 3.818 12.854 3.818 24c0 11.146 9.036 20.182 20.182 20.182 11.146 0 20.182-9.036 20.182-20.182 0-.182-.003-.364-.007-.545h.015z"
                  fill-rule="nonzero"
                />
              </svg>
            </div>
          </span>
        </div>
          
      </div>
    </div>
  </div>
</div>
`;

exports[`categorizer widget should snapshot: first render 1`] = `
<div>
  <div
    class="perseus-renderer perseus-renderer-responsive"
  >
    <div
      class="paragraph"
      data-perseus-paragraph-index="0"
    >
      <div
        class="paragraph"
      >
        <strong>
          Classify each graph according to the kind of relationship it suggests.
        </strong>
      </div>
    </div>
    <div
      class="paragraph perseus-paragraph-centered"
      data-perseus-paragraph-index="1"
    >
      <div
        class="perseus-block-math"
      >
        <div
          class="perseus-block-math-inner"
          style="overflow-x: auto; overflow-y: hidden; padding-top: 10px; padding-bottom: 10px; margin-top: -10px; margin-bottom: -10px;"
        >
          <span
            class="mock-TeX"
          >
            \\qquad\\qquad\\quad\\text{Graph 1}\\qquad\\qquad\\quad\\qquad\\qquad\\quad\\text{Graph 2}
          </span>
        </div>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="2"
    >
      <div
        class="paragraph"
      >
        <div
          class="perseus-widget-container widget-nohighlight widget-block"
        >
          <div
            class="categorizer-container "
          >
            <table
              class="categorizer-table mobileTable_1wmcwov"
            >
              <thead>
                <tr>
                  <td
                    class="emptyHeaderCell_1mttrgd"
                  />
                  <th
                    class="header_whrfk4"
                  >
                    <div
                      class="perseus-renderer perseus-renderer-responsive"
                    >
                      <div
                        class="paragraph"
                        data-perseus-paragraph-index="0"
                      >
                        <div
                          class="paragraph"
                        >
                          No relationship
                        </div>
                      </div>
                    </div>
                  </th>
                  <th
                    class="header_whrfk4"
                  >
                    <div
                      class="perseus-renderer perseus-renderer-responsive"
                    >
                      <div
                        class="paragraph"
                        data-perseus-paragraph-index="0"
                      >
                        <div
                          class="paragraph"
                        >
                          Positive linear relationship
                        </div>
                      </div>
                    </div>
                  </th>
                  <th
                    class="header_whrfk4"
                  >
                    <div
                      class="perseus-renderer perseus-renderer-responsive"
                    >
                      <div
                        class="paragraph"
                        data-perseus-paragraph-index="0"
                      >
                        <div
                          class="paragraph"
                        >
                          Negative linear relationship
                        </div>
                      </div>
                    </div>
                  </th>
                  <th
                    class="header_whrfk4"
                  >
                    <div
                      class="perseus-renderer perseus-renderer-responsive"
                    >
                      <div
                        class="paragraph"
                        data-perseus-paragraph-index="0"
                      >
                        <div
                          class="paragraph"
                        >
                          Nonlinear relationship
                        </div>
                      </div>
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>
                    <div
                      class="perseus-renderer perseus-renderer-responsive"
                    >
                      <div
                        class="paragraph"
                        data-perseus-paragraph-index="0"
                      >
                        <div
                          class="paragraph"
                        >
                          Graph 
                          <span
                            style="white-space: nowrap;"
                          >
                            <span />
                            <span
                              class="mock-TeX"
                            >
                              1
                            </span>
                            <span />
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td
                    class="category cell_13aakpm"
                  >
                    <div
                      aria-label="No relationship"
                      role="button"
                    >
                      <span
                        class="radioSpan_1mq3xs9"
                      >
                        <svg
                          aria-hidden="true"
                          height="1em"
                          role="img"
                          style="vertical-align: middle;"
                          viewBox="0 0 100 99.944"
                          width="1.0005603137757144em"
                        >
                          <path
                            d="M50.046 8.322q-8.493 0-16.188 3.306-15.561 6.669-22.173 22.23-3.363 7.695-3.363 16.188t3.306 16.188 8.949 13.281q5.586 5.586 13.281 8.892t16.188 3.306 16.188-3.306 13.281-8.892 8.892-13.281 3.306-16.188-3.306-16.188-8.892-13.281-13.281-8.949q-7.695-3.306-16.188-3.306zm0 91.713q-13.623 0-25.137-6.726t-18.183-18.183q-6.726-11.571-6.726-25.137t6.726-25.08 18.24-18.24 25.08-6.669q13.566 0 25.08 6.726 11.514 6.669 18.24 18.183t6.726 25.137-6.726 25.137-18.24 18.126q-11.514 6.726-25.08 6.726z"
                            fill="currentColor"
                          />
                        </svg>
                      </span>
                    </div>
                  </td>
                  <td
                    class="category cell_13aakpm"
                  >
                    <div
                      aria-label="Positive linear relationship"
                      role="button"
                    >
                      <span
                        class="radioSpan_1mq3xs9"
                      >
                        <svg
                          aria-hidden="true"
                          height="1em"
                          role="img"
                          style="vertical-align: middle;"
                          viewBox="0 0 100 99.944"
                          width="1.0005603137757144em"
                        >
                          <path
                            d="M50.046 8.322q-8.493 0-16.188 3.306-15.561 6.669-22.173 22.23-3.363 7.695-3.363 16.188t3.306 16.188 8.949 13.281q5.586 5.586 13.281 8.892t16.188 3.306 16.188-3.306 13.281-8.892 8.892-13.281 3.306-16.188-3.306-16.188-8.892-13.281-13.281-8.949q-7.695-3.306-16.188-3.306zm0 91.713q-13.623 0-25.137-6.726t-18.183-18.183q-6.726-11.571-6.726-25.137t6.726-25.08 18.24-18.24 25.08-6.669q13.566 0 25.08 6.726 11.514 6.669 18.24 18.183t6.726 25.137-6.726 25.137-18.24 18.126q-11.514 6.726-25.08 6.726z"
                            fill="currentColor"
                          />
                        </svg>
                      </span>
                    </div>
                  </td>
                  <td
                    class="category cell_13aakpm"
                  >
                    <div
                      aria-label="Negative linear relationship"
                      role="button"
                    >
                      <span
                        class="radioSpan_1mq3xs9"
                      >
                        <svg
                          aria-hidden="true"
                          height="1em"
                          role="img"
                          style="vertical-align: middle;"
                          viewBox="0 0 100 99.944"
                          width="1.0005603137757144em"
                        >
                          <path
                            d="M50.046 8.322q-8.493 0-16.188 3.306-15.561 6.669-22.173 22.23-3.363 7.695-3.363 16.188t3.306 16.188 8.949 13.281q5.586 5.586 13.281 8.892t16.188 3.306 16.188-3.306 13.281-8.892 8.892-13.281 3.306-16.188-3.306-16.188-8.892-13.281-13.281-8.949q-7.695-3.306-16.188-3.306zm0 91.713q-13.623 0-25.137-6.726t-18.183-18.183q-6.726-11.571-6.726-25.137t6.726-25.08 18.24-18.24 25.08-6.669q13.566 0 25.08 6.726 11.514 6.669 18.24 18.183t6.726 25.137-6.726 25.137-18.24 18.126q-11.514 6.726-25.08 6.726z"
                            fill="currentColor"
                          />
                        </svg>
                      </span>
                    </div>
                  </td>
                  <td
                    class="category cell_13aakpm"
                  >
                    <div
                      aria-label="Nonlinear relationship"
                      role="button"
                    >
                      <span
                        class="radioSpan_1mq3xs9"
                      >
                        <svg
                          aria-hidden="true"
                          height="1em"
                          role="img"
                          style="vertical-align: middle;"
                          viewBox="0 0 100 99.944"
                          width="1.0005603137757144em"
                        >
                          <path
                            d="M50.046 8.322q-8.493 0-16.188 3.306-15.561 6.669-22.173 22.23-3.363 7.695-3.363 16.188t3.306 16.188 8.949 13.281q5.586 5.586 13.281 8.892t16.188 3.306 16.188-3.306 13.281-8.892 8.892-13.281 3.306-16.188-3.306-16.188-8.892-13.281-13.281-8.949q-7.695-3.306-16.188-3.306zm0 91.713q-13.623 0-25.137-6.726t-18.183-18.183q-6.726-11.571-6.726-25.137t6.726-25.08 18.24-18.24 25.08-6.669q13.566 0 25.08 6.726 11.514 6.669 18.24 18.183t6.726 25.137-6.726 25.137-18.24 18.126q-11.514 6.726-25.08 6.726z"
                            fill="currentColor"
                          />
                        </svg>
                      </span>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td>
                    <div
                      class="perseus-renderer perseus-renderer-responsive"
                    >
                      <div
                        class="paragraph"
                        data-perseus-paragraph-index="0"
                      >
                        <div
                          class="paragraph"
                        >
                          Graph 
                          <span
                            style="white-space: nowrap;"
                          >
                            <span />
                            <span
                              class="mock-TeX"
                            >
                              2
                            </span>
                            <span />
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td
                    class="category cell_13aakpm"
                  >
                    <div
                      aria-label="No relationship"
                      role="button"
                    >
                      <span
                        class="radioSpan_1mq3xs9"
                      >
                        <svg
                          aria-hidden="true"
                          height="1em"
                          role="img"
                          style="vertical-align: middle;"
                          viewBox="0 0 100 99.944"
                          width="1.0005603137757144em"
                        >
                          <path
                            d="M50.046 8.322q-8.493 0-16.188 3.306-15.561 6.669-22.173 22.23-3.363 7.695-3.363 16.188t3.306 16.188 8.949 13.281q5.586 5.586 13.281 8.892t16.188 3.306 16.188-3.306 13.281-8.892 8.892-13.281 3.306-16.188-3.306-16.188-8.892-13.281-13.281-8.949q-7.695-3.306-16.188-3.306zm0 91.713q-13.623 0-25.137-6.726t-18.183-18.183q-6.726-11.571-6.726-25.137t6.726-25.08 18.24-18.24 25.08-6.669q13.566 0 25.08 6.726 11.514 6.669 18.24 18.183t6.726 25.137-6.726 25.137-18.24 18.126q-11.514 6.726-25.08 6.726z"
                            fill="currentColor"
                          />
                        </svg>
                      </span>
                    </div>
                  </td>
                  <td
                    class="category cell_13aakpm"
                  >
                    <div
                      aria-label="Positive linear relationship"
                      role="button"
                    >
                      <span
                        class="radioSpan_1mq3xs9"
                      >
                        <svg
                          aria-hidden="true"
                          height="1em"
                          role="img"
                          style="vertical-align: middle;"
                          viewBox="0 0 100 99.944"
                          width="1.0005603137757144em"
                        >
                          <path
                            d="M50.046 8.322q-8.493 0-16.188 3.306-15.561 6.669-22.173 22.23-3.363 7.695-3.363 16.188t3.306 16.188 8.949 13.281q5.586 5.586 13.281 8.892t16.188 3.306 16.188-3.306 13.281-8.892 8.892-13.281 3.306-16.188-3.306-16.188-8.892-13.281-13.281-8.949q-7.695-3.306-16.188-3.306zm0 91.713q-13.623 0-25.137-6.726t-18.183-18.183q-6.726-11.571-6.726-25.137t6.726-25.08 18.24-18.24 25.08-6.669q13.566 0 25.08 6.726 11.514 6.669 18.24 18.183t6.726 25.137-6.726 25.137-18.24 18.126q-11.514 6.726-25.08 6.726z"
                            fill="currentColor"
                          />
                        </svg>
                      </span>
                    </div>
                  </td>
                  <td
                    class="category cell_13aakpm"
                  >
                    <div
                      aria-label="Negative linear relationship"
                      role="button"
                    >
                      <span
                        class="radioSpan_1mq3xs9"
                      >
                        <svg
                          aria-hidden="true"
                          height="1em"
                          role="img"
                          style="vertical-align: middle;"
                          viewBox="0 0 100 99.944"
                          width="1.0005603137757144em"
                        >
                          <path
                            d="M50.046 8.322q-8.493 0-16.188 3.306-15.561 6.669-22.173 22.23-3.363 7.695-3.363 16.188t3.306 16.188 8.949 13.281q5.586 5.586 13.281 8.892t16.188 3.306 16.188-3.306 13.281-8.892 8.892-13.281 3.306-16.188-3.306-16.188-8.892-13.281-13.281-8.949q-7.695-3.306-16.188-3.306zm0 91.713q-13.623 0-25.137-6.726t-18.183-18.183q-6.726-11.571-6.726-25.137t6.726-25.08 18.24-18.24 25.08-6.669q13.566 0 25.08 6.726 11.514 6.669 18.24 18.183t6.726 25.137-6.726 25.137-18.24 18.126q-11.514 6.726-25.08 6.726z"
                            fill="currentColor"
                          />
                        </svg>
                      </span>
                    </div>
                  </td>
                  <td
                    class="category cell_13aakpm"
                  >
                    <div
                      aria-label="Nonlinear relationship"
                      role="button"
                    >
                      <span
                        class="radioSpan_1mq3xs9"
                      >
                        <svg
                          aria-hidden="true"
                          height="1em"
                          role="img"
                          style="vertical-align: middle;"
                          viewBox="0 0 100 99.944"
                          width="1.0005603137757144em"
                        >
                          <path
                            d="M50.046 8.322q-8.493 0-16.188 3.306-15.561 6.669-22.173 22.23-3.363 7.695-3.363 16.188t3.306 16.188 8.949 13.281q5.586 5.586 13.281 8.892t16.188 3.306 16.188-3.306 13.281-8.892 8.892-13.281 3.306-16.188-3.306-16.188-8.892-13.281-13.281-8.949q-7.695-3.306-16.188-3.306zm0 91.713q-13.623 0-25.137-6.726t-18.183-18.183q-6.726-11.571-6.726-25.137t6.726-25.08 18.24-18.24 25.08-6.669q13.566 0 25.08 6.726 11.514 6.669 18.24 18.183t6.726 25.137-6.726 25.137-18.24 18.126q-11.514 6.726-25.08 6.726z"
                            fill="currentColor"
                          />
                        </svg>
                      </span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="3"
    >
      <div
        class="paragraph"
      >
        <strong>
          Graph 1.
        </strong>
      </div>
    </div>
    <div
      class="paragraph perseus-paragraph-centered"
      data-perseus-paragraph-index="4"
    >
      <div
        class="paragraph"
      >
        <div
          class="fixed-to-responsive svg-image"
          style="max-width: 220px; max-height: 223px;"
        >
          <div
            style="padding-bottom: 101.36%;"
          />
          <span
            style="top: 0px; left: 0px; width: 100%; height: 100%; position: absolute; min-width: 20px; display: flex; justify-content: center; align-content: center;"
          >
            <div
              class="default_xu2jcg-o_O-spinnerContainer_agrn11"
            >
              <svg
                height="48"
                viewBox="0 0 48 48"
                width="48"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  class="loadingSpinner_1dvgeb3-o_O-inlineStyles_8njutv"
                  d="M44.19 23.455a1.91 1.91 0 1 1 3.801 0h.003c.004.18.006.363.006.545 0 13.255-10.745 24-24 24S0 37.255 0 24 10.745 0 24 0c.182 0 .364.002.545.006V.01a1.91 1.91 0 1 1 0 3.801v.015A20.564 20.564 0 0 0 24 3.818C12.854 3.818 3.818 12.854 3.818 24c0 11.146 9.036 20.182 20.182 20.182 11.146 0 20.182-9.036 20.182-20.182 0-.182-.003-.364-.007-.545h.015z"
                  fill-rule="nonzero"
                />
              </svg>
            </div>
          </span>
        </div>
      </div>
    </div>
    <div
      class="paragraph"
      data-perseus-paragraph-index="5"
    >
      <div
        class="paragraph"
      >
        <strong>
          Graph 2.
        </strong>
      </div>
    </div>
    <div
      class="paragraph perseus-paragraph-centered"
      data-perseus-paragraph-index="6"
    >
      <div
        class="paragraph"
      >
        <div
          class="fixed-to-responsive svg-image"
          style="max-width: 244px; max-height: 223px;"
        >
          <div
            style="padding-bottom: 91.39%;"
          />
          <span
            style="top: 0px; left: 0px; width: 100%; height: 100%; position: absolute; min-width: 20px; display: flex; justify-content: center; align-content: center;"
          >
            <div
              class="default_xu2jcg-o_O-spinnerContainer_agrn11"
            >
              <svg
                height="48"
                viewBox="0 0 48 48"
                width="48"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  class="loadingSpinner_1dvgeb3-o_O-inlineStyles_8njutv"
                  d="M44.19 23.455a1.91 1.91 0 1 1 3.801 0h.003c.004.18.006.363.006.545 0 13.255-10.745 24-24 24S0 37.255 0 24 10.745 0 24 0c.182 0 .364.002.545.006V.01a1.91 1.91 0 1 1 0 3.801v.015A20.564 20.564 0 0 0 24 3.818C12.854 3.818 3.818 12.854 3.818 24c0 11.146 9.036 20.182 20.182 20.182 11.146 0 20.182-9.036 20.182-20.182 0-.182-.003-.364-.007-.545h.015z"
                  fill-rule="nonzero"
                />
              </svg>
            </div>
          </span>
        </div>
          
      </div>
    </div>
  </div>
</div>
`;

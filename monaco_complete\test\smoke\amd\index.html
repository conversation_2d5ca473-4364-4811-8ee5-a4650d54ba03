<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
	</head>
	<body>
		<div id="editor-container" style="position: absolute; width: 500px; height: 400px"></div>
		<script src="../../../out/monaco-editor/dev/vs/loader.js"></script>
		<script>
			require.config({
				baseUrl: new URL('..', document.baseURI).toString(),
				paths: {
					vs: '../../../out/monaco-editor/dev/vs'
				}
			});
			require(['vs/editor/editor.main'], () => {
				window.monacoAPI = monaco;
			});
		</script>
	</body>
</html>
